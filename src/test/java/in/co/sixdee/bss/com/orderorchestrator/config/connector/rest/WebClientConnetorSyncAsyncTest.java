/*
 * package in.co.sixdee.bss.com.orderorchestrator.config.connector.rest;
 * 
 * import java.util.LinkedHashMap; import java.util.Map;
 * 
 * import org.junit.jupiter.api.AfterAll; import
 * org.junit.jupiter.api.AfterEach; import org.junit.jupiter.api.BeforeAll;
 * import org.junit.jupiter.api.BeforeEach; import org.junit.jupiter.api.Test;
 * import org.junit.jupiter.api.extension.ExtendWith; import
 * org.junit.platform.runner.JUnitPlatform; import org.junit.runner.RunWith;
 * import org.mockito.InjectMocks; import org.mockito.Mock; import
 * org.mockito.junit.jupiter.MockitoExtension; import
 * org.springframework.web.reactive.function.client.WebClient;
 * 
 * 
 * @ExtendWith(MockitoExtension.class)
 * 
 * @RunWith(JUnitPlatform.class) class WebClientConnetorSyncAsyncTest {
 * 
 * @InjectMocks private WebClientConnetorSyncAsync webClientConnetorSyncAsync;
 * 
 * @Mock private WebClient.Builder webClientBuilder;
 * 
 * static String uri; static Map<String, String> headers; static Map<String,
 * String> queryParams;
 * 
 * @BeforeAll static void setUpBeforeClass() throws Exception { uri = "http://";
 * headers = new LinkedHashMap<>(); queryParams = new LinkedHashMap<>();
 * headers.put("userid", "1"); queryParams.put("serviceId", "23456"); }
 * 
 * @AfterAll static void tearDownAfterClass() throws Exception { }
 * 
 * @BeforeEach void setUp() throws Exception { }
 * 
 * @AfterEach void tearDown() throws Exception { }
 * 
 * @Test void testGet() { webClientConnetorSyncAsync.webClientBuilder =
 * webClientBuilder; webClientConnetorSyncAsync.get(uri, 0, 0, headers,
 * queryParams);
 * 
 * }
 * 
 * @Test void testPost() {
 * 
 * }
 * 
 * @Test void testPut() {
 * 
 * }
 * 
 * @Test void testPatch() {
 * 
 * }
 * 
 * @Test void testDelete() {
 * 
 * }
 * 
 * }
 */