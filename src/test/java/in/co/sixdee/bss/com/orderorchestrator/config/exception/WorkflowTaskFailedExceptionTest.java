/*
package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import org.junit.Rule;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.rules.ExpectedException;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class WorkflowTaskFailedExceptionTest {
	
	@Rule
	public ExpectedException thrown = ExpectedException.none();

	@Test
	void testWorkflowTaskFailedException() {
		thrown.expect(WorkflowTaskFailedException.class);
		thrown.expectMessage("Failed task execution for:BSServiceCreation Caused by:199 Unknown Error");
		new WorkflowTaskFailedException("BSServiceCreation", "199", "Unknown Error");
	}

}
*/
