/*
package in.co.sixdee.bss.com.orderorchestrator.config.camunda.util;

import static org.mockito.Mockito.mock;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;

import org.apache.commons.io.FileUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.spin.plugin.variable.value.JsonValue;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.ErrorConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderConstants;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.util.NGTableColumnConstants;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext.ErrorDetail;
import in.co.sixdee.bss.om.model.dto.order.Order;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
class WorkFlowUtilTest {

	@InjectMocks
	private WorkFlowUtil workFlowUtil;

	@Mock
	GetDataFromCache cache;

	@Mock
	private ObjectMapper objectMapper;

	@Mock
	static DelegateExecution execution;

	static OrderFlowContext orderFlowContext;
	static CacheTableDataDTO workflowRetryConfigDto;
	static HashMap<String, String> ngTableData;
	static ErrorDetail errorDetail;
	static String spec ;

	@BeforeAll
	static void setUpBeforeClass() throws Exception {
		execution = mock(DelegateExecution.class);
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
		orderFlowContext = null;
		workflowRetryConfigDto = null;
		ngTableData = null;
		errorDetail = null;
	}

	@BeforeEach
	void setUp() throws Exception {
		orderFlowContext = new OrderFlowContext();
		orderFlowContext.setAttributes(new LinkedHashMap<>());
		orderFlowContext.setWorkItemAudits(new ArrayList<>());
		workflowRetryConfigDto = new CacheTableDataDTO();
		ngTableData = new HashMap<>();
		errorDetail = new OrderFlowContext().getErrorDetail();
		spec = FileUtils
				.readFileToString(new File("src/test/resources/BS_AddSubscription_Request.json"));

	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void testCreateWorkItemAudit() {
		boolean isCommonWorkItem = true;
		String executionStatus = "Completed";
		String subOrderId = "123456789";
		orderFlowContext.getAttributes().put(GenericConstants.ORDER_ID, "123456789");
		Mockito.doReturn("BSCreateService").when(execution).getCurrentActivityId();
		workFlowUtil.createWorkItemAudit(orderFlowContext, execution, executionStatus, subOrderId);
		Assertions.assertEquals(subOrderId, orderFlowContext.getWorkItemAudits().get(0).getSubOrderId());

	}

	@Test
	void testCreateWorkItemAuditException() {
		boolean isCommonWorkItem = true;
		String executionStatus = "Completed";
		String subOrderId = "123456789";
		orderFlowContext.setAttributes(null);
		Mockito.doReturn("BSCreateService").when(execution).getCurrentActivityId();
		workFlowUtil.createWorkItemAudit(orderFlowContext, execution, executionStatus, subOrderId);
		Assertions.assertEquals(1, orderFlowContext.getWorkItemAudits().size());

	}

	@Test
	void testIsTaskRetryEnabled() {
		String activityId = "BSCreateService";
		Order order = new Order();
		order.setOrderType("AddSubscription");
		orderFlowContext.setOrder(order);
		ngTableData.put(NGTableColumnConstants.COLUMN_RETRY_STRATEGY, OrderConstants.RETRY_STRATEGY_NORETRY);
		workflowRetryConfigDto.setNgTableData(ngTableData);
		Mockito.doReturn(workflowRetryConfigDto).when(cache).getCacheDetailsFromDBMap(
				NGTableConstants.CACHE_KEY_COM_WORKFLOW_RETRY_CONFIG,
				orderFlowContext.getOrder().getOrderType() + "_" + activityId);

		boolean taskRetryEnabled = workFlowUtil.isTaskRetryEnabled(orderFlowContext.getOrder().getOrderType(), activityId);
		Assertions.assertFalse(taskRetryEnabled);

	}

	@Test
	void testIsTaskRetryEnabledConnectionError() {
		String activityId = "BSCreateService";
		Order order = new Order();
		order.setOrderType("AddSubscription");
		orderFlowContext.setOrder(order);
		ngTableData.put(NGTableColumnConstants.COLUMN_RETRY_STRATEGY, OrderConstants.RETRY_STRATEGY_CONNECTION_ERROR);
		workflowRetryConfigDto.setNgTableData(ngTableData);
//		workFlowUtil.cache = cache;
		Mockito.doReturn(workflowRetryConfigDto).when(cache).getCacheDetailsFromDBMap(
				NGTableConstants.CACHE_KEY_COM_WORKFLOW_RETRY_CONFIG,
				orderFlowContext.getOrder().getOrderType() + "_" + activityId);
		errorDetail.setCode(ErrorConstants.ec_rest_call_error);
		orderFlowContext.setErrorDetail(errorDetail);
		boolean taskRetryEnabled = workFlowUtil.isTaskRetryEnabled(orderFlowContext.getOrder().getOrderType(), activityId);
		Assertions.assertTrue(taskRetryEnabled);

	}

	@Test
	void testIsTaskRetryEnabledAPIError() {
		String activityId = "BSCreateService";
		Order order = new Order();
		order.setOrderType("AddSubscription");
		orderFlowContext.setOrder(order);
		ngTableData.put(NGTableColumnConstants.COLUMN_RETRY_STRATEGY, OrderConstants.RETRY_STRATEGY_API_ERROR);
		workflowRetryConfigDto.setNgTableData(ngTableData);
//		workFlowUtil.cache = cache;
		Mockito.doReturn(workflowRetryConfigDto).when(cache).getCacheDetailsFromDBMap(
				NGTableConstants.CACHE_KEY_COM_WORKFLOW_RETRY_CONFIG,
				orderFlowContext.getOrder().getOrderType() + "_" + activityId);

		errorDetail.setCode("wdfghj");
		orderFlowContext.setErrorDetail(errorDetail);
		boolean taskRetryEnabled = workFlowUtil.isTaskRetryEnabled(orderFlowContext.getOrder().getOrderType(), activityId);
		Assertions.assertTrue(taskRetryEnabled);

	}

	//@Test
	void testIsTaskRetryEnabledException() {
		String activityId = "BSCreateService";
		Order order = new Order();
		order.setOrderType("AddSubscription");
		orderFlowContext.setOrder(null);
		ngTableData.put(NGTableColumnConstants.COLUMN_RETRY_STRATEGY, OrderConstants.RETRY_STRATEGY_API_ERROR);
		workflowRetryConfigDto.setNgTableData(ngTableData);
//		workFlowUtil.cache = cache;

		orderFlowContext.setErrorDetail(null);
		boolean taskRetryEnabled = workFlowUtil.isTaskRetryEnabled(orderFlowContext.getOrder().getOrderType(), activityId);
		Assertions.assertFalse(taskRetryEnabled);

	}

	@Test
	void testIsTaskRetryEnabledBothErrors() {
		String activityId = "BSCreateService";
		Order order = new Order();
		order.setOrderType("AddSubscription");
		orderFlowContext.setOrder(order);
		ngTableData.put(NGTableColumnConstants.COLUMN_RETRY_STRATEGY, OrderConstants.RETRY_STRATEGY_BOTH);
		workflowRetryConfigDto.setNgTableData(ngTableData);
//		workFlowUtil.cache = cache;
		Mockito.doReturn(workflowRetryConfigDto).when(cache).getCacheDetailsFromDBMap(
				NGTableConstants.CACHE_KEY_COM_WORKFLOW_RETRY_CONFIG,
				orderFlowContext.getOrder().getOrderType() + "_" + activityId);
		boolean taskRetryEnabled = workFlowUtil.isTaskRetryEnabled(orderFlowContext.getOrder().getOrderType(), activityId);
		Assertions.assertTrue(taskRetryEnabled);

	}

	@Test
	void testIsTaskRetryEnabledDefault() {
		String activityId = "BSCreateService";
		Order order = new Order();
		order.setOrderType("AddSubscription");
		orderFlowContext.setOrder(order);
		ngTableData.put(NGTableColumnConstants.COLUMN_RETRY_STRATEGY, "RETRY_STRATEGY");
		workflowRetryConfigDto.setNgTableData(ngTableData);
//		workFlowUtil.cache = cache;
		Mockito.doReturn(workflowRetryConfigDto).when(cache).getCacheDetailsFromDBMap(
				NGTableConstants.CACHE_KEY_COM_WORKFLOW_RETRY_CONFIG,
				orderFlowContext.getOrder().getOrderType() + "_" + activityId);
		boolean taskRetryEnabled = workFlowUtil.isTaskRetryEnabled(orderFlowContext.getOrder().getOrderType(), activityId);
		Assertions.assertFalse(taskRetryEnabled);

	}

	@Test
	void testIsTaskRetryEnabledNullValues() {
		String activityId = "BSCreateService";
		Order order = new Order();
		order.setOrderType("AddSubscription");
		orderFlowContext.setOrder(order);
//		workFlowUtil.cache = cache;
		Mockito.doReturn(null).when(cache).getCacheDetailsFromDBMap(
				NGTableConstants.CACHE_KEY_COM_WORKFLOW_RETRY_CONFIG,
				orderFlowContext.getOrder().getOrderType() + "_" + activityId);
		boolean taskRetryEnabled = workFlowUtil.isTaskRetryEnabled(orderFlowContext.getOrder().getOrderType(), activityId);
		Assertions.assertFalse(taskRetryEnabled);

	}

	@Test
	void testGetOrderStageByActivityId() {
		String activityId = "BSCreateService";
		ngTableData.put(CacheConstants.CacheFields.STAGE.name(),"ORD_ACK");
		workflowRetryConfigDto.setNgTableData(ngTableData);
		String orderStageByActivityId = workFlowUtil.getOrderStageByActivityId(activityId);
		Assertions.assertNull(orderStageByActivityId);

	}

	@Test
	void testCreateWorkflowData() {
		workFlowUtil.objectMapper = objectMapper;
		JsonValue createWorkflowData = workFlowUtil.createWorkflowData("BS_AccountCreation", orderFlowContext);
		

	}
	//@Test
	void testCreateWorkflowDataWithOrder() {
		Order order = new Order();
		order = new Gson().fromJson(spec, Order.class);
		orderFlowContext.setOrder(order);
		workFlowUtil.objectMapper = objectMapper;
		Assertions.assertThrows(NullPointerException.class, () ->{
		 workFlowUtil.createWorkflowData("BS_AccountCreation", orderFlowContext);
		});
		

	}

}
*/
