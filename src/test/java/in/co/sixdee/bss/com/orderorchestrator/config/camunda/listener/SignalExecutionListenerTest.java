/*

package in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener;

import java.util.HashMap;

import in.co.sixdee.bss.com.orderorchestrator.service.OrderStatusManager;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.xml.type.ModelElementType;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.ProcessVariableUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants.WorkFlowProcessVariables;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.WaitingProcessInfoRepository;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.core.AppInstanceIdManager;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Order;

@ExtendWith(MockitoExtension.class)

@RunWith(JUnitPlatform.class)
class SignalExecutionListenerTest {

	@Mock
	private OrderStatusManager orderStatusManager;

	@Mock
	private WaitingProcessInfoRepository	waitingProcessInfoRepository;

	@Mock
	private AppInstanceIdManager			getAppInstance;

	@Mock
	private ProcessVariableUtils			processVariableUtils;

	@Mock
	private GetDataFromCache				cache;

	@InjectMocks
	private SignalExecutionListener			signalExecutionListener;

	static OrderFlowContext					orderFlowContext;
	static DelegateExecution				execution;
	static FlowElement						bpmnModelElementInstance;
	static ModelElementType					elementType;
	static HashMap<String, String>			ngTableData;
	static CacheTableDataDTO				cacheTable;

	@BeforeAll
	static void setUpBeforeClass() throws Exception {
		execution = Mockito.mock(DelegateExecution.class);
		orderFlowContext = new OrderFlowContext();
		bpmnModelElementInstance = Mockito.mock(FlowElement.class);
		elementType = Mockito.mock(ModelElementType.class);
		orderFlowContext.setTraceId("27847867");
		orderFlowContext.setRequestId("276457365");
		var order = new Order();
		order.setOrderType("Onboarding");
		orderFlowContext.setOrder(order);
		cacheTable = new CacheTableDataDTO();
		ngTableData = new HashMap<String, String>();
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {}

	@BeforeEach
	void setUp() throws Exception {}

	@AfterEach
	void tearDown() throws Exception {}

	// @Test
	// void testNotifyDelegateExecution() throws Exception {
	// Mockito.doReturn("1").when(getAppInstance).getInstanceId();
	// Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
	// Mockito.doReturn(bpmnModelElementInstance).when(execution).getBpmnModelElementInstance();
	// Mockito.doReturn(elementType).when(bpmnModelElementInstance).getElementType();
	// Mockito.doReturn("intermediateCatchEvent").when(elementType).getTypeName();
	// Mockito.doReturn("start").when(execution).getEventName();
	// signalExecutionListener.notify(execution);
	// }
	//
	// @Test
	// void testNotifyDelegateExecutionAnotherType() throws Exception {
	// Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
	// Mockito.doReturn(bpmnModelElementInstance).when(execution).getBpmnModelElementInstance();
	// Mockito.doReturn(elementType).when(bpmnModelElementInstance).getElementType();
	// Mockito.doReturn("CatchEvent").when(elementType).getTypeName();
	// Mockito.doReturn("start").when(execution).getEventName();
	// signalExecutionListener.notify(execution);
	// }

	//@Test
	void testNotifyDelegateExecutionEnd() throws Exception {
		String entity = "200";
		ngTableData.put("STAGE_ID", "SOM_PROVISIONING");
		cacheTable.setNgTableData(ngTableData);
		Mockito.doReturn(cacheTable).when(cache).getCacheDetailsFromDBMap("STAGE_CONFIG_BY_ACTIVITY_ID_AND_ORDER_TYPE",
				orderFlowContext.getOrder().getOrderType() + "_" + "SOM_Provisioning");
		orderFlowContext.getAttributes().put("entityId", entity);
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(bpmnModelElementInstance).when(execution).getBpmnModelElementInstance();
		Mockito.doReturn(elementType).when(bpmnModelElementInstance).getElementType();
		Mockito.doReturn("SOM_Provisioning").when(execution).getCurrentActivityId();
		Mockito.doReturn("intermediateCatchEvent").when(elementType).getTypeName();
		Mockito.doReturn("end").when(execution).getEventName();
		signalExecutionListener.notify(execution);
	}

	//@Test
	void testNotifyDelegateExecutionNotEnd() throws Exception {
		String entity = "200";
		ngTableData.put("STAGE_ID", "SOM_PROVISIONING");
		cacheTable.setNgTableData(ngTableData);
		Mockito.doReturn(cacheTable).when(cache).getCacheDetailsFromDBMap("STAGE_CONFIG_BY_ACTIVITY_ID_AND_ORDER_TYPE",
				orderFlowContext.getOrder().getOrderType() + "_" + "SOM_Provisioning");
		orderFlowContext.getAttributes().put("entityId", entity);
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(bpmnModelElementInstance).when(execution).getBpmnModelElementInstance();
		Mockito.doReturn("SOM_Provisioning").when(execution).getCurrentActivityId();
		Mockito.doReturn(elementType).when(bpmnModelElementInstance).getElementType();
		Mockito.doReturn("intermediateCatchEvent").when(elementType).getTypeName();
		Mockito.doReturn("intermediate").when(execution).getEventName();
		signalExecutionListener.notify(execution);
	}

	//@Test
	void testNotifyDelegateExecutionEndElse() throws Exception {
		String entity = "200";
		ngTableData.put("STAGE_ID", "SOM_PROVISIONING");
		cacheTable.setNgTableData(ngTableData);
		Mockito.doReturn(cacheTable).when(cache).getCacheDetailsFromDBMap("STAGE_CONFIG_BY_ACTIVITY_ID_AND_ORDER_TYPE",
				orderFlowContext.getOrder().getOrderType() + "_" + "SOM_Provisioning");
		orderFlowContext.getAttributes().put("entityId", entity);
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(bpmnModelElementInstance).when(execution).getBpmnModelElementInstance();
		Mockito.doReturn(elementType).when(bpmnModelElementInstance).getElementType();
		Mockito.doReturn("SOM_Provisioning").when(execution).getCurrentActivityId();
		Mockito.doReturn("intermediateCatchEvent").when(elementType).getTypeName();
		Mockito.doReturn("end").when(execution).getEventName();
		signalExecutionListener.notify(execution);
	}

	//@Test
	void testNotifyDelegateExecutionCatch() throws Exception {
		orderFlowContext.getAttributes().put("entityId", null);
		ngTableData.put("STAGE_ID", "SOM_PROVISIONING");
		cacheTable.setNgTableData(ngTableData);
		Mockito.doReturn(cacheTable).when(cache).getCacheDetailsFromDBMap("STAGE_CONFIG_BY_ACTIVITY_ID_AND_ORDER_TYPE",
				orderFlowContext.getOrder().getOrderType() + "_" + "SOM_Provisioning");
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(bpmnModelElementInstance).when(execution).getBpmnModelElementInstance();
		Mockito.doReturn(elementType).when(bpmnModelElementInstance).getElementType();
		Mockito.doReturn("intermediateCatchEvent").when(elementType).getTypeName();
		Mockito.doReturn("SOM_Provisioning").when(execution).getCurrentActivityId();
		Mockito.doReturn("end").when(execution).getEventName();
		signalExecutionListener.notify(execution);
	}

}
*/
