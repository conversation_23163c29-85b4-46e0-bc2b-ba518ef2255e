/*
package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import java.util.List;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants.HttpConstants;

@RunWith(JUnitPlatform.class)
@ExtendWith(MockitoExtension.class)
class SchemaNotFoundExceptionTest {

	private StatusConstants.HttpConstants status;
	private String                        message;
	private List<String> errors;
	
	@InjectMocks
	private SchemaNotFoundException schemaNotFoundException;
	
	@BeforeAll
	static void setUpBeforeClass() throws Exception {
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
	}

	@BeforeEach
	void setUp() throws Exception {
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void testSchemaNotFoundExceptionHttpConstantsStringListOfString() {
		schemaNotFoundException=new SchemaNotFoundException(HttpConstants.INTERNAL_SERVER_ERROR,"SCHEMA VALIDATION FAILED",errors);

	}

	@Test
	void testSchemaNotFoundExceptionHttpConstantsStringString() {
		schemaNotFoundException=new SchemaNotFoundException(HttpConstants.INTERNAL_SERVER_ERROR,"SCHEMA VALIDATION FAILED","INTERNAL SERVER ERROR");

	}

	@Test
	void testSchemaNotFoundExceptionHttpConstantsString() {
		schemaNotFoundException=new SchemaNotFoundException(HttpConstants.INTERNAL_SERVER_ERROR,"SCHEMA VALIDATION FAILED");

		
	}

	@Test
	void testSchemaNotFoundExceptionString() {
		schemaNotFoundException=new SchemaNotFoundException("SCHEMA VALIDATION FAILED");
		
	}

	@Test
	void testGetStatus() {
		schemaNotFoundException.getStatus();

	}

	@Test
	void testSetStatus() {
		schemaNotFoundException.setStatus(HttpConstants.INTERNAL_SERVER_ERROR);
	}

	@Test
	void testGetMessage() {
		schemaNotFoundException.getMessage();

	}

	@Test
	void testSetMessage() {
		schemaNotFoundException.setMessage("SCHEMA VALIDATION FAILED");
	}

	@Test
	void testGetErrors() {
		schemaNotFoundException.getErrors();

	}

	@Test
	void testSetErrors() {
		schemaNotFoundException.setErrors(errors);
	}

}
*/
