/**
 *
 */
package in.co.sixdee.bss.com.orderorchestrator.web.rest.controller;

import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderCallBackService;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * <AUTHOR>
 *
 */
@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
class OrderOrchestratorControllerTest {

	@InjectMocks
	private OrderOrchestratorController orderOrchestratorController;

	@Mock
	private OrderCallBackService orderCallBackService;

	static OrderCallBack request;

	/**
	 * @throws java.lang.Exception
	 */
	@BeforeAll
	static void setUpBeforeClass() throws Exception {
		request = new OrderCallBack();
	}

	/**
	 * @throws java.lang.Exception
	 */
	@AfterAll
	static void tearDownAfterClass() throws Exception {
	}

	/**
	 * @throws java.lang.Exception
	 */
	@BeforeEach
	void setUp() throws Exception {
	}

	/**
	 * @throws java.lang.Exception
	 */
	@AfterEach
	void tearDown() throws Exception {
	}


	//@Test
	void testUpdateOrderStatus() {

		orderOrchestratorController.updateOrderStatus(request);

	}

	/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.web.rest.controller.OrderOrchestratorController#processInstanceModification(in.co.sixdee.bss.om.model.dto.WorkflowRequest)}.
	 */
	/*@Test
	void testProcessInstanceModification() {
		orderOrchestratorController.processInstanceModification(request);
	}*/
	@Test
	void testPaymentCallBack() {
		orderOrchestratorController.paymentCallBack(request);
	}

}
