/*
 * package in.co.sixdee.bss.com.orderorchestrator.config.util;
 * 
 * import java.util.ArrayList; import java.util.Collection; import
 * java.util.List;
 * 
 * import org.junit.jupiter.api.AfterAll; import
 * org.junit.jupiter.api.AfterEach; import org.junit.jupiter.api.Assertions;
 * import org.junit.jupiter.api.BeforeAll; import
 * org.junit.jupiter.api.BeforeEach; import org.junit.jupiter.api.Test; import
 * org.junit.jupiter.api.extension.ExtendWith; import
 * org.junit.platform.runner.JUnitPlatform; import org.junit.runner.RunWith;
 * import org.mockito.InjectMocks; import
 * org.mockito.junit.jupiter.MockitoExtension;
 * 
 * import com.fasterxml.jackson.core.type.TypeReference; import
 * com.fasterxml.jackson.databind.ObjectMapper; import
 * com.fasterxml.jackson.databind.PropertyNamingStrategy; import
 * com.google.gson.Gson;
 * 
 * import in.co.sixdee.bss.om.model.dto.OrderDTO; import
 * in.co.sixdee.bss.om.model.dto.ResponseWrapper;
 * 
 * @RunWith(JUnitPlatform.class)
 * 
 * @ExtendWith(MockitoExtension.class) class JsonUtilsTest {
 * 
 * @InjectMocks private JsonUtils jsonUtils; static String input; static
 * ResponseWrapper order;
 * 
 * @BeforeAll static void setUpBeforeClass() throws Exception {
 * 
 * }
 * 
 * @AfterAll static void tearDownAfterClass() throws Exception { }
 * 
 * @BeforeEach void setUp() throws Exception { order = new ResponseWrapper();
 * order.setMessage("Success"); input = new Gson().toJson(order); }
 * 
 * @AfterEach void tearDown() throws Exception { }
 * 
 * @Test void testMarshall() throws Exception { PropertyNamingStrategy name =
 * new PropertyNamingStrategy(); jsonUtils.marshall(null, name);
 * 
 * }
 * 
 * @Test void testUnmarshallStringTypeReferenceOfTCatch() throws Exception {
 * TypeReference TypeReference = new TypeReference<List<OrderDTO>>() { };
 * Assertions.assertThrows(Exception.class, () -> { Collection unmarshall =
 * jsonUtils.unmarshall(input, TypeReference);
 * 
 * });
 * 
 * }
 * 
 * @Test void testUnmarshallStringTypeReferenceOfT() throws Exception { OrderDTO
 * wrapper = new Gson().fromJson(input, OrderDTO.class); List<OrderDTO> wrappers
 * = new ArrayList<>(); wrappers.add(wrapper); String jsonArray = new
 * ObjectMapper().writeValueAsString(wrappers); TypeReference typeReference =
 * new TypeReference<java.util.Collection>() { };
 * jsonUtils.unmarshall(jsonArray, new TypeReference<List<OrderDTO>>() { }); }
 * 
 * @Test void testUnmarshallStringTypeReferenceOfTElse() throws Exception {
 * OrderDTO wrapper = new Gson().fromJson(input, OrderDTO.class); List<OrderDTO>
 * wrappers = new ArrayList<>(); wrappers.add(wrapper); TypeReference
 * typeReference = new TypeReference<java.util.Collection>() { };
 * jsonUtils.unmarshall(null, new TypeReference<List<OrderDTO>>() { }); }
 * 
 * @Test void testUnmarshallStringClassOfTNullValue() throws Exception {
 * OrderDTO unmarshall = jsonUtils.unmarshall(input, OrderDTO.class);
 * Assertions.assertNotNull(unmarshall); }
 * 
 * @Test void testUnmarshallStringClassOfT() throws Exception { OrderDTO
 * unmarshall = jsonUtils.unmarshall(null, OrderDTO.class);
 * Assertions.assertNull(unmarshall); }
 * 
 * @Test void testUnmarshallStringClassOfTCatch() throws Exception {
 * Assertions.assertThrows(Exception.class, () -> { jsonUtils.unmarshall(input,
 * Process.class); });
 * 
 * }
 * 
 * @Test void testmarshall() throws Exception { jsonUtils.marshall(null, null);
 * }
 * 
 * @Test void testIsJson() { String values = "{}"; jsonUtils.isJson(values); }
 * 
 * @Test void testIsJsonCatch() { String values = "{"; jsonUtils.isJson(values);
 * }
 * 
 * 
 * 
 * }
 */