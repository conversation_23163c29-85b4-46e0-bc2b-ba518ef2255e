/**
 * 
 *//*

package in.co.sixdee.bss.com.orderorchestrator.config;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

*/
/**
 * <AUTHOR>
 *
 *//*

@RunWith(JUnitPlatform.class)
@ExtendWith(MockitoExtension.class)
class JsonPathConfigTest {

	@InjectMocks
	private JsonPathConfig jsonPathConfig;

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@BeforeAll
	static void setUpBeforeClass() throws Exception {}

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@AfterAll
	static void tearDownAfterClass() throws Exception {}

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@BeforeEach
	void setUp() throws Exception {}

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@AfterEach
	void tearDown() throws Exception {}

	*/
/**
	 * Test method for {@link in.co.sixdee.bss.com.orderorchestrator.config.JsonPathConfig#init()}.
	 *//*

	@Test
	void testInit() {
		jsonPathConfig.init();
	}

}
*/
