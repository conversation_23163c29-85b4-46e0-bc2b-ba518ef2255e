package in.co.sixdee.bss.com.orderorchestrator.config.messaging;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.IOrderOrchestrator;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.RabbitMessageProcessingException;
import in.co.sixdee.bss.com.orderorchestrator.service.CancelOrderService;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.LinkedHashMap;
import java.util.function.Consumer;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
public class MessageListenerTest {


    @Mock
    private IOrderOrchestrator orderOrchestrator;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private MessageListener messageListener;

    @Mock
    private CancelOrderService cancelOrderService;

    private OrderFlowContext mockContext;

    @BeforeEach
    void setUp() {
        mockContext = mock(OrderFlowContext.class);
    }

    @Test
    void testReceiveOrderMessages_success() throws Exception {
        when(objectMapper.writeValueAsString(any())).thenReturn("{ \"orderId\": \"123\" }");

        Consumer<OrderFlowContext> consumer = messageListener.receiveOrderMessages();
        consumer.accept(mockContext);

        verify(objectMapper).writeValueAsString(mockContext);
        verify(orderOrchestrator).processRequest(mockContext);
    }

    @Test
    void testReceiveOrderMessages_exceptionDuringProcessing() throws Exception {
        when(objectMapper.writeValueAsString(any())).thenReturn("{ \"orderId\": \"123\" }");
        doThrow(new RuntimeException("Processing failed")).when(orderOrchestrator).processRequest(any());

        Consumer<OrderFlowContext> consumer = messageListener.receiveOrderMessages();

        RabbitMessageProcessingException thrown = Assertions.assertThrows(RabbitMessageProcessingException.class, () -> {
            consumer.accept(mockContext);
        });

        Assertions.assertEquals("Processing failed", thrown.getMessage());
    }

    @Test
    void testReceiveOrderMessages_exceptionDuringSerialization() throws Exception {
        when(objectMapper.writeValueAsString(any())).thenThrow(new JsonProcessingException("JSON error") {
        });

        Consumer<OrderFlowContext> consumer = messageListener.receiveOrderMessages();

        RabbitMessageProcessingException thrown = Assertions.assertThrows(RabbitMessageProcessingException.class, () -> {
            consumer.accept(mockContext);
        });

        Assertions.assertEquals("JSON error", thrown.getMessage());
    }

    @Test
    void testReceiveOrderMessages_CancelOrder() throws Exception {
        LinkedHashMap<String, String> attrs = new LinkedHashMap<>();
        attrs.put("cancelWithoutProcessId","true");
        when(mockContext.getAttributes()).thenReturn(attrs);

        when(objectMapper.writeValueAsString(any())).thenReturn("{ \"orderId\": \"123\" }");
        Consumer<OrderFlowContext> consumer = messageListener.receiveOrderMessages();
        consumer.accept(mockContext);
        verify(objectMapper).writeValueAsString(mockContext);
        verify(cancelOrderService).processCancelOrderRequest(mockContext);
    }

    @Test
    void testReceiveOrderMessages_CancelOrder_withRollBackProcessId() throws Exception {
        LinkedHashMap<String, String> attrs = new LinkedHashMap<>();
        attrs.put("rollbackProcessId","PortOutRollBack");
        when(mockContext.getAttributes()).thenReturn(attrs);

        when(objectMapper.writeValueAsString(any())).thenReturn("{ \"orderId\": \"123\" }");
        Consumer<OrderFlowContext> consumer = messageListener.receiveOrderMessages();
        consumer.accept(mockContext);
        verify(objectMapper).writeValueAsString(mockContext);
        verify(cancelOrderService).processCancelOrderRequest(mockContext);
    }
}
