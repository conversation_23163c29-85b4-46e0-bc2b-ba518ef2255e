/**
 * 
 *//*

package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.io.File;
import java.net.URI;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.camunda.spin.ProcessVars;
import in.co.sixdee.bss.com.orderorchestrator.config.connector.rest.WebClientConnector;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.jolt.JoltUtils;
import in.co.sixdee.bss.om.model.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Order;

*/
/**
 * <AUTHOR>
 *
 *//*

@RunWith(JUnitPlatform.class)
@ExtendWith(MockitoExtension.class)
class GenericExecutionDelegateTest {

	@InjectMocks
	private GenericExecutionDelegate	genericExecutionDelegate;
	
	@Mock
	JoltUtils					joltUtils;
	
	@Mock
	private WebClientConnector restConnector;
	
	@Mock
	GetDataFromCache cache;
	
	static AbstractDelegate abstractDelegate;

	static CacheTableDataDTO thirdPartyUrlConfig;
	static HashMap<String, String> thirdPartyCallDetails;
	static Map<String, Object> variables;
	static HashMap<String, String> ngTableData;
	static Map<String, String> headerMap;
	static OrderFlowContext				orderFlowContext;
	static String						request;
	static DelegateExecution			execution;
	static CallThirdPartyDTO captureResponse;
	static Order order;

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@BeforeAll
	static void setUpBeforeClass() throws Exception {
		execution = Mockito.mock(DelegateExecution.class);
		request = FileUtils.readFileToString(new File("src/test/resources/OnboardingRequest.json"));
	}

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@AfterAll
	static void tearDownAfterClass() throws Exception {
		orderFlowContext = null;
	}

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@BeforeEach
	void setUp() throws Exception {
		headerMap = new LinkedHashMap<String, String>();
		captureResponse = new CallThirdPartyDTO();
		orderFlowContext = new OrderFlowContext();
		orderFlowContext.setAttributes(new LinkedHashMap<>());
		
		
		var elementMap = new LinkedHashMap<String, Object>();
		
	
		
		orderFlowContext.setWorkflowData(new LinkedHashMap<>());
		orderFlowContext.getWorkflowData().put("workflowRequest", elementMap);
		orderFlowContext.getAttributes().put(GenericConstants.WORKITEM_REQUEST_ID, "387468765868");
		variables = new LinkedHashMap<String, Object>();
		
		thirdPartyUrlConfig = new CacheTableDataDTO();
		orderFlowContext = new OrderFlowContext();
		thirdPartyCallDetails = new HashMap<>();
		orderFlowContext.setAttributes(new LinkedHashMap<>());
		variables.put("workflowData", orderFlowContext);
		variables.put("thirdPartyId", "BILLING");
		order = new Order();
		variables.put("Status", "0");
		execution.setVariables(variables);
		ngTableData = new LinkedHashMap<String, String>();
	}

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@AfterEach
	void tearDown() throws Exception {}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.service.handlers.GenericExecutionDelegate#execute()}.
	 * 
	 * @throws Exception
	 *//*

	@Test
	void testExecute() throws Exception {
		order.setOrderType("Onboarding");
		orderFlowContext.setOrder(order);
		Mockito.doReturn(variables).when(execution).getVariables();
		Mockito.doReturn(orderFlowContext).when(execution).getVariable("workflowData");
		headerMap.put("userid", "1");
		ngTableData.put(CacheConstants.CacheFields.HEADERS.name(), "userid=1");
		ngTableData.put(CacheConstants.CacheFields.READ_TIMEOUT.name(), "3000");
		ngTableData.put(CacheConstants.CacheFields.CONNECTION_TIMEOUT.name(), "3000");
		ngTableData.put(CacheConstants.CacheFields.TRANSPORT_METHOD.name(), "GET");
		ngTableData.put("EXPECTED_HTTP_CODE", "200");
		
		ngTableData.put("ORDER_CODE", "OC_ONBOARDING");
		String updatedUrl = "http:".concat("/getOrderTypeById").concat("/" + "12345678").concat("/" + "3456789")
				.concat("/" + "ORD_ACK");
		ngTableData.put(CacheConstants.CacheFields.URL.name(), updatedUrl);
		
		thirdPartyUrlConfig.setNgTableData(ngTableData);
		
		captureResponse.setResponse("Onboarding");
		captureResponse.setResponseCode("200");
		Mockito.doReturn(captureResponse).when(restConnector).service("GET",new URI(updatedUrl), 3000, 3000,null, headerMap);
		Mockito.doReturn(thirdPartyUrlConfig).when(cache).getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(),
				"BILLING");
		genericExecutionDelegate.reqSpecKey = "BS_CreateProfile";
		genericExecutionDelegate.executionContext = orderFlowContext;
		genericExecutionDelegate.joltUtils = joltUtils;
		genericExecutionDelegate.thirdPartyCallDetails = ngTableData;
		genericExecutionDelegate.executionVariables = variables;
		genericExecutionDelegate.execute();
		Assertions.assertFalse(orderFlowContext.isError());
	}
	@Test
	void testExecuteNullThirdPartyDetails() throws Exception {
		order.setOrderType("Onboarding");
		orderFlowContext.setOrder(order);
		Mockito.doReturn(variables).when(execution).getVariables();
		Mockito.doReturn(orderFlowContext).when(execution).getVariable("workflowData");
		headerMap.put("userid", "1");
		ngTableData.put(CacheConstants.CacheFields.HEADERS.name(), "userid=1");
		ngTableData.put(CacheConstants.CacheFields.READ_TIMEOUT.name(), "3000");
		ngTableData.put(CacheConstants.CacheFields.CONNECTION_TIMEOUT.name(), "3000");
		ngTableData.put(CacheConstants.CacheFields.TRANSPORT_METHOD.name(), "GET");
		ngTableData.put("EXPECTED_HTTP_CODE", "200");
		
		ngTableData.put("ORDER_CODE", "OC_ONBOARDING");
		String updatedUrl = "http:".concat("/getOrderTypeById").concat("/" + "12345678").concat("/" + "3456789")
				.concat("/" + "ORD_ACK");
		ngTableData.put(CacheConstants.CacheFields.URL.name(), updatedUrl);
		
		thirdPartyUrlConfig.setNgTableData(ngTableData);
		
		captureResponse.setResponse("Onboarding");
		captureResponse.setResponseCode("200");
		Mockito.doReturn(null).when(restConnector).service("GET",new URI(updatedUrl), 3000, 3000,null, headerMap);
		Mockito.doReturn(thirdPartyUrlConfig).when(cache).getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(),
				"BILLING");
		genericExecutionDelegate.reqSpecKey = "BS_CreateProfile";
		genericExecutionDelegate.executionContext = orderFlowContext;
		genericExecutionDelegate.joltUtils = joltUtils;
		genericExecutionDelegate.thirdPartyCallDetails = ngTableData;
		genericExecutionDelegate.executionVariables = variables;
		genericExecutionDelegate.execute();
		Assertions.assertTrue(orderFlowContext.isError());
	}
	@Test
	void testExecuteErrorResponse() throws Exception {
		order.setOrderType("Onboarding");
		orderFlowContext.setOrder(order);
		Mockito.doReturn(variables).when(execution).getVariables();
		Mockito.doReturn(orderFlowContext).when(execution).getVariable("workflowData");
		headerMap.put("userid", "1");
		ngTableData.put(CacheConstants.CacheFields.HEADERS.name(), "userid=1");
		ngTableData.put(CacheConstants.CacheFields.READ_TIMEOUT.name(), "3000");
		ngTableData.put(CacheConstants.CacheFields.CONNECTION_TIMEOUT.name(), "3000");
		ngTableData.put(CacheConstants.CacheFields.TRANSPORT_METHOD.name(), "GET");
		ngTableData.put("ORDER_CODE", "OC_ONBOARDING");
		String updatedUrl = "http:".concat("/getOrderTypeById").concat("/" + "12345678").concat("/" + "3456789")
				.concat("/" + "ORD_ACK");
		ngTableData.put(CacheConstants.CacheFields.URL.name(), updatedUrl);
		
		thirdPartyUrlConfig.setNgTableData(ngTableData);
		
		captureResponse.setResponse("Onboarding");
		captureResponse.setResponseCode("20");
		Mockito.doReturn(captureResponse).when(restConnector).service("GET",new URI(updatedUrl), 3000, 3000,null, headerMap);
		Mockito.doReturn(thirdPartyUrlConfig).when(cache).getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(),
				"BILLING");
		genericExecutionDelegate.reqSpecKey = "BS_CreateProfile";
		genericExecutionDelegate.executionContext = orderFlowContext;
		genericExecutionDelegate.joltUtils = joltUtils;
		genericExecutionDelegate.thirdPartyCallDetails = ngTableData;
		genericExecutionDelegate.executionVariables = variables;
		genericExecutionDelegate.execute();
		Assertions.assertTrue(orderFlowContext.isError());
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate#execute(org.camunda.bpm.engine.delegate.DelegateExecution)}.
	 * @throws Exception 
	 *//*

	@Test
	void testExecuteDelegateExecution() throws Exception {
		orderFlowContext.setOrder(null);
		ProcessVars.orderFlowContext.on(execution).set(orderFlowContext);
		genericExecutionDelegate.executionContext = orderFlowContext;
		genericExecutionDelegate.reqSpecKey = "BS_CreateProfile";
		genericExecutionDelegate.execute();
		Assertions.assertTrue(orderFlowContext.isError());
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate#initHandler(org.camunda.bpm.engine.delegate.DelegateExecution)}.
	 *//*

	@Test
	void testInitHandler() {}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate#close(org.camunda.bpm.engine.delegate.DelegateExecution)}.
	 *//*

	@Test
	void testClose() {}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractHandler#initThirdPartyCallDetails(java.lang.String, java.lang.String)}.
	 *//*

	@Test
	void testInitThirdPartyCallDetails() {}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractHandler#getHeaderMap(java.lang.String)}.
	 *//*

	@Test
	void testGetHeaderMap() {}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractHandler#transformHeaders(java.util.Map)}.
	 *//*

	@Test
	void testTransformHeaders() {}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractHandler#resolveUriTokens(java.lang.String)}.
	 *//*

	@Test
	void testResolveUriTokens() {}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractHandler#validateResponse(in.co.sixdee.bss.om.model.dto.OrderFlowContext, java.util.Map, java.lang.String, in.co.sixdee.bss.om.model.dto.CallThirdPartyDTO)}.
	 *//*

	@Test
	void testValidateResponse() {}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractHandler#processThirdPartyResponse(java.util.Map, java.lang.String)}.
	 *//*

	@Test
	void testProcessThirdPartyResponse() {}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractHandler#formatUri(java.lang.String, in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractHandler.Param)}.
	 *//*

	@Test
	void testFormatUri() {}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractHandler#callViaFeign(java.lang.String)}.
	 *//*

	@Test
	void testCallViaFeign() {}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractHandler#callThirdParty(java.lang.String)}.
	 *//*

	@Test
	void testCallThirdParty() {}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractHandler#modifyWorkflowData(java.lang.String, java.lang.String, boolean, java.lang.String)}.
	 *//*

	@Test
	void testModifyWorkflowData() {}

}
*/
