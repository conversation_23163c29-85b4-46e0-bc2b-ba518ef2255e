package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import in.co.sixdee.bss.com.orderorchestrator.config.util.XmlRequestBuilder;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.mnp.NotifyPortOutValidation;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.LinkedHashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
public class NotifyPortOutValidationTest {

    @InjectMocks
    NotifyPortOutValidation notifyPortOutValidation;
    @Mock
    XmlRequestBuilder xmlRequestBuilder;

    GetDataFromCache cache;

    OrderFlowContext executionContext;

    @BeforeEach
    void setUp() {
        executionContext = new OrderFlowContext();
        notifyPortOutValidation.executionContext = executionContext;
        notifyPortOutValidation.orderType = "MNPPortOut";
        notifyPortOutValidation.reqSpecKey = "PortOut";

        cache = Mockito.mock(GetDataFromCache.class);
        notifyPortOutValidation.cache = cache;

        //if not able to access direcly , use the reflection util to set the value to variable
       // ReflectionTestUtils.setField(notifyPortOutValidation, "orderType", "MNPPortOut");

    }

    @Test
    void testNotifyPortOut_contractValidation() {
        LinkedHashMap<String, String> attrs = new LinkedHashMap<>();
        attrs.put("contractValidationFailed", "true");
        executionContext.setAttributes(attrs);
        String configuredRequest = "<?xml version=\"1.0\"?>\n" +
                "<S:Envelope xmlns:env=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\"><env:Header><wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"><wsse:UsernameToken>UsernameToken-E35D486525CC9C0C7B142535531967512<wsse:Username>mvne</wsse:Username> <wsse:Password Type=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText\">m^23Apr1&</wsse:Password> </wsse:UsernameToken></wsse:Security><ns16:sdpServiceHeaders xmlns:ns16=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\"><ns16:applicationIdentity>OMS</ns16:applicationIdentity><ns16:systemIdentity>BCC</ns16:systemIdentity><ns16:consumerReferenceId>server</ns16:consumerReferenceId><ns16:consumerReferenceDateTime>2018-09-17T17:35:34</ns16:consumerReferenceDateTime><ns16:csrIdentity>server</ns16:csrIdentity><ns16:userIdentity>server</ns16:userIdentity><ns16:languageCode>EN</ns16:languageCode><ns16:countryCode>US</ns16:countryCode></ns16:sdpServiceHeaders></env:Header><S:Body><ns4:notifyPortOutValidation xmlns:ns4=\"http://group.singtel.com/manageporting-types/v1\" xmlns:ns3=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\" xmlns:ns2=\"http://group.singtel.com/customerproduct-common/types/v1\" xmlns=\"http://group.singtel.com/core/types/v1\"><ns4:requestID><ns4:id>003-7113183</ns4:id></ns4:requestID><ns4:referenceID><ns4:id>666-S-453-20250424-J003</ns4:id></ns4:referenceID><ns4:result><ns4:serviceID><ns2:serviceID>6593220978</ns2:serviceID></ns4:serviceID><ns4:resultCode>3</ns4:resultCode><ns4:resultCodeSpecified>false</ns4:resultCodeSpecified></ns4:result></ns4:notifyPortOutValidation></S:Body></S:Envelope>\n";
        when(xmlRequestBuilder.buildRequest(notifyPortOutValidation.orderType + "_contractValidationFailed", executionContext, null)).thenReturn(configuredRequest);
        String request = notifyPortOutValidation.getRequestBasedOnValidationError();
        assertEquals(configuredRequest, request);
    }

    @Test
    void testNotifyPortOut_noAttribute() {
        LinkedHashMap<String, String> attrs = new LinkedHashMap<>();
        executionContext.setAttributes(attrs);
        String request = notifyPortOutValidation.getRequestBasedOnValidationError();
        assertNull(request);
    }

    @Test
    void testNotifyPortOut_Success() throws Exception {
        LinkedHashMap<String, String> attrs = new LinkedHashMap<>();
        executionContext.setAttributes(attrs);
        CallThirdPartyDTO mockDto = new CallThirdPartyDTO();
        mockDto.setResponse("transactionStatusCode>SUCCESS");
        mockDto.setResponseCode("200");
        String configuredRequest = "<?xml version=\"1.0\"?>\n" +
                "<S:Envelope xmlns:env=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\"><env:Header><wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"><wsse:UsernameToken>UsernameToken-E35D486525CC9C0C7B142535531967512<wsse:Username>mvne</wsse:Username> <wsse:Password Type=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText\">m^23Apr1&</wsse:Password> </wsse:UsernameToken></wsse:Security><ns16:sdpServiceHeaders xmlns:ns16=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\"><ns16:applicationIdentity>OMS</ns16:applicationIdentity><ns16:systemIdentity>BCC</ns16:systemIdentity><ns16:consumerReferenceId>server</ns16:consumerReferenceId><ns16:consumerReferenceDateTime>2018-09-17T17:35:34</ns16:consumerReferenceDateTime><ns16:csrIdentity>server</ns16:csrIdentity><ns16:userIdentity>server</ns16:userIdentity><ns16:languageCode>EN</ns16:languageCode><ns16:countryCode>US</ns16:countryCode></ns16:sdpServiceHeaders></env:Header><S:Body><ns4:notifyPortOutValidation xmlns:ns4=\"http://group.singtel.com/manageporting-types/v1\" xmlns:ns3=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\" xmlns:ns2=\"http://group.singtel.com/customerproduct-common/types/v1\" xmlns=\"http://group.singtel.com/core/types/v1\"><ns4:requestID><ns4:id>003-7113183</ns4:id></ns4:requestID><ns4:referenceID><ns4:id>666-S-453-20250424-J003</ns4:id></ns4:referenceID><ns4:result><ns4:serviceID><ns2:serviceID>6593220978</ns2:serviceID></ns4:serviceID><ns4:resultCode>3</ns4:resultCode><ns4:resultCodeSpecified>false</ns4:resultCodeSpecified></ns4:result></ns4:notifyPortOutValidation></S:Body></S:Envelope>\n";
        when(xmlRequestBuilder.buildRequest(notifyPortOutValidation.orderType + "_" + notifyPortOutValidation.reqSpecKey, executionContext, null)).thenReturn(configuredRequest);
        NotifyPortOutValidation spyNotify = Mockito.spy(notifyPortOutValidation);
        Method method = spyNotify.getClass().getDeclaredMethod("execute");
        method.setAccessible(true);
        Mockito.doReturn(mockDto).when(spyNotify).callThirdParty(configuredRequest);
        method.invoke(spyNotify);
        assertEquals("200", mockDto.getResponseCode());
    }

    @Test
    void testNotifyPortOut_CallThirdPartyDtoNull() throws Exception {
        LinkedHashMap<String, String> attrs = new LinkedHashMap<>();
        executionContext.setAttributes(attrs);
        String configuredRequest = "<?xml version=\"1.0\"?>\n" +
                "<S:Envelope xmlns:env=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\"><env:Header><wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"><wsse:UsernameToken>UsernameToken-E35D486525CC9C0C7B142535531967512<wsse:Username>mvne</wsse:Username> <wsse:Password Type=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText\">m^23Apr1&</wsse:Password> </wsse:UsernameToken></wsse:Security><ns16:sdpServiceHeaders xmlns:ns16=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\"><ns16:applicationIdentity>OMS</ns16:applicationIdentity><ns16:systemIdentity>BCC</ns16:systemIdentity><ns16:consumerReferenceId>server</ns16:consumerReferenceId><ns16:consumerReferenceDateTime>2018-09-17T17:35:34</ns16:consumerReferenceDateTime><ns16:csrIdentity>server</ns16:csrIdentity><ns16:userIdentity>server</ns16:userIdentity><ns16:languageCode>EN</ns16:languageCode><ns16:countryCode>US</ns16:countryCode></ns16:sdpServiceHeaders></env:Header><S:Body><ns4:notifyPortOutValidation xmlns:ns4=\"http://group.singtel.com/manageporting-types/v1\" xmlns:ns3=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\" xmlns:ns2=\"http://group.singtel.com/customerproduct-common/types/v1\" xmlns=\"http://group.singtel.com/core/types/v1\"><ns4:requestID><ns4:id>003-7113183</ns4:id></ns4:requestID><ns4:referenceID><ns4:id>666-S-453-20250424-J003</ns4:id></ns4:referenceID><ns4:result><ns4:serviceID><ns2:serviceID>6593220978</ns2:serviceID></ns4:serviceID><ns4:resultCode>3</ns4:resultCode><ns4:resultCodeSpecified>false</ns4:resultCodeSpecified></ns4:result></ns4:notifyPortOutValidation></S:Body></S:Envelope>\n";
        when(xmlRequestBuilder.buildRequest(notifyPortOutValidation.orderType + "_" + notifyPortOutValidation.reqSpecKey, executionContext, null))
                .thenReturn(configuredRequest);
        NotifyPortOutValidation spyNotify = Mockito.spy(notifyPortOutValidation);
        Method method = spyNotify.getClass().getDeclaredMethod("execute");
        method.setAccessible(true);
        Mockito.doReturn(null).when(spyNotify).callThirdParty(configuredRequest);
        method.invoke(spyNotify);
    }

    @Test
    void testNotifyPortOut_EmptyResponse() throws Exception {
        LinkedHashMap<String, String> attrs = new LinkedHashMap<>();
        executionContext.setAttributes(attrs);
        CallThirdPartyDTO mockDto = new CallThirdPartyDTO();
        mockDto.setResponse(null);
        mockDto.setResponseCode("200");
        String configuredRequest = "<?xml version=\"1.0\"?>\n" +
                "<S:Envelope xmlns:env=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\"><env:Header><wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"><wsse:UsernameToken>UsernameToken-E35D486525CC9C0C7B142535531967512<wsse:Username>mvne</wsse:Username> <wsse:Password Type=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText\">m^23Apr1&</wsse:Password> </wsse:UsernameToken></wsse:Security><ns16:sdpServiceHeaders xmlns:ns16=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\"><ns16:applicationIdentity>OMS</ns16:applicationIdentity><ns16:systemIdentity>BCC</ns16:systemIdentity><ns16:consumerReferenceId>server</ns16:consumerReferenceId><ns16:consumerReferenceDateTime>2018-09-17T17:35:34</ns16:consumerReferenceDateTime><ns16:csrIdentity>server</ns16:csrIdentity><ns16:userIdentity>server</ns16:userIdentity><ns16:languageCode>EN</ns16:languageCode><ns16:countryCode>US</ns16:countryCode></ns16:sdpServiceHeaders></env:Header><S:Body><ns4:notifyPortOutValidation xmlns:ns4=\"http://group.singtel.com/manageporting-types/v1\" xmlns:ns3=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\" xmlns:ns2=\"http://group.singtel.com/customerproduct-common/types/v1\" xmlns=\"http://group.singtel.com/core/types/v1\"><ns4:requestID><ns4:id>003-7113183</ns4:id></ns4:requestID><ns4:referenceID><ns4:id>666-S-453-20250424-J003</ns4:id></ns4:referenceID><ns4:result><ns4:serviceID><ns2:serviceID>6593220978</ns2:serviceID></ns4:serviceID><ns4:resultCode>3</ns4:resultCode><ns4:resultCodeSpecified>false</ns4:resultCodeSpecified></ns4:result></ns4:notifyPortOutValidation></S:Body></S:Envelope>\n";
        when(xmlRequestBuilder.buildRequest(notifyPortOutValidation.orderType + "_" + notifyPortOutValidation.reqSpecKey, executionContext, null)).thenReturn(configuredRequest);
        NotifyPortOutValidation spyNotify = Mockito.spy(notifyPortOutValidation);
        Method method = spyNotify.getClass().getDeclaredMethod("execute");
        method.setAccessible(true);
        Mockito.doReturn(mockDto).when(spyNotify).callThirdParty(configuredRequest);
        method.invoke(spyNotify);
    }

    @Test
    void testNotifyPortOut_FaultResponseWithProperXml() throws Exception {
        LinkedHashMap<String, String> attrs = new LinkedHashMap<>();
        executionContext.setAttributes(attrs);
        HashMap<String, String> errorDetails = new HashMap<>();
        errorDetails.put("SDP_1234", "Postpaid line with this MSISDN is not active in Singtel");
        errorDetails.put("ERROR_DESC", "Postpaid line with this MSISDN is not active in Singtel");
        CacheTableDataDTO errorMessageConfig = new CacheTableDataDTO();
        errorMessageConfig.setNgTableData(errorDetails);

        CallThirdPartyDTO mockDto = new CallThirdPartyDTO();

        String configuredRequest = "<?xml version=\"1.0\"?>\n" +
                "<S:Envelope xmlns:env=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\"><env:Header><wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"><wsse:UsernameToken>UsernameToken-E35D486525CC9C0C7B142535531967512<wsse:Username>mvne</wsse:Username> <wsse:Password Type=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText\">m^23Apr1&</wsse:Password> </wsse:UsernameToken></wsse:Security><ns16:sdpServiceHeaders xmlns:ns16=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\"><ns16:applicationIdentity>OMS</ns16:applicationIdentity><ns16:systemIdentity>BCC</ns16:systemIdentity><ns16:consumerReferenceId>server</ns16:consumerReferenceId><ns16:consumerReferenceDateTime>2018-09-17T17:35:34</ns16:consumerReferenceDateTime><ns16:csrIdentity>server</ns16:csrIdentity><ns16:userIdentity>server</ns16:userIdentity><ns16:languageCode>EN</ns16:languageCode><ns16:countryCode>US</ns16:countryCode></ns16:sdpServiceHeaders></env:Header><S:Body><ns4:notifyPortOutValidation xmlns:ns4=\"http://group.singtel.com/manageporting-types/v1\" xmlns:ns3=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\" xmlns:ns2=\"http://group.singtel.com/customerproduct-common/types/v1\" xmlns=\"http://group.singtel.com/core/types/v1\"><ns4:requestID><ns4:id>003-7113183</ns4:id></ns4:requestID><ns4:referenceID><ns4:id>666-S-453-20250424-J003</ns4:id></ns4:referenceID><ns4:result><ns4:serviceID><ns2:serviceID>6593220978</ns2:serviceID></ns4:serviceID><ns4:resultCode>3</ns4:resultCode><ns4:resultCodeSpecified>false</ns4:resultCodeSpecified></ns4:result></ns4:notifyPortOutValidation></S:Body></S:Envelope>\n";
        String response = "<?xml version=\"1.0\"?>\n" +
                "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\"><soapenv:Body><soapenv:Fault><faultcode>SDP_1234</faultcode><faultstring>Invalid Subscriber ID</faultstring></soapenv:Fault></soapenv:Body></soapenv:Envelope>";
        mockDto.setResponse(response);
        mockDto.setResponseCode("200");
        when(xmlRequestBuilder.buildRequest(notifyPortOutValidation.orderType + "_" + notifyPortOutValidation.reqSpecKey, executionContext, null)).thenReturn(configuredRequest);
        NotifyPortOutValidation spyNotify = Mockito.spy(notifyPortOutValidation);

       /* Field cacheField = NotifyPortOutValidation.class.getDeclaredField("cache");
        cacheField.setAccessible(true);
        cacheField.set(spyNotify, cache);*/

        when(cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.MNP_ERROR_CODE_MAPPING.toString(), "SDP_1234"))
                .thenReturn(errorMessageConfig);

        Method method = spyNotify.getClass().getDeclaredMethod("execute");
        method.setAccessible(true);
        Mockito.doReturn(mockDto).when(spyNotify).callThirdParty(configuredRequest);
        method.invoke(spyNotify);
    }

    @Test
    void testNotifyPortOut_FaultResponseWithProperXml_noErrorDescFromCache() throws Exception {
        LinkedHashMap<String, String> attrs = new LinkedHashMap<>();
        executionContext.setAttributes(attrs);

        HashMap<String, String> errorDetails = new HashMap<>();
        errorDetails.put("SDP_1234", "Postpaid line with this MSISDN is not active in Singtel");
        CacheTableDataDTO errorMessageConfig = new CacheTableDataDTO();
        errorMessageConfig.setNgTableData(errorDetails);

        CallThirdPartyDTO mockDto = new CallThirdPartyDTO();
        String response = "<?xml version=\"1.0\"?>\n" +
                "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\"><soapenv:Body><soapenv:Fault><faultcode>SDP_1234</faultcode><faultstring>Invalid Subscriber ID</faultstring></soapenv:Fault></soapenv:Body></soapenv:Envelope>";
        mockDto.setResponse(response);
        mockDto.setResponseCode("200");

        String configuredRequest = "<?xml version=\"1.0\"?>\n" +
                "<S:Envelope xmlns:env=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\"><env:Header><wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"><wsse:UsernameToken>UsernameToken-E35D486525CC9C0C7B142535531967512<wsse:Username>mvne</wsse:Username> <wsse:Password Type=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText\">m^23Apr1&</wsse:Password> </wsse:UsernameToken></wsse:Security><ns16:sdpServiceHeaders xmlns:ns16=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\"><ns16:applicationIdentity>OMS</ns16:applicationIdentity><ns16:systemIdentity>BCC</ns16:systemIdentity><ns16:consumerReferenceId>server</ns16:consumerReferenceId><ns16:consumerReferenceDateTime>2018-09-17T17:35:34</ns16:consumerReferenceDateTime><ns16:csrIdentity>server</ns16:csrIdentity><ns16:userIdentity>server</ns16:userIdentity><ns16:languageCode>EN</ns16:languageCode><ns16:countryCode>US</ns16:countryCode></ns16:sdpServiceHeaders></env:Header><S:Body><ns4:notifyPortOutValidation xmlns:ns4=\"http://group.singtel.com/manageporting-types/v1\" xmlns:ns3=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\" xmlns:ns2=\"http://group.singtel.com/customerproduct-common/types/v1\" xmlns=\"http://group.singtel.com/core/types/v1\"><ns4:requestID><ns4:id>003-7113183</ns4:id></ns4:requestID><ns4:referenceID><ns4:id>666-S-453-20250424-J003</ns4:id></ns4:referenceID><ns4:result><ns4:serviceID><ns2:serviceID>6593220978</ns2:serviceID></ns4:serviceID><ns4:resultCode>3</ns4:resultCode><ns4:resultCodeSpecified>false</ns4:resultCodeSpecified></ns4:result></ns4:notifyPortOutValidation></S:Body></S:Envelope>\n";
        when(xmlRequestBuilder.buildRequest(notifyPortOutValidation.orderType + "_" + notifyPortOutValidation.reqSpecKey, executionContext, null)).thenReturn(configuredRequest);
        NotifyPortOutValidation spyNotify = Mockito.spy(notifyPortOutValidation);

        /*Field cacheField = NotifyPortOutValidation.class.getDeclaredField("cache");
        cacheField.setAccessible(true);
        cacheField.set(spyNotify, cache);*/
        when(cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.MNP_ERROR_CODE_MAPPING.toString(), "SDP_1234"))
                .thenReturn(errorMessageConfig);

        Method method = spyNotify.getClass().getDeclaredMethod("execute");
        method.setAccessible(true);
        Mockito.doReturn(mockDto).when(spyNotify).callThirdParty(configuredRequest);
        method.invoke(spyNotify);
    }

    @Test
    void testNotifyPortOut_FaultResponseWithInvalidXml() throws Exception {
        LinkedHashMap<String, String> attrs = new LinkedHashMap<>();
        executionContext.setAttributes(attrs);

        CallThirdPartyDTO mockDto = new CallThirdPartyDTO();
        String response = "<Fault>\n" +
                "    <faultcode>ERR001</faultcode\n" +
                "    <faultstring>Invalid Request</faultstring>\n" +
                "</Fault>";
        mockDto.setResponse(response);
        mockDto.setResponseCode("200");
        String configuredRequest = "<?xml version=\"1.0\"?>\n" +
                "<S:Envelope xmlns:env=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\"><env:Header><wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"><wsse:UsernameToken>UsernameToken-E35D486525CC9C0C7B142535531967512<wsse:Username>mvne</wsse:Username> <wsse:Password Type=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText\">m^23Apr1&</wsse:Password> </wsse:UsernameToken></wsse:Security><ns16:sdpServiceHeaders xmlns:ns16=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\"><ns16:applicationIdentity>OMS</ns16:applicationIdentity><ns16:systemIdentity>BCC</ns16:systemIdentity><ns16:consumerReferenceId>server</ns16:consumerReferenceId><ns16:consumerReferenceDateTime>2018-09-17T17:35:34</ns16:consumerReferenceDateTime><ns16:csrIdentity>server</ns16:csrIdentity><ns16:userIdentity>server</ns16:userIdentity><ns16:languageCode>EN</ns16:languageCode><ns16:countryCode>US</ns16:countryCode></ns16:sdpServiceHeaders></env:Header><S:Body><ns4:notifyPortOutValidation xmlns:ns4=\"http://group.singtel.com/manageporting-types/v1\" xmlns:ns3=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\" xmlns:ns2=\"http://group.singtel.com/customerproduct-common/types/v1\" xmlns=\"http://group.singtel.com/core/types/v1\"><ns4:requestID><ns4:id>003-7113183</ns4:id></ns4:requestID><ns4:referenceID><ns4:id>666-S-453-20250424-J003</ns4:id></ns4:referenceID><ns4:result><ns4:serviceID><ns2:serviceID>6593220978</ns2:serviceID></ns4:serviceID><ns4:resultCode>3</ns4:resultCode><ns4:resultCodeSpecified>false</ns4:resultCodeSpecified></ns4:result></ns4:notifyPortOutValidation></S:Body></S:Envelope>\n";
        when(xmlRequestBuilder.buildRequest(notifyPortOutValidation.orderType + "_" + notifyPortOutValidation.reqSpecKey, executionContext, null)).thenReturn(configuredRequest);
        NotifyPortOutValidation spyNotify = Mockito.spy(notifyPortOutValidation);

        Method method = spyNotify.getClass().getDeclaredMethod("execute");
        method.setAccessible(true);
        Mockito.doReturn(mockDto).when(spyNotify).callThirdParty(configuredRequest);
        method.invoke(spyNotify);
    }

    @Test
    void testNotifyPortOut_requestNull() throws Exception {
        LinkedHashMap<String, String> attrs = new LinkedHashMap<>();
        executionContext.setAttributes(attrs);
        when(xmlRequestBuilder.buildRequest(notifyPortOutValidation.orderType + "_" + notifyPortOutValidation.reqSpecKey, executionContext, null)).thenReturn(null);
        NotifyPortOutValidation spyNotify = Mockito.spy(notifyPortOutValidation);
        Method method = spyNotify.getClass().getDeclaredMethod("execute");
        method.setAccessible(true);
        method.invoke(spyNotify);
    }


    @Test
    void testNotifyPortOut_FaultResponseWithProperXml_withNofaultCode() throws Exception {
        LinkedHashMap<String, String> attrs = new LinkedHashMap<>();
        executionContext.setAttributes(attrs);

        CallThirdPartyDTO mockDto = new CallThirdPartyDTO();
        String response = "<?xml version=\"1.0\"?>\n" +
                "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\"><soapenv:Body><soapenv:Fault><faultname>SDP_1234</faultname><faultstring>Invalid Subscriber ID</faultstring></soapenv:Fault></soapenv:Body></soapenv:Envelope>";
        mockDto.setResponse(response);
        mockDto.setResponseCode("200");

        String configuredRequest = "<?xml version=\"1.0\"?>\n" +
                "<S:Envelope xmlns:env=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:S=\"http://schemas.xmlsoap.org/soap/envelope/\"><env:Header><wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"><wsse:UsernameToken>UsernameToken-E35D486525CC9C0C7B142535531967512<wsse:Username>mvne</wsse:Username> <wsse:Password Type=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText\">m^23Apr1&</wsse:Password> </wsse:UsernameToken></wsse:Security><ns16:sdpServiceHeaders xmlns:ns16=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\"><ns16:applicationIdentity>OMS</ns16:applicationIdentity><ns16:systemIdentity>BCC</ns16:systemIdentity><ns16:consumerReferenceId>server</ns16:consumerReferenceId><ns16:consumerReferenceDateTime>2018-09-17T17:35:34</ns16:consumerReferenceDateTime><ns16:csrIdentity>server</ns16:csrIdentity><ns16:userIdentity>server</ns16:userIdentity><ns16:languageCode>EN</ns16:languageCode><ns16:countryCode>US</ns16:countryCode></ns16:sdpServiceHeaders></env:Header><S:Body><ns4:notifyPortOutValidation xmlns:ns4=\"http://group.singtel.com/manageporting-types/v1\" xmlns:ns3=\"http://group.singtel.com/enterpriseapplicationintegration/common-types/v1\" xmlns:ns2=\"http://group.singtel.com/customerproduct-common/types/v1\" xmlns=\"http://group.singtel.com/core/types/v1\"><ns4:requestID><ns4:id>003-7113183</ns4:id></ns4:requestID><ns4:referenceID><ns4:id>666-S-453-20250424-J003</ns4:id></ns4:referenceID><ns4:result><ns4:serviceID><ns2:serviceID>6593220978</ns2:serviceID></ns4:serviceID><ns4:resultCode>3</ns4:resultCode><ns4:resultCodeSpecified>false</ns4:resultCodeSpecified></ns4:result></ns4:notifyPortOutValidation></S:Body></S:Envelope>\n";
        when(xmlRequestBuilder.buildRequest(notifyPortOutValidation.orderType + "_" + notifyPortOutValidation.reqSpecKey, executionContext, null)).thenReturn(configuredRequest);
        NotifyPortOutValidation spyNotify = Mockito.spy(notifyPortOutValidation);

        Method method = spyNotify.getClass().getDeclaredMethod("execute");
        method.setAccessible(true);
        Mockito.doReturn(mockDto).when(spyNotify).callThirdParty(configuredRequest);
        method.invoke(spyNotify);
    }
}

