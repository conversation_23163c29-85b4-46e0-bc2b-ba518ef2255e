/*
package in.co.sixdee.bss.com.orderorchestrator.service;

import java.io.File;
import java.util.HashMap;
import java.util.LinkedHashMap;

import org.apache.commons.io.FileUtils;
import org.camunda.bpm.engine.RuntimeService;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.EdrConfig;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.WorkFlowUtil;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonExceptionResponse;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.EntityValidationException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.RestCallFailureException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.SchemaNotFoundException;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.WaitingProcessInfoEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.WaitingProcessInfoRepository;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Order;
import nl.jqno.equalsverifier.EqualsVerifier;
import nl.jqno.equalsverifier.Warning;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
class OrderOrchestratorImplTest {

	@InjectMocks
	OrderOrchestratorImpl			orderOrchestratorImpl;

	@Mock
	EdrConfig						edrConfig;

	@Mock
	GetDataFromCache				cache;

	@Mock
	WorkFlowUtil					workFlowutil;

	@Mock
	RuntimeService					runtimeService;

	@Mock
	WaitingProcessInfoRepository	waitingProcessInfoRepository;

	@Mock
	OrderEnrichment					orderEnrichment;

	static OrderFlowContext			orderFlowContext;
	static Order					order;
	static CacheTableDataDTO		orderTypeMapping;
	static HashMap<String, String>	ngTableData;
	static WaitingProcessInfoEntity	waitingProcessInfo;
	static HashMap<String, Object>	workflowData;
	static String					request;

	@BeforeAll
	static void setUpBeforeClass() throws Exception {}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
		orderFlowContext = null;
		orderTypeMapping = null;
		order = null;
		ngTableData = null;
		waitingProcessInfo = null;
		workflowData = null;
	}

	@BeforeEach
	void setUp() throws Exception {
		orderFlowContext = new OrderFlowContext();
		orderFlowContext.setAttributes(new LinkedHashMap<>());
		order = new Order();
		orderTypeMapping = new CacheTableDataDTO();
		ngTableData = new HashMap<>();
		waitingProcessInfo = new WaitingProcessInfoEntity();
		workflowData = new HashMap<>();
		request = FileUtils.readFileToString(new File("src/test/resources/BS_AddSubscription_Request.json"));
	}

	@AfterEach
	void tearDown() throws Exception {}

	*/
/*@Test
	void testProcessRequest() {
		Mockito.doReturn(true).when(sndService).checkSndWorkflowTriggerReqd(orderFlowContext);
		ngTableData.put("BPMN_PROCESS_ID", "Onboarding");
		orderTypeMapping.setNgTableData(ngTableData);
		Mockito.doReturn(orderTypeMapping).when(cache).getCacheDetailsFromDBMap("COM_ORDER_TYPE_CONFIG", "Onboarding");
		order.setOrderType("Onboarding");
		orderFlowContext.setOrder(order);
		orderOrchestratorImpl.processRequest(orderFlowContext);

	}*//*


	@Test
	void testProcessRequestException() {

		ngTableData.put("PROCESS_ID", "Onboarding");
		orderTypeMapping.setNgTableData(ngTableData);
		Mockito.doReturn(orderTypeMapping).when(cache).getCacheDetailsFromDBMap("COM_ORDER_TYPE_CONFIG", "Onboarding");
		order.setOrderType("Onboarding");
		orderFlowContext.setOrder(order);
		Assertions.assertThrows(CommonException.class, () -> {
			orderOrchestratorImpl.processRequest(orderFlowContext);
		});

	}

	@Test
	void testFindProcessId() {
		ngTableData.put("BPMN_PROCESS_ID", "Onboarding");
		order.setOrderType("Onboarding");
		orderFlowContext.setOrder(order);
		Assertions.assertThrows(CommonException.class, () -> {
			orderOrchestratorImpl.processRequest(null);
		});

	}

	@Test
	public void equalsRestCallFailureException() {
		Assertions.assertThrows(AssertionError.class, () -> {
			EqualsVerifier.forClass(RestCallFailureException.class).suppress(Warning.STRICT_INHERITANCE).verify();
		});
	}

	@Test
	public void equalsCommonExceptionResponse() {
		Assertions.assertThrows(AssertionError.class, () -> {
			EqualsVerifier.forClass(CommonExceptionResponse.class).suppress(Warning.STRICT_INHERITANCE).verify();
		});
	}

	@Test
	public void equalsPayloadValidationException() {
		Assertions.assertThrows(AssertionError.class, () -> {
			EqualsVerifier.forClass(EntityValidationException.class).suppress(Warning.STRICT_INHERITANCE).verify();
		});
	}

	@Test
	public void equalsCommonException() {
		Assertions.assertThrows(AssertionError.class, () -> {
			EqualsVerifier.forClass(CommonException.class).suppress(Warning.STRICT_INHERITANCE).verify();
		});
	}

	@Test
	public void equalsSchemaNotFoundException() {
		Assertions.assertThrows(AssertionError.class, () -> {
			EqualsVerifier.forClass(SchemaNotFoundException.class).suppress(Warning.STRICT_INHERITANCE).verify();
		});
	}

}
*/
