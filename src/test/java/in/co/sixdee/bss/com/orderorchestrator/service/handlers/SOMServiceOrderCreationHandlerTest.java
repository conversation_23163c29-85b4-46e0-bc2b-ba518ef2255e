package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import static org.mockito.Mockito.mock;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.camunda.spin.ProcessVars;
import in.co.sixdee.bss.com.orderorchestrator.config.connector.rest.WebClientConnector;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants.WorkFlowProcessVariables;
import in.co.sixdee.bss.om.model.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.CFSCharacteristicRef;
import in.co.sixdee.bss.om.model.dto.order.CFSRef;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.Service;
import in.co.sixdee.bss.om.model.dto.order.Subscription;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
class SOMServiceOrderCreationHandlerTest {

	@InjectMocks
	public SOMServiceOrderCreationHandler somServiceOrderCreationHandler;

	@Mock
	private WebClientConnector restConnector;

	static OrderFlowContext orderFlowContext;
	static Map<String, Object> kiMap;
	static DelegateExecution execution;
	static Map<String, Object> variables;
	static Order orderPayload;
	static String upcResponse;
	static HashMap<String, Object> workflowData;
	static Map<String, Object> currentExecutionMap;
	static String BSAddSubscriptionRequest;
	static Service service;
	static List<Subscription> subscriptions;
	static Subscription subscription;
	static HashMap<String, String> thirdPartyCallDetails;
	static HashMap<String, String> subOrderAttribute;
	static List<HashMap<String, String>> subOrderAttributes;
	static String upcPlanResponse;

	@BeforeAll
	static void setUpBeforeClass() throws Exception {
		execution = mock(DelegateExecution.class);
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
		workflowData = null;
		orderPayload = null;
		variables = null;
		kiMap = null;
		thirdPartyCallDetails = null;
		orderFlowContext = null;
		currentExecutionMap = null;
		service = null;
		subscriptions = null;
		subscription = null;
		subOrderAttributes = null;
		subOrderAttribute = null;
	}

	@BeforeEach
	void setUp() throws Exception {
		kiMap = new LinkedHashMap<String, Object>();
		currentExecutionMap = new HashMap<String, Object>();
		orderFlowContext = new OrderFlowContext();
		orderFlowContext.setAttributes(new LinkedHashMap<>());
		orderFlowContext.setSubOrderAttributes(new HashMap<>());
		orderFlowContext.setWorkflowData(new HashMap<>());
		subOrderAttributes = new ArrayList<>();
		subOrderAttribute = new HashMap<>();
		variables = new LinkedHashMap<String, Object>();
		variables.put("workflowData", orderFlowContext);
		variables.put("THIRD_PARTY_ID", "BILLING");
		service = new Service();
		service.setServiceId("23456789");
		variables.put("Status", "0");
		execution.setVariables(variables);
		subscriptions = new ArrayList<>();
		subscription = new Subscription();
		thirdPartyCallDetails = new LinkedHashMap<String, String>();
		orderPayload = new Order();
		workflowData = new HashMap<>();
		upcResponse = FileUtils.readFileToString(new File("src/test/resources/UPC_Response.json"));
		BSAddSubscriptionRequest = FileUtils
				.readFileToString(new File("src/test/resources/BS_AddSubscription_Request.json"));
		
		upcPlanResponse = FileUtils
				.readFileToString(new File("src/test/resources/UPCFetchPlans_Response.json"));
		
				
		thirdPartyCallDetails.put(CacheConstants.CacheFields.TRANSPORT_METHOD.name(), "POST");
		thirdPartyCallDetails.put(CacheConstants.CacheFields.URL.name(), "http://234567");

	}

	@AfterEach
	void tearDown() throws Exception {
	}

	//@Test
	void testExecute() throws Exception {
		
		variables.put(WorkFlowProcessVariables.EXECUTION_DATA.toString(), service);
		
		orderPayload.setDescription("Orders");
		orderPayload.setRequestedStartDate("2013-09-29T18:46:19Z");
		orderPayload.setRequestedCompletionDate("2013-09-29T18:46:19Z");
		orderPayload.setOrderType(OrderTypes.ADD_SUBSCRIPTION);
		orderFlowContext.setOrder(orderPayload);
		CallThirdPartyDTO callDetails = new CallThirdPartyDTO();
		/*
		 * Mockito.doReturn(callDetails).when(restConnector).service(null,new URI("http://234567"),
		 * 3000, 3000, "{\r\n" + "  \"externalServiceId\" : \"1234321255\",\r\n" +
		 * "  \"description\" : \"Orders\",\r\n" +
		 * "  \"requestedCompletionDate\" : \"2013-09-29T18:46:19Z\",\r\n" +
		 * "  \"requestedStartDate\" : \"2013-09-29T18:46:19Z\",\r\n" +
		 * "  \"serviceOrderItem\" : [ {\r\n" + "    \"id\" : \"1\",\r\n" +
		 * "    \"action\" : \"add\",\r\n" + "    \"service\" : {\r\n" +
		 * "      \"state\" : \"active\",\r\n" + "      \"type\" : \"CFS\",\r\n" +
		 * "      \"serviceCharacteristic\" : [ {\r\n" +
		 * "        \"name\" : \"SUBSCRIPTION_ID\",\r\n" + "        \"valueType\" : \"String\",\r\n"
		 * + "        \"value\" : \"34567\"\r\n" + "      } ],\r\n" +
		 * "      \"serviceSpecification\" : {\r\n" + "        \"id\" : \"24\",\r\n" +
		 * "        \"name\" : \"CFSS_GSM_VOICE\",\r\n" + "        \"@type\" : \"CFS\"\r\n" +
		 * "      }\r\n" + "    },\r\n" + "    \"@type\" : \"ServiceOrderItem\"\r\n" + "  } ],\r\n"
		 * + "  \"@type\" : \"ServiceOrder\"\r\n" + "}", null);
		 */
		
		orderFlowContext.getAttributes().put("subscriptionId", "34567");
		currentExecutionMap.put("UPCFetchPlanDetailsResponse",
				com.bazaarvoice.jolt.JsonUtils.jsonToObject(upcResponse));
		currentExecutionMap.put("BSAddsubscriptionRequest",
				com.bazaarvoice.jolt.JsonUtils.jsonToObject(BSAddSubscriptionRequest));
		workflowData.put("currentExecution", currentExecutionMap);
		orderFlowContext.setWorkflowData(workflowData);
		somServiceOrderCreationHandler.executionContext = orderFlowContext;
		somServiceOrderCreationHandler.thirdPartyCallDetails = thirdPartyCallDetails;
		somServiceOrderCreationHandler.executionVariables = variables;
		Mockito.doReturn(variables).when(execution).getVariables();
		//ProcessVars.executionData.from(execution).set(service);
		somServiceOrderCreationHandler.execution = execution;
		somServiceOrderCreationHandler.execute();

	}

	//@Test
	void testExecuteOnboarding() throws Exception {
		List<CFSRef> cfss = new ArrayList<>();
		CFSRef cfs = new CFSRef();
		List<CFSCharacteristicRef> characteristics = new ArrayList<>();
		CFSCharacteristicRef charcteristic = new CFSCharacteristicRef();
		charcteristic.setName("INTERNET");
		characteristics.add(charcteristic);

		cfs.setCharacteristics(characteristics);
		cfss.add(cfs);
		subscription.setCfss(cfss);
		subscription.setPrss(cfss);
		subscription.setLrss(cfss);
		subscription.setPlanId("100");
		subscriptions.add(subscription);
		service.setSubscriptions(subscriptions);
		//variables.put(WorkFlowProcessVariables.EXECUTION_DATA.toString(), service);
		Mockito.doReturn(variables).when(execution).getVariables();

		orderPayload.setDescription("Orders");
		orderPayload.setRequestedStartDate("2013-09-29T18:46:19Z");
		orderPayload.setRequestedCompletionDate("2013-09-29T18:46:19Z");
		orderPayload.setOrderType(OrderTypes.ONBOARDING);
		orderFlowContext.setOrder(orderPayload);

		subOrderAttribute.put("planId", "100");
		subOrderAttribute.put("subscriptionId", "980045");
		subOrderAttributes.add(subOrderAttribute);
		orderFlowContext.getSubOrderAttributes().put("subscriptionIdMap", subOrderAttributes);
		somServiceOrderCreationHandler.executionContext = orderFlowContext;
		ProcessVars.executionData.from(execution).set(somServiceOrderCreationHandler.getService());
		somServiceOrderCreationHandler.execute();

	}

	@Test
	void testCreateSOMRequest() throws Exception {
		orderPayload.setDescription("Orders");
		orderPayload.setOrderType(OrderTypes.ONBOARDING);
		orderPayload.setRequestedStartDate("2013-09-29T18:46:19Z");
		orderPayload.setRequestedCompletionDate("2013-09-29T18:46:19Z");
		orderFlowContext.setOrder(orderPayload);
		List<CFSRef> cfss = new ArrayList<>();
		List<CFSCharacteristicRef> characteristics = new ArrayList<>();
		List<Characteristic> serviceCharacteristics = new ArrayList<>();
		Characteristic charcteristic = new Characteristic();
		Characteristic charcteristic1 = new Characteristic();
		CFSCharacteristicRef charac = new CFSCharacteristicRef();
		charac.setName("ICCID");
		
		characteristics.add(charac);
		CFSRef cfs = new CFSRef();
		cfs.setSkipSom("false");
		cfss.add(cfs);
		cfs.setCharacteristics(characteristics);
		subscription.setCfss(cfss);
		subscription.setPrss(cfss);
		subscription.setLrss(cfss);
		subscription.setPlanId("34567");
		subscriptions.add(subscription);
		service.setSubscriptions(subscriptions);
		service.setServiceId("345678");
		charcteristic.setName("ICCID");
		charcteristic1.setName("IMSI");
		serviceCharacteristics.add(charcteristic1);
		serviceCharacteristics.add(charcteristic);
		service.setCharacteristics(serviceCharacteristics);
		subOrderAttribute.put("34567", "980045");
		subOrderAttribute.put("subscriptionId", "980045");
		orderFlowContext.getWorkflowData().put("subscriptionIdMap", subOrderAttribute);
		
		somServiceOrderCreationHandler.executionContext = orderFlowContext;
		/*somServiceOrderCreationHandler.service = service;
		somServiceOrderCreationHandler.orderPayload = orderPayload;
		somServiceOrderCreationHandler.createSOMRequest(orderPayload,service);*/
		}
	@Test
	void testCreateSOMRequestOtherThanOnboarding() throws Exception {
		orderPayload.setDescription("Orders");
		orderPayload.setOrderType(OrderTypes.ADD_SUBSCRIPTION);
		orderPayload.setRequestedCompletionDate("2013-09-29T18:46:19Z");
		orderFlowContext.setOrder(orderPayload);
		List<CFSRef> cfss = new ArrayList<>();
		List<CFSCharacteristicRef> characteristics = new ArrayList<>();
		List<Characteristic> serviceCharacteristics = new ArrayList<>();
		Characteristic charcteristic = new Characteristic();
		Characteristic charcteristic1 = new Characteristic();
		CFSCharacteristicRef charac = new CFSCharacteristicRef();
		charac.setName("ICCID");
		
		characteristics.add(charac);
		CFSRef cfs = new CFSRef();
		cfs.setSkipSom("false");
		cfss.add(cfs);
		cfs.setCharacteristics(characteristics);
		subscription.setCfss(cfss);
		subscription.setPrss(cfss);
		subscription.setLrss(cfss);
		subscription.setPlanId("34567");
		subscriptions.add(subscription);
		service.setSubscriptions(subscriptions);
		service.setServiceId("345678");
		charcteristic.setName("ICD");
		charcteristic1.setName("IMSI");
		serviceCharacteristics.add(charcteristic1);
		serviceCharacteristics.add(charcteristic);
		service.setCharacteristics(serviceCharacteristics);
		workflowData.put("currentExecution", com.bazaarvoice.jolt.JsonUtils.jsonToObject(upcPlanResponse));
		orderFlowContext.setWorkflowData(workflowData);
		subOrderAttribute.put("34567", "980045");
		subOrderAttribute.put("subscriptionId", "980045");
		orderFlowContext.getWorkflowData().put("subscriptionIdMap", subOrderAttribute);
		
		somServiceOrderCreationHandler.executionContext = orderFlowContext;
/*
		somServiceOrderCreationHandler.createSOMRequest(orderFlowContext.getOrder(),somServiceOrderCreationHandler.getService());
*/
		}
	
	
	/*@Test
	void testcreateServiceOrderItemBasedOnUPC() {
		orderFlowContext.setWorkflowData(null);
		somServiceOrderCreationHandler.executionContext = orderFlowContext;
		somServiceOrderCreationHandler.createServiceOrderItemBasedOnUPC();
	}*/


	@Test
	void testCreateServiceOrderItem() {

	}
	
	@Test
	void testparseUPCResponse() {
		orderPayload.setOrderType(OrderTypes.CHANGE_RISK_CATEGORY);
		orderFlowContext.setOrder(orderPayload);
		somServiceOrderCreationHandler.executionContext = orderFlowContext;
		/*somServiceOrderCreationHandler.orderPayload = orderPayload;
		somServiceOrderCreationHandler.parseUPCResponse(upcPlanResponse, orderFlowContext);*/
	}

	@Test
	void testCreateServiceItem() {

	}

	@Test
	void testSetAddonSubscriptionId() {
		List<Characteristic> characteristics = new ArrayList<>();
		orderFlowContext.getAttributes().put("subscriptionId", "34567");
		somServiceOrderCreationHandler.executionContext = orderFlowContext;
		/*somServiceOrderCreationHandler.setAddonSubscriptionId(characteristics);*/

	}

	@Test
	void testSetSubscriptionId() {

	}

	@Test
	void testCreateServiceCharacteristic() {
		somServiceOrderCreationHandler.executionContext = orderFlowContext;
		List<CFSCharacteristicRef> characteristics = new ArrayList<>();
		CFSCharacteristicRef charc = new CFSCharacteristicRef();
		charc.setName("IMSI");
		characteristics.add(charc);
		/*somServiceOrderCreationHandler.createServiceCharacteristic(characteristics, null);*/

	}

}
