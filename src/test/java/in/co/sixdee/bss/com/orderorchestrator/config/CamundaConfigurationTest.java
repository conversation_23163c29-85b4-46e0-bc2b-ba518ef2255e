/**
 * 
 *//*

package in.co.sixdee.bss.com.orderorchestrator.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

*/
/**
 * <AUTHOR>
 *
 *//*

@RunWith(JUnitPlatform.class)
@ExtendWith(MockitoExtension.class)
class CamundaConfigurationTest {

	@InjectMocks
	private CamundaConfiguration camundaConfiguration;

	@Test
	void test() {
	}

}
*/
