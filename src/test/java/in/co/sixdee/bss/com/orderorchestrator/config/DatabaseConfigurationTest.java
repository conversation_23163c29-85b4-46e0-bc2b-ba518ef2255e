/*
package in.co.sixdee.bss.com.orderorchestrator.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(JUnitPlatform.class)
@ExtendWith(MockitoExtension.class)
class DatabaseConfigurationTest {

	@InjectMocks
	private DatabaseConfiguration databaseConfiguration;

	@Test
	void test() {
	}

}
*/
