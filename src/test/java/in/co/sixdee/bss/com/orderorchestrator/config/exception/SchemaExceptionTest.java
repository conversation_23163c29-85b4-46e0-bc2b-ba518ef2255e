/*
package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import java.util.List;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants.HttpConstants;
import in.co.sixdee.bss.om.model.dto.WorkFlowErrorBean;

@RunWith(JUnitPlatform.class)
@ExtendWith(MockitoExtension.class)
class SchemaExceptionTest {

	private StatusConstants.HttpConstants status;
	private String message;
	private List<WorkFlowErrorBean> errors;

	@InjectMocks
	private SchemaException schemaException;

	@BeforeAll
	static void setUpBeforeClass() throws Exception {
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
	}

	@BeforeEach
	void setUp() throws Exception {
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void testSchemaExceptionHttpConstantsStringListOfWorkFlowErrorBean() {
		schemaException = new SchemaException(HttpConstants.SUCCESS, "SUCCESS", errors);
	}

	@Test
	void testSchemaExceptionHttpConstantsString() {

		schemaException = new SchemaException(HttpConstants.SUCCESS, "SUCCESS");
	}

	@Test
	void testSchemaExceptionString() {

		schemaException = new SchemaException("SUCCESS");

	}

	@Test
	void testGetStatus() {
		schemaException.getStatus();
	}

	@Test
	void testGetMessage() {
		schemaException.getMessage();
	}

	@Test
	void testGetErrors() {
		schemaException.getErrors();

	}

	@Test
	void testSetStatus() {
		schemaException.setStatus(HttpConstants.SUCCESS);
	}

	@Test
	void testSetMessage() {
		schemaException.setMessage("SUCCESS");
	}

	@Test
	void testSetErrors() {
		schemaException.setErrors(errors);
	}

}
*/
