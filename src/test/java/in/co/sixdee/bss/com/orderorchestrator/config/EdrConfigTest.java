/**
 * 
 *//*

package in.co.sixdee.bss.com.orderorchestrator.config;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.edr.async.pool.EdrThreadPool;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;

*/
/**
 * <AUTHOR>
 *
 *//*

@RunWith(JUnitPlatform.class)
@ExtendWith(MockitoExtension.class)
class EdrConfigTest {

	@InjectMocks
	private EdrConfig		edrConfig;

	@Mock
	private EdrThreadPool	edrThreadPool;

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@BeforeAll
	static void setUpBeforeClass() throws Exception {}

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@AfterAll
	static void tearDownAfterClass() throws Exception {}

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@BeforeEach
	void setUp() throws Exception {
	}

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@AfterEach
	void tearDown() throws Exception {}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.EdrConfig#addToEdrPool(in.co.sixdee.bss.om.model.dto.OrderFlowContext)}.
	 *//*

	@Test
	void testAddToEdrPool() {
		edrConfig.addToEdrPool(new OrderFlowContext());
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.EdrConfig#initiateThreadPools()}.
	 *//*

	@Test
	void testInitiateThreadPools() {
		edrConfig.initiateThreadPools();
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.EdrConfig#shutDownThread()}.
	 *//*

	@Test
	void testShutDownThread() {
		edrConfig.shutDownThread();
	}

}
*/
