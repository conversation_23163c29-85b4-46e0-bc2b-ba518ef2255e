/*
package in.co.sixdee.bss.com.orderorchestrator.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.google.gson.Gson;

import in.co.sixdee.bss.com.orderorchestrator.config.EdrConfig;
import in.co.sixdee.bss.com.orderorchestrator.model.ResponseWrapper;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.SubOrderEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderStageRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.SubOrderRepository;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.OrderStatusDTO;
import in.co.sixdee.bss.om.model.dto.OrderStatusDTO.SubOrders;
import in.co.sixdee.bss.om.model.dto.OrderStatusDTO.SubOrders.OrderStages;
import in.co.sixdee.bss.om.model.dto.SubOrderDTO;
import in.co.sixdee.bss.om.model.dto.WorkFlowErrorBean;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.event.StageStatusChangedEvent;
import nl.jqno.equalsverifier.EqualsVerifier;
import nl.jqno.equalsverifier.Warning;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
class OrderStatusUpdateServiceTest {
	
	@InjectMocks
	OrderStatusManager orderStatusManager;
	
	
	@Mock
	private OrderRepository					orderRepository;

	@Mock
	private SubOrderRepository				subOrderRepository;

	@Mock
	private OrderStageRepository			orderStageRepository;
	
	@Mock
	GetDataFromCache cache;

	

	@Mock
	private EdrConfig						edrConfig;

	static OrderFlowContext					orderFlowContext;

	static Gson                    gson;
	static OrderStageEntity        stage;
	static String                  request;
	static List<SubOrderDTO>				subOrders;
	static SubOrderDTO						subOrder;
	static SubOrders						subOrdersObject;
	static List<OrderStages>				orderStages;
	static OrderStages						orderStage;
	static OrderStatusDTO.SubOrders			subOrderStatusDTO;
	static OrderStatusDTO         orderStatusDTO;
	static List<SubOrderEntity>   subOrderEntityList;
	static SubOrderEntity         subOrderEntityObject;
	static List<OrderStageEntity> orderStagesListEntity;
	static StageStatusChangedEvent stageStatusChangedEvent;
	static CacheTableDataDTO cacheTable;
	static HashMap<String, String> ngTableData;
	static Order order;

	@BeforeAll
	static void setUpBeforeClass() throws Exception {
		request = "{\"id\":\"12345678\"}";
		gson = new Gson();
		order = new Order();
		stage = new OrderStageEntity();
		cacheTable = new CacheTableDataDTO();
		orderFlowContext = new OrderFlowContext();
		orderFlowContext.setAttributes(new LinkedHashMap<>());
		stage.setId(234567L);
		subOrders = new ArrayList<>();
		subOrder = new SubOrderDTO();
		subOrdersObject = new SubOrders();
		orderStages = new ArrayList<>();
		orderStage = new OrderStages();
		subOrderEntityObject = new SubOrderEntity();
		subOrderStatusDTO = new SubOrders();
		orderStatusDTO = new OrderStatusDTO();
		subOrderEntityList = new ArrayList<>();
		orderStagesListEntity = new ArrayList<>();
		stageStatusChangedEvent = new StageStatusChangedEvent();
		ngTableData = new HashMap<>();
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
		subOrders = null;
		stage = null;
		order = null;
		gson = null;
		subOrder = null;
		orderStage = null;
		orderStages = null;
		subOrdersObject = null;
		subOrderEntityList = null;
		subOrderStatusDTO = null;
		orderStatusDTO = null;
		subOrderEntityObject = null;
		orderStagesListEntity = null;
		stageStatusChangedEvent = null;
		ngTableData = null;
	}

	@BeforeEach
	void setUp() throws Exception {}

	@AfterEach
	void tearDown() throws Exception {}

	*/
/*@Test
	void testhandleStageStatusChangedEvent() {
		stageStatusChangedEvent.setOrderId("2345678");
		stageStatusChangedEvent.setStageStatus("inprogress");
		stageStatusChangedEvent.setIsCommonTask(true);
		stageStatusChangedEvent.setStageName("ORD_ACK");
		subOrdersObject.setOrderId("2345678");
		subOrderEntityList.add(subOrderEntityObject);
		Mockito.doReturn(subOrderEntityList).when(subOrderRepository)
					.getSubordersByOrderId(Long.parseLong(stageStatusChangedEvent.getOrderId()));
		Mockito.doReturn(true).when(orderStageRepository).existsByOrderIdAndName(Long.parseLong("2345678"), "ORD_ACK");
		orderFlowContext.setStageStatusChangedEvent(stageStatusChangedEvent);
		orderStatusManager.handleStageStatusChangedEvent(orderFlowContext);
	}

	@Test
	void testhandleStageStatusChangedEventCompletedStage() {
		stageStatusChangedEvent.setOrderId("2345678");
		stageStatusChangedEvent.setStageStatus("completed");
		stageStatusChangedEvent.setIsCommonTask(false);
		stageStatusChangedEvent.setSubOrderId("2345678");
		stageStatusChangedEvent.setStageExecutionOrder("2");
		orderFlowContext.setStageStatusChangedEvent(stageStatusChangedEvent);
		order.setOrderType("Onboarding");
		orderFlowContext.setOrder(order);
		subOrdersObject.setOrderId("2345678");
		subOrderEntityList.add(subOrderEntityObject);
		Mockito.doReturn(subOrderEntityList).when(subOrderRepository)
					.getSubordersByOrderId(Long.parseLong(stageStatusChangedEvent.getOrderId()));
//		Mockito.doReturn(true).when(subOrderRepository).isAllSubOrdersCompleted(Long.parseLong("2345678"));
		ngTableData.put("EDR_GENERATION_REQD", "true");
		 cacheTable.setNgTableData(ngTableData);
//		Mockito.doReturn(cacheTable).when(cache).getCacheDetailsFromDBMap("EDR_CONFIG", orderFlowContext.getOrder().getOrderType() + "_" + "FINAL");
		orderStatusUpdateService.handleStageStatusChangedEvent(orderFlowContext);
	}
	@Test
	void testhandleStageStatusChangedEventCommonStage() {
		stageStatusChangedEvent.setOrderId("2345678");
		stageStatusChangedEvent.setStageStatus("completed");
		stageStatusChangedEvent.setIsCommonTask(true);
		stageStatusChangedEvent.setSubOrderId("2345678");
		subOrdersObject.setOrderId("2345678");
		subOrderEntityList.add(subOrderEntityObject);
		Mockito.doReturn(subOrderEntityList).when(subOrderRepository)
					.getSubordersByOrderId(Long.parseLong(stageStatusChangedEvent.getOrderId()));
		orderFlowContext.setStageStatusChangedEvent(stageStatusChangedEvent);
//		Mockito.doReturn(true).when(subOrderRepository).isAllSubOrdersCompleted(Long.parseLong("2345678"));
		ngTableData.put("EDR_GENERATION_REQD", "true");
		 cacheTable.setNgTableData(ngTableData);
		 order.setOrderType("Onboarding");
			orderFlowContext.setOrder(order);
//		Mockito.doReturn(cacheTable).when(cache).getCacheDetailsFromDBMap("EDR_CONFIG", orderFlowContext.getOrder().getOrderType() + "_" + "FINAL");
		orderStatusUpdateService.handleStageStatusChangedEvent(orderFlowContext);
	}
	@Test
	void testhandleStageStatusChangedEventFailedStage() {
		stageStatusChangedEvent.setOrderId("2345678");
		stageStatusChangedEvent.setStageStatus("failed");
		stageStatusChangedEvent.setIsCommonTask(true);
		stageStatusChangedEvent.setSubOrderId("2345678");
		subOrdersObject.setOrderId("2345678");
		subOrderEntityList.add(subOrderEntityObject);
		Mockito.doReturn(subOrderEntityList).when(subOrderRepository)
					.getSubordersByOrderId(Long.parseLong(stageStatusChangedEvent.getOrderId()));
		orderFlowContext.setStageStatusChangedEvent(stageStatusChangedEvent);
		ngTableData.put("EDR_GENERATION_REQD", "true");
		 cacheTable.setNgTableData(ngTableData);
		 order.setOrderType("Onboarding");
			orderFlowContext.setOrder(order);
		Mockito.doReturn(cacheTable).when(cache).getCacheDetailsFromDBMap("EDR_CONFIG", orderFlowContext.getOrder().getOrderType() + "_" + "FINAL");
		orderStatusUpdateService.handleStageStatusChangedEvent(orderFlowContext);
	}
	
	@Test
	void testinsertNewStage() {
		stageStatusChangedEvent.setIsCommonTask(true);
		subOrderEntityObject.setId(234567L);
		stageStatusChangedEvent.setOrderId("23456");
		stageStatusChangedEvent.setStageExecutionOrder("456");
		subOrderEntityList.add(subOrderEntityObject);
		//orderStatusUpdateService.insertNewStage(stageStatusChangedEvent, subOrderEntityList);
	}
	
	@Test
	void testupdateStage() {
		stageStatusChangedEvent.setStageStatus("failed");
		stageStatusChangedEvent.setIsCommonTask(false);
		orderStatusUpdateService.updateStage(stageStatusChangedEvent);
	}
	
	@Test
	void testupdateSubOrderStatus() {
		subOrderEntityObject.setId(234567L);
		subOrderEntityList.add(subOrderEntityObject);
		stageStatusChangedEvent.setStageStatus("failed");
		stageStatusChangedEvent.setIsCommonTask(true);
		//orderStatusUpdateService.updateSubOrderStatus(stageStatusChangedEvent, subOrderEntityList, true);
	}
	@Test
	void testupdateSubOrderStatusCompleted() {
		subOrderEntityObject.setId(234567L);
		subOrderEntityObject.setOrderId(34567L);
		subOrderEntityList.add(subOrderEntityObject);
		stageStatusChangedEvent.setStageStatus("completed");
		stageStatusChangedEvent.setIsCommonTask(true);
		Mockito.doReturn(true).when(orderStageRepository).isAllStagesCompletedForOrder(34567L);
		//orderStatusUpdateService.updateSubOrderStatus(stageStatusChangedEvent, subOrderEntityList, false);
	}
	
	@Test
	void testupdateSubOrderStatusFalseCompleted() {
		subOrderEntityObject.setId(234567L);
		subOrderEntityObject.setOrderId(34567L);
		subOrderEntityList.add(subOrderEntityObject);
		stageStatusChangedEvent.setStageStatus("completed");
		stageStatusChangedEvent.setIsCommonTask(true);
		Mockito.doReturn(false).when(orderStageRepository).isAllStagesCompletedForOrder(34567L);
		orderStatusUpdateService.orderStageRepository = orderStageRepository;
		//orderStatusUpdateService.updateSubOrderStatus(stageStatusChangedEvent, subOrderEntityList, false);
	}
	@Test
	void testupdateSubOrderStatusFailed() {
		subOrderEntityObject.setId(234567L);
		subOrderEntityObject.setOrderId(34567L);
		subOrderEntityList.add(subOrderEntityObject);
		stageStatusChangedEvent.setStageStatus("failed");
		stageStatusChangedEvent.setIsCommonTask(false);
		//orderStatusUpdateService.updateSubOrderStatus(stageStatusChangedEvent, subOrderEntityList, true);
	}
	@Test
	void testupdateSubOrderStatusNotFailed() {
		subOrderEntityObject.setId(234567L);
		subOrderEntityObject.setOrderId(34567L);
		subOrderEntityList.add(subOrderEntityObject);
		stageStatusChangedEvent.setStageStatus("failed");
		stageStatusChangedEvent.setIsCommonTask(false);
		stageStatusChangedEvent.setSubOrderId("345678");
		//orderStatusUpdateService.updateSubOrderStatus(stageStatusChangedEvent, subOrderEntityList, false);
	}

	@Test
	void testUpdateAccountId() {
		Mockito.doReturn(1).when(orderRepository).updateAccountId(Long.valueOf("**********"), "23899");
		int updateAccountId = orderStatusUpdateService.updateAccountId("**********", "23899");
		Assertions.assertEquals(1, updateAccountId);

	}

	@Test
	void testUpdateProfileId() {
		Mockito.doReturn(1).when(orderRepository).updateCustomerId(Long.valueOf("**********"), "109876");
		int updateProfileId = orderStatusUpdateService.updateProfileId("**********", "109876");
		Assertions.assertEquals(1, updateProfileId);

	}

	@Test
	void testUpdateServiceId() {
		Mockito.doReturn(1).when(subOrderRepository).updateServiceId(Long.valueOf("**********"), "*********");
		int updateServiceId = orderStatusUpdateService.updateServiceId("**********", "*********", "9087553");
		Assertions.assertEquals(1, updateServiceId);

	}
	
	@Test
	void testupdateSuborderExecutionStart() {
		orderStatusUpdateService.updateSuborderExecutionStart("234567");
	}
	
	@Test
	void testupdateOrderExecutionStart() {
		orderStatusUpdateService.updateOrderExecutionStart("2345678");
	}
	
	@Test
	void testisOrderExists() {
		Mockito.doReturn(true).when(orderStageRepository).existsByOrderIdsubOrderIdandName(Long.valueOf("234567"), Long.valueOf("********6"), "ORD_ACK");
		boolean orderExists = orderStatusUpdateService.isOrderExists("234567", "********6", "ORD_ACK");
		Assertions.assertTrue(orderExists);
	}
	
	@Test
	public void equalsResponseWrapper() {
		Assertions.assertThrows(AssertionError.class, () -> {
			EqualsVerifier.forClass(ResponseWrapper.class).suppress(Warning.STRICT_INHERITANCE).verify();
		});
	}
	
	@Test
	public void equalsWorkFlowErrorBean() {
		Assertions.assertThrows(AssertionError.class, () -> {
			EqualsVerifier.forClass(WorkFlowErrorBean.class).suppress(Warning.STRICT_INHERITANCE).verify();
		});
	}
	*//*

	

}
*/
