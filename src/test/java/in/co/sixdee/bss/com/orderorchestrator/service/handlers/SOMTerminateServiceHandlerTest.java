package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import static org.mockito.Mockito.mock;

import java.io.File;
import java.net.URI;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.connector.rest.WebClientConnector;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants.WorkFlowProcessVariables;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.om.model.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.Service;
import in.co.sixdee.bss.om.model.dto.order.ServiceManagement;
import in.co.sixdee.bss.om.model.dto.order.Subscription;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
class SOMTerminateServiceHandlerTest {

	@InjectMocks
	SOMTerminateServiceHandler							somTerminateServiceHandler;

	@Mock
	GetDataFromCache						cache;

	@Mock
	private WebClientConnector restConnector;

	static OrderFlowContext					orderFlowContext;
	static Map<String, Object>				kiMap;
	static DelegateExecution				execution;
	static Map<String, Object>				variables;
	static Order							orderPayload;
	static String							upcResponse;
	static HashMap<String, Object>			workflowData;
	static Map<String, Object>				currentExecutionMap;
	static String							BSAddSubscriptionRequest;
	static Service							service;
	static List<Subscription>				subscriptions;
	static Subscription						subscription;
	static HashMap<String, String>			thirdPartyCallDetails;
	static HashMap<String, String>			subOrderAttribute;
	static List<HashMap<String, String>>	subOrderAttributes;
	static CacheTableDataDTO				cacheTable;
	static String							somResponse;
	static String							changePlan_Request;

	@BeforeAll
	static void setUpBeforeClass() throws Exception {
		execution = mock(DelegateExecution.class);
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
		workflowData = null;
		cacheTable = null;
		orderPayload = null;
		variables = null;
		kiMap = null;
		thirdPartyCallDetails = null;
		orderFlowContext = null;
		currentExecutionMap = null;
		service = null;
		subscriptions = null;
		subscription = null;
		subOrderAttributes = null;
		subOrderAttribute = null;
	}

	@BeforeEach
	void setUp() throws Exception {
		kiMap = new LinkedHashMap<String, Object>();
		cacheTable = new CacheTableDataDTO();
		currentExecutionMap = new HashMap<String, Object>();
		orderFlowContext = new OrderFlowContext();
		orderFlowContext.setAttributes(new LinkedHashMap<>());
		orderFlowContext.setSubOrderAttributes(new HashMap<>());
		orderFlowContext.setWorkflowData(new HashMap<>());
		subOrderAttributes = new ArrayList<>();
		subOrderAttribute = new HashMap<>();
		variables = new LinkedHashMap<String, Object>();
		variables.put("workflowData", orderFlowContext);
		variables.put("THIRD_PARTY_ID", "BILLING");
		service = new Service();
		service.setServiceId("23456789");
		variables.put("Status", "0");
		execution.setVariables(variables);
		subscriptions = new ArrayList<>();
		subscription = new Subscription();
		thirdPartyCallDetails = new LinkedHashMap<String, String>();
		orderPayload = new Order();
		workflowData = new HashMap<>();
		upcResponse = FileUtils.readFileToString(new File("src/test/resources/UPC_Response.json"));
		BSAddSubscriptionRequest = FileUtils.readFileToString(new File("src/test/resources/BS_AddSubscription_Request.json"));

		somResponse = FileUtils.readFileToString(new File("src/test/resources/ChangeSim_SOMResponse.json"));

		changePlan_Request = FileUtils.readFileToString(new File("src/test/resources/WorkFlowRequest.json"));
		thirdPartyCallDetails.put(CacheConstants.CacheFields.TRANSPORT_METHOD.name(), "POST");
		thirdPartyCallDetails.put(CacheConstants.CacheFields.URL.name(), "http://234567");
		thirdPartyCallDetails.put("EXPECTED_HTTP_CODE", "200");

	}

	@AfterEach
	void tearDown() throws Exception {}

	//@Test
	void testExecute() throws Exception {
		variables.put(WorkFlowProcessVariables.EXECUTION_DATA.toString(), service);
		variables.put(WorkFlowProcessVariables.THIRD_PARTY_ID.toString(), "BILLING");
		ServiceManagement serviceManagement = new ServiceManagement();
		serviceManagement.setServiceId("34567");
				orderPayload.setServiceManagement(serviceManagement);
		orderPayload.setDescription("Orders");
		orderPayload.setRequestedStartDate("2013-09-29T18:46:19Z");
		orderPayload.setRequestedCompletionDate("2013-09-29T18:46:19Z");
		orderPayload.setOrderType(OrderTypes.ADD_SUBSCRIPTION);
		orderFlowContext.setOrder(orderPayload);
		orderFlowContext.getAttributes().put("subscriptionId", "34567");
		currentExecutionMap.put("UPCFetchPlanDetailsResponse", com.bazaarvoice.jolt.JsonUtils.jsonToObject(upcResponse));
		currentExecutionMap.put("BSAddsubscriptionRequest",
				com.bazaarvoice.jolt.JsonUtils.jsonToObject(BSAddSubscriptionRequest));
		workflowData.put("currentExecution", currentExecutionMap);
		orderFlowContext.setWorkflowData(workflowData);
		CallThirdPartyDTO callDetails = new CallThirdPartyDTO();
		cacheTable.setNgTableData(thirdPartyCallDetails);
		Mockito.doReturn(cacheTable).when(cache)
				.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(), "BILLING");

		callDetails.setResponseCode("200");
		Mockito.doReturn(callDetails).when(restConnector).service("POST", new URI("http://234567"), 3000, 3000,
				"{\r\n"
				+ "  \"externalServiceId\" : \"34567\",\r\n"
				+ "  \"priority\" : \"1\",\r\n"
				+ "  \"requestedCompletionDate\" : \"2013-09-29T18:46:19Z\",\r\n"
				+ "  \"requestedStartDate\" : \"2013-09-29T18:46:19Z\",\r\n"
				+ "  \"serviceOrderItem\" : [ {\r\n"
				+ "    \"id\" : \"1\",\r\n"
				+ "    \"service\" : {\r\n"
				+ "      \"id\" : \"edc738cbe7414645a2d24798b554b5f5\",\r\n"
				+ "      \"state\" : \"active\",\r\n"
				+ "      \"type\" : \"PRS\",\r\n"
				+ "      \"serviceSpecification\" : {\r\n"
				+ "        \"id\" : \"edc738cbe7414645a2d24798b554b5f5\",\r\n"
				+ "        \"href\" : \"http://10.0.0.97:7777/ServiceCatalogue/ServiceSpecificationActive/5\",\r\n"
				+ "        \"name\" : \"PRS_SIM\",\r\n"
				+ "        \"version\" : \"1\"\r\n"
				+ "      }\r\n"
				+ "    },\r\n"
				+ "    \"@type\" : \"ServiceOrderItem\"\r\n"
				+ "  }, {\r\n"
				+ "    \"id\" : \"2\",\r\n"
				+ "    \"service\" : {\r\n"
				+ "      \"id\" : \"6ebf06a6972f48429ecfff515994d333\",\r\n"
				+ "      \"state\" : \"active\",\r\n"
				+ "      \"type\" : \"CFS\",\r\n"
				+ "      \"serviceSpecification\" : {\r\n"
				+ "        \"id\" : \"6ebf06a6972f48429ecfff515994d333\",\r\n"
				+ "        \"href\" : \"http://10.0.0.97:7777/ServiceCatalogue/ServiceSpecificationActive/22\",\r\n"
				+ "        \"name\" : \"CFSS_GSM_DATA\",\r\n"
				+ "        \"version\" : \"1\"\r\n"
				+ "      }\r\n"
				+ "    },\r\n"
				+ "    \"@type\" : \"ServiceOrderItem\"\r\n"
				+ "  }, {\r\n"
				+ "    \"id\" : \"3\",\r\n"
				+ "    \"service\" : {\r\n"
				+ "      \"id\" : \"RFS_98f67e0831234e01af304ce2f1f7935c\",\r\n"
				+ "      \"state\" : \"active\",\r\n"
				+ "      \"type\" : \"RFS\",\r\n"
				+ "      \"serviceSpecification\" : {\r\n"
				+ "        \"id\" : \"RFS_98f67e0831234e01af304ce2f1f7935c\",\r\n"
				+ "        \"href\" : \"http://10.0.0.97:7777/ServiceCatalogue/ServiceSpecificationActive/25\",\r\n"
				+ "        \"name\" : \"RFSS_GSM_DATA\",\r\n"
				+ "        \"version\" : \"1\"\r\n"
				+ "      }\r\n"
				+ "    },\r\n"
				+ "    \"@type\" : \"ServiceOrderItem\"\r\n"
				+ "  }, {\r\n"
				+ "    \"id\" : \"4\",\r\n"
				+ "    \"service\" : {\r\n"
				+ "      \"id\" : \"bf8f8377bb8f44b7b92887c75d836458\",\r\n"
				+ "      \"state\" : \"active\",\r\n"
				+ "      \"type\" : \"CFS\",\r\n"
				+ "      \"serviceSpecification\" : {\r\n"
				+ "        \"id\" : \"bf8f8377bb8f44b7b92887c75d836458\",\r\n"
				+ "        \"href\" : \"http://10.0.0.97:7777/ServiceCatalogue/ServiceSpecificationActive/23\",\r\n"
				+ "        \"name\" : \"CFSS_GSM_SMS\",\r\n"
				+ "        \"version\" : \"1\"\r\n"
				+ "      }\r\n"
				+ "    },\r\n"
				+ "    \"@type\" : \"ServiceOrderItem\"\r\n"
				+ "  }, {\r\n"
				+ "    \"id\" : \"5\",\r\n"
				+ "    \"service\" : {\r\n"
				+ "      \"id\" : \"RFS_7e3d4156516f440fb6e220dbef03539c\",\r\n"
				+ "      \"state\" : \"active\",\r\n"
				+ "      \"type\" : \"RFS\",\r\n"
				+ "      \"serviceSpecification\" : {\r\n"
				+ "        \"id\" : \"RFS_7e3d4156516f440fb6e220dbef03539c\",\r\n"
				+ "        \"href\" : \"http://10.0.0.97:7777/ServiceCatalogue/ServiceSpecificationActive/26\",\r\n"
				+ "        \"name\" : \"RFSS_GSM_SMS\",\r\n"
				+ "        \"version\" : \"1\"\r\n"
				+ "      }\r\n"
				+ "    },\r\n"
				+ "    \"@type\" : \"ServiceOrderItem\"\r\n"
				+ "  }, {\r\n"
				+ "    \"id\" : \"6\",\r\n"
				+ "    \"service\" : {\r\n"
				+ "      \"id\" : \"785c58b59b7d4ae69a87860f69dd7b54\",\r\n"
				+ "      \"state\" : \"active\",\r\n"
				+ "      \"type\" : \"CFS\",\r\n"
				+ "      \"serviceSpecification\" : {\r\n"
				+ "        \"id\" : \"785c58b59b7d4ae69a87860f69dd7b54\",\r\n"
				+ "        \"href\" : \"http://10.0.0.97:7777/ServiceCatalogue/ServiceSpecificationActive/24\",\r\n"
				+ "        \"name\" : \"CFSS_GSM_VOICE\",\r\n"
				+ "        \"version\" : \"1\"\r\n"
				+ "      }\r\n"
				+ "    },\r\n"
				+ "    \"@type\" : \"ServiceOrderItem\"\r\n"
				+ "  }, {\r\n"
				+ "    \"id\" : \"7\",\r\n"
				+ "    \"service\" : {\r\n"
				+ "      \"id\" : \"RFS_20ddf0bb6fa14c2fb9ae8dc64db73abb\",\r\n"
				+ "      \"state\" : \"active\",\r\n"
				+ "      \"type\" : \"RFS\",\r\n"
				+ "      \"serviceSpecification\" : {\r\n"
				+ "        \"id\" : \"RFS_20ddf0bb6fa14c2fb9ae8dc64db73abb\",\r\n"
				+ "        \"href\" : \"http://10.0.0.97:7777/ServiceCatalogue/ServiceSpecificationActive/27\",\r\n"
				+ "        \"name\" : \"RFSS_GSM_VOICE\",\r\n"
				+ "        \"version\" : \"1\"\r\n"
				+ "      }\r\n"
				+ "    },\r\n"
				+ "    \"@type\" : \"ServiceOrderItem\"\r\n"
				+ "  } ],\r\n"
				+ "  \"@type\" : \"ServiceOrder\"\r\n"
				+ "}",
				null);

		workflowData.put("SOMFetchServiceRegistryResponseAttributes", com.bazaarvoice.jolt.JsonUtils.jsonToObject(somResponse));
		//workflowData.put("workflowRequest", com.bazaarvoice.jolt.JsonUtils.jsonToObject(changePlan_Request));
		orderFlowContext.setWorkflowData(workflowData);
		somTerminateServiceHandler.executionContext = orderFlowContext;
		somTerminateServiceHandler.thirdPartyCallDetails = thirdPartyCallDetails;
		somTerminateServiceHandler.executionVariables = variables;
		Mockito.doReturn(variables).when(execution).getVariables();
		somTerminateServiceHandler.execution = execution;
		somTerminateServiceHandler.execute();
	}

	@Test
	void testCreateSOMRequest() {

	}

}
