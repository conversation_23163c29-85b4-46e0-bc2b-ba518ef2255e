package in.co.sixdee.bss.com.orderorchestrator.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.notification.NotificationUtils;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.WaitingProcessInfoEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.WaitingProcessInfoRepository;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Order;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.impl.batch.BatchEntity;
import org.camunda.bpm.engine.runtime.Incident;
import org.camunda.bpm.engine.runtime.IncidentQuery;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.runtime.ProcessInstanceQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.LinkedHashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
public class CancelOrderServiceTest {

    @InjectMocks
    CancelOrderService cancelOrderService;

    @Mock
    WaitingProcessInfoRepository waitingProcessInfoRepository;

    @Mock
    private RuntimeService runtimeService;

    @Mock
    private NotificationUtils notificationUtils;

    private OrderFlowContext orderFlowContext;

    private OrderFlowContext orderFlowContextDB;

    @BeforeEach
    void setUp() {
        orderFlowContext = new OrderFlowContext();
        orderFlowContext.setOrder(new Order());
        orderFlowContext.getOrder().setOrderType("CancelOrder");
        orderFlowContext.getOrder().setOrderId("1");
        orderFlowContextDB=new OrderFlowContext();
        orderFlowContextDB.setOrder(new Order());
        orderFlowContextDB.getOrder().setOrderType("CancelOrder");
        LinkedHashMap<String, String> attrs = new LinkedHashMap<>();
        attrs.put("accountId","123");
        orderFlowContextDB.setAttributes(attrs);
    }

    @Test
    void testProcessCancelOrderRequest_entryInWaitingProcessInfo() throws NoSuchMethodException {
        WaitingProcessInfoEntity waitingInfo = new WaitingProcessInfoEntity();
        waitingInfo.setProcessInstanceId("121");
        waitingInfo.setExecutionId("121");



        when(waitingProcessInfoRepository.findProcessInfoByOrderId(eq(orderFlowContext.getOrder().getOrderId()), anyList())).thenReturn(waitingInfo);
        doNothing().when(notificationUtils).sendNotification(any(), any(), any());
        when(runtimeService.deleteProcessInstancesAsync(anyList(), isNull(), any(), anyBoolean(), anyBoolean())).thenReturn(new BatchEntity());

        CancelOrderService spyNotify = Mockito.spy(cancelOrderService);
       /* Method method = spyNotify.getClass().getDeclaredMethod("initiateWorkflow",OrderFlowContext.class);
        method.setAccessible(true);*/
        doNothing().when(spyNotify).initiateWorkflow(any(OrderFlowContext.class));
        doReturn(orderFlowContextDB).when(spyNotify).initOrderFlowContext(waitingInfo.getExecutionId());
        spyNotify.processCancelOrderRequest(orderFlowContext);
    }

    @Test
    void testProcessCancelOrderRequest_NoEntryInWaitingProcessInfo(){

        when(waitingProcessInfoRepository.findProcessInfoByOrderId(eq(orderFlowContext.getOrder().getOrderId()), anyList())).thenReturn(null);

        ProcessInstance pi = mock(ProcessInstance.class);
        ProcessInstanceQuery processInstanceQuery = mock(ProcessInstanceQuery.class);
        IncidentQuery incidentQuery =mock(IncidentQuery.class);
        when(runtimeService.createProcessInstanceQuery()).thenReturn(processInstanceQuery);
        when(processInstanceQuery.processInstanceBusinessKey(orderFlowContext.getOrder().getOrderId()))
                .thenReturn(processInstanceQuery);
        when(processInstanceQuery.list()).thenReturn(List.of(pi));

        when(pi.getId()).thenReturn("pi1");
        var incident = mock(Incident.class);
        when(incident.getJobDefinitionId()).thenReturn("jobDefId1");
        when(incident.getProcessInstanceId()).thenReturn("pi1");

        when(runtimeService.createIncidentQuery()).thenReturn(incidentQuery);
        when(incidentQuery.incidentType("failedJob")).thenReturn(incidentQuery);
        when(incidentQuery.processInstanceId("pi1")).thenReturn(incidentQuery);
        when(incidentQuery.singleResult()).thenReturn(incident);

        CancelOrderService spyNotify = Mockito.spy(cancelOrderService);
        doReturn(orderFlowContextDB).when(spyNotify).initOrderFlowContext(any());
        doNothing().when(spyNotify).initiateWorkflow(any(OrderFlowContext.class));
        spyNotify.processCancelOrderRequest(orderFlowContext);
    }

    @Test
    void testProcessCancelOrderRequest_NoEntryInWaitingProcessInfo_PiListEmpty() {

        when(waitingProcessInfoRepository.findProcessInfoByOrderId(eq(orderFlowContext.getOrder().getOrderId()), anyList())).thenReturn(null);

        ProcessInstance pi = mock(ProcessInstance.class);
        ProcessInstanceQuery processInstanceQuery = mock(ProcessInstanceQuery.class);
        ProcessInstanceQuery processInstanceQuery2 = mock(ProcessInstanceQuery.class);
        IncidentQuery incidentQuery = mock(IncidentQuery.class);
        when(runtimeService.createProcessInstanceQuery()).thenReturn(processInstanceQuery);
        when(processInstanceQuery.processInstanceBusinessKey(orderFlowContext.getOrder().getOrderId()))
                .thenReturn(processInstanceQuery);
        when(processInstanceQuery.list()).thenReturn(List.of());

        when(processInstanceQuery.processInstanceBusinessKeyLike("1_%")).thenReturn(processInstanceQuery2);
        when(processInstanceQuery2.list()).thenReturn(List.of(pi));
        when(pi.getId()).thenReturn("pi1");
        var incident = mock(Incident.class);
        when(incident.getJobDefinitionId()).thenReturn("jobDefId1");
        when(incident.getProcessInstanceId()).thenReturn("pi1");

        when(runtimeService.createIncidentQuery()).thenReturn(incidentQuery);
        when(incidentQuery.incidentType("failedJob")).thenReturn(incidentQuery);
        when(incidentQuery.processInstanceId("pi1")).thenReturn(incidentQuery);
        when(incidentQuery.singleResult()).thenReturn(incident);

        CancelOrderService spyNotify = Mockito.spy(cancelOrderService);
        doReturn(orderFlowContextDB).when(spyNotify).initOrderFlowContext(any());
        doNothing().when(spyNotify).initiateWorkflow(any(OrderFlowContext.class));
        spyNotify.processCancelOrderRequest(orderFlowContext);
    }
    @Test
    void testProcessCancelOrderRequest_NoEntryInWaitingProcessInfo_PiListAlwaysEmpty(){

        when(waitingProcessInfoRepository.findProcessInfoByOrderId(eq(orderFlowContext.getOrder().getOrderId()), anyList())).thenReturn(null);

        ProcessInstance pi = mock(ProcessInstance.class);
        ProcessInstanceQuery processInstanceQuery = mock(ProcessInstanceQuery.class);
        ProcessInstanceQuery processInstanceQuery2 = mock(ProcessInstanceQuery.class);
        when(runtimeService.createProcessInstanceQuery()).thenReturn(processInstanceQuery);
        when(processInstanceQuery.processInstanceBusinessKey(orderFlowContext.getOrder().getOrderId()))
                .thenReturn(processInstanceQuery);
        when(processInstanceQuery.list()).thenReturn(List.of());
        when(processInstanceQuery.processInstanceBusinessKeyLike("1_%")).thenReturn(processInstanceQuery2);
        when(processInstanceQuery2.list()).thenReturn(List.of());
        cancelOrderService.processCancelOrderRequest(orderFlowContext);
    }
}
