/*
package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.io.File;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.common.util.JsonUtils;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.Subscription;

@RunWith(JUnitPlatform.class)
@ExtendWith(MockitoExtension.class)
class PlanComparisionHandlerTest {

	@InjectMocks
	private PlanComparisionHandler checkEligibilityCriteria;

	static String BSViewSubscriptionResponse;
	static String UPCResponse;
	static String ChangePlanRequest;
	static OrderFlowContext orderFlowContext;
	static HashMap<String, Object> workflowData;
	static Order order;

	@BeforeAll
	static void setUpBeforeClass() throws Exception {
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
		orderFlowContext = null;
		workflowData = null;
	}

	@BeforeEach
	void setUp() throws Exception {
		orderFlowContext = new OrderFlowContext();
		workflowData = new LinkedHashMap<String, Object>();
		BSViewSubscriptionResponse = FileUtils
				.readFileToString(new File("src/test/resources/BSViewSubscription_Response.json"));
		UPCResponse = FileUtils.readFileToString(new File("src/test/resources/UPC_BasePlan_Response.json"));
		ChangePlanRequest = FileUtils.readFileToString(new File("src/test/resources/ChangePlan_Request.json"));
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	//@Test
	void testExecute() throws Exception{
		order = JsonUtils.unmarshall(ChangePlanRequest, Order.class);
		orderFlowContext.setOrder(order);
		workflowData.put("BSViewSubscriptionResponse",com.bazaarvoice.jolt.JsonUtils.jsonToObject(BSViewSubscriptionResponse));
		workflowData.put("UPCFetchBasePlanDetailsResponse",com.bazaarvoice.jolt.JsonUtils.jsonToObject(UPCResponse));
		orderFlowContext.setWorkflowData(workflowData);
		checkEligibilityCriteria.executionContext = orderFlowContext;
		checkEligibilityCriteria.execute();

	}
	//@Test
	void testExecuteException() throws Exception{
		order = JsonUtils.unmarshall(ChangePlanRequest, Order.class);
		orderFlowContext.setOrder(order);
		workflowData.put("BSViewSubscriptionResponse",com.bazaarvoice.jolt.JsonUtils.jsonToObject(BSViewSubscriptionResponse));
		workflowData.put("UPCFetchBasePlanDetailsResponse",com.bazaarvoice.jolt.JsonUtils.jsonToObject(UPCResponse));
		orderFlowContext.setWorkflowData(null);
		checkEligibilityCriteria.executionContext = orderFlowContext;
		checkEligibilityCriteria.execute();

	}


@Test
	void testFormingNewCartDetailsFromUPCEligibilityOffers() {

		Subscription formingNewCartDetailsFromUPCEligibilityOffers = checkEligibilityCriteria.formingNewCartDetailsFromUPCEligibilityOffers(null, null);
		Assertions.assertNull(formingNewCartDetailsFromUPCEligibilityOffers.getPlanId());
	}

	@Test
	void testParseGetSubscriptionDetailsResponse() {
		String response = "{ \"recordsFiltered\": 10, \"recordsTotal\": 3, \"data\": [ { \"planId\":5, \"planType\": 1, \"status\": 1, } ] }";
		List<Subscription> parseGetSubscriptionDetailsResponse = checkEligibilityCriteria.parseGetSubscriptionDetailsResponse(response, orderFlowContext);
		Assertions.assertEquals(0, parseGetSubscriptionDetailsResponse.size());

	}
	
	@Test
	void testsetOrderFlowContextDetails() {
		orderFlowContext.setOrder(null);
		OrderFlowContext setOrderFlowContextDetails = checkEligibilityCriteria.setOrderFlowContextDetails(orderFlowContext);
		Assertions.assertNull(setOrderFlowContextDetails.getOrder());
		
	}


}
*/
