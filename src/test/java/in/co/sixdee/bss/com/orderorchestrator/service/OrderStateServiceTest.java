/*
package in.co.sixdee.bss.com.orderorchestrator.service;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.WaitingProcessInfoEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.WaitingProcessInfoRepository;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Order;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
class OrderStateServiceTest {

	@InjectMocks
	private OrderStateService				orderStateService;

	@Mock
	private WaitingProcessInfoRepository	waitingProcessInfoRepository;

	@Mock
	private OrderStatusManager		orderStatusManager;

	static OrderFlowContext					orderFlowContext;
	static String							updateKey;
	static Order							order;
	static WaitingProcessInfoEntity			waitingEntity;

	@BeforeAll
	static void setUpBeforeClass() throws Exception {
		orderFlowContext = new OrderFlowContext();
		orderFlowContext.setWorkflowData(new HashMap<String, Object>());
		order = new Order();
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
		orderFlowContext = null;
	}

	@BeforeEach
	void setUp() throws Exception {
		orderFlowContext.setOrder(order);
		orderFlowContext.getOrder().setOrderId("922110465665544331");
		orderFlowContext.setAttributes(new LinkedHashMap<String, String>());
		orderFlowContext.getAttributes().put(GenericConstants.PROFILE_ID, "10589");
		orderFlowContext.getAttributes().put(GenericConstants.ACCOUNT_ID, "6dbss3538");
		orderFlowContext.getAttributes().put(GenericConstants.SERVICE_ID, "**********");
		orderFlowContext.getAttributes().put(GenericConstants.SUBSCRIPTION_ID, "**************");
		orderFlowContext.setSubOrderAttributes(new HashMap<String, List<HashMap<String, String>>>());
	}

	@AfterEach
	void tearDown() throws Exception {}

	@Test
	void testProcessOrderUpdatesEmpty() {
		updateKey = "";
		orderStateService.processOrderUpdates(updateKey, orderFlowContext);
	}

	//@Test
	void testProcessOrderUpdatesException() {
		updateKey = null;
		orderStateService.processOrderUpdates(updateKey, orderFlowContext);
	}

	@Test
	void testProcessOrderUpdatesForOrderProfile() {
		updateKey = "ORDER_PROFILE";
		orderStateService.processOrderUpdates(updateKey, orderFlowContext);
	}

	@Test
	void testProcessOrderUpdatesForOrderProfileNull() {
		updateKey = "ORDER_PROFILE";
		orderFlowContext.getAttributes().put(GenericConstants.PROFILE_ID, null);
		orderStateService.processOrderUpdates(updateKey, orderFlowContext);
	}

	@Test
	void testProcessOrderUpdatesForOrderAccount() {
		updateKey = "ORDER_ACCOUNT";
		orderStateService.processOrderUpdates(updateKey, orderFlowContext);
	}

	@Test
	void testProcessOrderUpdatesForOrderAccountNull() {
		updateKey = "ORDER_ACCOUNT";
		orderFlowContext.getAttributes().put(GenericConstants.ACCOUNT_ID, null);
		orderStateService.processOrderUpdates(updateKey, orderFlowContext);
	}

	@Test
	void testProcessOrderUpdatesForSubOrderService() {
		updateKey = "SUBORDER_SERVICE_INTERNAL";
		orderStateService.processOrderUpdates(updateKey, orderFlowContext);
	}

	@Test
	void testProcessOrderUpdatesForSubOrderServiceNull() {
		updateKey = "SUBORDER_SERVICE_INTERNAL";
		orderFlowContext.getAttributes().put(GenericConstants.SERVICE_ID, null);
		orderStateService.processOrderUpdates(updateKey, orderFlowContext);
	}

	@Test
	void testProcessOrderUpdatesForSubOrderSubscription() {
		updateKey = "SUBORDER_SUBSCRIPTION";
		orderStateService.processOrderUpdates(updateKey, orderFlowContext);
	}

	@Test
	void testProcessOrderUpdatesForSubOrderSubscriptionNull() {
		updateKey = "SUBORDER_SUBSCRIPTION";
		orderFlowContext.getAttributes().put(GenericConstants.SUBSCRIPTION_ID, null);
		orderStateService.processOrderUpdates(updateKey, orderFlowContext);
	}

	*/
/*@Test
	void testValidatePaymentRequest() {
		var callback = new OrderCallBack();
		callback.setOrderId("**************");
		orderFlowContext.setContinueRequest(callback);
		Mockito.doReturn(waitingEntity).when(waitingProcessInfoRepository)
				.findProcessInfoByOrderId(orderFlowContext.getContinueRequest().getOrderId(),"Callback");
		Assertions.assertThrows(CommonException.class, () -> {
			orderStateService.validatePaymentRequest(orderFlowContext);
		});
	}*//*


	@Test
	void testPopulateSubOrderAttributesNull() {
		orderFlowContext.setAttributes(null);
		orderStateService.populateSubOrderAttributes(orderFlowContext);

	}

}
*/
