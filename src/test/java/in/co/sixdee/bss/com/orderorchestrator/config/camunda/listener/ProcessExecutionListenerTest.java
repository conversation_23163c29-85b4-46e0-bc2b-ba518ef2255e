/*

package in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.ProcessVariableUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants.WorkFlowProcessVariables;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderStateService;
import in.co.sixdee.bss.common.core.AppInstanceIdManager;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Order;
import org.apache.commons.io.FileUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.impl.history.event.HistoryEvent;
import org.camunda.bpm.engine.variable.value.TypedValue;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;
import org.camunda.bpm.model.xml.type.ModelElementType;
import org.camunda.bpm.spring.boot.starter.event.ExecutionEvent;
import org.camunda.bpm.spring.boot.starter.event.TaskEvent;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@ExtendWith(MockitoExtension.class)

@RunWith(JUnitPlatform.class)
class ProcessExecutionListenerTest {

	@Mock
	private ProcessVariableUtils processVariableUtils;

	@Mock
	private OrderStateService orderUpdateService;

	@Mock
	private AppInstanceIdManager appInstanceSequence;

	@InjectMocks
	private ProcessExecutionListener processExecutionListener;

	static OrderFlowContext              orderFlowContext;
	static DelegateExecution             execution;
	static DelegateTask                  taskDelegate;
	static TaskEvent                     taskEvent;
	static String                        request;
	static HashMap<String, Object>       customTransformationObjs;
	static LinkedHashMap<String, String> strAttributes;
	static Map<String, Object>           currentExecutionMap;
	static FlowElement                   bpmnModelElementInstance;
	static BpmnModelInstance             bpmnModelInstance;
	static ModelElementType              elementType;
	static ExecutionEvent                executionEvent;
	static HistoryEvent                  historyEvent;
	static ServiceTask                   serviceTask;
	static TypedValue                    typedValue;
	static String                        activityId = "DecomposeOrder";

	@BeforeAll
	static void setUpBeforeClass() throws Exception {
		execution = Mockito.mock(DelegateExecution.class);
		taskDelegate = Mockito.mock(DelegateTask.class);
		serviceTask = Mockito.mock(ServiceTask.class);
		bpmnModelElementInstance = Mockito.mock(FlowElement.class);
		bpmnModelInstance = Mockito.mock(BpmnModelInstance.class);
		elementType = Mockito.mock(ModelElementType.class);
		taskEvent = Mockito.mock(TaskEvent.class);
		orderFlowContext = new OrderFlowContext();
		executionEvent = Mockito.mock(ExecutionEvent.class);
		historyEvent = Mockito.mock(HistoryEvent.class);
		typedValue = Mockito.mock(TypedValue.class);
		customTransformationObjs = new LinkedHashMap<String, Object>();
		currentExecutionMap = new LinkedHashMap<String, Object>();
		strAttributes = new LinkedHashMap<String, String>();
		orderFlowContext.setTraceId("38725478678");
		orderFlowContext.setRequestId("384628465876");
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
	}

	@BeforeEach
	void setUp() throws Exception {
		request = FileUtils.readFileToString(new File("src/test/resources/SaveOrderRequest.json"));
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void testOnServiceTaskExecutionEvent() {
		Mockito.doReturn("value").when(execution)
				.getVariableLocal(WorkFlowConstants.WorkFlowProcessVariables.UPDATE_KEY.toString());
		Mockito.doReturn("DecomposeOrder").when(execution).getCurrentActivityId();
		Mockito.doReturn("0").when(execution).getVariable("Status");
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		processExecutionListener.onServiceTaskExecutionEnd(execution);
	}

	@Test
	void testOnServiceTaskExecutionEventOrderTypeResume() {
		Mockito.doReturn("value").when(execution)
				.getVariableLocal(WorkFlowConstants.WorkFlowProcessVariables.UPDATE_KEY.toString());
		Mockito.doReturn("value").when(execution).getCurrentActivityId();
		Mockito.doReturn("0").when(execution).getVariable("Status");
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		processExecutionListener.onServiceTaskExecutionEnd(execution);
	}

	@Test
	void testOnServiceTaskExecutionEventElse() {
		Mockito.doReturn(null).when(execution)
				.getVariableLocal(WorkFlowConstants.WorkFlowProcessVariables.UPDATE_KEY.toString());
		Mockito.doReturn("OrderValidation").when(execution).getCurrentActivityId();
		Mockito.doReturn("1").when(execution).getVariable("Status");
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		processExecutionListener.onServiceTaskExecutionEnd(execution);
	}

	*/
/*
	 * @Test void testOnServiceTaskExecutionEventCatch() {
	 * processExecutionListener.onServiceTaskExecutionEnd(null); }
	 *//*


	//@Test
	void testOnServiceTaskStart() throws Exception {
		var order = new Order();
		order.setOrderId("387284684836");
		orderFlowContext.setOrder(order);
		currentExecutionMap.put("workflowData", new ObjectMapper().writeValueAsString(orderFlowContext));
		customTransformationObjs.put("currentExecution", currentExecutionMap);
		orderFlowContext.setWorkflowData(customTransformationObjs);
		Mockito.doReturn(activityId).when(execution).getCurrentActivityId();
		Mockito.doReturn(bpmnModelInstance).when(execution).getBpmnModelInstance();
		serviceTask.setCamundaAsyncBefore(true);
		serviceTask.setCamundaAsyncAfter(true);
		Mockito.doReturn(serviceTask).when(bpmnModelInstance).getModelElementById(activityId);
//		Mockito.doReturn("1").when(appInstanceSequence).getAppInstanceId();
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(typedValue).when(execution).getVariableTyped("executionData");
		Mockito.doReturn(request).when(typedValue).getValue();
		Mockito.doReturn(typedValue).when(execution).getVariableTyped("execution");
		Mockito.doReturn(request).when(typedValue).getValue();
		processExecutionListener.onServiceTaskExecutionStart(execution);

	}

	@Test
	void testOnServiceTaskStartNullCurrentExecution() throws Exception {
		var order = new Order();
		order.setOrderId("387284684836");
		orderFlowContext.setOrder(order);
		currentExecutionMap.put("workflowData", new ObjectMapper().writeValueAsString(orderFlowContext));
		customTransformationObjs.put("currentExecution", null);
		orderFlowContext.setWorkflowData(customTransformationObjs);
		Mockito.doReturn(activityId).when(execution).getCurrentActivityId();
		Mockito.doReturn(bpmnModelInstance).when(execution).getBpmnModelInstance();
		serviceTask.setCamundaAsyncBefore(true);
		serviceTask.setCamundaAsyncAfter(true);
		Mockito.doReturn(serviceTask).when(bpmnModelInstance).getModelElementById(activityId);
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(typedValue).when(execution).getVariableTyped("executionData");
		Mockito.doReturn(request).when(typedValue).getValue();
		Mockito.doReturn(typedValue).when(execution).getVariableTyped("execution");
		Mockito.doReturn(request).when(typedValue).getValue();
		processExecutionListener.onServiceTaskExecutionStart(execution);

	}

	@Test
	void testOnServiceTaskStartNullTransformationObject() throws Exception {

		orderFlowContext.setWorkflowData(null);
		serviceTask.setCamundaAsyncBefore(true);
		serviceTask.setCamundaAsyncAfter(true);
		Mockito.doReturn(activityId).when(execution).getCurrentActivityId();
		Mockito.doReturn(bpmnModelInstance).when(execution).getBpmnModelInstance();
		Mockito.doReturn(serviceTask).when(bpmnModelInstance).getModelElementById(activityId);
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(typedValue).when(execution).getVariableTyped("executionData");
		Mockito.doReturn(request).when(typedValue).getValue();
		Mockito.doReturn(typedValue).when(execution).getVariableTyped("execution");
		Mockito.doReturn(request).when(typedValue).getValue();
		processExecutionListener.onServiceTaskExecutionStart(execution);

	}

	//@Test
	void testOnServiceTaskStartNullExecutionData() throws Exception {
		Mockito.doReturn(activityId).when(execution).getCurrentActivityId();
		Mockito.doReturn(bpmnModelInstance).when(execution).getBpmnModelInstance();
		Mockito.doReturn(serviceTask).when(bpmnModelInstance).getModelElementById(activityId);
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(typedValue).when(execution).getVariableTyped("executionData");
		Mockito.doReturn(null).when(typedValue).getValue();
		Mockito.doReturn(typedValue).when(execution).getVariableTyped("execution");
		Mockito.doReturn(null).when(typedValue).getValue();
		processExecutionListener.onServiceTaskExecutionStart(execution);

	}

	//@Test
	void testOnServiceTaskStartElse() throws Exception {
		Mockito.doReturn(activityId).when(execution).getCurrentActivityId();
		Mockito.doReturn(bpmnModelInstance).when(execution).getBpmnModelInstance();
		Mockito.doReturn(serviceTask).when(bpmnModelInstance).getModelElementById(activityId);
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(typedValue).when(execution).getVariableTyped("executionData");
		Mockito.doReturn(request).when(typedValue).getValue();
		Mockito.doReturn(typedValue).when(execution).getVariableTyped("executionData");
		Mockito.doReturn(request).when(typedValue).getValue();
		processExecutionListener.onServiceTaskExecutionStart(execution);

	}

	@Test
	void testOnServiceTaskStartCatch() throws Exception {
		processExecutionListener.onServiceTaskExecutionStart(null);

	}

	//@Test
	void testOnCallActivityExecutionStart() throws Exception {

		var order = new Order();
		order.setOrderId("387284684836");
		orderFlowContext.setOrder(order);
		currentExecutionMap.put("workflowData", new ObjectMapper().writeValueAsString(orderFlowContext));
		customTransformationObjs.put("currentExecution", null);
		orderFlowContext.setWorkflowData(customTransformationObjs);
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(bpmnModelElementInstance).when(execution).getBpmnModelElementInstance();
		Mockito.doReturn(elementType).when(bpmnModelElementInstance).getElementType();
		Mockito.doReturn("value").when(elementType).getTypeName();
		Mockito.doReturn("2").when(execution).getVariable("loopCounter");
		Mockito.doReturn(typedValue).when(execution).getVariableTyped("executionData");
		Mockito.doReturn(request).when(typedValue).getValue();
		processExecutionListener.onCallActivityExecutionStart(execution);

	}

	//@Test
	void testOnCallActivityExecutionStartWorkflowData() throws Exception {

		var order = new Order();
		order.setOrderId("387284684836");
		orderFlowContext.setOrder(order);
		orderFlowContext.setWorkflowData(null);
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(bpmnModelElementInstance).when(execution).getBpmnModelElementInstance();
		Mockito.doReturn(elementType).when(bpmnModelElementInstance).getElementType();
		Mockito.doReturn("value").when(elementType).getTypeName();
		Mockito.doReturn("2").when(execution).getVariable("loopCounter");
		Mockito.doReturn(typedValue).when(execution).getVariableTyped("executionData");
		Mockito.doReturn(request).when(typedValue).getValue();
		processExecutionListener.onCallActivityExecutionStart(execution);

	}

	@Test
	void testOnCallActivityExecutionStartNullExecutionData() throws Exception {

		var order = new Order();
		order.setOrderId("387284684836");
		orderFlowContext.setOrder(order);
		currentExecutionMap.put("workflowData", new ObjectMapper().writeValueAsString(orderFlowContext));
		customTransformationObjs.put("currentExecution", null);
		orderFlowContext.setWorkflowData(customTransformationObjs);
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(bpmnModelElementInstance).when(execution).getBpmnModelElementInstance();
		Mockito.doReturn(elementType).when(bpmnModelElementInstance).getElementType();
		Mockito.doReturn("value").when(elementType).getTypeName();
		Mockito.doReturn("2").when(execution).getVariable("loopCounter");
		processExecutionListener.onCallActivityExecutionStart(execution);

	}

	//@Test
	void testOnCallActivityExecutionStartNullLoopCounter() throws Exception {

		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(bpmnModelElementInstance).when(execution).getBpmnModelElementInstance();
		Mockito.doReturn(elementType).when(bpmnModelElementInstance).getElementType();
		Mockito.doReturn("value").when(elementType).getTypeName();
		Mockito.doReturn(null).when(execution).getVariable("loopCounter");
		processExecutionListener.onCallActivityExecutionStart(execution);

	}

	//@Test
	void testOnCallActivityExecutionStartCatch() throws Exception {
		processExecutionListener.onCallActivityExecutionStart(null);

	}

	*/
/*@Test
	void testOnCallActivityExecutionEnd() {
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(bpmnModelElementInstance).when(execution).getBpmnModelElementInstance();
		Mockito.doReturn(elementType).when(bpmnModelElementInstance).getElementType();
		Mockito.doReturn("value").when(elementType).getTypeName();
		Mockito.doReturn("2").when(execution).getVariable("loopCounter");
		processExecutionListener.onCallActivityExecutionEnd(execution);

	}*//*


	*/
/*@Test
	void testOnCallActivityExecutionEndNullLoopCounter() {
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(bpmnModelElementInstance).when(execution).getBpmnModelElementInstance();
		Mockito.doReturn(elementType).when(bpmnModelElementInstance).getElementType();
		Mockito.doReturn("value").when(elementType).getTypeName();
		Mockito.doReturn(null).when(execution).getVariable("loopCounter");
		processExecutionListener.onCallActivityExecutionEnd(execution);
	}*//*


//	@Test
//	void testOnCallActivityExecutionEndCatch() {
//		processExecutionListener.onCallActivityExecutionEnd(null);
//
//	}

	*/
/*@Test
	void testOnExecutionEvent() {
		processExecutionListener.onExecutionEvent(executionEvent);

	}

	@Test
	void testOnHistoryEvent() {
		processExecutionListener.onHistoryEvent(historyEvent);

	}*//*


	@Test
	void testOnStartEvent() {
		Mockito.doReturn(orderFlowContext).when(execution).getVariable(WorkFlowProcessVariables.WORKFLOW_DATA.toString());
		Mockito.doReturn(bpmnModelElementInstance).when(execution).getBpmnModelElementInstance();
		Mockito.doReturn(elementType).when(bpmnModelElementInstance).getElementType();
		Mockito.doReturn("value").when(elementType).getTypeName();
		processExecutionListener.onStartEvent(execution);

	}

//	@Test
//	void testOnStartEventCatch() {
//		processExecutionListener.onStartEvent(null);
//
//	}

}
*/
