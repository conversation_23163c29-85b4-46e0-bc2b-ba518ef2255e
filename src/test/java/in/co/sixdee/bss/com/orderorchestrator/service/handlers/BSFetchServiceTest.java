package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.WorkFlowUtil;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.common.jolt.JoltUtils;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
class BSFetchServiceTest {

    @InjectMocks
    private BSFetchService bsFetchService;

    @Mock
    private GetDataFromCache cache;

    @Mock
    private WorkFlowUtil workFlowUtil;

    @Mock
    private JoltUtils joltUtils;

    @Mock
    private DelegateExecution execution;

    @Mock
    private ObjectMapper objectMapper;

    private OrderFlowContext orderFlowContext;
    private LinkedHashMap<String, String> attributes;
    private HashMap<String, Object> workflowData;
    private String reqSpecKey = "BS_FETCH_SERVICE_REQ";

    @BeforeEach
    void setUp() {
        orderFlowContext = new OrderFlowContext();
        attributes = new LinkedHashMap<>();
        workflowData = new HashMap<>();
        orderFlowContext.setAttributes(attributes);
        orderFlowContext.setWorkflowData(workflowData);
        bsFetchService.executionContext = orderFlowContext;
        bsFetchService.execution = execution;
        bsFetchService.reqSpecKey = reqSpecKey;
        bsFetchService.objectMapper = new ObjectMapper();
    }

    @Test
    void testExecute_Success() throws Exception {
        BSFetchService spyService = Mockito.spy(bsFetchService);
        doReturn("request").when(spyService).getRequestFromSpec();
        CallThirdPartyDTO callThirdPartyDTO = new CallThirdPartyDTO();
        callThirdPartyDTO.setResponse("{\"data\":[{\"serviceId\":\"123\"}]}");
        doReturn(callThirdPartyDTO).when(spyService).callThirdParty(anyString());
        doNothing().when(spyService).validateResponse(any(CallThirdPartyDTO.class));
        doNothing().when(spyService).processResponseData(anyString());
        doNothing().when(spyService).modifyWorkflowData(anyString());

        spyService.execute();

        assertFalse(orderFlowContext.isError());
        assertTrue(spyService.workflowDataUpdated);
    }

    @Test
    void testExecute_ErrorInRequest() throws Exception {
        BSFetchService spyService = Mockito.spy(bsFetchService);
        doReturn("request").when(spyService).getRequestFromSpec();
        orderFlowContext.setError(true);

        spyService.execute();

        assertTrue(orderFlowContext.isError());
    }

    @Test
    void testExecute_NullThirdPartyResponse() throws Exception {
        BSFetchService spyService = Mockito.spy(bsFetchService);
        doReturn("request").when(spyService).getRequestFromSpec();
        doReturn(null).when(spyService).callThirdParty(anyString());

        spyService.execute();

        assertTrue(orderFlowContext.isError());
    }

    @Test
    void testExecute_ErrorInValidation() throws Exception {
        BSFetchService spyService = Mockito.spy(bsFetchService);
        doReturn("request").when(spyService).getRequestFromSpec();

        CallThirdPartyDTO callThirdPartyDTO = new CallThirdPartyDTO();
        callThirdPartyDTO.setResponse("{\"data\":[{\"serviceId\":\"123\"}]}");
        doReturn(callThirdPartyDTO).when(spyService).callThirdParty(anyString());

        doAnswer(invocation -> {
            orderFlowContext.setError(true);
            return null;
        }).when(spyService).validateResponse(any(CallThirdPartyDTO.class));

        spyService.execute();

        assertTrue(orderFlowContext.isError());
    }

    @Test
    void testProcessResponseData_NullDataNode() throws Exception {
        String response = "{\"status\":\"success\"}";

        bsFetchService.processResponseData(response);

        verify(execution).setVariable("accountandProfileDeletionReg", true);
        assertFalse(orderFlowContext.isError());
    }


    @Test
    void testProcessResponseData_OneServiceMatchingId() throws Exception {
        String response = "{\"data\":[{\"serviceId\":\"123\"}]}";
        attributes.put("serviceId", "123");

        bsFetchService.processResponseData(response);

        Mockito.verify(execution).setVariable("accountandProfileDeletionReg", true);
        assertFalse(orderFlowContext.isError());
    }

    @Test
    void testProcessResponseData_OneServiceNonMatchingId() throws Exception {
        String response = "{\"data\":[{\"serviceId\":\"123\"}]}";
        attributes.put("serviceId", "456");

        bsFetchService.processResponseData(response);

        Mockito.verify(execution).setVariable("accountandProfileDeletionReg", false);
        assertTrue(orderFlowContext.isError());
    }

    @Test
    void testProcessResponseData_MultipleServices() throws Exception {
        String response = "{\"data\":[{\"serviceId\":\"123\"},{\"serviceId\":\"456\"}]}";
        bsFetchService.processResponseData(response);

        Mockito.verify(execution).setVariable("accountandProfileDeletionReg", false);
        assertTrue(orderFlowContext.isError());
    }

    @Test
    void testProcessResponseData_OneServiceNoIdInContext() throws Exception {
        String response = "{\"data\":[{\"serviceId\":\"123\"}]}";

        bsFetchService.processResponseData(response);

        Mockito.verify(execution).setVariable("accountandProfileDeletionReg", false);
        assertTrue(orderFlowContext.isError());
    }
}