//package in.co.sixdee.bss.com.orderorchestrator.service;
//
//import java.util.ArrayList;
//import java.util.LinkedHashMap;
//import java.util.List;
//import java.util.Map;
//
//import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
//import org.camunda.bpm.engine.RuntimeService;
//import org.junit.jupiter.api.AfterAll;
//import org.junit.jupiter.api.AfterEach;
//import org.junit.jupiter.api.Assertions;
//import org.junit.jupiter.api.BeforeAll;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.junit.platform.runner.JUnitPlatform;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//
//import in.co.sixdee.bss.com.orderorchestrator.config.camunda.modification.ProcessInstanceModificationHandler;
//import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
//import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.WaitingProcessInfoEntity;
//import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderStageRepository;
//import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.WaitingProcessInfoRepository;
//import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
//import in.co.sixdee.bss.om.model.dto.Response;
//import in.co.sixdee.bss.om.model.dto.WorkflowRequest;
//
//@ExtendWith(MockitoExtension.class)
//@RunWith(JUnitPlatform.class)
//class OrderCallBackServiceTest {
//
//	@InjectMocks
//	private OrderCallBackService				orderCallBackService;
//
//	@Mock
//	private ProcessInstanceModificationHandler	processInstanceModificationHandler;
//
//	@Mock
//	private OrderStageRepository				orderStageRepository;
//
//	@Mock
//	private ObjectMapper						objectMapper;
//
//	@Mock
//	private RuntimeService						runtimeService;
//
//	@Mock
//	WaitingProcessInfoRepository				waitingProcessInfoRepository;
//
//	static WaitingProcessInfoEntity waitingProcessInfoEntity;
//	static OrderCallBack            callback;
//	static OrderFlowContext         orderFlowcontext;
//	static List<Map<String, Object>>			callBackDetailsList;
//	static Map<String, Object>					callBackDetails;
//	static WorkflowRequest						request;
//
//	@BeforeAll
//	static void setUpBeforeClass() throws Exception {
//		callback = new OrderCallBack();
//		orderFlowcontext = new OrderFlowContext();
//		callBackDetailsList = new ArrayList<>();
//	}
//
//	@AfterAll
//	static void tearDownAfterClass() throws Exception {
//		orderFlowcontext = null;
//		callBackDetailsList = null;
//		callback = null;
//		waitingProcessInfoEntity = null;
//		callBackDetails = null;
//		request = null;
//	}
//
//	@BeforeEach
//	void setUp() throws Exception {
//		waitingProcessInfoEntity = new WaitingProcessInfoEntity();
//		orderFlowcontext.setAttributes(new LinkedHashMap<>());
//		orderFlowcontext.setWorkflowData(new LinkedHashMap<>());
//		callBackDetails = new LinkedHashMap<String, Object>();
//		request = new WorkflowRequest();
//	}
//
//	@AfterEach
//	void tearDown() throws Exception {}
//
//	@Test
//	void testProcessContinueRequest() throws Exception {
//		callBackDetails.put("request", "{}");
//		callBackDetailsList.add(callBackDetails);
//		orderFlowcontext.getWorkflowData().put("callBackRequest", callBackDetailsList);
//		waitingProcessInfoEntity.setEventName("Signal");
//		waitingProcessInfoEntity.setProcessInstanceId("Onboarding");
//		waitingProcessInfoEntity.setExecutionId("123456789");
//		callback.setCorrelationId("Signal:Start");
//		callback.setOrderId("23456789");
//		// Mockito.doReturn(waitingProcessInfoEntity).when(waitingProcessInfoRepository)
//		// .findProcessInfoByOrderId(callback.getOrderId());
//		// Mockito.doReturn(orderFlowcontext).when(runtimeService).getVariable(waitingProcessInfoEntity.getExecutionId(),
//		// WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString());
//		// Mockito.doReturn(orderFlowcontext).when(objectMapper).readValue(orderFlowcontext.toString(),
//		// OrderFlowContext.class);
//		// Mockito.doReturn(new MessageCorrelationBuilderImpl(new CommandContext(new
//		// JtaProcessEngineConfiguration()),
//		// null)).when(runtimeService).createMessageCorrelation(waitingProcessInfoEntity.getEventName());
//		Assertions.assertThrows(NullPointerException.class, () -> {
//			orderCallBackService.processContinueRequest(callback);
//		});
//
//	}
//
//	@Test
//	void testProcessContinueRequestException() throws Exception {
//		waitingProcessInfoEntity.setEventName("Signal");
//		waitingProcessInfoEntity.setProcessInstanceId("Onboarding");
//		waitingProcessInfoEntity.setExecutionId("123456789");
//		callback.setCorrelationId("Signal:Start");
//		callback.setOrderId("23456789");
//		Assertions.assertThrows(NullPointerException.class, () -> {
//			orderCallBackService.processContinueRequest(callback);
//		});
//
//	}
//
//	@Test
//	void testvalidateCallback() {
//		orderCallBackService.validateCallback(callback, null);
//	}
//
//	@Test
//	void testvalidateCallbackException() {
//		callback.setCorrelationId("234567:ertyui");
//		Assertions.assertThrows(CommonException.class, () -> {
//			orderCallBackService.validateCallback(callback, "dfghj");
//		});
//	}
//
////	@Test
////	void testGetWaitEventInfo() {
////		Assertions.assertThrows(CommonException.class, () -> {
////			orderCallBackService.getWaitEventInfo("234567", "876543");
////		});
////	}
//
//	@Test
//	void testsetCallBackRequestToorderFlowContext() {
//		orderCallBackService.setCallBackRequestToorderFlowContext(orderFlowcontext, callback);
//	}
//
//	@Test
//	void testProcessModifyRequest() {
//		request.setAction("skip");
//		request.setOrderId("2345678");
//		request.setSubOrderId("876543");
//		request.setStageId("ORD_ACK");
//		Mockito.doReturn(true).when(orderStageRepository).existsByOrderIdsubOrderIdandName(Long.valueOf(request.getOrderId()),
//				Long.valueOf(request.getSubOrderId()), request.getStageId());
//		Response processModifyRequest = orderCallBackService.processModifyRequest(request);
//		Assertions.assertNotNull(processModifyRequest);
//
//	}
//
//	@Test
//	void testProcessModifyRequestException() {
//		request.setAction("retry");
//		request.setOrderId("2345678");
//		request.setSubOrderId("876543");
//		request.setStageId("ORD_ACK");
//		Mockito.doReturn(false).when(orderStageRepository).existsByOrderIdsubOrderIdandName(Long.valueOf(request.getOrderId()),
//				Long.valueOf(request.getSubOrderId()), request.getStageId());
//
//		Assertions.assertThrows(CommonException.class, () -> {
//			orderCallBackService.processModifyRequest(request);
//		});
//
//	}
//
//	@Test
//	void testcreateResponse() {
//		Response createResponse = orderCallBackService.createResponse(callback);
//		Assertions.assertNotNull(createResponse);
//	}
//
//}
