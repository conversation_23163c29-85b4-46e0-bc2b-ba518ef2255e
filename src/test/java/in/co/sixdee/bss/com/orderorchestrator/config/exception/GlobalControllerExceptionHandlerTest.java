/*
package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import java.util.List;

import in.co.sixdee.bss.com.orderorchestrator.web.errors.GlobalControllerExceptionHandler;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants.HttpConstants;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
class GlobalControllerExceptionHandlerTest {

	@InjectMocks
	private GlobalControllerExceptionHandler globalControllerExceptionHandler;

	@Mock
	private CommonExceptionResponse commonExceptionResponse;

	private CommonException commonException;
	private Throwable throwable;

	private StatusConstants.HttpConstants status;
	private List<String> errors;


	@BeforeAll
	static void setUpBeforeClass() throws Exception {
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
	}

	@BeforeEach
	void setUp() throws Exception {
	}

	@AfterEach
	void tearDown() throws Exception {
	}





	*/
/*@Test
	void testCommonException() {
		commonException = new CommonException("INTERNAL SERVER ERROR");
		commonException.setMessage("NOT FOUND");
		commonException.setStatus(HttpConstants.INTERNAL_SERVER_ERROR);
		commonException.setErrors(errors);
		globalControllerExceptionHandler.commonException(commonException);

	}
*//*

	*/
/*
	 * @Test void testCommonException1() { commonException = new CommonException("BAD_REQUEST");
	 * commonException.setStatus(null);
	 * globalControllerExceptionHandler.commonException(commonException);
	 * 
	 * }
	 *//*


	*/
/*
	 * @Test void testCommonException2() { Assertions.assertThrows(.class, () -> {
	 * globalControllerExceptionHandler.commonException(null); }); }
	 *//*


}
*/
