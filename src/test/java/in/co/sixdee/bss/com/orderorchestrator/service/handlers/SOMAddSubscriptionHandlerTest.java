package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import static org.mockito.Mockito.mock;

import java.io.File;
import java.net.URI;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.connector.rest.WebClientConnector;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.om.model.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.CFSCharacteristicRef;
import in.co.sixdee.bss.om.model.dto.order.CFSRef;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.OrderItem;
import in.co.sixdee.bss.om.model.dto.order.Product;
import in.co.sixdee.bss.om.model.dto.order.ProductOffering;
import in.co.sixdee.bss.om.model.dto.order.ProductSpecificationRef;
import in.co.sixdee.bss.om.model.dto.order.Service;
import in.co.sixdee.bss.om.model.dto.order.Subscription;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
class SOMAddSubscriptionHandlerTest {

	@InjectMocks
	private SOMAddSubscriptionHandler		somAddSubscriptionHandler;

	@Mock
	private WebClientConnector restConnector;

	@Mock
	GetDataFromCache						cache;

	static OrderFlowContext					orderFlowContext;
	static Map<String, Object>				kiMap;
	static DelegateExecution				execution;
	static Map<String, Object>				variables;
	static Order							orderPayload;
	static String							upcResponse;
	static HashMap<String, Object>			workflowData;
	static Map<String, Object>				currentExecutionMap;
	static String							BSAddSubscriptionRequest;
	static Service						service;
	static List<Subscription>				subscriptions;
	static Subscription						subscription;
	static HashMap<String, String>			thirdPartyCallDetails;
	static HashMap<String, String>			subOrderAttribute;
	static List<HashMap<String, String>>	subOrderAttributes;
	static CacheTableDataDTO				cacheTable;
	static List<OrderItem>					orderItems;
	static OrderItem						orderItem;
	static ProductOffering					productOffering;
	static Product							product;
	static ProductOffering					productOfferingCfss;
	static ProductSpecificationRef			productSpecification;

	@BeforeAll
	static void setUpBeforeClass() throws Exception {
		execution = mock(DelegateExecution.class);
		orderItems = new ArrayList<>();

	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
		workflowData = null;
		productSpecification = null;
		orderPayload = null;
		variables = null;
		productOffering = null;
		kiMap = null;
		thirdPartyCallDetails = null;
		orderFlowContext = null;
		currentExecutionMap = null;
		service = null;
		subscriptions = null;
		subscription = null;
		subOrderAttributes = null;
		subOrderAttribute = null;
		cacheTable = null;
		orderItems = null;
		orderItem = null;
		product = null;
		productOfferingCfss = null;
	}

	@BeforeEach
	void setUp() throws Exception {
		cacheTable = new CacheTableDataDTO();
		productOffering = new ProductOffering();
		orderItem = new OrderItem();
		productOfferingCfss = new ProductOffering();
		kiMap = new LinkedHashMap<String, Object>();
		currentExecutionMap = new HashMap<String, Object>();
		orderFlowContext = new OrderFlowContext();
		orderFlowContext.setAttributes(new LinkedHashMap<>());
		orderFlowContext.setSubOrderAttributes(new HashMap<>());
		subOrderAttributes = new ArrayList<>();
		subOrderAttribute = new HashMap<>();
		variables = new LinkedHashMap<String, Object>();
		variables.put("workflowData", orderFlowContext);
		variables.put("thirdPartyId", "BILLING");
		service = new Service();
		service.setServiceId("234567");
		variables.put("Status", "0");
		execution.setVariables(variables);
		subscriptions = new ArrayList<>();
		subscription = new Subscription();
		product = new Product();
		thirdPartyCallDetails = new LinkedHashMap<String, String>();
		orderPayload = new Order();
		workflowData = new HashMap<>();
		productSpecification = new ProductSpecificationRef();
		upcResponse = FileUtils.readFileToString(new File("src/test/resources/UPC_Response.json"));
		thirdPartyCallDetails.put(CacheConstants.CacheFields.TRANSPORT_METHOD.name(), "POST");
		thirdPartyCallDetails.put(CacheConstants.CacheFields.URL.name(), "http://234567");
		thirdPartyCallDetails.put("EXPECTED_HTTP_CODE", "200");
	}

	@AfterEach
	void tearDown() throws Exception {}

	//@Test
	void testExecute() throws Exception {
		Mockito.doReturn(variables).when(execution).getVariables();
		orderPayload.setDescription("Orders");
		orderPayload.setRequestedStartDate("2013-09-29T18:46:19Z");
		orderPayload.setRequestedCompletionDate("2013-09-29T18:46:19Z");
		orderPayload.setOrderType(OrderTypes.ADD_SUBSCRIPTION);
		List<CFSRef> cfss = new ArrayList<>();
		List<CFSCharacteristicRef> characteristics = new ArrayList<>();
		CFSCharacteristicRef charac = new CFSCharacteristicRef();
		charac.setName("dfghj");
		characteristics.add(charac);
		CFSRef cfs = new CFSRef();
		cfs.setSkipSom("false");
		cfss.add(cfs);
		cfs.setCharacteristics(characteristics);
		productOffering.setId("34567");
		orderItem.setProductOffering(productOffering);
		productSpecification.setCfss(cfss);
		product.setProductSpecification(productSpecification);
		orderItem.setProduct(product);
		service.setServiceId("23456789");
		orderItem.setService(service);
		orderItems.add(orderItem);
		orderPayload.setOrderItem(orderItems);
		orderFlowContext.setOrder(orderPayload);
		CallThirdPartyDTO callDetails = new CallThirdPartyDTO();
		cacheTable.setNgTableData(thirdPartyCallDetails);
		Mockito.doReturn(cacheTable).when(cache)
				.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(), "BILLING");
		callDetails.setResponseCode("200");
		Mockito.doReturn(callDetails).when(restConnector).service("POST", new URI("http://234567"), 3000, 3000,
				"{\r\n"
				+ "  \"description\" : \"Orders\",\r\n"
				+ "  \"requestedCompletionDate\" : \"2013-09-29T18:46:19Z\",\r\n"
				+ "  \"requestedStartDate\" : \"2013-09-29T18:46:19Z\",\r\n"
				+ "  \"serviceOrderItem\" : [ {\r\n"
				+ "    \"id\" : \"1\",\r\n"
				+ "    \"action\" : \"add\",\r\n"
				+ "    \"service\" : {\r\n"
				+ "      \"state\" : \"active\",\r\n"
				+ "      \"type\" : \"CFS\",\r\n"
				+ "      \"serviceCharacteristic\" : [ {\r\n"
				+ "        \"name\" : \"dfghj\"\r\n"
				+ "      }, {\r\n"
				+ "        \"name\" : \"SUBSCRIPTION_ID\",\r\n"
				+ "        \"valueType\" : \"String\",\r\n"
				+ "        \"value\" : \"980045\"\r\n"
				+ "      } ],\r\n"
				+ "      \"serviceSpecification\" : { }\r\n"
				+ "    },\r\n"
				+ "    \"@type\" : \"ServiceOrderItem\"\r\n"
				+ "  } ],\r\n"
				+ "  \"@type\" : \"ServiceOrder\"\r\n"
				+ "}",
				null);
		orderFlowContext.getAttributes();
		orderFlowContext.getAttributes().put("subscriptionId", "34567");
		workflowData.put("UPCQueryResponse", com.bazaarvoice.jolt.JsonUtils.jsonToObject(upcResponse));
		orderFlowContext.setWorkflowData(workflowData);
		subOrderAttribute.put("34567", "980045");
		subOrderAttribute.put("subscriptionId", "980045");
		subOrderAttributes.add(subOrderAttribute);
		orderFlowContext.getWorkflowData().put("subscriptionIdMap", subOrderAttribute);
		orderFlowContext.getSubOrderAttributes().put("subscriptionIdMap", subOrderAttributes);
		somAddSubscriptionHandler.executionContext = orderFlowContext;
		somAddSubscriptionHandler.thirdPartyCallDetails = thirdPartyCallDetails;

		somAddSubscriptionHandler.executionVariables = variables;
		somAddSubscriptionHandler.execute();
		Assertions.assertFalse(orderFlowContext.isError());

	}

	//@Test
	void testExecuteNullResponse() throws Exception {
		Mockito.doReturn(variables).when(execution).getVariables();
		orderPayload.setDescription("Orders");
		orderPayload.setRequestedStartDate("2013-09-29T18:46:19Z");
		orderPayload.setRequestedCompletionDate("2013-09-29T18:46:19Z");
		orderPayload.setOrderType(OrderTypes.ADD_SUBSCRIPTION);
		List<CFSRef> cfss = new ArrayList<>();
		CFSRef cfs = new CFSRef();
		cfs.setSkipSom("false");
		cfss.add(cfs);
		productOffering.setId("34567");
		orderItem.setProductOffering(productOffering);
		productSpecification.setCfss(cfss);
		product.setProductSpecification(productSpecification);
		orderItem.setProduct(product);
		service.setServiceId("23456789");
		orderItem.setService(service);
		orderItems.add(orderItem);
		orderPayload.setOrderItem(orderItems);
		orderFlowContext.setOrder(orderPayload);
		CallThirdPartyDTO callDetails = new CallThirdPartyDTO();
		cacheTable.setNgTableData(thirdPartyCallDetails);
		Mockito.doReturn(cacheTable).when(cache)
				.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(), "BILLING");
		callDetails.setResponseCode("200");
		Mockito.doReturn(null).when(restConnector).service("POST", new URI("http://234567"), 3000, 3000,
				"{\r\n"
				+ "  \"description\" : \"Orders\",\r\n"
				+ "  \"requestedCompletionDate\" : \"2013-09-29T18:46:19Z\",\r\n"
				+ "  \"requestedStartDate\" : \"2013-09-29T18:46:19Z\",\r\n"
				+ "  \"serviceOrderItem\" : [ {\r\n"
				+ "    \"id\" : \"1\",\r\n"
				+ "    \"action\" : \"add\",\r\n"
				+ "    \"service\" : {\r\n"
				+ "      \"state\" : \"active\",\r\n"
				+ "      \"type\" : \"CFS\",\r\n"
				+ "      \"serviceCharacteristic\" : [ {\r\n"
				+ "        \"name\" : \"dfghj\"\r\n"
				+ "      }, {\r\n"
				+ "        \"name\" : \"SUBSCRIPTION_ID\",\r\n"
				+ "        \"valueType\" : \"String\",\r\n"
				+ "        \"value\" : \"980045\"\r\n"
				+ "      } ],\r\n"
				+ "      \"serviceSpecification\" : { }\r\n"
				+ "    },\r\n"
				+ "    \"@type\" : \"ServiceOrderItem\"\r\n"
				+ "  } ],\r\n"
				+ "  \"@type\" : \"ServiceOrder\"\r\n"
				+ "}",
				null);
		orderFlowContext.getAttributes();
		orderFlowContext.getAttributes().put("subscriptionId", "34567");
		workflowData.put("UPCQueryResponse", com.bazaarvoice.jolt.JsonUtils.jsonToObject(upcResponse));
		orderFlowContext.setWorkflowData(workflowData);
		subOrderAttribute.put("planId", "1");
		subOrderAttribute.put("subscriptionId", "980045");
		subOrderAttributes.add(subOrderAttribute);
		orderFlowContext.getSubOrderAttributes().put("subscriptionIdMap", subOrderAttributes);
		somAddSubscriptionHandler.executionContext = orderFlowContext;
		somAddSubscriptionHandler.thirdPartyCallDetails = thirdPartyCallDetails;
		somAddSubscriptionHandler.executionVariables = variables;
		somAddSubscriptionHandler.execute();
		Assertions.assertFalse(orderFlowContext.isError());

	}

	//@Test
	void testExecuteInvalidResponse() throws Exception {
		Mockito.doReturn(variables).when(execution).getVariables();
		orderPayload.setDescription("Orders");
		orderPayload.setRequestedStartDate("2013-09-29T18:46:19Z");
		orderPayload.setRequestedCompletionDate("2013-09-29T18:46:19Z");
		orderPayload.setOrderType(OrderTypes.ADD_SUBSCRIPTION);
		List<CFSRef> cfss = new ArrayList<>();
		CFSRef cfs = new CFSRef();
		cfs.setSkipSom("false");
		cfss.add(cfs);
		productOffering.setId("34567");
		orderItem.setProductOffering(productOffering);
		productSpecification.setCfss(cfss);
		product.setProductSpecification(productSpecification);
		orderItem.setProduct(product);
		service.setServiceId("23456789");
		orderItem.setService(service);
		orderItems.add(orderItem);
		orderPayload.setOrderItem(orderItems);
		orderFlowContext.setOrder(orderPayload);
		CallThirdPartyDTO callDetails = new CallThirdPartyDTO();
		cacheTable.setNgTableData(thirdPartyCallDetails);
		Mockito.doReturn(cacheTable).when(cache)
				.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(), "BILLING");
		callDetails.setResponseCode("404");
		Mockito.doReturn(callDetails).when(restConnector).service("POST", new URI("http://234567"), 3000, 3000,
				"{\r\n"
				+ "  \"description\" : \"Orders\",\r\n"
				+ "  \"requestedCompletionDate\" : \"2013-09-29T18:46:19Z\",\r\n"
				+ "  \"requestedStartDate\" : \"2013-09-29T18:46:19Z\",\r\n"
				+ "  \"serviceOrderItem\" : [ {\r\n"
				+ "    \"id\" : \"1\",\r\n"
				+ "    \"action\" : \"add\",\r\n"
				+ "    \"service\" : {\r\n"
				+ "      \"state\" : \"active\",\r\n"
				+ "      \"type\" : \"CFS\",\r\n"
				+ "      \"serviceCharacteristic\" : [ {\r\n"
				+ "        \"name\" : \"dfghj\"\r\n"
				+ "      }, {\r\n"
				+ "        \"name\" : \"SUBSCRIPTION_ID\",\r\n"
				+ "        \"valueType\" : \"String\",\r\n"
				+ "        \"value\" : \"980045\"\r\n"
				+ "      } ],\r\n"
				+ "      \"serviceSpecification\" : { }\r\n"
				+ "    },\r\n"
				+ "    \"@type\" : \"ServiceOrderItem\"\r\n"
				+ "  } ],\r\n"
				+ "  \"@type\" : \"ServiceOrder\"\r\n"
				+ "}",
				null);
		orderFlowContext.getAttributes();
		orderFlowContext.getAttributes().put("subscriptionId", "34567");
		workflowData.put("UPCQueryResponse", com.bazaarvoice.jolt.JsonUtils.jsonToObject(upcResponse));
		orderFlowContext.setWorkflowData(workflowData);
		subOrderAttribute.put("planId", "1");
		subOrderAttribute.put("subscriptionId", "980045");
		subOrderAttributes.add(subOrderAttribute);
		orderFlowContext.getSubOrderAttributes().put("subscriptionIdMap", subOrderAttributes);
		somAddSubscriptionHandler.executionContext = orderFlowContext;
		somAddSubscriptionHandler.thirdPartyCallDetails = thirdPartyCallDetails;
		somAddSubscriptionHandler.executionVariables = variables;
		somAddSubscriptionHandler.execute();
		Assertions.assertFalse(orderFlowContext.isError());

	}

	//@Test
	void testExecuteException() throws Exception {
		Mockito.doReturn(variables).when(execution).getVariables();
		orderPayload.setDescription("Orders");
		orderPayload.setRequestedCompletionDate("2013-09-29T18:46:19Z");
		orderPayload.setOrderType(OrderTypes.ADD_SUBSCRIPTION);
		List<CFSRef> cfss = new ArrayList<>();
		CFSRef cfs = new CFSRef();
		cfs.setSkipSom("false");
		cfss.add(cfs);
		subscription.setCfss(cfss);
		subscription.setPrss(cfss);
		subscription.setLrss(cfss);
		productOffering.setId("34567");
		orderItem.setProductOffering(productOffering);
		productSpecification.setCfss(cfss);
		product.setProductSpecification(productSpecification);
		orderItem.setProduct(product);
		service.setServiceId("23456789");
		orderItem.setService(service);
		orderItems.add(orderItem);
		orderPayload.setOrderItem(orderItems);
		orderFlowContext.setOrder(orderPayload);
		CallThirdPartyDTO callDetails = new CallThirdPartyDTO();
		cacheTable.setNgTableData(thirdPartyCallDetails);
		Mockito.doReturn(cacheTable).when(cache)
				.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(), "BILLING");
		callDetails.setResponseCode("404");
		Mockito.doReturn(callDetails).when(restConnector).service("POST", new URI("http://234567"), 3000, 3000,
				"{\r\n"
				+ "  \"description\" : \"Orders\",\r\n"
				+ "  \"requestedCompletionDate\" : \"2013-09-29T18:46:19Z\",\r\n"
				+ "  \"requestedStartDate\" : \"2013-09-29T18:46:19Z\",\r\n"
				+ "  \"serviceOrderItem\" : [ {\r\n"
				+ "    \"id\" : \"1\",\r\n"
				+ "    \"action\" : \"add\",\r\n"
				+ "    \"service\" : {\r\n"
				+ "      \"state\" : \"active\",\r\n"
				+ "      \"type\" : \"CFS\",\r\n"
				+ "      \"serviceCharacteristic\" : [ {\r\n"
				+ "        \"name\" : \"dfghj\"\r\n"
				+ "      }, {\r\n"
				+ "        \"name\" : \"SUBSCRIPTION_ID\",\r\n"
				+ "        \"valueType\" : \"String\",\r\n"
				+ "        \"value\" : \"980045\"\r\n"
				+ "      } ],\r\n"
				+ "      \"serviceSpecification\" : { }\r\n"
				+ "    },\r\n"
				+ "    \"@type\" : \"ServiceOrderItem\"\r\n"
				+ "  } ],\r\n"
				+ "  \"@type\" : \"ServiceOrder\"\r\n"
				+ "}",
				null);
		orderFlowContext.getAttributes();
		orderFlowContext.getAttributes().put("subscriptionId", "34567");
		workflowData.put("UPCQueryResponse", com.bazaarvoice.jolt.JsonUtils.jsonToObject(upcResponse));
		orderFlowContext.setWorkflowData(workflowData);

		subOrderAttribute.put("planId", "34567");
		subOrderAttribute.put("subscriptionId", "980045");
		orderFlowContext.getWorkflowData().put("subscriptionIdMap", subOrderAttribute);
		subOrderAttributes.add(subOrderAttribute);
		orderFlowContext.getSubOrderAttributes().put("subscriptionIdMap", subOrderAttributes);
		somAddSubscriptionHandler.executionContext = orderFlowContext;
		somAddSubscriptionHandler.thirdPartyCallDetails = thirdPartyCallDetails;
		somAddSubscriptionHandler.executionVariables = variables;
		somAddSubscriptionHandler.execute();
		Assertions.assertFalse(orderFlowContext.isError());

	}

	@Test
	void testCreateSOMRequest() {

	}

	@Test
	void testCreateServiceOrderItem() {

	}

	@Test
	void testCreateServiceItem() {

	}

	//@Test
	void testSetSubscriptionId() {
		List<Characteristic> characteristics = new ArrayList<>();
		orderFlowContext.setWorkflowData(new HashMap<>());
		orderFlowContext.getWorkflowData().put("subscriptionIdMap", null);
		somAddSubscriptionHandler.executionContext = orderFlowContext;
		somAddSubscriptionHandler.setSubscriptionId(characteristics);

	}

	@Test
	void testCreateServiceCharacteristic() {

	}

}
