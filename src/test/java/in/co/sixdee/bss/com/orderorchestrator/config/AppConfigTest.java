/**
 * 
 *//*

package in.co.sixdee.bss.com.orderorchestrator.config;

import java.util.ArrayList;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

import in.co.sixdee.bss.common.cache.CacheManager;
import in.co.sixdee.bss.common.core.AppInstanceIdManager;
import in.co.sixdee.bss.common.vaidation.ValidationUtils;

*/
/**
 * <AUTHOR> in.co.sixdee.bss.common.vaidation.ValidationUtils;STS
 *
 *//*

@RunWith(JUnitPlatform.class)	
@ExtendWith(MockitoExtension.class)
class AppConfigTest {

	@InjectMocks
	private AppConfig							appConfig;


	@Mock
	private ValidationUtils						validationUtils;
	
	@Mock
	CacheManager cacheManager;

	@Mock
	private AppInstanceIdManager		getAppInstanceSequenceDetails;

	static private InterceptorRegistry			interceptorRegistry;
	static private InterceptorRegistration		interceptorRegistration;

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@BeforeAll
	static void setUpBeforeClass() throws Exception {
		interceptorRegistry = Mockito.mock(InterceptorRegistry.class);
		interceptorRegistration = Mockito.mock(InterceptorRegistration.class);
	}

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@AfterAll
	static void tearDownAfterClass() throws Exception {}

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@BeforeEach
	void setUp() throws Exception {}

	*/
/**
	 * @throws java.lang.Exception
	 *//*

	@AfterEach
	void tearDown() throws Exception {}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.ordervalidation.config.AppConfig#addInterceptors(org.springframework.web.servlet.config.annotation.InterceptorRegistry)}.
	 *//*

	@Test
	void testAddInterceptors() {
		var patterns = new ArrayList<String>();
		patterns.add("/order-validation");
		Mockito.doReturn(interceptorRegistration).when(interceptorRegistration).addPathPatterns(patterns);
		appConfig.addInterceptors(interceptorRegistry);
	}

	*/
/**
	 * Test method for {@link in.co.sixdee.bss.com.ordervalidation.config.AppConfig#init()}.
	 *//*

	@Test
	void testInit() {
		appConfig.initApp();
	}

}
*/
