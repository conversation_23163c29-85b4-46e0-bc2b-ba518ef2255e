/*
package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants.HttpConstants;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
class EntityValidationExceptionTest {

	@InjectMocks
	private EntityValidationException entityValidationException;
	
	private StatusConstants.HttpConstants status;
	private String                        message;
	
	@BeforeAll
	static void setUpBeforeClass() throws Exception {
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
	}

	@BeforeEach
	void setUp() throws Exception {
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void testPayloadValidationExceptionHttpConstantsStringListOfWorkFlowErrorBean() {
		entityValidationException = new EntityValidationException(HttpConstants.INTERNAL_SERVER_ERROR,message);

	}


	@Test
	void testPayloadValidationExceptionString() {
		entityValidationException = new EntityValidationException(message);
	
	}

	@Test
	void testGetStatus() {
		entityValidationException.getStatus();
	
	}

	@Test
	void testSetStatus() {
		entityValidationException.setStatus(status);
	
	}

	@Test
	void testGetMessage() {
		entityValidationException.getMessage();
	
	}

	@Test
	void testSetMessage() {
		entityValidationException.setMessage("VALIDATION FAILED");
	
	}

}
*/
