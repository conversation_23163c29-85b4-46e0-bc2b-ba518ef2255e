/*
package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import java.util.List;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants.HttpConstants;

@RunWith(JUnitPlatform.class)
@ExtendWith(MockitoExtension.class)
class CommonExceptionTest {

	private StatusConstants.HttpConstants status;
	private String message;
	private List<String> errors;
	private String error;

	@InjectMocks
	private CommonException commonException;

	@BeforeAll
	static void setUpBeforeClass() throws Exception {
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
	}

	@BeforeEach
	void setUp() throws Exception {
		message = "NOT FOUND";

	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void testCommonException() {

		commonException = new CommonException("test");
	}

	@Test
	void testCommonException1() {

		commonException = new CommonException(status, "test");
	}

	@Test
	void testCommonException2() {

		commonException = new CommonException(status, "test", errors);
	}

	@Test
	void testCommonException3() {

		commonException = new CommonException(status, "test", error);
	}

	@Test
	public void testSetMessage() {
		commonException = new CommonException(message);
		commonException.setMessage("NOT FOUND");
	}

	@Test
	public void testSetStatus() {
		commonException = new CommonException(message);
		commonException.setStatus(HttpConstants.SUCCESS);
	}

	@Test
	public void testSetErrors() {
		commonException = new CommonException(message);
		commonException.setErrors(errors);
	}

	@Test
	public void testGetMessage() {

		String getMessage = commonException.getMessage();
	}

	@Test
	public void testGetStatus() {

		commonException = new CommonException(message);
		StatusConstants.HttpConstants testGetStatus = commonException.getStatus();
	}
	
	@Test
	public void testGetErrors() {
		 commonException.getErrors();
	}
	
	

}
*/
