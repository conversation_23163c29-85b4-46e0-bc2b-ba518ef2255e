package in.co.sixdee.bss.com.orderorchestrator.service;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.connector.RestConnector;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.om.model.dto.BatchCallBackRequestDTO;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext.ErrorDetail;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.OrderStateType;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
class BatchCallBackServiceTest {

    @InjectMocks
	private BatchCallBackService batchCallBackService;
	@Mock
	private GetDataFromCache cache;
    @Mock
	private RestConnector restConnector;
	
    
	static OrderFlowContext			executionContext;
	static BatchCallBackRequestDTO  batchCallBackRequestDTO;
	static Order   order;
	static OrderStateType stateType;
	static ErrorDetail errorDetail;
	static CacheTableDataDTO thirdPartyUrlConfig;
	static CallThirdPartyDTO callThirdPartyDTO;
	static HashMap<String, String> ngtable;
	static Map<String, String>		headerMap;
	static String 	callBackrequest;

	
	@BeforeAll
	static void setUpBeforeClass() throws Exception {
		executionContext= new OrderFlowContext();
		batchCallBackRequestDTO=new BatchCallBackRequestDTO();
		order= new Order();
		callThirdPartyDTO = new CallThirdPartyDTO();
		ngtable= new HashMap<String, String>();
		headerMap=new HashMap<String, String>();
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
		executionContext=null;
		batchCallBackRequestDTO=null;
		order=null;
		callThirdPartyDTO=null;
		ngtable=null;
		headerMap=null;
	}

	@BeforeEach
	void setUp() throws Exception {
		executionContext.setBatchId("928973290228486144");
		executionContext.setBatchRowSeqId("928973111111116144");
		executionContext.setOrder(order);
		executionContext.getOrder().setOrderId("930444659209736192");
		executionContext.getOrder().setState(stateType.COMPLETED);
		executionContext.setErrorDetail(errorDetail);
		thirdPartyUrlConfig=new CacheTableDataDTO();
		thirdPartyUrlConfig.setNgTableData(ngtable);
		callBackrequest=FileUtils.readFileToString(new File("src/test/resources/BatchCallBack_Request.json"));
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	//@Test
	void testSendCallBackRequest() {
		String request= batchCallBackService.createCallBackRequest(executionContext);
		 Assertions.assertEquals(callBackrequest,request );
		 Mockito.doReturn(thirdPartyUrlConfig).when(cache).getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(), "batch-service");
		 batchCallBackService.sendCallBackRequest(executionContext);
		 
	}

	@Test
	void testCreateCallBackRequest() {
		batchCallBackRequestDTO.setBatchId(executionContext.getBatchId());
		batchCallBackRequestDTO.setBatchRowSeqId(executionContext.getBatchRowSeqId());
		batchCallBackRequestDTO.setOrderId(executionContext.getOrder().getOrderId()!=null? executionContext.getOrder().getOrderId():null);
		batchCallBackRequestDTO.setStatus(executionContext.getOrder().getState().name());
		batchCallBackRequestDTO.setFailureReason(executionContext.getErrorDetail().getSystem() != null
				? executionContext.getErrorDetail().getSystem() + ":" + executionContext.getErrorDetail().getMessage()
				: "");
		batchCallBackService.createCallBackRequest(executionContext);
	}

	@Test
	void testInitThirdPartyCallDetailsConfigurationNotValidException()  {
		thirdPartyUrlConfig=null;
		Assertions.assertThrows(ConfigurationNotValidException.class,()->batchCallBackService.initThirdPartyCallDetails(callBackrequest));
		
	}
	
	@Test
	void testInitThirdPartyCallDetails()  {
		Mockito.doReturn(thirdPartyUrlConfig).when(cache).getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(), "batch-service");
		batchCallBackService.initThirdPartyCallDetails(callBackrequest);
	}

	@Test
	void testCallThirdParty() {
		batchCallBackService.callThirdParty(executionContext, callThirdPartyDTO);
	}

	@Test
	void testGetHeaderMap() {
		Map<String,String> headerMap=new HashMap<String,String>();
		headerMap.put("Content-Type", "application/json");
		callThirdPartyDTO.setHeaders("Content-Type=application/json,channel=@($.channel),username=admin,X-Trace-Id=@($.traceId)");
		Assertions.assertEquals(headerMap.get("Content-Type"),  batchCallBackService.getHeaderMap(callThirdPartyDTO.getHeaders()).get("Content-Type"));
	}

	//@Test
	void testTransformHeaders() {
		headerMap.put("Content-Type", "application/json");
		headerMap.put("request-id", "{workitemRequestId}");
		headerMap.put(",channel-id", "@($.errorDetail.code)");
		headerMap.put("sourcenode", "@($.CHANNEL_REQUEST.Header.source_node)");
		headerMap.put("requestTimestamp", "date(epoch)");
		headerMap.put("timestamp", "date(YYYY-MM-dd hh:mm:ss)");
		ErrorDetail errorDetail = new ErrorDetail("200", "Success", "");
		
		executionContext.setErrorDetail(errorDetail);

		Map<String, String> transformHeaders = batchCallBackService.transformHeaders(headerMap, executionContext);
		Assertions.assertEquals(headerMap.get("Content-Type"), transformHeaders.get("Content-Type"));

	}

	@Test
	void testSetupCallThirdPartyDto() {
		thirdPartyUrlConfig.getNgTableData().put(CacheConstants.CacheFields.TRANSPORT_METHOD.name(), "POST");
		callThirdPartyDTO.setThirdPartyId(thirdPartyUrlConfig.getNgTableData().get("THIRD_PARTY_ID"));
		callThirdPartyDTO.setUrl(thirdPartyUrlConfig.getNgTableData().get(CacheConstants.CacheFields.URL.name()));
		callThirdPartyDTO.setReadTimeout(NumberUtils
				.toInt(thirdPartyUrlConfig.getNgTableData().get(CacheConstants.CacheFields.READ_TIMEOUT.name()), 3000));
		callThirdPartyDTO.setConnTimeout(NumberUtils.toInt(
				thirdPartyUrlConfig.getNgTableData().get(CacheConstants.CacheFields.CONNECTION_TIMEOUT.name()), 3000));
		callThirdPartyDTO.setRequest("request");
		callThirdPartyDTO
				.setHeaders(thirdPartyUrlConfig.getNgTableData().get(CacheConstants.CacheFields.HEADERS.name()));
		callThirdPartyDTO.setTransportMethod(CallThirdPartyDTO.TransportMethod
				.valueOf("POST"));
		batchCallBackService.setupCallThirdPartyDto(thirdPartyUrlConfig, "request");
	}

}
