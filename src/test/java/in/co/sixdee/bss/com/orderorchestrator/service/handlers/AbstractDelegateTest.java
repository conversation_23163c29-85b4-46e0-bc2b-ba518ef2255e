/*
package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import static org.mockito.Mockito.mock;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.config.EdrConfig;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.WorkFlowUtil;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants.WorkFlowProcessVariables;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.jolt.JoltUtils;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext.ErrorDetail;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.Service;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
class AbstractDelegateTest {

	@Mock
	GetDataFromCache				cache;

	@Mock
	WorkFlowUtil					workFlowUtil;

	@Mock
	EdrConfig						edrConfig;

	@Mock
	protected JoltUtils joltUtils;

	static AbstractDelegate			abstractDelegate;

	static DelegateExecution		execution;
	static Map<String, Object>		variables;
	static OrderFlowContext			orderFlowContext;
	static Service					service;
	static CacheTableDataDTO		thirdPartyUrlConfig;
	static HashMap<String, String>	thirdPartyCallDetails;
	static Order					order;
	static Map<String, String>		headerMap;
	static HashMap<String,Object> workflowData;

	@BeforeAll
	static void setUpBeforeClass() throws Exception {
		abstractDelegate = mock(AbstractDelegate.class, Mockito.CALLS_REAL_METHODS);
		execution = mock(DelegateExecution.class);
		headerMap = new HashMap<>();
	}

	@AfterAll
	static void tearDownAfterClass() throws Exception {
		service = null;
		orderFlowContext = null;
		variables = null;
		thirdPartyUrlConfig = null;
		thirdPartyCallDetails = null;
		order = null;
		workflowData = null;
	}

	@BeforeEach
	void setUp() throws Exception {
		variables = new LinkedHashMap<String, Object>();
		workflowData = new LinkedHashMap<>();
		service = new Service();
		thirdPartyUrlConfig = new CacheTableDataDTO();
		orderFlowContext = new OrderFlowContext();
		thirdPartyCallDetails = new HashMap<>();
		orderFlowContext.setAttributes(new LinkedHashMap<>());
		orderFlowContext.setWorkflowData(new HashMap<>());
		variables.put("workflowData", orderFlowContext);
		variables.put("thirdPartyId", "BILLING");
		order = new Order();
		variables.put("Status", "0");
		execution.setVariables(variables);
	}

	@AfterEach
	void tearDown() throws Exception {}

	//@Test
	void testExecute() throws Exception {
		order.setOrderType("Submit");
		service.setSubOrderId("2345678");
		orderFlowContext.setOrder(order);
		// variables.put(WorkFlowConstants.WorkFlowProcessVariables.EXECUTION_DATA.getStringValue(),
		// service);
		Mockito.doReturn(variables).when(execution).getVariables();
		Mockito.doReturn(orderFlowContext).when(execution).getVariable("workflowData");
		Mockito.doReturn(service).when(execution).getVariable("executionData");
		thirdPartyCallDetails.put("ORDER_TYPE", "Onboarding");
		thirdPartyUrlConfig.setNgTableData(thirdPartyCallDetails);
		*/
/*
		 * Mockito.doReturn(thirdPartyUrlConfig).when(cache)
		 * .getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(),
		 * "BILLING");
		 *//*

		Mockito.doReturn(thirdPartyUrlConfig).when(cache).getCacheDetailsFromDBMap(
				CacheConstants.CacheKeys.STAGE_CONFIG_BY_ACTIVITY_ID_AND_ORDER_TYPE.name(), "Submit" + "_" + null);
		*/
/*
		 * Mockito.doReturn(true).when(execution)
		 * .hasVariable(WorkFlowConstants.WorkFlowProcessVariables.EDR_TYPE.getStringValue());
		 * Mockito.doReturn(WorkFlowConstants.WorkFlowProcessVariables.EDR_FINAL.getStringValue()).
		 * when(execution)
		 * .getVariable(WorkFlowConstants.WorkFlowProcessVariables.EDR_TYPE.getStringValue());
		 *//*

		abstractDelegate.workFlowUtil = workFlowUtil;
		abstractDelegate.executionContext = orderFlowContext;
		abstractDelegate.cache = cache;
		// abstractDelegate.edrConfig = edrConfig;
		abstractDelegate.execute(execution);
	}

	//@Test
	void testExecuteException() throws Exception {
		order.setOrderType("Submit");
		service.setSubOrderId("2345678");
		orderFlowContext.setOrder(null);
		Mockito.doReturn(variables).when(execution).getVariables();
		Mockito.doReturn(orderFlowContext).when(execution).getVariable("workflowData");
		abstractDelegate.executionContext = orderFlowContext;

		abstractDelegate.execute(execution);
	}

	@Test
	void testExecuteDelegateExecution() {

	}

	@Test
	void testInitHandler() {

	}

	//@Test
	void testClose() {
		abstractDelegate.close();

	}

	@Test
	void testextractSubscriptionIds() {
		HashMap<String, String> subscriptionIdMap = new HashMap<>();
		subscriptionIdMap.put("", "");
		orderFlowContext.getWorkflowData().put("subscriptionIdMap", subscriptionIdMap);
		abstractDelegate.executionContext = orderFlowContext;
		abstractDelegate.activityId = GenericConstants.NCC_CREATE_SERVICE_HANDLER;
		String response = "{\"serviceCharacteristic\":[{\"name\":\"qwertyui\",\"value\":\"1234567898\",\"@type\":\"SubscriptionID\"}]}";
		abstractDelegate.objectMapper = new ObjectMapper();
		abstractDelegate.extractSubscriptionIds(response);
	}

	@Test
	void testextractSubscriptionIdsNullValueException() {

		orderFlowContext.getWorkflowData().put("subscriptionIdMap", null);
		abstractDelegate.executionContext = orderFlowContext;
		abstractDelegate.activityId = GenericConstants.NCC_CREATE_SERVICE_HANDLER;
		String response = "{\"serviceCharacteristic\":[{\"name\":\"qwertyui\",\"value\":\"1234567898\",\"type\":\"SubscriptionID\"}]}";
		abstractDelegate.objectMapper = new ObjectMapper();
		abstractDelegate.extractSubscriptionIds(response);
	}

	@Test
	void testextractSubscriptionIdsSM() {
		HashMap<String, String> subscriptionIdMap = new HashMap<>();
		subscriptionIdMap.put("", "");
		orderFlowContext.getWorkflowData().put("subscriptionIdMap", subscriptionIdMap);
		abstractDelegate.executionContext = orderFlowContext;
		abstractDelegate.activityId = GenericConstants.SMCreateSubscription;
		String response = "{\"resultParam\":{\"subscriptionId\":\"2345678\",\"planId\":\"45678\"}}";
		abstractDelegate.objectMapper = new ObjectMapper();
		abstractDelegate.extractSubscriptionIds(response);
	}

	@Test
	void testextractSubscriptionIdsSMException() {

		orderFlowContext.getWorkflowData().put("subscriptionIdMap", null);
		abstractDelegate.executionContext = orderFlowContext;
		abstractDelegate.activityId = GenericConstants.SMCreateSubscription;
		String response = "{\"SMresultParam\":{\"subscriptionId\":\"2345678\",\"planId\":\"45678\"}}";
		abstractDelegate.objectMapper = new ObjectMapper();
		abstractDelegate.extractSubscriptionIds(response);
	}

	//@Test
	void testprocessThirdPartyResponse() {

		String response = "{\"SMresultParam\":{\"subscriptionId\":\"2345678\",\"planId\":\"45678\"}}";
		String path = "path=$.SMresultParam.planId";
		variables.put(WorkFlowProcessVariables.RESPONSE_ATTRIBUTES.toString(), path);
		abstractDelegate.executionContext = orderFlowContext;
		abstractDelegate.processThirdPartyResponse();
	}

	//@Test
	void testprocessThirdPartyResponsePathNotFoundException() {

		String response = "{\"SMresultParam\":{\"subscriptionId\":\"2345678\",\"planId\":\"45678\"}}";
		String path = "path=$.resultParam.planId";
		variables.put(WorkFlowProcessVariables.RESPONSE_ATTRIBUTES.toString(), path);
		abstractDelegate.executionContext = orderFlowContext;
		abstractDelegate.processThirdPartyResponse();
	}

	//@Test
	void testprocessThirdPartyResponseSingleValue() {

		String response = "{\"SMresultParam\":{\"subscriptionId\":\"2345678\",\"planId\":\"45678\"}}";
		String path = "$.resultParam.planId";
		variables.put(WorkFlowProcessVariables.RESPONSE_ATTRIBUTES.toString(), path);
		abstractDelegate.executionContext = orderFlowContext;
		abstractDelegate.processThirdPartyResponse();
	}

	//@Test
	void testprocessThirdPartyResponseException() {

		String response = "{\"SMresultParam\":{\"subscriptionId\":\"2345678\",\"planId\":\"45678\"}}";
		String path = "path=$.SMresultParam.planId";
		variables.put(WorkFlowProcessVariables.RESPONSE_ATTRIBUTES.toString(), path);
		abstractDelegate.executionContext = null;
		abstractDelegate.processThirdPartyResponse();
	}

	@Test
	void testtransformHeaders() {
		headerMap.put("Content-Type", "application/json");
		headerMap.put("request-id", "{workitemRequestId}");
		headerMap.put(",channel-id", "@($.errorDetail.code)");
		headerMap.put("sourcenode", "@($.CHANNEL_REQUEST.Header.source_node)");
		headerMap.put("requestTimestamp", "date(epoch)");
		headerMap.put("timestamp", "date(YYYY-MM-dd hh:mm:ss)");
		ErrorDetail errorDetail = new ErrorDetail("200", "Success", "");

		orderFlowContext.setErrorDetail(errorDetail);

		abstractDelegate.executionContext = orderFlowContext;
		Map<String, String> transformHeaders = abstractDelegate.transformHeaders(headerMap);
		Assertions.assertEquals(headerMap.get("Content-Type"), transformHeaders.get("Content-Type"));

	}
	//@Test
	void testtransformHeadersException() {
		headerMap.put("Content-Type", "application/json");
		headerMap.put("request-id", "{workitemRequestId}");
		headerMap.put(",channel-id", "@($.errorDetail)");
		headerMap.put("sourcenode", "@($.CHANNEL_REQUEST.Header.source_node)");
		abstractDelegate.executionContext = orderFlowContext;
		Map<String, String> transformHeaders = abstractDelegate.transformHeaders(headerMap);
		Assertions.assertNull(transformHeaders.get("Content-Type"));

	}

	@Test
	void testvalidatePayload() {
		String response = "{\"responseCode\":200,\"responseMessage\":\"Success\"}";
		thirdPartyCallDetails.put("RESP_STATUS_PATH", "$.responseCode");
		thirdPartyCallDetails.put("RESP_STATUS_DESC_PATH", "$.responseMessage");
		thirdPartyCallDetails.put("EXPECTED_RESP_CODE", "400");
		abstractDelegate.thirdPartyCallDetails = thirdPartyCallDetails;
		//abstractDelegate.validatePayload(response);
	}
	@Test
	void testvalidatePayloadException() {

		abstractDelegate.thirdPartyCallDetails = thirdPartyCallDetails;
		//abstractDelegate.validatePayload(null);
	}

	@Test
	void testresolveUriTokens() {
		workflowData.put("subscriptionId", "234");
		orderFlowContext.getAttributes().put("service_id", "456789");
		orderFlowContext.setWorkflowData(workflowData);
		abstractDelegate.executionContext = orderFlowContext;
		abstractDelegate.resolveUriTokens("subscriptionId=$.subscriptionId|service_id=val(service_id)|planId=3456");
	}
	@Test
	void testresolveUriTokensNull() {
		workflowData.put("subscriptionId", "234");
		orderFlowContext.getAttributes().put("service_id", "456789");
		orderFlowContext.setWorkflowData(workflowData);
		abstractDelegate.executionContext = orderFlowContext;
		abstractDelegate.resolveUriTokens(null);
	}

	@Test
	void testmodifyWorkflowData() throws Exception {
		abstractDelegate.executionContext = orderFlowContext;
		variables.put("respSpecKey", "all");
		abstractDelegate.executionVariables = variables;
		abstractDelegate.isPartOfMultipleInstance = true;
		abstractDelegate.joltUtils =joltUtils;

		LinkedHashMap<String, Object> executionDataMap = new LinkedHashMap<>();
		executionDataMap.put("planId", "4567");
		workflowData.put(WorkFlowProcessVariables.CURRENT_EXECUTION.toString(), executionDataMap);
		orderFlowContext.setWorkflowData(workflowData);
		//		abstractDelegate.modifyWorkflowData("{\"SMresultParam\":{\"subscriptionId\":\"2345678\",\"planId\":\"45678\"}}",  "{\"responseCode\":200,\"responseMessage\":\"Success\"}");
	}
	@Test
	void testmodifyWorkflowDataAllTags() throws Exception {
		abstractDelegate.executionContext = orderFlowContext;
		variables.put("respSpecKey", "all");
		abstractDelegate.executionVariables = variables;
		abstractDelegate.joltUtils = joltUtils;
		LinkedHashMap<String, Object> executionDataMap = new LinkedHashMap<>();
		executionDataMap.put("planId", "4567");
		abstractDelegate.isPartOfMultipleInstance = false;
		workflowData.put(WorkFlowProcessVariables.CURRENT_EXECUTION.toString(), executionDataMap);
		orderFlowContext.setWorkflowData(workflowData);
		//		abstractDelegate.modifyWorkflowData("{\"SMresultParam\":{\"subscriptionId\":\"2345678\",\"planId\":\"45678\"}}",  "{\"responseCode\":200,\"responseMessage\":\"Success\"}");
	}
	@Test
	void testmodifyWorkflowDataOnlyOneTag() throws Exception {
		abstractDelegate.executionContext = orderFlowContext;
		variables.put("respSpecKey", "wedfghj");
		abstractDelegate.executionVariables = variables;
		abstractDelegate.joltUtils =joltUtils;

		LinkedHashMap<String, Object> executionDataMap = new LinkedHashMap<>();
		executionDataMap.put("planId", "4567");
		workflowData.put(WorkFlowProcessVariables.CURRENT_EXECUTION.toString(), executionDataMap);
		orderFlowContext.setWorkflowData(workflowData);
		//		abstractDelegate.modifyWorkflowData("{\"SMresultParam\":{\"subscriptionId\":\"2345678\",\"planId\":\"45678\"}}",  "{\"responseCode\":200,\"responseMessage\":\"Success\"}");
	}
	@Test
	void testmodifyWorkflowDataEmptyResponse() throws Exception {
		abstractDelegate.executionContext = orderFlowContext;
		variables.put("respSpecKey", "wedfghj");
		abstractDelegate.executionVariables = variables;
		//		abstractDelegate.modifyWorkflowData("",  "");
	}
	@Test
	void testmodifyWorkflowDataEmptyResSpec() throws Exception {
		abstractDelegate.executionContext = orderFlowContext;
		variables.put("respSpecKey", null);
		abstractDelegate.executionVariables = variables;
		//		abstractDelegate.modifyWorkflowData("{\"SMresultParam\":{\"subscriptionId\":\"2345678\",\"planId\":\"45678\"}}",  "{\"responseCode\":200,\"responseMessage\":\"Success\"}");
	}

	@Test
	void testresolveValExpression() {
		abstractDelegate.resolveValExpression(null, null, null);
	}

	@Test
	void testextractErrorDesc() {
		String response = "{\"responseCode\":\"SCOOO\",\"responseMessage\":\"Success\"}";
		thirdPartyCallDetails.put("RESP_STATUS_PATH", "$.responseCode");
		thirdPartyCallDetails.put("RESP_STATUS_DESC_PATH", "$.responseMessage");
		thirdPartyCallDetails.put("EXPECTED_RESP_CODE", "400");
		abstractDelegate.thirdPartyCallDetails = thirdPartyCallDetails;
		orderFlowContext.setErrorDetail(new ErrorDetail());
		abstractDelegate.executionContext = orderFlowContext;
		//abstractDelegate.extractErrorDesc(response);
	}
}

*/
