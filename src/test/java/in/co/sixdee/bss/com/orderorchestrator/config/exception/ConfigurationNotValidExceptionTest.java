/**
 * 
 *//*

package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;

*/
/**
 * <AUTHOR>
 *
 *//*

@RunWith(JUnitPlatform.class)
@ExtendWith(MockitoExtension.class)
class ConfigurationNotValidExceptionTest {

	private int								errorCode;
	private String							errorDetail;
	private String							errorSystem;

	@InjectMocks
	private ConfigurationNotValidException	configurationNotValidException;

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#hashCode()}.
	 *//*

	@Test
	void testHashCode() {
		configurationNotValidException.hashCode();
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#ConfigurationNotValidException()}.
	 *//*

	@Test
	void testConfigurationNotValidException() {

	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#ConfigurationNotValidException(java.lang.String)}.
	 *//*

	@Test
	void testConfigurationNotValidExceptionString() {
		configurationNotValidException = new ConfigurationNotValidException("configuration not found");
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#ConfigurationNotValidException(int, java.lang.String)}.
	 *//*

	@Test
	void testConfigurationNotValidExceptionIntString() {
		configurationNotValidException = new ConfigurationNotValidException(
				StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION.getCode(), "configuration not found");
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#ConfigurationNotValidException(java.lang.Throwable)}.
	 *//*

	@Test
	void testConfigurationNotValidExceptionThrowable() {
		configurationNotValidException = new ConfigurationNotValidException(
				StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION.getCode(), "configuration not found");
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#ConfigurationNotValidException(int, java.lang.Throwable)}.
	 *//*

	@Test
	void testConfigurationNotValidExceptionIntThrowable() {
		configurationNotValidException = new ConfigurationNotValidException(
				StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION.getCode(), new Throwable());
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#ConfigurationNotValidException(java.lang.String, java.lang.Throwable)}.
	 *//*

	@Test
	void testConfigurationNotValidExceptionStringThrowable() {
		configurationNotValidException = new ConfigurationNotValidException("configuration not found", new Throwable());
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#ConfigurationNotValidException(int, java.lang.String, java.lang.Throwable)}.
	 *//*

	@Test
	void testConfigurationNotValidExceptionIntStringThrowable() {
		configurationNotValidException = new ConfigurationNotValidException(
				StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION.getCode(), "configuration not found", new Throwable());
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#ConfigurationNotValidException(int, java.lang.String, java.lang.String, java.lang.String)}.
	 *//*

	@Test
	void testConfigurationNotValidExceptionIntStringStringString() {
		configurationNotValidException = new ConfigurationNotValidException(
				StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION.getCode(), "configuration not found", "failure",
				StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION.getDesc());
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#getErrorCode()}.
	 *//*

	@Test
	void testGetErrorCode() {
		configurationNotValidException.getErrorCode();
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#getErrorDetail()}.
	 *//*

	@Test
	void testGetErrorDetail() {
		configurationNotValidException.getErrorDetail();
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#getErrorSystem()}.
	 *//*

	@Test
	void testGetErrorSystem() {
		configurationNotValidException.getErrorSystem();
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#setErrorCode(int)}.
	 *//*

	@Test
	void testSetErrorCode() {
		configurationNotValidException.setErrorCode(StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION.getCode());
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#setErrorDetail(String)}.
	 *//*

	@Test
	void testSetErrorDetail() {
		configurationNotValidException.setErrorDetail(StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION.getDesc());
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#setErrorSystem(String)}.
	 *//*

	@Test
	void testSetErrorSystem() {
		configurationNotValidException.setErrorSystem("CRM");
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#equals(java.lang.Object)}.
	 *//*

	@Test
	void testEqualsObject() {
		configurationNotValidException.equals(configurationNotValidException);
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#canEqual(java.lang.Object)}.
	 *//*

	@Test
	void testCanEqual() {
		configurationNotValidException.canEqual(configurationNotValidException);
	}

	*/
/**
	 * Test method for
	 * {@link in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException#toString()}.
	 *//*

	@Test
	void testToString() {
		configurationNotValidException.toString();
	}

}
*/
