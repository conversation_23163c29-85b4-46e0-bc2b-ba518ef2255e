/*
package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import java.util.List;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.om.model.dto.WorkFlowErrorBean;

@RunWith(JUnitPlatform.class)
@ExtendWith(MockitoExtension.class)
class CommonExceptionResponseTest {

	private String					transactionId;
	private String					timestamp;
	private Integer					code;
	private String					status;
	private String					message;

	private List<WorkFlowErrorBean>	errors;

	@InjectMocks
	private CommonExceptionResponse	commonExceptionResponse;

	@BeforeAll
	static void setUpBeforeClass() throws Exception {}

	@AfterAll
	static void tearDownAfterClass() throws Exception {}

	@BeforeEach
	void setUp() throws Exception {}

	@AfterEach
	void tearDown() throws Exception {}

	@Test
	void testCommonExceptionResponse() {
		commonExceptionResponse = new CommonExceptionResponse();

	}

	@Test
	void testCommonExceptionResponseCodeMessage() {
		commonExceptionResponse = new CommonExceptionResponse(code, message);

	}

	@Test
	void testCommonExceptionResponseTransactionId() {
		commonExceptionResponse = new CommonExceptionResponse(transactionId, code, message, status, errors);
	}

	@Test
	void testCommonExceptionstatus() {
		commonExceptionResponse = new CommonExceptionResponse(code, message, transactionId);

	}
	
	@Test
	void testCommonExceptionResponseCode() {
		commonExceptionResponse = new CommonExceptionResponse(code, message, transactionId);

	}


	@Test
	void testCommonExceptionResponseMessage() {
		commonExceptionResponse = new CommonExceptionResponse(message);

	}

	@Test
	void testGetTransactionId() {
		commonExceptionResponse.getRequestId();

	}

	@Test
	void testGetTimestamp() {
		commonExceptionResponse.getTimestamp();

	}

	@Test
	void testGetCode() {
		commonExceptionResponse.getCode();

	}

	@Test
	void testGetStatus() {
		commonExceptionResponse.getStatus();

	}

	@Test
	void testGetMessage() {
		commonExceptionResponse.getMessage();

	}

	@Test
	void testGetErrors() {
		commonExceptionResponse.getErrors();

	}

	@Test
	void testSetTransactionId() {
		commonExceptionResponse.setRequestId(transactionId);

	}

	@Test
	void testSetTimestamp() {
		commonExceptionResponse.setTimestamp(timestamp);

	}

	@Test
	void testSetCode() {
		commonExceptionResponse.setCode(code);

	}

	@Test
	void testSetStatus() {
		commonExceptionResponse.setStatus(status);

	}

	@Test
	void testSetMessage() {
		commonExceptionResponse.setMessage(message);

	}

	@Test
	void testSetErrors() {
		commonExceptionResponse.setErrors(errors);

	}

	@Test
	void testEqualsObject() {
		commonExceptionResponse.equals(code);
	}

	@Test
	void testCanEqual() {
		commonExceptionResponse.canEqual(code);

	}

	@Test
	void testToString() {
		commonExceptionResponse.toString();

	}

	@Test
	void testHashCode() {
		commonExceptionResponse.hashCode();

	}

	*/
/*
	 * @Test public void equalsContract() { EqualsVerifier.forClass(CommonExceptionResponse.class )
	 * .suppress(Warning.STRICT_INHERITANCE ) .verify(); }
	 *//*


}
*/
