/*
package in.co.sixdee.bss.com.orderorchestrator.service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.runner.JUnitPlatform;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import in.co.sixdee.bss.com.orderorchestrator.config.EdrConfig;
import in.co.sixdee.bss.om.model.dto.OrderDTO;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.SubOrderDTO;
import in.co.sixdee.bss.om.model.dto.SubOrderStageDTO;
import in.co.sixdee.bss.om.model.dto.WorkItemAuditDTO;

@ExtendWith(MockitoExtension.class)
@RunWith(JUnitPlatform.class)
public class OrderStatusManagerTest {

	@Mock
	private EdrConfig				edrConfig;

	@InjectMocks
	private OrderStatusManager		orderStatusManager;

	static OrderFlowContext			orderFlowContext;
	static SubOrderStageDTO			subOrderStagesDTO;
	static List<SubOrderStageDTO>	subOrderStages;
	static WorkItemAuditDTO			workItemAuditDTO;
	static List<WorkItemAuditDTO>	workItemAudits;
	static SubOrderDTO				subOrderDTO;
	static List<SubOrderDTO>		subOrders;
	static OrderDTO					masterOrder;

	*/
/**
	 * @throws java.lang.Exception
	 *//*


	@BeforeAll
	static void setUpBeforeClass() throws Exception {
		subOrderStagesDTO = new SubOrderStageDTO();
		workItemAuditDTO = new WorkItemAuditDTO();

		masterOrder = new OrderDTO();
		subOrderStages = new ArrayList<>();
		workItemAudits = new ArrayList<>();
		subOrders = new ArrayList<>();
	}

	*/
/**
	 * @throws java.lang.Exception
	 *//*


	@AfterAll
	static void tearDownAfterClass() throws Exception {}

	*/
/**
	 * @throws java.lang.Exception
	 *//*


	@BeforeEach
	void setUp() throws Exception {
		orderFlowContext = new OrderFlowContext();
		orderFlowContext.setAttributes(new LinkedHashMap<>());
		subOrderDTO = new SubOrderDTO();
		orderFlowContext.setWorkItemAudits(new ArrayList<>());
	}

	*/
/**
	 * @throws java.lang.Exception
	 *//*


	@AfterEach
	void tearDown() throws Exception {}

	*/
/**
	 * Test method for
	 * {@link com.sixdee.om.services.OrderStatusChecker#processRequest(com.sixdee.om.dto.orderFlowContext, java.lang.String, java.lang.String, boolean, java.lang.String)}.
	 *//*


	
}
*/
