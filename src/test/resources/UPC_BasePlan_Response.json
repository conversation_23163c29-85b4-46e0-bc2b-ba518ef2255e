{"id": "1", "name": "GSM Base PO 1", "sellable": true, "dynamicBundle": false, "description": "GSM Base PO 1", "invoiceDescription": "GSM Base PO 1", "marketingDescription": "GSM Base PO 1", "versionNo": "4", "versionStatus": "ACTIVE", "createdBy": "<EMAIL>", "sortingPreference": "1", "isLocked": "false", "poStatus": {"id": "0", "name": "Active"}, "validFor": {"startDateTime": "10/01/2021"}, "productOfferGroup": {"id": "1", "name": "GSM Prepaid", "description": "GSM Prepaid", "isBundleOffer": "false", "status": {"id": "0", "name": "Active"}, "productFamily": {"id": "0", "name": "GSM", "href": "http://*********:8191/UPC/ProductFamily/0", "versionNo": "1", "versionStatus": "ACTIVE", "validFor": {"startDateTime": "09/16/2021"}, "createdBy": "SMAdmin", "isLocked": "false"}, "versionNo": "1", "versionStatus": "ACTIVE", "validFor": {"startDateTime": "09/16/2021"}, "isLocked": "false", "partner": "335", "serviceProvider": "334"}, "productOfferingPrice": [{"id": "4", "name": "GSM Prepaid Base Price", "price": "0.00", "factor": "1", "isApplicable": "false", "isStackable": "false", "dynamicPrice": false, "applyToExistingCustomer": false, "priceOverride": false, "tieredDiscount": false, "priceMode": {"id": "2", "name": "Recurring"}, "priceCategory": {"id": "0", "name": "Base Price", "href": "http://*********:8191/UPC/PriceCategory/0", "billingChargeType": {"id": "10", "name": "Others"}, "mandatory": false, "taxable": false, "billingType": {"id": "1", "name": "Service Level"}, "validFor": {"startDateTime": "09/03/2021"}, "billSection": "", "isRecoverableUpfrontCost": "false", "contractualObligation": "false", "isNonAccountingItem": "false", "trigger": {}}, "priceType": {"id": "0", "name": "Fixed"}, "priceCode": {"id": "1", "name": "WAKANDA", "code": "100", "description": "WAKANDA", "synonym": {"id": "1", "name": "ACT"}, "status": {"id": "0", "name": "Active"}, "taxable": "0", "reversal": "0", "normalizetax": "0", "validFor": {"startDateTime": "09/16/2021"}, "versionStatus": "ACTIVE", "isLocked": "false", "createdBy": "SMAdmin", "versionNo": "1"}, "popType": {"id": "0", "name": "Normal"}, "prorataEnable": false, "prorataEnabledInSuspension": false, "prorataEnabledInTermination": false, "prorataEnabledInPlanChange": false, "prorataEnabledInActivation": false, "tierbased": false, "frequency": {"id": "2", "name": "Monthly"}, "rules": [], "discountAvailabilities": [], "isApplicableForAlasCarte": "false", "minMRC": "", "maxMRC": "", "discountEvolution": {}, "manualWaiver": "false", "versionNo": "3", "versionStatus": "ACTIVE", "validFor": {"startDateTime": "10/01/2021"}, "createdBy": "0", "splitDetails": [], "cartTierDetails": [], "currentDate": false, "isLocked": "false", "isAutomatic": "false", "defineSpecificInclusionExclusion": [], "partner": "335", "applyFallBack": "false", "serviceProviderID": "334", "graceValidity": "5", "suspendValidity": "5", "fixedPayment": "false", "activationParking": "false", "billed": false}], "productSpecifications": [{"id": "4", "name": "GSM PS BASE 1", "type": {"id": "1", "name": "Base"}, "serviceType": {"id": "0", "name": "All"}, "productFamily": {"id": "0"}, "status": {"id": "0", "name": "Active"}, "cfss": [{"id": "24", "name": "CFSS_GSM_VOICE", "serviceType": "1", "serviceClass": "2", "transportType": "5", "serviceCategory": "4", "validFor": {"startDateTime": "09/05/2021"}, "vesionNo": "1", "characteristics": []}], "transportType": {"id": "5", "name": "GSM"}, "releationships": [], "versionNo": "2", "versionStatus": "ACTIVE", "createdBy": "<EMAIL>", "isLocked": "false", "validFor": {"startDateTime": "10/08/2021"}, "peerRelationShip": [], "ratingParameters": [], "partner": "335", "serviceProvider": "334"}], "contracts": [], "poBundles": [], "poDynamicGroups": [], "dynamicOfferAvailabilities": [], "eligibilityOffers": [{"name": "Prepaid Free Bytes", "required": "false", "minQuantity": "100", "maxQuantity": "100", "eligibilityOfferId": "6", "versionNo": "4", "versionStatus": "ACTIVE", "poType": {"id": "2", "name": "ADDON"}, "eligibilityType": {"id": "3", "name": "AllowedUpgrades"}, "productOfferings": [{"name": "GSM Hybrid 299", "sellable": false, "dynamicBundle": false, "poId": "6", "minQuantity": "0", "maxQuantity": "1", "versionNo": "4", "versionStatus": "ACTIVE"}], "subGroupPo": []}, {"name": "Prepaid Free Bytes 250", "required": "false", "minQuantity": "250", "maxQuantity": "250", "eligibilityOfferId": "7", "versionNo": "4", "versionStatus": "ACTIVE", "poType": {"id": "2", "name": "ADDON"}, "eligibilityType": {"id": "5", "name": "OptionalOffer"}, "productOfferings": [{"name": "Addon 1 249", "sellable": false, "dynamicBundle": false, "poId": "7", "minQuantity": "0", "maxQuantity": "1", "versionNo": "4", "versionStatus": "ACTIVE"}], "subGroupPo": []}, {"name": "Prepaid Free Mins", "required": "false", "minQuantity": "120", "maxQuantity": "120", "eligibilityOfferId": "8", "versionNo": "4", "versionStatus": "ACTIVE", "poType": {"id": "2", "name": "ADDON"}, "eligibilityType": {"id": "1", "name": "OptionalOffer"}, "productOfferings": [{"name": "Postpaid 249", "sellable": false, "dynamicBundle": false, "poId": "8", "minQuantity": "0", "maxQuantity": "1", "versionNo": "4", "versionStatus": "ACTIVE"}], "subGroupPo": []}], "bucketDetails": [], "partner": "335", "serviceProvider": "334"}