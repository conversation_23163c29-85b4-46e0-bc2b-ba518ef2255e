{"BillingSystem": {"request_id": "**************", "request_timestamp": "**************", "response_timestamp": "**************", "action": "FetchAccountDetail", "source": "CRM", "username": "SuperAdmin", "userid": "2", "entity_id": "200", "result_code": "0", "result_desc": "Success", "upfront_payment": "NA", "response": {"dataset": {"totalrecordcnt": 1, "recordcnt": 1, "account_info": [{"account_id": "**********", "account_connection_type": "1", "profile_id": "**********", "billing_title": "", "billing_name": "<PERSON><PERSON>", "billing_name_unicode": "", "last_name": "Da", "language_id": "1", "bill_currency": "0", "preferred_currency": "0", "billing_region": "0", "tax_code": "0", "dispatch_mode": "1", "dispatch_fax_number": "", "dunning_schedule": "14", "dunning_schedule_value": "", "risk_category": "1", "risk_category_value": "Low", "email_notification": "1", "notification_email_id": "<EMAIL>", "sms_notification": "1", "sms_numbers": "**********", "create_user": "1", "modify_user": "0", "create_date": "**************", "modify_date": "**************", "entity_id": "200", "account_type": "", "itemized_required": "0", "tax_plan_group": "", "tax_plan_group_value": "", "comment": "", "bill_id": "", "bill_cycle_id": "77", "bill_cycle_id_value": "32", "external_group_owner": "", "dunning_cancellation": "0", "dunning_enabled": "0", "addresses": [{"address_id": "********", "subscriber": "**********", "subscriber_level": "1", "address_type": "2", "addr_line1": "6d Technologies", "addr_line2": "Bannerghatta Main Rd", "addr_line3": "Bengaluru", "addr_line4": "Karnataka", "addr_line5": " 560076", "addr_line6": "", "addr_line7": "", "addr_line8": "", "language_id": "1", "company_name": "", "branch": ""}], "balance_details": {"total_credit": "0.000000", "outstanding_balance": "0.000000", "last_bill_id": "", "last_payment_amount": "0.000000", "last_invoice_amount": "0.000000", "last_payment_date": "", "last_bill_run": "", "next_bill_date": "07-02-2019 00:00:00", "from_bill_date": "", "to_bill_date": "", "bill_date": "", "bill_due_date": "", "last_invoice_total_amount": "0.000000"}, "migrated_account_no": "", "summary_dispatch": "3", "itemized_dispatch": "1", "itemized_preferences": {}, "auto_pay": "", "card_number": "", "bundle_id": "", "bundle_name": "", "eai_project_key": "", "sbb_based": "N", "profile_name": "<PERSON><PERSON>", "bill_cycle_day": "29", "activation_date": "**************", "first_name_ol": "", "middle_name_ol": "", "last_name_ol": "", "credit_class": "1", "subscriber_category": "", "subscriber_sub_category": "", "profile_type": "0", "template_id": "", "grp_account_id": "", "status": 1, "credit_card_details": "", "fraud_lock": "", "ivr_notification": "0", "ivr_number": "", "bank_account_details": "", "tax_exemption": "0", "is_care_coin_enrollment": "1", "tax_exemption_details": [], "owner": "", "pin": "", "care_coin_enrollment_date": "", "credit_class_id": "1", "credit_class_name": "Priority Accounts", "pre_terminated_date": "", "autopay_blocked_status": "1", "autopay_status_change_reason": "", "payment_term": "", "frn_no": "", "spin_no": "", "form471_app_no": "", "marketing_message_opted": "", "gl_posting_required": "Y", "contacts": [], "is_shared_account": "0", "parent_seq_id": ""}]}}}}