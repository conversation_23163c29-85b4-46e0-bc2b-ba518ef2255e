{"orderType": "Onboarding", "description": "Product Order illustration sample", "profile1": {"account1": {"name": "test account", "billcycleId": 1, "billingOnHold": true, "chargingPattern": 0, "dispatchMode": 0, "dunningExclusion": false, "entityId": 200, "itemizedbillRequired": true, "languageId": 0, "notificationAllowed": "000", "remarks": "string", "riskcategoryId": 19, "status": 0, "taxCode": 0, "taxExemptionCode": 0, "title": "Mr.", "unicodeName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contactMedium": [{"characteristic": {"contactType": "smsNumber", "phoneNumber": "***********"}, "mediumType": "telephoneNumber"}, {"characteristic": {"contactType": "faxNumber", "faxNumber": "********"}, "mediumType": "telephoneNumber"}, {"characteristic": {"contactType": "emailAddress", "emailAddress": "<EMAIL>"}, "mediumType": "emailAddress"}, {"characteristic": {"contactType": "residentialAddress", "city": "Bangalore", "country": "India", "postCode": "560036", "stateOrProvince": "karnataka", "street1": "btm layout", "street2": "jp nagar"}, "mediumType": "address"}], "serviceGroups": [{"services": [{"serviceId": "1234512355", "name": "GSM Service", "status": "0", "serviceType": "GSM", "connectionType": "prepaid", "languageId": "1", "contactMedium": [{"preferred": false, "mediumType": "address", "characteristic": {"city": "Bangalore", "country": "India", "postCode": "560036", "stateOrProvince": "karnataka", "street1": "btm layout", "street2": "jp nagar", "contactType": "postal"}}], "subscriptions": [{"planId": "1", "planName": "TMF25", "planDesc": "GSM Base Plan", "planType": 0, "upcVersion": "1", "activationDate": "2021-09-21T08:43:21.395Z", "charges": [{"upcChargeName": "Base offer Fee", "upcChargeId": "1001", "chargeDesc": "Base offer Fee", "overridden": 0, "chargeRecurranceType": 1, "chargeCategory": 0, "chargeFrequency": 0, "quantity": 1, "rate": 10, "amount": 0, "chargeVersion": 0, "prorationFlag": "0", "prorataEnabled": false}], "cfss": [{"id": "122", "name": "CFSS_GSM_VOICE", "serviceType": "1", "serviceClass": "2", "transportType": "9", "serviceCategory": "8", "validFor": {"startDateTime": "2021-09-07T08:57:53.545Z"}, "characteristics": [{"dataType": "Boolean", "isAttribute": "0", "defaultValue": "true", "isMandatory": "1", "isPriceImpacting": "0", "isOrderEntryAttribute": "0", "startDate": "24-09-2021", "name": "Coverage", "value": "International"}]}], "lrss": [{"id": "86", "name": "LRS_MSISDN", "href": "http://*********:8190/UPC/SOMServices/86", "serviceType": "4", "serviceClass": "1", "transportType": "5", "serviceCategory": "4", "validFor": {"startDateTime": "2021-09-07T08:57:53.545Z"}, "characteristics": [{"dataType": "String", "isAttribute": "0", "isMandatory": "1", "isPriceImpacting": "0", "isOrderEntryAttribute": "1", "startDate": "2021-09-07T08:57:53.545Z", "name": "MSISDN", "value": "21321321312"}]}], "prss": [{"id": "76", "name": "PRS_SIM", "href": "http://*********:8190/UPC/SOMServices/76", "serviceType": "3", "serviceClass": "2", "transportType": "5", "serviceCategory": "4", "validFor": {"startDateTime": "2021-09-07T08:57:53.545Z"}, "characteristics": [{"dataType": "String", "isAttribute": "0", "isMandatory": "1", "isPriceImpacting": "0", "isOrderEntryAttribute": "1", "startDate": "2021-09-07T08:57:53.545Z", "name": "IMSI", "value": "*****************"}, {"dataType": "String", "isAttribute": "0", "isMandatory": "1", "isPriceImpacting": "0", "isOrderEntryAttribute": "1", "startDate": "2021-09-07T08:57:53.545Z", "name": "ICCID", "value": "***************"}]}]}], "characteristics": [{"name": "ICCID", "valueType": "string", "value": "321321"}, {"name": "IMSI", "valueType": "string", "value": "***************"}]}]}], "accountType": 0, "contacts": [{"contactId": "1059", "contactType": "string", "status": "A"}], "billingRegion": "0", "preferredCurrency": "USD", "smsNumbers": "************", "contactEmailId": "<EMAIL>", "creditLimit": "10", "majorSicCode": 1, "sicCode": 1}}, "sourceNode": "CRM"}