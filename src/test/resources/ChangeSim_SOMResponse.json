[{"id": "edc738cbe7414645a2d24798b554b5f5", "href": "http://*********:7777/ServiceInventory/edc738cbe7414645a2d24798b554b5f5", "category": "PRS", "name": "PRS_SIM", "serviceCharacteristic": [{"name": "KI", "value": ""}, {"name": "IMSI", "value": "432989329329191"}, {"name": "ICCID", "value": "8923016932932912481"}, {"name": "TPLID", "value": ""}, {"name": "SUBSCRIPTION_ID", "value": "256300533010886"}, {"name": "BLACKLIST_REASON", "value": ""}], "serviceSpecification": {"id": "edc738cbe7414645a2d24798b554b5f5", "href": "http://*********:7777/ServiceCatalogue/ServiceSpecificationActive/5"}, "state": "active"}, {"id": "6ebf06a6972f48429ecfff515994d333", "href": "http://*********:7777/ServiceInventory/6ebf06a6972f48429ecfff515994d333", "category": "CFS", "name": "CFSS_GSM_DATA", "serviceCharacteristic": [{"name": "SUBSCRIPTION_ID", "value": "256300533010886"}], "serviceRelationship": [{"relationshipType": "has", "service": {"id": "1", "href": "http://*********:7777/ServiceInventory/ServiceCatalogue/ServiceMappingActive/1"}}], "serviceSpecification": {"id": "6ebf06a6972f48429ecfff515994d333", "href": "http://*********:7777/ServiceCatalogue/ServiceSpecificationActive/22"}, "state": "active"}, {"id": "RFS_98f67e0831234e01af304ce2f1f7935c", "href": "http://*********:7777/ServiceInventory/RFS_98f67e0831234e01af304ce2f1f7935c", "category": "RFS", "name": "RFSS_GSM_DATA", "serviceCharacteristic": [{"name": "CONNECTION_TYPE", "value": ""}, {"name": "SUBSCRIPTION_ID", "value": "256300533010886"}], "serviceRelationship": [{"relationshipType": "reliesOn", "service": {"id": "19", "href": "http://*********:7777/ServiceInventory/ServiceCatalogue/ServiceMappingActive/19"}}, {"relationshipType": "reliesOn", "service": {"id": "464", "href": "http://*********:7777/ServiceInventory/ServiceCatalogue/ServiceMappingActive/464"}}], "serviceSpecification": {"id": "RFS_98f67e0831234e01af304ce2f1f7935c", "href": "http://*********:7777/ServiceCatalogue/ServiceSpecificationActive/25"}, "state": "active"}, {"id": "bf8f8377bb8f44b7b92887c75d836458", "href": "http://*********:7777/ServiceInventory/bf8f8377bb8f44b7b92887c75d836458", "category": "CFS", "name": "CFSS_GSM_SMS", "serviceCharacteristic": [{"name": "SUBSCRIPTION_ID", "value": "256300533010886"}], "serviceRelationship": [{"relationshipType": "has", "service": {"id": "3", "href": "http://*********:7777/ServiceInventory/ServiceCatalogue/ServiceMappingActive/3"}}], "serviceSpecification": {"id": "bf8f8377bb8f44b7b92887c75d836458", "href": "http://*********:7777/ServiceCatalogue/ServiceSpecificationActive/23"}, "state": "active"}, {"id": "RFS_7e3d4156516f440fb6e220dbef03539c", "href": "http://*********:7777/ServiceInventory/RFS_7e3d4156516f440fb6e220dbef03539c", "category": "RFS", "name": "RFSS_GSM_SMS", "serviceCharacteristic": [{"name": "CONNECTION_TYPE", "value": ""}, {"name": "SUBSCRIPTION_ID", "value": "256300533010886"}], "serviceRelationship": [{"relationshipType": "reliesOn", "service": {"id": "15", "href": "http://*********:7777/ServiceInventory/ServiceCatalogue/ServiceMappingActive/15"}}, {"relationshipType": "reliesOn", "service": {"id": "17", "href": "http://*********:7777/ServiceInventory/ServiceCatalogue/ServiceMappingActive/17"}}], "serviceSpecification": {"id": "RFS_7e3d4156516f440fb6e220dbef03539c", "href": "http://*********:7777/ServiceCatalogue/ServiceSpecificationActive/26"}, "state": "active"}, {"id": "785c58b59b7d4ae69a87860f69dd7b54", "href": "http://*********:7777/ServiceInventory/785c58b59b7d4ae69a87860f69dd7b54", "category": "CFS", "name": "CFSS_GSM_VOICE", "serviceCharacteristic": [{"name": "SUBSCRIPTION_ID", "value": "256300533010886"}], "serviceRelationship": [{"relationshipType": "has", "service": {"id": "4", "href": "http://*********:7777/ServiceInventory/ServiceCatalogue/ServiceMappingActive/4"}}], "serviceSpecification": {"id": "785c58b59b7d4ae69a87860f69dd7b54", "href": "http://*********:7777/ServiceCatalogue/ServiceSpecificationActive/24"}, "state": "active"}, {"id": "RFS_20ddf0bb6fa14c2fb9ae8dc64db73abb", "href": "http://*********:7777/ServiceInventory/RFS_20ddf0bb6fa14c2fb9ae8dc64db73abb", "category": "RFS", "name": "RFSS_GSM_VOICE", "serviceCharacteristic": [{"name": "CONNECTION_TYPE", "value": ""}, {"name": "SUBSCRIPTION_ID", "value": "256300533010886"}], "serviceRelationship": [{"relationshipType": "reliesOn", "service": {"id": "16", "href": "http://*********:7777/ServiceInventory/ServiceCatalogue/ServiceMappingActive/16"}}, {"relationshipType": "reliesOn", "service": {"id": "18", "href": "http://*********:7777/ServiceInventory/ServiceCatalogue/ServiceMappingActive/18"}}], "serviceSpecification": {"id": "RFS_20ddf0bb6fa14c2fb9ae8dc64db73abb", "href": "http://*********:7777/ServiceCatalogue/ServiceSpecificationActive/27"}, "state": "active"}]