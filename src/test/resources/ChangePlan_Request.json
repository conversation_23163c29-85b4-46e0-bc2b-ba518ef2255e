{"orderType": "ChangeSubscription", "description": "Product Order illustration sample", "externalId": "PO-456", "requestedCompletionDate": "2021-10-05T07:06:02.094Z", "requestedStartDate": "2021-10-05T07:06:02.094Z", "serviceManagement": {"serviceId": "1234512324", "oldCart": {"basePlanId": "121", "compatibleAddons": [{"subscriptionId": "121323", "planId": "125"}], "inCompatibleAddons": [{"subscriptionId": "121323", "planId": "125"}]}, "newCart": {"subscriptions": [{"planId": "8", "planName": "TMF25", "planDesc": "GSM Base Plan", "upcVersion": "1", "planType": 0, "activationDate": "2021-10-05T07:06:02.094Z", "expiryDate": "2021-10-05T07:06:02.094Z", "charges": [{"upcChargeId": "1001", "upcChargeName": "Base offer Fee", "chargeDesc": "Base offer Fee", "chargeRecurranceType": 1, "chargeCategory": 0, "chargeNameOl": "string", "discountDescription": 0, "discountLevel": "string", "discountName": "string", "discountType": "string", "discountValue": 0, "nextChargeDate": "2021-10-11T14:05:25.278Z", "chargeFrequency": 0, "chargeType": "0", "chargeFactor": "0", "chargeGlCode": "0", "chargeValidity": "0", "quantity": 1, "rate": 10, "chargeStartDate": "2021-10-11T14:05:25.278Z", "chargeEndDate": "2021-10-11T14:05:25.278Z", "amount": 0, "chargeVersion": 0, "isProrata": 0, "noOfCycles": 0, "overridenCharge": 0, "overridenCycle": 0, "overridenKey": 0, "overridenType": "0", "possibleOverride": "0", "prorationFlag": "0000"}], "cfss": [{"id": "122", "name": "CFSS_VOICE", "serviceType": "1", "serviceClass": "2", "transportType": "9", "serviceCategory": "8", "characteristics": [{"dataType": "Boolean", "isAttribute": "0", "defaultValue": "true", "isMandatory": "1", "isPriceImpacting": "0", "isOrderEntryAttribute": "0", "startDate": "2021-09-07", "name": "Coverage", "value": "International"}]}], "prss": [{"id": "76", "name": "PRS_SIM", "href": "http://10.0.0.91:8190/UPC/SOMServices/76", "serviceType": "3", "serviceClass": "2", "transportType": "5", "serviceCategory": "4", "characteristics": [{"dataType": "String", "isAttribute": "0", "isMandatory": "1", "isPriceImpacting": "0", "isOrderEntryAttribute": "1", "startDate": "2021-09-07", "name": "IMSI", "value": "23434324234235325"}, {"dataType": "String", "isAttribute": "0", "isMandatory": "1", "isPriceImpacting": "0", "isOrderEntryAttribute": "1", "startDate": "2021-09-07", "name": "ICCID", "value": "232432432432432"}]}], "lrss": [{"id": "86", "name": "LRS_MSISDN", "href": "http://10.0.0.91:8190/UPC/SOMServices/86", "serviceType": "4", "serviceClass": "1", "transportType": "5", "serviceCategory": "4", "characteristics": [{"dataType": "String", "isAttribute": "0", "isMandatory": "1", "isPriceImpacting": "0", "isOrderEntryAttribute": "1", "startDate": "2021-09-07", "name": "MSISDN", "value": "21321321312"}]}]}]}}, "payment": {"accounId": "35435435", "upfrontPayment": "true", "isPaymentCollected": "true", "amount": "60.0", "totalAmount": "60.0", "collectionSourceType": "NORMAL", "collectionId": "12", "comment": "order payment", "currencyCode": "USD", "invoiceIds": "1001", "invoiceAmounts": "11", "collectionDate": "2021-10-05T07:06:02.094Z", "transactionId": "13213234234", "paymentDetail": [{"paymentMode": "1", "referenceExternalId": "4604", "amountPaid": "11.0", "cardNumber": "4453", "chequeNo": "1321323232", "ddNo": "232324343434", "chequeDate": "2021-10-05T07:06:02.094Z", "certificateNumber": "21424234234324"}]}}