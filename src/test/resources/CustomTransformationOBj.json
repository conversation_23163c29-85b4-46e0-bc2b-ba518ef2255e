{"CURRENT_EXECUTION": {"EXECUTION_DATA": {"subOrderId": "11310196930645", "orderId": "11310196930621", "subOrderName": "Onboarding Order", "createDate": 1599031905302, "createUser": "superAdmin", "subOrderStatus": "pending", "serviceType": "Internet", "serviceGroupId": "0", "orderCode": "OC_ONBOARDING", "depositAmount": 0.0, "partOfBundle": false, "serviceName": "INTERNET", "subOrderType": "SERVICE", "subOrderStages": [], "serviceInformation": {"service_name": "Internet", "activation_date": "06/22/2020", "registration_date": "06/21/2020", "network_activation_date": "06/21/2020", "rate_plan_name": "FAMILY PLAN", "language_id": "1", "status": "0", "connection_type": "1", "first_name": "JONAS", "last_name": "WATSON", "credit_limit": "0", "service_code": "3", "sales_rep_id": "", "sales_rep_name": "", "icc_id": "128739", "deposit_amount": "500.00", "access_type": "0", "addresses": [{"language_id": "1", "address_type": "3", "addr_line1": "930 Laurel St", "addr_line2": "", "addr_line3": "Columbia", "addr_line4": "SC", "addr_line5": "29201", "addr_line6": "5798 OGEECHEE RD UNIT 951", "geo_code": "12323"}], "subscriptions": [{"product_id": "83", "product_name": "Internet", "plan_id": "332", "plan_name": "Internet 200 Mbps", "plan_desc": "Internet 200 mbps", "effective_date": "11032020000000", "expiry_date": "26032020120153", "is_base_plan": "1", "status": "0", "is_prorata": "1", "subscription_type": "0", "cfss": [{"id": "2788", "name": "CFSS_DATA", "type": "1", "transport_type": "1", "service_type": "1", "service_class": "2", "service_category": "1", "service_registry_primary_key": "531", "characteristics": []}], "subscription_charges": [{"charge_category": "0", "is_prorata": "0", "prorata_flags": "", "charge_type": "2", "charge_code": "65", "charge_name": "HSI200_POP", "charge_desc": "", "charge_amount": "90", "start_date": "26032020120153", "end_date": "26032020120153", "charge_tax": "", "total_charge": "90", "charge_frequency": "2", "charge_factor": "1", "charge_amount_type": "Fixed", "quantity_based": "N", "original_charge_amount": "90.00"}, {"charge_category": "5", "prorata_flags": "1001", "charge_type": "2", "charge_code": "1", "charge_name": "Dual Play Promotion", "charge_desc": "Dual Play Promotion", "charge_amount": "32.00", "start_date": "13042020125004", "end_date": "31102040125004", "charge_tax": "", "charge_frequency": "2", "charge_factor": "1", "charge_amount_type": "Fixed", "discount_type": "F", "original_charge_amount": "32.00"}], "instance_id": "10001"}, {"product_id": "83", "product_name": "Internet", "plan_id": "332", "plan_name": "Internet 200 Mbps", "plan_desc": "Internet 200 mbps", "effective_date": "11032020000000", "expiry_date": "26032020120153", "is_base_plan": "0", "status": "0", "is_prorata": "1", "subscription_type": "0", "cfss": [{"id": "2788", "name": "CFSS_DATA", "type": "1", "transport_type": "1", "service_type": "1", "service_class": "2", "service_category": "1", "service_registry_primary_key": "531", "characteristics": []}], "subscription_charges": [{"charge_category": "0", "is_prorata": "0", "prorata_flags": "", "charge_type": "2", "charge_code": "65", "charge_name": "HSI200_POP", "charge_desc": "", "charge_amount": "90", "start_date": "26032020120153", "end_date": "26032020120153", "charge_tax": "", "total_charge": "90", "charge_frequency": "2", "charge_factor": "1", "charge_amount_type": "Fixed", "quantity_based": "N", "original_charge_amount": "90.00"}, {"charge_category": "5", "prorata_flags": "1001", "charge_type": "2", "charge_code": "1", "charge_name": "Dual Play Promotion", "charge_desc": "Dual Play Promotion", "charge_amount": "32.00", "start_date": "13042020125004", "end_date": "31102040125004", "charge_tax": "", "charge_frequency": "2", "charge_factor": "1", "charge_amount_type": "Fixed", "discount_type": "F", "original_charge_amount": "32.00"}], "instance_id": "10001"}], "param_list": [{"id": "business_office", "value": "RDSV"}], "deposit": {"currency_code": "USD", "comment": "deposit", "booking_id": "1", "status": "1", "deposit_amount": "30.00", "actual_amount": "50.00", "deposit_details": [{"payment_details": [{"payment_mode": "3", "charge_code": "55", "deposit_amount": "30.00"}]}]}, "dependant_devices": ["10002"]}, "basePlanId": "332", "installationType": "2"}}}