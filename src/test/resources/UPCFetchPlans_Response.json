{"UPCFetchPlanDetailsResponseAttributes": {"id": "14", "name": "BP with 4 Addons Prepaid", "href": "http://*********:8191/UPC/ProductOffering/14", "sellable": true, "dynamicBundle": false, "description": "BP with 4 Addons Prepaid", "invoiceDescription": "BP with 4 Addons Prepaid", "marketingDescription": "BP with 4 Addons Prepaid", "versionNo": "16", "versionStatus": "ACTIVE", "createdBy": "<EMAIL>", "sortingPreference": "0", "isLocked": "false", "productSpecifications": [{"id": "13", "name": "DSV_SPEC", "type": {"id": "2", "name": "AddOn"}, "productFamily": {"id": "1"}, "status": {"id": "0", "name": "Active"}, "cfss": [{"id": "22", "name": "CFSS_GSM_DATA", "serviceType": "1", "serviceClass": "2", "transportType": "5", "serviceCategory": "4", "validFor": {"startDateTime": "09/05/2021"}, "vesionNo": "1", "characteristics": [{"dataType": "String", "isAttribute": "1", "isMandatory": "1", "isPriceImpacting": "0", "isOrderEntryAttribute": "1", "startDate": "05/09/2021", "action": "ADD", "propogatedFrom": "", "name": "SUBSCRIPTION_ID"}]}, {"id": "23", "name": "CFSS_GSM_SMS", "serviceType": "1", "serviceClass": "2", "transportType": "5", "serviceCategory": "4", "validFor": {"startDateTime": "09/05/2021"}, "vesionNo": "1", "characteristics": [{"dataType": "String", "isAttribute": "1", "isMandatory": "1", "isPriceImpacting": "0", "isOrderEntryAttribute": "1", "startDate": "05/09/2021", "action": "ADD", "propogatedFrom": "", "name": "SUBSCRIPTION_ID"}]}, {"id": "24", "name": "CFSS_GSM_VOICE", "serviceType": "1", "serviceClass": "2", "transportType": "5", "serviceCategory": "4", "validFor": {"startDateTime": "09/05/2021"}, "vesionNo": "1", "characteristics": [{"dataType": "String", "isAttribute": "1", "isMandatory": "1", "isPriceImpacting": "0", "isOrderEntryAttribute": "1", "startDate": "05/09/2021", "action": "ADD", "propogatedFrom": "", "name": "SUBSCRIPTION_ID"}]}], "transportType": {"id": "5", "name": "GSM"}, "releationships": [], "versionNo": "1", "versionStatus": "ACTIVE", "createdBy": "<EMAIL>", "isLocked": "false", "validFor": {"startDateTime": "10/04/2021"}, "peerRelationShip": [], "comment": "Data_Sms_Voice_SPEC", "ratingParameters": [], "partner": "335", "serviceProvider": "334"}], "dynamicOfferAvailabilities": [{"isAvailable": "true", "versionNo": "16", "versionStatus": "ACTIVE", "preferredOffer": "false", "validFor": {"startDateTime": "10/29/2021"}, "conditionId": "53", "contextParam": [{"name": "ConnectionType", "value": "2", "valueName": "PREPAID", "versionNo": "16", "versionStatus": "ACTIVE"}], "disclaimer": {"id": "3", "name": "DisclaimerPrepaidPO", "disclaimerText": "Play by Prepaid Rules", "versionNo": "2", "disclaimerType": {"id": "1", "name": "PO Disclaimer"}, "validFor": {"startDateTime": "10/28/2021"}, "createdBy": "<EMAIL>", "versionStatus": "ACTIVE", "isLocked": "false"}}], "eligibilityOffers": [{"name": "Required Offers", "required": "false", "minQuantity": "1", "maxQuantity": "2", "eligibilityOfferId": "168", "versionNo": "16", "versionStatus": "ACTIVE", "poType": {"id": "2", "name": "ADDON"}, "eligibilityType": {"id": "5", "name": "RequiredOffer"}, "productOfferings": [{"id": "10", "name": "Free Data SMS Voice Addon", "sellable": true, "dynamicBundle": false, "description": "Free Data SMS Voice Addon", "invoiceDescription": "Free Data SMS Voice Addon", "marketingDescription": "Free Data SMS Voice Addon", "minQuantity": "1", "maxQuantity": "1", "versionNo": "1", "versionStatus": "ACTIVE", "createdBy": "<EMAIL>", "sortingPreference": "0", "isLocked": "false", "poStatus": {"id": "0", "name": "Active"}, "validFor": {"startDateTime": "09/30/2021"}, "productOfferGroup": {"id": "1", "name": "GSM PO Group", "description": "Common POG for GSM", "isBundleOffer": "false", "status": {"id": "0", "name": "Active"}, "productFamily": {"id": "1", "name": "GSM", "versionNo": "1", "versionStatus": "ACTIVE", "validFor": {"startDateTime": "09/16/2021"}, "createdBy": "SMAdmin", "isLocked": "false"}, "versionNo": "1", "versionStatus": "ACTIVE", "validFor": {"startDateTime": "09/16/2021"}, "isLocked": "false", "partner": "335", "serviceProvider": "334"}, "productOfferingPrice": [{"id": "13", "name": "DSV Addon NRBP", "price": "0.00", "factor": "1", "isApplicable": "false", "isStackable": "false", "dynamicPrice": false, "applyToExistingCustomer": false, "priceOverride": false, "tieredDiscount": false, "priceMode": {"id": "1", "name": "Non-Recurring"}, "priceCategory": {"id": "0", "name": "Base Price", "billingChargeType": {"id": "10", "name": "Others"}, "mandatory": false, "taxable": false, "billingType": {"id": "1", "name": "Service Level"}, "validFor": {"startDateTime": "11/09/2021"}, "billSection": "", "isRecoverableUpfrontCost": "true", "contractualObligation": "false", "isNonAccountingItem": "false", "trigger": {}, "billingChargeCategory": {"id": "3", "name": "Both"}}, "priceType": {"id": "0", "name": "Fixed"}, "priceCode": {"id": "1", "name": "WAKANDA", "code": "100", "description": "WAKANDA", "synonym": {"id": "1", "name": "ACT"}, "status": {"id": "0", "name": "Active"}, "taxable": "0", "reversal": "0", "normalizetax": "0", "validFor": {"startDateTime": "09/16/2021"}, "versionStatus": "ACTIVE", "isLocked": "false", "createdBy": "SMAdmin", "versionNo": "1"}, "popType": {"id": "0", "name": "Normal"}, "prorataEnable": false, "prorataEnabledInSuspension": false, "prorataEnabledInTermination": false, "prorataEnabledInPlanChange": false, "prorataEnabledInActivation": false, "tierbased": false, "frequency": {"id": "2", "name": "Monthly"}, "rules": [], "discountAvailabilities": [], "isApplicableForAlasCarte": "false", "minMRC": "", "maxMRC": "", "discountEvolution": {}, "manualWaiver": "false", "versionNo": "1", "versionStatus": "ACTIVE", "validFor": {"startDateTime": "10/04/2021"}, "createdBy": "0", "splitDetails": [], "cartTierDetails": [], "currentDate": false, "isLocked": "false", "isAutomatic": "false", "defineSpecificInclusionExclusion": [], "partner": "335", "applyFallBack": "false", "serviceProviderID": "334", "fixedPayment": "false", "activationParking": "false", "billed": false}], "productSpecifications": [{"id": "13", "name": "DSV_SPEC", "type": {"id": "2", "name": "AddOn"}, "productFamily": {"id": "1"}, "status": {"id": "0", "name": "Active"}, "cfss": [{"id": "22", "name": "CFSS_GSM_DATA", "serviceType": "1", "serviceClass": "2", "transportType": "5", "serviceCategory": "4", "validFor": {"startDateTime": "09/05/2021"}, "vesionNo": "1", "characteristics": [{"dataType": "String", "isAttribute": "1", "isMandatory": "1", "isPriceImpacting": "0", "isOrderEntryAttribute": "1", "startDate": "05/09/2021", "action": "ADD", "propogatedFrom": "", "name": "SUBSCRIPTION_ID"}]}, {"id": "23", "name": "CFSS_GSM_SMS", "serviceType": "1", "serviceClass": "2", "transportType": "5", "serviceCategory": "4", "validFor": {"startDateTime": "09/05/2021"}, "vesionNo": "1", "characteristics": [{"dataType": "String", "isAttribute": "1", "isMandatory": "1", "isPriceImpacting": "0", "isOrderEntryAttribute": "1", "startDate": "05/09/2021", "action": "ADD", "propogatedFrom": "", "name": "SUBSCRIPTION_ID"}]}, {"id": "24", "name": "CFSS_GSM_VOICE", "serviceType": "1", "serviceClass": "2", "transportType": "5", "serviceCategory": "4", "validFor": {"startDateTime": "09/05/2021"}, "vesionNo": "1", "characteristics": [{"dataType": "String", "isAttribute": "1", "isMandatory": "1", "isPriceImpacting": "0", "isOrderEntryAttribute": "1", "startDate": "05/09/2021", "action": "ADD", "propogatedFrom": "", "name": "SUBSCRIPTION_ID"}]}], "transportType": {"id": "5", "name": "GSM"}, "releationships": [], "versionNo": "1", "versionStatus": "ACTIVE", "createdBy": "<EMAIL>", "isLocked": "false", "validFor": {"startDateTime": "10/04/2021"}, "peerRelationShip": [], "comment": "Data_Sms_Voice_SPEC", "ratingParameters": [], "partner": "335", "serviceProvider": "334"}], "contracts": [], "poBundles": [], "poDynamicGroups": [], "dynamicOfferAvailabilities": [], "bucketDetails": [], "partner": "335", "serviceProvider": "334"}], "subGroupPo": []}], "bucketDetails": []}, "executionData": {"service": {"id": "234567890"}, "itemCharacteristic": [{"name": "beneficiary", "value": "345678"}]}}