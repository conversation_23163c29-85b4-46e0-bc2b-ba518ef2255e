{"draw": 0, "recordsFiltered": 10, "recordsTotal": 3, "totalPages": 1, "last": true, "data": [{"subscriptionId": 638, "planId": 1, "planName": "plan 1", "upcVersion": 1, "planType": 1, "status": 1, "createUser": "789", "activationDate": "2021-10-13T23:07:17.000Z", "createDate": "2021-10-19T05:17:43.000Z", "lastModifiedDate": "2021-10-19T05:17:43.000Z", "serviceSeqId": 3639, "serviceId": "**********", "accountId": "6dbss1149", "charges": [{"chargeId": 679, "subscriptionId": 638, "upcChargeId": 1001, "upcChargeName": "Base offer Fee", "chargeRecurranceType": 1, "chargeCategory": 0, "chargeDesc": "Base offer Fee", "chargeVersion": 0, "chargeFrequency": 0, "quantity": 1, "amount": 0.0, "prorationFlag": "0", "createUser": "789", "chargeStartDate": "2021-10-19T05:16:48.000Z", "chargeEndDate": "2021-10-19T05:16:48.000Z", "createDate": "2021-10-19T05:17:43.000Z", "lastModifiedDate": "2021-10-19T05:17:43.000Z", "isProrata": 1, "status": 1}]}, {"subscriptionId": 638, "planId": 7, "planName": "plan 1", "upcVersion": 19, "planType": 1, "status": 1, "createUser": "789", "activationDate": "2021-10-13T23:07:17.000Z", "createDate": "2021-10-19T05:17:43.000Z", "lastModifiedDate": "2021-10-19T05:17:43.000Z", "serviceSeqId": 3639, "serviceId": "**********", "accountId": "6dbss1149", "charges": [{"chargeId": 679, "subscriptionId": 638, "upcChargeId": 1001, "upcChargeName": "Base offer Fee", "chargeRecurranceType": 1, "chargeCategory": 0, "chargeDesc": "Base offer Fee", "chargeVersion": 0, "chargeFrequency": 0, "quantity": 1, "amount": 0.0, "prorationFlag": "0", "createUser": "789", "chargeStartDate": "2021-10-19T05:16:48.000Z", "chargeEndDate": "2021-10-19T05:16:48.000Z", "createDate": "2021-10-19T05:17:43.000Z", "lastModifiedDate": "2021-10-19T05:17:43.000Z", "isProrata": 1, "status": 1}]}, {"subscriptionId": 638, "planId": 7, "planName": "plan 1", "upcVersion": 4, "planType": 1, "status": 1, "createUser": "789", "activationDate": "2021-10-13T23:07:17.000Z", "createDate": "2021-10-19T05:17:43.000Z", "lastModifiedDate": "2021-10-19T05:17:43.000Z", "serviceSeqId": 3639, "serviceId": "**********", "accountId": "6dbss1149", "charges": [{"chargeId": 679, "subscriptionId": 638, "upcChargeId": 1001, "upcChargeName": "Base offer Fee", "chargeRecurranceType": 1, "chargeCategory": 0, "chargeDesc": "Base offer Fee", "chargeVersion": 0, "chargeFrequency": 0, "quantity": 1, "amount": 0.0, "prorationFlag": "0", "createUser": "789", "chargeStartDate": "2021-10-19T05:16:48.000Z", "chargeEndDate": "2021-10-19T05:16:48.000Z", "createDate": "2021-10-19T05:17:43.000Z", "lastModifiedDate": "2021-10-19T05:17:43.000Z", "isProrata": 1, "status": 1}]}], "channelId": "CRM", "entityId": "200", "requestId": "************", "transactionId": "*************", "responseTimestamp": "2021-10-19T11:30:39.623Z"}