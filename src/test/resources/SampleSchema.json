{"$schema": "http://json-schema.org/draft-07/schema", "$id": "http://example.com/example.json", "type": "object", "title": "The root schema", "description": "The root schema comprises the entire JSON document.", "default": {}, "examples": [{"order_type": "ChangePlan"}], "required": ["order_type"], "properties": {"order_type": {"$id": "#/properties/order_type", "type": "string", "title": "The order_type schema", "description": "An explanation about the purpose of this instance.", "default": "", "examples": ["ChangePlan"]}}, "additionalProperties": true}