package in.co.sixdee.bss.com.orderorchestrator.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.WaitingProcessInfoEntity;
import in.co.sixdee.bss.om.model.dto.order.Deposit;
import in.co.sixdee.bss.om.model.dto.order.OrderPayment;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class OrderCallBack implements Serializable {

	/**
	 *
	 */
	private static final long                     serialVersionUID = 1L;
	private              String                   orderId;
	private              String                   correlationId;
	private              String                   status;
	private              String                   failureReason;
	@JsonProperty("isRollbacked")
	private              boolean                  rollbacked;
	private              OrderPayment             payment;
	private              List<Deposit>            deposit;
	private              String                   approver;
	private              String                   comments;
	private              String                   externalId;
	private              String                   subOrderId;
	private              String                   approvalId;
	private              String                   timestamp;
	private              String                   callbackType;
	private              WaitingProcessInfoEntity waitingEventInfo;
	private              String                   referenceId;
	private              String                   newIMSI;
	private              String                   newICCID;
	private              String                   type;
	private 		     String					  profileId;
	private  			 String					  accountId;
	private  			 String					  orderType;
	private  			 String					  entityId;
	private  			 String					  stageCode;
	private              CallBackHeader           callbackHeader;
	@Getter(value= AccessLevel.NONE)
	private 			HashMap<String,Object>    mnpAttributes;
 
	@Getter(value = AccessLevel.NONE)
	private Integer numberOfRetry = 0;

	public Integer isNumberOfRetry() {
		return this.numberOfRetry = numberOfRetry + 1;
	}
	
	public HashMap<String, Object> getmnpAttributes() {
		if (mnpAttributes == null)
			mnpAttributes = new HashMap<>();
		return mnpAttributes;
	}

}
