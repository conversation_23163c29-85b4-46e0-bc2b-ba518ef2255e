
/**
 * 
 */
package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.esb.ESBRequest;
import in.co.sixdee.bss.om.model.dto.esb.ESBRequest.CharacteristicRelationship;
import in.co.sixdee.bss.om.model.dto.esb.ESBRequest.ServiceCharacteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 *
 */
@Log4j2
@Component(value = "nccChangePlan")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class NCCChangePlanHandler extends AbstractDelegate {

	protected Order orderPayload = null;
	protected String request = null;
	protected int index = 0;

	protected final ObjectMapper objectMapper;

	@Override
	protected void execute() throws Exception {
		try {
			request = createNCCRequest();
			var callThirdPartyDTO = callThirdParty(request);
			if (callThirdPartyDTO == null) {
				executionContext.setError(true);
				return;
			}
			validateResponse(callThirdPartyDTO);
		} catch (Exception e) {
			log.error("{} In {} .execute Exception occurred ", executionContext.getTraceId(), this.getClass().getName(),
					e);
		}
	}

	protected String createNCCRequest() throws Exception {
		ESBRequest esbRequqest = new ESBRequest();
		List<ServiceCharacteristic> serviceCharacteristics = new ArrayList<>();
		List<CharacteristicRelationship> characteristicRelationship = new ArrayList<>();
		ServiceCharacteristic serviceCharacteristic = new ServiceCharacteristic();

		CharacteristicRelationship charRelationship = new CharacteristicRelationship();
		charRelationship.setId("string");
		charRelationship.setRelationshipType("string");
		characteristicRelationship.add(charRelationship);

		serviceCharacteristic = createDeviceCharacteristics();
		serviceCharacteristic.setCharacteristicRelationship(characteristicRelationship);
		serviceCharacteristics.add(serviceCharacteristic);

		List<ServiceCharacteristic> addonCharacteristics = createAddonCharacteristics();
		addonCharacteristics.forEach(
				addonCharacteristic -> addonCharacteristic.setCharacteristicRelationship(characteristicRelationship));
		serviceCharacteristics.addAll(addonCharacteristics);

		if (ObjectUtils.isNotEmpty(serviceCharacteristics))
			esbRequqest.setServiceCharacteristic(serviceCharacteristics);
		if (ObjectUtils.isNotEmpty(esbRequqest))
			request = objectMapper.writeValueAsString(esbRequqest);
		return request;
	}

	private List<ServiceCharacteristic> createAddonCharacteristics() {
		List<ServiceCharacteristic> serviceCharacteristics = new ArrayList<>();

		if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData().containsKey("stopRenewalList"))) {
			var subscriptions = objectMapper.convertValue(executionContext.getWorkflowData().get("stopRenewalList"),
					new TypeReference<List<Subscription>>() {
					});

			var changePlanOptionConfig = cache.getCacheDetailsFromDBMap(
					NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG, "NCC_CHANGE_PLAN_OPTION");

			var carryOverFlagConfig = cache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG,
					"NCC_CHANGE_PLAN_CARRY_OVER_FLAG");

			for (Subscription subscription : subscriptions) {
				if (subscription.isEsbCallRqd()) {
					ServiceCharacteristic oldBundleName = new ServiceCharacteristic();
					oldBundleName.setId("string");
					oldBundleName.setName("oldBundleName");
					oldBundleName.setValueType("string");
					oldBundleName.setValue(subscription.getExternalOfferId());
					oldBundleName.setBaseType("string");
					oldBundleName.setSchemaLocation("string");
					oldBundleName.setType("changePlanDataList");
					oldBundleName.setArrayIndex(String.valueOf(index));
					serviceCharacteristics.add(oldBundleName);

					if (ObjectUtils.isNotEmpty(changePlanOptionConfig)
							&& ObjectUtils.isNotEmpty(changePlanOptionConfig.getNgTableData().get("CONFIG_VALUE"))) {
						ServiceCharacteristic changePlanOption = new ServiceCharacteristic();
						changePlanOption.setId("string");
						changePlanOption.setName("changePlanOption");
						changePlanOption.setValueType("string");
						changePlanOption
								.setValue(changePlanOptionConfig.getNgTableData().get("CONFIG_VALUE").toString());
						changePlanOption.setBaseType("string");
						changePlanOption.setSchemaLocation("string");
						changePlanOption.setType("changePlanDataList");
						changePlanOption.setArrayIndex(String.valueOf(index));
						serviceCharacteristics.add(changePlanOption);
					}

					if (ObjectUtils.isNotEmpty(carryOverFlagConfig)
							&& ObjectUtils.isNotEmpty(carryOverFlagConfig.getNgTableData().get("CONFIG_VALUE"))) {
						ServiceCharacteristic carryOverFlag = new ServiceCharacteristic();
						carryOverFlag.setId("string");
						carryOverFlag.setName("carryOverFlag");
						carryOverFlag.setValueType("string");
						carryOverFlag.setValue(carryOverFlagConfig.getNgTableData().get("CONFIG_VALUE").toString());
						carryOverFlag.setBaseType("string");
						carryOverFlag.setSchemaLocation("string");
						carryOverFlag.setType("changePlanDataList");
						carryOverFlag.setArrayIndex(String.valueOf(index));
						serviceCharacteristics.add(carryOverFlag);
					}
					index = index + 1;
				}
			}
		}

		return serviceCharacteristics;
	}

	private ServiceCharacteristic createDeviceCharacteristics() {
		String prefix = null;
		String ocsServiceSeqId = null;
		ServiceCharacteristic serviceCharacteristic = new ServiceCharacteristic();

		var appConfig = cache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG,
				"NCC_DEVICE_PREFIX");
		if (ObjectUtils.isNotEmpty(appConfig) && ObjectUtils.isNotEmpty(appConfig.getNgTableData().get("CONFIG_VALUE")))
			prefix = appConfig.getNgTableData().get("CONFIG_VALUE").toString();

		if (executionContext.getEnrichmentResults() != null) {
			var enrichmentResults = executionContext.getEnrichmentResults();
			if (enrichmentResults.containsKey("serviceInfo")) {
				var serviceInfo = objectMapper.convertValue(enrichmentResults.get("serviceInfo"), LinkedHashMap.class);
				if (ObjectUtils.isNotEmpty(serviceInfo.get("ocsServiceSeqId"))) {
					ocsServiceSeqId = serviceInfo.get("ocsServiceSeqId").toString();
					if (StringUtils.isNotEmpty(prefix))
						ocsServiceSeqId = prefix.concat(ocsServiceSeqId);
				}
			}
			if (StringUtils.isNotEmpty(ocsServiceSeqId)) {
				serviceCharacteristic.setId("string");
				serviceCharacteristic.setName("entityID");
				serviceCharacteristic.setValueType("string");
				serviceCharacteristic.setValue(ocsServiceSeqId);
				serviceCharacteristic.setBaseType("string");
				serviceCharacteristic.setSchemaLocation("string");
				serviceCharacteristic.setType("string");
				serviceCharacteristic.setArrayIndex("-1");
			}
		}
		return serviceCharacteristic;
	}

}
