package in.co.sixdee.bss.com.orderorchestrator.config.util;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.*;
import in.co.sixdee.bss.com.orderorchestrator.core.uniquenumgen.UniqueNumberGenerator;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderStageService;
import in.co.sixdee.bss.common.util.SequenceGenerator;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.enrichment.ProfileInfo;
import in.co.sixdee.bss.om.model.dto.order.IndividualIdentification;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.ProfileRef;
import in.co.sixdee.bss.om.model.dto.order.ServiceManagement;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
@RequiredArgsConstructor
@Log4j2
public class MNPServiceUtil {

    final GetDataFromCache cache;

    private final ObjectMapper objectMapper;

    private final UniqueNumberGenerator uniqueNumberGenerator;

    String operatorDataConfigTable = CacheConstants.CacheKeys.COM_OPERATOR_DATA_CONFIG.toString();

    public String generateMNPReferenceId(String donorTelcoId, String recipientTelcoId) {
        String mnpReferenceId = null;
        try {
            //int randomNumber = RandomGenerator.getInstance().nextRandom();
            int fourDigitSequence = uniqueNumberGenerator.getNextUniqueNumber();
            String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            mnpReferenceId = recipientTelcoId + "-M-" + donorTelcoId + "-" + currentDate + "-F" + fourDigitSequence;
            CacheTableDataDTO prefixConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(), CacheConstants.CacheFields.MNP_REFERENCE_ID_PREFIX.toString());
            if (prefixConfig != null) {
                String mnpReferenceIdPrefix = prefixConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
                mnpReferenceId = mnpReferenceIdPrefix + mnpReferenceId;
            } else {
                log.info("Unable to find prefix configuration for MNP reference id. generating reference id with out prefix");
            }

        } catch (Exception e) {
            log.info("Exception occurred while generating MNP reference id", e);
        }
        return mnpReferenceId;
    }

    public String generateMNPRequestId(String orderId) {

        long random = SequenceGenerator.getSequencerInstance().nextId();
        String stringRand = String.valueOf(random);
        String last8 = stringRand.substring(stringRand.length() - 8);
        return "003" + "-" + last8;
    }

    public void getServiceIdWithoutCC(OrderFlowContext executionContext) {
        String countryCode = null;
        String serviceId = null;
        CacheTableDataDTO countryCodeConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.SINGAPORE_COUNTRY_CODE.toString());
        if (OrderTypes.INTERIM_NUMBER_PORT_IN.equals(executionContext.getOrder().getOrderType())) {
            serviceId = executionContext.getOrder().getServiceManagement().getNewMsisdn();
        } else if (ObjectUtils.isNotEmpty(executionContext.getOrder().getProfile().getAccount().getServiceGroups().get(0).getServices().get(0).getServiceId())) {
            serviceId = executionContext.getOrder().getProfile().getAccount().getServiceGroups().get(0).getServices().get(0).getServiceId();
        }
        if (countryCodeConfig != null && serviceId != null) {
            countryCode = countryCodeConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
            if (serviceId.startsWith(countryCode)) {
                serviceId = serviceId.substring(countryCode.length());
            }
            executionContext.getAttributes().put("msisdn_without_cc", serviceId);
            log.info("service id without cc : {}", serviceId);
        }

    }


    public boolean isExternalOperator(String operatorName) {
        CacheTableDataDTO externalOperatorNamesConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(), CacheConfigKeys.CONFIG_KEY_EXTERNAL_TELCO_NAMES);
        return externalOperatorNamesConfig != null && Arrays.asList(externalOperatorNamesConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString()).split(",")).contains(operatorName);
    }

    public boolean isSingtelOperator(String operatorName) {
        CacheTableDataDTO singtelOperatorNamesConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(), CacheConfigKeys.CONFIG_KEY_SINGTEL_TELCO_NAMES);
        return singtelOperatorNamesConfig != null && Arrays.asList(singtelOperatorNamesConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString()).split(",")).contains(operatorName);
    }

    public boolean findDonorOperatorDetails(OrderFlowContext executionContext, String operator) {
        CacheTableDataDTO operatorDetails = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_OPERATOR_DATA_CONFIG_BY_OP_NAME.toString(), operator);
        if (operatorDetails != null) {
            Map<String, String> operatorDetailsMap = operatorDetails.getNgTableData();
            executionContext.getAttributes().put(MNPConstants.DONOR_OPERATOR_CODE, operatorDetailsMap.get(CacheConstants.CacheFields.OPERATOR_CODE.toString()));
            executionContext.getAttributes().put(MNPConstants.DONOR_OPERATOR_NAME, operatorDetailsMap.get(CacheConstants.CacheFields.OPERATOR_NAME.toString()));
            executionContext.getAttributes().put(MNPConstants.DONOR_OPERATOR_ROUTE_NUMBER, operatorDetailsMap.get(CacheConstants.CacheFields.ROUTE_NUMBER.toString()));
            return true;
        }
        return false;
    }

    public boolean findOwnerOperator(OrderFlowContext executionContext, String serviceId) { // find the owner using the msisdn range.
        ArrayList<CacheTableDataDTO> operatorDetails = cache.getCacheDetailsFromDBList(operatorDataConfigTable);
        boolean operatorFound = false;
        String serviceIdwithoutCC = serviceIdWithoutCC(serviceId);
        int first4Digits = NumberUtils.toInt(serviceIdwithoutCC.substring(0, 4));
        for (CacheTableDataDTO operatorInfo : operatorDetails) {
            String msisdnRanges = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.MSISDN_RANGE.toString());
            if (msisdnRanges != null) {
                // an operator may have multiple msisdn ranges
                String[] rangeList = msisdnRanges.split(GenericConstants.OPERATOR_COMMA);
                for (String range : rangeList) {
                    String[] bounds = range.split(GenericConstants.OPERATOR_HIFEN);
                    if (bounds.length == 2) {
                        int lowerBound = NumberUtils.toInt(bounds[0]);
                        int upperBound = NumberUtils.toInt(bounds[1]);
                        if (first4Digits >= lowerBound && first4Digits <= upperBound) {
                            String operatorCode = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.OPERATOR_CODE.toString());
                            String operatorName = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.OPERATOR_NAME.toString());
                            String routeNumber = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.ROUTE_NUMBER.toString());
                            executionContext.getAttributes().put(MNPConstants.OWNER_OPERATOR_CODE, operatorCode);
                            executionContext.getAttributes().put(MNPConstants.OWNER_OPERATOR_NAME, operatorName);
                            executionContext.getAttributes().put(MNPConstants.OWNER_OPERATOR_ROUTE_NUMBER, routeNumber);
                            log.info("Found owner operator {}", operatorName);
                            operatorFound = true;
                            return operatorFound;
                        }
                    } else {
                        log.info("Invalid range configuration. skipping the range {}", range);
                    }
                }
            } else {
                log.info("Unable to find msisdn range configuration for owner operator {}", operatorInfo.getNgTableData().get(CacheConstants.CacheFields.OPERATOR_NAME.toString()));
            }

        }
        return operatorFound;

    }

    private String serviceIdWithoutCC(String serviceId) {

        String countryCode = null;
        CacheTableDataDTO countryCodeConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.SINGAPORE_COUNTRY_CODE.toString());

        if (countryCodeConfig != null) {
            countryCode = countryCodeConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
            if (serviceId.startsWith(countryCode)) {
                return (serviceId.substring(countryCode.length()));
            }
        }
        return serviceId;
    }

    private boolean findOperatorByMvnoId(OrderFlowContext executionContext, String entityId, boolean isRecipient) {
        boolean operatorFound = false;
        ArrayList<CacheTableDataDTO> operatorDetails = cache.getCacheDetailsFromDBList(operatorDataConfigTable);
        if (entityId == null || operatorDetails == null) {
            log.info("Unable to find operator details as entity id or operator code config is not available. entityId: {}, operatorDetails: {}", entityId, operatorDetails);
            return false;
        }

        for (CacheTableDataDTO operatorInfo : operatorDetails) {
            String mvnoIds = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.MVNO_ID.toString());
            if (mvnoIds != null) {
                List<String> mvnoList = Arrays.asList(mvnoIds.split(GenericConstants.OPERATOR_COMMA));
                if (mvnoList.contains(entityId)) {
                    String operatorCode = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.OPERATOR_CODE.toString());
                    String operatorName = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.OPERATOR_NAME.toString());
                    String routeNumber = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.ROUTE_NUMBER.toString());
                    if (isRecipient) {
                        executionContext.getAttributes().put(MNPConstants.RECIPIENT_OPERATOR_CODE, operatorCode);
                        executionContext.getAttributes().put(MNPConstants.RECIPIENT_OPERATOR_NAME, operatorName);
                        executionContext.getAttributes().put(MNPConstants.RECIPIENT_OPERATOR_ROUTE_NUMBER, routeNumber);
                    } else {
                        executionContext.getAttributes().put(MNPConstants.DONOR_OPERATOR_CODE, operatorCode);
                        executionContext.getAttributes().put(MNPConstants.DONOR_OPERATOR_NAME, operatorName);
                        executionContext.getAttributes().put(MNPConstants.DONOR_OPERATOR_ROUTE_NUMBER, routeNumber);

                    }
                    operatorFound = true;
                    break;
                }
            } else {
                log.info("Unable to find mvno id details for operator {}", operatorInfo.getNgTableData().get(CacheConstants.CacheFields.OPERATOR_NAME.toString()));
            }
        }
        if (!operatorFound) {
            log.info("Unable to find operator details from {} table.", operatorDataConfigTable);
        }
        return operatorFound;
    }

    public boolean findRecipientOperatorByMvnoId(OrderFlowContext executionContext, String entityId) {
        return findOperatorByMvnoId(executionContext, entityId, true);
    }

    public boolean findDonorOperatorByMvnoId(OrderFlowContext executionContext, String entityId) {
        return findOperatorByMvnoId(executionContext, entityId, false);
    }


    public String findMNPCustomerNameAndId(OrderFlowContext executionContext, boolean isRetry, OrderStageEntity failedStage) {
        String mnpCustomerName = null;
        String customerType = "Individual";

        Order order = executionContext.getOrder();
        ProfileRef profile = order.getProfile();
        ServiceManagement serviceManagement = order.getServiceManagement();

		if (OrderTypes.INTERIM_NUMBER_PORT_IN.equals(order.getOrderType())) {
			mnpCustomerName = getCustomerName(serviceManagement.getFirstName(), serviceManagement.getLastName());
		     customerType=fetchIdentificationDetailsForInterim(executionContext, serviceManagement);
		} else {
            boolean mnpAddService = StringUtils.isNotEmpty(profile.getProfileId());
            boolean isIdFound = false;

            if (mnpAddService) {
                if (ObjectUtils.isNotEmpty(profile) && ObjectUtils.isNotEmpty(profile.getIndividualIdentification())) {
                    customerType = findIdDetails(executionContext, profile.getIndividualIdentification());
                    isIdFound = true;
                }

                if (ObjectUtils.isNotEmpty(executionContext.getEnrichmentResults())
                        && ObjectUtils.isNotEmpty(executionContext.getEnrichmentResults().get("profileInfo"))) {
                    ProfileInfo profileInfo = objectMapper.convertValue(
                            executionContext.getEnrichmentResults().get("profileInfo"), ProfileInfo.class);
                    mnpCustomerName = getCustomerName(profileInfo.getGivenName(), profileInfo.getFamilyName());
                    if (!isIdFound) {
                        customerType = findIdDetails(executionContext, profileInfo.getIndividualIdentification());
                    }
                }
            } else {
                mnpCustomerName = getCustomerName(profile.getGivenName(), profile.getFamilyName());
                customerType = findIdDetails(executionContext, profile.getIndividualIdentification());
            }
        }
        executionContext.getAttributes().put(MNPConstants.MNP_CUSTOMER_NAME, mnpCustomerName);
        executionContext.getAttributes().put(MNPConstants.MNP_TIMESTAMP, LocalDateTime.now().format(DateTimeFormatter.ofPattern(MNPConstants.MNP_DATETIME_FORMAT)));
        executionContext.getAttributes().put(MNPConstants.MNP_PORTING_TIME, findPortingTimeStamp(isRetry, failedStage));

        return customerType;
    }

	private String fetchIdentificationDetailsForInterim(OrderFlowContext executionContext,
			ServiceManagement serviceManagement) {
		String customerType = null;
		ProfileInfo profileInfo = objectMapper.convertValue(executionContext.getEnrichmentResults().get("profileInfo"),
				ProfileInfo.class);
		if (ObjectUtils.isNotEmpty(profileInfo) && ObjectUtils.isNotEmpty(profileInfo.getIndividualIdentification())
				&& ObjectUtils.isNotEmpty(serviceManagement.getIndividualIdentification())) {

			List<IndividualIdentification> matchingIdentifications = fetchMatchingIdentificationDetails(
					serviceManagement.getIndividualIdentification(), profileInfo.getIndividualIdentification());

			if (ObjectUtils.isNotEmpty(matchingIdentifications)) {
				customerType = findIdDetails(executionContext, matchingIdentifications);
			} else {
				customerType = findIdDetails(executionContext, profileInfo.getIndividualIdentification());
			}
		}
		if (StringUtils.isEmpty(customerType) && ObjectUtils.isNotEmpty(profileInfo.getIndividualIdentification()))
			customerType = findIdDetails(executionContext, profileInfo.getIndividualIdentification());
		return customerType;
	}

	private List<IndividualIdentification> fetchMatchingIdentificationDetails(
			List<IndividualIdentification> requestIdDetails, List<IndividualIdentification> profileIdDetails) {
		List<IndividualIdentification> matchedIndividualIdentifications = new ArrayList<>();

		if (ObjectUtils.isNotEmpty(requestIdDetails) && ObjectUtils.isNotEmpty(profileIdDetails)) {
			for (IndividualIdentification requestIdDetail : requestIdDetails) {
				for (IndividualIdentification profileIdDetail : profileIdDetails) {
					if (ObjectUtils.isNotEmpty(requestIdDetail.getIdentificationId())
							&& ObjectUtils.isNotEmpty(profileIdDetail.getIdentificationId()) && profileIdDetail
									.getIdentificationId().equalsIgnoreCase(requestIdDetail.getIdentificationId())) {
						matchedIndividualIdentifications.add(profileIdDetail);
						log.info("Matched IndividualIdentification details: " + profileIdDetail);
					}
				}
			}
		}

		return matchedIndividualIdentifications.isEmpty() ? null : matchedIndividualIdentifications;
	}

    private String findIdDetails(OrderFlowContext executionContext, List<IndividualIdentification> individualIdentifications) {
        String customerType = "Individual";
        if (individualIdentifications == null) {
            log.info("identification details are null. unable to find the id type and number");
            return customerType;
        }
        String cacheField = CacheConstants.CacheFields.MNP_ID_TYPE_CORPORATE.toString();
        CacheTableDataDTO idTypeMnpCorpConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(), cacheField);

        if (idTypeMnpCorpConfig == null || idTypeMnpCorpConfig.getNgTableData() == null) {
            log.info("Unable to find id configuration for {}", cacheField);
            getIdentificationIdAndType(executionContext, individualIdentifications.get(0));
            return customerType;
        }
        //Set<String> corpIdSet = new HashSet<>(Arrays.asList(idTypeMnpCorpConfig.getNgTableData().get(cacheField).split(",")));
        List<String> corpIdSet = Arrays.asList(idTypeMnpCorpConfig.getNgTableData().get("CONFIG_VALUE").split(","));
		for (IndividualIdentification individualIdentification : individualIdentifications) {
			if (corpIdSet.contains(individualIdentification.getIdentificationType())) {
				getIdentificationIdAndType(executionContext, individualIdentification);
				return "Corporate";
			}
		}
		getIdentificationIdAndType(executionContext, individualIdentifications.get(0));
		return customerType;
    }

	private void getIdentificationIdAndType(OrderFlowContext executionContext,
			IndividualIdentification individualIdentification) {
		if (StringUtils.isNotEmpty(individualIdentification.getKycIDType())) {
			executionContext.getAttributes().put(MNPConstants.MNP_ID_TYPE, individualIdentification.getKycIDType());
			executionContext.getAttributes().put(MNPConstants.MNP_ID_NUMBER,
					individualIdentification.getIdentificationId());
		} else {
			executionContext.getAttributes().put(MNPConstants.MNP_ID_TYPE,
					individualIdentification.getIdentificationType());
			executionContext.getAttributes().put(MNPConstants.MNP_ID_NUMBER,
					individualIdentification.getIdentificationId());
		}
	}

    private String getCustomerName(String firstName, String lastName) {
        return StringUtils.isNotEmpty(lastName) ? firstName + " " + lastName : firstName;
    }

    private String findPortingTimeStamp(boolean isRetry, OrderStageEntity failedStage) {

        LocalDateTime currentDate = LocalDateTime.now();
        if (isRetry && failedStage != null && failedStage.getLastModifiedDate() != null) {
            LocalDateTime stageFailedTime = failedStage.getLastModifiedDate().toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            if (stageFailedTime.toLocalDate().equals(currentDate.toLocalDate()) && isQuotaError(failedStage.getStateReason())) {
                currentDate = currentDate.plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
            }
        }
        LocalDateTime targetPortInSubmitTime = getTargetDateTime(currentDate);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(MNPConstants.MNP_DATETIME_FORMAT);
        return targetPortInSubmitTime.format(formatter);
    }

    private boolean isQuotaError(String errorDesc) {
        CacheTableDataDTO quotaErrorCodeConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(), CacheConstants.CacheFields.MNP_INSUFFICIENT_QUOTA_ERROR_CODE.toString());
        if (quotaErrorCodeConfig != null) {
            String errorCode = quotaErrorCodeConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
            return StringUtils.isNotEmpty(errorDesc) && errorDesc.contains(errorCode);
        }
        log.info("Configuration missing for MNP_INSUFFICIENT_QUOTA_ERROR_CODE in OM_APPLICATION_CONFIG table. setting quota error as false");
        return false;
    }


    public LocalDateTime getTargetDateTime(LocalDateTime currentDateTime) {
        LocalTime currentTime = currentDateTime.toLocalTime();
        LocalDateTime targetDateTime;
        LocalTime beforeMidNight = LocalTime.of(23, 59, 59);

        if (isSaturdayAfter8pm(currentDateTime) || isSunday(currentDateTime)) {
            // If it's Saturday after 8 PM or Sunday, return next Monday 11 PM
            targetDateTime = getNextMonday(currentDateTime).withHour(23).withMinute(0).withSecond(0).withNano(0);
        } else if (currentTime.isAfter(LocalTime.of(19, 59, 59)) && (currentTime.isBefore(beforeMidNight) || currentTime.equals(beforeMidNight))) {
            // If it's any other day after 8 PM and before 12 AM, return next day's 11 PM
            targetDateTime = currentDateTime.plusDays(1).withHour(23).withMinute(0).withSecond(0).withNano(0);
        } else {
            // Else, return current day's 11 PM
            targetDateTime = currentDateTime.withHour(23).withMinute(0).withSecond(0).withNano(0);
        }

        return targetDateTime;
    }

    private boolean isSaturdayAfter8pm(LocalDateTime dateTime) {
        return dateTime.getDayOfWeek() == DayOfWeek.SATURDAY && dateTime.toLocalTime().isAfter(LocalTime.of(19, 59, 59));
    }

    private boolean isSunday(LocalDateTime dateTime) {
        return dateTime.getDayOfWeek() == DayOfWeek.SUNDAY;
    }


    private static LocalDateTime getNextMonday(LocalDateTime dateTime) {
        return dateTime.with(TemporalAdjusters.next(DayOfWeek.MONDAY));
    }

    public LocalDateTime findTargetDateTime(String timeString) {

        LocalTime targetTime = null;
        if (timeString != null && !timeString.isEmpty()) {
            String format = "HH:mm:ss";
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
                targetTime = LocalTime.parse(timeString, formatter);
            } catch (DateTimeParseException e) {
                log.warn("Format for the configured time {} is not correct. Expected format is {}", timeString, format);
            }
        } else {
            log.info("Invalid value provided for time string {}", timeString);
        }

        // If no valid format was found, return the date with default hour
        if (targetTime == null) {
            int defaultHour = Integer.parseInt(MNPConstants.MNP_PORTIN_MVNO_MVNO_PROV_HOUR);
            log.info("Unable to find a date with configured value {}. taking the default hour as {}", timeString, defaultHour);
            targetTime = LocalTime.of(defaultHour, 0);
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayTargetTime = now.with(targetTime);

        return todayTargetTime.isBefore(now) ? todayTargetTime.plusDays(1) : todayTargetTime;
    }
}
