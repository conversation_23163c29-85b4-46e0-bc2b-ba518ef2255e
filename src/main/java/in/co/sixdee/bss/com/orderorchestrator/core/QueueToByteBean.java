package in.co.sixdee.bss.com.orderorchestrator.core;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.stream.Collectors;

import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.ApplicationQueueEntity;
import lombok.extern.log4j.Log4j2;

@Component
@Log4j2
public class QueueToByteBean {

	@Autowired
	ConcurrentLinkedQueue<OrderCallBack> applicationScopeQueue;


	public ApplicationQueueEntity getQueueEntity() {
		ApplicationQueueEntity entity = new ApplicationQueueEntity();
		List<OrderCallBack> list = null;
		try {
			if (ObjectUtils.isNotEmpty(applicationScopeQueue))
				list = new ArrayList<>(applicationScopeQueue);
			if (ObjectUtils.isNotEmpty(list)) {
				entity.setQueue(getBytes(list));
				entity.setInstanceId("SERVER1");
			}
		} catch (IOException e) {
			log.error(":::::::: Exception occured ::::::: " + e, e);
		}
		return entity;
	}

	public byte[] getBytes(List<OrderCallBack> queue) throws IOException {
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		ObjectOutputStream oos = new ObjectOutputStream(bos);
		oos.writeObject(queue);
		oos.flush();
		return bos.toByteArray();

	}

	@SuppressWarnings("unchecked")
	public List<OrderCallBack> deserialize(byte[] data) {
		ByteArrayInputStream in = new ByteArrayInputStream(data);
		ObjectInputStream is = null;
		try {
			is = new ObjectInputStream(in);
			return (List<OrderCallBack>) is.readObject();
		} catch (ClassNotFoundException | IOException e) {
			log.error(":::::::: Exception occured ::::::: " + e, e);
		}
		return null;
	}

}
