package in.co.sixdee.bss.com.orderorchestrator.config.util.constants;

public enum CallbackAuditStrategy {

	FILE("FILE"), TABLE("TABLE");

	private String name;

	private CallbackAuditStrategy(String name) {
		this.name = name;
	}


	public static final CallbackAuditStrategy nameOf(String name) throws IllegalArgumentException {
		for (CallbackAuditStrategy cs : values()) {
			if (cs.getName().equalsIgnoreCase(name)) {
				return cs;
			}
		}
		throw new IllegalArgumentException("No enum const TransportMethodType with name as " + name);
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}
}
