package in.co.sixdee.bss.com.orderorchestrator.config.util.constants;

/**
 * <AUTHOR>
 **/

public class StatusConstants {

	public enum HttpConstants {

		SUCCESS(200, "Success"),
		
		PARTIAL_CONTENT(206,  "Partial Content"),

		CUSTOM_FIELD_VALIDATION(400, "Failure"),

		NOT_FOUND(404, "Not Found"),

		UNAUTHORIZED_STATUS_CODE(401, null),

		FORBIDDEN_STATUS_CODE(403, null),

		INTERNAL_SERVER_ERROR(500, "System error! Unable to process the request"),

		RANGE_NOT_SATISFIABLE(416, null),

		FAILED_DEPENDENCY(424, null),

		UNPROCESSABLE_ENTITY(422, null);

		private Integer code;

		private String desc;

		private HttpConstants(Integer code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		public Integer getCode() {
			return code;
		}

		public void setCode(Integer code) {
			this.code = code;
		}

		public String getDesc() {
			return desc;
		}

		public void setDesc(String desc) {
			this.desc = desc;
		}

	}

}
