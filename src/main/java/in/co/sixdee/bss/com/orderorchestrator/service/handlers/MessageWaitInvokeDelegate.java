package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.SuspendInvokeHandler;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.RequiredArgsConstructor;

@Component(value = "messageWaitInvokeDelegate")
@RequiredArgsConstructor
public class MessageWaitInvokeDelegate implements JavaDelegate {

    private final SuspendInvokeHandler suspendInvokeHandler;

    private final ProcessDataAccessor processDataAccessor;

    @Override
    public void execute(DelegateExecution execution) throws Exception {
        OrderFlowContext orderFlowContext = processDataAccessor.getOrderFlowContext(execution);
        suspendInvokeHandler.invokeSuspendEvent(orderFlowContext.getOrder().getOrderId(), orderFlowContext.getAttributes().get(GenericConstants.SUB_ORDER_ID));
    }
}