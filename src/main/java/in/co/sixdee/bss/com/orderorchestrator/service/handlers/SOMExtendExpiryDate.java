package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.jolt.CustomOperations.getExpiryDate;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.OrderItem;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SomUpdateEndDate;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Log4j2
@Component(value = "somExtendExpiryDate")
@RequiredArgsConstructor
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SOMExtendExpiryDate extends AbstractDelegate {

	private Order orderPayload = null;

	protected String request = null;

	protected int index = 0;

	protected final ObjectMapper objectMapper;

	@Override
	protected void execute() throws Exception {
		try {
			orderPayload = executionContext.getOrder();
			request = createSOMRequest();
			var callThirdPartyDTO = callThirdParty(request);
			if (callThirdPartyDTO == null) {
				executionContext.setError(true);
				return;
			}
			validateResponse(callThirdPartyDTO);
		} catch (Exception e) {
			log.error("{} In {} .execute Exception occured ", executionContext.getTraceId(), this.getClass().getName(),
					e);
		}
	}

	protected String createSOMRequest() throws Exception {
		var serviceOrder = new SOMServiceOrderDTO();
		serviceOrder.setRegistryId(executionContext.getEntityId());
		serviceOrder.setExternalServiceId(executionContext.getAttributes().get(GenericConstants.SERVICE_ID));
		String subscriptionId = getSubscriptionIdFromRequest(executionContext);
		String expiryDate = getExpiryDateFromEsb();
		String expiryDateForSom = getExpiryDateForSomRequest(expiryDate);
		List<String> somIdsList = compareSubscriptionIdFromSomResponseAndRequest(serviceOrder, subscriptionId);
		if (StringUtils.isNotEmpty(expiryDateForSom) && ObjectUtils.isNotEmpty(somIdsList)) {
			serviceOrder.setUpdateEndDate(createUpdateEndDateForSom(expiryDateForSom, somIdsList));
		}
		return objectMapper.writeValueAsString(serviceOrder);
	}

	private String getExpiryDateForSomRequest(String expiryDate) {
		if (StringUtils.isNotEmpty(expiryDate)) {
			LocalDateTime requestExpiryDate = LocalDateTime.parse(expiryDate,
					DateTimeFormatter.ofPattern(GenericConstants.TIMESTAMP_FORMAT));
			return requestExpiryDate.format(DateTimeFormatter.ofPattern(GenericConstants.SOM_DATE_FORMAT));
		}
		return null;
	}

	private List<SomUpdateEndDate> createUpdateEndDateForSom(String expiryDate, List<String> somIdsList) {
		List<SomUpdateEndDate> endDateList = new ArrayList<>();
		for (String somId : somIdsList) {
			SomUpdateEndDate somUpdateEndDate = new SomUpdateEndDate();
			somUpdateEndDate.setEndDate(expiryDate);
			somUpdateEndDate.setServiceId(somId);
			endDateList.add(somUpdateEndDate);
		}
		return endDateList;
	}

	private List<String> compareSubscriptionIdFromSomResponseAndRequest(SOMServiceOrderDTO serviceOrder,
			String subscriptionId) {
		List<String> somIds = new ArrayList<>();
		List<SOMServiceOrderDTO.SOMService> somFetchServices = getSOMServiceRegistry();
		if (ObjectUtils.isNotEmpty(somFetchServices)) {
			for (SOMService somService : somFetchServices) {
				fetchIdFromSomResponse(somService, subscriptionId, somIds);
			}
		} else {
			log.info("Som fetch registry response is empty...not able to form som  request..");
		}
		return somIds;
	}

	private void fetchIdFromSomResponse(SOMService somService, String subscriptionId, List<String> somIds) {
		List<Characteristic> characteristics = somService.getServiceCharacteristic();
		for (Characteristic data : characteristics) {
			if ("SUBSCRIPTION_ID".equalsIgnoreCase(data.getName()) && StringUtils.isNotEmpty(data.getValue())) {
				if (StringUtils.isNotEmpty(subscriptionId) && subscriptionId.equalsIgnoreCase(data.getValue())
						&& StringUtils.isNotEmpty(somService.getId())) {
					somIds.add(somService.getId());
				}
			}
		}
	}

	private String getSubscriptionIdFromRequest(OrderFlowContext orderFlowContext) {
		Map<String, Object> workflowData = (Map<String, Object>) executionContext.getWorkflowData();
		if (ObjectUtils.isNotEmpty(workflowData) && (ObjectUtils.isNotEmpty(workflowData.get("currentExecution")))) {
			Map<String, Object> currentExecution = (Map<String, Object>) executionContext.getWorkflowData()
					.get("currentExecution");
			if (ObjectUtils.isNotEmpty(currentExecution)) {
				Map<String, Object> executionData = (Map<String, Object>) currentExecution.get("executionData");
				if (ObjectUtils.isNotEmpty(executionData)) {
					List<Map<String, Object>> characteristics = (List<Map<String, Object>>) executionData
							.get("itemCharacteristic");
					if (ObjectUtils.isNotEmpty(characteristics)) {
						for (Map<String, Object> characteristic : characteristics) {
							if ("SUBSCRIPTION_ID".equalsIgnoreCase((String) characteristic.get("name"))
									&& StringUtils.isNotEmpty((String) characteristic.get("value"))) {
								return (String) characteristic.get("value");
							}
						}
					}
				}
			}
		}
		return null;
	}

	private List<SOMServiceOrderDTO.SOMService> getSOMServiceRegistry() {
		List<SOMServiceOrderDTO.SOMService> somFetchServices = new ArrayList<>();
		if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData())
				&& (ObjectUtils.isNotEmpty(executionContext.getWorkflowData().get("currentExecution")))) {
			Map<String, Object> currentExecution = (Map<String, Object>) executionContext.getWorkflowData()
					.get("currentExecution");
			if (currentExecution.containsKey("SOMFetchServiceRegistryResponseAttributes")) {
				somFetchServices = objectMapper.convertValue(
						currentExecution.get("SOMFetchServiceRegistryResponseAttributes"),
						new TypeReference<List<SOMServiceOrderDTO.SOMService>>() {
						});
			}

		}
		if (ObjectUtils.isEmpty(somFetchServices)) {
			log.info(" No services found with the serviceId :: {}",
					executionContext.getAttributes().get(GenericConstants.SERVICE_ID));
			return null;
		}

		return somFetchServices;
	}

	private String getExpiryDateFromEsb() {
		String expiryDate = null;
		if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData())
				&& ObjectUtils.isNotEmpty(executionContext.getWorkflowData().get("currentExecution"))) {

			Map<String, Object> currentExecution = (Map<String, Object>) executionContext.getWorkflowData()
					.get("currentExecution");
			if (currentExecution.containsKey("OCSExtendExpiryDateResponseAttributes")) {
				Map<String, String> expiryDateMap = objectMapper.convertValue(
						currentExecution.get("OCSExtendExpiryDateResponseAttributes"),
						new TypeReference<Map<String, String>>() {
						});
				expiryDate = expiryDateMap.get("endDate");
			}
		}

		return expiryDate;
	}

}
