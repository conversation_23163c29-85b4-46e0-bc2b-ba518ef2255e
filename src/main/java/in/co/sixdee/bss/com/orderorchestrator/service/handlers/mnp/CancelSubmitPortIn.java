package in.co.sixdee.bss.com.orderorchestrator.service.handlers.mnp;


import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.util.MNPServiceUtil;
import in.co.sixdee.bss.com.orderorchestrator.config.util.SOAPMessageUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.XMLUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.XmlRequestBuilder;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.common.util.CommonUtils;
import in.co.sixdee.bss.om.model.dto.order.ProfileRef;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;


@Log4j2
@Component(value = "cancelPortIn")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CancelSubmitPortIn extends AbstractDelegate {


    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    XmlRequestBuilder xmlRequestBuilder;

    @Autowired
    SOAPMessageUtils soapMessageUtils;

    @Autowired
    MNPServiceUtil mnpServiceUtil;
    
    @Autowired
    MNPAttributeService mnpAttributeService;

    @Override
    protected void execute() throws Exception {

    	
      if(StringUtils.isNotEmpty(executionContext.getAttributes().get("actualOrderType")))
      {
    	orderType= executionContext.getAttributes().get("actualOrderType"); 
      }
        String request = xmlRequestBuilder.buildRequest(orderType+"-Rollback-SubmitPortRequest", executionContext,null);
        if (CommonUtils.INSTANCE.validateField(request)) {
            CallThirdPartyDTO callThirdPartyDTO = callThirdParty(request);
            if (callThirdPartyDTO == null) {
                executionContext.setError(true);
                return;
            }
            var response = callThirdPartyDTO.getResponse();
            validateSubmitPortRequestResponse(response);
        } else {
            log.error("Unable to form the tp request, setting error");
            executionContext.getErrorDetail().setCode("COM-001");
            executionContext.getErrorDetail().setMessage("Internal Error: error in forming tp request");
            executionContext.getErrorDetail().setSystem("COM");
            executionContext.setError(true);
        }
        
        workflowDataUpdated=true;

    }


    public void generateRequestTokens() {
        String donorTelcoId = executionContext.getAttributes().get(MNPConstants.DONOR_OPERATOR_CODE);
        String recipientTelcoId = executionContext.getAttributes().get(MNPConstants.RECIPIENT_OPERATOR_CODE);
        executionContext.getAttributes().put(MNPConstants.MNP_REQUEST_ID,
                mnpServiceUtil.generateMNPRequestId(executionContext.getOrder().getOrderId()));
        executionContext.getAttributes().put(MNPConstants.MNP_REFERENCE_ID,
                mnpServiceUtil.generateMNPReferenceId(donorTelcoId, recipientTelcoId));
        findMNPCustomerNameAndId();
    }


    public void findMNPCustomerNameAndId() {
        // if service level mnp name tag is available, take it, else profile level first name+last name
        String mnpCustomerName = null;
        String idTypeCorporate = null;
        CacheTableDataDTO idTypeMnpCorpConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.MNP_ID_TYPE_CORPORATE.toString());
        if (idTypeMnpCorpConfig != null) {
            idTypeCorporate = idTypeMnpCorpConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
        }
        boolean isCorporateId = false;
        ProfileRef profile = executionContext.getOrder().getProfile();
        if (profile!=null && profile.getIndividualIdentification() != null) {
            executionContext.getAttributes().put(MNPConstants.MNP_ID_TYPE, profile.getIndividualIdentification().get(0).getIdentificationType());
            executionContext.getAttributes().put(MNPConstants.MNP_ID_NUMBER, profile.getIndividualIdentification().get(0).getIdentificationNumber());
            isCorporateId = idTypeCorporate != null && idTypeCorporate.equals(profile.getIndividualIdentification().get(0).getIdentificationNumber());
            mnpCustomerName = profile.getGivenName() + " " + profile.getFamilyName();
        }
   
           
            
            if(OrderTypes.INTERIM_NUMBER_PORT_IN.equals(orderType)) {
            	if(executionContext.getOrder().getServiceManagement().getLastName()!=null)
            	mnpCustomerName= executionContext.getOrder().getServiceManagement().getFirstName() + " " + executionContext.getOrder().getServiceManagement().getLastName();
            	else
                	mnpCustomerName= executionContext.getOrder().getServiceManagement().getFirstName();
            }
 
        if (isCorporateId && mnpCustomerName.contains(GenericConstants.OPERATOR_AND)) {
            CacheTableDataDTO mnpReplacingNameConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                    CacheConstants.CacheFields.MNP_CUSTOMER_NAME_REPLACEMENT.toString());
            if (mnpReplacingNameConfig != null) {
                String mnpReplacingName = mnpReplacingNameConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
                mnpCustomerName = mnpCustomerName.replace(GenericConstants.OPERATOR_AND, mnpReplacingName);
            }
        }
        executionContext.getAttributes().put(MNPConstants.MNP_CUSTOMER_NAME, mnpCustomerName);
        executionContext.getAttributes().put(MNPConstants.MNP_TIMESTAMP, LocalDateTime.now().format(DateTimeFormatter.ofPattern(MNPConstants.MNP_DATETIME_FORMAT)));
    }


    private void validateSubmitPortRequestResponse(String response) {
        String errorCode = null;
        String errorMessage = null;


        try {

            if (StringUtils.isNotEmpty(response)) {

                if (response.contains(
                        "transactionStatusCode>SUCCESS")) {
                    log.info("SDP call is success");
                } else if (response.contains("Fault")) {
                    DocumentBuilderFactory dbFactory = XMLUtils.getDocumentBuilderFactory();
                    DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
                    org.w3c.dom.Document doc = null;
                    doc = dBuilder.parse(new ByteArrayInputStream(response.getBytes(StandardCharsets.UTF_8)));

                    if (response.contains("faultcode"))
                        errorCode = doc.getElementsByTagName("faultcode").item(0).getTextContent();
                    if (response.contains("faultstring"))
                        errorMessage = doc.getElementsByTagName("faultstring").item(0).getTextContent();

                    log.info("Got failure response from Singtel SDP");
                    if (errorCode != null) {
                        CacheTableDataDTO errorCodeMappingDTO = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.MNP_ERROR_CODE_MAPPING.toString(), errorCode);
                        if (errorCodeMappingDTO != null) {
                            String errorMessageMapping = errorCodeMappingDTO.getNgTableData().get(CacheConstants.CacheFields.ERROR_DESC.toString());
                            if (StringUtils.isNotEmpty(errorMessageMapping))
                                errorMessage = errorMessageMapping;
                        }
                    }
                    executionContext.getErrorDetail().setSystem("Singtel-SDP");
                    executionContext.setError(true);
                    executionContext.getErrorDetail().setCode(errorCode);
                    executionContext.getErrorDetail().setMessage(errorMessage);
                }

            } else {
                log.info("No response received from SDP. setting error");
                executionContext.getErrorDetail().setSystem("Singtel-SDP");
                executionContext.setError(true);
                executionContext.getErrorDetail().setCode("COM-005");
                executionContext.getErrorDetail().setMessage("No response received from SDP");
            }

        } catch (Exception e) {
            log.error(" Exception occurred in parseSubmitPortRequestResponse ", e);

        }
    }


}
