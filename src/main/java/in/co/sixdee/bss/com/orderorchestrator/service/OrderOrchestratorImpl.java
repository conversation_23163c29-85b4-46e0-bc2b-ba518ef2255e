
package in.co.sixdee.bss.com.orderorchestrator.service;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.RuntimeService;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.config.IOrderOrchestrator;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.ProcessVariableUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.WorkFlowUtil;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants.WorkFlowProcessVariables;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.constants.ApiConstants;
import in.co.sixdee.bss.common.vaidation.RequestValidationException;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor

@Log4j2
public class OrderOrchestratorImpl implements IOrderOrchestrator {

    protected final RuntimeService runtimeService;

    protected final GetDataFromCache cache;

    protected final ObjectMapper objectMapper;

    protected final ProcessVariableUtils processVariableUtils;

    protected final OrderPayloadService orderPayloadService;

    /**
     * This method will process an order orchestration request
     */
    @Override
    public OrderFlowContext processRequest(OrderFlowContext orderFlowContext) {
        String processId;
        try {
            if (orderFlowContext == null)
                throw new CommonException("Un processable entity. orderFlowContext is null");
            var orderType = orderFlowContext.getOrder().getOrderType();
            orderFlowContext.setRequestId(MDC.get(ApiConstants.REQUEST_ID));
            orderFlowContext.getOrder().setRequestId(MDC.get(ApiConstants.REQUEST_ID));
            orderFlowContext.getOrder().setSourceNode(MDC.get(ApiConstants.CHANNEL));
            if (!StringUtils.isNotEmpty(orderFlowContext.getAttributes().get("rollbackProcessId"))) {
                orderPayloadService.createOrderPayloadDetails(orderFlowContext);
            }
            if (GenericConstants.CREATE_SERVICE_ORDER_TYPES.contains(orderType))
                setServiceProvisioningParams(orderFlowContext);
            if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder())
                    && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getApprovalDetails())
                    && "true".equals(orderFlowContext.getOrder().getApprovalDetails().getApprovalReqd())) {
                var variables = processVariableUtils.createApprovalProcessVariables(orderType, orderFlowContext);
                runtimeService.startProcessInstanceByKey("ApprovalWorkflow", orderFlowContext.getOrder().getOrderId(),
                        variables);
            } else {
                if (StringUtils.isNotEmpty(orderFlowContext.getAttributes().get("rollbackProcessId"))) {
                    processId = orderFlowContext.getAttributes().get("rollbackProcessId");
                } else {
                    processId = findProcessId(orderType);
                    if (processId == null) {
                        throw new CommonException(
                                "Un processable entity. no process id mapped for the order type: " + orderType);
                    }
                }
                var variables = processVariableUtils.createProcessVariables(orderType, orderFlowContext);
                variables.put(WorkFlowProcessVariables.PROCESS_ID.toString(), processId);
                log.info("Starting bpmn workflow {}", processId);
                Instant start = Instant.now();
                runtimeService.startProcessInstanceByKey(processId, orderFlowContext.getOrder().getOrderId(),
                        variables);
                log.info("Time taken for staring the process {} is {} ms", processId, Duration.between(start, Instant.now()).toMillis());
            }
        } catch (Exception e) {
            log.error("Exception while processing request {}", e.getMessage());
        }
        return orderFlowContext;
    }

    private void setServiceProvisioningParams(OrderFlowContext orderFlowContext) {
        orderFlowContext.setWorkflowData(new HashMap<>());
        if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile()) && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getAccount())
                && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getAccount().getServiceGroups())) {
            var msisdn = orderFlowContext.getOrder().getProfile().getAccount().getServiceGroups().get(0).getServices()
                    .get(0).getServiceId();
            orderFlowContext.getWorkflowData().put("msisdn", msisdn);

//		if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment())) {
//			var iccId = orderFlowContext.getOrder().getProfile().getAccount().getServiceGroups().stream()
//					.flatMap(group -> group.getServices().stream()
//							.flatMap(service -> service.getCharacteristics().stream()
//									.filter(characteristic -> characteristic.getName().equalsIgnoreCase("ICCID"))
//									.map(characteristic -> characteristic.getValue())))
//					.collect(Collectors.toList());
//			orderFlowContext.getWorkflowData().put("serialNumbers", iccId);
//		}
        }
    }


    /**
     * This method will find the process is mapped to an order type
     */
    protected String findProcessId(String orderType) {
        var orderTypeMapping = cache.getCacheDetailsFromDBMap("COM_ORDER_TYPE_CONFIG", orderType);
        return orderTypeMapping != null ? orderTypeMapping.getNgTableData().get("BPMN_PROCESS_ID") : null;
    }

}
