package in.co.sixdee.bss.com.orderorchestrator.model.mnp.connectService;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Data;

import java.io.Serializable;

@XmlRootElement(name = "ConnectServiceRequest")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class ConnectServiceRequest implements Serializable {

	private static final long serialVersionUID = 1L;
	@XmlElement(name = "connectService")
	private ConnectService connectService;

	
}
