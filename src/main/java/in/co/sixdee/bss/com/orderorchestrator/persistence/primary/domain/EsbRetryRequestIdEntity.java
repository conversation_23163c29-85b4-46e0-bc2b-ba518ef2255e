
package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.beans.factory.annotation.Value;

import java.io.Serializable;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@DynamicUpdate
@Table(name = "COM_ESB_RETRY_REQUEST_ID_ATTRIBUTES")
public class EsbRetryRequestIdEntity implements Serializable {

	private static final long	serialVersionUID	= 1L;

	@Id
	@Column(name = "SUB_ORDER_ID", length = 20, unique = true)
	private Long				subOrderId;
	
	@Column(name = "ORDER_ID", length = 20)
	private Long				orderId;

	@Column(name = "RETRY_REQUEST_ID", length = 100)
	@Value("NULL")
	private String				retryRequestId;

}
