package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.constants.GenericLogConstants;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;


@Log4j2
@Component(value = "somTerminateService")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class SOMTerminateServiceHandler extends AbstractDelegate {

    private Order orderPayload = null;

    protected String request = null;

    protected int index = 0;

    protected final ObjectMapper objectMapper;

    protected final GetDataFromCache cache;

    @Override
    protected void execute() throws Exception {
        try {
            orderPayload = executionContext.getOrder();
            request = createSOMRequest();
            var callThirdPartyDTO = callThirdParty(request);
            if (callThirdPartyDTO == null) {
                executionContext.setError(true);
                return;
            }
            validateResponse(callThirdPartyDTO);
        } catch (Exception e) {
            log.error("{} In {} .execute Exception occurred ", executionContext.getTraceId(), this.getClass().getName(),
                    e);
        }
    }

    protected String createSOMRequest() throws Exception {
        List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
        var serviceOrder = new SOMServiceOrderDTO();
        executionContext.getAttributes().put("callbackCorrelationId", callbackEvent + ":" + executionContext.getAttributes().get(GenericConstants.SUB_ORDER_ID));
        serviceOrder.setExternalId(orderPayload.getOrderId());
        if (orderType.equals(OrderTypes.MNP_PORT_IN))
            serviceOrder.setExternalServiceId(orderPayload.getProfile().getAccount().getServiceGroups().get(0).getServices().get(0).getServiceId());
        else if (orderType.equals(OrderTypes.INTERIM_NUMBER_PORT_IN))
            serviceOrder.setExternalServiceId(orderPayload.getServiceManagement().getNewMsisdn());
        else if (orderType.equals(OrderTypes.MNP_PORT_OUT))
            serviceOrder.setExternalServiceId(executionContext.getAttributes().get(MNPConstants.MNP_SERVICE_ID));
        else
            serviceOrder.setExternalServiceId(orderPayload.getServiceManagement().getServiceId());

        serviceOrder.setPriority("1");
        if (OrderTypes.TERMINATE_SERVICE.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.CONNECTION_MIGRATION.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.CHANGE_SUBSCRIPTION.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.NUMBER_RECYCLE.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.MNP_PORT_IN.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.INTERIM_NUMBER_PORT_IN.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.MNP_PORT_OUT.equalsIgnoreCase(executionContext.getOrder().getOrderType())) {
            serviceOrder.setDescription("Terminate all data services");
            serviceOrder.setCategory("DATA");

        } else if (OrderTypes.SUSPEND_SERVICE.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.LIFECYCLESYNC_TERMINATION.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.LINE_BARRING.equalsIgnoreCase(executionContext.getOrder().getOrderType())) {
            serviceOrder.setDescription("Suspend all GSM HLR services");
            serviceOrder.setCategory("MOBILESERVICE");

        } else if (OrderTypes.RESUME_SERVICE.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.LINE_UNBARRING.equalsIgnoreCase(executionContext.getOrder().getOrderType())) {
            serviceOrder.setDescription("Resume all GSM HLR services");
            serviceOrder.setCategory("MOBILESERVICE");

        }

       
        serviceOrder.setRequestedStartDate(
                StringUtils.isNotEmpty(orderPayload.getRequestedStartDate()) ? orderPayload.getRequestedStartDate()
                        : Instant.now().toString());
        serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
        serviceOrder.setType("ServiceOrder");
        serviceOrder.setRegistryId(fetchEntityId());
        serviceOrderItemList = createServiceOrderItem();
        serviceOrder.setServiceOrderItem(serviceOrderItemList);
        if (ObjectUtils.isNotEmpty(serviceOrder))
            request = objectMapper.writeValueAsString(serviceOrder);
        return request;
    }
	private String fetchEntityId() {
		 if(OrderTypes.MNP_PORT_IN.equalsIgnoreCase(executionContext.getOrder().getOrderType()) 
	        		|| OrderTypes.INTERIM_NUMBER_PORT_IN.equalsIgnoreCase(executionContext.getOrder().getOrderType())
	        		 || OrderTypes.MNP_PORT_OUT.equalsIgnoreCase(executionContext.getOrder().getOrderType())) 
	        	 return executionContext.getAttributes().get("donorEntityId");
	        else 
	        	return executionContext.getEntityId();	        
	}

    private List<ServiceOrderItem> createServiceOrderItem() {
        List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
        ServiceOrderItem serviceOrderItem = new ServiceOrderItem();
        List<SOMService> serviceList = new ArrayList<SOMService>();
        HashMap<String, Object> attributes = new HashMap<>();


        try {
            //var services = getServicesFromSOM();
            List<SOMService> services = null;
            String cfssNames = null;
            if (executionContext.getWorkflowData().containsKey("SOMFetchServiceRegistryResponseAttributes")) {
                services = objectMapper.convertValue(executionContext.getWorkflowData().get("SOMFetchServiceRegistryResponseAttributes"), new TypeReference<List<SOMService>>() {
                });
                if (OrderTypes.LINE_UNBARRING.equalsIgnoreCase(executionContext.getOrder().getOrderType())) {
                    services = services.stream()
                            .filter(service -> StringUtils.equalsIgnoreCase(service.getState(), "inactive"))
                            .collect(Collectors.toList());
                }
                if (OrderTypes.LINE_BARRING.equalsIgnoreCase(executionContext.getOrder().getOrderType())) {
                    var appConfig = cache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG,
                            "CFSS_TO_BE_EXCLUDED_IN_LINEBARRING");
                    if (appConfig != null) {
                        cfssNames = appConfig.getNgTableData().get("CONFIG_VALUE");
                        Set<String> cfssName = new HashSet<String>(Arrays.asList(cfssNames.split(",")));
                        services = services.stream()
                                .filter(serviceSpecifications -> (!cfssName.contains(serviceSpecifications.getName())))
                                .collect(Collectors.toList());
                    }
                }
            }

            if (ObjectUtils.isNotEmpty(services)) {
                for (SOMService service : services) {
                    if (OrderTypes.TERMINATE_SERVICE.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                            || OrderTypes.CONNECTION_MIGRATION.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                            || OrderTypes.CHANGE_SUBSCRIPTION.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                            || OrderTypes.NUMBER_RECYCLE.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                            || OrderTypes.MNP_PORT_IN.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                            || OrderTypes.MNP_PORT_OUT.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                            || OrderTypes.INTERIM_NUMBER_PORT_IN.equalsIgnoreCase(executionContext.getOrder().getOrderType())) {
                        service.setState("terminated");

                    } else if (OrderTypes.SUSPEND_SERVICE.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                            || OrderTypes.LIFECYCLESYNC_TERMINATION.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                            || OrderTypes.LINE_BARRING.equalsIgnoreCase(executionContext.getOrder().getOrderType())) {
                        service.setState("inactive");

                    } else if (OrderTypes.RESUME_SERVICE.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                            || OrderTypes.LINE_UNBARRING.equalsIgnoreCase(executionContext.getOrder().getOrderType())) {
                        service.setState("active");
                    }

                    String somServiceName = service.getName();
                    Object somServiceId = service.getId();
                    attributes.put(somServiceName, somServiceId);

                    service.setType(service.getCategory());
                    //service.getServiceSpecification().setName(service.getName());
                    service.getServiceSpecification().setAtType("MobileService");
                    service.getServiceSpecification().setVersion("1");
                    handleServiceCharacteristics(service, executionContext.getOrder().getOrderType());
                    service.setName(null);
                    service.setCategory(null);
                    service.setHref(null);
                    serviceList.add(service);
                }

                if (MapUtils.isNotEmpty(attributes)) {
                    executionContext.getObjectAttributes().put("SOMServiceParameters", attributes);
                }

            }
            if (ObjectUtils.isNotEmpty(serviceList)) {
                for (SOMService service : serviceList) {
                    serviceOrderItem = setServiceOrderItem(service);
                    serviceOrderItemList.add(serviceOrderItem);
                }
            }
        } catch (Exception e) {
            log.error(GenericLogConstants.TAG_APP + " | Exception occured in createServiceOrderItem ", e);
        }

        return serviceOrderItemList;
    }


    private void handleServiceCharacteristics(SOMService somService, String orderType) {
        if ("CFSS_LMP_DB_UPDATE".equalsIgnoreCase(somService.getName())) {
            if (OrderTypes.MNP_PORT_OUT.equalsIgnoreCase(orderType)) {
                handlePortOutCharacteristics(somService.getServiceCharacteristic());
            } else {
                for (Characteristic characteristic : somService.getServiceCharacteristic()) {
                    if (StringUtils.equalsAnyIgnoreCase(characteristic.getName(), "ownerTelco", "sourceTelco", "destTelco", "portIdentifier")) {
                        characteristic.setValue("");
                    }
                }
            }
        } else {
            somService.setServiceCharacteristic(null);
        }
    }

    private void handlePortOutCharacteristics(List<Characteristic> serviceCharacteristics) {
        if (serviceCharacteristics == null)
            return;
        String ownerTelco = null;
        String destTelco = null;
        for (Characteristic characteristic : serviceCharacteristics) {
            if ("portIdentifier".equalsIgnoreCase(characteristic.getName())) {
                characteristic.setValue("PORT_OUT");
            } else if ("sourceTelco".equalsIgnoreCase(characteristic.getName())) {
                characteristic.setValue("Singtel");
            } else if ("ownerTelco".equalsIgnoreCase(characteristic.getName())) {
                characteristic.setValue(ownerTelco=findMNOByMvno(executionContext.getAttributes().get(MNPConstants.OWNER_OPERATOR_NAME)));
            } else if ("destTelco".equalsIgnoreCase(characteristic.getName())) {
                characteristic.setValue(destTelco=findMNOByMvno(executionContext.getAttributes().get(MNPConstants.RECIPIENT_OPERATOR_NAME)));
            }
        }
        
        checkForSameOwnerAndDestTelco(ownerTelco, destTelco, serviceCharacteristics);
    }

    private void checkForSameOwnerAndDestTelco(String ownerTelco, String destTelco, List<Characteristic> serviceCharacteristics) {

        String commonValue = "Singtel";
        if (commonValue.equalsIgnoreCase(ownerTelco) && commonValue.equalsIgnoreCase(destTelco)) {
            for (Characteristic characteristic : serviceCharacteristics) {
                if ("sourceTelco".equalsIgnoreCase(characteristic.getName()) || "ownerTelco".equalsIgnoreCase(characteristic.getName()) 
                		|| "destTelco".equalsIgnoreCase(characteristic.getName())|| "portIdentifier".equalsIgnoreCase(characteristic.getName())){
                    characteristic.setValue("");
                }
            }
        }
    }

    private String findMNOByMvno(String mvno) {
        //Finding MNO name by MVNO
        String mno = null;
        CacheTableDataDTO operatorConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_MVNO_MNO_MAPPING_CONFIG.toString(), mvno);
        if (operatorConfig != null) {
            mno = operatorConfig.getNgTableData().get(CacheConstants.CacheFields.MNO.toString());
            log.info("MNO name {} found from configuration table {} ", mno, CacheConstants.CacheKeys.COM_MVNO_MNO_MAPPING_CONFIG.toString());
        }
        return mno;
    }

    private ServiceOrderItem setServiceOrderItem(SOMService service) {

        var serviceOrderItem = new ServiceOrderItem();
        index = index + 1;
        serviceOrderItem.setId(String.valueOf(index));
        if (OrderTypes.TERMINATE_SERVICE.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.CONNECTION_MIGRATION.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.CHANGE_SUBSCRIPTION.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.NUMBER_RECYCLE.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.MNP_PORT_IN.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.INTERIM_NUMBER_PORT_IN.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                || OrderTypes.MNP_PORT_OUT.equalsIgnoreCase(executionContext.getOrder().getOrderType())
        ) {
            serviceOrderItem.setAction("delete");

        } else {
            serviceOrderItem.setAction("modify");

        }
        serviceOrderItem.setType("ServiceOrderItem");
        serviceOrderItem.setService(service);
        return serviceOrderItem;
    }

}
