package in.co.sixdee.bss.com.orderorchestrator.config.util;

import lombok.extern.log4j.Log4j2;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

@Log4j2
public class XMLUtils {

    private XMLUtils() {
    }

    public static DocumentBuilderFactory getDocumentBuilderFactory() {
        DocumentBuilderFactory factory = null;
        try {
            factory = DocumentBuilderFactory.newInstance();
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            factory.setNamespaceAware(true); // Enable namespace support
            factory.setXIncludeAware(false);
            factory.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
        } catch (ParserConfigurationException e) {
            log.error("Exception occurred while creating DocumentBuilderFactory instance", e);
        }
        return factory;
    }
}
