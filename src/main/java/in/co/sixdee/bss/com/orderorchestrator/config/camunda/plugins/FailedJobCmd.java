package in.co.sixdee.bss.com.orderorchestrator.config.camunda.plugins;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.WorkflowTaskFailedException;
import org.camunda.bpm.engine.impl.cmd.DefaultJobRetryCmd;
import org.camunda.bpm.engine.impl.cmd.JobRetryCmd;
import org.camunda.bpm.engine.impl.interceptor.CommandContext;
import org.camunda.bpm.engine.impl.persistence.entity.JobEntity;

public class FailedJobCmd extends DefaultJobRetryCmd {

	public FailedJobCmd(String jobId, Throwable exception) {
		super(jobId, exception);
	}

	@Override
	public Object execute(CommandContext commandContext) {
		if (super.exception instanceof WorkflowTaskFailedException) {
			new SetJobRetriesToZeroCmd(jobId, exception).execute(commandContext);
			return null;
		} else {
			return super.execute(commandContext);
		}
	}

	public static class SetJobRetriesToZeroCmd extends JobRetryCmd {

		public SetJobRetriesToZeroCmd(String jobId, Throwable exception) {
			super(jobId, exception);
		}

		public Object execute(CommandContext commandContext) {
			JobEntity job = getJob();

			job.unlock();
			logException(job);
			job.setRetries(0);
			notifyAcquisition(commandContext);

			return null;
		}

	}
}
