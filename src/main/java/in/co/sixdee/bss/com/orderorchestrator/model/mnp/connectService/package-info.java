/**
 * 
 */
/**
 * <AUTHOR>
 *
 */
@XmlSchema(namespace = "http://tempuri.org/",elementFormDefault=XmlNsForm.QUALIFIED)



/*@XmlSchema(xmlns = {
                @XmlNs(prefix = "ns2", namespaceURI = "http://schemas.microsoft.com/2003/10/Serialization/Arrays")
                //,@XmlNs(prefix = "", namespaceURI = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
                ,@XmlNs(prefix = "ns3", namespaceURI = "http://tempuri.org/")
        },elementFormDefault= XmlNsForm.QUALIFIED
)*/
package in.co.sixdee.bss.com.orderorchestrator.model.mnp.connectService;

import jakarta.xml.bind.annotation.XmlNsForm;
import jakarta.xml.bind.annotation.XmlSchema;

