package in.co.sixdee.bss.com.orderorchestrator.config.notification;


import in.co.sixdee.bss.com.orderorchestrator.config.util.Queue;
import in.co.sixdee.bss.common.core.AppInstanceIdManager;
import in.co.sixdee.bss.common.util.ApplicationContextUtils;
import in.co.sixdee.bss.common.util.ApplicationProcessContext;
import in.co.sixdee.bss.om.model.dto.notification.NotificationDTO;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class NotificationThread extends Thread {

	protected AppInstanceIdManager	appInstanceSequence	= ApplicationContextUtils.getBean(AppInstanceIdManager.class);

	private Queue<NotificationDTO>	notificationQueue;
	private String					name;
	private boolean					flag				= true;

	public NotificationThread(Queue<NotificationDTO> notificationQueue, int threadNo, String name) {
		super(name + "_" + threadNo);
		this.name = name + "_" + threadNo;
		this.notificationQueue = notificationQueue;
	}

	@Override
	public void run() {
		try {
			NotificationDTO notificationDTO = null;
			NotificationBO notificationBO = null;

			while (flag) {
				try {
					notificationBO = new NotificationBO();
					notificationDTO = notificationQueue.dequeue();
					var processContext = new ApplicationProcessContext(appInstanceSequence.getInstanceId(),
							notificationDTO.getTraceId(), notificationDTO.getRequestId(),notificationDTO.getChannel(),notificationDTO.getUsername(),notificationDTO.getEntityId());
					processContext.setMdc();
					notificationBO.sendNotification(notificationDTO);

				} catch (Exception e) {
					log.error(" ::Exception ::" + e.getMessage(), e);
				} finally {
					log.info("Notification Thread : {} process completed", name);
				}
			}
		} catch (Exception ee) {
			log.error(" Exception : " + ee.getMessage(), ee);
		}
	}

	public void shutdown() {
		flag = false;
	}

}
