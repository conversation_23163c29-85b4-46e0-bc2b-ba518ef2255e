package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Log4j2
@Component(value = "portOutDataValidator")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PortOutDataValidator extends AbstractDelegate {

    @Override
    protected void execute() throws Exception {

        if (StringUtils.isNotEmpty(executionContext.getAttributes().get("contractValidationFailed"))) {

            log.info("contract plan is present in BSS..customer is not eligible for port out");
            setError("645", "There is Active Contract present for the service");

        } else if (StringUtils.isNotEmpty(executionContext.getAttributes().get("idValidationFailed"))) {

            log.info("Incoming identification id is not matching with id present in BSS..");
            setError("643", "Incoming identification id is not matching with identification id in the system");

        } else if (StringUtils.isNotEmpty(executionContext.getAttributes().get("serviceStatusValidationFailed"))) {

            log.info("Service status is not eligible for port out..");
            setError("646", "Service is not in active state");

        }

    }

    private void setError(String errorCode, String errorMessage) {
        executionContext.setError(true);
        executionContext.getErrorDetail().setSystem("Billing");
        executionContext.getErrorDetail().setCode(errorCode);
        executionContext.getErrorDetail().setMessage(errorMessage);
    }
}
