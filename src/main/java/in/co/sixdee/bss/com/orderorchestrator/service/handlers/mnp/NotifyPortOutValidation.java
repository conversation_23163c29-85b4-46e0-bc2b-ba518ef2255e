package in.co.sixdee.bss.com.orderorchestrator.service.handlers.mnp;


import in.co.sixdee.bss.com.orderorchestrator.config.util.XMLUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.XmlRequestBuilder;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.ErrorConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.common.util.CommonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.data.repository.query.parser.Part;
import org.springframework.stereotype.Component;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;


@Log4j2
@Component(value = "notifyPortOutValidation")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class NotifyPortOutValidation extends AbstractDelegate {

    private final XmlRequestBuilder xmlRequestBuilder;
    //private final GetDataFromCache cache;
    private static final Map<String, String> VALIDATION_KEYS = Map.of(
            "contractValidationFailed", "_contractValidationFailed",
            "idValidationFailed", "_idValidationFailed",
            "serviceStatusValidationFailed", "_serviceStatusValidationFailed"
    );


    @Override
    protected void execute() throws Exception {

        String request = getRequestBasedOnValidationError();
        log.info("request formed for calling third party:::"+request);
        if (StringUtils.isEmpty(request)) {
            request = xmlRequestBuilder.buildRequest(orderType + "_" + reqSpecKey, executionContext, null);
        }
        if (CommonUtils.INSTANCE.validateField(request)) {
            CallThirdPartyDTO callThirdPartyDTO = callThirdParty(request);
            if (callThirdPartyDTO == null) {
                executionContext.setError(true);
                return;
            }
            var response = callThirdPartyDTO.getResponse();
            validateSubmitPortRequestResponse(response);
        } else {
            log.error("Unable to form the tp request, setting error");
            executionContext.getErrorDetail().setCode(ErrorConstants.ERROR_CODE_REQUEST_FORMING_ERROR);
            executionContext.getErrorDetail().setMessage("Internal Error: error in forming tp request");
            executionContext.getErrorDetail().setSystem(ErrorConstants.ERROR_SYSTEM_COM);
            executionContext.setError(true);
        }

        workflowDataUpdated = true;

    }


    public String getRequestBasedOnValidationError() {
        for (Map.Entry<String, String> entry : VALIDATION_KEYS.entrySet()) {
            String attr = executionContext.getAttributes().get(entry.getKey());
            log.info("attr in the class {}",attr);
            if (StringUtils.isNotEmpty(attr)) {
                log.info("Taking request based on {}", entry.getKey());
                return xmlRequestBuilder.buildRequest(orderType + entry.getValue(), executionContext, null);
            }
        }
        return null;
    }


    private void validateSubmitPortRequestResponse(String response) {

            if (StringUtils.isEmpty(response)) {
                handleEmptyResponse();
                return;
            }
            if (response.contains("transactionStatusCode>SUCCESS")) {
                log.info("SDP call is successful");
                return;
            }

            if (response.contains("Fault")) {
                handleFaultResponse(response);
            }
    }

    private void handleEmptyResponse() {
        log.warn("No response received from SDP. Setting error.");
        setExecutionError(ErrorConstants.ERROR_CODE_EMPTY_RESPONSE,
                "No response received from SDP");
    }

    private void handleFaultResponse(String response) {
        log.info("Got failure response from Singtel SDP");

        org.w3c.dom.Document document = parseXml(response);
        if (document == null) {
            setExecutionError(ErrorConstants.ERROR_CODE_RESPONSE_PARSING_ERROR,
                    "Failed to parse SDP response XML");
            return;
        }

        String errorCode = getTagValue(document, "faultcode");
        String errorMessage = getTagValue(document, "faultstring");

        errorMessage = getMappedErrorMessage(errorCode, errorMessage);

        setExecutionError(errorCode, errorMessage);
    }

    private String getMappedErrorMessage(String errorCode, String fallbackMessage) {
        if (StringUtils.isEmpty(errorCode)) return fallbackMessage;

        CacheTableDataDTO errorCodeMappingDTO = cache.getCacheDetailsFromDBMap(
                CacheConstants.CacheKeys.MNP_ERROR_CODE_MAPPING.toString(), errorCode);

        if (errorCodeMappingDTO != null) {
            String mappedMessage = errorCodeMappingDTO.getNgTableData()
                    .get(CacheConstants.CacheFields.ERROR_DESC.toString());
            if (StringUtils.isNotEmpty(mappedMessage)) {
                return mappedMessage;
            }
        }

        return fallbackMessage;
    }


    private org.w3c.dom.Document parseXml(String xml) {
        try {
            DocumentBuilderFactory dbFactory = XMLUtils.getDocumentBuilderFactory();
            DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
            return dBuilder.parse(new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            log.error("Failed to parse XML", e);
            return null;
        }
    }

    private String getTagValue(org.w3c.dom.Document doc, String tagName) {
        try {
            return doc.getElementsByTagName(tagName).item(0).getTextContent();
        } catch (Exception e) {
            log.warn("Tag [{}] not found in document", tagName, e);
            return null;
        }
    }

    private void setExecutionError(String errorCode, String message) {
        executionContext.setError(true);
        executionContext.getErrorDetail().setSystem(ErrorConstants.ERROR_SYSTEM_SINGTEL_SDP);
        executionContext.getErrorDetail().setCode(errorCode);
        executionContext.getErrorDetail().setMessage(message);
    }
}