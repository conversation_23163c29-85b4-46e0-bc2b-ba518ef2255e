package in.co.sixdee.bss.com.orderorchestrator.core;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.CallbackProcessingException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderCallBackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.jboss.logging.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.RejectedExecutionException;

//@Component
//@EnableScheduling
//@ConditionalOnProperty(value = GenericConstants.CALLBACK_RETRY_SCHEDULER_ENABLE)
@Log4j2
@RequiredArgsConstructor
public class RetryQueueWatcher {


	private final ConcurrentLinkedQueue<OrderCallBack> callbackJobQueue;

	private final OrderCallBackService orderCallBackService;


	@Value("${callback.retry.queue-watcher.min-queue-size-to-poll:1}")
	int minQueueSizeToPoll;

   // @Scheduled(fixedDelayString = "${callback.retry.queue-watcher.polling-interval}", initialDelayString = "${callback.retry.queue-watcher.startup-delay}")
    public void lifeCycle() {
        int queueSize = callbackJobQueue.size();
        if (queueSize >= minQueueSizeToPoll) {
            log.info("Callback queue size reached to the polling threshold {}. processing the callback requests. current queue size {}", minQueueSizeToPoll, callbackJobQueue.size());
            for (int i = 0; i < queueSize; i++) {
                OrderCallBack callback = null;
                try {
                    callback = callbackJobQueue.poll();
                    processCallback(callback);
                } catch (CallbackProcessingException ex) {
                    log.error("not able to find the data again avoiding the dequeue :::: and continue for the next element ::::", ex);
                    orderCallBackService.addToRetryQueue(callback);
                } catch (RejectedExecutionException e) {
                    log.info("Job rejected by thread pool executor as the queue is busy. this job will be executed in the next polling");
                    callbackJobQueue.offer(callback);
                } catch (Exception e) {
                    log.error("not able to find the data again avoiding the dequeue :::: and continue for the next element ::::", e);
                    callbackJobQueue.offer(callback);
                }
            }
        } else {
            log.debug("Queue size not reached to the polling threshold. Current queue size {}, polling threshold {}", callbackJobQueue.size(), minQueueSizeToPoll);
        }
    }

	//@Async("callbackRetryThreadPoolTaskExecutor")
	public void processCallback(OrderCallBack callback) {
		MDC.put(GenericConstants.ORDER_ID, callback.getOrderId());
		MDC.put("traceId", callback.getOrderId());
		orderCallBackService.signalProcessContinuation(callback);
	}

}
