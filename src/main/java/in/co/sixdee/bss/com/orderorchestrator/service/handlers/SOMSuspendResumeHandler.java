package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.constants.GenericLogConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Log4j2
@Component(value = "somSuspendResumeHandler")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class SOMSuspendResumeHandler extends AbstractDelegate {

	private Order orderPayload = null;

	protected String request = null;

	protected int index = 0;

	protected final ObjectMapper objectMapper;

	@Override
	protected void execute() throws Exception {
		try {
			orderPayload = executionContext.getOrder();

			request = createSOMRequest();
			var callThirdPartyDTO = callThirdParty(request);
			if (callThirdPartyDTO == null) {
				executionContext.setError(true);
				return;
			}
			validateResponse(callThirdPartyDTO);
		} catch (Exception e) {
			log.error("{} In {} .execute Exception occurred ", executionContext.getTraceId(), this.getClass().getName(),
					e);
		}
	}

	protected String createSOMRequest() throws Exception {
		var serviceOrder = new SOMServiceOrderDTO();
		executionContext.getAttributes().put("callbackCorrelationId", callbackEvent + ":" + executionContext.getAttributes().get(GenericConstants.SUB_ORDER_ID));
		serviceOrder.setExternalId(orderPayload.getOrderId());
		serviceOrder.setDescription(orderPayload.getDescription());
		serviceOrder.setPriority("1");
		serviceOrder.setCategory("MobileService");
		serviceOrder.setRequestedStartDate(
				StringUtils.isNotEmpty(orderPayload.getRequestedStartDate()) ? orderPayload.getRequestedStartDate()
						: Instant.now().toString());
		serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
		serviceOrder.setType("ServiceOrder");
		var serviceOrderItemList = createServiceOrderItem();
		serviceOrder.setExternalServiceId(executionContext.getAttributes().get(GenericConstants.SERVICE_ID));
		serviceOrder.setServiceOrderItem(serviceOrderItemList);
		request = objectMapper.writeValueAsString(serviceOrder);
		return request;
	}

	private List<ServiceOrderItem> createServiceOrderItem() {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		ServiceOrderItem serviceOrderItem = new ServiceOrderItem();
		List<SOMService> serviceList = new ArrayList<SOMService>();
		try {
			var services = getServicesFromSOM();
			var subscriptionId = getSubscriptionId();


			if (ObjectUtils.isNotEmpty(services)) {
				for (SOMService service : services) {
					//CFS addons matching the subs id of current Execution shud be cancelled
					//if ("CFS".equalsIgnoreCase(service.getCategory())&& ObjectUtils.isNotEmpty(service.getServiceCharacteristic())) {
					if (service.getServiceCharacteristic() != null) {
						for (Characteristic serviceCharacteristic : service.getServiceCharacteristic()) {

							if (StringUtils.isNotEmpty(serviceCharacteristic.getName())
									&& ObjectUtils.isNotEmpty(serviceCharacteristic.getValue()) && StringUtils
									.equalsIgnoreCase("SUBSCRIPTION_ID", serviceCharacteristic.getName())) {

								String somSubscriptionId = serviceCharacteristic.getValue();
								if (StringUtils.isNoneEmpty(subscriptionId)
										&& somSubscriptionId.equalsIgnoreCase(subscriptionId)) {
									if (StringUtils.isNotEmpty(executionContext.getOrder().getOrderType())
											&& StringUtils.equalsAnyIgnoreCase(
											executionContext.getOrder().getOrderType(),
											OrderTypes.SUSPEND_SUBSCRIPTION)) {
										service.setState("inactive");
									} else {
										service.setState("active");
									}

									service.setType(service.getCategory());
									service.getServiceSpecification().setName(service.getName());
									service.getServiceSpecification().setVersion("1");
									service.setName(null);
									service.setCategory(null);
									service.setHref(null);
      								service.setServiceCharacteristic(null);
									serviceList.add(service);
								}

							}
						}
					}
				}
			}
			if (ObjectUtils.isNotEmpty(serviceList)) {
				for (SOMService service : serviceList) {
					serviceOrderItem = setServiceOrderItem(service);
					serviceOrderItemList.add(serviceOrderItem);
				}
			}
		} catch (Exception e) {
			log.error("Exception occurred in createServiceOrderItem ", e);

		}

		return serviceOrderItemList;

	}

	@SuppressWarnings("unused")
	private List<SOMService> getServicesFromSOM() {
		Gson gson = null;
		List<SOMService> somResponse = null;
		try {
			gson = new Gson();
			var workFlowData = in.co.sixdee.bss.common.util.JsonUtils.marshall(executionContext.getWorkflowData(),
					null);
			if (ObjectUtils.isNotEmpty(workFlowData)) {
				somResponse = parseSOMResponse(workFlowData, executionContext);
			}
		} catch (Exception e) {
			log.error(GenericLogConstants.TAG_APP
					+ " :::::  Exception occured in parse UPCResponse execute method  :::::", e);
		}
		return somResponse;
	}

	private List<SOMService> parseSOMResponse(String workFlowData, OrderFlowContext orderFlowContext) {
		List<SOMService> serviceList = null;
		JsonObject responseReceived = null;
		JsonArray parseSOMResponse = null;
		Gson gson = null;

		try {
			gson = new Gson();
			if (ObjectUtils.isNotEmpty(workFlowData) && workFlowData.contains("SOMFetchServiceRegistryResponseAttributes")) {
				responseReceived = gson.fromJson(workFlowData, JsonObject.class);
				parseSOMResponse = responseReceived.getAsJsonObject("currentExecution").getAsJsonArray("SOMFetchServiceRegistryResponseAttributes");
				serviceList = gson.fromJson(parseSOMResponse, new TypeToken<List<SOMService>>() {
				}.getType());
			}
		} catch (Exception e) {
			log.error(GenericLogConstants.TAG_APP + " | Exception occured in parseSOMResponse ", e);

		}
		return serviceList;
	}


	private String getSubscriptionId() {
		Gson gson = null;
		List<Characteristic> characteristics = null;
		JsonObject wfData = null;
		JsonObject executionData = null;
		JsonArray itemCharacteristic = null;
		String subscriptionIdToBeCancelled = null;

		try {
			gson = new Gson();
			var workFlowData = in.co.sixdee.bss.common.util.JsonUtils.marshall(executionContext.getWorkflowData(),
					null);
			if (ObjectUtils.isNotEmpty(workFlowData)) {
				if (ObjectUtils.isNotEmpty(workFlowData)) {

					if (ObjectUtils.isNotEmpty(workFlowData) && workFlowData.contains("executionData")) {
						wfData = gson.fromJson(workFlowData, JsonObject.class);
						executionData = wfData.getAsJsonObject("currentExecution").getAsJsonObject("executionData");
						if (ObjectUtils.isNotEmpty(executionData)) {
							if (ObjectUtils.isNotEmpty(executionData.getAsJsonArray("itemCharacteristic"))) {
								itemCharacteristic = executionData.getAsJsonArray("itemCharacteristic");
								characteristics = gson.fromJson(itemCharacteristic, new TypeToken<List<Characteristic>>() {
								}.getType());
								if (ObjectUtils.isNotEmpty(characteristics)) {
									for (Characteristic charac : characteristics) {
										if ("SUBSCRIPTION_ID".equalsIgnoreCase(charac.getName())) {
											subscriptionIdToBeCancelled = String.valueOf(charac.getValue());
										}
									}
								}
							}

							if (ObjectUtils.isNotEmpty(executionData.getAsJsonObject("service")) && ObjectUtils.isNotEmpty(executionData.getAsJsonObject("service").get("id").getAsString())) {
								String serviceId = executionData.getAsJsonObject("service").get("id").getAsString();
								executionContext.getAttributes().put(GenericConstants.SERVICE_ID, serviceId);
							}
						}
					}
				}
			}
		} catch (Exception e) {
			log.error(GenericLogConstants.TAG_APP
					+ " :::::  Exception occured in parse getAddonsTobeCancelled execute method  :::::", e);
		}
		return subscriptionIdToBeCancelled;
	}


	private ServiceOrderItem setServiceOrderItem(SOMService service) {

		var serviceOrderItem = new ServiceOrderItem();
		index = index + 1;
		serviceOrderItem.setId(String.valueOf(index));
		serviceOrderItem.setAction("modify");
		serviceOrderItem.setType("ServiceOrderItem");
		serviceOrderItem.setService(service);
		return serviceOrderItem;
	}

}
