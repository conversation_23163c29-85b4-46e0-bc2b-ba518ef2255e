package in.co.sixdee.bss.com.orderorchestrator.persistence.primary;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.function.Supplier;

@Service
public class TransactionHandler {

	@Transactional(propagation = Propagation.REQUIRED)
	public <T> T runInTransaction(Supplier<T> supplier) {
		return supplier.get();
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public <T> T runInNewTransaction(Supplier<T> supplier) {
		return supplier.get();
	}
}
