
package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.order.*;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

@Log4j2
@Component(value = "somAddSubscription")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class SOMAddSubscriptionHandler extends AbstractDelegate {

	protected String planId = null;

	protected String addonServiceId = null;

	private Order orderPayload = null;

	protected String request = null;

	protected int index = 0;

	protected final ObjectMapper objectMapper;
	
	protected String connectionType = null;
	
    private String expiryDate;
    private String startDate;
	protected String upcPlanId = null;

	@Override
	protected void execute() throws Exception {
		try {
			orderPayload = executionContext.getOrder();
			request = createSOMRequest();
			var callThirdPartyDTO = callThirdParty(request);
			validateResponse(callThirdPartyDTO);
		} catch (Exception e) {
			log.error("{} In {} .execute Exception occured ", executionContext.getTraceId(), this.getClass().getName(), e);
		}
	}
	protected String createSOMRequest() throws Exception {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		var serviceOrder = new SOMServiceOrderDTO();
		serviceOrder.setExternalId(executionContext.getOrder().getOrderId());
		serviceOrder.setRegistryId(executionContext.getEntityId());
		serviceOrder.setDescription(
				StringUtils.isNotEmpty(orderPayload.getDescription()) ? orderPayload.getDescription()
						: "Add Service Order Items");
		serviceOrder.setRequestedStartDate(
				StringUtils.isNotEmpty(orderPayload.getRequestedStartDate()) ? orderPayload.getRequestedStartDate()
						: Instant.now().toString());
		serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
		serviceOrder.setType("ServiceOrder");
		executionContext.getAttributes().put("callbackCorrelationId",
				callbackEvent + ":" + executionContext.getAttributes().get(GenericConstants.SUB_ORDER_ID));
		if (executionContext.getEnrichmentResults().containsKey("serviceInfo")) {
			var serviceInfo = objectMapper.convertValue(executionContext.getEnrichmentResults().get("serviceInfo"),
					LinkedHashMap.class);
			connectionType = serviceInfo.get("chargingPattern").toString();
		}
		serviceOrderItemList = createServiceOrderItemBasedOnEnrichedData();
		serviceOrder.setExternalServiceId(addonServiceId);
		serviceOrder.setServiceOrderItem(serviceOrderItemList);
		request = objectMapper.writeValueAsString(serviceOrder);
		return request;
	}

	public List<ServiceOrderItem> createServiceOrderItemBasedOnEnrichedData() {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		List<ProductSpecificationRef> productSpecification = new ArrayList<ProductSpecificationRef>();
		List<OrderItem> orderItem = new ArrayList<>();
		try {
			// only cfs needs to be sent to som during addsubs.
			// iterate over each orderItem[] and get the productSpecifications.
			orderItem.add(getorderItemFromExecutionData());
			addonServiceId = orderItem.get(0).getService().getId();// multiple plans can come but under same service
			orderItem.forEach(item -> {
				if (ObjectUtils.isNotEmpty(item.getProduct())
						&& ObjectUtils.isNotEmpty(item.getProduct().getProductSpecification())) {
					planId = item.getProductOffering().getId();
					expiryDate= item.getProductOffering().getSubscription().getExpiryDate();
					upcPlanId=item.getProductOffering().getSubscription().getUpcPlanId();

					var cfss = item.getProduct().getProductSpecification().getCfss();
					if (ObjectUtils.isNotEmpty(cfss)) {
					cfss.forEach(cfs -> {
						if (ObjectUtils.isNotEmpty(cfs.getSkipSom()) && Boolean.parseBoolean(cfs.getSkipSom())) {

						} else {
							var serviceOrderItem = createServiceOrderItem(cfs, "CFS");
							serviceOrderItemList.add(serviceOrderItem);
						}

					});
					}
				}

			});

			return serviceOrderItemList;

		} catch (Exception e) {
			log.error(" :::::  Exception occurred in parse UPCResponse execute method  :::::", e);
		}
		return null;

	}
	 protected OrderItem getorderItemFromExecutionData() {
		 OrderItem orderItem = null;
	        LinkedHashMap<String, Object> currentExecution = (LinkedHashMap<String, Object>) executionContext.getWorkflowData().get("currentExecution");
	        if (currentExecution != null) {
	            try {
	            	orderItem = objectMapper.readValue(objectMapper.writeValueAsString(currentExecution.get("executionData")), OrderItem.class);
	            } catch (Exception e) {
	                log.error("Exception occurred while getting service", e);
	            }
	        }
	        return orderItem;
	    }

	protected ServiceOrderItem createServiceOrderItem(CFSRef cfss, String type) {
		var serviceOrderItem = new ServiceOrderItem();
		index = index + 1;
		serviceOrderItem.setId(String.valueOf(index));
		serviceOrderItem.setAction("add_addon");
		serviceOrderItem.setType("ServiceOrderItem");
		var serviceItem = createServiceItem(cfss, type);
		serviceOrderItem.setService(serviceItem);
		return serviceOrderItem;
	}

	protected SOMService createServiceItem(CFSRef cfss, String type) {
		var serviceItem = new SOMService();
		serviceItem.setState("active");
		serviceItem.setType(type);
		var specification = new ProductSpecification();
		specification.setName(cfss.getName());
//		specification.setType(type);
		specification.setId(cfss.getId());
		serviceItem.setServiceSpecification(specification);
		var characteristics = createServiceCharacteristic(cfss.getCharacteristics(),cfss.getName());
		if (type.equalsIgnoreCase("CFS"))
			setSubscriptionId(characteristics);
		serviceItem.setServiceCharacteristic(characteristics);
		return serviceItem;
	}

	protected void setSubscriptionId(List<Characteristic> characteristics) {
		HashMap<String, String> subscriptionIdMap = null;
		if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData().get("subscriptionIdMap"))) {
			subscriptionIdMap = (HashMap<String, String>) executionContext.getWorkflowData().get("subscriptionIdMap");
		} else if (ObjectUtils.isNotEmpty(executionContext.getEnrichmentResults().get("subscriptionIdMap"))) {
			subscriptionIdMap = (HashMap<String, String>) executionContext.getEnrichmentResults().get("subscriptionIdMap");
		}
			if ((subscriptionIdMap != null && !subscriptionIdMap.isEmpty()) && subscriptionIdMap.get(planId) != null) {
				var characteristic = new Characteristic();
				characteristic.setName("SUBSCRIPTION_ID");
				characteristic.setValueType("String");
				characteristic.setValue(subscriptionIdMap.get(planId));
				characteristics.add(characteristic);
			}


	}

	protected List<Characteristic> createServiceCharacteristic(List<CFSCharacteristicRef> characteristics, String cfsName) {
		//POLICY_NAME,START_DATE,END_DATE
		
		boolean isPcrfAddon=findCfsIsPCRF(cfsName);
        
        
		var characteristicList = new ArrayList<Characteristic>();
		if (ObjectUtils.isNotEmpty(characteristics))
			characteristics.forEach(cfsCharacteristic -> {
				var characteristic = new Characteristic();
				if (!"SUBSCRIPTION_ID".equals(cfsCharacteristic.getName())) {
					characteristic.setName(cfsCharacteristic.getName());
					characteristic.setValueType(cfsCharacteristic.getDataType());
					if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& ObjectUtils.isNotEmpty(connectionType)
							&& cfsCharacteristic.getName().equalsIgnoreCase("CONNECTION_TYPE"))
						characteristic.setValue(connectionType);
					   else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
	                            && cfsCharacteristic.getName().equalsIgnoreCase("POLICY_NAME"))
	                        characteristic.setValue(upcPlanId);
	                    else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
	                            && cfsCharacteristic.getName().equalsIgnoreCase("START_DATE")) 
	                        characteristic.setValue(getDate("START_DATE",isPcrfAddon));
	                    else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
	                            && cfsCharacteristic.getName().equalsIgnoreCase("END_DATE")) 
	                        characteristic.setValue(getDate("END_DATE",isPcrfAddon));
					else
						characteristic.setValue(cfsCharacteristic.getValue());
					characteristicList.add(characteristic);
				}
			});
		return characteristicList;
	}

	
	private boolean findCfsIsPCRF(String cfsName) {
		var cfsPCRF = getApplicationConfigValue("SOM_PCRF_NAME");
		if (cfsPCRF == null)
			cfsPCRF = "CFSS_PCRF_ADDON,CFSS_PCRF";

        return Arrays.asList(cfsPCRF.split(",")).contains(cfsName) ;
	}
	
	  private String getDate(String dateType,boolean isPcrf ) {
	    	
	    	String defaultDateTimeFormat="yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
			String  pcrfDateTimeFormat="dd-MM-yyyy HH:mm:ss";
	    	
	    	String resultDate="";
	    	
	    	if(dateType.equals("START_DATE")) {
	    		if(isPcrf)
	    			resultDate= LocalDateTime.now().format(DateTimeFormatter.ofPattern(pcrfDateTimeFormat));
	    		else
	    			resultDate= LocalDateTime.now().format(DateTimeFormatter.ofPattern(defaultDateTimeFormat));
	    	}
	    	else if (dateType.equals("END_DATE")) {
	    		if(isPcrf) {
	    			var inputDate= LocalDateTime.parse(expiryDate,DateTimeFormatter.ofPattern(defaultDateTimeFormat) );
	    			resultDate= inputDate.format(DateTimeFormatter.ofPattern(pcrfDateTimeFormat));
	    		}
	    		else
	    			resultDate= expiryDate;
	    			
	    	}
	    	
	    	return resultDate;
	    }
	    
	    protected String getApplicationConfigValue(String configName) {
	        var appConfig = cache.getCacheDetailsFromDBMap(
	                NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG, configName);
	        return appConfig != null ? appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()) : null;
	    }
}
