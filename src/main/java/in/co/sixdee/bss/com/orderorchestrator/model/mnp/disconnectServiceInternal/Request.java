package in.co.sixdee.bss.com.orderorchestrator.model.mnp.disconnectServiceInternal;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
public class Request {

	@XmlElement(name = "MessageReceiverTelco", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private String messageReciverTelco;
	@XmlElement(name = "MessageSenderTelco", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private String messageSenderTelco;
	@XmlElement(name = "RequestId", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private String requestId;
	@XmlElement(name = "Timestamp", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private String timestamp;

	@XmlElement(name = "SDInfos", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private SDInfos sdInfos;

	public String getMessageReciverTelco() {
		return messageReciverTelco;
	}

	public void setMessageReciverTelco(String messageReciverTelco) {
		this.messageReciverTelco = messageReciverTelco;
	}

	public String getMessageSenderTelco() {
		return messageSenderTelco;
	}

	public void setMessageSenderTelco(String messageSenderTelco) {
		this.messageSenderTelco = messageSenderTelco;
	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public String getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}

	public SDInfos getSdInfos() {
		return sdInfos;
	}

	public void setSdInfos(SDInfos sdInfos) {
		this.sdInfos = sdInfos;
	}

}
