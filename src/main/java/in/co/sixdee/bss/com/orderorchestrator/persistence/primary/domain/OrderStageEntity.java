package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;
import org.springframework.data.domain.Persistable;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "COM_ORDER_STAGES")
@lombok.Generated
public class OrderStageEntity extends AbstractAuditingEntity implements Serializable, Persistable<Long> {

	/**
	 *
	 */
	private static final long	serialVersionUID	= 1L;

	@Id
	@Column(name = "STAGE_ID", nullable = false)
	private Long				id;

	@Column(name = "SUB_ORDER_ID", length = 20)
	private Long				subOrderId;

	@Column(name = "ORDER_ID", length = 20)
	private Long				orderId;

	@Column(name = "NAME", length = 50)
	private String				name;

	@Column(name = "STAGE_CODE", length = 100)
	private String				stageCode;

	@Column(name = "STATE", length = 50)
	private String				state;

	@Column(name = "STATE_REASON")
	private String				stateReason;

	@Column(name = "DESCRIPTION", length = 50)
	private String				description;

	@Column(name = "EXECUTION_ORDER", length = 20)
	private Integer				executionOrder;

	@Column(name = "ROLL_BACK_STATUS", length = 20)
	private String				rollbackStatus;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "COMPLETION_DATE", columnDefinition = "DATETIME DEFAULT CURRENT_TIMESTAMP", insertable = false, updatable = false)
	private Date				completionDate;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SUB_ORDER_ID", insertable = false, updatable = false)
	@ToString.Exclude
	private SubOrderEntity subOrderEntity;
	
	@Column(name = "ENTITY_ID", length = 20)
	private String				entityId;

	@Column(name = "USERNAME", length = 100)
	private String				username;

	public OrderStageEntity(String stageCode,String state){
		this.stageCode = stageCode;
		this.state = state;
	}

	public OrderStageEntity(Date lastModifiedDate,String stateReason){
		this.stateReason = stateReason;
		setLastModifiedDate(lastModifiedDate);
	}

	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
			return false;
		OrderStageEntity that = (OrderStageEntity) o;

		return Objects.equals(id, that.id);
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

	@Override
	public boolean isNew() {
		return true;
	}
}
