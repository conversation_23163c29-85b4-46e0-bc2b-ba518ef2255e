package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "somFetchSubscriptionsByOwner")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SOMFetchSubscriptionByOwner extends AbstractDelegate {

	@Override
	protected void execute() throws Exception {
		execution.setVariable("somCallForGroupReqd", false);
		String request = getRequestFromSpec();
		if (executionContext.isError())
			return;

		var callThirdPartyDTO = callThirdParty(request);
		if (callThirdPartyDTO == null) {
			executionContext.setError(true);
			return;
		}

		var response = callThirdPartyDTO.getResponse();
		validateResponse(callThirdPartyDTO);
		if (executionContext.isError())
			return;
		if (StringUtils.isNotEmpty(response)) {
			List<SOMService> groupServiceOrderItems = filterGroupSubscriptionCfss(response);
			validateGroupServiceOrderItem(groupServiceOrderItems);	
		}
	}

	private void validateGroupServiceOrderItem(List<SOMService> groupServiceOrderItems) {
		if (ObjectUtils.isNotEmpty(groupServiceOrderItems)) {
			execution.setVariable("somCallForGroupReqd", true);
			executionContext.getWorkflowData().put("groupPlanCfss",
					groupServiceOrderItems);
			workflowDataUpdated = true;
			
		} else {
			execution.setVariable("somCallForGroupReqd", false);
		}
		
	}

	private List<SOMService> filterGroupSubscriptionCfss(String response)
			throws JsonMappingException, JsonProcessingException {
		List<Subscription> bsSubscriptions = getSubscriptionsFromViewResponse();
		List<SOMService> somServiceOrderItems = getSubscriptionsFromSomResponse(response);
		List<SOMService> getMatchingSomServices = new ArrayList<>();
		if (ObjectUtils.isNotEmpty(bsSubscriptions) && ObjectUtils.isNotEmpty(somServiceOrderItems)) {
			 for (Subscription subs : bsSubscriptions){
				for (SOMService serviceOrderItem : somServiceOrderItems) {
					String somSubscriptionId = getSomSubscriptionId(serviceOrderItem);
					if (somSubscriptionId != null && somSubscriptionId.equalsIgnoreCase(subs.getOldSubscriptionId())) {
						getMatchingSomServices.add(serviceOrderItem);
					}
				}
			}
		} else {
			log.info(
					"Either Billing subcriptions or SOM service registry entites are null. unable to create the delete list for SOM");
		}
		return getMatchingSomServices;
	}

	private String getSomSubscriptionId(SOMService serviceOrderItem) {
		String subscriptionId = null;
		if (serviceOrderItem != null && serviceOrderItem.getServiceCharacteristic() != null) {
			for (Characteristic characteristic : serviceOrderItem.getServiceCharacteristic()) {
				if ("SUBSCRIPTION_ID".equals(characteristic.getName())) {
					subscriptionId = characteristic.getValue();
					break;

				}
			}
		}
		return subscriptionId;
	}

	private List<SOMServiceOrderDTO.SOMService> getSubscriptionsFromSomResponse(String response)
			throws JsonMappingException, JsonProcessingException {
		return objectMapper.readValue(response, new TypeReference<List<SOMServiceOrderDTO.SOMService>>() {
		});
	}

	private List<Subscription> getSubscriptionsFromViewResponse() {
		List<Subscription> bsFetchSubscription = new ArrayList<>();

		if (executionContext.getWorkflowData().containsKey("BS_ViewGroupSubscriptionResponseAttributes")) {
			bsFetchSubscription = objectMapper.convertValue(
					executionContext.getWorkflowData().get("BS_ViewGroupSubscriptionResponseAttributes"),
					new TypeReference<>() {
					});
		}
		return bsFetchSubscription;
	}
}
