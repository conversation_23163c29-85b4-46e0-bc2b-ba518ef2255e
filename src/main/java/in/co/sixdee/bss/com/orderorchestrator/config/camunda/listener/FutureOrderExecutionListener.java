package in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener;

import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.WaitingProcessInfoEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.SubOrderService;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.WaitingProcessInfoRepository;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderStatusManager;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.core.AppInstanceIdManager;
import in.co.sixdee.bss.common.util.ApplicationProcessContext;
import in.co.sixdee.bss.common.util.NGTableConstants;
import lombok.extern.log4j.Log4j2;

import java.util.Collections;

@Component
@Log4j2
public class FutureOrderExecutionListener implements ExecutionListener {

    @Autowired
    private GetDataFromCache cache;

    @Autowired
    private AppInstanceIdManager appInstanceSequence;

    protected ApplicationProcessContext processContext;

    @Autowired
    protected ProcessDataAccessor processDataAccessor;

    @Autowired
    protected SubOrderService subOrderService;

    @Autowired
    protected OrderRepository orderRepository;

    @Autowired
    private OrderStatusManager orderStatusManager;

    @Autowired
    private WaitingProcessInfoRepository waitingProcessInfoRepository;



    @Override
    public void notify(DelegateExecution execution) {

        String statusReason = null;
        String executionStatus = null;
        String linkedActivityId = null;
        String subOrderId = null;
        String orderId = null;
        try {
            var orderFlowContext = processDataAccessor.getOrderFlowContext(execution);
            orderId = orderFlowContext.getOrder().getOrderId();
            subOrderId = orderFlowContext.getAttributes().get(GenericConstants.SUB_ORDER_ID);
            if (StringUtils.equals(ExecutionListener.EVENTNAME_START, execution.getEventName())) {

                log.info("timer waiting event {} started for activity :: {} even name :: {}", execution.getId(),
                        execution.getCurrentActivityId(), execution.getEventName());
                processContext = new ApplicationProcessContext(appInstanceSequence.getInstanceId(),
                        orderFlowContext.getTraceId(), orderFlowContext.getRequestId(), orderFlowContext.getChannel(),
                        orderFlowContext.getUsername(), orderFlowContext.getEntityId());
                processContext.setMdc();

                if ("FutureOrderTimer".equalsIgnoreCase(execution.getCurrentActivityId())) {
                    linkedActivityId = execution.getCurrentActivityId();
                    executionStatus = "Held";
                    statusReason = "Scheduled for execution on " + orderFlowContext.getOrder().getRequestedStartDate();
                    orderFlowContext.getAttributes().put("FutureOrderTimerReason", statusReason);
                    createWaitingProcessInfoEntity(execution, orderFlowContext, orderId, subOrderId);
                }

                orderStatusManager.processStatusUpdates(orderFlowContext, linkedActivityId, "WAIT_EVENT",
                        true, executionStatus);

            } else if (StringUtils.equals(ExecutionListener.EVENTNAME_END, execution.getEventName())) {
                log.info("timer waiting  end event started for activity {} for order id {} and sub order id {}",execution.getCurrentActivityId(),orderId,subOrderId);
                linkedActivityId = execution.getCurrentActivityId();
                executionStatus = "Completed";
                deleteWaitingProcessInfoEntity(orderFlowContext, orderId, subOrderId);
                orderStatusManager.processStatusUpdates(orderFlowContext, linkedActivityId, "WAIT_EVENT",
                        true, executionStatus);
            }
        } catch (Exception e) {
            log.info(" Exception occurred in SignalExecutionListener.notify", e);
            throw e;
        }
    }

    private void deleteWaitingProcessInfoEntity(OrderFlowContext orderFlowContext, String orderId, String subOrderId) {
        waitingProcessInfoRepository
                .deletebyOrderIdSubOrderIdAndWaitType(orderId, subOrderId, "Timer");
    }

    private void createWaitingProcessInfoEntity(DelegateExecution execution, OrderFlowContext orderFlowContext, String orderId, String subOrderId) {
        var linkedStage = cache.getCacheDetailsFromDBMap("STAGE_CONFIG_BY_ACTIVITY_ID_AND_ORDER_TYPE",
                orderFlowContext.getOrder().getOrderType() + "_" + execution.getCurrentActivityId());
        WaitingProcessInfoEntity waitingProcessInfoEntity = new WaitingProcessInfoEntity();
        waitingProcessInfoEntity.setOrderId(orderId);
        waitingProcessInfoEntity.setSubOrderId(subOrderId);
        waitingProcessInfoEntity.setEventName(execution.getCurrentActivityName());
        waitingProcessInfoEntity.setWaitType("Timer");
        waitingProcessInfoEntity.setExecutionId(execution.getId());
        waitingProcessInfoEntity.setProcessInstanceId(execution.getProcessInstanceId());
        if (ObjectUtils.isNotEmpty(linkedStage))
            waitingProcessInfoEntity.setStageCode(linkedStage.getNgTableData().get("STAGE_ID"));
        waitingProcessInfoRepository.save(waitingProcessInfoEntity);

    }

    @Transactional(readOnly = true)
    public WaitingProcessInfoEntity getWaitEventInfo(String orderId, String subOrderId) {
        WaitingProcessInfoEntity waitingProcessInfo = null;
        if (StringUtils.isNotEmpty(orderId) && StringUtils.isNotEmpty(subOrderId))
            waitingProcessInfo = waitingProcessInfoRepository.findProcessInfoByOrderIdAndSubOrderId(orderId, subOrderId,
                    Collections.singletonList("Callback"));
        else
            waitingProcessInfo = waitingProcessInfoRepository.findProcessInfoByOrderId(orderId, Collections.singletonList("Callback"));
        return waitingProcessInfo;
    }

    private String getStatusReasonforTimerEndEvent(String key) {
        String statusReason = null;

        var appConfig = cache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG, key);
        if (appConfig != null
                && appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()) != null) {
            statusReason = appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
        }
        return statusReason;

    }
}
