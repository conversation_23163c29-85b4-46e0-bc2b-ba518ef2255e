package in.co.sixdee.bss.com.orderorchestrator.config.camunda.asynOnError;

import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.ProcessEngine;
import org.camunda.bpm.engine.ProcessEngineConfiguration;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.camunda.bpm.engine.impl.ManagementServiceImpl;
import org.camunda.bpm.engine.impl.ProcessInstantiationBuilderImpl;
import org.camunda.bpm.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.camunda.bpm.engine.impl.interceptor.Command;
import org.camunda.bpm.engine.impl.interceptor.CommandExecutor;
import org.camunda.bpm.engine.runtime.Job;
import org.camunda.bpm.engine.runtime.ProcessInstanceWithVariables;
import org.camunda.bpm.engine.runtime.ProcessInstantiationBuilder;

import javax.inject.Named;
import java.util.List;


@Named
@Log4j2
public class AsyncOnError {

	public AsyncOnErrorActivityBehavior getAsyncOnErrorActivityBehavior(JavaDelegate delegate) {
		return new AsyncOnErrorActivityBehavior(delegate);
	}

	public static ProcessInstanceWithVariables startProcessInstance(ProcessEngine processEngine, ProcessInstantiationBuilder processInstantiationBuilder) {

		log.info("inside AsyncOnErrorActivityBehavior.startProcessInstance");
		// ensure we are not participating in any open TX, which would be marked for rollback only by exceptions
		CommandExecutor commandExecutor = getCommandExecutorTxRequiresNew(processEngine);

		// start process and suspend jobs created at start
		Command<ProcessInstanceWithVariables> cmd = new StartProcessInstanceAndSuspendJobsCmd((ProcessInstantiationBuilderImpl) processInstantiationBuilder);
		ProcessInstanceWithVariables processInstance = commandExecutor.execute(cmd);

		// initialize ManagementService to enforce new TX
		ManagementServiceImpl managementService = getManagementService(commandExecutor, processEngine.getProcessEngineConfiguration());
		// query jobs
		List<Job> jobs = managementService.createJobQuery()
				.processInstanceId(processInstance.getId())
				.suspended()
				.list();
		for (Job job : jobs) {
			try {
				// try to run jobs synchronously
				managementService.executeJob(job.getId());
			} catch (Exception e) {
				// if it fails hand over to Job Executor to retry asynchronously
				managementService.activateJobById(job.getId());
			}
		}
		return processInstance;
	}

	private static ManagementServiceImpl getManagementService(CommandExecutor commandExecutor, ProcessEngineConfiguration processEngineConfiguration) {
		ManagementServiceImpl managementService = new ManagementServiceImpl(processEngineConfiguration);
		managementService.setCommandExecutor(commandExecutor);
		return managementService;
	}

	private static CommandExecutor getCommandExecutorTxRequiresNew(ProcessEngine processEngine) {
		return ((ProcessEngineConfigurationImpl) processEngine.getProcessEngineConfiguration()).getCommandExecutorTxRequiresNew();
	}

}