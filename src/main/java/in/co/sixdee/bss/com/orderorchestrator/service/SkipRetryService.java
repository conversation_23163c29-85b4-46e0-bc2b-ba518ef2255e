package in.co.sixdee.bss.com.orderorchestrator.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.EdrConfig;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.modification.ProcessInstanceModificationHandler;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.BulkRetryInvalidOrderException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.PartialResultException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderAttributeRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderStageRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.WaitingProcessInfoRepository;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.constants.ApiConstants;
import in.co.sixdee.bss.common.util.NGTableColumnConstants;
import in.co.sixdee.bss.om.model.dto.Response;
import in.co.sixdee.bss.om.model.dto.WorkflowRequest;
import in.co.sixdee.bss.om.model.dto.WorkflowRequest.Orders;
import in.co.sixdee.bss.om.model.dto.WorkflowRequest.SubOrders;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.RuntimeService;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentLinkedQueue;

@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Service
@Log4j2
@RequiredArgsConstructor
public class SkipRetryService {

	private final ProcessInstanceModificationHandler processInstanceModificationHandler;

	private final OrderStageRepository orderStageRepository;

	private final GetDataFromCache getDataFromCache;

	private final OrderRepository orderRepository;

	protected Set<Orders> inValidOrders = null;
	protected Set<Orders> validOrders   = null;

	public Response processModifyRequest(WorkflowRequest request) {
		if (!validateModifyRequest(request))
			throw new CommonException(StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION,
					"No record found for the order and sub order combination, order id : " + request.getOrderId()
							+ ", sub order id : " + request.getSubOrderId());
		processInstanceModificationHandler.processSkipRetryRequest(request);
		return createModifyResponse(request);
	}


	protected boolean validateModifyRequest(WorkflowRequest request) {
		if (StringUtils.equalsAnyIgnoreCase(request.getAction(), "skip", "retry"))
			return orderStageRepository.existsByOrderIdsubOrderIdandName(Long.valueOf(request.getOrderId()),
					Long.valueOf(request.getSubOrderId()), request.getStageId());
		return false;
	}

	protected Response createModifyResponse(WorkflowRequest request) {
		var response = new Response();
		response.setOrderId(request.getOrderId());
		response.setRequestId(StringUtils.isNotEmpty(MDC.get(ApiConstants.TRACE_ID)) ? MDC.get(ApiConstants.TRACE_ID)
				: MDC.get(ApiConstants.REQUEST_ID));
		response.setStatus("Success");
		response.setCode("200");
		response.setMessage("Skip/Retry request initiated successfully");
		return response;
	}


	public Response processBulkSkipRetryRequest(WorkflowRequest request) {
		var errorDesc = "";
		request.setAction(request.getAction());
		var appConfig = getDataFromCache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
				"ERROR_DESC_FOR_BULK_RETRY");
		if (ObjectUtils.isNotEmpty(appConfig))
			errorDesc = appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
		if (!validateBulkSkipRetryRequest(request))
			throw new BulkRetryInvalidOrderException(StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION, errorDesc,
					inValidOrders);
		Set<Orders> invalidOrders = processInstanceModificationHandler.processBulkOrderRetry(request, validOrders,
				inValidOrders);
		return createBulkSkipRetryResponse(invalidOrders, request);
	}

	protected boolean validateBulkSkipRetryRequest(WorkflowRequest request) {
		boolean validation = false;
		Set<Orders> channelOrders = new HashSet<>();
		validOrders = new HashSet<>();
		inValidOrders = new HashSet<>();

		List<Long> orderIds = new ArrayList<>();
		List<Long> subOrderIds = new ArrayList<>();
		if (ObjectUtils.isNotEmpty(request.getOrders())) {
			channelOrders = request.getOrders();
			channelOrders.forEach(order -> orderIds.add(Long.valueOf(order.getOrderId())));
			List<OrderEntity> findFailedOrdersByOrderId = orderRepository.findOrdersByOrderId(orderIds);

			channelOrders.forEach(channelOrder -> {
				findFailedOrdersByOrderId.stream().filter(order -> StringUtils
								.equalsAnyIgnoreCase(channelOrder.getOrderId(), String.valueOf(order.getOrderId().toString())))
						.forEach(order -> {
							channelOrder.setOrderType(order.getType());
							channelOrder.setState(order.getState());
						});
			});
			for (Orders order : channelOrders) {
				Orders inValidOrder = new Orders();
				var orderId = Long.valueOf(order.getOrderId());
				if (!StringUtils.equalsAnyIgnoreCase(order.getState(), "Failed", "PartialSuccess")) {
					order.setStatusReason("Order is in '" + order.getState() + "' state. Cannot be skipped/retried");
					order.setOrderType(null);
					order.setState(null);
					inValidOrders.add(order);
				} else {
					List<OrderStageEntity> subOrders = null;
					Set<SubOrders> validSubOrders = new HashSet<>();
					Set<SubOrders> inValidSubOrders = new HashSet<>();
					var channelOrder = SerializationUtils.clone(order);
					if (ObjectUtils.isNotEmpty(channelOrder.getSubOrders())) {

						order.setSubOrders(null);
						channelOrder.getSubOrders()
								.forEach(subOrder -> subOrderIds.add(Long.valueOf(subOrder.getSubOrderId())));
						subOrders = orderStageRepository.findSubOrdersBySubOrderId(orderId, subOrderIds);

						for (OrderStageEntity subOrderStage : subOrders) {
							if (StringUtils.equalsIgnoreCase(subOrderStage.getState(), "Failed")) {

								if (isSkipRetryEnabled(order.getOrderType(), subOrderStage, request.getAction())) {
									SubOrders validSubOrder = new SubOrders();
									validSubOrder.setSubOrderId(subOrderStage.getSubOrderId().toString());
									validSubOrders.add(validSubOrder);
								} else {
									SubOrders inValidSubOrder = new SubOrders();
									inValidSubOrder.setStatusReason(request.getAction() + " is not enabled for stage '"
											+ subOrderStage.getStageCode() + "'");
									inValidSubOrder.setSubOrderId(subOrderStage.getSubOrderId().toString());
									inValidSubOrders.add(inValidSubOrder);
								}

							} else {
								SubOrders inValidSubOrder = new SubOrders();
								inValidSubOrder.setStatusReason(
										"Suborder is in '" + subOrderStage.getState() + "' state. Cannot be retried.");
								inValidSubOrder.setSubOrderId(subOrderStage.getSubOrderId().toString());
								inValidSubOrders.add(inValidSubOrder);
							}
						}
					} else {
						subOrders = orderStageRepository.findFailedSubOrdersByOrderIdandState(orderId);
						for (OrderStageEntity subOrder : subOrders) {
							if (isSkipRetryEnabled(order.getOrderType(), subOrder, request.getAction())) {
								SubOrders validSubOrder = new SubOrders();
								validSubOrder.setSubOrderId(subOrder.getSubOrderId().toString());
								validSubOrders.add(validSubOrder);
							} 
							/*else {
								SubOrders inValidSubOrder = new SubOrders();
								inValidSubOrder.setStatusReason(
										request.getAction() + " is not enabled for stage '" + subOrder.getStageCode() + "'");
								inValidSubOrder.setSubOrderId(subOrder.getSubOrderId().toString());
								inValidSubOrders.add(inValidSubOrder);
							}*/

						}
					}
					if (ObjectUtils.isNotEmpty(validSubOrders)) {
						order.setSubOrders(validSubOrders);
						validOrders.add(order);
					}
					if (ObjectUtils.isNotEmpty(inValidSubOrders)) {
						inValidOrder.setSubOrders(inValidSubOrders);
						inValidOrder.setOrderId(channelOrder.getOrderId());
						inValidOrders.add(inValidOrder);
					}
				}
			}

			if (ObjectUtils.isNotEmpty(validOrders) && validOrders.size() >= 1)
				validation = true;

		}
		return validation;
	}

	private boolean isSkipRetryEnabled(String orderType, OrderStageEntity stage, String action) {

		var orderStageConfigDTO = getDataFromCache.getCacheDetailsFromDBMap(
				CacheConstants.CacheKeys.STAGE_CONFIG_BY_ORDER_TYPE_AND_STAGE_ID.name(),
				orderType + "_" + stage.getStageCode());
		if (orderStageConfigDTO == null)
			return false;
		var workFlowRetryConfig = getDataFromCache
				.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_WORKFLOW_RETRY_CONFIG.name(), orderType + "_"
						+ orderStageConfigDTO.getNgTableData().get(CacheConstants.CacheFields.LINKED_ACTIVITY.name()));

		if (workFlowRetryConfig == null) {
			if (StringUtils.isNotEmpty(stage.getRollbackStatus()) && stage.getRollbackStatus().equalsIgnoreCase("true")) {
				return true;
			}
		} else {
			if (StringUtils.isNotEmpty(action) && action.equalsIgnoreCase("skip")) {
				var skipEnabledConfig = workFlowRetryConfig.getNgTableData().get("SKIP_ENABLE");
				if (BooleanUtils.toBoolean(skipEnabledConfig))
					return true;
			} else {
				String retryStrategy = workFlowRetryConfig.getNgTableData()
						.get(NGTableColumnConstants.COLUMN_RETRY_STRATEGY);
				if (StringUtils.equalsAnyIgnoreCase(retryStrategy, OrderConstants.RETRY_STRATEGY_CONNECTION_ERROR,
						OrderConstants.RETRY_STRATEGY_API_ERROR, OrderConstants.RETRY_STRATEGY_BOTH)) {
					if (StringUtils.isNotEmpty(stage.getStageCode()) && stage.getStageCode().contains("SOM")) {
						if (StringUtils.isEmpty(stage.getRollbackStatus())
								|| stage.getRollbackStatus().equalsIgnoreCase("true")) {
							return true;
						} else {
							return false;
						}
					}
					return true;
				}
			}
		}
		return false;
	}

	private Response createBulkSkipRetryResponse(Set<Orders> invldOrders, WorkflowRequest request) {
		var response = new Response();
		var errorDesc = "";

		response.setRequestId(StringUtils.isNotEmpty(MDC.get(ApiConstants.TRACE_ID)) ? MDC.get(ApiConstants.TRACE_ID)
				: MDC.get(ApiConstants.REQUEST_ID));
		if (ObjectUtils.isNotEmpty(invldOrders) && invldOrders.size() >= 1) {
			var appConfig = getDataFromCache.getCacheDetailsFromDBMap(
					CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "PARTIAL_ERROR_DESC_FOR_BULK_RETRY");
			if (ObjectUtils.isNotEmpty(appConfig))
				errorDesc = appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
			throw new PartialResultException(StatusConstants.HttpConstants.PARTIAL_CONTENT, errorDesc, invldOrders);
		} else {
			response.setStatus("Success");
			response.setCode("200");
			response.setMessage("Bulk " + request.getAction() + " request initiated successfully");
			request.getOrders().forEach(order -> {
				order.setOrderType(null);
				order.setState(null);
			});
			response.setOrders(request.getOrders());
		}
		return response;
	}

}
