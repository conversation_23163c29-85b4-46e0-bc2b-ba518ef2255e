package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import in.co.sixdee.bss.com.orderorchestrator.config.ApplicationProperties;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;

@Repository
@RequiredArgsConstructor
@Log4j2
public class NativeWaitingProcessInfoRepository {


	@PersistenceContext
	protected EntityManager em;

	protected final ApplicationProperties applicationProperties;


	public void save(OrderStageEntity stage) {
		var session = em.unwrap(Session.class);
		final String sql = "INSERT INTO WAITING_PROCESS_INFO (ORDER_ID, SUB_ORDER_ID, PROCESS_INSTANCE_ID, STAGE_CODE, EXECUTION_ID) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
		session.doWork(connection -> {
			try (PreparedStatement ps = connection.prepareStatement(sql)) {
				ps.setString(1, stage.getCreatedBy());
				ps.setString(2, stage.getLastModifiedBy());
				ps.setString(3, stage.getDescription());
				ps.setInt(4, stage.getExecutionOrder());
				ps.setString(5, stage.getName());
				ps.setLong(6, stage.getOrderId());
				ps.setString(7, stage.getStageCode());
				ps.setString(8, stage.getState());
				ps.setString(9, stage.getStateReason());
				ps.setLong(10, stage.getSubOrderId());
				ps.setLong(11, stage.getId());
				ps.executeUpdate();
			} catch (SQLException e) {
				log.error("An exception occurred in NativeStageRepository.save", e);
			}
		});
	}

}
