package in.co.sixdee.bss.com.orderorchestrator.config.util.constants;

import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */

public final class AppConstants {


	private AppConstants() {

	}
	public static final String			  SM_DATE_FORMAT				= "yyyy-MM-dd HH:mm:ss";
	public static final String            SYSTEM_DATE_FORMAT            = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
	public static final DateTimeFormatter SYSTEM_DATE_PATTERN_FORMATTER = DateTimeFormatter.ofPattern(SYSTEM_DATE_FORMAT);



	public enum CacheKeys {
		COM_THIRD_PARTY_URL_CONFIG, COM_THIRD_PARTY_URL_TOKEN_CONFIG, APPLICATION_CONFIG, COM_THIRD_PARTY_URL_CONFIG_BY_OPERATION_ID
	}

	public enum CacheFields {
		CONFIG_VALUE, READ_TIMEOUT, CONNECTION_TIMEOUT, HEADERS, TRANSPORT_METHOD, URL, TOKENS, QUERY_PARAMS, PAYLOAD_VALIDATION, THIRD_PARTY_ID,
		EXPECTED_HTTP_CODE, EXPECTED_RESP_CODE, RESP_STATUS_PATH, RESP_STATUS_DESC_PATH, THIRD_PARTY_SYSTEM, PERMANENT_ERROR_CODES
	}


	public enum ProcessVariables {
		GENERIC_DTO("GenericDTO"),
		STATUS("Status"),
		EXECUTION_CONTEXT("executionContext"),
		THIRD_PARTY_ID("thirdPartyId"),
		REQ_SPEC_KEY("reqSpecKey"),
		CURRENT_EXECUTION("currentExecution"),
		RESPONSE_ATTRIBUTES("responseAttributes"),
		PROCESS_VARIABLES("processVariables"),
		PROCESS_ID("processId"),
		FILTER_PROFILE("profile"),
		FILTER_ACCOUNT("account"),
		PORT_IN_TYPE("portInType"),

		MNP_PROVISIONING_DATE("mnpProvisioningDate"),

		MNP_DEPROVISIONING_DATE("mnpDeProvisioningDate"),
		
		MNP_CHANGE_PRODUCT_DATE("mnpChangeProductDate"),
		
		MNP_CONNECT_SERVICE_INT_DATE("mnpConnectServiceIntDate"),
		QUERY_MNP_REQUIRED("queryMnpRequired")
		;


		private final String value;

		ProcessVariables(String newValue) {
			value = newValue;
		}

		public String value() {
			return String.valueOf(value);
		}
	}

	public enum ProcessVariableValues {
		STATUS_SUCCESS("0"),
		STATUS_FAILURE("1");


		private final String value;

		ProcessVariableValues(String newValue) {
			value = newValue;
		}

		public String value() {
			return String.valueOf(value);
		}
	}

	public enum ProcessIds {

		PROCESS_QUERY_SUBSCRIBER_SERVICE("QuerySubscriberService");

		private final String value;

		ProcessIds(String newValue) {
			value = newValue;
		}

		public String value() {
			return String.valueOf(value);
		}
	}

	public enum NotificationMessageTypes {
		NCC_INSUFFICIENT_BALANCE("NCC_INSUFFICIENT_BALANCE"),
		NOTIFICATION_FOR_UPDATE_TAGS("NOTIFICATION_FOR_UPDATE_TAGS"),
		WELCOME_NOTIFICATION("WELCOME_NOTIFICATION");

		private final String value;

		NotificationMessageTypes(String newValue) {
			value = newValue;
		}
		public String value() {
			return String.valueOf(value);
		}
		

	}

}
