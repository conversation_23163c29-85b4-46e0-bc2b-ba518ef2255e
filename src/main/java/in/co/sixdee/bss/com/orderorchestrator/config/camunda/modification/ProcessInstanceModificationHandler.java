/**
 *
 */
package in.co.sixdee.bss.com.orderorchestrator.config.camunda.modification;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.WorkFlowUtil;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.notification.NotificationUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.*;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants.ProcessConstants;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.TransactionHandler;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.*;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderApprovalService;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderStageService;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderStatusManager;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.SubscriptionSyncHandler;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.constants.ApiConstants;
import in.co.sixdee.bss.common.util.NGTableColumnConstants;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.WorkflowRequest;
import in.co.sixdee.bss.om.model.dto.WorkflowRequest.Orders;
import in.co.sixdee.bss.om.model.dto.WorkflowRequest.SubOrders;
import in.co.sixdee.bss.om.model.dto.order.Order;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.ManagementService;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.runtime.Incident;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.runtime.ProcessInstanceModificationBuilder;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.xml.instance.ModelElementInstance;
import org.camunda.spin.plugin.variable.SpinValues;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class ProcessInstanceModificationHandler {

    private final WorkFlowUtil workFlowUtil;

    private final ManagementService managementService;

    private final RepositoryService repositoryService;

    private final RuntimeService runtimeService;

    private final ObjectMapper objectMapper;

    private final OrderRepository orderRepository;

    private final WaitingProcessInfoRepository waitingInfoRepository;

    private final OrderStatusManager orderStatusManager;

    private final GetDataFromCache cache;

    private final OrderStageService orderStageService;

    private final NotificationUtils notificationUtils;

    private final OrderStageRepository orderStageRepository;

    private final SubscriptionSyncHandler subscriptionSyncHandler;

    private final OrderApprovalService orderApprovalService;

    private final EsbRetryRequestIdRepository esbRetryRequestIdRepository;


    public void processSkipRetryRequest(WorkflowRequest request) {
        String orderId = request.getOrderId();
        Long orderIdAsLong = Long.valueOf(request.getOrderId());
        String subOrderId = request.getSubOrderId();
        String stageId = request.getStageId();
        String orderIdWithSubOrder = orderId + "_" + subOrderId;

        String orderType = orderRepository.findOrderTypeByOrderId(orderIdAsLong);
        OrderFlowContext orderFlowContext = initOrderFlowContext(orderType, request);
        String activityId = getStageConfig(orderFlowContext, request);


        var piList = runtimeService.createProcessInstanceQuery().processInstanceBusinessKey(orderIdWithSubOrder).list();

        if (ObjectUtils.isEmpty(piList)) {
            log.info("No process instances obtained in sub order level. searching for process instances created at order level");
            piList = runtimeService.createProcessInstanceQuery().processInstanceBusinessKey(orderId).list();
        }
        log.info("Process instances available for the order {} are {}", orderId, piList);

        if (ObjectUtils.isEmpty(piList)) {
            handleEmptyProcessInstanceList(orderFlowContext, activityId, orderId);
        } else {
            handleProcessInstances(piList, request, activityId, orderType, stageId);
        }
    }

    private void handleEmptyProcessInstanceList(OrderFlowContext orderFlowContext, String activityId, String orderId) {
        if (activityId.equalsIgnoreCase("Approval")) {
            orderFlowContext.getAttributes().put("skipUser", MDC.get(ApiConstants.USER_NAME));
            orderStatusManager.processStatusUpdates(orderFlowContext, "Approval", "SERVICE_TASK", true, WorkFlowConstants.StageStatusConstants.STAGE_STATUS_SKIPPED.getValue());
            orderApprovalService.processApprovalOrder(orderId, true);
        } else {
            throw new CommonException(StatusConstants.HttpConstants.INTERNAL_SERVER_ERROR, "Process Instances not available for this orderId: " + orderId);
        }
    }


    private void handleProcessInstances(List<ProcessInstance> piList, WorkflowRequest request, String activityId, String orderType, String stageId) {
        if (isPortInNotificationStage(activityId)) {
            handlePortInNotificationStages(request, piList, orderType, activityId);
            return;
        }
        Incident incident = null;
        ProcessInstance instance = null;
        for (ProcessInstance processInstance : piList) {
            if (StringUtils.isNotEmpty(processInstance.getId())) {
                incident = runtimeService.createIncidentQuery().incidentType("failedJob").processInstanceId(processInstance.getId()).singleResult();
                instance = processInstance;
                log.info("Process Instance Id: {} :: Business Key: {}", instance.getId(), instance.getBusinessKey());
                if ((ObjectUtils.isNotEmpty(incident) && StringUtils.isNotEmpty(incident.getJobDefinitionId()))) {
                    log.info("JobDefinitionId: {}",incident.getJobDefinitionId());
                    break;
                }
            }
        }
        handleIncident(incident, instance, request, activityId, orderType, stageId);


    }

    private void handlePortInNotificationStages(WorkflowRequest workflowRequest, List<ProcessInstance> piList, String orderType, String failedActivityId) {
        // last item in the list will be the currently executing process instance
        ProcessInstance processInstance = null;
        for (ProcessInstance pi : piList) {
            log.info("process definition id {}, process instance id {}", pi.getProcessDefinitionId(), pi.getProcessDefinitionId());
            if (!pi.getProcessDefinitionId().startsWith("MNPPortIn:") && !pi.getProcessDefinitionId().startsWith("InterimNumberPortIn:")) {
                processInstance = pi;
                break;
            }
        }
        if (processInstance == null) {
            log.info("Unable to find the process instance id for mnp port in sub process");
            return;
        }
        String stageId = orderStageService.findSubmitPortInStage(Long.parseLong(workflowRequest.getOrderId()));
        String activityId = getActivityId(orderType, stageId);
        var waitingInfo = waitingInfoRepository.findProcessInfoByOrderIdAndSubOrderId(workflowRequest.getOrderId(), workflowRequest.getSubOrderId(), Arrays.asList("Callback", "Timer"));
        ProcessInstanceModificationBuilder processInstanceModificationBuilder = runtimeService.createProcessInstanceModification(processInstance.getProcessInstanceId());
        if (waitingInfo != null) {
            log.info("Cancelling the wait event {} of type {}", waitingInfo.getEventName(), waitingInfo.getWaitType());
            processInstanceModificationBuilder.cancelAllForActivity(waitingInfo.getEventName());
            waitingInfoRepository.deletebySeqId(waitingInfo.getSeqId());
        }
        runtimeService.setVariable(processInstance.getProcessInstanceId(), "retry", true);
        runtimeService.setVariable(processInstance.getProcessInstanceId(), "failedActivityId", failedActivityId);
        processInstanceModificationBuilder.startBeforeActivity(activityId).execute();

    }


    private boolean isPortInNotificationStage(String activityId) {
        return StringUtils.equalsAnyIgnoreCase(activityId, MNPConstants.PORT_IN_NOTIFICATION_PNREJ, MNPConstants.PORT_IN_NOTIFICATION_PNINV, MNPConstants.PORT_IN_NOTIFICATION_PNCNT);
    }

    private void handleIncident(Incident incident, ProcessInstance processInstance, WorkflowRequest request, String activityId, String orderType, String stageId) {
        if (incident == null || ObjectUtils.isEmpty(incident)) {
            handleNoIncident(processInstance, request, activityId, stageId);
        } else if (StringUtils.isNotEmpty(incident.getJobDefinitionId())) {
            handleExistingIncident(incident, processInstance, request, activityId, orderType, stageId);
        }
    }

    private void handleNoIncident(ProcessInstance processInstance, WorkflowRequest request, String activityId, String stageId) {
        if (StringUtils.startsWithAny(stageId, "SOM", "NGW")) {
            skipRetrySOMExecution(processInstance, activityId, request.getOrderId(), request.getSubOrderId(), request.getAction(), stageId);
        } else {
            throw new CommonException(StatusConstants.HttpConstants.INTERNAL_SERVER_ERROR, "No Incident available for this orderId: " + request.getOrderId());
        }
    }

    private void handleExistingIncident(Incident incident, ProcessInstance processInstance, WorkflowRequest request, String activityId, String orderType, String stageId) {
        String retryRequestId = getRetryRequestId(request.getOrderId(), request.getSubOrderId(), stageId, orderType);
        if (request.getAction().equalsIgnoreCase(ProcessConstants.ACTION_RETRY.desc) && workFlowUtil.isTaskRetryEnabled(orderType, activityId)) {
            if (StringUtils.isNotEmpty(retryRequestId)) {
                runtimeService.setVariable(processInstance.getId(), "retryRequestId", retryRequestId);
            }
            retryExecution(incident, request.getOrderId(), stageId);
        } else {
            if (StringUtils.isNotEmpty(retryRequestId)) {
                runtimeService.removeVariable(processInstance.getId(), "retryRequestId");
                orderStageService.deleteRetryRequestIdAttributes(request.getOrderId(), request.getSubOrderId());
            }
            skipExecution(incident, orderType, activityId, request, stageId);
        }
    }


    public String getRetryRequestId(String orderId, String subOrderId, String stageId, String orderType) {
        String retryRequestId = null;

        var stageConfigs = cache.getCacheDetailsFromDBMap(NGTableConstants.COM_ESB_RETRY_REQUIRED_STAGES, orderType);
        if (ObjectUtils.isNotEmpty(stageConfigs) && ObjectUtils.isNotEmpty(stageConfigs.getNgTableData().get("STAGE_ID"))) {
            List<String> stageIds = Arrays.asList(stageConfigs.getNgTableData().get("STAGE_ID").split(","));
            boolean stageExists = stageIds.stream().anyMatch(stageId::equalsIgnoreCase);
            if (stageExists) {
                retryRequestId = esbRetryRequestIdRepository.getAttributesByOrderIdAndAttribute(Long.valueOf(orderId), Long.valueOf(subOrderId));
            }
        }
        return retryRequestId;
    }

    private OrderFlowContext initOrderFlowContext(String orderType, WorkflowRequest request) {
        var orderFlowContext = new OrderFlowContext();
        var order = new Order();
        order.setOrderType(orderType);
        order.setOrderId(request.getOrderId());
        orderFlowContext.setOrder(order);
        orderFlowContext.getAttributes().put("skipUser", MDC.get(ApiConstants.USER_NAME));
        orderFlowContext.getAttributes().put(GenericConstants.SUB_ORDER_ID, request.getSubOrderId());
        return orderFlowContext;
    }

    private void skipRetrySOMExecution(ProcessInstance processInstance, String activityId, String orderId, String subOrderId, String action, String stageId) {
        if (StringUtils.isNotEmpty(orderId) && StringUtils.isNotEmpty(subOrderId)) {
            var waitingInfo = waitingInfoRepository.findProcessInfoByOrderIdAndSubOrderId(orderId, subOrderId, Collections.singletonList("Callback"));
            if (StringUtils.isNotEmpty(waitingInfo.getProcessInstanceId())) {
                if (action.equalsIgnoreCase("retry")) {
                    orderStatusManager.updateRollBackStatusForSOM(subOrderId, stageId);
                    runtimeService.createProcessInstanceModification(waitingInfo.getProcessInstanceId()).cancelAllForActivity(waitingInfo.getEventName()).startBeforeActivity(activityId).execute();
                } else {
                    try {
                        OrderFlowContext executionContext = objectMapper.readValue(runtimeService.getVariable(waitingInfo.getProcessInstanceId(), WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString()).toString(), OrderFlowContext.class);

                        notificationUtils.sendNotification(executionContext, AppConstants.NotificationMessageTypes.WELCOME_NOTIFICATION.value(), "SUB_ORDER");

                        orderStatusManager.updateStageStatusToSkipped(subOrderId, stageId);
                        runtimeService.setVariable(waitingInfo.getProcessInstanceId(),"skipEnabled",true);
                        //runtimeService.createProcessInstanceModification(waitingInfo.getProcessInstanceId()).cancelAllForActivity(waitingInfo.getEventName()).startAfterActivity(waitingInfo.getEventName()).execute(false, false);
                        runtimeService.createProcessInstanceModification(waitingInfo.getProcessInstanceId()).cancelAllForActivity(waitingInfo.getEventName()).startAfterActivity(waitingInfo.getEventName()).executeAsync(false,false);
                    } catch (Exception e) {
                        log.info("Exception occured in ", e);
                    }
                }
                waitingInfoRepository.deletebySeqId(waitingInfo.getSeqId());
            }
        }
    }

    private OrderFlowContext getOrderFlowContextFromExecution(Incident incident) {
        OrderFlowContext orderFlowContext = null;
        try {
            orderFlowContext = objectMapper.readValue(runtimeService.getVariable(incident.getExecutionId(), WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString()).toString(), OrderFlowContext.class);
        } catch (Exception e) {
            throw new CommonException("couldn't obtain order flow context");
        }
        return orderFlowContext;

    }

    protected void retryExecution(Incident incident, String suborderId, String stageId) {
        log.info(" retrying the instance :: {}", incident.getId());
        if (StringUtils.isNotEmpty(stageId) && stageId.startsWith("SOM")) {
            orderStatusManager.updateRollBackStatusForSOM(suborderId, stageId);
        }
        orderStageService.updateModifiedUser(MDC.get(ApiConstants.USER_NAME), suborderId, stageId);
        var jobList = managementService.createJobQuery().processInstanceId(incident.getProcessInstanceId()).noRetriesLeft().list();
        if (ObjectUtils.isEmpty(jobList))
            throw new CommonException(StatusConstants.HttpConstants.INTERNAL_SERVER_ERROR, "No jobs available for the processInstanceId: " + incident.getProcessInstanceId());
        jobList.stream().filter(job -> ObjectUtils.isNotEmpty(job)).forEach(job -> {
            managementService.setJobRetries(job.getId(), 1);
        });
    }

    protected void skipExecution(Incident incident, String orderType, String activityId, WorkflowRequest request, String stageCode) {
        log.info(" Enabled skip handler so calling process instance modification :: {}", incident.getActivityId());
        if (StringUtils.equals(incident.getActivityId(), activityId) && StringUtils.isNotEmpty(incident.getProcessDefinitionId())) {

            if (!isSkipEnabled(activityId, orderType))
                throw new CommonException(StatusConstants.HttpConstants.INTERNAL_SERVER_ERROR, "Skip featute not available for this stage");
            var configValue = getConfigValue("ERROR_CODE_FOR_SUBSCRIPTION_ID_SYNC");
            if (ObjectUtils.isNotEmpty(configValue) && (configValue.stream().anyMatch(value -> incident.getIncidentMessage().contains(value)))) {
                findSubscriptionIdSyncReqdByStageCode(incident, stageCode);
            }
            skipHandlerExecution(incident, request, activityId, stageCode);
        }
    }

    protected void findSubscriptionIdSyncReqdByStageCode(Incident incident, String stageCode) {
        var subscriptionSyncReqd = false;
        if (stageCode.startsWith("NCC")) {
            subscriptionSyncReqd = checkStageCode(stageCode, "NCC_STAGE_CODE_FOR_SUBSCRIPTIONID");
        } else if (stageCode.startsWith("SM")) {
            subscriptionSyncReqd = checkStageCode(stageCode, "SM_STAGE_CODE_FOR_SUBSCRIPTIONID");
        }
        if (subscriptionSyncReqd) {
            var orderFlowContext = getOrderFlowContextFromExecution(incident);
            var orderId = Long.parseLong(orderFlowContext.getOrder().getOrderId());
            var createdDate = orderRepository.findOrderCreationDate(orderId);
            log.info("EnrichmentResults ::: {}", JsonUtils.toJsonString(orderFlowContext.getEnrichmentResults()));
            subscriptionSyncHandler.fetchSubscriptionId(orderFlowContext, stageCode, createdDate);
            try {
                runtimeService.setVariable(incident.getExecutionId(), WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString(), SpinValues.jsonValue(objectMapper.writeValueAsString(orderFlowContext)).create());
            } catch (Exception e) {
                log.info("Exception while setting orderFlowContext to DelegateExecution", e);
                throw new CommonException("Exception while setting orderFlowContext to DelegateExecution" + e.getMessage());
            }
        }

    }

    protected boolean checkStageCode(String stageCode, String key) {
        var configValue = getConfigValue(key);
        return configValue.contains(stageCode);
    }

    protected List<String> getConfigValue(String key) {
        var appConfig = cache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG, key);
        if (ObjectUtils.isNotEmpty(appConfig)) {
            return Arrays.asList(appConfig.getNgTableData().get("CONFIG_VALUE").split(","));
        }
        return null;

    }


    protected void skipHandlerExecution(Incident incident, WorkflowRequest request, String activityId, String stageCode) {
        var foundService = false;
        while (!foundService) {
            ModelElementInstance modelElementById = repositoryService.getBpmnModelInstance(incident.getProcessDefinitionId()).getModelElementById(activityId);
            FlowNode node = (FlowNode) modelElementById;
            forLoop:
            for (SequenceFlow flow : node.getOutgoing()) {
                var flowElement = flow.getTarget().getElementType().getTypeName();
                var flowElementId = flow.getTarget().getId();
                var input = "";
                log.info(" Target activity Id :: {} :::: Type :: {} ", flowElementId, flowElement);
                if (flowElement.equalsIgnoreCase(WorkFlowConstants.BpmnConstants.RECEIVE_TASK_ACTIVITY.getStringValue())) {
                    if (flow.getTarget().getExtensionElements().getElementsQuery() != null) {
                        CamundaProperties singleResult = flow.getTarget().getExtensionElements().getElementsQuery().filterByType(CamundaProperties.class).singleResult();
                        if (singleResult != null) {
                            input = Objects.requireNonNull(singleResult.getCamundaProperties().stream().map(property -> property.getCamundaName()).findAny().orElse(null));
                        }
                    }
                }
                var processInstanceModificationBuilder = runtimeService.createProcessInstanceModification(incident.getProcessInstanceId()).cancelAllForActivity(incident.getActivityId());
                if (!flowElement.equals(WorkFlowConstants.BpmnConstants.INTERMEDIATE_THROW_EVENT_ACTIVITY.getStringValue()) && !flow.getTarget().getElementType().getTypeName().equals(WorkFlowConstants.BpmnConstants.END_EVENT.getStringValue()))
                    activityId = flow.getTarget().getId();
                switch (flowElement) {
                    case "serviceTask":
                        foundService = skipIfServiceActivity(incident.getActivityId(), processInstanceModificationBuilder, request);
                        break forLoop;
                    case "receiveTask":
                        foundService = skipIfReceiveActivity(flowElementId, processInstanceModificationBuilder, input, request, activityId);
                        break forLoop;
                    case "callActivity":
                        foundService = skipIfCallActivity(flowElementId, processInstanceModificationBuilder, incident, request);
                        break forLoop;
                    case "endEvent":
                        foundService = skipIfEndActivity(flowElementId, processInstanceModificationBuilder, incident);
                        if (foundService) break forLoop;
                        else break;
                    default:
                        break;
                }
            }
            if (foundService) {
                var orderFlowContext = getOrderFlowContextFromExecution(incident);
                orderFlowContext.getAttributes().put("skipUser", MDC.get(ApiConstants.USER_NAME));
                orderStatusManager.processStatusUpdates(orderFlowContext, incident.getActivityId(), "SERVICE_TASK", true, WorkFlowConstants.StageStatusConstants.STAGE_STATUS_SKIPPED.getValue());
                if (stageCode.startsWith("SOM") && !stageCode.contains("FETCH"))
                    notificationUtils.sendNotification(orderFlowContext, AppConstants.NotificationMessageTypes.WELCOME_NOTIFICATION.value(), "SUB_ORDER");

                if (stageCode.equalsIgnoreCase("APPROVAL")) {
                    orderApprovalService.processApprovalOrder(orderFlowContext.getOrder().getOrderId(), true);
                }
            }

        }
    }

    protected boolean skipIfCallActivity(String flowElementId, ProcessInstanceModificationBuilder processInstanceModificationBuilder, Incident incident, WorkflowRequest request) {
        processInstanceModificationBuilder.startAfterActivity(incident.getActivityId()).executeAsync(true, false);
        return true;
    }

    protected boolean skipIfReceiveActivity(String flowElementId, ProcessInstanceModificationBuilder processInstanceModificationBuilder, String input, WorkflowRequest request, String activityId) {
        if ("linkedServiceTask".equalsIgnoreCase(input)) {
            processInstanceModificationBuilder.startAfterActivity(flowElementId).executeAsync(false, false);
            return true;
        } else {
            processInstanceModificationBuilder.startAfterActivity(activityId).executeAsync(true, false);
            return true;
        }
    }

    protected boolean skipIfServiceActivity(String activityId, ProcessInstanceModificationBuilder processInstanceModificationBuilder, WorkflowRequest request) {
        processInstanceModificationBuilder.startAfterActivity(activityId).executeAsync(false, false);
        return true;
    }

    protected boolean skipIfEndActivity(String flowElementId, ProcessInstanceModificationBuilder processInstanceModificationBuilder, Incident incident) {
        if (flowElementId.startsWith(WorkFlowConstants.BpmnConstants.SUBPROCESS_END_EVENT.getStringValue()) || flowElementId.startsWith(WorkFlowConstants.BpmnConstants.ORDER_EXEC_END.getStringValue())) {
            runtimeService.setVariableLocal(incident.getExecutionId(), "isEndEventSkipped", true);
            processInstanceModificationBuilder.startBeforeActivity(flowElementId).executeAsync();
            return true;
        }
        return false;
    }

    public boolean isSkipEnabled(String activityId, String orderType) {
        boolean skipEnabled = false;
        var workflowRetryConfigDto = cache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_COM_WORKFLOW_RETRY_CONFIG, orderType + "_" + activityId);
        if (ObjectUtils.isEmpty(workflowRetryConfigDto)) {
            workflowRetryConfigDto = cache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_COM_WORKFLOW_RETRY_CONFIG, orderType + "-Rollback" + "_" + activityId);
        }
        if (workflowRetryConfigDto == null)
            log.info("unable to find the skip configration for activity:{} and order type:{}", activityId, orderType);
        if (workflowRetryConfigDto != null) {
            var skipEnabledConfig = workflowRetryConfigDto.getNgTableData().get("SKIP_ENABLE");
            if (BooleanUtils.toBoolean(skipEnabledConfig)) skipEnabled = true;
        }
        return skipEnabled;
    }

    public String getStageConfig(OrderFlowContext orderFlowContext, WorkflowRequest request) {
        var orderStageConfigDTO = cache.getCacheDetailsFromDBMap("STAGE_CONFIG_BY_ORDER_TYPE_AND_STAGE_ID", orderFlowContext.getOrder().getOrderType() + "_" + request.getStageId());
        if (ObjectUtils.isEmpty(orderStageConfigDTO)) {
            orderStageConfigDTO = cache.getCacheDetailsFromDBMap("STAGE_CONFIG_BY_ORDER_TYPE_AND_STAGE_ID", orderFlowContext.getOrder().getOrderType() + "-Rollback" + "_" + request.getStageId());
        }
        var activityId = "";
        if (ObjectUtils.isNotEmpty(orderStageConfigDTO) && StringUtils.isNotEmpty(orderStageConfigDTO.getNgTableData().get(NGTableColumnConstants.COLUMN_LINKED_ACTIVITY))) {
            activityId = orderStageConfigDTO.getNgTableData().get(NGTableColumnConstants.COLUMN_LINKED_ACTIVITY);
        }
        return activityId;
    }

    public Set<Orders> processBulkOrderRetry(WorkflowRequest request, Set<Orders> validOrders, Set<Orders> inValidOrders) {

        for (Orders order : validOrders) {
            Orders inValidOrder = new Orders();
            Set<SubOrders> retrySubOrderIds = new HashSet<>();
            Set<SubOrders> inValidSubOrder = new HashSet<>();
            var orderType = order.getOrderType();
            var orderFlowContext = initOrderFlowContextForBulkSkip(orderType, order);

            if (ObjectUtils.isNotEmpty(order.getSubOrders())) {
                retrySubOrderIds = order.getSubOrders();
                for (SubOrders subOrder : retrySubOrderIds) {
                    SubOrders subOrders = new SubOrders();
                    orderFlowContext.getAttributes().put(GenericConstants.SUB_ORDER_ID, subOrders.getSubOrderId());

                    var stageCode = getStageCodeBasedOnOrderAndSubOrder(order.getOrderId(), subOrder.getSubOrderId());
                    var activityId = getActivityId(orderType, stageCode);
                    var retryRequestId = getRetryRequestId(order.getOrderId(), subOrder.getSubOrderId(), stageCode, orderType);
                    var piList = runtimeService.createProcessInstanceQuery().processInstanceBusinessKey(order.getOrderId() + "_" + subOrder.getSubOrderId()).list();
                    log.info("process instance list when querying by order id {} and sub order id", piList);
                    if (ObjectUtils.isEmpty(piList)) {
                        piList = runtimeService.createProcessInstanceQuery().processInstanceBusinessKey(order.getOrderId()).list();
                        log.info("process instance list when querying by order id {}", piList);
                    }
                    if (ObjectUtils.isEmpty(piList)) {
                        if (activityId.equalsIgnoreCase("Approval")) {
                            orderFlowContext.getAttributes().put("skipUser", MDC.get(ApiConstants.USER_NAME));
                            orderStatusManager.processStatusUpdates(orderFlowContext, "Approval", "SERVICE_TASK", true, WorkFlowConstants.StageStatusConstants.STAGE_STATUS_SKIPPED.getValue());
                            orderApprovalService.processApprovalOrder(order.getOrderId(), true);
                        }

                        if (StringUtils.isNotEmpty(subOrder.getSubOrderId())) {
                            inValidOrder.setOrderId(order.getOrderId());
                            subOrders.setSubOrderId(subOrder.getSubOrderId());
                            inValidSubOrder.add(subOrders);
                            inValidOrder.setStatusReason("No process instance is available for the orderId " + order.getOrderId());
                            inValidOrder.setSubOrders(inValidSubOrder);
                        }
                    } else {
                        log.info(" process instance modification started ");
                        piList.stream().filter(processInstance -> StringUtils.isNotEmpty(processInstance.getId())).forEach(processInstance -> {
                            log.info(" Process Instance Id :: {} :: Business Key :: {}", processInstance.getId(), processInstance.getBusinessKey());
                            var incident = runtimeService.createIncidentQuery().incidentType("failedJob").processInstanceId(processInstance.getId()).singleResult();
                            if (ObjectUtils.isEmpty(incident)) {
                                if (activityId.startsWith("SOM")) {
                                    skipRetrySOMExecution(processInstance, activityId, order.getOrderId(), subOrder.getSubOrderId(), request.getAction(), stageCode);
                                } else {
                                    if (StringUtils.isNotEmpty(subOrder.getSubOrderId())) {
                                        inValidOrder.setOrderId(order.getOrderId());
                                        subOrders.setSubOrderId(subOrder.getSubOrderId());
                                        subOrders.setStatusReason("No Incident available for the combination of orderId " + order.getOrderId() + " and subOrderId " + subOrder.getSubOrderId());
                                        inValidSubOrder.add(subOrders);
                                        inValidOrder.setSubOrders(inValidSubOrder);
                                    }
                                    log.info("No Incident available for this orderId : {} and subOrderId : {} combination", order.getOrderId(), subOrder.getSubOrderId());
                                }
                            } else if (StringUtils.isNotEmpty(incident.getJobDefinitionId())) {
                                if (request.getAction().equalsIgnoreCase(ProcessConstants.ACTION_RETRY.desc)) {
                                    if (StringUtils.isNotEmpty(retryRequestId))
                                        runtimeService.setVariable(processInstance.getId(), "retryRequestId", retryRequestId);
                                    retryExecution(incident, subOrder.getSubOrderId(), stageCode);
                                } else {
                                    if (StringUtils.isNotEmpty(retryRequestId)) {
                                        runtimeService.removeVariable(processInstance.getId(), "retryRequestId");
                                        orderStageService.deleteRetryRequestIdAttributes(order.getOrderId(), subOrder.getSubOrderId());
                                    }
                                    skipExecution(incident, orderType, activityId, request, stageCode);
                                }
                            }
                        });
                    }
                }
            } else {
                inValidOrder.setOrderId(order.getOrderId());
            }
            if (ObjectUtils.isNotEmpty(inValidOrder) && ObjectUtils.isNotEmpty(inValidOrder.getOrderId())) {
                inValidOrders.add(inValidOrder);
            }

        }
        return inValidOrders;
    }

    private String getStageCodeBasedOnOrderAndSubOrder(String orderId, String subOrderId) {
        return orderStageRepository.getStageIdByOrderIdandSubOrderId(Long.valueOf(orderId), Long.valueOf(subOrderId));
    }

    private String getActivityId(String orderType, String stageCode) {

        var orderStageConfigDTO = cache.getCacheDetailsFromDBMap("STAGE_CONFIG_BY_ORDER_TYPE_AND_STAGE_ID", orderType + "_" + stageCode);
        if (ObjectUtils.isEmpty(orderStageConfigDTO)) {
            orderStageConfigDTO = cache.getCacheDetailsFromDBMap("STAGE_CONFIG_BY_ORDER_TYPE_AND_STAGE_ID", orderType + "-Rollback" + "_" + stageCode);
        }
        var activityId = "";
        if (ObjectUtils.isNotEmpty(orderStageConfigDTO) && StringUtils.isNotEmpty(orderStageConfigDTO.getNgTableData().get(NGTableColumnConstants.COLUMN_LINKED_ACTIVITY))) {
            activityId = orderStageConfigDTO.getNgTableData().get(NGTableColumnConstants.COLUMN_LINKED_ACTIVITY);
        }
        return activityId;

    }

    private OrderFlowContext initOrderFlowContextForBulkSkip(String orderType, Orders order) {
        var orderFlowContext = new OrderFlowContext();
        var ordr = new Order();
        ordr.setOrderType(orderType);
        ordr.setOrderId(order.getOrderId());
        orderFlowContext.setOrder(ordr);
        orderFlowContext.getAttributes().put("skipUser", MDC.get(ApiConstants.USER_NAME));
        return orderFlowContext;
    }
}
