package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.util.LinkedHashMap;

import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.ProcessVariableUtils;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderStatusManager;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.RuntimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.EnrichmentFailedException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderEnrichment;
import in.co.sixdee.bss.om.model.dto.order.Cart;

@Component(value = "changePlanService")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class ChangePlanService extends AbstractDelegate {

	@Autowired
	protected RuntimeService	runtimeService;

	@Autowired
	protected OrderEnrichment	orderEnrichment;

	@Autowired
	protected OrderStatusManager orderStatusManager;

	@Autowired
	protected ProcessVariableUtils processVariableUtils;

	@Override
	protected void execute() throws Exception {

		var orderFlowContext = SerializationUtils.clone(executionContext);
		var changePlanOrderId = executionContext.getAttributes().get(GenericConstants.CHANGEPLAN_ORDER_ID);
		fetchParamsFromRequest(orderFlowContext);
		setChangePlanParams(changePlanOrderId, orderFlowContext);
		orderStatusManager.updateOrderParams(changePlanOrderId, executionContext.getAttributes().get(GenericConstants.PROFILE_ID),
				executionContext.getAttributes().get(GenericConstants.ACCOUNT_ID));
		orderEnrichment.enrichOrder(orderFlowContext);
		if (executionContext.isEnrichmentError()) {
			throw new EnrichmentFailedException(executionContext.getErrorCode(), executionContext.getEnrichmentFailureReason());
		}
		var executionVariables=setWorkflowVariables(orderFlowContext);
		runtimeService.startProcessInstanceByKey("ChangeSubscription", changePlanOrderId, executionVariables);
	}

	private void fetchParamsFromRequest(OrderFlowContext orderFlowContext) {
		if(StringUtils.isNotEmpty(executionContext.getOrder().getServiceManagement().getDestinationProfileId())) {
			executionContext.getAttributes().put(GenericConstants.PROFILE_ID, executionContext.getOrder().getServiceManagement().getDestinationProfileId());
			orderFlowContext.getAttributes().put(GenericConstants.PROFILE_ID, executionContext.getOrder().getServiceManagement().getDestinationProfileId());

		}
		if(StringUtils.isNotEmpty(executionContext.getOrder().getServiceManagement().getDestinationAccountId())) {
			executionContext.getAttributes().put(GenericConstants.ACCOUNT_ID, executionContext.getOrder().getServiceManagement().getDestinationAccountId());
			orderFlowContext.getAttributes().put(GenericConstants.ACCOUNT_ID, executionContext.getOrder().getServiceManagement().getDestinationAccountId());

		}
	}

	private void setChangePlanParams(String changePlanOrderId, OrderFlowContext orderFlowContext) {
		var changePlanSubOrderId = executionContext.getAttributes().get(GenericConstants.CHANGEPLAN_SUB_ORDER_ID);
		orderFlowContext.getAttributes().put(GenericConstants.ORDER_ID, changePlanOrderId);
		orderFlowContext.getAttributes().put(GenericConstants.SUB_ORDER_ID, changePlanSubOrderId);
		orderFlowContext.getOrder().setOrderId(changePlanOrderId);
		orderFlowContext.getOrder().setOrderType(OrderTypes.CHANGE_SUBSCRIPTION);
		orderFlowContext.getOrder().setDescription("Change Subscription order");
		// since deposits and credit limits are updated as part of TOS main flow 
		orderFlowContext.getOrder().getServiceManagement().setDeposit(null);
		orderFlowContext.getOrder().getServiceManagement().setDepositReqd(null);
		orderFlowContext.getOrder().getServiceManagement().setCreditLimit(null);
		orderFlowContext.getOrder().getServiceManagement().setCreditLimitUpdateReqd(null);

		var newCart = new Cart();
		newCart.setSubscriptions(executionContext.getOrder().getServiceManagement().getSubscriptions());
		orderFlowContext.getOrder().getServiceManagement().setNewCart(newCart);
		orderFlowContext.getOrder().getServiceManagement().setSubscriptions(null);
	}

	private LinkedHashMap<String,Object> setWorkflowVariables(OrderFlowContext orderFlowContext) {
		orderFlowContext.setWorkflowData(new LinkedHashMap<>());
		orderFlowContext.getWorkflowData().put("enrichmentResults", executionContext.getEnrichmentResults());
		executionVariables = new LinkedHashMap<String,Object>();
		executionVariables.put("sndWorkflowTrigger", false);
		executionVariables.put("erpDeposit", false);
		executionVariables.put("priority", execution.getVariable("priority"));
		executionVariables.put(WorkFlowConstants.WorkFlowProcessVariables.STATUS.toString(), WorkFlowConstants.WorkFlowProcessVariables.STATUS_SUCCESS.toString());
		executionVariables.put(WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString(),processVariableUtils.createWorkflowData(orderFlowContext));
		processVariableUtils.findChangeSubscriptionProcessVariables(orderFlowContext,executionVariables);
		return (LinkedHashMap<String, Object>) executionVariables;
	}

}
