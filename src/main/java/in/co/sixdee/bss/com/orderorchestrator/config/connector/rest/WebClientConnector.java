package in.co.sixdee.bss.com.orderorchestrator.config.connector.rest;

import in.co.sixdee.bss.om.model.dto.CallThirdPartyDTO;
import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClient.RequestBodySpec;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

import java.net.URI;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Log4j2
@Component
public class WebClientConnector {

	@Autowired
	private WebClient.Builder webClientBuilder;

	public CallThirdPartyDTO service(String transportMethod, URI uri, int readTimeout, int connectionTimeout, String request, Map<String, String> headers) {
		CallThirdPartyDTO callThirdPartyDTO = null;
		switch (transportMethod) {
			case "POST":
				callThirdPartyDTO = doPost(uri, readTimeout, connectionTimeout, request, headers);
				break;
			case "GET":
				callThirdPartyDTO = doGet(uri, readTimeout, connectionTimeout, headers);
				break;
			case "PUT":
				callThirdPartyDTO = doPut(uri, readTimeout, connectionTimeout, request, headers);
				break;
			case "PATCH":
				callThirdPartyDTO = doPatch(uri, readTimeout, connectionTimeout, request, headers);
				break;
			case "DELETE":
				callThirdPartyDTO = doDelete(uri, readTimeout, connectionTimeout, request, headers);
		}
		return callThirdPartyDTO;
	}


	public CallThirdPartyDTO doGet(URI uri, int readTimeout, int connectionTimeout, Map<String, String> headers) {
		log.info("Sending GET Request, URL: {} Http Headers: {}", uri, headers);
		var clientResponse = buildWebClient(readTimeout, connectionTimeout).get().uri(uri)
				.headers(httpHeaders -> setHttpHeaders(httpHeaders, headers)).exchange().block();
		return populateDtoFromClientResponse(clientResponse);
	}


	public CallThirdPartyDTO doPost(URI uri, int readTimeout, int connectionTimeout, String request, Map<String, String> headers) {
		log.info("Sending POST Request, Payload: {}  URL: {} Http Headers: {}", request, uri,
				headers);
		var clientResponse = buildWebClient(readTimeout, connectionTimeout).post().uri(uri)
				.headers(httpHeaders -> setHttpHeaders(httpHeaders, headers)).body(Mono.just(request), String.class).exchange()
				.block();
		return populateDtoFromClientResponse(clientResponse);
	}


	public CallThirdPartyDTO doPut(URI uri, int readTimeout, int connectionTimeout, String request, Map<String, String> headers) {
		log.info("Sending PUT Request, Payload: {}  URL: {} Http Headers: {}", request, uri,
				headers);
		var clientResponse = buildWebClient(readTimeout, connectionTimeout).put().uri(uri)
				.headers(httpHeaders -> setHttpHeaders(httpHeaders, headers)).body(Mono.just(request), String.class).exchange()
				.block();
		return populateDtoFromClientResponse(clientResponse);
	}


	public CallThirdPartyDTO doPatch(URI uri, int readTimeout, int connectionTimeout, String request, Map<String, String> headers) {
		log.info("Sending Patch Request, Payload: {}  URL: {} Http Headers: {}", request, uri,
				headers);
		var clientResponse = buildWebClient(readTimeout, connectionTimeout).patch().uri(uri)
				.headers(httpHeaders -> setHttpHeaders(httpHeaders, headers)).body(Mono.just(request), String.class).exchange()
				.block();
		return populateDtoFromClientResponse(clientResponse);
	}


	public CallThirdPartyDTO doDelete(URI uri, int readTimeout, int connectionTimeout, String request,
									  Map<String, String> headers) {
		ClientResponse clientResponse = null;
		if (StringUtils.isNotEmpty(request)) {
			log.info("Sending Delete Request, Payload: {} URL: {} Http Headers: {}", request,
					uri, headers);
			clientResponse = ((RequestBodySpec) buildWebClient(readTimeout, connectionTimeout).delete().uri(uri)
					.headers(httpHeaders -> setHttpHeaders(httpHeaders, headers))).body(Mono.just(request), String.class)
					.exchange().block();
		} else {
			log.info("Sending Delete Request,  URL: {} Http Headers: {}",
					uri, headers);
			clientResponse = ((RequestBodySpec) buildWebClient(readTimeout, connectionTimeout).delete().uri(uri)
					.headers(httpHeaders -> setHttpHeaders(httpHeaders, headers))).exchange().block();
		}


		return populateDtoFromClientResponse(clientResponse);
	}

	private CallThirdPartyDTO populateDtoFromClientResponse(ClientResponse clientResponse) {
		if (clientResponse == null)
			return new CallThirdPartyDTO();
		var thirdPartyDto = new CallThirdPartyDTO();
		thirdPartyDto.setResponse(clientResponse.bodyToMono(String.class).block());
		thirdPartyDto.setResponseCode(String.valueOf(clientResponse.statusCode().value()));
		thirdPartyDto.setResponseMessage(clientResponse.statusCode().toString());
		return thirdPartyDto;
	}

	private WebClient buildWebClient(int readTimeout, int connectionTimeout) {
		var httpClient = HttpClient.create()
				.tcpConfiguration(client -> client.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectionTimeout)
						.doOnConnected(conn -> conn.addHandlerLast(new ReadTimeoutHandler(readTimeout, TimeUnit.MILLISECONDS)))).wiretap(true);
		return webClientBuilder.clientConnector(new ReactorClientHttpConnector(httpClient)).build();
		/*return webClientBuilder.clientConnector(new ReactorClientHttpConnector(httpClient)).filter(logRequest())
				.filter(logResponse()).build();*/
	}


	private void setHttpHeaders(HttpHeaders httpHeaders, Map<String, String> headers) {
		if (headers == null || headers.isEmpty())
			return;
		for (Map.Entry<String, String> entry : headers.entrySet()) {
			httpHeaders.add(entry.getKey(), entry.getValue());
		}
	}

	private ExchangeFilterFunction logRequest() {
		return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
			log.info("Sending {} Request. URL: {}, Headers: {}, Body: {}",
					clientRequest.method(), clientRequest.url(), clientRequest.headers(),
					String.valueOf(clientRequest.body()));
			return Mono.just(clientRequest);
		});
	}

	private ExchangeFilterFunction logResponse() {
		return ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {
			log.info("Received response. Status Code: {}, Headers: {}, Body: {}",
					clientResponse.statusCode(), clientResponse.headers(),
					clientResponse.bodyToMono(String.class));
			return Mono.just(clientResponse);
		});
	}

	public Mono<? extends String> doPost(URI url) {
		return buildWebClient(3000, 3000).post().uri(url).exchange()
				.flatMap(clientResponse -> clientResponse.bodyToMono(String.class));
	}


	public Mono<String> postRequestAsync(URI uri, int readTimeout, int connectionTimeout, String request, Map<String, String> headers) {
		Mono<String> response = buildWebClient(readTimeout, connectionTimeout).post().uri(uri)
				.headers(httpHeaders -> setHttpHeaders(httpHeaders, headers)).accept(MediaType.APPLICATION_JSON)
				.bodyValue(request).retrieve().onStatus(HttpStatusCode::is4xxClientError, notFount -> {
					Mono<String> errorMessage = notFount.bodyToMono(String.class);
					return errorMessage.flatMap(message -> {
						throw new RuntimeException("4xx from target source");
					});
				}).onStatus(HttpStatusCode::is5xxServerError, errorResponse -> {
					Mono<String> errorMessage = errorResponse.bodyToMono(String.class);
					return errorMessage.flatMap(message -> {
						throw new RuntimeException("5xx from target source");
					});
				}).bodyToMono(String.class);
		response.subscribe(log::info);
		return response;
	}
}
