package in.co.sixdee.bss.com.orderorchestrator.config;

import in.co.sixdee.bss.common.constants.SystemConstants;
import in.co.sixdee.bss.common.logging.aop.LoggingAspect;
import in.co.sixdee.bss.common.logging.aop.PerformanceLoggingAspect;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;

@Configuration
@EnableAspectJAutoProxy
public class LoggingAspectConfiguration {

	@Bean
	public LoggingAspect loggingAspect(Environment environment) {
		return new LoggingAspect(environment);
	}

	//@Bean
	//@Profile(SystemConstants.SPRING_PROFILE_DEVELOPMENT)
	public PerformanceLoggingAspect performanceLoggingAspect(Environment environment) {
		return new PerformanceLoggingAspect(environment);
	}


}
