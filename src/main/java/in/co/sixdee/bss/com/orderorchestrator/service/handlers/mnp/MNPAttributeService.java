package in.co.sixdee.bss.com.orderorchestrator.service.handlers.mnp;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderAttributeEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderAttributeRepository;
import in.co.sixdee.bss.common.util.SequenceGenerator;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Order;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Log4j2
public class MNPAttributeService {


    final OrderAttributeRepository orderAttributeRepository;


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void savePortInTypeAsOrderAttribute(String portInType, OrderFlowContext executionContext) {
        OrderAttributeEntity orderAttribute = new OrderAttributeEntity();
        orderAttribute.setSeqId(SequenceGenerator.getSequencerInstance().nextId());
        orderAttribute.setType("String");
        orderAttribute.setKey("portInType");
        orderAttribute.setValue(portInType);
        orderAttribute.setCreatedBy(executionContext.getUsername());
        orderAttribute.setOrderId(Long.valueOf(executionContext.getAttributes().get("orderId")));
        orderAttributeRepository.save(orderAttribute);

    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveMNPReqAndRefIdAsAttributes(OrderFlowContext executionContext, boolean isRetry) {

        String mnpRequestId = executionContext.getAttributes().get(MNPConstants.MNP_REQUEST_ID);
        String mnpReferenceId = executionContext.getAttributes().get(MNPConstants.MNP_REFERENCE_ID);
        if (isRetry) {
            updateAttributes(executionContext, mnpRequestId, mnpReferenceId);
        } else {
            var mnpRequestIdAttr = createMNPAttribute(MNPConstants.MNP_REQUEST_ID, mnpRequestId, executionContext);
            var mnpReferenceIdAttr = createMNPAttribute(MNPConstants.MNP_REFERENCE_ID, mnpReferenceId, executionContext);
            List<OrderAttributeEntity> orderAttributes = new ArrayList<OrderAttributeEntity>();
            orderAttributes.add(mnpRequestIdAttr);
            orderAttributes.add(mnpReferenceIdAttr);
            saveAttributes(orderAttributes);
        }


    }

    private void saveAttributes(List<OrderAttributeEntity> orderAttrList) {
        log.info("Saving mnp request id and mnp reference id as new attributes");
        orderAttributeRepository.saveAll(orderAttrList);
    }

    private void updateAttributes(OrderFlowContext executionContext, String mnpRequestId, String mnpReferenceId) {
        log.info("Updating the values of mnp request id and mnp reference id in COM_ORDER_ATTRIBUTES_TABLE");
        long orderId = Long.parseLong(executionContext.getAttributes().get("orderId"));
        long subOrderId = Long.parseLong(executionContext.getAttributes().get("subOrderId"));
        orderAttributeRepository.updateAttributeByOrderId(orderId, subOrderId, MNPConstants.MNP_REQUEST_ID, mnpRequestId);
        orderAttributeRepository.updateReferenceIdByOrderId(orderId, subOrderId, MNPConstants.MNP_REFERENCE_ID, mnpReferenceId);

    }


    private OrderAttributeEntity createMNPAttribute(String key, String value, OrderFlowContext executionContext) {
        var orderAttribute = new OrderAttributeEntity();
        orderAttribute.setSeqId(SequenceGenerator.getSequencerInstance().nextId());
        orderAttribute.setType("String");
        orderAttribute.setKey(key);
        orderAttribute.setValue(value);
        orderAttribute.setCreatedBy(executionContext.getUsername());
        orderAttribute.setOrderId(Long.valueOf(executionContext.getAttributes().get("orderId")));
        orderAttribute.setSubOrderId(Long.valueOf(executionContext.getAttributes().get("subOrderId")));
        if (MNPConstants.MNP_REFERENCE_ID.equals(key))
            orderAttribute.setReferenceId(value);
        return orderAttribute;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveOperatorCodesInAttributes(OrderFlowContext executionContext) {
        var recipientOperatorEntity = createMNPAttribute(MNPConstants.RECIPIENT_OPERATOR_CODE, executionContext.getAttributes().get(MNPConstants.RECIPIENT_OPERATOR_CODE), executionContext);
        var ownerOperatorEntity = createMNPAttribute(MNPConstants.OWNER_OPERATOR_CODE, executionContext.getAttributes().get(MNPConstants.OWNER_OPERATOR_CODE), executionContext);
        var donorOperatorEntity = createMNPAttribute(MNPConstants.DONOR_OPERATOR_CODE, executionContext.getAttributes().get(MNPConstants.DONOR_OPERATOR_CODE), executionContext);
        var recipientOperatorNameEntity = createMNPAttribute(MNPConstants.RECIPIENT_OPERATOR_NAME, executionContext.getAttributes().get(MNPConstants.RECIPIENT_OPERATOR_NAME), executionContext);
        var ownerOperatorNameEntity = createMNPAttribute(MNPConstants.OWNER_OPERATOR_NAME, executionContext.getAttributes().get(MNPConstants.OWNER_OPERATOR_NAME), executionContext);
        var donorOperatorNameEntity = createMNPAttribute(MNPConstants.DONOR_OPERATOR_NAME, executionContext.getAttributes().get(MNPConstants.DONOR_OPERATOR_NAME), executionContext);
        orderAttributeRepository.saveAll(List.of(recipientOperatorEntity, ownerOperatorEntity, donorOperatorEntity, donorOperatorNameEntity, ownerOperatorNameEntity, recipientOperatorNameEntity));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void createAndSaveMNPAttribute(String attrKey, String attrValue, OrderFlowContext executionContext) {
        var attrEntity = createMNPAttribute(attrKey, attrValue, executionContext);
        orderAttributeRepository.save(attrEntity);
    }
}
