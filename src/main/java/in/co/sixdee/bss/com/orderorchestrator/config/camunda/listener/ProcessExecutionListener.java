/**
 *
 */
package in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener;

import com.bazaarvoice.jolt.JsonUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.config.notification.NotificationUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.ActivityExecutionEnd;
import in.co.sixdee.bss.com.orderorchestrator.service.ActivityExecutionStart;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderStatusManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Log4j2
@RequiredArgsConstructor
public class ProcessExecutionListener {

    protected final ProcessDataAccessor processDataAccessor;

    protected final ActivityExecutionEnd activityExecutionEnd;

    protected final ActivityExecutionStart activityExecutionStart;

    protected final OrderStatusManager orderStatusManager;

    protected final NotificationUtils notificationUtils;

    @EventListener(condition = "#execution != null && #execution.bpmnModelElementInstance.elementType.typeName=='serviceTask' && #execution.eventName=='end'")
    public void onServiceTaskExecutionEnd(DelegateExecution execution) {
        var activityId = execution.getCurrentActivityId();
		/*if ("CreateInstancesTask,GenerateDataDelegateTask,CheckFinishedInstancesTask".contains(activityId)) {
			log.debug("Execution listeners are not required for activity: {}", activityId);
			return;
		}*/
        if (execution.getVariable("isEndEventSkipped") != null && (boolean) execution.getVariable("isEndEventSkipped")) {
            execution.getProcessEngine().getRuntimeService().removeVariableLocal(execution.getId(), "isEndEventSkipped");
            return;
        }
        //activityExecutionEnd.closeActivity(execution, GenericConstants.ACTIVITY_TYPE_SERVICE_TASK);
        log.info("Execution finished for activity :: {} ", activityId);
    }

    @EventListener(condition = "#execution != null && #execution.bpmnModelElementInstance.elementType.typeName=='serviceTask' && #execution.eventName=='start'")
    public void onServiceTaskExecutionStart(DelegateExecution execution) {
        var activityId = execution.getCurrentActivityId();
        log.info("Execution started for activity {}", activityId);
        if ("CreateInstancesTask,GenerateDataDelegateTask,CheckFinishedInstancesTask".contains(activityId)) {
            log.debug("execution listeners are not required for activity: {}", activityId);
            return;
        }
        activityExecutionStart.startActivity(execution, GenericConstants.ACTIVITY_TYPE_SERVICE_TASK);
    }

    @EventListener(condition = "#delegateExecution != null && #delegateExecution.bpmnModelElementInstance != null && #delegateExecution.bpmnModelElementInstance.elementType !=null && "
            + "(#delegateExecution.bpmnModelElementInstance.elementType.typeName=='callActivity' || #delegateExecution.bpmnModelElementInstance.elementType.typeName=='subProcess') &&"
            + " #delegateExecution.eventName=='start'")
    public void onCallActivityExecutionStart(DelegateExecution delegateExecution) {
        log.info("delegateExecution  " + delegateExecution);
        activityExecutionStart.startActivity(delegateExecution, GenericConstants.ACTIVITY_TYPE_SUB_PROCESS);
    }


    @EventListener(condition = "#executionDelegate != null && #executionDelegate.bpmnModelElementInstance != null && #executionDelegate.bpmnModelElementInstance.elementType !=null "
            + "&& #executionDelegate.bpmnModelElementInstance.elementType.typeName=='startEvent'&& #executionDelegate.eventName=='start'")
    public void onStartEvent(DelegateExecution executionDelegate) {
        if (StringUtils.isNotEmpty(executionDelegate.getActivityInstanceId()))
            return;
        var id = executionDelegate.getCurrentActivityId();
        if (StringUtils.equalsIgnoreCase(WorkFlowConstants.BpmnConstants.ORDER_EXEC_START.getStringValue(), id)) {
            var orderFlowContext = processDataAccessor.getOrderFlowContext(executionDelegate);
            notificationUtils.sendNotification(orderFlowContext, "ORDER_PROCESSING", "ORDER");
            orderStatusManager.updateOrderExecutionStart(orderFlowContext.getOrder().getOrderId());
        }
    }

    @EventListener(condition = "#delegateExecution != null && #delegateExecution.bpmnModelElementInstance != null && #delegateExecution.bpmnModelElementInstance.elementType !=null "
            + "&& #delegateExecution.bpmnModelElementInstance.elementType.typeName=='endEvent'&& #delegateExecution.eventName=='end'")
    public void onEndEvent(DelegateExecution delegateExecution) {
        activityExecutionEnd.closeActivity(delegateExecution, null, GenericConstants.ACTIVITY_TYPE_END_EVENT);
    }
}
