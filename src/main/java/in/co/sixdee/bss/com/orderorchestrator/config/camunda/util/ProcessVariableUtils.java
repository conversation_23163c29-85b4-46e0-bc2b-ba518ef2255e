package in.co.sixdee.bss.com.orderorchestrator.config.camunda.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Objects;

import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.spin.plugin.variable.SpinValues;
import org.camunda.spin.plugin.variable.value.JsonValue;
import org.hibernate.cache.spi.support.AbstractReadWriteAccess;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.AdditionalChargesDTO;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.enrichment.ProfileInfo;
import in.co.sixdee.bss.om.model.dto.enrichment.ServiceInfo;
import in.co.sixdee.bss.om.model.dto.order.ModeDetail;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.Service;
import in.co.sixdee.bss.om.model.dto.order.ServiceGroups;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.ARMFetchAssetDetails;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.FetchDetailsFromArm;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;

@Log4j2
@Component
@RequiredArgsConstructor
public class ProcessVariableUtils {


    protected final ObjectMapper objectMapper;
    @Autowired
    private GetDataFromCache cache;
    

    public Map<String, Object> createProcessVariables(String feature, OrderFlowContext orderFlowContext) {
        Map<String, Object> processVariables = new HashMap<String, Object>();
        processVariables.put(WorkFlowConstants.WorkFlowProcessVariables.STATUS.toString(),
                WorkFlowConstants.WorkFlowProcessVariables.STATUS_SUCCESS.toString());
        switch (feature) {
            case OrderTypes.STARTERPACK_PROVISIONING:
                processVariables.putAll(findStarterPackProcessVariables());
                break;
            case OrderTypes.UPDATE_STARTER_PACK_KYC:
                processVariables.putAll(findUpdateStarterPackProcessVariables(orderFlowContext));
                findCVMCallReqdProcessVariables(orderFlowContext,processVariables);
                break;
            case OrderTypes.UPDATE_SERVICE:
                findUpdateServiceProcessVariables(orderFlowContext,processVariables);
                break;
            case OrderTypes.ADD_SUBSCRIPTION:
                findAddSubscriptionProcessVariables(orderFlowContext, processVariables);
                break;
            case OrderTypes.ONBOARDING:
                findOnboardingProcessVariables(orderFlowContext, processVariables);
                findCVMCallReqdProcessVariables(orderFlowContext, processVariables);
                findNMSCallReqdProcessVariables(orderFlowContext, processVariables);
                break;
            case OrderTypes.CREATE_PROFILE_AND_ACCOUNT:
                findCreateProfileAndAccountProcessVariables(orderFlowContext,processVariables);
                break;
            case OrderTypes.ADD_SERVICE, OrderTypes.ADD_SERVICE_TO_NEW_ACCOUNT:
                findAddServiceProcessVariables(orderFlowContext, processVariables);
                findNMSCallReqdProcessVariables(orderFlowContext, processVariables);
                break;
            case OrderTypes.UPDATE_ACCOUNT, OrderTypes.UPDATE_ACCOUNT_ADDRESS:
                findUpdateAccountProcessVariables(orderFlowContext,processVariables);
                break;
            case OrderTypes.CREDIT_REFUND:
                processVariables.putAll(findCreditRefundProcessVariables(orderFlowContext));
                break;
            case OrderTypes.CONNECTION_MIGRATION:
                processVariables.putAll(findConnectionMigrationProcessVariables(orderFlowContext));
                break;
            case OrderTypes.CHANGE_SUBSCRIPTION:
                findChangeSubscriptionProcessVariables(orderFlowContext,processVariables);
                break;
            case OrderTypes.TRANSFER_OF_SERVICE:
                findTransferOfServiceProcessVariables(orderFlowContext,processVariables);
                findPortInProcessVariables(orderFlowContext,processVariables);
                break;
            case OrderTypes.SOFT_BARRING, OrderTypes.HARD_BARRING, OrderTypes.SUSPEND_SERVICE:
                findSuspendAndBarringProcessVariables(orderFlowContext, feature, processVariables);
                break;
            case OrderTypes.HARD_UNBARRING, OrderTypes.RESUME_SERVICE, OrderTypes.SOFT_UNBARRING:
                findResumeAndUnbarringProcessVariables(orderFlowContext, feature,processVariables);
                break;
            case OrderTypes.MAKE_PAYMENT:
                findMakePaymentProcessVariables(orderFlowContext,processVariables);
                break;
            case OrderTypes.TERMINATE_SERVICE:
                findTerminateServiceProcessVariables(orderFlowContext,processVariables);
                break;
            case OrderTypes.LINE_BARRING, OrderTypes.LINE_UNBARRING:
                processVariables.putAll(findLineBarUnBarProcessVariables(orderFlowContext));
                break;
            case OrderTypes.FREEZE:
                processVariables.putAll(findFreezeProcessVariables(orderFlowContext));
                break;
            case OrderTypes.CREATE_SB_GROUP:
                findSubscriptionProcessVariables(orderFlowContext,processVariables);
                findCreateSBGroupProcessVariables(orderFlowContext,processVariables);
                break;
            case OrderTypes.CANCEL_SUBSCRIPTION:
                findCancelSubscriptionProcessVariables(orderFlowContext,processVariables);
                break;
            case OrderTypes.MANAGE_HLR_SERVICES:
                processVariables.putAll(findManageHLRProcessVariables(orderFlowContext));
                break;
            case OrderTypes.CHANGE_MSISDN:
                findPortInProcessVariables(orderFlowContext,processVariables);
                break;
            case OrderTypes.CHANGE_SIM:
                findChangeSimProcessVariables(orderFlowContext,processVariables);
                break;
            case OrderTypes.RESTORE_SERVICE:
                processVariables.putAll(findRestoreServiceProcessVariables(orderFlowContext));
                break;
            case "ActivateService":
                processVariables.putAll(findActivateProcessVariables(orderFlowContext));
                break;
            case OrderTypes.MNP_PORT_IN:
                findMNPProcessVariables(orderFlowContext, processVariables);
                break;
            case OrderTypes.INTERIM_NUMBER_PORT_IN:
                findInterimPortInProcessVariables(orderFlowContext, processVariables);
                break;
            case OrderTypes.CREATE_SAFE_CUSTODY:
                findSubscriptionProcessVariables(orderFlowContext,processVariables);
                break;
            case OrderTypes.MNP_PORT_OUT:
                findMNPPortOutProcessVariables(orderFlowContext, processVariables);
                break;
            case OrderTypes.EXTEND_EXPIRY_DATE:
                findExtendExpiryDateProcessVariables(orderFlowContext, processVariables);
                break;
            default:
        }
        processVariables.put(WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString(),
                createWorkflowData(orderFlowContext));
        processVariables.put("sndWorkflowTrigger", checkSndWorkflowTriggerReqd(orderFlowContext));
        processVariables.put("erpDeposit", paymentChannelCheckForERPDeposit(orderFlowContext));
        processVariables.put("priority", checkPriorityForOrderType(orderFlowContext));
        processVariables.put("taxCalculationReqd", checkTaxCalculationReqd(orderFlowContext));
        processVariables.put("futureOrderRequired", findFutureOrderRequired(orderFlowContext.getOrder().getOrderType(), orderFlowContext.getEntityId()));
        processVariables.put("isLegacy", orderFlowContext.getOrder().isLegacy());
        processVariables.put("somDeleteGroupSubscriptions", false);
        processVariables.put("subscriptionCallReq", false);
        processVariables.put("isEsimCall",false);
        
        getOfAdditionalCharges(orderFlowContext, processVariables);   
        return processVariables;
    }
    
    private void findExtendExpiryDateProcessVariables(OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
            processVariables.put("paymentCallReqd", false);
            if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment()) &&
                    "true".equalsIgnoreCase(orderFlowContext.getOrder().getPayment().getUpfrontPayment())) {
                processVariables.put("paymentCallReqd", true);
            }
		
	}

	private boolean isIccidValidationIsReqd(OrderFlowContext orderFlowContext) {
		if (ObjectUtils.isNotEmpty(orderFlowContext) && ObjectUtils.isNotEmpty(orderFlowContext.getOrder())
				&& ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getAccount())
				&& ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getAccount().getServiceGroups())) {
			var serviceGroups = orderFlowContext.getOrder().getProfile().getAccount().getServiceGroups();
			if (ObjectUtils.isNotEmpty(serviceGroups)) {
				for (ServiceGroups serviceGroup : serviceGroups) {
					var services = serviceGroup.getServices();
					if (ObjectUtils.isNotEmpty(services)) {
						for (Service service : services) {
							var characteristics = service.getCharacteristics();
							if (ObjectUtils.isNotEmpty(characteristics)) {
								  for (Characteristic characteristic : characteristics) {
								if (StringUtils.isNotEmpty(characteristic.getName()) && 
                            		    StringUtils.isNotEmpty(characteristic.getValue()) &&
                            		    GenericConstants.CHARACTERISC_NAME_FOR_ICCID.trim().equalsIgnoreCase(characteristic.getName().trim())) {
										return true;
                                }
							}
							}
						}
					}
				}
			}
		}
		return false;
	}

    private void findNMSCallReqdProcessVariables(OrderFlowContext orderFlowContext, Map<String, Object> processVariables) {
        processVariables.put("nmsUpdateCallReqd", false);

        CacheTableDataDTO mvnoConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.NMS_SKIP_CHANNEL_IDS.toString());
        if (mvnoConfig != null) {
            String configValue = mvnoConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
            if (configValue != null) {
                processVariables.put("nmsCallReqd", !Arrays.asList(configValue.split(",")).contains(orderFlowContext.getChannel()));
            }
        }
    }

    private void findCreateProfileAndAccountProcessVariables(OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
        processVariables.put("isProfileAccountCreatedInEnrichment", isEnrichmentFlowEnabled());
    }


    private void findMNPPortOutProcessVariables(OrderFlowContext orderFlowContext, Map<String, Object> processVariables) {
        processVariables.put("portOutType", orderFlowContext.getAttributes().get("portOutType"));
        processVariables.put("isIdValidationReqd", checkIdValidationRequiredForPortOut(orderFlowContext));
        processVariables.put("isContractPlanValidationReqd", checkContractPlanValidationRequiredForPortOut());
    }

    private boolean checkContractPlanValidationRequiredForPortOut() {

        String configPlans = null;
        if (ObjectUtils.isNotEmpty(cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                CacheConstants.CacheFields.CONTRACT_PLAN_IDS.toString()))) {
            configPlans = cache
                    .getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                            CacheConstants.CacheFields.CONTRACT_PLAN_IDS.toString())
                    .getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
        }
        return StringUtils.isNotEmpty(configPlans);

    }

    private boolean checkIdValidationRequiredForPortOut(OrderFlowContext orderFlowContext) {
        List<String> mvnoConfigValue = null;
        CacheTableDataDTO mvnoConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_MNP_PROPERTY_CONFIG.toString(),
                CacheConstants.CacheFields.PORTOUT_ID_VALIDATION_REQD_MVNOS.toString());
        if (mvnoConfig != null)
            mvnoConfigValue = Arrays.asList(mvnoConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString()).split(","));
        return (mvnoConfigValue != null && mvnoConfigValue.contains(orderFlowContext.getAttributes().get(MNPConstants.DONOR_ENTITY_ID)));
    }

    private void findMNPProcessVariables(OrderFlowContext orderFlowContext, Map<String, Object> processVariables) {

        processVariables.put("simDeliveryCallReqd", true); // As per Neethu sim delivery is always needed.

        boolean paymentCallReqd = ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment()) && "true".equals(orderFlowContext.getOrder().getPayment().getUpfrontPayment());

        processVariables.put("paymentCallReqd", paymentCallReqd);

        processVariables.put("isExistingProfile", StringUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getProfileId()));

        processVariables.put("isExistingAccount", StringUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getAccount().getAccountId()));

        processVariables.put("isIdValidationReqd", isIdValidationRequired(orderFlowContext.getOrder().getOrderType()));
        
        processVariables.put("isIccidValidationReq",isIccidValidationIsReqd(orderFlowContext));

    }

    private boolean isIdValidationRequired(String orderType) {
        CacheTableDataDTO mvnoConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.ID_VALIDATION_REQD_FOR + "_" + orderType);
        if (mvnoConfig != null)
            return Boolean.parseBoolean(mvnoConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString()));
        return false;
    }

    private void findInterimPortInProcessVariables(OrderFlowContext orderFlowContext, Map<String, Object> processVariables) {
        boolean paymentCallReqd = ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment()) && "true".equals(orderFlowContext.getOrder().getPayment().getUpfrontPayment());
        processVariables.put("paymentCallReqd", paymentCallReqd);
        processVariables.put("isIdValidationReqd", isIdValidationRequired(orderFlowContext.getOrder().getOrderType()));
    }

    private Map<String, Object> findRestoreServiceProcessVariables(OrderFlowContext orderFlowContext) {
        Map<String, Object> processVariables = new HashMap<>();
        var enrichmentResults = orderFlowContext.getEnrichmentResults();
        if (enrichmentResults != null && enrichmentResults.get("serviceInfo") != null) {
            var serviceInfo = objectMapper.convertValue(enrichmentResults.get("serviceInfo"),
                    ServiceInfo.class);
            processVariables.put("isPostpaid", "1".equals(serviceInfo.getChargingPattern()));
        }
        return processVariables;
    }

    private void findChangeSimProcessVariables(OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
    	findSimType(orderFlowContext, processVariables);
////        if (ObjectUtils.isNotEmpty(orderFlowContext) && ObjectUtils.isNotEmpty(orderFlowContext.getOrder())
//                && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement())
//                && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getCharacteristics())) {
//
//            var characteristic = orderFlowContext.getOrder().getServiceManagement().getCharacteristics().stream()
//                    .filter(party -> StringUtils.isNotEmpty(party.getName()) &&
//                            StringUtils.equalsIgnoreCase(party.getName(), "isEsim"))
//                    .findAny().orElse(null);
//
//            if (characteristic != null && StringUtils.isNotEmpty(characteristic.getValue())) {
//                processVariables.put("isEsim", "true".equals(characteristic.getValue()));
//            } else {
//                processVariables.put("isEsim", false);
//            }

            var enrichmentResults = orderFlowContext.getEnrichmentResults();
            if (enrichmentResults != null && enrichmentResults.get("serviceInfo") != null) {
                var serviceInfo = objectMapper.convertValue(enrichmentResults.get("serviceInfo"),
                        ServiceInfo.class);
                processVariables.put("isPostpaid", "1".equals(serviceInfo.getChargingPattern()));
            } else {
                processVariables.put("isPostpaid", false);
            }
//        } else {
//            log.info("ignoring attributes");
//        }
    }

	private void findSimType(OrderFlowContext orderFlowContext, Map<String, Object> processVariables) {
		processVariables.put("eSIMCallReq", false);
		if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder())
				&& ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement())) {
			if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getSimType())) {
				if (StringUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getSimType())
						&& (GenericConstants.CHARACTERISC_VALUE_FOR_ESIM.equalsIgnoreCase(
								orderFlowContext.getOrder().getServiceManagement().getSimType().trim()))) {
					processVariables.put("eSIMCallReq", true);
				} else {
					processVariables.put("eSIMCallReq", false);
				}
			}
		}
	}

    private void getOfAdditionalCharges(OrderFlowContext orderFlowContext, Map<String, Object> processVariables) {
        if (ObjectUtils.isNotEmpty(orderFlowContext) && ObjectUtils.isNotEmpty(orderFlowContext.getOrder())
                && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment())
                && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment().getAdditionalCharges())) {
            boolean additionalCharges = false;
            for (AdditionalChargesDTO charge : orderFlowContext.getOrder().getPayment().getAdditionalCharges()) {
                if (StringUtils.equals(charge.getChargeType(), "serviceCharges")
                        && StringUtils.isNotEmpty(charge.getAmount()) && Double.parseDouble(charge.getAmount()) > 0) {
                    additionalCharges = true;
                    break;
                }
            }
            processVariables.put("additionalCharges", additionalCharges);
        } else {
            processVariables.put("additionalCharges", false);
        }
    }

    private void findPortInProcessVariables(OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
        processVariables.put("deviceId",
                ObjectUtils.isNotEmpty(orderFlowContext.getAttributes().get("deviceId")));
        var enrichmentResults = orderFlowContext.getEnrichmentResults();
        if (enrichmentResults != null && enrichmentResults.get("serviceInfo") != null) {
            var serviceInfo = objectMapper.convertValue(enrichmentResults.get("serviceInfo"),
                    ServiceInfo.class);
            processVariables.put("isPostpaid", "1".equals(serviceInfo.getChargingPattern()));
        } else {
            processVariables.put("isPostpaid", false);
        }
    }

    protected boolean checkTaxCalculationReqd(OrderFlowContext orderFlowContext) {

        var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "TAX_CALCULATION_REQD_CHARGE_TYPES");
        var configChargeType = appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
        var chargeTypes = Set.of(configChargeType.split(","));
        if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment())
                && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment().getAdditionalCharges())) {
            return orderFlowContext.getOrder().getPayment().getAdditionalCharges().stream().anyMatch(charge ->
                    StringUtils.isNotEmpty(charge.getChargeType()) && chargeTypes.contains(charge.getChargeType()));

        }
        return false;
    }


    private Map<String, Object> findManageHLRProcessVariables(OrderFlowContext orderFlowContext) {
        Map<String, Object> processVariables = new HashMap<>();
        log.info(" WaitingDelay :: {}", orderFlowContext.getOrder().getServiceManagement().getWaitingDelay());

        try {
            if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder()) && StringUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getHlrServices().get(0).getName())) {

                processVariables.put("hlr_service_enable_time_delay", null);
                var hlrServiceName = orderFlowContext.getOrder().getServiceManagement().getHlrServices().get(0).getName();
                var hlrActivationServiceName = cache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG,
                        "HLR_5G_SERVICE_NAME");
                if (StringUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getHlrServices().get(0).getName())
                        && StringUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getHlrServices().get(0).getStatus())
                        && orderFlowContext.getOrder().getServiceManagement().getHlrServices().get(0).getStatus().equalsIgnoreCase("true")
                        && orderFlowContext.getOrder().getServiceManagement().getHlrServices().get(0).getName()
                        .equalsIgnoreCase(hlrActivationServiceName.getNgTableData().get("CONFIG_VALUE"))) {
                    var status = "";
                    if (orderFlowContext.getEnrichmentResults().containsKey("serviceInfo")) {
                        var serviceInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("serviceInfo"),
                                LinkedHashMap.class);
                        status = serviceInfo.get("status").toString();
                        if (StringUtils.isNotEmpty(status) && status.equals("0")) {
                            orderFlowContext.getOrder().getServiceManagement().setWaitingDelay("true");
                            log.info("status is in pre-active state setting waiting delay flag is true :: {}", orderFlowContext.getOrder().getServiceManagement().getWaitingDelay());
                        }
                    }

                    if (StringUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getWaitingDelay())
                            && orderFlowContext.getOrder().getServiceManagement().getWaitingDelay().equalsIgnoreCase("false")) {
                        processVariables.put("hlr_service_enable_time_delay", null);
                        log.info("Not setting the delay during HLR Service Activation :: {}", processVariables.get("hlr_service_enable_time_delay"));
                    } else if (StringUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getWaitingDelay())
                            && orderFlowContext.getOrder().getServiceManagement().getWaitingDelay().equalsIgnoreCase("true")) {
                        var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                                "HLR_WAITING_DURATION");
                        if (appConfig != null && StringUtils.isNotEmpty(appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name())))
                            processVariables.put("hlr_service_enable_time_delay", appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()));
                        else
                            processVariables.put("hlr_service_enable_time_delay", "PT60S");
                        log.info("Setting the delay during HLR Service Activation :: {}", processVariables.get("hlr_service_enable_time_delay"));
                    } else {
                        processVariables.put("hlr_service_enable_time_delay", null);
                        log.info("Not setting the delay during HLR Service :: {}", processVariables.get("hlr_service_enable_time_delay"));
                    }
                }

                if (hlrServiceName.equalsIgnoreCase("barIncomingInternationalCalls")) {
                    processVariables.put("isBIIC", true);
                } else {
                    processVariables.put("isBIIC", false);
                }
            }
        } catch (Exception e) {
            log.info("Exception occurred at findManageHLRProcessVariables  " + e, e);

        }

        return processVariables;
    }

    private Map<String, Object> findLineBarUnBarProcessVariables(
            OrderFlowContext orderFlowContext) {
        Map<String, Object> processVariables = new HashMap<>();
        var cfsConfig = cache.getCacheDetailsFromDBMap(
                CacheConstants.CacheKeys.COM_STARTERPACK_BARRING_CFS_CONFIG.name(),
                orderFlowContext.getOrder().getOrderType());
        processVariables.put("somBarringCallReq",
                String.valueOf(cfsConfig != null && ObjectUtils.isNotEmpty(cfsConfig.getNgTableData().get("NAME"))));

        var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                "FULL_SUSPENSION_REQUIRED_FOR_LINEBAR-UNBAR");
        processVariables.put("fullSuspensionReq", appConfig != null &&
                Boolean.valueOf(appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name())));

        return processVariables;
    }

    private void findOnboardingProcessVariables(OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
        String profileType = null;

        processVariables.put("upfrontPayment", ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment()) && "true".equalsIgnoreCase(orderFlowContext.getOrder().getPayment().getUpfrontPayment()));
        processVariables.put("paymentCallReqd", ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment()) && "true".equalsIgnoreCase(orderFlowContext.getOrder().getPayment().getUpfrontPayment()));

        processVariables.put("isPrepaidAccount", ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getAccount()) && "2".equals(orderFlowContext.getOrder().getProfile().getAccount().getChargingPattern()));
        processVariables.put("isPostPaidAccount", ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getAccount()) && "1".equals(orderFlowContext.getOrder().getProfile().getAccount().getChargingPattern()));

        if (orderFlowContext.getOrder().getProfile() != null
                && orderFlowContext.getOrder().getProfile().getPartyCharacteristic() != null) {
            var partyCharacteristic = orderFlowContext.getOrder().getProfile().getPartyCharacteristic().stream()
                    .filter(characteristic -> "profileType".equals(characteristic.getName())).findAny().orElse(null);
            if (partyCharacteristic != null)
                profileType = partyCharacteristic.getValue();
            processVariables.put("isCorporate", StringUtils.isNotEmpty(profileType) && profileType.equals("1")
                    && orderFlowContext.getOrder().getProfile().getAccount().getChargingPattern().equals("2"));

        }
        processVariables.put("isProfileAccountCreatedInEnrichment", isEnrichmentFlowEnabled());
        processVariables.put("isIccidValidationReq",isIccidValidationIsReqd(orderFlowContext));
    }

    private void findAddServiceProcessVariables(OrderFlowContext orderFlowContext, Map<String, Object> processVariables) {
        processVariables.put("upfrontPayment", false);
        processVariables.put("paymentCallReqd", false);

        processVariables.put("isPrepaidAccount", ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getAccount()) && "2".equals(orderFlowContext.getOrder().getProfile().getAccount().getChargingPattern()));
        processVariables.put("isPostPaidAccount", ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getAccount()) && "1".equals(orderFlowContext.getOrder().getProfile().getAccount().getChargingPattern()));

        if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment()) &&
                StringUtils.isNotEmpty(orderFlowContext.getOrder().getPayment().getUpfrontPayment()) &&
                orderFlowContext.getOrder().getPayment().getUpfrontPayment().equalsIgnoreCase("true")) {
            processVariables.put("upfrontPayment", true);
            processVariables.put("paymentCallReqd", true);
        }
        var newAccount = StringUtils.isEmpty(orderFlowContext.getOrder().getProfile().getAccount().getAccountId())
                && ObjectUtils.isEmpty(orderFlowContext.getEnrichmentResults().get("accountInfo"));
        processVariables.put("isPostPaidAccount", newAccount && "1".equals(orderFlowContext.getOrder().getProfile().getAccount().getChargingPattern()));
        processVariables.put("isNewAccount", newAccount);
        processVariables.put("isProfileAccountCreatedInEnrichment", isEnrichmentFlowEnabled());
        processVariables.put("isIccidValidationReq",isIccidValidationIsReqd(orderFlowContext));

    }

    public void getServiceIdWithoutCC(String serviceId, OrderFlowContext executionContext) {
        String countryCode = null;
        CacheTableDataDTO countryCodeConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.SINGAPORE_COUNTRY_CODE.toString());

        if (countryCodeConfig != null && serviceId != null) {
            countryCode = countryCodeConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
            if (serviceId.startsWith(countryCode)) {
                serviceId = serviceId.substring(countryCode.length());
            }
            executionContext.getAttributes().put("msisdn_without_cc", serviceId);
            log.info("service id without cc : {}", serviceId);
        }

    }

    private Map<String, Object> findSOMProvsioningReqd(OrderFlowContext orderFlowContext) {
        Map<String, Object> processVariables = new HashMap<>();
        var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                "SOM_CALL_REQUIRED");
        processVariables.put("isSomCallReqd", ObjectUtils.isNotEmpty(appConfig) && appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()).contains("true"));
        return processVariables;
    }

    private void findMakePaymentProcessVariables(OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
        if (StringUtils.isNotEmpty(orderFlowContext.getOrder().getPayment().getPgwTrigger())
                && orderFlowContext.getOrder().getPayment().getPgwTrigger().equalsIgnoreCase("true")) {
            List<ModeDetail> modeDetailList = orderFlowContext.getOrder().getPayment().getModeDetail();
            if (modeDetailList.stream()
                    .anyMatch(modeDetail -> "payment_subscriber_id".equalsIgnoreCase(modeDetail.getKey())
                            && StringUtils.isNotEmpty(modeDetail.getValue()))) {
                processVariables.put("pgwIntegration", true);
            }
        } else if (StringUtils.isNotEmpty(orderFlowContext.getOrder().getPayment().getPaymentSubsId())) {
            processVariables.put("pgwIntegration", true);
        } else
            processVariables.put("pgwIntegration", StringUtils.isNotEmpty(orderFlowContext.getOrder().getPayment().getPaymentSubsId()));
    }


    private Map<String, Object> findFreezeProcessVariables(OrderFlowContext orderFlowContext) {
        Map<String, Object> processVariables = new HashMap<String, Object>();
        processVariables.put("nccUpdateUserReqd", StringUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getAlternatePhoneNumber()));
        return processVariables;
    }

    private void findCreateSBGroupProcessVariables(OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
        processVariables.put("isBeneficiaryPresent", ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getBeneficiaries()));
        processVariables.put("isLegacy", orderFlowContext.getOrder().isLegacy());
    }

    public boolean checkSndWorkflowTriggerReqd(OrderFlowContext orderFlowContext) {
        var orderPayload = orderFlowContext.getOrder();
        if (orderPayload != null) {
            if (ObjectUtils.isNotEmpty(orderPayload.getPayment())
                    && StringUtils.isNotEmpty(orderPayload.getPayment().getUpfrontPayment()) && checkPaymentAllowedForTheNetWorkType(orderPayload)) {
                if (StringUtils.equalsIgnoreCase(orderPayload.getPayment().getUpfrontPayment(), "false")) {
                    return false;
                } else if (StringUtils.isEmpty(orderPayload.getPayment().getPaymentReceived())
                        || "false".equalsIgnoreCase(orderPayload.getPayment().getPaymentReceived())) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checkPaymentAllowedForTheNetWorkType(Order orderPayload) {

        String paymentRequiredNetworkTypesConfig = null;
        if (GenericConstants.CREATE_SERVICE_ORDER_TYPES.contains(orderPayload.getOrderType())) {
            var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                    "PAYMENT_REQUIRED_NETWORK_TYPES");
            if (appConfig != null) {
                paymentRequiredNetworkTypesConfig = appConfig.getNgTableData()
                        .get(CacheConstants.CacheFields.CONFIG_VALUE.name());
                if (ObjectUtils.isNotEmpty(orderPayload.getProfile().getAccount()) && ObjectUtils.isNotEmpty(orderPayload.getProfile().getAccount().getServiceGroups()) && ObjectUtils
                        .isNotEmpty(orderPayload.getProfile().getAccount().getServiceGroups().get(0).getServices()
                                .get(0).getNetworkServiceId())
                        && StringUtils.isNotEmpty(paymentRequiredNetworkTypesConfig)) {
                    var netWorkType = orderPayload.getProfile().getAccount().getServiceGroups().get(0).getServices()
                            .get(0).getNetworkServiceId();
                    if (!Arrays.asList(paymentRequiredNetworkTypesConfig.split(",")).contains(netWorkType))
                        return false;
                }
            }
        }
        return true;

    }


    private void findAddSubscriptionProcessVariables(OrderFlowContext orderFlowContext, Map<String, Object> processVariables) {
        processVariables.put("paymentCallReqd", false);
        processVariables.put("ocsCallReqd", true);
        if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment()) &&
                "true".equalsIgnoreCase(orderFlowContext.getOrder().getPayment().getUpfrontPayment())) {
            processVariables.put("paymentCallReqd", true);
        }
        if (StringUtils.equalsIgnoreCase(orderFlowContext.getChannel(), "OCS")) {
            List<Characteristic> itemCharacteristics = orderFlowContext.getOrder().getOrderItem().get(0).getItemCharacteristic();
            if (itemCharacteristics != null && !itemCharacteristics.isEmpty()) {
                for (Characteristic itemChar : itemCharacteristics) {
                    if ("SUBSCRIPTION_ID".equals(itemChar.getName())) {
                        processVariables.put("ocsCallReqd", false);
                        break;
                    }
                }
            }
        }
        orderFlowContext.getAttributes().put("addonType", "1");
    }

    private void findCancelSubscriptionProcessVariables(
            OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
        processVariables.put("paymentCallReqd", false);
        processVariables.put("waiveoffcallbackReqd", false);
        processVariables.put("vasActivationReqd", true);

        var cfsConfig = cache.getCacheDetailsFromDBMap(
                CacheConstants.CacheKeys.COM_STARTERPACK_BARRING_CFS_CONFIG.name(),
                orderFlowContext.getOrder().getOrderType());
        processVariables.put("somBarringCallReq",
                String.valueOf(cfsConfig != null && ObjectUtils.isNotEmpty(cfsConfig.getNgTableData().get("NAME"))));

        var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                "FULL_SUSPENSION_REQUIRED_FOR_LINEBAR-UNBAR");
        processVariables.put("fullSuspensionReq", appConfig != null &&
                Boolean.valueOf(appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name())));

        if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment()) &&
                StringUtils.isNotEmpty(orderFlowContext.getOrder().getPayment().getUpfrontPayment()) &&
                orderFlowContext.getOrder().getPayment().getUpfrontPayment().equalsIgnoreCase("true")) {
            processVariables.put("paymentCallReqd", true);
        } else {
            processVariables.put("paymentCallReqd", false);

        }
        if (StringUtils.equalsIgnoreCase(orderFlowContext.getChannel(), "OCS"))
            processVariables.put("ocsCallReqd", false);
        else
            processVariables.put("ocsCallReqd", true);
    }

    private void findCVMCallReqdProcessVariables(OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
        var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "CVM_CALL_REQD");
        if (ObjectUtils.isNotEmpty(appConfig)
                && "true".equalsIgnoreCase(appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()))) {
            processVariables.put("cvmCallReqd", true);
        } else {
            processVariables.put("cvmCallReqd", false);
        }
    }

    private Map<String, Object> findCreditRefundProcessVariables(OrderFlowContext orderFlowContext) {
        Map<String, Object> processVariables = new HashMap<>();
        if (orderFlowContext.getOrder().getCreditRefund() != null
                && orderFlowContext.getOrder().getCreditRefund().getRefundType() != null
                && orderFlowContext.getOrder().getCreditRefund().getRefundType().equalsIgnoreCase("2")) {
            processVariables.put("nccAdjustMABalanceCallRqd", true);
        } else {
            processVariables.put("nccAdjustMABalanceCallRqd", false);
        }
        return processVariables;
    }


    private void findUpdateServiceProcessVariables(OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
        var esbCallReq = (orderFlowContext.getOrder().getServiceManagement().getBasicInfo().stream()
                .filter(basicInfo -> "LanguageId".equalsIgnoreCase(basicInfo.getId()) && basicInfo.getValue() != null).findAny()
                .orElse(null)) != null;
        processVariables.put("esbUpdateCallReq", String.valueOf(esbCallReq));
    }

    private Map<String, Object> findStarterPackProcessVariables() {
        Map<String, Object> processVariables = new HashMap<>();
        var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                "NMS_STARTERPACK_PROVISIONING_STATUS_UPDATE");
        if (ObjectUtils.isNotEmpty(appConfig)
                && "true".equalsIgnoreCase(appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()))) {
            processVariables.put("nmsUpdateReq", "true");
        } else {
            processVariables.put("nmsUpdateReq", "false");
        }
        return processVariables;
    }

    private Map<String, Object> findUpdateStarterPackProcessVariables(OrderFlowContext orderFlowContext) {
        Map<String, Object> processVariables = new HashMap<>();
        String profileType = null;
        if (orderFlowContext.getOrder().getCustomerInfo() != null
                && orderFlowContext.getOrder().getCustomerInfo().getPartyCharacteristic() != null) {
            var partyCharacteristic = orderFlowContext.getOrder().getCustomerInfo().getPartyCharacteristic().stream()
                    .filter(characteristic -> "profileType".equals(characteristic.getName())).findFirst().orElse(null);
            if (partyCharacteristic != null)
                profileType = partyCharacteristic.getValue();
        } else if (orderFlowContext.getEnrichmentResults() != null
                && orderFlowContext.getEnrichmentResults().get("profileInfo") != null) {
            var profileInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("profileInfo"),
                    ProfileInfo.class);
            profileType = profileInfo.getProfileType();
        }
        if (StringUtils.isNotEmpty(profileType)) {
            processVariables.put("nccWelcomSubCallReq",
                    String.valueOf(checkWelcomeSubscriptionRequired(profileType, "2", "1", orderFlowContext)));
        } else {
            log.info("Unable to find profile type. disabling welcome subscription call");
            processVariables.put("nccWelcomSubCallReq", "false");
        }
        var topUpreq = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_TOPUP_CONFIG_DETAILS.name(),
                profileType + "_" + "2");
        if (topUpreq != null && BooleanUtils.toBoolean(topUpreq.getNgTableData().get("TOPUP_REQD"))) {
            processVariables.put("topUpCallRqd", true);
        } else {
            processVariables.put("topUpCallRqd", false);
        }

        var cfsConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_STARTERPACK_BARRING_CFS_CONFIG.name(),
                OrderTypes.UPDATE_STARTER_PACK_KYC);
        processVariables.put("somBarringCallReq",
                String.valueOf(cfsConfig != null && ObjectUtils.isNotEmpty(cfsConfig.getNgTableData().get("NAME"))));
        return processVariables;
    }

    private void findUpdateAccountProcessVariables(OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
        if (OrderTypes.UPDATE_ACCOUNT.equals(orderFlowContext.getOrder().getOrderType())) {
            boolean erpCallReqd = false;
            var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                    "ERP_UPDATE_ACCOUNT_TAGS");
            if (ObjectUtils.isNotEmpty(appConfig)) {
                var configValue = appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
                var basicInfos = orderFlowContext.getOrder().getAccountManagement().getBasicInfo();
                for (var entry : basicInfos) {
                    var id = entry.getId();
                    if (StringUtils.contains(configValue, id)) {
                        erpCallReqd = true;
                        break;
                    }

                }
            }
            processVariables.put("erpUpdateAccountCallReqd", erpCallReqd);
        }
        var chargingPattern = "";
        if (orderFlowContext.getEnrichmentResults().containsKey("accountInfo")) {
            var accountInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("accountInfo"),
                    LinkedHashMap.class);
            chargingPattern = accountInfo.get("chargingPattern").toString();
        }
        processVariables.put("isPostpaid", "1".equals(chargingPattern));

    }

    private boolean checkWelcomeSubscriptionRequired(String profileType, String chargingPattern, String networkType,
                                                     OrderFlowContext orderFlowContext) {
        var cacheConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_NCC_WELCOM_SUBSCRIPTION_CONFIG.name(),
                chargingPattern + "_" + profileType + "_" + networkType);
        if (ObjectUtils.isNotEmpty(cacheConfig)) {
            orderFlowContext.getAttributes().put("NCCOfferId", cacheConfig.getNgTableData().get("NCC_OFFER_ID"));
            orderFlowContext.getAttributes().put("welcomePlanId", cacheConfig.getNgTableData().get("UPC_PLAN_ID"));
            return true;
        }
        return false;
    }

    public Map<String, Object> createSPProcessVariables(OrderFlowContext orderFlowContext,
                                                        in.co.sixdee.bss.om.model.dto.order.Service service) {
        Map<String, Object> variables = new HashMap<String, Object>();
        String profileType = null;
        String chargingPattern = service.getChargingPattern();
        var networkType = service.getNetworkServiceId();
        if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile()) && (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getPartyCharacteristic()))) {
            var partyCharacteristicList = orderFlowContext.getOrder().getProfile().getPartyCharacteristic();
            var partyCharacteristic = Objects.requireNonNull(partyCharacteristicList.stream()
                    .filter(characteristic -> StringUtils.isNotEmpty(characteristic.getName())
                            && characteristic.getName().equalsIgnoreCase("profileType"))
                    .findFirst().orElse(null));
            if (ObjectUtils.isNotEmpty(partyCharacteristic) && StringUtils.isNotEmpty(partyCharacteristic.getValue()))
                profileType = partyCharacteristic.getValue();
        } else if (ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults())
                && ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults().get("profileInfo"))) {
            var profileInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("profileInfo"),
                    ProfileInfo.class);
            if (StringUtils.isNotEmpty(profileInfo.getProfileType()))
                profileType = profileInfo.getProfileType();
        }
        if (StringUtils.equalsAnyIgnoreCase(orderFlowContext.getOrder().getOrderType(), OrderTypes.ONBOARDING,
                OrderTypes.ADD_SERVICE, OrderTypes.ADD_SERVICE_TO_NEW_ACCOUNT)) {
            var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                    "CVM_CALL_REQD");
            if (ObjectUtils.isNotEmpty(appConfig)
                    && "true".equalsIgnoreCase(appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()))) {
                variables.put("cvmCallReqd", true);

            } else {
                variables.put("cvmCallReqd", false);
            }
        }

        var welcomeSubConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_NCC_WELCOM_SUBSCRIPTION_CONFIG.name(),
                chargingPattern + "_" + profileType + "_" + networkType);
        if (welcomeSubConfig != null) {
            variables.put("nccWelcomeSubCallRqd", true);
            orderFlowContext.getAttributes().put("NCCOfferId", welcomeSubConfig.getNgTableData().get("NCC_OFFER_ID"));
            orderFlowContext.getAttributes().put("welcomePlanId", welcomeSubConfig.getNgTableData().get("UPC_PLAN_ID"));
        } else {
            variables.put("nccWelcomeSubCallRqd", false);
        }

        var topUpreq = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_TOPUP_CONFIG_DETAILS.name(),
                profileType + "_" + chargingPattern);
        if (topUpreq != null && BooleanUtils.toBoolean(topUpreq.getNgTableData().get("TOPUP_REQD"))) {
            variables.put("topUpCallRqd", true);
        } else {
            variables.put("topUpCallRqd", false);
        }
        getServiceIdWithoutCC(service.getServiceId(), orderFlowContext);

        return variables;
    }

    public JsonValue createWorkflowData(OrderFlowContext orderFlowContext) {
        JsonValue workflowData = null;
        if (orderFlowContext.getWorkflowData() == null)
            orderFlowContext.setWorkflowData(new LinkedHashMap<>());
        if (ObjectUtils.isNotEmpty(orderFlowContext.getSubscriptionIdPlanIdMap())) {
            LinkedHashMap<String, String> subscriptionIdPlanIdMap = new LinkedHashMap<>(orderFlowContext.getSubscriptionIdPlanIdMap());
            orderFlowContext.getWorkflowData().put("subscriptionIdPlanIdMap", subscriptionIdPlanIdMap);
            orderFlowContext.setSubscriptionIdPlanIdMap(null);
        }
        try {
            workflowData = SpinValues.jsonValue(objectMapper.writeValueAsString(orderFlowContext)).create();
        } catch (JsonProcessingException e) {
            // handle exception, return the message back to queue or discard message after updating
            // order status
        }
        return workflowData;
    }

    protected int checkPriorityForOrderType(OrderFlowContext orderFlowContext) {
        var orderType = orderFlowContext.getOrder().getOrderType();
        var profileType = "";
        var channel = orderFlowContext.getChannel();
        String configuredPriority = null;
        if (orderType.equalsIgnoreCase(OrderTypes.ONBOARDING)) {
            if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getPartyCharacteristic())) {
                profileType = Objects.requireNonNull(orderFlowContext.getOrder().getProfile().getPartyCharacteristic().stream()
                        .filter(partyChar -> ObjectUtils.isNotEmpty(partyChar) && StringUtils.isNotEmpty(partyChar.getName())
                                && StringUtils.isNotEmpty(partyChar.getValue())
                                && partyChar.getName().equalsIgnoreCase("profileType"))
                        .map(partyChar -> partyChar.getValue()).findAny().orElse(null));
            }
        } else if (orderType.equalsIgnoreCase(OrderTypes.UPDATE_STARTER_PACK_KYC)) {
            if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getCustomerInfo().getPartyCharacteristic())) {
                profileType = Objects.requireNonNull(orderFlowContext.getOrder().getCustomerInfo().getPartyCharacteristic().stream()
                        .filter(partyChar -> ObjectUtils.isNotEmpty(partyChar) && StringUtils.isNotEmpty(partyChar.getName())
                                && StringUtils.isNotEmpty(partyChar.getValue())
                                && partyChar.getName().equalsIgnoreCase("profileType"))
                        .map(partyChar -> partyChar.getValue()).findAny().orElse(null));
            } else {
                profileType = getProfileTypeFromEnrichment(orderFlowContext);
            }

        } else {
            profileType = getProfileTypeFromEnrichment(orderFlowContext);
        }
        if (StringUtils.isNotEmpty(orderFlowContext.getBatchId())) {
            orderType = orderType + "_BATCH";
        }
        /*
         * var priorityConfig =
         * cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_ORDER_PRIORITY_CONFIG.name(),
         * orderType + "_" + profileType + "_" + channel);
         */
        var priority = 0;
        var priorityConfig=cache.getCacheDetailsFromDBMapAryList(CacheConstants.CacheKeys.COM_ORDER_PRIORITY_CONFIG.name(),
                orderType);
        if (ObjectUtils.isNotEmpty(priorityConfig)) {
            for (CacheTableDataDTO priorityConfigSingle : priorityConfig) {
                Map<String, String> ngTableData = priorityConfigSingle.getNgTableData();
                String configuredEntityId = ngTableData.get("ENTITY_ID");
                configuredPriority = ngTableData.get("PRIORITY");
                if (StringUtils.isNotEmpty(configuredEntityId) && configuredEntityId.equalsIgnoreCase(orderFlowContext.getEntityId())) {
                    break;
                }
            }
        }

        if (ObjectUtils.isNotEmpty(configuredPriority)) {
            priority = Integer.parseInt(configuredPriority);
        } else {
            var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                    CacheConstants.CacheFields.DEFAULT_PRIORITY_CONFIG.name());

            priority = appConfig != null && ObjectUtils.isNotEmpty(appConfig.getNgTableData())
                    && ObjectUtils.isNotEmpty(appConfig.getNgTableData().get("CONFIG_VALUE"))
                    ? Integer.valueOf(appConfig.getNgTableData().get("CONFIG_VALUE"))
                    : 0;
        }
        log.info("Priority for the flow is defined as {}",priority);
        return priority;
    }

    protected String getProfileTypeFromEnrichment(OrderFlowContext orderFlowContext) {
        var profileType = "";
        if (ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults())) {
            if (orderFlowContext.getEnrichmentResults().containsKey("profileInfo")) {
                var profileInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("profileInfo"),
                        LinkedHashMap.class);
                if (ObjectUtils.isNotEmpty(profileInfo.get("profileType")))
                    profileType = profileInfo.get("profileType").toString();

            }
        }
        return profileType;
    }

    private Map<String, Object> findConnectionMigrationProcessVariables(OrderFlowContext orderFlowContext) {
        Map<String, Object> processVariables = new HashMap<>();
        var order = orderFlowContext.getOrder();
        var newAccount = order.getProfile() != null && ObjectUtils.isNotEmpty(order.getProfile().getAccount())
                && StringUtils.isEmpty(order.getProfile().getAccount().getAccountId());
        var isPostpaidMigration = "1".equals(order.getServiceManagement().getDestinationConnectionType());
        processVariables.put("isPostpaidMigration", isPostpaidMigration);
        processVariables.put("isNewAccount", newAccount);
        processVariables.put("erpAccountCreation", isPostpaidMigration && newAccount);
        processVariables.put("bookDeposit", "true".equals(order.getServiceManagement().getDepositReqd()));
        processVariables.put("hasContract", ObjectUtils.isNotEmpty(order.getServiceManagement().getContract()));
        var hasAttachment = ObjectUtils.isNotEmpty(order.getProfile())
                && ObjectUtils.isNotEmpty(order.getProfile().getAttachments());
        processVariables.put("hasAttachment", hasAttachment);
        var enrichmentResults = orderFlowContext.getEnrichmentResults();
        if (enrichmentResults != null) {
            processVariables.put("esbAddSubCallRqd", enrichmentResults.get("esbCallRqd") != null && Boolean.parseBoolean((String) enrichmentResults.get("esbCallRqd")));
        }
        return processVariables;
    }

    public void findChangeSubscriptionProcessVariables(OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
        var order = orderFlowContext.getOrder();
        var chargingPattern = "";
        if (orderFlowContext.getEnrichmentResults().containsKey("serviceInfo")) {
            var serviceInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("serviceInfo"),
                    LinkedHashMap.class);
            chargingPattern = serviceInfo.get("chargingPattern").toString();
        }
        var isPostpaid = "1".equals(chargingPattern);
        processVariables.put("isPostpaid", isPostpaid);
        processVariables.put("creditLimitUpdate", "true".equals(order.getServiceManagement().getCreditLimitUpdateReqd()));
        processVariables.put("bookDeposit", "true".equals(order.getServiceManagement().getDepositReqd()));
        processVariables.put("hasContract", ObjectUtils.isNotEmpty(order.getServiceManagement().getContract()));
        processVariables.put("futureOrder", true);
        processVariables.put("futureDatePresent", true);
        processVariables.put("newBasePlan", true);
        if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment()) &&
                StringUtils.isNotEmpty(orderFlowContext.getOrder().getPayment().getUpfrontPayment()) &&
                orderFlowContext.getOrder().getPayment().getUpfrontPayment().equalsIgnoreCase("true")) {
            processVariables.put("paymentCallReqd", true);
        } else {
            processVariables.put("paymentCallReqd", false);

        }
    }


    public void findSubscriptionProcessVariables(OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
        var order = orderFlowContext.getOrder();
        if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getSubscriptions())) {
            processVariables.put("IsSubscriptionPresent", true);
        } else {
            processVariables.put("IsSubscriptionPresent", false);

        }
        processVariables.put("isLegacy", order.isLegacy());
    }


    private boolean paymentChannelCheckForERPDeposit(OrderFlowContext orderFlowContext) {
        var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                CacheConstants.CacheKeys.PAYMENT_CHANNEL_FOR_ERP_DEPOSIT.toString());
        if (ObjectUtils.isNotEmpty(appConfig) && ObjectUtils.isNotEmpty(appConfig.getNgTableData())) {
            var configValue = appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
            if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder())
                    && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment())
                    && StringUtils.isNotEmpty(orderFlowContext.getOrder().getPayment().getPaymentChannel())) {
                var paymentChannel = orderFlowContext.getOrder().getPayment().getPaymentChannel();
                return StringUtils.isNotEmpty(configValue) && configValue.contains(paymentChannel) ? true : false;
            } else if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getDeposit())
                    && StringUtils.isNotEmpty(orderFlowContext.getOrder().getDeposit().getPaymentChannel())
                    && configValue.contains(orderFlowContext.getOrder().getDeposit().getPaymentChannel())) {
                var depositChannel = orderFlowContext.getOrder().getDeposit().getPaymentChannel();
                return StringUtils.isNotEmpty(configValue) && configValue.contains(depositChannel) ? true : false;
            }
        }
        return false;
    }

    private void findTransferOfServiceProcessVariables(OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
        var order = orderFlowContext.getOrder();
        var enrichmentResults = orderFlowContext.getEnrichmentResults();
        boolean isPostpaid = false;
        boolean newAccount = false;
        if (enrichmentResults != null && enrichmentResults.get("serviceInfo") != null) {
            var serviceInfo = objectMapper.convertValue(enrichmentResults.get("serviceInfo"),
                    ServiceInfo.class);
            isPostpaid = "1".equals(serviceInfo.getChargingPattern());
        }

        if (enrichmentResults != null && enrichmentResults.get("profileInfo") != null) {
            var profileInfo = objectMapper.convertValue(enrichmentResults.get("profileInfo"),
                    LinkedHashMap.class);
            try {
                //processVariables.put("destinationProfileType", profileInfo.get("profileType").toString());
                //processVariables.put("customerName", profileInfo.get("customerName").toString());
            } catch (Exception e) {
                log.info("Message::", e.getMessage(), e);
            }
        }
        var newProfile = StringUtils.isEmpty(order.getServiceManagement().getDestinationProfileId());
        processVariables.put("newProfile", newProfile);
        if (newProfile) {
            newAccount = true;
        } else {
            newAccount = StringUtils.isEmpty(order.getServiceManagement().getDestinationAccountId());
            newAccount = newAccount ? ObjectUtils.isNotEmpty(order.getProfile()) && ObjectUtils.isNotEmpty(order.getProfile().getAccount()) : false;
        }
        processVariables.put("isPostpaid", isPostpaid);
        processVariables.put("newAccount", newAccount);
        processVariables.put("newPostpaidAccount", newAccount && isPostpaid);
        String profileType = null;
        if (orderFlowContext.getOrder().getProfile() != null
                && orderFlowContext.getOrder().getProfile().getPartyCharacteristic() != null) {
            var partyCharacteristic = orderFlowContext.getOrder().getProfile().getPartyCharacteristic().stream()
                    .filter(characteristic -> "profileType".equals(characteristic.getName())).findAny().orElse(null);
            if (partyCharacteristic != null)
                profileType = partyCharacteristic.getValue();
            processVariables.put("isCorporateAccount", StringUtils.isNotEmpty(profileType) && profileType.equals("1")
                    && orderFlowContext.getOrder().getProfile().getAccount().getChargingPattern().equals("2"));
        }
        if (StringUtils.isNotEmpty(orderFlowContext.getEnrichmentType()) && orderFlowContext.getEnrichmentType().equalsIgnoreCase("TransferOfServiceExistingProfile")) {
            processVariables.put("isCorporateAccount", true);
        }
        if (StringUtils.isNotEmpty(orderFlowContext.getEntityId()) && StringUtils.equalsIgnoreCase(orderFlowContext.getEntityId(), "200")) {
            processVariables.put("isCorporateAccount", false);
        }

        processVariables.put("skipCreateAO", false);
        if (orderFlowContext.getEnrichmentResults().containsKey("contactIdInfo")) {
            var contactIdInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("contactIdInfo"),
                    LinkedHashMap.class);
            var contactId = contactIdInfo.get("contactId").toString();
            if (StringUtils.isNotEmpty(contactId)) {
                processVariables.put("skipCreateAO", true);
                log.info("skipCreateAO " + processVariables.get("skipCreateAO"));
            }
        }
        log.info("skipCreateAO is " + processVariables.get("skipCreateAO"));

        var hasAttachment = ObjectUtils.isNotEmpty(order.getProfile())
                && ObjectUtils.isNotEmpty(order.getProfile().getAttachments());
        processVariables.put("hasAttachment", !newProfile && hasAttachment);
        processVariables.put("changePlan", ObjectUtils.isNotEmpty(order.getServiceManagement().getSubscriptions()) && !order.getServiceManagement().getSubscriptions().isEmpty());
    }

    private void findSuspendAndBarringProcessVariables(OrderFlowContext orderFlowContext,
                                                                      String orderType,Map<String, Object> processVariables) {

        String addonToFetch = null;
        String connectionType = null;
        var isLineBarred = false;
        var isBarringPlansConfigured = false;
        var isSuspendForSoftBarring = false;
        var isSuspendForHardBarring = false;
        String currentReasonCode = null;
        var subscriptionCallFlowReqd = false;
        var SOMCallReqd = false;
        var SMCallReqd = true;
        var order = orderFlowContext.getOrder();

        var enrichmentResults = orderFlowContext.getEnrichmentResults();
        var hasAttachment = ObjectUtils.isNotEmpty(order.getAttachments());
        processVariables.put("hasAttachment", hasAttachment);
        if (enrichmentResults != null && ObjectUtils.isNotEmpty(enrichmentResults.get("serviceInfo"))) {
            var serviceInfo = objectMapper.convertValue(enrichmentResults.get("serviceInfo"), LinkedHashMap.class);

            if (ObjectUtils.isNotEmpty(serviceInfo.get("chargingPattern")))
                connectionType = serviceInfo.get("chargingPattern").toString();

            if (ObjectUtils.isNotEmpty(serviceInfo.get("islineBarred")))
                isLineBarred = Boolean.parseBoolean(serviceInfo.get("islineBarred").toString());
            if (ObjectUtils.isNotEmpty(serviceInfo.get("reasonCode")))
                currentReasonCode = serviceInfo.get("reasonCode").toString();
        }
        try {
            var ocsReq = cache
                    .getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "OCS_SUSPEND_REQ")
                    .getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
            if ("true".equalsIgnoreCase(ocsReq) && "1".equals(connectionType)) {
                processVariables.put("isocsReq", true);
            } else {
                processVariables.put("isocsReq", false);
            }
        } catch (Exception e) {

            processVariables.put("isocsReq", false);
            log.info("No configuration found for OCS_SUSPEND_REASONCODE, hence skipping OCS call flow");
        }
        if (OrderTypes.SUSPEND_SERVICE.equals(orderType)) {
            processVariables.put("paymentCallReqd", false);
        }
        if ("1".equals(connectionType)) {
            if (OrderTypes.SUSPEND_SERVICE.equals(orderType)) {
                var isBarringCallFlow = false;
                var incomingReasonCode = orderFlowContext.getOrder().getServiceManagement().getReason();
                String softBarringReasonCode = null;
                String hardBarringReasonCode = null;

                var softBarringCodeConfig = cache.getCacheDetailsFromDBMap(
                        CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "SOFT_BARRING_REASON_CODE");
                if (ObjectUtils.isNotEmpty(softBarringCodeConfig) && ObjectUtils.isNotEmpty(
                        softBarringCodeConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()))) {
                    softBarringReasonCode = softBarringCodeConfig.getNgTableData()
                            .get(CacheConstants.CacheFields.CONFIG_VALUE.name());
                }

                var hardBarringCodeConfig = cache.getCacheDetailsFromDBMap(
                        CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "HARD_BARRING_REASON_CODE");
                if (ObjectUtils.isNotEmpty(hardBarringCodeConfig) && ObjectUtils.isNotEmpty(
                        hardBarringCodeConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()))) {
                    hardBarringReasonCode = hardBarringCodeConfig.getNgTableData()
                            .get(CacheConstants.CacheFields.CONFIG_VALUE.name());
                }

                if (StringUtils.equalsAny(incomingReasonCode, softBarringReasonCode, hardBarringReasonCode))
                    isBarringCallFlow = true;

                if (StringUtils.equals(incomingReasonCode, softBarringReasonCode)) {
                    isBarringCallFlow = true;
                    isSuspendForSoftBarring = true;
                    enrichmentResults.put("barringType", "softBar");

                } else if (StringUtils.equals(incomingReasonCode, hardBarringReasonCode)) {
                    isBarringCallFlow = true;
                    isSuspendForHardBarring = true;
                    enrichmentResults.put("barringType", "hardBar");

                }

                processVariables.put("isBarringCallFlow", isBarringCallFlow);

            }

            if (orderType.equals("SoftBarring") || isSuspendForSoftBarring)
                addonToFetch = "SOFTBARRING_ADDONS";
            else if (orderType.equals("HardBarring") || isSuspendForHardBarring)
                addonToFetch = "HARDBARRING_ADDONS";

            var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                    addonToFetch);
            if (ObjectUtils.isNotEmpty(appConfig) && ObjectUtils
                    .isNotEmpty(appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()))) {
                String planIds = appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
                orderFlowContext.getAttributes().put("planIds", planIds);
                isBarringPlansConfigured = true;
            }
            if (isBarringPlansConfigured)
                subscriptionCallFlowReqd = true;
            else
                log.info("No plans configured,hence skipping subscription call flow");

            if (!isLineBarred && isBarringPlansConfigured)
                SOMCallReqd = true;
            else
                log.info("isBarringPlansConfigured:{} & isLineBarred:{} for the service,hence skipping SOM call",
                        isBarringPlansConfigured, isLineBarred);

            if (BooleanUtils.toBoolean(orderFlowContext.getAttributes().get("isSubscriberSuspended")))
                SMCallReqd = false;

            processVariables.put("subscriptionCallFlowReqd", subscriptionCallFlowReqd);
            processVariables.put("SOMCallReqd", SOMCallReqd);
            processVariables.put("SMCallReqd", SMCallReqd);
        } else {
            processVariables.put("isBarringCallFlow", false);
        }
        processVariables.put("isLineBarred", isLineBarred);

    }

    private void findResumeAndUnbarringProcessVariables(OrderFlowContext orderFlowContext, String orderType, Map<String, Object> processVariables) {

        var isLineBarred = false;
        var isBarringPlansConfigured = false;
        String connectionType = null;
        String currentReasonCode = null;
        var order = orderFlowContext.getOrder();
        var subscriptionCallFlowReqd = false;
        var SOMCallReqd = false;

        var enrichmentResults = orderFlowContext.getEnrichmentResults();
        var hasAttachment = ObjectUtils.isNotEmpty(order.getAttachments());
        processVariables.put("hasAttachment", hasAttachment);
        if (enrichmentResults != null && ObjectUtils.isNotEmpty(enrichmentResults.get("serviceInfo"))) {
            var serviceInfo = objectMapper.convertValue(enrichmentResults.get("serviceInfo"), LinkedHashMap.class);
            if (ObjectUtils.isNotEmpty(serviceInfo.get("chargingPattern")))
                connectionType = serviceInfo.get("chargingPattern").toString();
            processVariables.put("isSuspendResumeReq", "2".equals(connectionType));
            try {
                var ocsReq = cache
                        .getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "OCS_RESUME_REQ")
                        .getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
                if ("true".equalsIgnoreCase(ocsReq) && "1".equals(connectionType)) {
                    processVariables.put("isSuspendResumeReq", true);
                } else if ("false".equalsIgnoreCase(ocsReq) && "1".equals(connectionType)) {
                    processVariables.put("isSuspendResumeReq", false);
                }
            } catch (Exception e) {

                processVariables.put("isSuspendResumeReq", true);
                log.info("No configuration found for OCS_RESUME_REASONCODE, hence skipping OCS call flow");
            }
            if (ObjectUtils.isNotEmpty(serviceInfo.get("islineBarred")))
                isLineBarred = Boolean.parseBoolean(serviceInfo.get("islineBarred").toString());

            if (ObjectUtils.isNotEmpty(serviceInfo.get("reasonCode")))
                currentReasonCode = serviceInfo.get("reasonCode").toString();
        }
        try {
            var configReasonCode = cache
                    .getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "OCS_RESUME_REASONCODE")
                    .getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
            var reasonArray = StringUtils.split(currentReasonCode, ",");
            log.info("ocs call required:{}", StringUtils.containsAny(currentReasonCode, reasonArray));
            if (StringUtils.containsAny(currentReasonCode, reasonArray)) {
                processVariables.put("isocsReq", true);
            } else {
                processVariables.put("isocsReq", false);
            }
        } catch (Exception e) {

            processVariables.put("isocsReq", false);
            log.info("No configuration found for OCS_RESUME_REASONCODE, hence skipping OCS call flow");
        }

        if (OrderTypes.RESUME_SERVICE.equals(orderType)) {
            processVariables.put("paymentCallReqd", false);
        }
        if ("1".equals(connectionType)) {
            isBarringPlansConfigured = getConfiguredPlanIdsForUnbarringActions(orderFlowContext);

            if (isBarringPlansConfigured)
                subscriptionCallFlowReqd = true;
            else
                log.info("No plans configured,hence skipping subscription call flow");

            if (!isLineBarred && isBarringPlansConfigured)
                SOMCallReqd = true;
            else
                log.info("isBarringPlansConfigured:{} & isLineBarred:{} for the service,hence skipping SOM call",
                        isBarringPlansConfigured, isLineBarred);

            if (OrderTypes.RESUME_SERVICE.equals(orderType)) {
                var isUnbarringCallFlow = false;
                if ("1".equals(connectionType)) {
                    String softBarringReasonCode = null;
                    String hardBarringReasonCode = null;

                    var softBarringCodeConfig = cache.getCacheDetailsFromDBMap(
                            CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "SOFT_BARRING_REASON_CODE");
                    if (ObjectUtils.isNotEmpty(softBarringCodeConfig)
                            && ObjectUtils.isNotEmpty(softBarringCodeConfig.getNgTableData()
                            .get(CacheConstants.CacheFields.CONFIG_VALUE.name()))) {
                        softBarringReasonCode = softBarringCodeConfig.getNgTableData()
                                .get(CacheConstants.CacheFields.CONFIG_VALUE.name());
                    }

                    var hardBarringCodeConfig = cache.getCacheDetailsFromDBMap(
                            CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "HARD_BARRING_REASON_CODE");
                    if (ObjectUtils.isNotEmpty(hardBarringCodeConfig)
                            && ObjectUtils.isNotEmpty(hardBarringCodeConfig.getNgTableData()
                            .get(CacheConstants.CacheFields.CONFIG_VALUE.name()))) {
                        hardBarringReasonCode = hardBarringCodeConfig.getNgTableData()
                                .get(CacheConstants.CacheFields.CONFIG_VALUE.name());
                    }

                    if (StringUtils.equalsAny(currentReasonCode, softBarringReasonCode, hardBarringReasonCode))
                        isUnbarringCallFlow = true;
                }
                processVariables.put("isUnbarringCallFlow", isUnbarringCallFlow);

            }

            processVariables.put("subscriptionCallFlowReqd", subscriptionCallFlowReqd);
            processVariables.put("SOMCallReqd", SOMCallReqd);

        } else {
            processVariables.put("isUnbarringCallFlow", false);
        }
        processVariables.put("isLineBarred", isLineBarred);
    }


    private boolean getConfiguredPlanIdsForUnbarringActions(OrderFlowContext orderFlowContext) {
        String softBarringAddon = null;
        String hardBarringAddon = null;
        ArrayList<String> barringAddons = new ArrayList<String>();
        var isPlansConfigured = false;

        var softBarringAddonConfig = cache
                .getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "SOFTBARRING_ADDONS");
        var hardBarringAddonConfig = cache
                .getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "HARDBARRING_ADDONS");

        if (ObjectUtils.isNotEmpty(softBarringAddonConfig) && ObjectUtils.isNotEmpty(
                softBarringAddonConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()))) {
            softBarringAddon = softBarringAddonConfig.getNgTableData()
                    .get(CacheConstants.CacheFields.CONFIG_VALUE.name());
            barringAddons.add(softBarringAddon);

        }
        if (ObjectUtils.isNotEmpty(hardBarringAddonConfig) && ObjectUtils.isNotEmpty(
                hardBarringAddonConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()))) {
            hardBarringAddon = hardBarringAddonConfig.getNgTableData()
                    .get(CacheConstants.CacheFields.CONFIG_VALUE.name());
            barringAddons.add(hardBarringAddon);
        }

        if (ObjectUtils.isNotEmpty(barringAddons)) {
            orderFlowContext.getObjectAttributes().put("configuredBarringPlanIds", barringAddons);
            isPlansConfigured = true;
        }

        return isPlansConfigured;

    }

    private void findTerminateServiceProcessVariables(OrderFlowContext orderFlowContext,Map<String, Object> processVariables) {
        var order = orderFlowContext.getOrder();
        processVariables.put("paymentCallReqd", false);

        processVariables.put("hasContract", ObjectUtils.isNotEmpty(order.getServiceManagement().getContract()));
        var enrichmentResults = orderFlowContext.getEnrichmentResults();

        var attribues = orderFlowContext.getAttributes();
        processVariables.put("deviceId", ObjectUtils.isNotEmpty(attribues.get("deviceId")));
        if (enrichmentResults != null && enrichmentResults.get("serviceInfo") != null) {
            var serviceInfo = objectMapper.convertValue(enrichmentResults.get("serviceInfo"),
                    ServiceInfo.class);
            processVariables.put("isPostpaid", "1".equals(serviceInfo.getChargingPattern()));
            processVariables.put("esbAddSubCallRqd", enrichmentResults.get("esbCallRqd") != null && Boolean.parseBoolean((String) enrichmentResults.get("esbCallRqd")));
        } else {
            processVariables.put("isPostpaid", false);
        }
        processVariables.put("fetchAccountCallReqd", checkFetchAccountCallReqd(orderFlowContext));
    }

    private Object checkFetchAccountCallReqd(OrderFlowContext orderFlowContext) {
        List<String> mvnoConfigValue = null;
        CacheTableDataDTO mvnoConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.SEVEN_DAYS_POLICY_CHECK_REQD_MVNO.toString());
        if (mvnoConfig != null)
            mvnoConfigValue = Arrays.asList(mvnoConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString()).split(","));
        return ((mvnoConfigValue != null && !mvnoConfigValue.isEmpty()) && OrderTypes.TERMINATE_SERVICE.equalsIgnoreCase(orderFlowContext.getOrder().getOrderType()) && mvnoConfigValue.contains(orderFlowContext.getEntityId()));
    }

    public Map<String, Object> createApprovalProcessVariables(String orderType, OrderFlowContext orderFlowContext) {

        Map<String, Object> processVariables = new HashMap<String, Object>();
        processVariables.put(WorkFlowConstants.WorkFlowProcessVariables.STATUS.toString(),
                WorkFlowConstants.WorkFlowProcessVariables.STATUS_SUCCESS.toString());
        processVariables.put(WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString(),
                createWorkflowData(orderFlowContext));
        processVariables.put("priority", checkPriorityForOrderType(orderFlowContext));
        processVariables.put("somCancelSubReq", false);

        return processVariables;
    }

    private Map<String, Object> findActivateProcessVariables(OrderFlowContext orderFlowContext) {
        Map<String, Object> processVariables = new HashMap<String, Object>();
        log.info(" WaitingDelay :: {}", orderFlowContext.getOrder().getServiceManagement().getWaitingDelay());
        try {
            if (StringUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getWaitingDelay())
                    && orderFlowContext.getOrder().getServiceManagement().getWaitingDelay().equalsIgnoreCase("true")) {
                var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                        "ACTIVATE_WAITING_DURATION");
                if (appConfig != null && StringUtils.isNotEmpty(appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name())))
                    processVariables.put("service_enable_time_delay", appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()));
                else
                    processVariables.put("service_enable_time_delay", "PT60S");
                log.info("Setting the delay during Service Activation :: {}", processVariables.get("service_enable_time_delay"));
            } else {
                processVariables.put("service_enable_time_delay", null);
                log.info("Not setting the delay during Service Activation:: {}", processVariables.get("service_enable_time_delay"));
            }
        } catch (Exception e) {
            log.error("Exception occurred in findActivateProcessVariables", e);
        }

        return processVariables;
    }

    public boolean findFutureOrderRequired(String orderType, String entityId) {
        boolean futureOrderRequired = false;
        try {
            CacheTableDataDTO futureOrderConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_FUTURE_ORDER_CONFIG.name(), orderType + GenericConstants.OPERATOR_UNDERSCORE + entityId);
            if (futureOrderConfig != null) {
                futureOrderRequired = BooleanUtils.toBoolean(futureOrderConfig.getNgTableData().get(CacheConstants.CacheFields.FUTURE_ORDER_REQD.name()));
            }
        } catch (Exception e) {
            log.error("Exception occurred in findFutureOrderRequired", e);
        }
        return futureOrderRequired;
    }

    private boolean isEnrichmentFlowEnabled() {
        var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                "ENRICHMENT_LVL_ORDER_FLOW_ENABLED");
        return appConfig != null && "true".equalsIgnoreCase(appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()));
    }


}
