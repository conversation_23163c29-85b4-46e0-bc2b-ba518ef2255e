package in.co.sixdee.bss.com.orderorchestrator.config.camunda.history;

import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.ProcessEngineException;
import org.camunda.bpm.engine.impl.context.Context;
import org.camunda.bpm.engine.impl.db.entitymanager.DbEntityManager;
import org.camunda.bpm.engine.impl.history.HistoryLevel;
import org.camunda.bpm.engine.impl.history.event.HistoryEvent;
import org.camunda.bpm.engine.impl.history.event.HistoryEventType;
import org.camunda.bpm.engine.impl.persistence.entity.ExecutionEntity;
import org.camunda.bpm.engine.impl.persistence.entity.IncidentEntity;
import org.camunda.bpm.engine.impl.persistence.entity.MessageEntity;
import org.camunda.bpm.engine.runtime.VariableInstance;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.camunda.bpm.engine.impl.history.event.HistoryEventTypes.*;

@Log4j2
public class PerProcessHistoryLevel implements HistoryLevel {

	public static final PerProcessHistoryLevel INSTANCE = new PerProcessHistoryLevel();

	protected Map<String, HistoryLevel> historyLevels                  = new HashMap<>();
	protected Map<String, HistoryLevel> delegateHistoryLevelPerProcess = new HashMap<String, HistoryLevel>();


	public static PerProcessHistoryLevel getInstance() {
		return INSTANCE;
	}

	public PerProcessHistoryLevel() {
		historyLevels.put(HISTORY_LEVEL_NONE.getName(), HISTORY_LEVEL_NONE);
		historyLevels.put(HISTORY_LEVEL_ACTIVITY.getName(), HISTORY_LEVEL_ACTIVITY);
		historyLevels.put(HISTORY_LEVEL_AUDIT.getName(), HISTORY_LEVEL_AUDIT);
		historyLevels.put(HISTORY_LEVEL_FULL.getName(), HISTORY_LEVEL_FULL);
	}

	public void addHistoryLevels(List<HistoryLevel> historyLevels) {
		for (HistoryLevel historyLevel : historyLevels) {
			this.historyLevels.put(historyLevel.getName(), historyLevel);
		}
	}

	public int getId() {
		return 12;
	}

	public String getName() {
		return "per-process";
	}

	public boolean isHistoryEventProduced(HistoryEventType eventType, Object entity) {
		if (entity == null || !shouldProduceHistoryEvent(eventType)) {
			return false;
		}
		return isDelegateHistoryLevelEventProduced(eventType, entity);
	}

	protected void setDelegateHistoryLevel(ExecutionEntity execution) {
		Collection<CamundaProperty> camundaProperties = getCamundaProperties(execution);
		boolean isHistoryLevelSet = false;
		if (camundaProperties != null) {
			for (CamundaProperty camundaProperty : camundaProperties) {
				if ("history".equals(camundaProperty.getCamundaName())) {
					String historyLevelName = camundaProperty.getCamundaValue();
					HistoryLevel historyLevel = historyLevels.get(historyLevelName);
					delegateHistoryLevelPerProcess.put(execution.getProcessInstanceId(), historyLevel);
					isHistoryLevelSet = true;
					break;
				}
			}
		}
		if (!isHistoryLevelSet) {
			// set the default history level as audit
			delegateHistoryLevelPerProcess.put(execution.getProcessInstanceId(), HistoryLevel.HISTORY_LEVEL_AUDIT);
		}
	}

	protected Collection<CamundaProperty> getCamundaProperties(ExecutionEntity execution) {
		Process process = (Process) execution.getBpmnModelInstance().getDefinitions().getUniqueChildElementByType(Process.class);
		ExtensionElements extensionElements = process.getExtensionElements();
		if (extensionElements != null) {
			CamundaProperties properties = (CamundaProperties) extensionElements.getUniqueChildElementByType(CamundaProperties.class);
			if (properties != null) {
				return properties.getCamundaProperties();
			}
		}
		return null;
	}

	protected HistoryLevel getDelegateHistoryLevel(String processInstanceId) {
		HistoryLevel delegateHistoryLevel = delegateHistoryLevelPerProcess.get(processInstanceId);

		if (delegateHistoryLevel == null) {
			DbEntityManager dbEntityManager = Context.getCommandContext().getDbEntityManager();
			ExecutionEntity processInstance = dbEntityManager.selectById(ExecutionEntity.class, processInstanceId);
			setDelegateHistoryLevel(processInstance);
			delegateHistoryLevel = delegateHistoryLevelPerProcess.get(processInstanceId);
		}

		return delegateHistoryLevel;
	}

	protected boolean isDelegateHistoryLevelEventProduced(HistoryEventType historyEventType, Object entity) {
		String processInstanceId = getProcessInstanceId(entity);
		HistoryLevel delegateHistoryLevel = getDelegateHistoryLevel(processInstanceId);

		return delegateHistoryLevel != null && delegateHistoryLevel.isHistoryEventProduced(historyEventType, entity);
	}

	protected String getProcessInstanceId(Object entity) {
		if (entity instanceof ExecutionEntity) {
			return ((ExecutionEntity) entity).getProcessInstanceId();
		} else if (entity instanceof VariableInstance) {
			return ((VariableInstance) entity).getProcessInstanceId();
		} else if (entity instanceof HistoryEvent) {
			return ((HistoryEvent) entity).getProcessInstanceId();
		} else if (entity instanceof MessageEntity) {
			return ((MessageEntity) entity).getProcessInstanceId();
		} else if (entity instanceof IncidentEntity) {
			return ((IncidentEntity) entity).getProcessInstanceId();
		} else {
			throw new ProcessEngineException("Unable to find process instance id for class " + entity.getClass().getName());
		}
	}


	public boolean shouldProduceHistoryEvent(HistoryEventType eventType) {
		return PROCESS_INSTANCE_START == eventType
				|| PROCESS_INSTANCE_UPDATE == eventType
				|| PROCESS_INSTANCE_MIGRATE == eventType
				|| PROCESS_INSTANCE_END == eventType

				|| TASK_INSTANCE_CREATE == eventType
				|| TASK_INSTANCE_UPDATE == eventType
				|| TASK_INSTANCE_MIGRATE == eventType
				|| TASK_INSTANCE_COMPLETE == eventType
				|| TASK_INSTANCE_DELETE == eventType

				|| ACTIVITY_INSTANCE_START == eventType
				|| ACTIVITY_INSTANCE_UPDATE == eventType
				|| ACTIVITY_INSTANCE_MIGRATE == eventType
				|| ACTIVITY_INSTANCE_END == eventType

				|| CASE_INSTANCE_CREATE == eventType
				|| CASE_INSTANCE_UPDATE == eventType
				|| CASE_INSTANCE_CLOSE == eventType

				|| CASE_ACTIVITY_INSTANCE_CREATE == eventType
				|| CASE_ACTIVITY_INSTANCE_UPDATE == eventType
				|| CASE_ACTIVITY_INSTANCE_END == eventType
				|| VARIABLE_INSTANCE_CREATE == eventType
				|| VARIABLE_INSTANCE_UPDATE == eventType
				|| VARIABLE_INSTANCE_MIGRATE == eventType
				|| VARIABLE_INSTANCE_DELETE == eventType

				|| FORM_PROPERTY_UPDATE == eventType
				;
	}
}
