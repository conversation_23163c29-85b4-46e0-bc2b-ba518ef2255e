package in.co.sixdee.bss.com.orderorchestrator.config.messaging;

import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.IOrderOrchestrator;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.RabbitMessageProcessingException;
import in.co.sixdee.bss.com.orderorchestrator.service.CancelOrderService;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.util.function.Consumer;

@Configuration
@Log4j2
@Order(Ordered.LOWEST_PRECEDENCE)
public class MessageListener {

    @Autowired
    IOrderOrchestrator orderOrchestrator;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    CancelOrderService cancelOrderService;

    @Bean
    public Consumer<OrderFlowContext> receiveOrderMessages() {
        return orderFlowContext -> {
            try {
                log.info("Received order from the message queue :: {} ", objectMapper.writeValueAsString(orderFlowContext));

                if (orderFlowContext != null && orderFlowContext.getAttributes() != null && (orderFlowContext.getAttributes().get("rollbackProcessId") != null ||(StringUtils.isNotEmpty(orderFlowContext.getAttributes().get("cancelWithoutProcessId"))))) {
                    cancelOrderService.processCancelOrderRequest(orderFlowContext);
                } else {
                    orderOrchestrator.processRequest(orderFlowContext);
                }
            } catch (Exception e) {
                log.error("Exception occurred while processing request {}", e.getMessage());
                throw new RabbitMessageProcessingException(e.getMessage());
            }
        };
    }

}

