package in.co.sixdee.bss.com.orderorchestrator.model.mnp.disconnectServiceInternal;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "disconnectServiceInternal")
@XmlAccessorType(XmlAccessType.FIELD)
public class DisconnectServiceInternal {

	@XmlElement(name = "Request" )
	private Request request;

	public Request getRequest() {
		return request;
	}

	public void setRequest(Request request) {
		this.request = request;
	}

}
