package in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderPayloadService;
import in.co.sixdee.bss.common.core.AppInstanceIdManager;
import in.co.sixdee.bss.common.util.ApplicationProcessContext;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.CFSRef;
import in.co.sixdee.bss.om.model.dto.order.Service;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.HashMap;

@Component
@Log4j2
public class ExecutionContextListener implements ExecutionListener {

    protected ApplicationProcessContext processContext;

    @Autowired
    protected ProcessDataAccessor processDataAccessor;

    @Autowired
    private AppInstanceIdManager appInstanceSequence;

    @Autowired
    private OrderPayloadService orderPayloadService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void notify(DelegateExecution execution) throws Exception {
        var service = new Service();
        Map<String, Object> currentExecutionMap = new HashMap<>();

        var orderFlowContext = processDataAccessor.getOrderFlowContext(execution);
        createApplicationProcessContext(orderFlowContext);
        var payloadContext = orderPayloadService.getOrderFlowContext(orderFlowContext.getOrder().getOrderId());

        if (payloadContext == null)
            return;
        if (orderFlowContext.getWorkflowData().containsKey("currentExecution")) {
            currentExecutionMap = (Map<String, Object>) orderFlowContext.getWorkflowData().get("currentExecution");
            service = objectMapper.convertValue(currentExecutionMap.get("executionData"), Service.class);
        }

        if (StringUtils.equals(ExecutionListener.EVENTNAME_START, execution.getEventName())) {
            var sgId = service.getGroupId();
            var serviceId = service.getServiceId();
            var sgGroup = Objects.requireNonNull(payloadContext.getOrder().getProfile().getAccount().getServiceGroups().stream().filter(
                    sg -> sg.getId() != null && sg.getId().equals(sgId)).findFirst().orElse(null));
            service.getSubscriptions().forEach(sub -> {
                if (ObjectUtils.isNotEmpty(sgGroup.getSubscriptions())) {
                    if (execution.getCurrentActivityId().equalsIgnoreCase(GenericConstants.BS_ADD_SUBSCRIPTION)) {
                        setChargesToWorkflowData(sgGroup.getSubscriptions(), sub);
                    } else if (execution.getCurrentActivityId().equalsIgnoreCase(GenericConstants.NCC_CREATE_SERVICE_HANDLER)) {
                        setCfssNccToWorkflowData(sgGroup.getSubscriptions(), sub, new ArrayList<CFSRef>());
                    }
                }
            });
            Service serviceFromPayload = sgGroup.getServices().stream().filter(svc -> svc.getServiceId().equals(serviceId)).findFirst().orElse(null);
            service.getSubscriptions().forEach(sub -> {
                if (ObjectUtils.isNotEmpty(serviceFromPayload.getSubscriptions())) {
                    if (execution.getCurrentActivityId().equalsIgnoreCase(GenericConstants.BS_ADD_SUBSCRIPTION)) {
                        setChargesToWorkflowData(serviceFromPayload.getSubscriptions(), sub);
                    } else if (execution.getCurrentActivityId().equalsIgnoreCase(GenericConstants.NCC_CREATE_SERVICE_HANDLER)) {
                        setCfssNccToWorkflowData(serviceFromPayload.getSubscriptions(), sub, new ArrayList<CFSRef>());
                    }
                }
            });
        } else if (StringUtils.equals(ExecutionListener.EVENTNAME_END, execution.getEventName())) {
            if (execution.getCurrentActivityId().equalsIgnoreCase(GenericConstants.BS_ADD_SUBSCRIPTION)) {
                service.getSubscriptions().forEach(sub -> {
                    sub.setCharges(null);
                });
                if (currentExecutionMap != null && !currentExecutionMap.isEmpty())
                    currentExecutionMap.remove("execution");
            } else if (execution.getCurrentActivityId().equalsIgnoreCase(GenericConstants.NCC_CREATE_SERVICE_HANDLER)) {
                service.getSubscriptions().forEach(sub -> {
                    sub.setCfss(null);
                });
            } else if (execution.getCurrentActivityId().equalsIgnoreCase(GenericConstants.BS_BOOKDEPOSIT_HANDLER)) {
//              orderFlowContext.getWorkflowData().remove("callBackDetails");
                orderFlowContext.getOrder().setPayment(null);
            }
        }
        try {
            currentExecutionMap.put("executionData",
                    com.bazaarvoice.jolt.JsonUtils.jsonToObject(objectMapper.writeValueAsString(service)));
        } catch (JsonProcessingException e) {
            log.error("Exception occurred while creating executionData", e);
        }
        orderFlowContext.getWorkflowData().put("currentExecution", currentExecutionMap);
        processDataAccessor.setOrderFlowContext(execution, orderFlowContext);
    }





    private void createApplicationProcessContext(OrderFlowContext orderFlowContext) {
        processContext = new ApplicationProcessContext(appInstanceSequence.getInstanceId(), orderFlowContext.getTraceId(),
                orderFlowContext.getRequestId(), orderFlowContext.getChannel(), orderFlowContext.getChannel(), orderFlowContext.getEntityId());
        processContext.setMdc();
    }

    private void setChargesToWorkflowData(List<Subscription> subscriptions, Subscription subscription) {
        subscriptions.stream().filter(sub -> subscription.getPlanId().equalsIgnoreCase(sub.getPlanId())).
                forEach(sub -> subscription.setCharges(sub.getCharges()));
    }

    private void setCfssNccToWorkflowData(List<Subscription> subscriptions, Subscription subscription, ArrayList<CFSRef> cfssList) {
        subscriptions.stream().filter(sub -> subscription.getPlanId().equalsIgnoreCase(sub.getPlanId())).
                forEach(sub -> {
                    sub.getCfss().stream().filter(cfs -> cfs.getName().equalsIgnoreCase(GenericConstants.CFS_M1_Base)).
                            forEach(cfs -> cfssList.add(cfs));
                    subscription.setCfss(cfssList);
                });
    }

}
