package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import in.co.sixdee.bss.com.orderorchestrator.config.ApplicationProperties;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.SubOrderEntity;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

@Repository
@RequiredArgsConstructor
@Log4j2
@Transactional
public class NativeSubOrderRepository {


	protected final ApplicationProperties applicationProperties;

	@PersistenceContext
	protected EntityManager em;


	public void save(SubOrderEntity subOrderEntity) {
		var session = em.unwrap(Session.class);
		final String sql = "insert into COM_SUB_ORDER_DETAILS (CREATED_BY, LAST_MODIFIED_BY , ORDER_ID, SERVICE_ID, SUB_ORDER_STATE, STATE_REASON, SUB_ORDER_ID, ENTITY_ID, USERNAME) values (?, ?, ?, ?, ?, ?, ?, ?, ?)";
		session.doWork(connection -> {
			try (PreparedStatement ps = connection.prepareStatement(sql)) {
				ps.setString(1, subOrderEntity.getCreatedBy());
				ps.setString(2, subOrderEntity.getLastModifiedBy());
				ps.setLong(3, subOrderEntity.getOrderId());
				ps.setString(4, subOrderEntity.getServiceId());
				ps.setString(5, subOrderEntity.getState());
				ps.setString(6, subOrderEntity.getStateReason());
				ps.setLong(7, subOrderEntity.getId());
				ps.setString(8, subOrderEntity.getEntityId());
				ps.setString(9, subOrderEntity.getUsername());
				ps.executeUpdate();
			} catch (SQLException e) {
				log.error("An exception occurred in NativeSubOrderRepository.save", e);
			}
		});
	}


	public void saveAll(List<SubOrderEntity> subOrders) {
		var session = em.unwrap(Session.class);
		final String sql = "insert into COM_SUB_ORDER_DETAILS (CREATED_BY, LAST_MODIFIED_BY , ORDER_ID, SERVICE_ID, SUB_ORDER_STATE, STATE_REASON, SUB_ORDER_ID, ENTITY_ID, USERNAME) values (?, ?, ?, ?, ?, ?, ?, ?, ?)";
		session.doWork(connection -> {
			try (PreparedStatement ps = connection.prepareStatement(sql)) {
				int i = 1;
				for (SubOrderEntity subOrder : subOrders) {
					ps.setString(1, subOrder.getCreatedBy());
					ps.setString(2, subOrder.getLastModifiedBy());
					ps.setLong(3, subOrder.getOrderId());
					ps.setString(4, subOrder.getServiceId());
					ps.setString(5, subOrder.getState());
					ps.setString(6, subOrder.getStateReason());
					ps.setLong(7, subOrder.getId());
					ps.setString(8, subOrder.getEntityId());
					ps.setString(9, subOrder.getUsername());
					ps.addBatch();
					if (i % applicationProperties.getJdbcBatchSize() == 0)
						ps.executeBatch();
					i++;
				}
				ps.executeBatch();
			} catch (SQLException e) {
				log.error("An exception occurred in NativeSubOrderRepository.saveAll", e);
			}
		});
	}
	
	public void saveAllSubOrder(List<SubOrderEntity> subOrders) {
		var session = em.unwrap(Session.class);
		final String sql = "insert into COM_SUB_ORDER_DETAILS (CREATED_BY, LAST_MODIFIED_BY , ORDER_ID, SERVICE_ID, SUB_ORDER_STATE, STATE_REASON, SUB_ORDER_ID, CHARGING_PATTERN, ENTITY_ID, USERNAME) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
		session.doWork(connection -> {
			try (PreparedStatement ps = connection.prepareStatement(sql)) {
				int i = 1;
				for (SubOrderEntity subOrder : subOrders) {
					ps.setString(1, subOrder.getCreatedBy());
					ps.setString(2, subOrder.getLastModifiedBy());
					ps.setLong(3, subOrder.getOrderId());
					ps.setString(4, subOrder.getServiceId());
					ps.setString(5, subOrder.getState());
					ps.setString(6, subOrder.getStateReason());
					ps.setLong(7, subOrder.getId());
					ps.setInt(8, subOrder.getChargingPattern());
					ps.setString(9, subOrder.getEntityId());
					ps.setString(10, subOrder.getUsername());
					ps.addBatch();
					if (i % applicationProperties.getJdbcBatchSize() == 0)
						ps.executeBatch();
					i++;
				}
				ps.executeBatch();
			} catch (SQLException e) {
				log.error("An exception occurred in NativeSubOrderRepository.saveAll", e);
			}
		});
	}

}
