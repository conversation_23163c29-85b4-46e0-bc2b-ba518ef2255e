package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.ApplicationQueueEntity;

@Repository
public interface ApplicationQueueRepository extends JpaRepository<ApplicationQueueEntity, Long> {

	@Query("SELECT a FROM ApplicationQueueEntity a WHERE a.instanceId = :instanceId")
	public List<ApplicationQueueEntity> findBasedOnInstanceId(@Param("instanceId") String instanceId);

}
