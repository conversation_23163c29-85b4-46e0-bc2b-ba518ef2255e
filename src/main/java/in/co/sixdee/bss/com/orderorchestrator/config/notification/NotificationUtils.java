package in.co.sixdee.bss.com.orderorchestrator.config.notification;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.AppConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.connector.RestConnector;
import in.co.sixdee.bss.common.constants.ApiConstants;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.common.jolt.JoltUtils;
import in.co.sixdee.bss.common.util.CommonUtils;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.notification.NotificationDTO;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Log4j2
@Component
public class NotificationUtils {

    @Autowired
    protected GetDataFromCache cache;
    @Autowired
    protected JoltUtils joltUtils;
    @Autowired
    private RestConnector restConnector;
    @Autowired
    private ObjectMapper objectMapper;

    String orderType = "";
    String entityId = "";
    String networkType = "";
    String connectionType = "";
    String profileType = "";
    ArrayList<CacheTableDataDTO> smsTemplates = new ArrayList<>() ;
	ArrayList<CacheTableDataDTO> emailTemplates = new ArrayList<>();
    public void sendNotification(OrderFlowContext orderFlowContext, String messageType, String notificationLevel) {
    	

        orderType = orderFlowContext.getOrder().getOrderType();
        entityId = orderFlowContext.getEntityId();
        networkType = getNetworkType(orderFlowContext, orderType);
        connectionType = getConnectionType(orderFlowContext, orderType);
        profileType = getProfileType(orderFlowContext, orderType);
        if (orderType.equalsIgnoreCase(OrderTypes.MANAGE_HLR_SERVICES)) {
            var hlrServices = orderFlowContext.getOrder().getServiceManagement().getHlrServices().stream()
                    .filter(hlr -> hlr.getName().equalsIgnoreCase("mca") && hlr.getStatus().equalsIgnoreCase("true")).findAny()
                    .orElse(null);
            if (ObjectUtils.isNotEmpty(hlrServices)) {
                messageType = messageType.concat("_ACTIVATE");
            } else {
                messageType = messageType.concat("_DEACTIVATE");
            }
        } else if (orderType.equalsIgnoreCase(OrderTypes.UPDATE_CREDIT_LIMIT)
                && StringUtils.equalsAnyIgnoreCase(messageType, "ORDER_COMPLETION")) {
            var suffix = getUpdateCreditLimitMsgType(orderFlowContext);
            messageType = messageType.concat(suffix);
        } else if (StringUtils.equalsAnyIgnoreCase(orderFlowContext.getChannel(), GenericConstants.DUNNING_POLLER_CHANNEL_VALUE)) {
            messageType = getMessageTypeForDunning(orderFlowContext, messageType);

        } else if (orderType.equalsIgnoreCase(OrderTypes.CREDIT_REFUND)) {
            if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getCreditRefund().getRefundType())) {
                if (orderFlowContext.getOrder().getCreditRefund().getRefundType().equalsIgnoreCase("1")) {
                    messageType = messageType.concat("_BANK");
                } else if (orderFlowContext.getOrder().getCreditRefund().getRefundType().equalsIgnoreCase("2")) {
                    messageType = messageType.concat("_AIRTIME");
                }
            }
        }
        log.info("orderType {} :: entityId {} :: networkType {} :: connectionType {} :: profileType{} :: messageType {} :: notificationLevel {} :: ",orderType,entityId,networkType,connectionType,profileType,messageType,notificationLevel);
        getEntityIdBasedTemplates(messageType, notificationLevel);
        getNetworkTypeBasedTemplates(messageType, notificationLevel);
        getConnectionTypeBasedTemplates(messageType, notificationLevel);
        getDefaultSmsAndEmailTemplate(messageType, notificationLevel);
        
        extractNotificationTemplates(emailTemplates, profileType, messageType, orderFlowContext);
        extractNotificationTemplates(smsTemplates, profileType, messageType, orderFlowContext);
    }

    private void getConnectionTypeBasedTemplates(String messageType, String notificationLevel) {
        if (StringUtils.isNotEmpty(connectionType) && ObjectUtils.isEmpty(emailTemplates) && ObjectUtils.isEmpty(smsTemplates)) {
            log.info("Notification template based on connectionType: {}" + connectionType);
            emailTemplates = cache.getCacheDetailsFromDBMapAryList(NGTableConstants.CACHE_KEY_COM_EMAIL_NOTIFICATION_DETAILS_BY_CONNECTION_TYPE,
                    orderType + "_" + GenericConstants.NOTIFICATION_EMAIL + "_" + messageType + "_" + notificationLevel + "_"
                            + profileType + "_" + connectionType);
            smsTemplates = cache.getCacheDetailsFromDBMapAryList(NGTableConstants.CACHE_KEY_COM_EMAIL_NOTIFICATION_DETAILS_BY_CONNECTION_TYPE,
                    orderType + "_" + GenericConstants.NOTIFICATION_SMS + "_" + messageType + "_" + notificationLevel + "_"
                            + profileType + "_" + connectionType);
        }
    }

    private void getNetworkTypeBasedTemplates(String messageType, String notificationLevel) {
        if (StringUtils.isNotEmpty(networkType) && ObjectUtils.isEmpty(emailTemplates) && ObjectUtils.isEmpty(smsTemplates)) {
            log.info("Notification template based on networkType: {}" + networkType);
            emailTemplates = cache.getCacheDetailsFromDBMapAryList(CacheConstants.CacheKeys.COM_EMAIL_NOTIFICATION_DETAILS_BY_CONNECTION_TYPE_NETWORK_TYPE.name(),
                    orderType + "_" + GenericConstants.NOTIFICATION_EMAIL + "_" + messageType + "_" + notificationLevel + "_"
                            + profileType + "_" + connectionType + "_" + networkType);
            smsTemplates = cache.getCacheDetailsFromDBMapAryList(CacheConstants.CacheKeys.COM_EMAIL_NOTIFICATION_DETAILS_BY_CONNECTION_TYPE_NETWORK_TYPE.name(),
                    orderType + "_" + GenericConstants.NOTIFICATION_SMS + "_" + messageType + "_" + notificationLevel + "_"
                            + profileType + "_" + connectionType + "_" + networkType);
        }
    }

    public void getEntityIdBasedTemplates(String messageType, String notificationLevel) {
        if (StringUtils.isNotEmpty(entityId)) {
            log.info("Notification template based on entityId: {}", entityId);
            emailTemplates = cache.getCacheDetailsFromDBMapAryList(CacheConstants.CacheKeys.COM_EMAIL_NOTIFICATION_DETAILS_BY_ENTITY_ID.name(),
                    orderType + "_" + GenericConstants.NOTIFICATION_EMAIL + "_" + messageType + "_" + notificationLevel + "_"+ entityId);
            smsTemplates = cache.getCacheDetailsFromDBMapAryList(CacheConstants.CacheKeys.COM_EMAIL_NOTIFICATION_DETAILS_BY_ENTITY_ID.name(),
                    orderType + "_" + GenericConstants.NOTIFICATION_SMS + "_" + messageType + "_" + notificationLevel + "_" + entityId);
        }
    }

    private void getDefaultSmsAndEmailTemplate(String messageType, String notificationLevel) {
        if (ObjectUtils.isEmpty(emailTemplates)) {
            log.info("Taking Default Notification emailTemplates");
            emailTemplates = cache.getCacheDetailsFromDBMapAryList(NGTableConstants.CACHE_KEY_COM_EMAIL_NOTIFICATION_DETAILS,
                    orderType + "_" + GenericConstants.NOTIFICATION_EMAIL + "_" + messageType + "_" + notificationLevel + "_"
                            + profileType);

        }
        if (ObjectUtils.isEmpty(smsTemplates)) {
        	log.info("Taking Default Notification smsTemplates");
            smsTemplates = cache.getCacheDetailsFromDBMapAryList(NGTableConstants.CACHE_KEY_COM_EMAIL_NOTIFICATION_DETAILS,
                    orderType + "_" + GenericConstants.NOTIFICATION_SMS + "_" + messageType + "_" + notificationLevel + "_"
                            + profileType);

        }

    }

    private String getMessageTypeForDunning(OrderFlowContext orderFlowContext, String messageType) {

        var serviceManagement = orderFlowContext.getOrder().getServiceManagement();
        if (ObjectUtils.isNotEmpty(serviceManagement) && StringUtils.isNotEmpty(serviceManagement.getDunningAction())) {
            String dunningAction = serviceManagement.getDunningAction();
            messageType = messageType.concat("_").concat(dunningAction).concat("_DUNNING");
        } else {
            messageType = messageType.concat("_DUNNING");

        }
        log.info("message type based on dunning is {}", messageType);
        return messageType;
    }

    private String getNetworkType(OrderFlowContext orderFlowContext, String orderType) {
        var networkType = "";
        switch (orderType) {
            case OrderTypes.ONBOARDING:
            case OrderTypes.ADD_SERVICE:
            case OrderTypes.ADD_SERVICE_TO_NEW_ACCOUNT:
                if (MapUtils.isNotEmpty(orderFlowContext.getWorkflowData())
                        && ObjectUtils.isNotEmpty(orderFlowContext.getWorkflowData().get("currentExecution"))) {
                    Map<String, Object> currentExecution = (Map<String, Object>) orderFlowContext.getWorkflowData()
                            .get("currentExecution");

                    Object executionData = currentExecution.get("executionData");
                    Map<String, Object> execution = com.bazaarvoice.jolt.JsonUtils
                            .jsonToMap(com.bazaarvoice.jolt.JsonUtils.toJsonString(executionData));
                    if (MapUtils.isNotEmpty(execution) && execution.containsKey("networkServiceId"))
                        networkType = execution.get("networkServiceId").toString();
                }
                break;
            case OrderTypes.MNP_PORT_IN:
                networkType = orderFlowContext.getOrder().getProfile().getAccount().getServiceGroups().get(0).getServices().get(0).getNetworkServiceId();
                break;
            default:
                networkType = getNetworkTypeFromEnrichment(orderFlowContext);
                break;
        }
        return networkType;
    }

    private String getNetworkTypeFromEnrichment(OrderFlowContext orderFlowContext) {
        var networkType = "";
        if (ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults())
                && ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults().get("serviceInfo"))) {
            var serviceInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("serviceInfo"),
                    JsonNode.class);
            if (ObjectUtils.isNotEmpty(serviceInfo) && serviceInfo.has("networkServiceId")) {
                networkType = serviceInfo.get("networkServiceId").asText();
            }
        }
        return networkType;
    }

    private void extractNotificationTemplates(ArrayList<CacheTableDataDTO> templates, String profileType, String messageType,
                                              OrderFlowContext orderFlowContext) {

        if (ObjectUtils.isNotEmpty(templates)) {
            for (CacheTableDataDTO template : templates) {
                if (BooleanUtils.toBoolean(template.getNgTableData().get("SPOC_NOTIFICATION_ENABLE"))) {
                    if (StringUtils.isNotEmpty(profileType) && "1".equalsIgnoreCase(profileType)
                        /* && "ORDER_COMPLETION".equalsIgnoreCase(messageType) */) {
                        var notificationReq = billingFetchContactDetails(orderFlowContext);
                        if (notificationReq) {
                            boolean spocNotifitnReq = true;
                            if (StringUtils.equalsIgnoreCase(template.getNgTableData().get("DELIVERY_TYPE"), "EMAIL")) {
                                if (!checkSpocEmailNotificationReq(orderFlowContext)) {
                                    spocNotifitnReq = false;
                                    log.info("SA Form Attachment details are not available to initiate the notifications for order type "
                                            + orderFlowContext.getOrder().getOrderType());
                                }

                            }
                            if (spocNotifitnReq)
                                initNotificationDTO(template, orderFlowContext, true, null);
                        } else {
                            log.info("Spoc details are not available to initiate the notifications for order type "
                                    + orderFlowContext.getOrder().getOrderType());
                        }
                    }
                } else if ("ACCOUNT".equalsIgnoreCase(template.getNgTableData().get("CUSTOMER_LEVEL"))) {
                    String smsNumber = getAccountLevelSmsNumber(orderFlowContext);
                    if (StringUtils.isNotEmpty(smsNumber)) {
                        initNotificationDTO(template, orderFlowContext, false, smsNumber);
                    }
                } else if ("PROFILE".equalsIgnoreCase(template.getNgTableData().get("CUSTOMER_LEVEL"))) {
                    if (Arrays.asList(GenericConstants.PROFILE_LEVEL_ORDER_ENRICHMENT_TYPES.split(",")).contains(orderFlowContext.getEnrichmentType()))
                        if (checkPhoneNumberPresent(orderFlowContext)) {
                            billingGetBasicDetails(orderFlowContext);
                            initNotificationDTO(template, orderFlowContext, false, null);
                        } else
                            log.info("PhoneNumber is not available to initiate the notifications for order type "
                                    + orderFlowContext.getOrder().getOrderType());
                } else {
                    initNotificationDTO(template, orderFlowContext, false, null);

                }
            }
        } else
            log.info("customer has not opted for notifications or templates are not configured for order type "
                    + orderFlowContext.getOrder().getOrderType());
    }

    private boolean checkSpocEmailNotificationReq(OrderFlowContext orderFlowContext) {
        if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getSaFormFileName())
                && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getSaFormFilePath()))
            return true;
        return false;
    }

    private boolean checkPhoneNumberPresent(OrderFlowContext orderFlowContext) {

        boolean isPhoneNumberPresent = false;

        if (StringUtils.isNotEmpty(orderFlowContext.getAttributes().get("phoneNumber")))
            isPhoneNumberPresent = true;
        else {
            if (ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults())
                    && ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults().get("profileInfo"))) {
                var profileInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("profileInfo"),
                        JsonNode.class);
                if (ObjectUtils.isNotEmpty(profileInfo) && profileInfo.has("phoneNumber")) {
                    orderFlowContext.getAttributes().put("phoneNumber", profileInfo.get("phoneNumber").asText());
                    isPhoneNumberPresent = true;
                }
            }
        }
        return isPhoneNumberPresent;
    }

    private String getAccountLevelSmsNumber(OrderFlowContext orderFlowContext) {
        String smsNumber = null;

        if (StringUtils.equalsAnyIgnoreCase(orderFlowContext.getOrder().getOrderType(), OrderTypes.ONBOARDING,
                OrderTypes.ADD_SERVICE_TO_NEW_ACCOUNT)
                || StringUtils.equalsAnyIgnoreCase(orderFlowContext.getOrder().getOrderType(),
                OrderTypes.CONNECTION_MIGRATION)
                && !ObjectUtils
                .isNotEmpty(orderFlowContext.getOrder().getProfile().getAccount().getAccountId())) {
            if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getAccount().getSmsNumbers()))
                smsNumber = orderFlowContext.getOrder().getProfile().getAccount().getSmsNumbers();

        } else if (ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults())
                && ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults().get("accountInfo"))) {
            var accountInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("accountInfo"),
                    JsonNode.class);
            if (ObjectUtils.isNotEmpty(accountInfo) && accountInfo.has("smsNumbers")) {
                smsNumber = accountInfo.get("smsNumbers").asText();
            }
        }

        return smsNumber;
    }

    protected String getProfileType(OrderFlowContext orderFlowContext, String orderType) {
        var profileType = "";
        switch (orderType) {
            case OrderTypes.ONBOARDING:

                var partyChar = orderFlowContext.getOrder().getProfile().getPartyCharacteristic();
                profileType = getProfileTypeFromRequest(partyChar);
                break;

            case OrderTypes.UPDATE_STARTER_PACK_KYC:

                if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder())
                        && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getCustomerInfo())
                        && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getCustomerInfo().getPartyCharacteristic())) {
                    var partyCharacter = orderFlowContext.getOrder().getCustomerInfo().getPartyCharacteristic();
                    profileType = getProfileTypeFromRequest(partyCharacter);
                } else
                    profileType = getProfileTypeFromEnrichment(orderFlowContext);
                break;

            case OrderTypes.TRANSFER_OF_SERVICE:
                if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder())
                        && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement())
                        && ObjectUtils.isNotEmpty(
                        orderFlowContext.getOrder().getServiceManagement().getDestinationProfileId())) {
                    profileType = getProfileTypeFromEnrichment(orderFlowContext);

                } else if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder())
                        && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile())
                        && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getPartyCharacteristic())) {
                    var partyChars = orderFlowContext.getOrder().getProfile().getPartyCharacteristic();
                    profileType = getProfileTypeFromRequest(partyChars);
                }
                break;

            default:

                profileType = getProfileTypeFromEnrichment(orderFlowContext);
                break;

        }
        return profileType;
    }

    protected String getConnectionType(OrderFlowContext orderFlowContext, String orderType) {
        String connectionType = "";
        switch (orderType) {
            case OrderTypes.ONBOARDING:
            case OrderTypes.ADD_SERVICE:
            case OrderTypes.ADD_SERVICE_TO_NEW_ACCOUNT:
                if (MapUtils.isNotEmpty(orderFlowContext.getWorkflowData())
                        && ObjectUtils.isNotEmpty(orderFlowContext.getWorkflowData().get("currentExecution"))) {
                    Map<String, Object> currentExecution = (Map<String, Object>) orderFlowContext.getWorkflowData()
                            .get("currentExecution");

                    Object executionData = currentExecution.get("executionData");
                    Map<String, Object> execution = com.bazaarvoice.jolt.JsonUtils
                            .jsonToMap(com.bazaarvoice.jolt.JsonUtils.toJsonString(executionData));
                    if (MapUtils.isNotEmpty(execution) && execution.containsKey("chargingPattern"))
                        connectionType = execution.get("chargingPattern").toString();
                }
                break;

            case OrderTypes.CONNECTION_MIGRATION:
                connectionType = orderFlowContext.getOrder().getServiceManagement().getDestinationConnectionType();
                break;

            case OrderTypes.MNP_PORT_IN:
                connectionType = orderFlowContext.getOrder().getProfile().getAccount().getServiceGroups().get(0).getServices().get(0).getChargingPattern();
                break;

            default:
                connectionType = getConnectionTypeFromEnrichment(orderFlowContext);
                break;
        }
        return connectionType;
    }

    protected String getProfileTypeFromRequest(List<Characteristic> partyChar) {
        var profileType = "";
        for (Characteristic characteristic : partyChar) {
            if (ObjectUtils.isNotEmpty(characteristic.getName()) && ObjectUtils.isNotEmpty(characteristic.getValue())
                    && "profileType".equalsIgnoreCase(characteristic.getName())) {
                profileType = characteristic.getValue();
                break;
            }
        }
        return profileType;

    }

    protected String getProfileTypeFromEnrichment(OrderFlowContext orderFlowContext) {
        var profileType = "";
        if (ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults())
                && ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults().get("profileInfo"))) {
            var profileInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("profileInfo"),
                    JsonNode.class);
            if (ObjectUtils.isNotEmpty(profileInfo) && profileInfo.has("profileType")) {
                profileType = profileInfo.get("profileType").asText();
            }
        }
        return profileType;

    }

    private String getUpdateCreditLimitMsgType(OrderFlowContext orderFlowContext) {
        var messageType = "";
        var creditLimit = 0.00;
        BigDecimal limitDiff = new BigDecimal(0);
        BigDecimal creditLimitChannel = new BigDecimal(0);

        if (ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults())
                && ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults().get("serviceInfo"))) {
            var serviceInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("serviceInfo"),
                    JsonNode.class);
            if (ObjectUtils.isNotEmpty(serviceInfo) && serviceInfo.has("creditLimit")) {
                var creditLimitSerInfo = new BigDecimal(serviceInfo.get("creditLimit").asText());
                if (ObjectUtils.isNotEmpty(
                        orderFlowContext.getOrder().getServiceManagement().getCreditLimitInfo().getCreditLimit())) {
                    creditLimit = Double.parseDouble(
                            orderFlowContext.getOrder().getServiceManagement().getCreditLimitInfo().getCreditLimit());
                    creditLimitChannel = BigDecimal.valueOf(creditLimit).setScale(2, RoundingMode.HALF_EVEN);

                }
                if (creditLimitChannel.compareTo(creditLimitSerInfo) >= 0) {
                    messageType = "_INCR";
                    limitDiff = creditLimitChannel.subtract(creditLimitSerInfo);
                } else if (creditLimitChannel.compareTo(creditLimitSerInfo) < 0) {
                    limitDiff = creditLimitSerInfo.subtract(creditLimitChannel);
                    messageType = "_DECR";
                }
                orderFlowContext.getAttributes().put("creditLimitDifference", limitDiff.toString());
            }
        }

        return messageType;
    }

    protected String getConnectionTypeFromEnrichment(OrderFlowContext orderFlowContext) {
        var chargingPattern = "";
        if (ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults())
                && ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults().get("serviceInfo"))) {
            var serviceInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("serviceInfo"),
                    JsonNode.class);
            if (ObjectUtils.isNotEmpty(serviceInfo) && serviceInfo.has("chargingPattern")) {
                chargingPattern = serviceInfo.get("chargingPattern").asText();
            }
        }
        return chargingPattern;

    }

    protected boolean billingFetchContactDetails(OrderFlowContext orderFlowContext) {

        String responseAttributesJson = null;
        var notificationReq = false;
        try {
            if (orderFlowContext.getWorkflowData().containsKey("BSFetchContactResponseAttributes")) {
                notificationReq = true;
            } else {
                var orderType = orderFlowContext.getOrder().getOrderType();
                var callThirdPartyDTO = callThirdParty(orderFlowContext, "bs-fetch-contact");
                if (callThirdPartyDTO == null) {
                    return notificationReq;
                }
                var response = callThirdPartyDTO.getResponse();
                if (StringUtils.isNotEmpty(response) && in.co.sixdee.bss.common.util.JsonUtils.isJson(response)) {
                    responseAttributesJson = joltUtils.convert("BSFetchContact", orderType,
                            JsonUtils.jsonToObject(response), null);
                    if (CommonUtils.INSTANCE.validateField(responseAttributesJson)) {
                        notificationReq = true;
                        orderFlowContext.getWorkflowData().put("BSFetchContact" + "ResponseAttributes",
                                JsonUtils.jsonToObject(responseAttributesJson));
                    }
                }
            }
        } catch (Exception e) {
            log.error("Exception Occured in  :: {}", e.getMessage());

        }
        return notificationReq;

    }

    private void billingGetBasicDetails(OrderFlowContext orderFlowContext) {

        var callThirdPartyDTO = callThirdParty(orderFlowContext, "basic-details-by-id");
        var orderType = orderFlowContext.getOrder().getOrderType();
        String responseAttributesJson = null;
        var response = callThirdPartyDTO.getResponse();
        if (callThirdPartyDTO != null) {
            if (StringUtils.isNotEmpty(response) && in.co.sixdee.bss.common.util.JsonUtils.isJson(response)) {
                try {
                    responseAttributesJson = joltUtils.convert("BSGetBasicDetails", orderType,
                            JsonUtils.jsonToObject(response), null);
                } catch (Exception e) {
                    // TODO Auto-generated catch block
                    log.error("Exception Occured in  :: {}", e.getMessage());
                }
                if (CommonUtils.INSTANCE.validateField(responseAttributesJson)) {
                    orderFlowContext.getWorkflowData().put("BSGetBasicDetails" + "ResponseAttributes",
                            JsonUtils.jsonToObject(responseAttributesJson));
                }
            }
        }
    }

    protected in.co.sixdee.bss.common.dto.CallThirdPartyDTO callThirdParty(OrderFlowContext orderFlowContext, String thirdPartyId) {
        //var thirdPartyId = "bs-fetch-contact";
        var callThirdPartyDTO = initThirdPartyCallDetails(thirdPartyId, orderFlowContext.getOrder().getOrderType());
        // callThirdPartyDTO.setRequest(request);
        var headerMap = getHeaderMap(callThirdPartyDTO.getHeaders());
        if (headerMap != null)
            callThirdPartyDTO.setHeaderMap(transformHeaders(headerMap, orderFlowContext));
        var uri = callThirdPartyDTO.getUrl();
        var urlTokenConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_TOKEN_CONFIG.name(),
                thirdPartyId + "_" + orderFlowContext.getOrder().getOrderType());
        if (urlTokenConfig != null) {
            var urlTokenConfigs = urlTokenConfig.getNgTableData();
            uri = formatUri(uri, resolveUriTokens(urlTokenConfigs.get(AppConstants.CacheFields.TOKENS.name()), orderFlowContext));
        }
        callThirdPartyDTO.setUrl(uri);
        try {
            callThirdPartyDTO.setUri(new URI(uri.trim()));
        } catch (URISyntaxException e) {
            throw new CommonException(null, e.getMessage());
        }
        restConnector.service(callThirdPartyDTO);
        return callThirdPartyDTO;
    }

    protected String formatUri(String uri, Param params) {
        return new StringSubstitutor(params).replace(uri);
    }

    protected Param resolveUriTokens(String tokens, OrderFlowContext orderFlowContext) {
        if (tokens == null)
            return new Param();
        DocumentContext context = null;
        try {
            context = tokens.contains("$") ? JsonPath.parse(objectMapper.writeValueAsString(orderFlowContext)) : null;
        } catch (Exception e) {
            log.info("Exception occurred in resolveUriTokens: {}", e.getMessage());
            return new Param();
        }
        var tokenArray = tokens.split("\\|");
        var uriParams = new Param();
        for (var s : tokenArray) {
            var param = s.split("=", 2);
            if (param.length > 1) {
                var token = param[0];
                var value = param[1];
                if (value.startsWith("$")) {
                    resolveJsonPath(context, token, value, uriParams);
                } else if (value.startsWith("val")) {
                    resolveValExpression(token, value, uriParams, orderFlowContext);
                } else {
                    uriParams.put(token, value);
                }
            }
        }
        return uriParams;
    }

    private void resolveJsonPath(DocumentContext context, String key, String path, Param uriParams) {
        if (context == null || path == null || uriParams == null)
            return;
        var value = context.read(path);
        if (value instanceof String || value instanceof Integer) {
            uriParams.put(key, String.valueOf(value));
        } else if (value instanceof List<?>) {
            uriParams.put(key, String.valueOf(value).replace("[", "").replace("]", "").replaceAll("\\s", ""));
        } else {
            log.info("ignoring token {}, as it is not a primitive", key);
        }
    }

    protected void resolveValExpression(String key, String path, Param uriParams, OrderFlowContext orderFlowContext) {
        if (path == null || uriParams == null)
            return;
        var valPath = path.substring(path.indexOf("(") + 1, path.indexOf(")"));
        if (orderFlowContext.getAttributes().containsKey(valPath)) {
            uriParams.put(key, orderFlowContext.getAttributes().get(valPath));
        } else if (key.equalsIgnoreCase("requestTimeStamp")) {
            uriParams.put(key, LocalDateTime.now().format(DateTimeFormatter.ofPattern(ApiConstants.API_DATE_FORMAT)));
        }
    }

    public Map<String, String> getHeaderMap(String headerParams) {
        Map<String, String> headerMap = null;
        try {
            if (StringUtils.isNotEmpty(headerParams)) {
                headerMap = new HashMap<String, String>();
                String[] headerParam = headerParams.split(",");
                for (String header : headerParam) {
                    if (header.contains("=")) {
                        String name = null, value = null;
                        try {
                            name = header.split("=")[0];
                            value = header.split("=")[1];
                            headerMap.put(name, value);
                        } catch (Exception e) {
                            throw e;
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw e;
        }
        return headerMap;
    }

    protected Map<String, String> transformHeaders(Map<String, String> headerMap, OrderFlowContext orderFlowContext) {
        Set<Map.Entry<String, String>> entrySet = null;
        Map<String, String> newMap = new HashMap<>();
        String path = null;
        String value = null;
        try {
            var docContext = JsonPath.parse(objectMapper.writeValueAsString(orderFlowContext));
            entrySet = headerMap.entrySet();
            for (Map.Entry<String, String> entry : entrySet) {
                if (entry.getValue().startsWith("@(") && entry.getValue().endsWith(")")) {
                    path = entry.getValue().substring(2, entry.getValue().length() - 1);
                    try {
                        value = docContext.read(path);
                    } catch (PathNotFoundException e) {
                        log.warn("Path {} not found!!!", path);
                    }
                    if (ObjectUtils.isNotEmpty(value))
                        newMap.put(entry.getKey(), value);
                } else if (entry.getValue().startsWith("{") && entry.getValue().endsWith("}")) {
                    newMap.put(entry.getKey(),
                            orderFlowContext.getAttributes().get(entry.getValue().substring(1, entry.getValue().length() - 1)));
                } else if (entry.getValue().startsWith("date(") && entry.getValue().endsWith(")")) {
                    String timestamp = null;
                    String format = entry.getValue().substring(5, entry.getValue().length() - 1);
                    if (StringUtils.isNotEmpty(format)) {
                        if (StringUtils.equalsIgnoreCase(format, "epoch")) {
                            timestamp = String.valueOf(System.currentTimeMillis());
                        } else {
                            SimpleDateFormat sdf = null;
                            sdf = new SimpleDateFormat(format);
                            timestamp = sdf.format(System.currentTimeMillis());
                        }
                        newMap.put(entry.getKey(), timestamp);
                    }
                } else
                    newMap.put(entry.getKey(), entry.getValue());

            }

        } catch (Exception e) {
            log.error("Exception occurred in transformHeaders", e);
        }
        return newMap;
    }

    @SuppressWarnings("unchecked")
    protected void initNotificationDTO(CacheTableDataDTO templates, OrderFlowContext orderFlowContext, boolean spocEnabledFlag, String smsNumber) {

        var notificationDTO = new NotificationDTO();
        notificationDTO.setTraceId(orderFlowContext.getTraceId());
        notificationDTO.setRequestId(orderFlowContext.getRequestId());
        notificationDTO.setChannel(orderFlowContext.getChannel());
        notificationDTO.setUsername(orderFlowContext.getUsername());
        notificationDTO.setOrder(orderFlowContext.getOrder());
        notificationDTO.setEntityId(orderFlowContext.getEntityId());
        notificationDTO.setAttributes((LinkedHashMap<String, String>) orderFlowContext.getAttributes().clone());
        if (spocEnabledFlag)
            notificationDTO.setSpocEnabled(spocEnabledFlag);
        if (StringUtils.isNotEmpty(smsNumber))
            notificationDTO.setSmsNumber(smsNumber);
        notificationDTO.setEnrichmentResults(SerializationUtils.clone(orderFlowContext.getEnrichmentResults()));
        notificationDTO.setWorkflowData(SerializationUtils.clone(orderFlowContext.getWorkflowData()));
        notificationDTO.getWorkflowData().put("keyword", templates.getNgTableData().get("DELIVERY_TYPE"));
        notificationDTO.getWorkflowData().put("template_id", templates.getNgTableData().get("TEMPLATE_ID"));
        notificationDTO.getAttributes().put("messageTokens", templates.getNgTableData().get("TOKENS"));
        notificationDTO.getWorkflowData().put("messageTemplate", templates.getNgTableData().get("MESSAGE_TEMPLATE"));
        if (!templates.getNgTableData().get("NOTIFICATION_REQ_SPEC_KEY").equalsIgnoreCase("null")) {
            notificationDTO.getWorkflowData().put("notificationReqSpecKey",
                    templates.getNgTableData().get("NOTIFICATION_REQ_SPEC_KEY"));
        }

        ThreadInitiator.notificationQueue.enqueue(notificationDTO);
    }

    protected CallThirdPartyDTO initThirdPartyCallDetails(String thirdPartyId, String orderType) throws CommonException {

        var thirdPartyUrlConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(),
                thirdPartyId);
        if (Objects.isNull(thirdPartyUrlConfig))
            throw new ConfigurationNotValidException("url configuration not found for the third party id :: " + thirdPartyId);
        var callThirdPartyDTO = new in.co.sixdee.bss.common.dto.CallThirdPartyDTO();
        callThirdPartyDTO.setRespPayloadValidationRqd(BooleanUtils
                .toBoolean(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.PAYLOAD_VALIDATION.name())));
        callThirdPartyDTO
                .setThirdPartyId(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.THIRD_PARTY_ID.name()));
        callThirdPartyDTO.setUrl(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.URL.name()));
        callThirdPartyDTO.setReadTimeout(
                NumberUtils.toInt(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.READ_TIMEOUT.name()), 3000));
        callThirdPartyDTO.setConnTimeout(NumberUtils
                .toInt(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.CONNECTION_TIMEOUT.name()), 3000));
        callThirdPartyDTO.setExpectedHttpCode(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.EXPECTED_HTTP_CODE.name()));
        callThirdPartyDTO.setExpectedRespCode(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.EXPECTED_RESP_CODE.name()));
        callThirdPartyDTO.setHeaders(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.HEADERS.name()));
        callThirdPartyDTO.setTransportMethod(in.co.sixdee.bss.common.dto.CallThirdPartyDTO.TransportMethod
                .valueOf(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.TRANSPORT_METHOD.name())));
        callThirdPartyDTO.setResponseCodeJsonPath(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.RESP_STATUS_PATH.name()));
        callThirdPartyDTO.setResponseMessageJsonPath(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.RESP_STATUS_DESC_PATH.name()));
        callThirdPartyDTO.setThirdPartySystem(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.THIRD_PARTY_SYSTEM.name()));
        return callThirdPartyDTO;

    }

    static class Param extends HashMap<String, String> {
        /**
         *
         */
        private static final long serialVersionUID = 1L;

        @Override
        public String get(Object key) {
            if (!super.containsKey(key)) {
                super.put(key.toString(), "");
            }
            return super.getOrDefault(key, "t");
        }

        @Override
        public boolean containsKey(Object arg0) {
            return true;
        }
    }

}