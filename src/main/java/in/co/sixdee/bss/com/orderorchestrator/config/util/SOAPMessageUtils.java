package in.co.sixdee.bss.com.orderorchestrator.config.util;


import jakarta.xml.soap.MessageFactory;
import jakarta.xml.soap.SOAPException;
import jakarta.xml.soap.SOAPMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.oxm.XmlMappingException;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Component;

import javax.xml.transform.dom.DOMSource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

@Component
@Log4j2
@RequiredArgsConstructor
public class SOAPMessageUtils {


    final Jaxb2Marshaller jaxb2Marshaller;

    @SuppressWarnings("unchecked")
    public <T> T unmarshall(SOAPMessage soapMessage, Class<T> type)
            throws XmlMappingException, IOException, SOAPException {
        return (T) jaxb2Marshaller.unmarshal(
                new DOMSource(soapMessage.getSOAPBody().extractContentAsDocument()));
    }


    public  SOAPMessage getSOAPMessageFromXMLString(String xmlString,
                                                          String namespacePrefix, String namespaceURI) {
        SOAPMessage soapMessage = null;
        try {
            InputStream is = new ByteArrayInputStream(xmlString.getBytes(StandardCharsets.UTF_8));
            soapMessage = MessageFactory.newInstance().createMessage(null, is);
            //SOAPEnvelope soapEnvelope = soapMessage.getSOAPPart().getEnvelope();
            //soapEnvelope.addNamespaceDeclaration(namespacePrefix, namespaceURI);
            soapMessage.saveChanges();
        } catch (Exception e) {
            log.error("Exception occured while converting xml string to SOAP Message", e);
        }
        return soapMessage;
    }
}
