package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.net.URI;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.google.gson.Gson;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;

import in.co.sixdee.bss.com.orderorchestrator.model.CallBackHeader;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.text.StringSubstitutor;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.cglib.transform.impl.AccessFieldTransformer;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.EntityValidationException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.AppConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderPayloadService;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.connector.RestConnector;
import in.co.sixdee.bss.common.constants.ApiConstants;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.common.jolt.JoltUtils;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class ARMFetchAssetDetails extends AbstractDelegate {

	@Autowired
	protected GetDataFromCache cache;
	@Autowired
	protected JoltUtils joltUtils;
	@Autowired
	private RestConnector restConnector;
	@Autowired
	private ObjectMapper objectMapper;


	protected Order orderPayload = null;

	private void validateResponseNew(String response, OrderCallBack callback) throws JsonMappingException, JsonProcessingException {
		// TODO Auto-generated method stub
		String message = null;
		var billResp = objectMapper.readTree(response);
		if (response.contains("message")) {
			if (billResp.get("message") != null) {
				message = billResp.get("message").asText();
				if (!"SUCCESS".equalsIgnoreCase(message))
					throw new EntityValidationException(callThirdPartyDTO.getStatus() + " " + callThirdPartyDTO.getResponse());
			}

		}
		fetchImsiForAsset(response, callback);
		if (response.contains("serialnumber")) {

			String iccid = null;
			if (response.contains("content"))
				iccid = billResp.get("data").get("content").get(0).get("serialnumber").asText();
			else
				iccid = billResp.get("data").get(0).get("serialnumber").asText();

			if (StringUtils.isNotEmpty(iccid) && StringUtils.isNotEmpty(callback.getNewICCID())
					&& !iccid.equalsIgnoreCase(callback.getNewICCID())) {
				CallThirdPartyDTO callThirdPartyDTO = new CallThirdPartyDTO();
				callThirdPartyDTO.setError(true);
				callThirdPartyDTO.setResponseCode("450");
				callThirdPartyDTO.setStatus(CallThirdPartyDTO.CallStatus.Failure);
				callThirdPartyDTO.setResponse("Invalid ICCID");
				throw new EntityValidationException(callThirdPartyDTO.getResponse());

			}

		} else
			throw new EntityValidationException(callThirdPartyDTO.getResponse());
	}

	private void fetchImsiForAsset(String response, OrderCallBack callback) throws JsonMappingException, JsonProcessingException {
		List<Map<String, Object>> assetDetails = new ArrayList<>();
		var armResponse = objectMapper.readTree(response);
		if (armResponse.has("data") && armResponse.get("data").get(0).has("assetDetails")) {
			assetDetails = objectMapper.convertValue(armResponse.get("data").get(0).get("assetDetails"),
					new TypeReference<List<Map<String, Object>>>() {
					});
			for (Map<String, Object> asset : assetDetails) {
				String productAttributeName = (String) asset.get("productAttributeName");
				if ("imsi".equalsIgnoreCase(productAttributeName)) {
					String attributeValue = (String) asset.get("attributeValue");
					callback.setNewIMSI(attributeValue);
				}
			}
		}
	}

	protected CallThirdPartyDTO initThirdPartyCallDetails(String thirdPartyId, String orderType) throws CommonException {

		var thirdPartyUrlConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(),
				thirdPartyId);
		if (Objects.isNull(thirdPartyUrlConfig))
			throw new ConfigurationNotValidException("url configuration not found for the third party id :: " + thirdPartyId);
		var callThirdPartyDTO = new in.co.sixdee.bss.common.dto.CallThirdPartyDTO();
		callThirdPartyDTO.setRespPayloadValidationRqd(BooleanUtils
				.toBoolean(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.PAYLOAD_VALIDATION.name())));
		callThirdPartyDTO
				.setThirdPartyId(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.THIRD_PARTY_ID.name()));
		callThirdPartyDTO.setUrl(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.URL.name()));
		callThirdPartyDTO.setReadTimeout(
				NumberUtils.toInt(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.READ_TIMEOUT.name()), 3000));
		callThirdPartyDTO.setConnTimeout(NumberUtils
				.toInt(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.CONNECTION_TIMEOUT.name()), 3000));
		callThirdPartyDTO.setExpectedHttpCode(
				thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.EXPECTED_HTTP_CODE.name()));
		callThirdPartyDTO.setExpectedRespCode(
				thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.EXPECTED_RESP_CODE.name()));
		callThirdPartyDTO.setHeaders(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.HEADERS.name()));
		callThirdPartyDTO.setTransportMethod(in.co.sixdee.bss.common.dto.CallThirdPartyDTO.TransportMethod
				.valueOf(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.TRANSPORT_METHOD.name())));
		callThirdPartyDTO.setResponseCodeJsonPath(
				thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.RESP_STATUS_PATH.name()));
		callThirdPartyDTO.setResponseMessageJsonPath(
				thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.RESP_STATUS_DESC_PATH.name()));
		callThirdPartyDTO.setThirdPartySystem(
				thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.THIRD_PARTY_SYSTEM.name()));
		return callThirdPartyDTO;

	}

	public void getDetailsForCallBack(OrderCallBack callback){

			log.info("call arm....");
		boolean responseValidationReqd = checkResponseValidationReqd(callback);
			var callThirdPartyDTO = callThirdParty(callback, "arm-fetch-asset");

			if (callThirdPartyDTO == null) {
				if(responseValidationReqd) {
					throw new EntityValidationException("asset details cannot be validated as ARM response is empty");
				}
			} else {
				if (!"200".equalsIgnoreCase(callThirdPartyDTO.getResponseCode())) {
					if(responseValidationReqd)
					throw new EntityValidationException("ARM Fetch failed with http code: " + callThirdPartyDTO.getResponseCode());
				}
				//(callThirdPartyDTO.getResponse(), callback);
				validateResponse(callThirdPartyDTO.getResponse(),callback,responseValidationReqd);
			}
	}

	private boolean checkResponseValidationReqd(OrderCallBack callback) {
		var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "ARM_RESPONSE_VALIDATION_NOT_REQUIRED_MVNOS");
		if (ObjectUtils.isNotEmpty(appConfig)) {
			String mvnos = appConfig.getNgTableData().get("CONFIG_VALUE");
			if (StringUtils.isNotEmpty(callback.getEntityId()) && StringUtils.isNotEmpty(mvnos) && Arrays.asList(mvnos.split(",")).contains(callback.getEntityId())) {
				log.info("ARM Response validation is not required for entity id {}", callback.getEntityId());
				return false;
			}
		}
		return true;
	}

	private void validateResponse(String response,OrderCallBack callback,boolean responseValidationReqd) {
		// TODO Auto-generated method stub
		try {
			String message = null;
			var billResp = objectMapper.readTree(response);
			if (response.contains("message")) {
				if (billResp.get("message") != null) {
					message = billResp.get("message").asText();
					if (!"SUCCESS".equalsIgnoreCase(message)) {
						if(responseValidationReqd)
						throw new EntityValidationException(callThirdPartyDTO.getStatus() + " " + callThirdPartyDTO.getResponse());
					}
				}

			}
			if(ObjectUtils.isEmpty(callback.getNewIMSI()))
			{
				log.info("IMSI is not present in the request... need to fetch from ARM");
				fetchImsiForAsset(response, callback);
			}
			if (response.contains("serialnumber")) {

				String iccid = null;
				if (response.contains("content"))
					iccid = billResp.get("data").get("content").get(0).get("serialnumber").asText();
				else
					iccid = billResp.get("data").get(0).get("serialnumber").asText();

				if (StringUtils.isNotEmpty(iccid) && StringUtils.isNotEmpty(callback.getNewICCID())
						&& !iccid.equalsIgnoreCase(callback.getNewICCID()) && responseValidationReqd) {
					CallThirdPartyDTO callThirdPartyDTO = new CallThirdPartyDTO();
					callThirdPartyDTO.setError(true);
					callThirdPartyDTO.setResponseCode("450");
					callThirdPartyDTO.setStatus(CallThirdPartyDTO.CallStatus.Failure);
					callThirdPartyDTO.setResponse("Invalid ICCID");
					throw new EntityValidationException(callThirdPartyDTO.getResponse());

				}

			} else if(responseValidationReqd)
				throw new EntityValidationException(callThirdPartyDTO.getResponse());
		} catch (Exception e) {
			if(responseValidationReqd)
			throw new EntityValidationException("iccid is invalid");
		}
	}

	private in.co.sixdee.bss.common.dto.CallThirdPartyDTO callThirdParty(OrderCallBack callback, String thirdPartyId) {
		var callThirdPartyDTO = initThirdPartyCallDetails(thirdPartyId, callback.getOrderType());
		if(callThirdPartyDTO != null && ObjectUtils.isNotEmpty(callThirdPartyDTO) && callback != null) {
		var headerMap = getHeaderMap(callThirdPartyDTO.getHeaders());
		if (headerMap != null)
			callThirdPartyDTO.setHeaderMap(transformHeadersNew(headerMap, callback));
		var uri = callThirdPartyDTO.getUrl();
		var urlTokenConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_TOKEN_CONFIG.name(),
				thirdPartyId + "_" + callback.getOrderType());
		if (urlTokenConfig != null) {
			var urlTokenConfigs = urlTokenConfig.getNgTableData();
			uri = formatUriForARM(uri, resolveUriTokensNew(urlTokenConfigs.get(AppConstants.CacheFields.TOKENS.name()), callback));
		}
		callThirdPartyDTO.setUrl(uri);
		try {
			callThirdPartyDTO.setUri(new URI(uri.trim()));
		} catch (URISyntaxException e) {
			throw new CommonException(null, e.getMessage());
		}
		restConnector.service(callThirdPartyDTO);
		return callThirdPartyDTO;
		}
		return null;
	}

	private Param resolveUriTokensNew(String tokens, OrderCallBack callback) {
		if (tokens == null)
			return new Param();
		DocumentContext context = null;
		try {
			context = tokens.contains("$") ? JsonPath.parse(objectMapper.writeValueAsString(callback)) : null;
		} catch (Exception e) {
			log.info("Exception occurred in resolveUriTokens: {}", e.getMessage());
			return new Param();
		}
		var tokenArray = tokens.split("\\|");
		var uriParams = new Param();
		for (var s : tokenArray) {
			var param = s.split("=", 2);
			if (param.length > 1) {
				var token = param[0];
				var value = param[1];
				log.info("token {} and value {} from url ",token,value);
				if (value.startsWith("$")) {
					resolveJsonPathARM(context, token, value, uriParams);
				} /*else if (value.startsWith("val")) {
					resolveValExpression(token, value, uriParams, orderFlowContext);
				} */ else {
					uriParams.put(token, value);
				}
			}
		}
		return uriParams;
	}

	private Map<String, String> transformHeadersNew(Map<String, String> headerMap, OrderCallBack callback) {
		Set<Map.Entry<String, String>> entrySet = null;
		Map<String, String> newMap = new HashMap<>();
		String path = null;
		String value = null;
		try {
			var docContext = JsonPath.parse(objectMapper.writeValueAsString(callback));
			entrySet = headerMap.entrySet();
			for (Map.Entry<String, String> entry : entrySet) {
				if (entry.getValue().startsWith("@(") && entry.getValue().endsWith(")")) {
					path = entry.getValue().substring(2, entry.getValue().length() - 1);
					log.info("Path for docContext " +path);
					try {
						value = docContext.read(path);
						log.info("Path value for docContext " +value);
					} catch (PathNotFoundException e) {
						log.warn("Path {} not found!!!", path);
					}
					if (ObjectUtils.isNotEmpty(value))
						newMap.put(entry.getKey(), value);
				} else if (entry.getValue().startsWith("{") && entry.getValue().endsWith("}")) {
                   /* newMap.put(entry.getKey(),
                            orderFlowContext.getAttributes().get(entry.getValue().substring(1, entry.getValue().length() - 1)));*/
				} else if (entry.getValue().startsWith("date(") && entry.getValue().endsWith(")")) {
					String timestamp = null;
					String format = entry.getValue().substring(5, entry.getValue().length() - 1);
					if (StringUtils.isNotEmpty(format)) {
						if (StringUtils.equalsIgnoreCase(format, "epoch")) {
							timestamp = String.valueOf(System.currentTimeMillis());
						} else {
							SimpleDateFormat sdf = null;
							sdf = new SimpleDateFormat(format);
							timestamp = sdf.format(System.currentTimeMillis());
						}
						newMap.put(entry.getKey(), timestamp);
					}
				} else
					newMap.put(entry.getKey(), entry.getValue());

			}

		} catch (Exception e) {
			log.error("Exception occurred in transformHeaders", e);
		}
		return newMap;
	}

	static class Param extends HashMap<String, String> {
		/**
		 *
		 */
		private static final long serialVersionUID = 1L;

		@Override
		public String get(Object key) {
			if (!super.containsKey(key)) {
				super.put(key.toString(), "");
			}
			return super.getOrDefault(key, "t");
		}

		@Override
		public boolean containsKey(Object arg0) {
			return true;
		}
	}

	protected String formatUriForARM(String uri, Param params) {
		return new StringSubstitutor(params).replace(uri);
	}

	protected Param resolveUriTokens(String tokens, OrderFlowContext orderFlowContext) {
		if (tokens == null)
			return new Param();
		DocumentContext context = null;
		try {
			context = tokens.contains("$") ? JsonPath.parse(objectMapper.writeValueAsString(orderFlowContext)) : null;
		} catch (Exception e) {
			log.info("Exception occurred in resolveUriTokens: {}", e.getMessage());
			return new Param();
		}
		var tokenArray = tokens.split("\\|");
		var uriParams = new Param();
		for (var s : tokenArray) {
			var param = s.split("=", 2);
			if (param.length > 1) {
				var token = param[0];
				var value = param[1];
				if (value.startsWith("$")) {
					resolveJsonPathARM(context, token, value, uriParams);
				} else if (value.startsWith("val")) {
					resolveValExpression(token, value, uriParams, orderFlowContext);
				} else {
					uriParams.put(token, value);
				}
			}
		}
		return uriParams;
	}

	private void resolveJsonPathARM(DocumentContext context, String key, String path, Param uriParams) {
		if (context == null || path == null || uriParams == null)
			return;
		log.info("path from url {}" ,path);
		var value = context.read(path);
		log.info("value for url param {}" ,value);
		if (value instanceof String || value instanceof Integer) {
			uriParams.put(key, String.valueOf(value));
		} else if (value instanceof List<?>) {
			uriParams.put(key, String.valueOf(value).replace("[", "").replace("]", "").replaceAll("\\s", ""));
		} else {
			log.info("ignoring token {}, as it is not a primitive", key);
		}
	}

	protected void resolveValExpression(String key, String path, Param uriParams, OrderFlowContext orderFlowContext) {
		if (path == null || uriParams == null)
			return;
		var valPath = path.substring(path.indexOf("(") + 1, path.indexOf(")"));
		if (orderFlowContext.getAttributes().containsKey(valPath)) {
			uriParams.put(key, orderFlowContext.getAttributes().get(valPath));
		} else if (key.equalsIgnoreCase("requestTimeStamp")) {
			uriParams.put(key, LocalDateTime.now().format(DateTimeFormatter.ofPattern(ApiConstants.API_DATE_FORMAT)));
		}
	}

	public Map<String, String> getHeaderMap(String headerParams) {
		Map<String, String> headerMap = null;
		try {
			if (StringUtils.isNotEmpty(headerParams)) {
				headerMap = new HashMap<String, String>();
				String[] headerParam = headerParams.split(",");
				for (String header : headerParam) {
					if (header.contains("=")) {
						String name = null, value = null;
						try {
							name = header.split("=")[0];
							value = header.split("=")[1];
							log.info("getHeaderMap name :" + name + " value :" + value);
							headerMap.put(name, value);
						} catch (Exception e) {
							throw e;
						}
					}
				}
			}
		} catch (Exception e) {
			throw e;
		}
		return headerMap;
	}

	protected Map<String, String> transformHeaders(Map<String, String> headerMap, OrderFlowContext orderFlowContext) {
		Set<Map.Entry<String, String>> entrySet = null;
		Map<String, String> newMap = new HashMap<>();
		String path = null;
		String value = null;
		try {
			var docContext = JsonPath.parse(objectMapper.writeValueAsString(orderFlowContext));
			entrySet = headerMap.entrySet();
			for (Map.Entry<String, String> entry : entrySet) {
				if (entry.getValue().startsWith("@(") && entry.getValue().endsWith(")")) {
					path = entry.getValue().substring(2, entry.getValue().length() - 1);
					try {
						value = docContext.read(path);
					} catch (PathNotFoundException e) {
						log.warn("Path {} not found!!!", path);
					}
					if (ObjectUtils.isNotEmpty(value))
						newMap.put(entry.getKey(), value);
				} else if (entry.getValue().startsWith("{") && entry.getValue().endsWith("}")) {
					newMap.put(entry.getKey(),
							orderFlowContext.getAttributes().get(entry.getValue().substring(1, entry.getValue().length() - 1)));
				} else if (entry.getValue().startsWith("date(") && entry.getValue().endsWith(")")) {
					String timestamp = null;
					String format = entry.getValue().substring(5, entry.getValue().length() - 1);
					if (StringUtils.isNotEmpty(format)) {
						if (StringUtils.equalsIgnoreCase(format, "epoch")) {
							timestamp = String.valueOf(System.currentTimeMillis());
						} else {
							SimpleDateFormat sdf = null;
							sdf = new SimpleDateFormat(format);
							timestamp = sdf.format(System.currentTimeMillis());
						}
						newMap.put(entry.getKey(), timestamp);
					}
				} else
					newMap.put(entry.getKey(), entry.getValue());

			}

		} catch (Exception e) {
			log.error("Exception occurred in transformHeaders", e);
		}
		return newMap;
	}

	@Override
	protected void execute() throws Exception {
		// TODO Auto-generated method stub

	}

}
