package in.co.sixdee.bss.com.orderorchestrator.service.wfdatamodifier;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderPayloadService;
import in.co.sixdee.bss.common.core.AppInstanceIdManager;
import in.co.sixdee.bss.common.util.ApplicationProcessContext;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Service;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Map;

@Component
@Log4j2
public class SubscriptionCfsModifier implements ExecutionListener {

    protected ApplicationProcessContext processContext;

    @Autowired
    protected ProcessDataAccessor processDataAccessor;

    @Autowired
    private AppInstanceIdManager appInstanceSequence;

    @Autowired
    private OrderPayloadService orderPayloadService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void notify(DelegateExecution execution) throws Exception {
        var service = new Service();
        Map<String, Object> currentExecutionMap = null;
        log.info(" {} Execution started for activity {} to modify the workflowData ", this.getClass().getSimpleName(), execution.getCurrentActivityId());
        var orderFlowContext = processDataAccessor.getOrderFlowContext(execution);
        createApplicationProcessContext(orderFlowContext);
        var payloadData = orderPayloadService.getOrderFlowContext(orderFlowContext.getOrder().getOrderId());

        if (payloadData == null) {
            log.info("Unable to find the payload data for the order {}", orderFlowContext.getOrderId());
            return;
        }
        if (orderFlowContext.getWorkflowData().containsKey("currentExecution")) {
            currentExecutionMap = (Map<String, Object>) orderFlowContext.getWorkflowData().get("currentExecution");
            service = objectMapper.convertValue(currentExecutionMap.get("executionData"), Service.class);
            if (StringUtils.equals(ExecutionListener.EVENTNAME_START, execution.getEventName())) {
                enrichCfss(payloadData, service);
            } else if (StringUtils.equals(ExecutionListener.EVENTNAME_END, execution.getEventName())) {
                if (execution.getCurrentActivityId().equalsIgnoreCase(GenericConstants.NCC_CREATE_SERVICE_HANDLER)) {
                    service.getSubscriptions().forEach(sub -> {
                        sub.setCfss(null);
                    });
                }
            }
            try {
                currentExecutionMap.put("executionData",
                        com.bazaarvoice.jolt.JsonUtils.jsonToObject(objectMapper.writeValueAsString(service)));
            } catch (JsonProcessingException e) {
                log.error("Exception occurred while creating executionData", e);
            }
            orderFlowContext.getWorkflowData().put("currentExecution", currentExecutionMap);
            processDataAccessor.setOrderFlowContext(execution, orderFlowContext);
        }
    }

    private void enrichCfss(OrderFlowContext payloadData, Service service) {
        var sgId = service.getGroupId();
        var serviceId = service.getServiceId();
        var sgGroup = payloadData.getOrder().getProfile().getAccount().getServiceGroups().stream().filter(
                sg -> sg.getId() != null && sg.getId().equals(sgId)).findFirst().orElse(null);
        var serviceFromPayload = sgGroup.getServices().stream().filter(svc -> svc.getServiceId().equals(serviceId)).findFirst().orElse(null);
        boolean subscriptionFound = false;
        for (Subscription sub : service.getSubscriptions()) {
            subscriptionFound = false;
            if (ObjectUtils.isNotEmpty(sgGroup.getSubscriptions())) {
                var subscriptionFromPayload = sgGroup.getSubscriptions().stream().filter(subsc -> subsc.getPlanId().equals(sub.getPlanId())).findFirst().orElse(null);
                if (subscriptionFromPayload != null) {
                    subscriptionFound = true;
                    setCfssNccToWorkflowData(subscriptionFromPayload, sub);
                }
            }
            if (!subscriptionFound && ObjectUtils.isNotEmpty(serviceFromPayload.getSubscriptions())) {
                var subscriptionFromPayload = serviceFromPayload.getSubscriptions().stream().filter(subsc -> subsc.getPlanId().equals(sub.getPlanId())).findFirst().orElse(null);
                if (subscriptionFromPayload != null) {
                    setCfssNccToWorkflowData(subscriptionFromPayload, sub);
                }
            }
        }
    }

    private void createApplicationProcessContext(OrderFlowContext orderFlowContext) {
        processContext = new ApplicationProcessContext(appInstanceSequence.getInstanceId(), orderFlowContext.getTraceId(),
                orderFlowContext.getRequestId(), orderFlowContext.getChannel(), orderFlowContext.getChannel(),orderFlowContext.getEntityId());
        processContext.setMdc();
    }


    private void setCfssNccToWorkflowData(Subscription subscriptionFromPayload, Subscription subscription) {
        subscription.setCfss(new ArrayList<>());
        subscriptionFromPayload.getCfss().stream().filter(cfs -> cfs.getName().equalsIgnoreCase(GenericConstants.CFS_M1_Base)).
                forEach(cfs -> subscription.getCfss().add(cfs));
    }

}
