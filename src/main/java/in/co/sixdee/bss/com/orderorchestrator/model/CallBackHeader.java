package in.co.sixdee.bss.com.orderorchestrator.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
public class CallBackHeader implements Serializable {

    private String traceId;
    private String entityId;
    private String channel;
    private String username;
    private String userId;
    private String legacyApi;
}
