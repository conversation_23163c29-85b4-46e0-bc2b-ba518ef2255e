package in.co.sixdee.bss.com.orderorchestrator.model.mnp.submitportin;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Data;

import java.io.Serializable;

@XmlRootElement(name = "submitPortResponse")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class SubmitPortResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @XmlElement(name = "SDPStatusResponse", namespace = "http://group.singtel.com/enterpriseapplicationintegration/common-types/v1")
    private SDPStatusResponse sdpStatusResponse;

}
