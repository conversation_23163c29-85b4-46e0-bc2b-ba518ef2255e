package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.om.model.dto.WorkFlowErrorBean;
import lombok.Data;

/*
 * base Exception Class
 * 
 */
@Data
public class CommonExceptionResponse {

	private String					requestId;
	private String					timestamp;
	private Integer					code;
	private String					status;
	private String					message;

	private List<WorkFlowErrorBean>	errors;

	private String					reason;

	public CommonExceptionResponse(String requestId, Integer code, String message, String status) {
		this.requestId = requestId;
		this.code = code;
		this.message = message;
		this.status = status;
		this.timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(GenericConstants.TIMESTAMP_FORMAT));
	}

	public CommonExceptionResponse(String requestId, Integer code, String message, String status, String reason) {
		this.requestId = requestId;
		this.code = code;
		this.message = message;
		this.status = status;
		this.reason = reason;
		this.timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(GenericConstants.TIMESTAMP_FORMAT));
	}

	public CommonExceptionResponse(String requestId, Integer code, String message, String status, List<WorkFlowErrorBean> errors,
			String reason) {
		this.requestId = requestId;
		this.code = code;
		this.message = message;
		this.errors = errors;
		this.status = status;
		this.reason = reason;
		this.timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(GenericConstants.TIMESTAMP_FORMAT));
	}

	public CommonExceptionResponse() {}

	public CommonExceptionResponse(String requestId, Integer code, String message) {
		this.requestId = requestId;
		this.code = code;
		this.message = message;
		this.timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(GenericConstants.TIMESTAMP_FORMAT));
	}

	public CommonExceptionResponse(String requestId, Integer code, String message, String status,
			List<WorkFlowErrorBean> errors) {
		this.requestId = requestId;
		this.code = code;
		this.message = message;
		this.errors = errors;
		this.status = status;
		this.timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(GenericConstants.TIMESTAMP_FORMAT));
	}

	public CommonExceptionResponse(Integer code, String defaultMessage, String requestId) {
		this.requestId = requestId;
		this.code = code;
		this.message = defaultMessage;
		this.timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(GenericConstants.TIMESTAMP_FORMAT));
	}

	public CommonExceptionResponse(String message) {
		this.message = message;
		this.timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(GenericConstants.TIMESTAMP_FORMAT));
	}

	public CommonExceptionResponse(Integer code, String defaultMessage) {
		this.code = code;
		this.message = defaultMessage;
		this.timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(GenericConstants.TIMESTAMP_FORMAT));
	}

}
