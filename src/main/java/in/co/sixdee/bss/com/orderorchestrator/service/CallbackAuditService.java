package in.co.sixdee.bss.com.orderorchestrator.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CallbackAuditStrategy;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.CallBackAuditInfoEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.CallbackAuditInfoRepository;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.constants.ApiConstants;
import in.co.sixdee.bss.common.util.SequenceGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Level;
import org.slf4j.MDC;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
@Log4j2
public class CallbackAuditService {

    private CallbackAuditStrategy auditStrategy = CallbackAuditStrategy.FILE;

    protected final GetDataFromCache cache;

    private static final Level LOG_LEVEL_CALLBACK = Level.getLevel("LOG_LEVEL_CALLBACK");

    protected final ObjectMapper objectMapper;

    protected final CallbackAuditInfoRepository callbackAuditInfoRepository;


    public CallbackAuditStrategy getAuditStrategy() {
        var auditConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), CacheConstants.CacheFields.CALLBACK_AUDIT_STRATEGY.name());
        if (auditConfig != null) {
            try {
                auditStrategy = CallbackAuditStrategy.nameOf(auditConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()));
            } catch (IllegalArgumentException e) {
                log.info("No audit strategy exists in the system which matches the configuration and taking the default strategy as {}", auditStrategy.getName());
            }
        }
        return auditStrategy;
    }

    public void auditCallback(OrderCallBack callback) {
        switch (getAuditStrategy()) {
            case TABLE:
                createTableAudit(callback);
                break;
            case FILE:
                createFileAudit(callback);
                break;
            default:
        }
    }


    private void createFileAudit(OrderCallBack callBack) {
        try {
            MDC.put(GenericConstants.ORDER_ID, callBack.getOrderId());
            MDC.put("callbackAuditTimestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd-MM-yyyy-HH-mm-ss")));
            log.info("Writing callback audit for the order {}", callBack.getOrderId());
            log.log(LOG_LEVEL_CALLBACK, objectMapper.writeValueAsString(callBack));
        } catch (Exception e) {
            log.error("Exception occurred while writing callback audit to file {}", e.getMessage());
        }
    }


    private void createTableAudit(OrderCallBack callBack) {
        try {
            CallBackAuditInfoEntity callBackAuditInfoEntity = new CallBackAuditInfoEntity();
            callBackAuditInfoEntity.setSeqId(SequenceGenerator.getSequencerInstance().nextId());
            callBackAuditInfoEntity.setOrderId(Long.parseLong(callBack.getOrderId()));
            if (callBack.getSubOrderId() != null)
                callBackAuditInfoEntity.setSubOrderId(Long.parseLong(callBack.getSubOrderId()));
            callBackAuditInfoEntity.setPayload(objectMapper.writeValueAsString(callBack));
            callBackAuditInfoEntity.setChannel(MDC.get(ApiConstants.CHANNEL));
            callbackAuditInfoRepository.save(callBackAuditInfoEntity);
        } catch (Exception e) {
            log.error("Exception occurred while writing callback audit to table {}", e.getMessage());
        }
    }

    private Integer getcallBackRetryCount() {
        String value = null;

        var keyConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "CALLBACK_MAX_RETRY_COUNT");
        if (keyConfig != null)
            value = keyConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
        else
            log.error("CONFIG_KEY:{} is missing in {} table", "CALLBACK_MAX_RETRY_COUNT",
                    CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name());

        return StringUtils.isNotEmpty(value) ? Integer.parseInt(value) : 0;
    }

    public void createTableAuditForCallBack(OrderCallBack callBack) {
        try {
            CallBackAuditInfoEntity callBackAuditInfoEntity = new CallBackAuditInfoEntity();
            //callBackAuditInfoEntity.setSeqId(SequenceGenerator.getSequencerInstance().nextId());
            callBackAuditInfoEntity.setOrderId(Long.parseLong(callBack.getOrderId()));
            if (callBack.getSubOrderId() != null)
                callBackAuditInfoEntity.setSubOrderId(Long.parseLong(callBack.getSubOrderId()));
            callBackAuditInfoEntity.setPayload(objectMapper.writeValueAsString(callBack));
            callBackAuditInfoEntity.setChannel(MDC.get(ApiConstants.CHANNEL));
            callBackAuditInfoEntity.setRemainingRetries(getcallBackRetryCount());
            callBackAuditInfoEntity.setIsProcessed(GenericConstants.CALL_BACK_STATUS_NON_PROCESSED);
            if(StringUtils.isNotEmpty(callBack.getCallbackType())) {
                callBackAuditInfoEntity.setCallbackType(callBack.getCallbackType());
            }
            callbackAuditInfoRepository.save(callBackAuditInfoEntity);
        } catch (Exception e) {
            log.error("Exception occurred while writing callback audit to table {}", e.getMessage());
        }
    }

    @Transactional
    public List<CallBackAuditInfoEntity> getDataForCallBackScheduler(Pageable page) {
        return callbackAuditInfoRepository.getDataforCallBackScheduler(page);
    }

    @Transactional
    public int updateDataForCallBackScheduler(String isProcessed, int version, Date lockExpTime, Long orderId, Long subOrderId, String callbackType) {
        return callbackAuditInfoRepository.updateIsProcessedToPicked(isProcessed, version, lockExpTime, orderId, subOrderId, callbackType);
    }

    @Transactional
    public int updateDataForCallBackSchedulerOrderId(String isProcessed, int version, Date lockExpTime, Long orderId, String callbackType) {
        return callbackAuditInfoRepository.updateIsProcessedToPickedWithOrderId(isProcessed, version, lockExpTime, orderId, callbackType);
    }

    public void createTableAuditForCallBackFromScheduler(OrderCallBack callBack, CallBackAuditInfoEntity auditInfoEntity) {
        try {
            CallBackAuditInfoEntity callBackAuditInfoEntity = new CallBackAuditInfoEntity();
            //callBackAuditInfoEntity.setSeqId(SequenceGenerator.getSequencerInstance().nextId());
            callBackAuditInfoEntity.setOrderId(Long.parseLong(callBack.getOrderId()));
            if (StringUtils.isNotEmpty(callBack.getSubOrderId()))
                callBackAuditInfoEntity.setSubOrderId(Long.parseLong(callBack.getSubOrderId()));
            callBackAuditInfoEntity.setPayload(objectMapper.writeValueAsString(callBack));
            callBackAuditInfoEntity.setChannel(MDC.get(ApiConstants.CHANNEL));
            callBackAuditInfoEntity.setIsProcessed(GenericConstants.CALL_BACK_STATUS_NON_PROCESSED);
            callBackAuditInfoEntity.setRemainingRetries(getRemainingRetries(auditInfoEntity.getRemainingRetries()));
            if (callBackAuditInfoEntity.getRemainingRetries() > 0) {
                callbackAuditInfoRepository.save(callBackAuditInfoEntity);
            }
        } catch (Exception e) {
            log.error("Exception occurred while writing callback audit to table {}", e.getMessage());
        }
    }

    private Integer getRemainingRetries(Integer remainingRetries) {
        return Math.max(remainingRetries - 1, 0);
    }

    public CallBackAuditInfoEntity getAuditDetailsForCallBack(String callBackStatusNonProcessed, String orderId, String subOrderId, String callBackType) {
        if(StringUtils.isNotEmpty(callBackType)) {
            if (StringUtils.isNotEmpty(orderId) && StringUtils.isNotEmpty(subOrderId))
                return callbackAuditInfoRepository.getDetailsForCallBackListener(callBackStatusNonProcessed, Long.parseLong(orderId), Long.parseLong(subOrderId), callBackType);
            else
                return callbackAuditInfoRepository.getDetailsForCallBackListenerByOrderId(callBackStatusNonProcessed, Long.parseLong(orderId), callBackType);
        }
        return null;
    }

    @Transactional
    public void updateCallBackToProcessed(String orderId, String subOrderId, String callBackStatusProcessed, String callBackType) {
        if (StringUtils.isNotEmpty(orderId) && StringUtils.isNotEmpty(subOrderId)) {
            callbackAuditInfoRepository.updateForCallBackListener(callBackStatusProcessed, Long.parseLong(orderId), Long.parseLong(subOrderId), callBackType);
        } else
            callbackAuditInfoRepository.updateForCallBackListenerByOrderId(callBackStatusProcessed, Long.parseLong(orderId), callBackType);
    }

    @Transactional
    public void updateCallBackEntryFromScheduler(Long orderId, Long subOrderId) {
        log.info("" +
                "" +
                "" +
                "data in updateCallBackEntryFromScheduler order id {}, sub order id {}",orderId,subOrderId);
        if ( orderId!=null && subOrderId!=null) {
            log.info("data in updateCallBackEntryFromScheduler tow order id {}, sub order id {}",orderId,subOrderId);

            callbackAuditInfoRepository.updateAuditEntryForCallBack(GenericConstants.CALL_BACK_STATUS_NON_PROCESSED,getcallBackRetryCount(),0,null, orderId, subOrderId);
        } else
            callbackAuditInfoRepository.updateAuditEntryForCallBackOrderId(GenericConstants.CALL_BACK_STATUS_NON_PROCESSED,getcallBackRetryCount(),0,null, orderId);
    }
}
