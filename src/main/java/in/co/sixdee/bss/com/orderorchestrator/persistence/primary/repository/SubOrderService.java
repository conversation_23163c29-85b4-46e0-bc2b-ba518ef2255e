/**
 *
 */
package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SubOrderService {

    private final SubOrderRepository subOrderRepository;


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int updateSubOrderStatusByOrderId(String state, String stateReason, Long orderId, Date lastModifiedDate, String failedStageCode) {
        return subOrderRepository.updateSubOrderStatusByOrderId(state, stateReason, orderId, failedStageCode);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int updateSubOrderStatus(String status, String statusReason, long subOrderId, Date lastModifiedDate, String failedStageCode) {
        return subOrderRepository.updateSubOrderStatus(status, statusReason, subOrderId, failedStageCode);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<String> getSuborderStatusByOrderId(Long orderId) {
        return subOrderRepository.getSuborderStatusByOrderId(orderId);
    }

    public List<Long> getSubordersByOrderId(Long orderId) {
        return subOrderRepository.getSubordersByOrderId(orderId);
    }

    @Transactional
    public void updateSubOrderToInProgress(Long id) {
        subOrderRepository.updateSubOrderToInProgress(id);
    }

}