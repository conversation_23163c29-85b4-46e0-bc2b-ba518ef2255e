package in.co.sixdee.bss.com.orderorchestrator.service;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.EdrConfig;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.ProcessVariableUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CallbackProcessingException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.EntityValidationException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.WorkflowTaskFailedException;
import in.co.sixdee.bss.com.orderorchestrator.config.notification.NotificationUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.*;
import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.*;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.*;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.ARMFetchAssetDetails;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.constants.ApiConstants;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.Response;
import in.co.sixdee.bss.om.model.dto.order.Dataset;
import in.co.sixdee.bss.om.model.dto.order.Param;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.OptimisticLockingException;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.spin.plugin.variable.SpinValues;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;

@Service
@Log4j2
@RequiredArgsConstructor
public class OrderCallBackService {

    private final OrderStageRepository orderStageRepository;

    private final OrderAttributeRepository orderAttributeRepository;

    private final OrderStatusManager orderStatusManager;

    private final ObjectMapper objectMapper;

    private final RuntimeService runtimeService;

    private final GetDataFromCache getDataFromCache;

    private final WaitingProcessInfoRepository waitingProcessInfoRepository;

    private final OrderRepository orderRepository;

    @Value("${callback.retry.max-retries-allowed:1}")
    protected Integer maxRetriesAllowed;

    private final ConcurrentLinkedQueue<OrderCallBack> sessionQueue;

    private final EdrConfig edrConfig;

    private final CallbackAuditService callbackAuditService;

    @Autowired
    protected NotificationUtils notificationUtils;

    @Autowired
    protected ARMFetchAssetDetails armFetchDetails;

    protected final ProcessVariableUtils processVariableUtils;

    private final SubOrderRepository subOrderRepository;

    public Response processSomCallback(OrderCallBack callback) throws JsonMappingException, JsonProcessingException {
        log.info("Received SOM callback for the order id {} with correlation id {}", callback.getOrderId(),
                callback.getCorrelationId());
        validateCallback(callback);

        executeCallback(callback);
        return createResponse(callback);
    }

    public Response processCallback(OrderCallBack callback) {
        OrderFlowContext orderFlowContext=new OrderFlowContext();
        if (callback.getOrderId() == null) {
            validateReferenceIdCallback(callback);
            log.info("Received callback for ReferenceId {}", callback.getReferenceId());
        } else {
            log.info("Received callback for the order id {}", callback.getOrderId());
            validateCallback(callback);
        }
        if (callback.getType() != null && callback.getType().toUpperCase().contains(WorkFlowConstants.OrderCallBackTypes.SIM_DELIVERY.desc)) {
            callback.setCallbackType(WorkFlowConstants.OrderCallBackTypes.SIM_DELIVERY.desc);
            if (callback.getWaitingEventInfo() == null) {
                var waitingEventInfo = getWaitEventInfo(callback.getOrderId(), callback.getSubOrderId());
                if (waitingEventInfo != null) {
                    callback.setWaitingEventInfo(waitingEventInfo);
                } else {
                    log.info(
                            "Unable to find the wait event information for this order id {}. Perhaps the wait event info is not populated yet",
                            callback.getOrderId());
                    throw new CommonException("Unable to get the wait event info");
                }
            }
            try {
                orderFlowContext = objectMapper.readValue(
                        runtimeService.getVariable(callback.getWaitingEventInfo().getProcessInstanceId(),
                                WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString()).toString(),
                        OrderFlowContext.class);
                //orderFlowContext.getWorkflowData().put("CallBackDetails", JsonUtils.jsonToObject(JsonUtils.toJsonString(callback)));
                orderFlowContext.getAttributes().put("callbacknewIMSI", callback.getNewIMSI());
                orderFlowContext.getAttributes().put("callbacknewICCID", callback.getNewICCID());
               armFetchDetails.getDetailsForCallBack(callback);
               
            } catch (JsonProcessingException e) {
                // TODO Auto-generated catch block
            }
        }
        executeCallback(callback);
        if (ObjectUtils.isNotEmpty(orderFlowContext) && callback.getType() != null && callback.getType().toUpperCase().contains(WorkFlowConstants.OrderCallBackTypes.SIM_DELIVERY.desc))
            return createResponseForSimDelivery(callback, orderFlowContext);
        else
            return createResponse(callback);
    }

    private Response createResponseForSimDelivery(OrderCallBack request, OrderFlowContext orderFlowContext) {
        var response = new Response();
        createResponseBody(response, request);
        if (shouldCreateDataSet(orderFlowContext.getEntityId())) {
            createDataset(response, request);
        }
        return response;
    }

    private void createDataset(Response response, OrderCallBack request) {
        List<Param> params = new ArrayList<Param>();
        if (request.getProfileId() != null) {
            Param param = new Param();
            param.setId("profileId");
            param.setValue(request.getProfileId());
            params.add(param);
        }
        if (request.getAccountId() != null) {
            Param param = new Param();
            param.setId("accountId");
            param.setValue(request.getAccountId());
            params.add(param);
        }
        if (!params.isEmpty()) {
            Dataset dataset = new Dataset();
            dataset.setParam(params);
            response.setDataset(dataset);
        }
    }

    private void createResponseBody(Response response, OrderCallBack request) {
        response.setRequestId(StringUtils.isNotEmpty(MDC.get(ApiConstants.TRACE_ID)) ? MDC.get(ApiConstants.TRACE_ID)
                : MDC.get(ApiConstants.REQUEST_ID));
        response.setStatus("Success");
        response.setCode("200");
        response.setMessage("Callback initiated successfully");
        response.setOrderId(request.getOrderId());
    }


    public void validateReferenceIdCallback(OrderCallBack callback) {
        long orderId;

        if (ObjectUtils.isNotEmpty(callback.getmnpAttributes())&& ObjectUtils.isNotEmpty(callback.getmnpAttributes().get(MNPConstants.MNP_CALLBACK_TYPE))&&MNPConstants.MNP_CALLBACK_TYPE_DISCONNECT_INT.equalsIgnoreCase(callback.getmnpAttributes().get(MNPConstants.MNP_CALLBACK_TYPE).toString()) && ObjectUtils.isNotEmpty(callback.getmnpAttributes().get(MNPConstants.MNP_SERVICE_ID))) {
            fetchOrderDetailsForDisconnectServiceInternal(callback);
        } else {
            fetchOrderDetails(callback);
        }
        MDC.put(ApiConstants.ORDER_ID, callback.getOrderId());
        try {
            orderId = Long.parseLong(callback.getOrderId());

        } catch (Exception e) {
            log.info("Exception occurred {}", e.getMessage());
            throw new EntityValidationException(
                    "Invalid order id : " + callback.getOrderId() + ". This order is not present in the system");
        }
        validateStatus(callback);
        validateOrder(orderId);
        String subOrderId = null;
        try {
            subOrderId = callback.getCorrelationId().split(":")[1];
            callback.setSubOrderId(subOrderId);
        } catch (Exception e) {
            log.info("Invalid correlation id : {}", callback.getCorrelationId());
            throw new EntityValidationException("Invalid correlation id : " + callback.getCorrelationId());
        }
    }

    private void fetchOrderDetailsForDisconnectServiceInternal(OrderCallBack callback) {
        try {
            String serviceId = getServiceIdWithCC(callback.getmnpAttributes().get(MNPConstants.MNP_SERVICE_ID).toString());
            SubOrderEntity orderAttributes = subOrderRepository
                    .getDetailsForDisconnectServiceInternal(serviceId, WorkFlowConstants.WorkItemExecutionStatus.STATUS_INPROGRESS.getValue(), WorkFlowActivityConstants.activityReasonDescriptionMap.get("DisconnectServiceIntCallback"));
            if (ObjectUtils.isEmpty(orderAttributes)) {
                log.info("Unable to find the order when searching with service id {}", serviceId);
                throw new EntityValidationException(
                        "Unable to find the order when searching with service id " + serviceId);
            }
            callback.setOrderId(String.valueOf(orderAttributes.getOrderId()));
            var activityId = callback.getCorrelationId();
            callback.setSubOrderId(String.valueOf(orderAttributes.getId()));
            callback.setCorrelationId(activityId + ":" + orderAttributes.getId());
        } catch (Exception e) {
            log.info("Exception occurred", e);

        }
    }

    private String getServiceIdWithCC(String serviceId) {
        CacheTableDataDTO countryCodeConfig = getDataFromCache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG, "COUNTRY_CODE");
        if (countryCodeConfig == null) {
            log.info("Unable to find COUNTRY_CODE configuration from OM_APPLICATION_CONFIG");
            return serviceId;
        }
        String countryCode = countryCodeConfig.getNgTableData().get("CONFIG_VALUE");
        if (StringUtils.isEmpty(countryCode)) {
            log.info("COUNTRY_CODE configuration value is empty in OM_APPLICATION_CONFIG");
            return serviceId;
        }
        if (StringUtils.isNotEmpty(serviceId) && !serviceId.startsWith(countryCode)) {
            return countryCode + serviceId;
        }
        return serviceId;
    }

    private void validateStatus(OrderCallBack callback) {
        // TODO Auto-generated method stub
        try {
            var appConfig = getDataFromCache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                    "CALLBACK_STATUS_VALIDATE");
            String configValue = appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
            if (configValue.equalsIgnoreCase("true")) {
                if ("completed".equalsIgnoreCase(callback.getStatus()) || "failed".equalsIgnoreCase(callback.getStatus()) || "rejected".equalsIgnoreCase(callback.getStatus()) || "cancelled".equalsIgnoreCase(callback.getStatus())) {

                } else {
                    throw new CommonException(configValue);
                }
            }

        } catch (CommonException e) {
            // TODO: handle exception
            throw new EntityValidationException("Invalid status : " + callback.getStatus());
        } catch (Exception e) {
            // TODO: handle exception
            log.info("possible cache null");
            //throw new EntityValidationException("Invalid status : " + callback.getStatus());
        }


    }

    public void fetchOrderDetails(OrderCallBack callback) {
        try {
            String referenceId = callback.getReferenceId();
            OrderAttributeEntity orderAttributes = orderAttributeRepository
                    .getAttributesByReferenceId(referenceId);
            if (ObjectUtils.isEmpty(orderAttributes)) {
                log.info("Unable to find the order when searching with referenceId column. checking if the order exist with mnp requestId as {}", referenceId);
                orderAttributes = orderAttributeRepository
                        .getAttributesByRequestId(referenceId);
                if (orderAttributes == null)
                    throw new EntityValidationException(
                            "Unable to find any order matching with referenceId/requestId : " + callback.getReferenceId());
            }
            callback.setOrderId(orderAttributes.getOrderId().toString());
            var activityId = callback.getCorrelationId();
            callback.setSubOrderId(String.valueOf(orderAttributes.getSubOrderId()));
            callback.setCorrelationId(activityId + ":" + orderAttributes.getSubOrderId());
        } catch (Exception e) {
            log.info("Exception occurred", e);

        }

    }

    public void executeCallback(OrderCallBack callback) {
        try {
            if (WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue()
                    .equalsIgnoreCase(callback.getStatus())
                    || WorkFlowConstants.OrderCallBackTypes.PAYMENT.desc.equals(callback.getCallbackType())
                    || WorkFlowConstants.OrderCallBackTypes.APPROVAL.desc.equals(callback.getCallbackType())
                    || WorkFlowConstants.OrderCallBackTypes.SIM_DELIVERY.desc.equals(callback.getCallbackType())) {
                signalProcessContinuation(callback);
            } else {
                handleFailureCallback(callback);
            }
        } catch (CommonException e) {
            log.info("Exception occurred in OrderCallBackService.executeCallback {}. adding to the retry queue", e.getMessage());
            addToRetryQueue(callback);
        } catch (Exception e) {
            log.info("Exception occurred in OrderCallBackService.executeCallback {}", e.getMessage());
            throw e;
        }
    }

    private void handleFailureCallback(OrderCallBack callback) throws CommonException {
        OrderStageEntity stageInfo = null;
        if ("failed".equalsIgnoreCase(callback.getStatus()) || "rejected".equalsIgnoreCase(callback.getStatus()) || "cancelled".equalsIgnoreCase(callback.getStatus()))
            callback.setStatus("Failed");
        if (!"Completed".equalsIgnoreCase(callback.getStatus())) {
            callback.setStatus("Failed");
        }
        if (callback.getCorrelationId() != null && callback.getCorrelationId().startsWith("NG")) {
            stageInfo = getStageInfoBySubOrderIdAndStageCode(callback.getSubOrderId(), "NG");
        } else if (callback.getCorrelationId() != null && callback.getCorrelationId().startsWith("MNP")) {
            stageInfo = getStageInfoBySubOrderIdAndStageCode(callback.getSubOrderId(), "MNP");
        }
        if (callback.getCorrelationId() != null && callback.getCorrelationId().startsWith("SOM")) {
            stageInfo = getStageInfoBySubOrderIdAndStageCode(callback.getSubOrderId(), "SOM");
        }

        if (stageInfo == null)
            throw new CommonException("Unable to get the stage info ");
        var waitingEventInfo = getWaitEventInfo(callback.getOrderId(), callback.getSubOrderId());
        if (waitingEventInfo == null)
            throw new CommonException("Unable to get the wait event info ");
        if (callback.isRollbacked()) {
            if (Boolean.parseBoolean(stageInfo.getRollbackStatus()))
                throw new EntityValidationException(StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION,
                        "stage : " + waitingEventInfo.getStageCode() + " is already marked as rollback complete");
        }
        try {
            OrderFlowContext orderFlowContext = objectMapper.readValue(
                    runtimeService.getVariable(waitingEventInfo.getExecutionId(),
                            WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString()).toString(),
                    OrderFlowContext.class);
            if (StringUtils.isNotEmpty(callback.getFailureReason()))
                orderFlowContext.getErrorDetail().setMessage(callback.getFailureReason());
            //orderFlowContext.getErrorDetail().setSystem("MNP");
            orderStatusManager.UpdateCallBackOrderStatus(orderFlowContext, callback, stageInfo);
        } catch (Exception e) {
            log.info("Exception while getting orderFlowContext for edr writing: {}", e.getMessage());
            throw new CallbackProcessingException("Unable to get the orderFlowContext for edr writing");
        }

    }

    private void validateCallback(OrderCallBack callback) {
        long orderId;
        MDC.put(ApiConstants.ORDER_ID, callback.getOrderId());
        try {
            orderId = Long.parseLong(callback.getOrderId());

        } catch (Exception e) {
            log.info("Exception occurred {}", e.getMessage());
            throw new EntityValidationException("Invalid order id : " + callback.getOrderId() + ". This order is not present in the system");
        }

        OrderEntity orderEntity = validateOrder(orderId);
        if (orderEntity != null) {
            callback.setProfileId(orderEntity.getCustomerId());
            callback.setAccountId(orderEntity.getBillingAccountId());
        }
        String subOrderId = null;
        try {
            if (StringUtils.isNotEmpty(callback.getCorrelationId())) {
                subOrderId = callback.getCorrelationId().split(":")[1];
                callback.setSubOrderId(subOrderId);
            }
        } catch (Exception e) {
            log.info("Invalid correlation id : {}", callback.getCorrelationId());
            throw new EntityValidationException("Invalid correlation id : " + callback.getCorrelationId());
        }
    }

    public void signalProcessContinuation(OrderCallBack callback) throws CommonException {
        if (callback.getWaitingEventInfo() == null) {
            var waitingEventInfo = getWaitEventInfo(callback.getOrderId(), callback.getSubOrderId());
            if (waitingEventInfo != null) {
                callback.setWaitingEventInfo(waitingEventInfo);
            } else {
                log.info(
                        "Unable to find the wait event information for this order id {}. Perhaps the wait event info is not populated yet",
                        callback.getOrderId());
                throw new CommonException("Unable to get the wait event info");
            }
        }
        var isReadyToReceive = runtimeService.createExecutionQuery().messageEventSubscription()
                .processInstanceId(callback.getWaitingEventInfo().getProcessInstanceId()).count() > 0;
        if (!isReadyToReceive) {
            log.info(
                    "Process instance {} for order {} is not ready to receive the callback event", callback.getWaitingEventInfo().getProcessInstanceId(), callback.getOrderId());
            throw new CommonException("Process instance " + callback.getWaitingEventInfo().getProcessInstanceId() + " is not ready to receive callbacks");
        } else {
            log.info("Process instance {} for order {} is ready to receive callback event",
                    callback.getWaitingEventInfo().getProcessInstanceId(), callback.getOrderId());
            try {
                OrderFlowContext orderFlowContext = objectMapper.readValue(
                        runtimeService.getVariable(callback.getWaitingEventInfo().getProcessInstanceId(),
                                WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString()).toString(),
                        OrderFlowContext.class);
                if (WorkFlowConstants.OrderCallBackTypes.SOM.desc.equals(callback.getCallbackType())) {
                    notificationUtils.sendNotification(orderFlowContext,
                            AppConstants.NotificationMessageTypes.WELCOME_NOTIFICATION.value(), "SUB_ORDER");
                }


                if (WorkFlowConstants.OrderCallBackTypes.SIM_DELIVERY.desc.equals(callback.getCallbackType()) || WorkFlowConstants.OrderCallBackTypes.PAYMENT.desc.equals(callback.getCallbackType())) {
                    setCallBackRequestToorderFlowContext(orderFlowContext, callback);
//                    extractPaymentCallbackAttributes(orderFlowContext, callback);
                    var spinValue = SpinValues.jsonValue(objectMapper.writeValueAsString(orderFlowContext)).create();
                    runtimeService.setVariable(callback.getWaitingEventInfo().getExecutionId(),
                            WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString(), spinValue);
                }
//                if (WorkFlowConstants.OrderCallBackTypes.DISCONNECT_SERVICE.desc.equals(callback.getCallbackType())||WorkFlowConstants.OrderCallBackTypes.DISCONNECT_SERVICE_INTERNAL.desc.equals(callback.getCallbackType())) {
//                 callback.getmnpAttributes().putAll(orderFlowContext.getAttributes()); // storing attributes got in initial validate portout req
//                }

                log.info(" invoking call back signal. orderId: {}, subOrderId: {}", callback.getOrderId(), callback.getSubOrderId());
                runtimeService.setVariable(callback.getWaitingEventInfo().getExecutionId(), "waitEventEntityId",
                        String.valueOf(callback.getWaitingEventInfo().getSeqId()));
                runtimeService.createMessageCorrelation(callback.getWaitingEventInfo().getEventName())
                        .processInstanceId(callback.getWaitingEventInfo().getProcessInstanceId()).correlate();
            } catch (OptimisticLockingException e) {
                log.info("Got OptimisticLockingException. adding to retry queue", e);
                throw new CallbackProcessingException(e.getMessage());
            } catch (Exception e) {
                log.info("Got Exception. adding to retry queue", e);
                throw new CallbackProcessingException(e.getMessage());
            }
        }
    }

    public void addToRetryQueue(OrderCallBack callback) {
        try {
            if (callback.isNumberOfRetry() <= maxRetriesAllowed) {
                sessionQueue.add(callback);
            } else {
                log.info("All retries are exhausted for the order id {}", callback.getOrderId());
                callbackAuditService.auditCallback(callback);
            }
        } catch (Exception e) {
            log.info("Exception while adding to callback queue {}", e.getMessage());
            callbackAuditService.auditCallback(callback);
        }
    }

    public Response processPaymentRequest(OrderCallBack callback) {
        validatePaymentOrder(Long.parseLong(callback.getOrderId()));
        validatePaymentCallback(callback);
        executeCallback(callback);
        return createResponse(callback);
    }

    protected OrderFlowContext initOrderFlowContext(WaitingProcessInfoEntity waitingEventInfo, OrderCallBack callback) {
        OrderFlowContext orderFlowContext;
        try {
            orderFlowContext = objectMapper.readValue(
                    runtimeService.getVariable(waitingEventInfo.getExecutionId(),
                            WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString()).toString(),
                    OrderFlowContext.class);
            orderFlowContext.getAttributes().put("eventEntityId", String.valueOf(waitingEventInfo.getSeqId()));
        } catch (Exception e) {
            throw new CommonException("couldn't obtain order flow context");
        }
        return orderFlowContext;
    }

    protected void validatePaymentCallback(OrderCallBack callback) {
        var orderAttributes = orderAttributeRepository.getAttributesByOrderId(Long.valueOf(callback.getOrderId()));
        if (ObjectUtils.isEmpty(orderAttributes))
            throw new EntityValidationException("Order is not waiting for the orderId : " + callback.getOrderId());
        validatePaymentAmount(orderAttributes, callback);
        //validateDepositAmount(orderAttributes, callback);
    }

    protected void validatePaymentAmount(List<OrderAttributeEntity> orderAttributeEntities, OrderCallBack callback) {
        var paymentAmount = orderAttributeEntities.stream()
                .filter(attribute -> attribute.getKey().equalsIgnoreCase("paymentAmount"))
                .map(attribute -> attribute.getValue()).findAny().orElse(null);
        if (ObjectUtils.isNotEmpty(callback.getPayment()) && StringUtils.isEmpty(paymentAmount))
            throw new EntityValidationException("Payment Amount is not available for the order");
        if (ObjectUtils.isEmpty(callback.getPayment()) && StringUtils.isNotEmpty(paymentAmount))
            throw new EntityValidationException(
                    "Payment Amount is available for the order, its not present in the request");
        if (ObjectUtils.isNotEmpty(callback.getPayment()) && StringUtils.isNotEmpty(callback.getPayment().getAmount())
                && StringUtils.isNotEmpty(paymentAmount)) {
            BigDecimal callbackAmount = new BigDecimal(callback.getPayment().getAmount());
            BigDecimal paymentAmnt = new BigDecimal(paymentAmount);
            if (!(callbackAmount.compareTo(paymentAmnt) == 0))
                throw new EntityValidationException("Payment Amount is not matching with the order amount");
        }

    }

    protected void validateDepositAmount(List<OrderAttributeEntity> orderAttributeEntities, OrderCallBack callback) {
        var depositAmount = orderAttributeEntities.stream()
                .filter(attribute -> attribute.getKey().equalsIgnoreCase("depositAmount"))
                .map(attribute -> attribute.getValue()).findAny().orElse(null);
        if (ObjectUtils.isNotEmpty(callback.getDeposit()) && StringUtils.isEmpty(depositAmount))
            throw new EntityValidationException("Deposit Amount is not available for the order");
        if (ObjectUtils.isEmpty(callback.getDeposit()) && StringUtils.isNotEmpty(depositAmount))
            throw new EntityValidationException(
                    "Deposit Amount is available for the order, its not present in the request");
        if (ObjectUtils.isNotEmpty(callback.getDeposit())
                && StringUtils.isNotEmpty(callback.getDeposit().get(0).getDepositAmount())) {
            BigDecimal callbackAmount = new BigDecimal(callback.getDeposit().get(0).getDepositAmount());
            BigDecimal depositAmnt = new BigDecimal(depositAmount);
            if (!(callbackAmount.compareTo(depositAmnt) == 0))
                throw new EntityValidationException("Deposit Amount is not matching with the order amount");
        }
    }

    protected String getSubOrderId(String correlationId) {
        if (StringUtils.isEmpty(correlationId) || !correlationId.contains(":"))
            return null;
        var subOrderId = correlationId.split(":")[1];
        return subOrderId;
    }


    private OrderStageEntity getStageInfo(WaitingProcessInfoEntity waitingEventInfo) {
        var stageInfo = orderStageRepository.findStagebySubOrderIdAndName(Long.valueOf(waitingEventInfo.getSubOrderId()),
                waitingEventInfo.getStageCode());
        if (stageInfo == null)
            throw new EntityValidationException(StatusConstants.HttpConstants.UNPROCESSABLE_ENTITY,
                    "There is no wait events found for the activity: " + waitingEventInfo.getStageCode());
        return stageInfo;
    }

    private OrderStageEntity getStageInfoBySubOrderIdAndStageCode(String subOrderId, String stageCode) {
        var stageInfo = orderStageRepository.findInProgressStageBySubOrderIdAndStageCodePrefix(Long.valueOf(subOrderId), stageCode);
        if (stageInfo == null)
            throw new EntityValidationException(StatusConstants.HttpConstants.UNPROCESSABLE_ENTITY,
                    "No stages are waiting for the" + stageCode + " callback for the sub order id: " + subOrderId);
        return stageInfo;
    }

    private OrderStageEntity getStageInfoForMNP(String subOrderId) {
        var stageInfo = orderStageRepository.findInProgressStageBySubOrderIdAndStageCodePrefix(Long.valueOf(subOrderId), "MNP");
        if (stageInfo == null)
            throw new EntityValidationException(StatusConstants.HttpConstants.UNPROCESSABLE_ENTITY,
                    "No stages are waiting for the SOM callback for the sub order id: " + subOrderId);
        return stageInfo;
    }

    private OrderStageEntity getStageInfoForNG(String subOrderId) {
        var stageInfo = orderStageRepository.findInProgressStageBySubOrderIdAndStageCodePrefix(Long.valueOf(subOrderId), "NG");
        if (stageInfo == null)
            throw new EntityValidationException(StatusConstants.HttpConstants.UNPROCESSABLE_ENTITY,
                    "No stages are waiting for the NG callback for the sub order id: " + subOrderId);
        return stageInfo;
    }

    protected void validateCallback(OrderCallBack callBack, String eventToExecute) {
        if (StringUtils.isEmpty(callBack.getCorrelationId()) || !callBack.getCorrelationId().contains(":"))
            return;
        var incomingEvent = callBack.getCorrelationId().split(":")[0];
        if (!incomingEvent.equals(eventToExecute))
            throw new CommonException("Order is not waiting for the event : " + incomingEvent);
    }

    public WaitingProcessInfoEntity getWaitEventInfo(String orderId, String subOrderId) {
        WaitingProcessInfoEntity waitingProcessInfo = null;
        if (StringUtils.isNotEmpty(orderId) && StringUtils.isNotEmpty(subOrderId))
            waitingProcessInfo = waitingProcessInfoRepository.findProcessInfoByOrderIdAndSubOrderId(orderId, subOrderId,
                    Arrays.asList("Callback", "Timer"));
        else
            waitingProcessInfo = waitingProcessInfoRepository.findProcessInfoByOrderId(orderId, Arrays.asList("Callback", "Timer"));
        return waitingProcessInfo;
    }

    protected void setCallBackRequestToorderFlowContext(OrderFlowContext orderFlowContext, OrderCallBack callbackRequest) {
        Map<String, Object> callBackDetailMap = new HashMap<>();
        Map<String, Object> requestMap = new HashMap<>();
        List<Map<String, Object>> callBackDetailsList = null;
        OrderCallBack callback = null;
        try {
            callback = objectMapper.readValue(objectMapper.writeValueAsString(callbackRequest), OrderCallBack.class);
        } catch (JsonProcessingException e) {
            log.error("Exception while creating sub order context ", e);
            throw new WorkflowTaskFailedException("CreateInstancesDelegateOnboarding", "COM-005", "Unable to clone callback request");
        }
        callback.setWaitingEventInfo(null);
        callBackDetailMap.put("request", JsonUtils.jsonToObject(JsonUtils.toJsonString(callback)));
        requestMap.put("callBackRequest", callBackDetailMap);
        if (MapUtils.isNotEmpty(orderFlowContext.getWorkflowData())
                && ObjectUtils.isNotEmpty(orderFlowContext.getWorkflowData().get("callBackRequest"))) {
            callBackDetailsList = (List<Map<String, Object>>) (orderFlowContext.getWorkflowData()
                    .get(GenericConstants.CALL_BACK_REQUEST));
        } else {
            callBackDetailsList = new ArrayList<>();
        }
        callBackDetailsList.add(requestMap);
        orderFlowContext.getWorkflowData().put("callBackDetails", callBackDetailsList);
        orderFlowContext.getAttributes().put("newIMSI", callbackRequest.getNewIMSI());
        orderFlowContext.getAttributes().put("newICCID", callbackRequest.getNewICCID());
        orderFlowContext.getOrder().setDeviceSubscriptions(null);
        if (ObjectUtils.isNotEmpty(orderFlowContext.getWorkflowData())) {
            orderFlowContext.getWorkflowData().remove("serialNumbers");
        }
    }

    /*private void extractPaymentCallbackAttributes(OrderFlowContext orderFlowContext, OrderCallBack callbackRequest) {
        if (ObjectUtils.isNotEmpty(callbackRequest.getPayment())) {
            if (StringUtils.isNotEmpty(callbackRequest.getPayment().getTransactionId())) {
                orderFlowContext.getAttributes().put("transactionId", callbackRequest.getPayment().getTransactionId());
            }
            if (ObjectUtils.isNotEmpty(callbackRequest.getPayment().getPaymentDetail())) {
                var paymentDetailList = callbackRequest.getPayment().getPaymentDetail();
                var bankCodeObj = paymentDetailList.stream().filter(paymentDetailObj -> StringUtils.isNotEmpty(paymentDetailObj.getBankCode())).findAny().orElse(null);
                if (ObjectUtils.isNotEmpty(bankCodeObj))
                    orderFlowContext.getAttributes().put("bankCode", bankCodeObj.getBankCode());
            }
        }
    }*/

    protected Response createResponse(OrderCallBack request) {
        var response = new Response();
        createResponseBody(response, request);
        createDataset(response, request);
        return response;
    }

    private boolean shouldCreateDataSet(String entityId) {

        List<String> mvnoConfigValue = null;
        CacheTableDataDTO mvnoConfig = getDataFromCache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.DATASET_NOT_REQD_MVNOS.toString());
        if (mvnoConfig != null)
            mvnoConfigValue = Arrays.asList(mvnoConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString()).split(","));
        return (mvnoConfigValue != null && !mvnoConfigValue.contains(entityId));
    }

    public OrderEntity validateOrder(long orderId) {
        OrderEntity orderEntity = orderRepository.findProfileIdAndAccountIdByOrderId(orderId);
        if (ObjectUtils.isEmpty(orderEntity))
            throw new EntityValidationException(
                    "Invalid order id : " + orderId + ". Perhaps this order doesn't exist in the system");
        if (WorkFlowConstants.StageStatusConstants.STAGE_STATUS_CANCELLED.getValue().equalsIgnoreCase(orderEntity.getState()))
            throw new EntityValidationException(StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION,
                    "Order is already in Cancelled state");
        return orderEntity;
    }

    public void validatePaymentOrder(long orderId) {
        boolean orderStatus = orderStageRepository.findPaymentByOrderIdandStateReason(orderId);
        if (!orderStatus)
            throw new EntityValidationException(
                    "Invalid order id : " + orderId + ". The order may not exist in the system or is not waiting for payment callback");
    }

    public OrderFlowContext getOrderFlowContext(String processInstanceId) throws JsonProcessingException {
        OrderFlowContext orderFlowContext = objectMapper.readValue(
                runtimeService.getVariable(processInstanceId,
                        WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString()).toString(),
                OrderFlowContext.class);
        return orderFlowContext;
    }
}
