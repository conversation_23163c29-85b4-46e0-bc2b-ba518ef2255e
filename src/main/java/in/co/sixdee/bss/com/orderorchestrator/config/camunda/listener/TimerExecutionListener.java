package in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener;

import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.WaitingProcessInfoEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.WaitingProcessInfoRepository;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderStatusManager;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.core.AppInstanceIdManager;
import in.co.sixdee.bss.common.util.ApplicationProcessContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Log4j2
@RequiredArgsConstructor
public class TimerExecutionListener implements ExecutionListener {

    private final WaitingProcessInfoRepository waitingProcessInfoRepository;

    private final OrderStatusManager orderStatusManager;

    private final GetDataFromCache cache;

    private final AppInstanceIdManager appInstanceSequence;

    private ApplicationProcessContext processContext;

    private final ProcessDataAccessor processDataAccessor;


    @Override
    public void notify(DelegateExecution execution) {
        try {
            String subOrderId = null;
            var orderFlowContext = processDataAccessor.getOrderFlowContext(execution);
            processContext = new ApplicationProcessContext(appInstanceSequence.getInstanceId(), orderFlowContext.getTraceId(),
                    orderFlowContext.getRequestId(), orderFlowContext.getChannel(), orderFlowContext.getChannel(), orderFlowContext.getEntityId());
            processContext.setMdc();
            subOrderId = orderFlowContext.getAttributes().get(GenericConstants.SUB_ORDER_ID);
            var linkedActivityId = execution.getVariables().containsKey("linkedServiceTask")
                    ? execution.getVariable("linkedServiceTask").toString()
                    : execution.getCurrentActivityId();
            if (StringUtils.equals(ExecutionListener.EVENTNAME_START, execution.getEventName())) {
                var linkedStage = cache.getCacheDetailsFromDBMap("STAGE_CONFIG_BY_ACTIVITY_ID_AND_ORDER_TYPE",
                        orderFlowContext.getOrder().getOrderType() + "_" + linkedActivityId);
                var stageCode = "";
                if (ObjectUtils.isNotEmpty(linkedStage))
                    stageCode = linkedStage.getNgTableData().get("STAGE_ID");
                WaitingProcessInfoEntity waitingProcessInfoEntity = new WaitingProcessInfoEntity();
                waitingProcessInfoEntity.setOrderId(orderFlowContext.getOrder().getOrderId());
                waitingProcessInfoEntity.setSubOrderId(subOrderId);

                waitingProcessInfoEntity.setEventName(execution.getCurrentActivityName());
                waitingProcessInfoEntity.setWaitType("Timer");

                waitingProcessInfoEntity.setExecutionId(execution.getId());
                waitingProcessInfoEntity.setProcessInstanceId(execution.getProcessInstanceId());
                waitingProcessInfoEntity.setStageCode(stageCode);
                waitingProcessInfoRepository.save(waitingProcessInfoEntity);
                if (!execution.getVariables().containsKey("linkedServiceTask")) {
                    orderStatusManager.processStatusUpdates(orderFlowContext, linkedActivityId, "TIMER_EVENT", false, null);
                }
            } else if (StringUtils.equals(ExecutionListener.EVENTNAME_END, execution.getEventName())) {
                if (StringUtils.isNotEmpty(orderFlowContext.getOrder().getOrderId()) && StringUtils.isNotEmpty(subOrderId)) {
                    log.info("deleting the waiting process info for orderId : {} and subOrderId : {}",
                            orderFlowContext.getOrder().getOrderId(), subOrderId);
                    waitingProcessInfoRepository
                            .deletebyOrderIdSubOrderIdAndWaitType(orderFlowContext.getOrder().getOrderId(),subOrderId,"Timer");
                } else
                    log.info("no information available for the waiting process info entity id");
                orderStatusManager.processStatusUpdates(orderFlowContext, linkedActivityId, "TIMER_EVENT", true, null);
            }
        } catch (Exception e) {
            log.info(" Exception occurred in TimerExecutionListener.notify", e);
            throw e;
        }
    }

}