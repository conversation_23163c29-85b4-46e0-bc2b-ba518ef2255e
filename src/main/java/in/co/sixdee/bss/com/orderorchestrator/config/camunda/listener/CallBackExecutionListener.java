package in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.EntityValidationException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants;
import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.CallBackAuditInfoEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderPayloadEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderStageRepository;
import in.co.sixdee.bss.com.orderorchestrator.service.CallbackAuditService;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderPayloadService;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderStatusManager;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.core.AppInstanceIdManager;
import in.co.sixdee.bss.common.util.ApplicationProcessContext;
import in.co.sixdee.bss.common.util.NGTableColumnConstants;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.camunda.spin.plugin.variable.SpinValues;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Log4j2
@RequiredArgsConstructor
public class CallBackExecutionListener implements ExecutionListener {

    @Autowired
    protected ProcessDataAccessor processDataAccessor;

    @Autowired
    private AppInstanceIdManager appInstanceSequence;

    protected ApplicationProcessContext processContext;

    protected final CallbackAuditService callbackAuditService;

    private final ObjectMapper objectMapper;

    @Autowired
    private OrderStatusManager orderStatusManager;

    @Autowired
    private GetDataFromCache cache;

    private final RuntimeService runtimeService;

    private final OrderStageRepository orderStageRepository;

    @Value("${CallBackExecutionListenerEnabled}")
    private boolean listenerEnabled;

    @Override
    public void notify(DelegateExecution execution) {

        String orderId = null;
        String subOrderId = null;
        String linkedActivityId = null;
        OrderCallBack callback = null;
        String orderType=null;
        String executionStatus = "Completed";
        try {
            execution.setVariable("callBackProcessReqd", true);
            if(listenerEnabled) {
                var orderFlowContext = processDataAccessor.getOrderFlowContext(execution);
                execution.setVariable("callBackProcessReqd", true);
                log.info("Received order from the message queue :: {} ", objectMapper.writeValueAsString(orderFlowContext));
                orderId = orderFlowContext.getOrder().getOrderId();
                subOrderId = orderFlowContext.getAttributes().get(GenericConstants.SUB_ORDER_ID);
                orderType = orderFlowContext.getOrder().getOrderType();
                log.info("Order id {}  sub order id {} and execution.getInstanceId() {} execution.getId() {}", orderId, subOrderId, execution.getProcessInstanceId(), execution.getId());
                processContext = new ApplicationProcessContext(appInstanceSequence.getInstanceId(), orderFlowContext.getTraceId(),
                        orderFlowContext.getRequestId(), orderFlowContext.getChannel(), orderFlowContext.getChannel(), orderFlowContext.getEntityId());
                processContext.setMdc();
                if (StringUtils.equals(ExecutionListener.EVENTNAME_START, execution.getEventName())) {
                    String callBackType = getCallBackTypeFromFlow(execution);
                    CallBackAuditInfoEntity auditInfoEntity = callbackAuditService.getAuditDetailsForCallBack(GenericConstants.CALL_BACK_STATUS_NON_PROCESSED, orderId, subOrderId, callBackType);
                    if (ObjectUtils.isNotEmpty(auditInfoEntity)) {
                        log.info("call back for {} received as entry is present in the audit table..", callBackType);
                        callback = objectMapper.readValue(auditInfoEntity.getPayload(), OrderCallBack.class);
                        if (callback.getType() != null && callback.getType().toUpperCase().contains(WorkFlowConstants.OrderCallBackTypes.SIM_DELIVERY.desc)) {
                            setCallBackRequestToOrderFlowContext(orderFlowContext, callback);
                            var spinValue = SpinValues.jsonValue(objectMapper.writeValueAsString(orderFlowContext)).create();
                            execution.setVariable(WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString(), spinValue);
                        } else {
                            executionStatus = getExecutionStatusFromCallBack(callback);
                        }
                        String type = getTypeToFetchStageCode(callback.getCallbackType());
                        callback.setStageCode(getStageCode(type, orderType));
                        linkedActivityId = getActivityIdForTheCallBack(callback.getStageCode(), orderFlowContext);
                        log.info("type to fetch the stage code {} is {} with execution status {} and linked activity {}", callback.getStageCode(), type, executionStatus, linkedActivityId);
                        callbackAuditService.updateCallBackToProcessed(orderId, subOrderId, GenericConstants.CALL_BACK_STATUS_PROCESSED, callBackType);
                        if (executionStatus != null && executionStatus.equalsIgnoreCase("Completed")) {
                            log.info("call back received is a success callback");
                            orderStatusManager.processStatusUpdates(orderFlowContext, linkedActivityId, "WAIT_EVENT", true, null);
                        } else {
                            log.info("call back received is a failure callback");
                            OrderStageEntity stageInfo = validateStageInfoForFailureCallBack(callback);
                            if (ObjectUtils.isNotEmpty(stageInfo) && ObjectUtils.isNotEmpty(orderFlowContext))
                                orderStatusManager.UpdateCallBackOrderStatus(orderFlowContext, callback, stageInfo);
                        }
                        execution.setVariable("callBackProcessReqd", false);
                        log.info("is {} waiting required {}", callBackType, execution.getVariable("callBackProcessReqd"));
                    }
                }
            }else{
                log.info("call back audit info check is not required..Continuing the flow");
            }
        } catch (Exception e) {
            log.error(" Exception occurred in CallBackExecutionListener.notify", e);
        }
    }

    private OrderStageEntity validateStageInfoForFailureCallBack(OrderCallBack callback) {
        OrderStageEntity stageInfo = null;
        callback.setStatus("Failed");
        if (callback.getCorrelationId() != null && callback.getCorrelationId().startsWith("NG")) {
            stageInfo = getStageInfoBySubOrderIdAndStageCode(callback.getSubOrderId(), "NG");
        } else if (callback.getCorrelationId() != null && callback.getCorrelationId().startsWith("MNP")) {
            stageInfo = getStageInfoBySubOrderIdAndStageCode(callback.getSubOrderId(), "MNP");
        } else if (callback.getCorrelationId() != null && callback.getCorrelationId().startsWith("SOM")) {
            stageInfo = getStageInfoBySubOrderIdAndStageCode(callback.getSubOrderId(), "SOM");
        }
        if (stageInfo == null)
            throw new CommonException("Unable to get the stage info ");
        if (callback.isRollbacked()) {
            if (Boolean.parseBoolean(stageInfo.getRollbackStatus()))
                throw new EntityValidationException(StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION,
                        "stage : " + stageInfo.getStageCode() + " is already marked as rollback complete");
        }
        return stageInfo;
    }

    private OrderStageEntity getStageInfoBySubOrderIdAndStageCode(String subOrderId, String stageCode) {
        var stageInfo = orderStageRepository.findInProgressStageBySubOrderIdAndStageCodePrefix(Long.valueOf(subOrderId), stageCode);
        if (stageInfo == null)
            throw new EntityValidationException(StatusConstants.HttpConstants.UNPROCESSABLE_ENTITY,
                    "No stages are waiting for the" + stageCode + " callback for the sub order id: " + subOrderId);
        return stageInfo;
    }

    private String getExecutionStatusFromCallBack(OrderCallBack callback) {
        return callback.getStatus();
    }

    private String getTypeToFetchStageCode(String callbackType) {
        if(callbackType.startsWith("SIM")) {
            return "SIM";
        }
        else if(callbackType.startsWith("SOM")){
            return "SOM";
        }
        return null;
    }

    private String getCallBackTypeFromFlow(DelegateExecution execution) {
        String callBackType = null;
        Collection<CamundaProperty> camundaProperties = execution.getBpmnModelElementInstance().getExtensionElements().getElementsQuery()
                .filterByType(CamundaProperties.class).singleResult().getCamundaProperties();
        if (ObjectUtils.isNotEmpty(camundaProperties)) {
            for (CamundaProperty camProperty : camundaProperties) {
                log.info("name {} value {}",camProperty.getCamundaName(),camProperty.getCamundaValue());
                if (GenericConstants.CALLBACK_EXTENSION_PROPERTY_NAME.equalsIgnoreCase(camProperty.getCamundaName())) {
                    callBackType = camProperty.getCamundaValue();
                    break;
                }
            }
        }
        return callBackType;
    }

    private String getStageCode(String type, String orderType) {
        List<CacheTableDataDTO> stageConfigs = cache.getCacheDetailsFromDBMapAryList(NGTableConstants.CACHE_KEY_COM_ORDER_STAGE_CONFIG, orderType);
        if (ObjectUtils.isNotEmpty(stageConfigs)) {
            for (CacheTableDataDTO stageConfig : stageConfigs) {
                String stageId = stageConfig.getNgTableData().get(NGTableColumnConstants.COLUMN_STAGE_ID);
                if (stageId.startsWith(type)) {
                    log.info("Stage {} found for call back type {} ", stageId, type);
                    return stageId;
                }
            }
        }
        return null;
    }

    private String getActivityIdForTheCallBack(String stageCode, OrderFlowContext orderFlowContext) {

        var stageConfig = cache.getCacheDetailsFromDBMap("STAGE_CONFIG_BY_ORDER_TYPE_AND_STAGE_ID", orderFlowContext.getOrder().getOrderType() + "_" + stageCode);
        return stageConfig != null ? stageConfig.getNgTableData().get("LINKED_ACTIVITY") : null;
    }

    private void setCallBackRequestToOrderFlowContext(OrderFlowContext orderFlowContext, OrderCallBack callbackRequest) {
        Map<String, Object> callBackDetailMap = new HashMap<>();
        Map<String, Object> requestMap = new HashMap<>();
        List<Map<String, Object>> callBackDetailsList = null;
        var callback = SerializationUtils.clone(callbackRequest);
        callback.setWaitingEventInfo(null);
        callBackDetailMap.put("request", JsonUtils.jsonToObject(JsonUtils.toJsonString(callback)));
        requestMap.put("callBackRequest", callBackDetailMap);
        if (MapUtils.isNotEmpty(orderFlowContext.getWorkflowData())
                && ObjectUtils.isNotEmpty(orderFlowContext.getWorkflowData().get("callBackRequest"))) {
            callBackDetailsList = (List<Map<String, Object>>) (orderFlowContext.getWorkflowData()
                    .get(GenericConstants.CALL_BACK_REQUEST));
        } else {
            callBackDetailsList = new ArrayList<>();
        }
        callBackDetailsList.add(requestMap);
        orderFlowContext.getWorkflowData().put("callBackDetails", callBackDetailsList);
        orderFlowContext.getAttributes().put("newIMSI", callbackRequest.getNewIMSI());
        orderFlowContext.getAttributes().put("newICCID", callbackRequest.getNewICCID());
        orderFlowContext.getAttributes().put("callbacknewIMSI", callbackRequest.getNewIMSI());
        orderFlowContext.getOrder().setDeviceSubscriptions(null);
    }
}
