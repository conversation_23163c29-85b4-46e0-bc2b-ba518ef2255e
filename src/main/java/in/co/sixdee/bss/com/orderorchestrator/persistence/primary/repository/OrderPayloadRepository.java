package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderPayloadEntity;

@Repository
public interface OrderPayloadRepository extends JpaRepository<OrderPayloadEntity, Long> {

	@Query("select o.payload from OrderPayloadEntity o where o.correlationId = :correlationId")
	public String getPayloadDetails(@Param("correlationId") Long orderId);

	@Modifying(clearAutomatically = true)
	@Query("delete from OrderPayloadEntity o where o.correlationId = :correlationId")
	public void deletePayloadDetails(@Param("correlationId") Long orderId);

	@Modifying(clearAutomatically = true)
	@Query("UPDATE OrderPayloadEntity o SET o.payload = :payload where o.correlationId = :correlationId")
	public void updatePayloadDetails(@Param("payload") String payload, @Param("correlationId") Long orderId);

}
