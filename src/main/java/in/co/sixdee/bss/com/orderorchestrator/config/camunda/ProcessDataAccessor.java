package in.co.sixdee.bss.com.orderorchestrator.config.camunda;

import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.spin.plugin.variable.SpinValues;
import org.camunda.spin.plugin.variable.value.JsonValue;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.time.Instant;

@Configuration
@RequiredArgsConstructor
@Log4j2
public class ProcessDataAccessor {

	protected final ObjectMapper objectMapper;

	public OrderFlowContext getOrderFlowContext(DelegateExecution execution) {
		//var t1 = Instant.now();
		//try {
			JsonValue orderFlowContextJson = execution.getVariableTyped(WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString(), true);
			return orderFlowContextJson.getValue().mapTo(OrderFlowContext.class);
		//} finally {
			//log.info("Time taken in getting orderFlowContext {} ms", Duration.between(t1, Instant.now()).toMillis());
		//}

	}

	public boolean setOrderFlowContext(DelegateExecution execution, OrderFlowContext orderFlowContext) {
		//var t1 = Instant.now();
		try {
			execution.setVariable(WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString(), SpinValues.jsonValue(objectMapper.writeValueAsString(orderFlowContext)).create());
			return true;
		} catch (Exception e) {
			log.info("Exception while setting orderFlowContext to DelegateExecution", e);
			return false;
		}// finally {
			//log.info("Time taken in setting orderFlowContext  {} ms", Duration.between(t1, Instant.now()).toMillis());
		//}
	}

	public Service getExecutionData(DelegateExecution execution) {
		JsonValue executionDataJson = execution.getVariableTyped("executionData", true);
		return executionDataJson.getValue().mapTo(Service.class);
	}
}
