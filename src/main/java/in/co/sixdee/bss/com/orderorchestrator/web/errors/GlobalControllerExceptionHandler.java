package in.co.sixdee.bss.com.orderorchestrator.web.errors;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.*;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.MessageHandlingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import in.co.sixdee.bss.common.constants.ApiConstants;
import in.co.sixdee.bss.common.core.CommonErrorResponse;
import in.co.sixdee.bss.common.vaidation.RequestValidationException;
import lombok.extern.log4j.Log4j2;

/**
 * Handles all the exceptions.
 **/

@Order(Ordered.HIGHEST_PRECEDENCE)
@RestControllerAdvice
@Log4j2
public class GlobalControllerExceptionHandler extends ResponseEntityExceptionHandler {

	//
	// @Override
	// protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException
	// ex,
	// HttpHeaders headers, HttpStatus status, WebRequest request) {
	//
	// String transactionId = sequencer.getSequenceId();
	//
	// CommonExceptionResponse errorDetail = new
	// CommonExceptionResponse(HttpConstants.CUSTOM_FIELD_VALIDATION.getCode(),
	// ex.getBindingResult().getFieldError().getDefaultMessage(), transactionId);
	// if (log.isErrorEnabled())
	// log.error("Method Argument Not Valid : {}", errorDetail.toString());
	// return new ResponseEntity<Object>(errorDetail, HttpStatus.BAD_REQUEST);
	// }

	// @ExceptionHandler(InsufficientPrivilegesException.class)
	// @ResponseStatus(HttpStatus.UNAUTHORIZED)
	// public ResponseEntity<Object> handleInsufficientPrivilegesException(Throwable ex) {
	//
	// if (log.isErrorEnabled())
	// log.error("InsufficientPrivilegesException : {}", ex.toString());
	//
	// String transactionId = sequencer.getSequenceId();
	//
	// CommonExceptionResponse response = new
	// CommonExceptionResponse(HttpConstants.UNAUTHORIZED_STATUS_CODE.getCode(),
	// ex.getMessage(), transactionId);
	//
	// return new ResponseEntity<Object>(response, HttpStatus.UNAUTHORIZED);
	// }

	// @ExceptionHandler(DataException.class)
	// @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	// public ResponseEntity<Object> handleDataException(Throwable ex) {
	//
	// if (log.isErrorEnabled())
	// log.error("Runtime Exception error : {}", ex.toString());
	//
	// String transactionId = sequencer.getSequenceId();
	//
	// CommonExceptionResponse response = new
	// CommonExceptionResponse(HttpConstants.INTERNAL_SERVER_ERROR.getCode(),
	// MessageUtil.get("dms.runtime.exception.message"), transactionId);
	// ex.printStackTrace();
	// return new ResponseEntity<Object>(response, HttpStatus.INTERNAL_SERVER_ERROR);
	// }
	//
	// @ExceptionHandler(InvalidDeviceIdException.class)
	// @ResponseStatus(HttpStatus.UNAUTHORIZED)
	// protected ResponseEntity<Object> handleInvalidDeviceIdException(InvalidDeviceIdException ex)
	// {
	// if (log.isErrorEnabled())
	// log.error("InvalidDeviceIdException: {}", ex.getMessage());
	//
	// String transactionId = sequencer.getSequenceId();
	//
	// CommonExceptionResponse response = new CommonExceptionResponse(ex.getStatusCode(),
	// ex.getMessage(), transactionId);
	//
	// return new ResponseEntity<Object>(response, HttpStatus.UNAUTHORIZED);
	// }

	// @ExceptionHandler(AuthorizationHeaderException.class)
	// @ResponseStatus(HttpStatus.FORBIDDEN)
	// protected ResponseEntity<Object>
	// handleAuthorizationHeaderException(AuthorizationHeaderException ex) {
	// if (log.isErrorEnabled())
	// log.error("AuthorizationHeaderException: {}", ex.getMessage());
	//
	// String transactionId = sequencer.getSequenceId();
	//
	// CommonExceptionResponse response = new
	// CommonExceptionResponse(HttpConstants.FORBIDDEN_STATUS_CODE.getCode(),
	// ex.getMessage(), transactionId);
	//
	// return new ResponseEntity<Object>(response, HttpStatus.FORBIDDEN);
	// }

	@ExceptionHandler({ Exception.class })
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	public ResponseEntity<CommonErrorResponse> handlerExceptionResolver(Exception e) {
		var response = CommonErrorResponse.Builder.getInstance()
				// .setRequestId(MDC.get(ApiConstants.REQUEST_ID))
				// .setTraceId(MDC.get(ApiConstants.TRACE_ID))
				// .setTimestamp(MDC.get(ApiConstants.TIMESTAMP))
				.setCode(ApiConstants.HttpConstants.INTERNAL_SERVER_ERROR.getCodeAsString())
				.setStatus(ApiConstants.ResponseStatus.FAILURE.toString()).setMessage(e.getMessage()).build();

		return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
	}

	@ExceptionHandler({ MessageHandlingException.class })
	@ResponseStatus(HttpStatus.SERVICE_UNAVAILABLE)
	public ResponseEntity<CommonErrorResponse> handlerAmqpException(MessageHandlingException e) {
		var response = CommonErrorResponse.Builder.getInstance()
				// .setRequestId(MDC.get(ApiConstants.REQUEST_ID))
				// .setTraceId(MDC.get(ApiConstants.TRACE_ID))
				// .setTimestamp(MDC.get(ApiConstants.TIMESTAMP))
				.setCode(ApiConstants.HttpConstants.SERVICE_UNAVAILABLE.getCodeAsString())
				.setStatus(ApiConstants.ResponseStatus.FAILURE.toString()).setMessage(e.getMessage()).build();

		return new ResponseEntity<>(response, HttpStatus.SERVICE_UNAVAILABLE);
	}

	@ExceptionHandler({ PartialResultException.class })
	@ResponseStatus(HttpStatus.PARTIAL_CONTENT)
	public ResponseEntity<PartialErrorResponse> partialFailureResolver(PartialResultException e) {
		var response = PartialErrorResponse.Builder.getInstance()
				.setCode(ApiConstants.HttpConstants.PARTIAL_CONTENT.getCodeAsString())
				.setStatus(ApiConstants.ResponseStatus.PARTIAL_SUCCESS.toString()).setMessage(e.getMessage())
				.setOrders(e.getOrders()).build();

		return new ResponseEntity<>(response, HttpStatus.PARTIAL_CONTENT);
	}

	@ExceptionHandler({ BulkRetryInvalidOrderException.class })
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	public ResponseEntity<PartialErrorResponse> bulkRetryInvalidOrderException(BulkRetryInvalidOrderException e) {
		var response = PartialErrorResponse.Builder.getInstance()
				.setCode(ApiConstants.HttpConstants.INTERNAL_SERVER_ERROR.getCodeAsString())
				.setStatus(ApiConstants.ResponseStatus.FAILURE.toString()).setMessage(e.getMessage())
				.setOrders(e.getOrders()).build();

		return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
	}

	// @ExceptionHandler({ResourceNotFoundException.class})
	// @ResponseStatus(HttpStatus.NOT_FOUND)
	// public ResponseEntity<Object> handlerExceptionResolver(ResourceNotFoundException e) {
	//
	// if(log.isInfoEnabled())
	// log.info("Resource Not Found : {}", e);
	//
	// String transactionId = sequencer.getSequenceId();
	//
	// CommonExceptionResponse response = new
	// CommonExceptionResponse(HttpConstants.CUSTOM_FIELD_VALIDATION.getCode(),
	// e.getMessage(), transactionId);
	//
	// return new ResponseEntity<Object>(response, HttpStatus.NOT_FOUND);
	// }
	//
	//
	// @ExceptionHandler({BadCredentialsException.class})
	// @ResponseStatus(HttpStatus.UNAUTHORIZED)
	// public ResponseEntity<Object> handlerExceptionResolver(BadCredentialsException e) {
	//
	// if(log.isInfoEnabled())
	// log.info("Invalid username/Password : {}", e.getMessage());
	//
	// String transactionId = sequencer.getSequenceId();
	//
	// CommonExceptionResponse response = new
	// CommonExceptionResponse(HttpConstants.UNAUTHORIZED_STATUS_CODE.getCode(),
	// e.getMessage(), transactionId);
	// return new ResponseEntity<Object>(response, HttpStatus.UNAUTHORIZED);
	// }
	//
	// @ExceptionHandler({LockedException.class})
	// @ResponseStatus(HttpStatus.UNAUTHORIZED)
	// public ResponseEntity<Object> handlerExceptionResolver(LockedException e) {
	//
	// if(log.isInfoEnabled())
	// log.info("Account Blocked : {}", e.getMessage());
	//
	// String transactionId = sequencer.getSequenceId();
	//
	// CommonExceptionResponse response = new
	// CommonExceptionResponse(HttpConstants.FORBIDDEN_STATUS_CODE.getCode(),
	// e.getMessage(), transactionId);
	// return new ResponseEntity<Object>(response, HttpStatus.UNAUTHORIZED);
	// }

	@ExceptionHandler(value = { EntityValidationException.class })
	@ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
	public ResponseEntity<CommonErrorResponse> entityException(EntityValidationException ex) {
		int statusCode = (ex != null && ex.getStatus() != null) ? ex.getStatus().getCode()
				: ApiConstants.HttpConstants.UNPROCESSABLE_ENTITY.getCode();
		String message = ex != null && ex.getMessage() != null ? ex.getMessage()
				: ApiConstants.HttpConstants.UNPROCESSABLE_ENTITY.getCodeAsString();
		var response = CommonErrorResponse.Builder.getInstance()
				 .setRequestId(MDC.get(ApiConstants.TRACE_ID))
				 .setOrderId(MDC.get(ApiConstants.ORDER_ID))
				// .setTraceId(MDC.get(ApiConstants.TRACE_ID))
				// .setTimestamp(MDC.get(ApiConstants.TIMESTAMP))
				.setCode(ApiConstants.HttpConstants.UNPROCESSABLE_ENTITY.getCodeAsString())
				.setStatus(ApiConstants.ResponseStatus.FAILURE.toString()).setMessage(message).build();
		return new ResponseEntity<>(response, HttpStatus.UNPROCESSABLE_ENTITY);
	}

	@ExceptionHandler(value = { CommonException.class })
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	public ResponseEntity<CommonErrorResponse> commonException(CommonException ex) {
		int statusCode = (ex != null && ex.getStatus() != null) ? ex.getStatus().getCode()
				: ApiConstants.HttpConstants.INTERNAL_SERVER_ERROR.getCode();
		String message = ex != null && ex.getMessage() != null ? ex.getMessage()
				: ApiConstants.HttpConstants.INTERNAL_SERVER_ERROR.getCodeAsString();
		var response = CommonErrorResponse.Builder.getInstance()
				 .setRequestId(MDC.get(ApiConstants.TRACE_ID))
				 .setOrderId(MDC.get(ApiConstants.ORDER_ID))
				// .setTraceId(MDC.get(ApiConstants.TRACE_ID))
				// .setTimestamp(MDC.get(ApiConstants.TIMESTAMP))
				.setCode(String.valueOf(statusCode))
				.setStatus(ApiConstants.ResponseStatus.FAILURE.toString()).setMessage(message).build();
		return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
	}

	@ExceptionHandler(value = { FailedDependencyException.class })
	@ResponseStatus(HttpStatus.FAILED_DEPENDENCY)
	public ResponseEntity<CommonErrorResponse> failedDependencyException(FailedDependencyException ex) {
		int statusCode = (ex != null && ex.getStatus() != null) ? ex.getStatus().getCode()
				: StatusConstants.HttpConstants.FAILED_DEPENDENCY.getCode();
		String message = ex != null && ex.getMessage() != null ? ex.getMessage()
				: StatusConstants.HttpConstants.FAILED_DEPENDENCY.getCode().toString();
		var response = CommonErrorResponse.Builder.getInstance()
				.setRequestId(MDC.get(ApiConstants.TRACE_ID))
				.setOrderId(MDC.get(ApiConstants.ORDER_ID))
				.setCode(String.valueOf(statusCode))
				.setStatus(ApiConstants.ResponseStatus.FAILURE.toString()).setMessage(message).build();
		return new ResponseEntity<>(response, HttpStatus.FAILED_DEPENDENCY);
	}

	@ExceptionHandler({ RequestValidationException.class })
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	public ResponseEntity<CommonErrorResponse> handleRequestValidationException(RequestValidationException ex) {
		String message = ex != null && ex.getMessage() != null ? ex.getMessage()
				: ApiConstants.HttpConstants.CUSTOM_FIELD_VALIDATION.getCodeAsString();
		var response = CommonErrorResponse.Builder.getInstance()
				// .setRequestId(MDC.get(ApiConstants.REQUEST_ID))
				// .setTraceId(MDC.get(ApiConstants.TRACE_ID))
				// .setTimestamp(MDC.get(ApiConstants.TIMESTAMP))
				.setCode(ApiConstants.HttpConstants.CUSTOM_FIELD_VALIDATION.getCodeAsString())
				.setStatus(ApiConstants.ResponseStatus.FAILURE.toString()).setMessage(message)
				.setErrors(ex != null ? ex.getErrorDetails() : null).build();
		return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
	}

	// @ExceptionHandler({ SQLException.class })
	// @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	// public ResponseEntity<Object> sqlException(SQLException ex) {
	// int statusCode = HttpConstants.CUSTOM_FIELD_VALIDATION.getCode();
	// String message = MessageUtil.get("dms.runtime.exception.message");
	//
	// String transactionId = sequencer.getSequenceId();
	//
	// CommonExceptionResponse response = new CommonExceptionResponse(statusCode, message,
	// transactionId);
	//
	// log.error("CommonException : ResultCode-" + statusCode + " : Message : " + message);
	//
	// return new ResponseEntity<Object>(response, HttpStatus.INTERNAL_SERVER_ERROR);
	// }

	// @ExceptionHandler({ HttpMessageConversionException.class })
	// @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	// public ResponseEntity<Object> httpMessageConversionException(HttpMessageConversionException
	// ex) {
	// int statusCode = HttpConstants.CUSTOM_FIELD_VALIDATION.getCode();
	// String message = MessageUtil.get("dms.runtime.exception.message");
	//
	// String transactionId = sequencer.getSequenceId();
	//
	// CommonExceptionResponse response = new CommonExceptionResponse(statusCode, message,
	// transactionId);
	//
	// log.error("CommonException : ResultCode-" + statusCode + " : Message : " + message);
	//
	// return new ResponseEntity<Object>(response, HttpStatus.INTERNAL_SERVER_ERROR);
	// }

	// @ExceptionHandler({MaxUploadSizeExceededException.class})
	// @ResponseStatus(HttpStatus.BAD_REQUEST)
	// public ResponseEntity<Object> handleMaxSizeException(MaxUploadSizeExceededException e) {
	//
	// if(log.isInfoEnabled())
	// log.info("File upload size exceeded", e);
	//
	// String transactionId = sequencer.getSequenceId();
	// return new ResponseEntity<Object>(new
	// CommonExceptionResponse(HttpConstants.CUSTOM_FIELD_VALIDATION.getCode(),
	// "File upload size exceeded.", transactionId), HttpStatus.BAD_REQUEST);
	// }
	//
	// @ExceptionHandler({ NonUniqueResultException.class })
	// @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	// public ResponseEntity<Object> nonUniqueResultPersistanceExceptiom(NonUniqueResultException
	// ex) {
	// Integer statusCode = HttpConstants.INTERNAL_SERVER_ERROR.getCode();
	// String message = MessageUtil.get("dms.runtime.exception.message");
	//
	// String transactionId = sequencer.getSequenceId();
	//
	// CommonExceptionResponse response = new CommonExceptionResponse(statusCode, message,
	// transactionId);
	//
	// log.error("NonUniqueResultException : ResultCode-" + statusCode + " : Message : " +
	// message);
	//
	// return new ResponseEntity<Object>(response, HttpStatus.INTERNAL_SERVER_ERROR);
	// }
}
