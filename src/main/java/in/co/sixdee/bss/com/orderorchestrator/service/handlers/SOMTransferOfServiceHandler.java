package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Log4j2
@Component(value = "somTransferOfService")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class SOMTransferOfServiceHandler extends AbstractDelegate {

    protected String request = null;

    protected Order orderPayload = null;

    protected String msisdn = null;

    protected int index = 0;

    @Override
    protected void execute() throws Exception {

        try {
            orderPayload = executionContext.getOrder();
            request = createRequest();
            var callThirdPartyDTO = callThirdParty(request);
            if (callThirdPartyDTO == null) {
                executionContext.setError(true);
                return;
            }
            validateResponse(callThirdPartyDTO);
        } catch (Exception e) {
            log.error("{} In {} .execute Exception occurred ", executionContext.getTraceId(), this.getClass().getName(), e);
        }
    }

    private String createRequest() throws JsonProcessingException {
        var serviceOrder = new SOMServiceOrderDTO();
        setRequestBodyParams(serviceOrder);
        List<SOMServiceOrderDTO.ServiceOrderItem> serviceOrderItemList=createOrderItemForModify(serviceOrder);
        serviceOrder.setServiceOrderItem(serviceOrderItemList);
         return objectMapper.writeValueAsString(serviceOrder);
    }

    private List<SOMServiceOrderDTO.ServiceOrderItem> createOrderItemForModify(SOMServiceOrderDTO serviceOrder) {

        List<SOMServiceOrderDTO.ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
        List<SOMServiceOrderDTO.SOMService> somFetchServices = getSOMServiceRegistry();
        if(ObjectUtils.isNotEmpty(somFetchServices)) {
            for (SOMService somService : somFetchServices) {
                var serviceOrderItem = createServiceOrderItemFromFetch(somService);
                if (ObjectUtils.isNotEmpty(serviceOrderItem)) {
                    serviceOrderItemList.add(serviceOrderItem);
                }
            }
        }else{
            log.info("Som fetch registry response is empty...not able to form som tos request..");
        }
        return serviceOrderItemList;
    }

    private ServiceOrderItem createServiceOrderItemFromFetch(SOMService somService) {

        ServiceOrderItem serviceOrderItem = null;
        SOMService serviceItem = createServiceForServiceOrderItem(somService);
        if (ObjectUtils.isNotEmpty(serviceItem) && ObjectUtils.isNotEmpty(serviceItem.getServiceCharacteristic())) {
            serviceOrderItem= new ServiceOrderItem();
            index = index + 1;
            serviceOrderItem.setId(String.valueOf(index));
            serviceOrderItem.setAction("modifyUpdateSubscriptionId");
            serviceOrderItem.setType("ServiceOrderItem");
            serviceOrderItem.setService(serviceItem);
            return serviceOrderItem;
        }
        return serviceOrderItem;
    }

    private SOMService createServiceForServiceOrderItem(SOMService somService) {

        somService.setType(somService.getCategory());
        List<Characteristic> characteristics = somService.getServiceCharacteristic();
        somService.setServiceCharacteristic(createCharacteristic(characteristics));
        return somService;
    }

    private List<Characteristic> createCharacteristic(List<Characteristic> characteristics) {

        List<Characteristic> characteristicList = new ArrayList<>();
        for (Characteristic characteristic : characteristics) {
            if ("SUBSCRIPTION_ID".equalsIgnoreCase(characteristic.getName()) && StringUtils.isNotEmpty(characteristic.getValue())) {
                Characteristic subIdcharacteristic =createCharacteristicWithNewSubId(characteristic.getValue());
                if (ObjectUtils.isNotEmpty(subIdcharacteristic)) {
                    characteristicList.add(subIdcharacteristic);
                }
            }

        }
        return characteristicList;
    }

    private Characteristic createCharacteristicWithNewSubId(String oldSubscriptionId) {
        Characteristic characteristic = null;
        String newSubcriptionId = getNewSubscriptionId(oldSubscriptionId);
        if (ObjectUtils.isNotEmpty(newSubcriptionId)) {
            characteristic = new Characteristic();
            characteristic.setName("SUBSCRIPTION_ID");
            characteristic.setValueType("String");
            characteristic.setValue(newSubcriptionId);
            characteristic.setFinalValue(newSubcriptionId);
        }
        return characteristic;
    }

    private String getNewSubscriptionId(String oldSubscriptionId) {

        HashMap<String, String> subscriptionIdPlanIdMap = getSubscriptionIdPlanIdMap();
        String newSubId = null;
        if (subscriptionIdPlanIdMap != null && ObjectUtils.isNotEmpty(subscriptionIdPlanIdMap)) {
        for (Map.Entry<String, String> existingMapEntry : subscriptionIdPlanIdMap.entrySet()) {
            if (StringUtils.isNotEmpty(existingMapEntry.getKey()) && existingMapEntry.getKey().equalsIgnoreCase(oldSubscriptionId)) {
                newSubId = existingMapEntry.getValue();
                break;
            }
        }
    }
        return newSubId;
    }

    private HashMap<String, String> getSubscriptionIdPlanIdMap() {
        HashMap<String, String> subscriptionIdPlanIdMap = null;
        if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData().get("subscriptionIdPlanIdMap"))) {
            subscriptionIdPlanIdMap = (HashMap<String, String>) executionContext.getWorkflowData()
                    .get("subscriptionIdPlanIdMap");
            return subscriptionIdPlanIdMap;
        }
        return subscriptionIdPlanIdMap;
    }

    private List<SOMServiceOrderDTO.SOMService> getSOMServiceRegistry() {
        List<SOMServiceOrderDTO.SOMService> somFetchServices = new ArrayList<>();
        if (executionContext.getWorkflowData().containsKey("SOMFetchServiceRegistryResponseAttributes")) {
            somFetchServices = objectMapper.convertValue(
                    executionContext.getWorkflowData().get("SOMFetchServiceRegistryResponseAttributes"),
                    new TypeReference<List<SOMServiceOrderDTO.SOMService>>() {
                    });
        }
        if (ObjectUtils.isEmpty(somFetchServices)) {
            log.info(" No services found with the serviceId :: {}", msisdn);
            return null;
        }
        somFetchServices = somFetchServices.stream().filter(service -> service.getState().equalsIgnoreCase("active"))
                .collect(Collectors.toList());
        return somFetchServices;
    }

    private void setRequestBodyParams(SOMServiceOrderDTO serviceOrder) {

        msisdn = orderPayload.getServiceManagement().getServices().get(0).getMsisdn();
        serviceOrder.setExternalId(orderPayload.getOrderId());
        serviceOrder.setDescription(orderPayload.getDescription());
        serviceOrder.setOrderType("ChangePlan");
        serviceOrder.setRequestedStartDate(
                StringUtils.isNotEmpty(orderPayload.getRequestedStartDate()) ? orderPayload.getRequestedStartDate()
                        : Instant.now().toString());
        serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
        serviceOrder.setType("ServiceOrder");
        serviceOrder.setExternalServiceId(msisdn);
        serviceOrder.setRegistryId(executionContext.getEntityId());

    }
}
