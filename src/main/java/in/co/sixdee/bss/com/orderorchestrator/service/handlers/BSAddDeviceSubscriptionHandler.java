package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.Service;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "addBillingDeviceSubscription")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class BSAddDeviceSubscriptionHandler extends AbstractDelegate {

	private Order orderPayload = null;

	protected String request = null;

	protected final ObjectMapper objectMapper;

	@Override
	protected void execute() throws Exception {
		try {
			orderPayload = executionContext.getOrder();
			request = createDeviceSubscritionRequest();
			var callThirdPartyDTO = callThirdParty(request);
			if (callThirdPartyDTO == null) {
				executionContext.setError(true);
				return;
			}
			var response = callThirdPartyDTO.getResponse();
			validateResponse(callThirdPartyDTO);
			modifyWorkflowData(response);
		} catch (Exception e) {
			log.error("{} In {} .execute Exception occurred ", executionContext.getTraceId(), this.getClass().getName(),
					e);
		}
	}

	protected String createDeviceSubscritionRequest() throws Exception {
		List<Subscription> subscriptions = new ArrayList<>();
		var serviceOrder = new Service();
		if (StringUtils.isNotEmpty(executionContext.getAttributes().get("accountId")))
			serviceOrder.setAccountId(executionContext.getAttributes().get("accountId"));
		subscriptions = createSubscriptionOrderItem();
		serviceOrder.setSubscriptions(subscriptions);
		if (ObjectUtils.isNotEmpty(serviceOrder))
			request = objectMapper.writeValueAsString(serviceOrder);
		return request;
	}

	private List<Subscription> createSubscriptionOrderItem() {
		List<Subscription> subscriptionList = new ArrayList<>();
		int quantity = 1;
		try {
			if (ObjectUtils.isNotEmpty(orderPayload.getDeviceSubscriptions())) {
				List<Subscription> deviceSubscriptions = orderPayload.getDeviceSubscriptions();
				if (ObjectUtils.isNotEmpty(deviceSubscriptions)) {

					for (Subscription subscription : deviceSubscriptions) {
						if (StringUtils.isNotEmpty(subscription.getQuantity()))
							quantity = Integer.parseInt(subscription.getQuantity());

						for (int i = 1; i <= quantity; i++) {
							subscription.setCfss(null);
							subscription.setPrss(null);
							subscription.setLrss(null);
							subscriptionList.add(subscription);
						}
					}

				}
			}

		} catch (Exception e) {
			log.error(" | Exception occured in createSubscriptionOrderItem ", e);
		}

		return subscriptionList;
	}

}
