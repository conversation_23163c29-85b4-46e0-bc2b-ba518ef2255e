package in.co.sixdee.bss.com.orderorchestrator.model.mnp.portinnotification;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import lombok.Data;

import java.io.Serializable;

//@XmlRootElement(name = "PortInNotification")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class PortInNotification implements Serializable {

	private static final long serialVersionUID = 1L;

	@XmlElement(name = "requestID", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private RequestIdPortIn requestId;
	@XmlElement(name = "referenceID", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private ReferenceId referenceID;
	@XmlElement(name = "portingResult", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private PortingResult portingResult;
	@XmlElement(name = "donor", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private Donor donor;
	@XmlElement(name = "orderedApprovalTime", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private OrderedApprovalTime orderedApprovalTime;
	@XmlElement(name = "recommendTransferTime", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private RecommendTransferTime recommendTransferTime;
	@XmlElement(name = "serviceID", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private ServiceId serviceId;



}
