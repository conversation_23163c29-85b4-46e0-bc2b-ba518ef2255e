package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EnrichmentFailedException extends RuntimeException {

	private static final long serialVersionUID = 1L;

	private String code;


	public EnrichmentFailedException(String message, Throwable cause) {
		super(message, cause);
	}

	public EnrichmentFailedException(String errorCode, String message, Throwable cause) {
		super(message, cause);
		this.code = errorCode;
	}

	public EnrichmentFailedException(String errorCode, String message) {
		super(message);
		this.code = errorCode;
	}

	public EnrichmentFailedException(String msg) {
		super(msg);
	}


}
