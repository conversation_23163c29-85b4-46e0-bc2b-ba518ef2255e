package in.co.sixdee.bss.com.orderorchestrator.config.notification;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import in.co.sixdee.bss.com.orderorchestrator.config.util.Queue;
import in.co.sixdee.bss.om.model.dto.notification.NotificationDTO;

public class NotificationThreadPool {
	
	private Logger logger = LogManager.getLogger(NotificationThreadPool.class);

	private String name = null;
	private Queue<NotificationDTO> notificationQueue = null;
	private NotificationThread[] notificationThread = null;

	public NotificationThreadPool(Queue<NotificationDTO> notificationQueue, int noOfConnections, String name) {
		this.notificationQueue = notificationQueue;
		this.name = name;
		notificationThread = new NotificationThread[noOfConnections];
		startThreads(noOfConnections);
	}

	private void startThreads(int noOfConnections) {
		for (int i = 0; i < noOfConnections; i++) {
			notificationThread[i] = new NotificationThread(notificationQueue, i, this.name + "_" + i);
			notificationThread[i].start();
		}
	}

	public int getSize() {
		return this.notificationThread.length;
	}

	public void shutdown() {
		try {
			logger.info(" ********************************* Notification Thread is shutting down *********************");
			while (true) {
				if (notificationQueue.size() <= 0) {
					for (int i = 0; i < notificationThread.length; i++) {
						notificationThread[i].shutdown();
					}
					notificationQueue.shutdown();
					break;
				} else {
					Thread.sleep(1000);
				}
			}
		} catch (IllegalArgumentException | InterruptedException exception) {
			logger.error("An error has occurred: ", exception);
			Thread.currentThread().interrupt();
		}

	}

}
