package in.co.sixdee.bss.com.orderorchestrator.config.util;

import java.security.SecureRandom;

public class RandomGenerator {
    private static RandomGenerator instance;

    private SecureRandom secureRandom = null;

    private RandomGenerator(){
        secureRandom = new SecureRandom();
    }

    public static RandomGenerator getInstance() {
        if (instance == null) {
            instance = new RandomGenerator();
        }
        return instance;
    }


    public int nextRandom() {
       return 1000 + secureRandom.nextInt(9000);
    }

}
