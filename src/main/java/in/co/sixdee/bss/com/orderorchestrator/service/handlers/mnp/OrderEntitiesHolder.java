package in.co.sixdee.bss.com.orderorchestrator.service.handlers.mnp;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.*;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class OrderEntitiesHolder {

	private OrderEntity    orderEntityMaster;
	private List<SubOrderEntity> subOrders;
	private List<OrderStageEntity>					orderStages;


	public OrderEntitiesHolder() {
		subOrders = new ArrayList<>();
		orderStages = new ArrayList<>();
		
	}
}
