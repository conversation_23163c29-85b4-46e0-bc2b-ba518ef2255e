package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.WorkFlowUtil;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.WorkflowTaskFailedException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.AppConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants.WorkFlowProcessVariables;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.SubOrderRepository;
import in.co.sixdee.bss.com.orderorchestrator.service.ActivityExecutionEnd;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.connector.RestConnector;
import in.co.sixdee.bss.common.constants.GenericLogConstants;
import in.co.sixdee.bss.common.jolt.JoltUtils;
import in.co.sixdee.bss.common.util.CommonUtils;
import in.co.sixdee.bss.common.util.SequenceGenerator;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.esb.ESBResponse;
import in.co.sixdee.bss.om.model.dto.esb.ServiceCharacteristic;
import in.co.sixdee.bss.om.model.dto.order.Service;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.sm.SMResponse;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.text.StringSubstitutor;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
public abstract class AbstractDelegate implements JavaDelegate {

    protected Map<String, Object> executionVariables;
    protected String activityId;
    protected boolean isPartOfMultipleInstance;
    protected String reqSpecKey;
    protected String respSpecKey;
    protected String orderId;
    protected String subOrderId;
    protected String callbackCorrelationId;
    protected String callbackEvent;
    protected HashMap<String, String> thirdPartyCallDetails;
    protected HashMap<String, String> urlTokenConfigs;
    protected OrderFlowContext executionContext;
    protected DelegateExecution execution;
    protected String orderType;
    protected boolean workflowDataUpdated = false;
    protected Map<String, String> stageConfig;
    @Getter
    private boolean retry;

    protected in.co.sixdee.bss.common.dto.CallThirdPartyDTO callThirdPartyDTO;
    DocumentContext responseContext;

    @Autowired
    protected ProcessDataAccessor processDataAccessor;

    @Autowired
    protected JoltUtils joltUtils;

    @Autowired
    WorkFlowUtil workFlowUtil;

    @Autowired
    protected ObjectMapper objectMapper;

    @Autowired
    private RestConnector restConnector;

    DocumentContext orderFlowJsonPathContext = null;

    @Value("${flow.simulation:false}")
    private boolean flowSimulation;

    @Autowired
    protected ActivityExecutionEnd activityExecutionEnd;

    @Autowired
    protected SubOrderRepository subOrderRepository;

    @Autowired
    protected OrderRepository orderRepository;

    protected abstract void execute() throws Exception;

    @Autowired
    public GetDataFromCache cache;

    @Override
    public void execute(DelegateExecution execution) throws Exception {
        try {
            this.execution = execution;
            initHandler(execution);
            execute();
        } catch (Exception e) {
            log.error(":::::Exception occurred n {}.executeWorkItem ::::: ", this.getClass().getName(), e);
            if (e instanceof WorkflowTaskFailedException) {
				WorkflowTaskFailedException exception = (WorkflowTaskFailedException) e;
				handleError(exception.getFailureCode(), exception.getFailureDescription(), null, null);
			} else {
				executionContext.setError(true);
			}
        } finally {
            close();
        }
    }

    protected void close() {
        if ("NMSUpdateProvisioningStatus".equals(activityId) || flowSimulation)
            executionContext.setError(false);
        if (!executionContext.isError()) {
            if (StringUtils.isNotEmpty(executionContext.getRetryRequestId())) {
                executionContext.setRetryRequestId(null);
                execution.removeVariable("retryRequestId");
            }
        }
        activityExecutionEnd.closeActivity(execution, executionContext, GenericConstants.ACTIVITY_TYPE_SERVICE_TASK);
        if (workflowDataUpdated)
            processDataAccessor.setOrderFlowContext(execution, executionContext);
    }

    protected DocumentContext getOrderFlowJsonPathContext(OrderFlowContext orderFlowContext)
            throws JsonProcessingException {
        if (orderFlowJsonPathContext == null)
            orderFlowJsonPathContext = JsonPath.parse(objectMapper.writeValueAsString(orderFlowContext));
        return orderFlowJsonPathContext;
    }

    public void initHandler(DelegateExecution execution) throws Exception {
        executionVariables = execution.getVariables();
        initRetryVariable();
        executionContext = processDataAccessor.getOrderFlowContext(execution);
        executionContext.clearError();
        activityId = execution.getCurrentActivityId();
        orderType = executionContext.getOrder().getOrderType();
        var stageConfigDto = cache.getCacheDetailsFromDBMap(
                CacheConstants.CacheKeys.STAGE_CONFIG_BY_ACTIVITY_ID_AND_ORDER_TYPE.toString(),
                orderType + "_" + activityId);
        if (stageConfigDto == null)
            throw new ConfigurationNotValidException("Unable to find the stage configurations for activity "
                    + activityId + ". Please configure in COM_ORDER_STAGE_CONFIG table");
        stageConfig = stageConfigDto.getNgTableData();
        reqSpecKey = stageConfig.get(WorkFlowProcessVariables.REQ_SPEC_KEY.toString());
        orderId = String.valueOf(executionVariables.get("orderId"));
        subOrderId = executionContext.getAttributes().get(GenericConstants.SUB_ORDER_ID);
        callbackEvent = stageConfig.get(WorkFlowProcessVariables.CALLBACK_EVENT.toString());
        callbackCorrelationId = callbackEvent + ":" + subOrderId;
        executionContext.getAttributes().put("callbackCorrelationId", callbackCorrelationId);
        isPartOfMultipleInstance = BooleanUtils.toBoolean(
                (String) executionVariables.get(WorkFlowProcessVariables.IS_PART_OF_MULTI_INSTANCE.toString()));

        if (StringUtils.containsAnyIgnoreCase(activityId, "NCC", "ESB", "POS", "ERP", "OCS")) {
            if (ObjectUtils.isNotEmpty(execution.getVariable("retryRequestId")))
                executionContext.setRetryRequestId(execution.getVariable("retryRequestId").toString());
        }

    }

    private void initRetryVariable() {
        if (executionVariables.get("retry") != null && executionVariables.get("retry") instanceof Boolean) {
            retry = (Boolean) executionVariables.get("retry");
        } else {
            retry = false;
        }
    }

    protected Service getService() {
        Service service = null;
        LinkedHashMap<String, Object> currentExecution = (LinkedHashMap<String, Object>) executionContext
                .getWorkflowData().get("currentExecution");
        if (currentExecution != null) {
            try {
                service = objectMapper.readValue(objectMapper.writeValueAsString(currentExecution.get("executionData")),
                        Service.class);
            } catch (Exception e) {
                log.error("Exception occurred while getting service", e);
            }
        }
        return service;
    }

    protected void initThirdPartyCallDetails() throws CommonException, IllegalStateException {
        if (stageConfig.get(WorkFlowProcessVariables.THIRD_PARTY_ID.toString()) == null) {
            throw new IllegalStateException("third party id is not configured for the activity :: " + activityId);
        }
        var thirdPartyId = stageConfig.get(WorkFlowProcessVariables.THIRD_PARTY_ID.toString());
        var operationId = executionContext.getOrder().getOrderType();
        var thirdPartyUrlConfig = cache
                .getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(), thirdPartyId);
        if (Objects.isNull(thirdPartyUrlConfig))
            throw new ConfigurationNotValidException(
                    "url configuration not found for the third party id :: " + thirdPartyId);
        setupCallThirdPartyDto(thirdPartyUrlConfig);
        thirdPartyCallDetails = thirdPartyUrlConfig.getNgTableData();
        var urlTokenConfig = cache.getCacheDetailsFromDBMap(
                CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_TOKEN_CONFIG.name(), thirdPartyId + "_" + operationId);
        if (urlTokenConfig != null) {
            urlTokenConfigs = urlTokenConfig.getNgTableData();
        }
    }

    public void setupCallThirdPartyDto(CacheTableDataDTO thirdPartyUrlConfig) {
        callThirdPartyDTO = new in.co.sixdee.bss.common.dto.CallThirdPartyDTO();
        callThirdPartyDTO.setPermanentErrorCodes(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.PERMANENT_ERROR_CODES.name()));
        callThirdPartyDTO.setRespPayloadValidationRqd(BooleanUtils.toBoolean(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.PAYLOAD_VALIDATION.name())));
        callThirdPartyDTO.setThirdPartyId(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.THIRD_PARTY_ID.name()));
        callThirdPartyDTO.setUrl(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.URL.name()));
        callThirdPartyDTO.setReadTimeout(NumberUtils
                .toInt(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.READ_TIMEOUT.name()), 3000));
        callThirdPartyDTO.setConnTimeout(NumberUtils.toInt(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.CONNECTION_TIMEOUT.name()), 3000));
        callThirdPartyDTO.setExpectedHttpCode(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.EXPECTED_HTTP_CODE.name()));
        callThirdPartyDTO.setExpectedRespCode(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.EXPECTED_RESP_CODE.name()));
        callThirdPartyDTO.setHeaders(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.HEADERS.name()));
        callThirdPartyDTO.setTransportMethod(in.co.sixdee.bss.common.dto.CallThirdPartyDTO.TransportMethod
                .valueOf(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.TRANSPORT_METHOD.name())));
        callThirdPartyDTO.setResponseCodeJsonPath(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.RESP_STATUS_PATH.name()));
        callThirdPartyDTO.setResponseMessageJsonPath(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.RESP_STATUS_DESC_PATH.name()));
        callThirdPartyDTO.setThirdPartySystem(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.THIRD_PARTY_SYSTEM.name()));
    }

    protected void validateResponse(in.co.sixdee.bss.common.dto.CallThirdPartyDTO callThirdPartyDTO) {
        List<String> permanentErrorCodes = new ArrayList<>();
        if (StringUtils.isNotEmpty(callThirdPartyDTO.getPermanentErrorCodes())) {
            permanentErrorCodes = Arrays.asList(callThirdPartyDTO.getPermanentErrorCodes().split(","));
        }
        if (callThirdPartyDTO.getResponse() != null) {
            try {
                responseContext = JsonPath.parse(callThirdPartyDTO.getResponse());
            } catch (Exception e) {
                log.error("Exception while creating the response context {}", e.getMessage());
            }
        }
        if (!StringUtils.contains(callThirdPartyDTO.getExpectedHttpCode(), callThirdPartyDTO.getResponseCode())) {
            executionContext.setError(true);
            executionContext.getErrorDetail().setSystem(callThirdPartyDTO.getThirdPartySystem());
            executionContext.getErrorDetail().setCode(callThirdPartyDTO.getResponseCode());
            executionContext.getErrorDetail().setMessage(callThirdPartyDTO.getResponseMessage());
            extractErrorDesc(callThirdPartyDTO);
            if (ObjectUtils.isNotEmpty(permanentErrorCodes))
                executionContext
                        .setPermanentFailed(permanentErrorCodes.contains(executionContext.getErrorDetail().getCode()));

            return;
        }

        if (callThirdPartyDTO.isRespPayloadValidationRqd()) {
            validatePayload(callThirdPartyDTO);
        } else {
            log.info("Payload validation is not enabled for the third party :: {}",
                    callThirdPartyDTO.getThirdPartyId());
        }
        if (ObjectUtils.isNotEmpty(permanentErrorCodes))
            executionContext
                    .setPermanentFailed(permanentErrorCodes.contains(executionContext.getErrorDetail().getCode()));

    }

    private void extractErrorDesc(in.co.sixdee.bss.common.dto.CallThirdPartyDTO callThirdPartyDTO) {
        if (responseContext != null) {
            if (StringUtils.isNotEmpty(callThirdPartyDTO.getResponseCodeJsonPath())) {
                try {
                    executionContext.getErrorDetail()
                            .setCode(responseContext.read(callThirdPartyDTO.getResponseCodeJsonPath()) + "");
                } catch (Exception e) {
                    log.warn("error while parsing response status :: {}", e.getMessage());
                }
            }
            if (StringUtils.isNotEmpty(callThirdPartyDTO.getResponseMessageJsonPath())) {
                try {
                    var errorMessage = responseContext.read(callThirdPartyDTO.getResponseMessageJsonPath()) + "";
                    errorMessage = modifyStateReasonLength(errorMessage);
                    executionContext.getErrorDetail().setMessage(errorMessage);
                } catch (Exception e) {
                    log.warn("error while parsing response status description :: {}", e.getMessage());
                }
            }
        }
    }

    private void validatePayload(in.co.sixdee.bss.common.dto.CallThirdPartyDTO callThirdPartyDTO) {
        if (responseContext != null) {
            try {
                var statusCode="";
                var statusDesc="";
                if (StringUtils.isNotEmpty(callThirdPartyDTO.getThirdPartySystem()) && GenericConstants.ARM_THIRD_PARTY_SYSTEM_NAME.equalsIgnoreCase(callThirdPartyDTO.getThirdPartySystem())) {
                    try {
                        String typeOfResponse = responseContext.read(callThirdPartyDTO.getResponseMessageJsonPath()) + "";
                        if (GenericConstants.ARM_FAILURE_MESSAGE.equalsIgnoreCase(typeOfResponse)) {
                            statusCode = responseContext.read(callThirdPartyDTO.getResponseCodeJsonPath()) + "";
                            statusDesc = responseContext.read("$.errors.[0].errorDescription") + "";
                        } else {
                            statusCode = "ARM-001";
                            statusDesc = "Success";
                        }
                    } catch (Exception e) {
                        log.error("Exception occurred", e);
                    }
                } else {
                    statusCode = responseContext.read(callThirdPartyDTO.getResponseCodeJsonPath()) + "";
                    statusDesc = responseContext.read(callThirdPartyDTO.getResponseMessageJsonPath()) + "";
                }
                if (!StringUtils.contains(callThirdPartyDTO.getExpectedRespCode(), statusCode)) {
                    callThirdPartyDTO.setResponseMessage(statusDesc);
                    executionContext.setError(true);
                    executionContext.getErrorDetail().setCode(statusCode);
                    statusDesc = modifyStateReasonLength(statusDesc);
                    executionContext.getErrorDetail().setMessage(statusDesc);
                    executionContext.getErrorDetail().setSystem(callThirdPartyDTO.getThirdPartySystem());
                }
            } catch (Exception e) {
                log.error("Exception occurred", e);
            }
        } else {
            executionContext.setError(true);
            String system = callThirdPartyDTO.getThirdPartySystem();
            executionContext.getErrorDetail().setCode("500");
            executionContext.getErrorDetail().setMessage("Response received from " + system + " is null or empty");
            executionContext.getErrorDetail().setSystem(callThirdPartyDTO.getThirdPartySystem());
        }
    }

    public void processThirdPartyResponse() {
        String responseAttributes = stageConfig.get(WorkFlowProcessVariables.RESPONSE_ATTRIBUTES.toString());
        if (responseContext == null || !CommonUtils.INSTANCE.validateField(responseAttributes)) {
            return; // Early return for invalid inputs
        }

        String[] keys = responseAttributes.split(",");
        for (String keyPath : keys) {
            String keyName, kPath;
            String[] val = keyPath.split("=");
            if (val.length > 1) {
                keyName = val[0];
                kPath = val[1];
            } else {
                keyName = val[0].substring(keyPath.lastIndexOf(".") + 1);
                kPath = val[0];
            }
            try {
                processKey(kPath, keyName);
            } catch (Exception e) {
                log.error("Exception occurred while processing key {}: {}", keyName, e.getMessage());
            }
        }
        workflowDataUpdated = true;
    }

    private void processKey(String kPath, String keyName) {
        var jsonPathVal = responseContext.read(kPath);
        if (jsonPathVal instanceof String || jsonPathVal instanceof Integer) {
            executionContext.getAttributes().put(keyName, String.valueOf(jsonPathVal));
        } else {
            log.info("Ignoring response attribute {} as it is not a primitive", keyName);
        }
    }

    public Map<String, String> getHeaderMap(String headerParams) {
        Map<String, String> headerMap = null;
        try {
            if (StringUtils.isNotEmpty(headerParams)) {
                headerMap = new HashMap<String, String>();
                String[] headerParam = headerParams.split(",");
                for (String header : headerParam) {
                    if (header.contains("=")) {
                        String name = null, value = null;
                        try {
                            name = header.split("=")[0];
                            value = header.split("=")[1];
                            headerMap.put(name, value);
                        } catch (Exception e) {
                            throw e;
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw e;
        }
        return headerMap;
    }

    protected Map<String, String> transformHeaders(Map<String, String> headerMap) {
        Map<String, String> newMap = new HashMap<>();

        try {
            DocumentContext docContext = getOrderFlowJsonPathContext(executionContext);

            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                if (value.startsWith("@(") && value.endsWith(")")) {
                    String path = value.substring(2, value.length() - 1);
                    processHeaderValueFromJsonPath(docContext, newMap, key, path);
                } else if (value.startsWith("{") && value.endsWith("}")) {
                    processHeaderValueFromAttributes(newMap, key, value);
                } else if (value.startsWith("date(") && value.endsWith(")")) {
                    processDateHeaderValue(newMap, key, value);
                } else if (value.startsWith("generateUniqueId(") && value.endsWith(")")) {
                    newMap.put(key, String.valueOf(SequenceGenerator.getSequencerInstance().nextId()));
                } else if (value.startsWith("esbRequestId(") && value.endsWith(")")) {
                    processEsbRequestId(newMap, key);
                } else {
                    newMap.put(key, value);
                }
            }

        } catch (Exception e) {
            log.info(GenericLogConstants.TAG_APP + "{} | Exception occurred in transformHeaders: ",
                    executionContext.getTraceId(), e);
        }

        return newMap;
    }

    private void processHeaderValueFromJsonPath(DocumentContext docContext, Map<String, String> newMap, String key,
                                                String path) {
        try {
            String value = docContext.read(path);
            if (ObjectUtils.isNotEmpty(value))
                newMap.put(key, value);
        } catch (PathNotFoundException e) {
            log.warn("Path {} not found!!!", path);
        }
    }

    private void processHeaderValueFromAttributes(Map<String, String> newMap, String key, String value) {
        String attributeName = value.substring(1, value.length() - 1);
        String attributeValue = executionContext.getAttributes().get(attributeName);
        if (attributeValue != null)
            newMap.put(key, attributeValue);
    }

    private void processDateHeaderValue(Map<String, String> newMap, String key, String value) {
        String timestamp = null;
        String format = value.substring(5, value.length() - 1);
        if (StringUtils.isNotEmpty(format)) {
            if (StringUtils.equalsIgnoreCase(format, "epoch")) {
                timestamp = String.valueOf(Instant.now().getEpochSecond());
            } else {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
                timestamp = LocalDateTime.now().format(formatter);
            }
            newMap.put(key, timestamp);
        }
    }

    private void processEsbRequestId(Map<String, String> newMap, String key) {
        newMap.put(key, executionContext.getTraceId() + "_"
                + String.valueOf(SequenceGenerator.getSequencerInstance().nextId()));
    }

    protected Param resolveUriTokens(String tokens) {
        if (tokens == null)
            return new Param();
        DocumentContext context = null;
        try {
            context = tokens.contains("$") ? getOrderFlowJsonPathContext(executionContext) : null;
        } catch (Exception e) {
            log.info("Exception occurred in resolveUriTokens: {}", e.getMessage());
            return new Param();
        }
        var tokenArray = tokens.split("\\|");
        var uriParams = new Param();
        for (var token : tokenArray) {
            var param = token.split("=", 2);
            if (param.length > 1) {
                var key = param[0];
                var value = param[1];
                if (value.startsWith("$")) {
                    resolveJsonPath(context, key, value, uriParams);
                } else if (value.startsWith("val")) {
                    resolveValExpression(key, value, uriParams);
                } else {
                    uriParams.put(key, value);
                }
            }
        }
        return uriParams;
    }

    private void resolveJsonPath(DocumentContext context, String key, String path, Param uriParams) {
        if (context == null || path == null || uriParams == null)
            return;
        var value = context.read(path);
        if (value instanceof String || value instanceof Integer || value instanceof Long) {
            uriParams.put(key, String.valueOf(value));
        } else if (value instanceof List<?>) {
            uriParams.put(key, String.valueOf(value).replace("[", "").replace("]", "").replaceAll("\\s", ""));
        } else {
            log.info("ignoring token {}, as it is not a primitive", key);
        }
    }

    protected void resolveValExpression(String key, String path, Param uriParams) {
        if (path == null || uriParams == null)
            return;
        var valPath = path.substring(path.indexOf("(") + 1, path.indexOf(")"));
        if (executionContext.getAttributes().containsKey(valPath)) {
            uriParams.put(key, executionContext.getAttributes().get(valPath));
        }
    }

    protected String formatUri(String uri, Param params) {
        return new StringSubstitutor(params).replace(uri);
    }

    protected in.co.sixdee.bss.common.dto.CallThirdPartyDTO callThirdParty(String request) {
        initThirdPartyCallDetails();
        callThirdPartyDTO.setRequest(request);
        var headerMap = getHeaderMap(callThirdPartyDTO.getHeaders());
        if (headerMap != null)
            callThirdPartyDTO.setHeaderMap(transformHeaders(headerMap));
        var uri = callThirdPartyDTO.getUrl();
        if (urlTokenConfigs != null) {
            uri = formatUri(uri, resolveUriTokens(urlTokenConfigs.get(AppConstants.CacheFields.TOKENS.name())));
            uri = setQueryParams(uri);
            callThirdPartyDTO.setUrl(uri);
        }
        try {
            callThirdPartyDTO.setUri(new URI(uri.trim()));
        } catch (URISyntaxException e) {
            throw new CommonException(null, e.getMessage());
        }
        restConnector.service(callThirdPartyDTO);
        executionContext.setTimeTakenForThirdParty(
                executionContext.getTimeTakenForThirdParty() + callThirdPartyDTO.getTimeTaken());
        return callThirdPartyDTO;
    }

    protected String setQueryParams(String uri) {
        var queryParams = thirdPartyCallDetails.get(CacheConstants.CacheFields.QUERY_PARAMS.name());
        if (StringUtils.isNotEmpty(queryParams)) {
            var params = resolveUriTokens(queryParams);
            if (MapUtils.isNotEmpty(params)) {
                var param = params.keySet().stream().map(key -> key + "=" + params.get(key))
                        .collect(Collectors.joining("&"));
                uri = StringUtils.join(uri, param);
            }
        }
        return uri;
    }

    protected void modifyWorkflowData(String response) throws Exception {

        String responseAttributesJson = null;
        if (StringUtils.isNotEmpty(response) && in.co.sixdee.bss.common.util.JsonUtils.isJson(response)) {
            respSpecKey = stageConfig.get(WorkFlowProcessVariables.RESP_SPEC_KEY.toString());
            if (!CommonUtils.INSTANCE.validateField(respSpecKey))
                return;
            responseAttributesJson = "all".equalsIgnoreCase(respSpecKey) ? response
                    : joltUtils.convert(respSpecKey, orderType, JsonUtils.jsonToObject(response), null);
            if (responseAttributesJson != null) {
                if (isPartOfMultipleInstance) {
                    LinkedHashMap<String, Object> executionDataMap = (LinkedHashMap<String, Object>) executionContext
                            .getWorkflowData().get(WorkFlowProcessVariables.CURRENT_EXECUTION.toString());
                    executionDataMap.put(activityId + "ResponseAttributes",
                            JsonUtils.jsonToObject(responseAttributesJson));
                } else {
                    executionContext.getWorkflowData().put(activityId + "ResponseAttributes",
                            JsonUtils.jsonToObject(responseAttributesJson));
                }
                workflowDataUpdated = true;
            }
        } else {
            log.info("unable to extract response attributes as the response is not a valid json");
        }

    }

    static class Param extends HashMap<String, String> {
        /**
         *
         */
        private static final long serialVersionUID = 1L;

        @Override
        public String get(Object key) {
            if (!super.containsKey(key)) {
                super.put(key.toString(), "");
            }
            return super.getOrDefault(key, "t");
        }

        @Override
        public boolean containsKey(Object arg0) {
            return true;
        }
    }

    protected void extractSubscriptionIds(String response) {
        HashMap<String, String> subscriptionIdMap = getSubscriptionIdMap();

        try {
            if (GenericConstants.NCC_CREATE_SERVICE_HANDLER.equals(activityId)
                    || GenericConstants.NCC_CREATE_SUBSCRIPTION_HANDLER.equals(activityId)
                    || GenericConstants.NCC_CREATE_WELCOME_SUBSCRIPTION.equals(activityId)
                    || GenericConstants.NCC_CONNECTION_MIGRATION.equals(activityId)) {

                ESBResponse esbResponse = objectMapper.readValue(response, ESBResponse.class);
                addSubscriptionIdsFromESBResponse(subscriptionIdMap, esbResponse);

            } else if (GenericConstants.SMCreateSubscription.equals(activityId)) {

                SMResponse smResponse = objectMapper.readValue(response, SMResponse.class);
                addSubscriptionIdsFromSMResponse(subscriptionIdMap, smResponse);

            } else if (StringUtils.equalsAnyIgnoreCase(activityId, "BSAddSubscription", "BSChangeSubscription","BSAddSubscriptionMVNO")) {

                JsonNode billingResponse = objectMapper.readTree(response);
                addSubscriptionIdsFromBilling(subscriptionIdMap, billingResponse);
            }
        } catch (JsonProcessingException e) {
            log.error("Exception occurred while extracting subscription ids", e.getMessage());
            return;
        }

        executionContext.getWorkflowData().put("subscriptionIdMap", subscriptionIdMap);
        workflowDataUpdated = true;
    }

    private HashMap<String, String> getSubscriptionIdMap() {
        HashMap<String, String> subscriptionIdMap = executionContext.getWorkflowData().get("subscriptionIdMap") != null
                ? (HashMap<String, String>) executionContext.getWorkflowData().get("subscriptionIdMap")
                : new HashMap<>();
        return subscriptionIdMap;
    }

    private void addSubscriptionIdsFromESBResponse(HashMap<String, String> subscriptionIdMap, ESBResponse esbResponse) {
        List<ServiceCharacteristic> serviceCharacteristics = esbResponse.getServiceCharacteristic();
        if (serviceCharacteristics != null) {
            for (ServiceCharacteristic characteristic : serviceCharacteristics) {
                if (GenericConstants.ESB_CHARACTERISTIC_TYPE_SUBSCRIPTION_ID.equals(characteristic.getType())) {
                    subscriptionIdMap.put(characteristic.getName(), characteristic.getValue());
                }
            }
        }
    }

    private void addSubscriptionIdsFromSMResponse(HashMap<String, String> subscriptionIdMap, SMResponse smResponse) {
        if (smResponse.getResultParam().getSubscriptionId() != null
                && smResponse.getResultParam().getPlanId() != null) {
            subscriptionIdMap.put(smResponse.getResultParam().getPlanId(),
                    smResponse.getResultParam().getSubscriptionId());
        }
    }

    private void addSubscriptionIdsFromBilling(HashMap<String, String> subscriptionIdMap, JsonNode json) {
        JsonNode responseNode = json.get("response");
        if (responseNode != null) {
            JsonNode subscriptionsNode = responseNode.get("subscriptions");
            if (subscriptionsNode != null) {
                subscriptionsNode
                        .forEach(subscriptionDetail -> subscriptionIdMap.put(subscriptionDetail.get("planId").asText(),
                                subscriptionDetail.get("subscriptionId").asText()));
            }

            JsonNode subscriptionIdNode = responseNode.get("subscriptionId");
            JsonNode planIdNode = responseNode.get("planId");
            if (subscriptionIdNode != null && planIdNode != null) {
                subscriptionIdMap.put(planIdNode.asText(), subscriptionIdNode.asText());
            }
        }
    }

    private String modifyStateReasonLength(String stateReason) {
        if (StringUtils.isNotEmpty(stateReason)) {
            int stateReasonLen = 200;
            var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                    CacheConstants.CacheFields.ORDER_ERROR_MESSAGE_LENGTH.name());
            if (ObjectUtils.isNotEmpty(appConfig))
                stateReasonLen = Integer
                        .parseInt(appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()));
            if (stateReason.length() > stateReasonLen)
                stateReason = stateReason.substring(0, stateReasonLen);
        }
        return stateReason;

    }

    protected void updateSOMCancelSubProcessVariables(String response) throws JsonProcessingException {

        if (StringUtils.equalsAnyIgnoreCase(orderType, OrderTypes.CANCEL_SUBSCRIPTION)
                && StringUtils.equalsAnyIgnoreCase(activityId, GenericConstants.SOM_FETCH_SERVICE_REGISTRY)) {
            var somCallReq = updateCancelSubProcessVariables(response);
            execution.setVariable("somCancelSubReq", somCallReq);
        }
    }

    protected void checkViewSubscriptionResponseEmptyOrNot(String response) throws JsonProcessingException {
        if (StringUtils.equalsAnyIgnoreCase(activityId, GenericConstants.BS_VIEW_GROUP_SUBSCRIPTION)) {
            if (ObjectUtils.isNotEmpty(response)) {
                JsonNode billingResponse = objectMapper.readTree(response);
                if (ObjectUtils.isNotEmpty(billingResponse) && billingResponse.get("data") != null
                        && billingResponse.get("data").get(0) != null) {
                    execution.setVariable("subscriptionCallReq", true);
                } else {
                    execution.setVariable("subscriptionCallReq", false);
                }
            }
        }

    }

    protected void getNMSProcessVariable(String response) throws JsonProcessingException {
        List<String> statusidList = null;
        if (StringUtils.equalsAnyIgnoreCase(orderType, OrderTypes.ONBOARDING, OrderTypes.ADD_SERVICE,
                OrderTypes.ADD_SERVICE_TO_NEW_ACCOUNT)
                && StringUtils.equalsAnyIgnoreCase(activityId, GenericConstants.NMS_FETCH_ASSET)) {

            if ((!response.isBlank()) && StringUtils.isNotEmpty(response)
                    && in.co.sixdee.bss.common.util.JsonUtils.isJson(response)) {
                if (cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                        "NMS_CONFIGURED_STATUS_IDS") != null
                        && cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                        "NMS_CONFIGURED_STATUS_IDS").getNgTableData().get("CONFIG_VALUE") != null) {
                    statusidList = Arrays
                            .asList(cache
                                    .getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                                            "NMS_CONFIGURED_STATUS_IDS")
                                    .getNgTableData().get("CONFIG_VALUE").split(","));
                }
                var docContext = JsonPath.parse(response);
                if (docContext != null) {
                    try {
                        int statusId = docContext.read("$.data.respData[0].statusId");
                        if ((statusidList != null && !statusidList.isEmpty()) && StringUtils.isNotEmpty(String.valueOf(statusId))
                                && statusidList.contains(String.valueOf(statusId))) {
                            execution.setVariable("nmsUpdateCallReqd", true);

                        }
                    } catch (Exception e) {
                        // TODO: handle exception
                        log.error("No status details present:", e.getMessage());
                    }
                }
            }

        }

    }

    protected void updateNGWProcessVariables(String response) throws JsonProcessingException {
        var deviceId = getDeviceDetails(response);
        execution.setVariable("deviceId", ObjectUtils.isNotEmpty(deviceId));
    }

    private String getDeviceDetails(String response) {
        // TODO Auto-generated method stub
        LinkedHashMap<String, String> attrMap = new LinkedHashMap<>();
        LinkedHashMap<String, String> valueMap = new LinkedHashMap<>();
        String deviceId = null;
        try {

            attrMap.put("deviceId", "$.deviceDetails[0].deviceId");
            attrMap.put("seqId", "$.deviceDetails[0].seqId");

            var docContext = JsonPath.parse(response);
            // deviceId= docContext.read("$.deviceDetails[0].deviceId");
            int seqId = docContext.read("$.deviceDetails[0].seqId");
            // String deviceType= docContext.read("$.deviceDetails[0].deviceType");
            // valueMap.put("deviceId", deviceId);
            // valueMap.put("deviceType", deviceType);
            valueMap.put("seqId", String.valueOf(seqId));
            deviceId = String.valueOf(seqId);
        } catch (Exception e) {
            // TODO: handle exception
            log.info("No device details present:", e.getMessage());
        }

        return deviceId;
    }

    private boolean updateCancelSubProcessVariables(String response) throws JsonProcessingException {
        List<SOMService> somServices = objectMapper.readValue(response, new TypeReference<List<SOMService>>() {
        });
        var subIdCharacteristic = Objects
                .requireNonNull(executionContext.getOrder().getOrderItem().get(0).getItemCharacteristic().stream()
                        .filter(item -> StringUtils.equalsAnyIgnoreCase(item.getName(), "SUBSCRIPTION_ID")).findFirst()
                        .orElse(null));

        if (ObjectUtils.isNotEmpty(somServices)) {
            return somServices.stream()
                    .anyMatch(service -> ObjectUtils.isNotEmpty(service.getServiceCharacteristic())
                            && service.getServiceCharacteristic().stream()
                            .anyMatch(ch -> ch.getName().equalsIgnoreCase("SUBSCRIPTION_ID")
                                    && ch.getValue().equalsIgnoreCase(subIdCharacteristic.getValue())));
        }
        return false;
    }

    protected boolean checkTOSPendingOrders(String serviceId, String profileId) {
        boolean ordersPresent = false;
        ArrayList<String> orderTypes = new ArrayList<>();
        try {
            var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                    "TOS_PENDING_CHECK_ORDER_TYPES");
            if (appConfig != null) {
                orderTypes = new ArrayList<>(Arrays.asList(
                        appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()).split(",")));
            }
        } catch (Exception e) {
            log.info("config conversion catch");
        }
        if (orderTypes.isEmpty()) {
            orderTypes.add(OrderTypes.TRANSFER_OF_SERVICE);
        }
        // log.info("serviceId fetch:{}",serviceId);
        // log.info("Orders by Service
        // ID{}:{}",serviceId,subOrderRepository.getOrderIdsCountByServiceId(serviceId));
        // log.info("Orders by Service
        // ID{}:{}",serviceId,subOrderRepository.getOrderIdsCountByServiceIdAndState(serviceId));
        // log.info("Failed orders by Service
        // ID:{}",subOrderRepository.isAnyOrderIdsByServiceId(serviceId));
        // log.info("Failed orders by Service
        // ID:{}",subOrderRepository.isAnyOrderIdsByServiceId(serviceId));
        log.info("Failed orders by Service ID and reason:{}",
                subOrderRepository.isAnyOrderIdsByServiceIdAndReason(serviceId));
        log.info("Failed orders count by Service ID and reason:{}",
                subOrderRepository.getOrderIdsCountByServiceIdAndReason(serviceId));
        List<Long> orderIds = subOrderRepository.findOrderIdsByServiceId(serviceId);
        log.info("pending orderIds:{}", orderIds);
        List<OrderEntity> orders = orderRepository.findOrdersByOrderIdAndProfileId(orderTypes, orderIds, profileId);
        log.info("pending orderIds:{}", orders);
        if (!orders.isEmpty()) {
            ordersPresent = true;
        }
        orderIds = null;
        orders = null;

        return ordersPresent;
    }

    protected void handleError(String code, String message, String logMessage, String system) {
        if (StringUtils.isNotEmpty(logMessage))
            log.error(logMessage);
        executionContext.getErrorDetail().setCode(code);
        executionContext.getErrorDetail().setMessage(message);
        executionContext.getErrorDetail().setSystem(system);
        executionContext.setError(true);
    }

    protected String getRequestFromSpec() {
        String request = null;
        if (CommonUtils.INSTANCE.validateField(reqSpecKey)) {
            try {
                var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                        "PRINT_EXECUTION_DATA");
                if (ObjectUtils.isNotEmpty(appConfig)) {
                    log.info("Execution context ==================>>> {}", JsonUtils.toJsonString(executionContext));
                }
                request = joltUtils.convert(reqSpecKey, executionContext.getOrder().getOrderType(),
                        objectMapper.convertValue(executionContext, Map.class), executionContext.getAttributes());
            } catch (Exception e) {
                log.error("Exception occurred. unable to form the tp request {}", e.getMessage());
                handleError("COM-001", "Internal Error: error in forming tp request", null, "COM");
            }
        }
        return request;
    }
}
