package in.co.sixdee.bss.com.orderorchestrator.model.mnp.portinnotification;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import lombok.Data;

import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class PortingResult implements Serializable {

	private static final long serialVersionUID = 1L;

	@XmlElement(name = "responseCode", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private String responseCode;
	@XmlElement(name = "reasonText", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private String reasonText;
	@XmlElement(name = "rejectionCode", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private String rejectionCode;

	
}
