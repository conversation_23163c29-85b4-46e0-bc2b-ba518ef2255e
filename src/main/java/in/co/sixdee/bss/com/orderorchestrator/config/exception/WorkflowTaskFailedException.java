package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import java.io.Serial;

import lombok.Getter;
import lombok.Setter;
@Getter
@Setter
public class WorkflowTaskFailedException extends RuntimeException {

    /**
     *
     */
    @Serial
    private static final long serialVersionUID = 1L;
    private String failedTaskName;
    private String failureCode;
    private String failureDescription;
    public WorkflowTaskFailedException(String failedTaskName, String failureCode, String failureDescription) {
        super("Failed task execution for:" + failedTaskName + " Caused by:" + failureCode + " " + failureDescription);
        this.failedTaskName = failedTaskName;
        this.failureCode = failureCode;
        this.failureDescription = failureDescription;
    }

    @Override
    public synchronized Throwable fillInStackTrace() {
        return this;
    }
}
