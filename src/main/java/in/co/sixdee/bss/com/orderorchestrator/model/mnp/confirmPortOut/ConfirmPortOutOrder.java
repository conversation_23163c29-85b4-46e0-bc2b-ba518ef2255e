package in.co.sixdee.bss.com.orderorchestrator.model.mnp.confirmPortOut;

import in.co.sixdee.bss.com.orderorchestrator.model.mnp.connectService.ReferenceID;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "confirmPortOutOrder")
@XmlAccessorType(XmlAccessType.FIELD)
public class ConfirmPortOutOrder {

	@XmlElement(name = "referenceID", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private ReferenceID referenceId;
	@XmlElement(name = "requestId", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private RequestId requestID;
	
	
	public ReferenceID getReferenceId() {
		return referenceId;
	}
	
	public void setReferenceId(ReferenceID referenceId) {
		this.referenceId = referenceId;
	}

	public RequestId getRequestID() {
		return requestID;
	}

	public void setRequestID(RequestId requestID) {
		this.requestID = requestID;
	}
	
}
