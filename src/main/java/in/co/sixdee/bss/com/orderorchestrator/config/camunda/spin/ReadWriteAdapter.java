package in.co.sixdee.bss.com.orderorchestrator.config.camunda.spin;

import java.util.Optional;

/**
 * Adapter to read variables.
 *
 * @param <T>
 *            type of value.
 */
public interface ReadWriteAdapter<T> {

	/**
	 * Reads a variable.s
	 *
	 * @return value.
	 * @exception VariableNotFoundException
	 *                if the required variable is missing or can't be read.
	 */
	T get();

	/**
	 * Reads a variable and returns a value if exists or an empty.
	 *
	 * @return optional.
	 */

	Optional<T> getOptional();

	/**
	 * Writes a value.
	 *
	 * @param value
	 *            value to write.
	 */
	void set(T value);

	/**
	 * Reads a variable and returns a value if exists or null.
	 *
	 * @return value or <code>null</code>>
	 */

	default T getOrNull() {
		return getOptional().orElse(null);
	}

}