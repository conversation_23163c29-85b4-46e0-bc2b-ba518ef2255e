/**
 *
 */
package in.co.sixdee.bss.com.orderorchestrator.web.rest.api;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import in.co.sixdee.bss.om.model.dto.Response;
import in.co.sixdee.bss.om.model.dto.WorkflowRequest;
import in.co.sixdee.bss.om.model.dto.order.CancelOrder;


@RequestMapping("/orders/")
public interface OrderOrchestratorAPI {

	@PostMapping(value = { "/continue-order", "/sim-delivery"} ,produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Response> updateOrderStatus(@RequestBody OrderCallBack request);
	
	
	@PostMapping(value = "/skip-or-retry", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Response> processInstanceModification(@RequestBody WorkflowRequest request);

	@PostMapping(value = "/payment-callback", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Response> paymentCallBack(@RequestBody OrderCallBack request);
	
	@PostMapping(value = "/approve", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Response> approve(@RequestBody OrderCallBack request);

	/*@PostMapping(value="/cancel",produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Response> cancelOrder(@RequestBody CancelOrder request);*/
	
	@PostMapping(value = "/bulk-skip-or-retry", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Response> processBulkSkipRetryOrder(@RequestBody WorkflowRequest request);

}
