package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.OrderItem;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Log4j2
@Component(value = "somCancelNsaPlan")
@RequiredArgsConstructor
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SOMCancelNsaPlan extends AbstractDelegate {

    private Order orderPayload = null;

    protected String request = null;

    protected int index = 0;

    protected final ObjectMapper objectMapper;

    @Override
    protected void execute() throws Exception {
        try {
            orderPayload = executionContext.getOrder();
            request = createSOMRequest();
            var callThirdPartyDTO = callThirdParty(request);
            if (callThirdPartyDTO == null) {
                executionContext.setError(true);
                return;
            }
            validateResponse(callThirdPartyDTO);
        } catch (Exception e) {
            log.error("{} In {} .execute Exception occured ", executionContext.getTraceId(), this.getClass().getName(), e);
        }
    }

    protected String createSOMRequest() throws Exception {
        List<ServiceOrderItem> serviceOrderItemList;
        var serviceOrder = new SOMServiceOrderDTO();
        executionContext.getAttributes().put("callbackCorrelationId",
                callbackEvent + ":" + executionContext.getAttributes().get(GenericConstants.SUB_ORDER_ID));
        serviceOrder.setExternalId(orderPayload.getOrderId());
        serviceOrder.setDescription(orderPayload.getDescription());
        serviceOrder.setPriority("1");
        serviceOrder.setCategory("MobileService");
        serviceOrder.setRequestedStartDate(
                StringUtils.isNotEmpty(orderPayload.getRequestedStartDate()) ? orderPayload.getRequestedStartDate()
                        : Instant.now().toString());
        serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
        serviceOrder.setType("ServiceOrder");
        serviceOrder.setRegistryId(executionContext.getEntityId());
        serviceOrderItemList = createServiceOrderItem();
        serviceOrder.setExternalServiceId(executionContext.getOrder().getServiceManagement().getServiceId());
        serviceOrder.setServiceOrderItem(serviceOrderItemList);
        request = objectMapper.writeValueAsString(serviceOrder);
        return request;
    }

    private List<ServiceOrderItem> createServiceOrderItem() {
        List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
        ServiceOrderItem serviceOrderItem;
        List<SOMService> serviceList = new ArrayList<SOMService>();
        String subscriptionIdToBeCancelled = null;
        try {
            var services = getServicesFromSOM();
            subscriptionIdToBeCancelled = getNsaSubscriptionId();
            if (ObjectUtils.isNotEmpty(services) && StringUtils.isNotEmpty(subscriptionIdToBeCancelled)) {
                for (SOMService service : services) {
                    // CFS addons matching the subs id of current Execution shud be cancelled
                    if ("CFS".equalsIgnoreCase(service.getCategory())
                            && ObjectUtils.isNotEmpty(service.getServiceCharacteristic())) {
                        var serviceCharacteristics = service.getServiceCharacteristic();
                        for (Characteristic serviceCharacteristic : serviceCharacteristics) {
                            if (StringUtils.isNotEmpty(serviceCharacteristic.getName())
                                    && ObjectUtils.isNotEmpty(serviceCharacteristic.getValue())
                                    && StringUtils.equalsIgnoreCase("SUBSCRIPTION_ID", serviceCharacteristic.getName())) {

                                String somSubscriptionId = (String) serviceCharacteristic.getValue();
                                if (StringUtils.equalsIgnoreCase(subscriptionIdToBeCancelled, somSubscriptionId)) {
									log.info("Subscription id from SOM is {} and from billing is {}",somSubscriptionId,subscriptionIdToBeCancelled);
                                    service.setState("terminated");
                                    service.getServiceSpecification().setName(service.getName());
                                    service.setName(null);
                                    service.setCategory(null);
                                    service.setType("CFS");
                                    serviceList.add(service);
                                }
                            }
                        }
                    }
                }
            }
            if (ObjectUtils.isNotEmpty(serviceList)) {
                for (SOMService service : serviceList) {
                    serviceOrderItem = setServiceOrderItem(service);
                    serviceOrderItemList.add(serviceOrderItem);
                }
            }
        } catch (Exception e) {
            log.error("Exception occurred in createServiceOrderItem ", e);
        }

        return serviceOrderItemList;
    }

    private String getNsaSubscriptionId() {
        if (executionContext.getObjectAttributes().containsKey("nsaSubscriptionDetails")) {
            Subscription subscription = objectMapper.convertValue(
                    executionContext.getObjectAttributes().get("nsaSubscriptionDetails"),
                    new TypeReference<Subscription>() {
                    });
            if (ObjectUtils.isNotEmpty(subscription) && StringUtils.isNotEmpty(subscription.getSubscriptionId())) {
                return subscription.getSubscriptionId();
            }
        }
        return null;
    }

    @SuppressWarnings("unused")
    private List<SOMService> getServicesFromSOM() {
        List<SOMService> somResponse = null;
        try {
            var workFlowData = objectMapper.writeValueAsString(executionContext.getWorkflowData());
            if (ObjectUtils.isNotEmpty(workFlowData)) {
                if (ObjectUtils.isNotEmpty(workFlowData)) {
                    somResponse = parseSOMResponse(workFlowData, executionContext);
                }
            }
        } catch (Exception e) {
            log.error(" :::::  Exception occurred in parse UPCResponse execute method  :::::", e);
        }
        return somResponse;
    }

    private List<SOMService> parseSOMResponse(String workFlowData, OrderFlowContext orderFlowContext) {
        List<SOMService> serviceList = null;
        JsonObject responseReceived = null;
        JsonArray parseSOMResponse = null;
        Gson gson = null;
        try {
            gson = new Gson();
            if (ObjectUtils.isNotEmpty(workFlowData) && workFlowData.contains("SOMFetchServiceRegistryResponseAttributes")) {
                responseReceived = gson.fromJson(workFlowData, JsonObject.class);

                parseSOMResponse = responseReceived.getAsJsonArray("SOMFetchServiceRegistryResponseAttributes");
                serviceList = gson.fromJson(parseSOMResponse, new TypeToken<List<SOMService>>() {
                }.getType());


            }
        } catch (Exception e) {
            log.error("Exception occurred in parseSOMResponse ", e);

        }
        return serviceList;
    }
    private ServiceOrderItem setServiceOrderItem(SOMService service) {

        var serviceOrderItem = new ServiceOrderItem();
        index = index + 1;
        serviceOrderItem.setId(String.valueOf(index));
        serviceOrderItem.setAction("delete_addon");
        serviceOrderItem.setType("ServiceOrderItem");
        serviceOrderItem.setService(service);
        return serviceOrderItem;
    }

}
