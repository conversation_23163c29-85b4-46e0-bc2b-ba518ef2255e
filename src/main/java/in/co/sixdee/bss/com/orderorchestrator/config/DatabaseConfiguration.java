package in.co.sixdee.bss.com.orderorchestrator.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration
@EnableJpaRepositories("in.co.sixdee.bss")
@EntityScan(basePackages = {"in.co.sixdee.bss"})
public class DatabaseConfiguration {
}
