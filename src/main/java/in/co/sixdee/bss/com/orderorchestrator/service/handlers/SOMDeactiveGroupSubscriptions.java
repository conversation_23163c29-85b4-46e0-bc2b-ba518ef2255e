package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "somDeactiveGroupSubscriptions")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class SOMDeactiveGroupSubscriptions extends AbstractDelegate  {
	protected String request = null;

	protected Order orderPayload = null;

	protected String msisdn = null;

	protected int index = 0;

	List<Subscription> bsSubscriptions = new ArrayList<>();
	protected void execute() throws Exception {

		try {
			orderPayload = executionContext.getOrder();
			request = createRequest();
			var callThirdPartyDTO = callThirdParty(request);
			if (callThirdPartyDTO == null) {
				executionContext.setError(true);
				return;
			}
			validateResponse(callThirdPartyDTO);
		} catch (Exception e) {
			log.error("{} In {} .execute Exception occurred ", executionContext.getTraceId(), this.getClass().getName(),
					e);
		}
	}
	private String createRequest() throws JsonProcessingException {
		var serviceOrder = new SOMServiceOrderDTO();
		setRequestBodyParams(serviceOrder);
		List<SOMServiceOrderDTO.ServiceOrderItem> serviceOrderItemList = createOrderItemForDelete(serviceOrder);
		serviceOrder.setServiceOrderItem(serviceOrderItemList);
		return objectMapper.writeValueAsString(serviceOrder);
	}
	
	private List<SOMServiceOrderDTO.ServiceOrderItem> createOrderItemForDelete(SOMServiceOrderDTO serviceOrder) {

		List<SOMServiceOrderDTO.ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		List<SOMServiceOrderDTO.SOMService> somFetchServices = getSOMServiceRegistry();
		if (ObjectUtils.isNotEmpty(somFetchServices)) {
			for (SOMService somService : somFetchServices) {
				var serviceOrderItem = createServiceOrderItemFromFetch(somService);
				if (ObjectUtils.isNotEmpty(serviceOrderItem))
					serviceOrderItemList.add(serviceOrderItem);
			}
		} else {
			log.info("Som fetch registry response is empty...not able to form som delete-Addon request..");
		}
		return serviceOrderItemList;
	}
	
	private ServiceOrderItem createServiceOrderItemFromFetch(SOMService somService) {

		SOMService serviceItem = createServiceForServiceOrderItem(somService);
		if (ObjectUtils.isNotEmpty(serviceItem)) {
			var serviceOrderItem = new ServiceOrderItem();
			index = index + 1;
			serviceOrderItem.setId(String.valueOf(index));
			serviceOrderItem.setAction("delete_addon");
			serviceOrderItem.setType("ServiceOrderItem");
			serviceOrderItem.setService(serviceItem);
			return serviceOrderItem;
		}
		return null;
	}
	
	private SOMService createServiceForServiceOrderItem(SOMService somService) {
		
			somService.setType(somService.getCategory());
			somService.setState("terminated");
			somService.setCategory(null);
			somService.setName(null);
			return somService;
	}
	
	
	private List<SOMServiceOrderDTO.SOMService> getSOMServiceRegistry() {
		List<SOMServiceOrderDTO.SOMService> somFetchServices = new ArrayList<>();
		if (executionContext.getWorkflowData().containsKey("groupPlanCfss")) {
			somFetchServices = objectMapper.convertValue(
					executionContext.getWorkflowData().get("groupPlanCfss"),
					new TypeReference<List<SOMServiceOrderDTO.SOMService>>() {
					});
		}
		if (ObjectUtils.isEmpty(somFetchServices)) {
			log.info(" No services found with the serviceId :: {}", msisdn);
			return null;
		}
		somFetchServices = somFetchServices.stream().filter(service -> service.getState().equalsIgnoreCase("active"))
				.collect(Collectors.toList());
		return somFetchServices;
	}
	
	private void setRequestBodyParams(SOMServiceOrderDTO serviceOrder) {

		msisdn = getSponsorMsisdnFromBsShareBundle();
		serviceOrder.setExternalId(orderPayload.getOrderId());
		serviceOrder.setDescription("CancelSubscription");
		serviceOrder.setCategory("MobileService");
		serviceOrder.setPriority("1");
		serviceOrder.setType("ServiceOrder");
		serviceOrder.setRequestedStartDate(
				StringUtils.isNotEmpty(orderPayload.getRequestedStartDate()) ? orderPayload.getRequestedStartDate()
						: Instant.now().toString());
		serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
		serviceOrder.setExternalServiceId(msisdn);
		serviceOrder.setRegistryId(fetchEntityId());

	}
	private String fetchEntityId() {
		if(OrderTypes.MNP_PORT_OUT.equalsIgnoreCase(executionContext.getOrder().getOrderType()) )
		return executionContext.getAttributes().get("donorEntityId");
		else
		return executionContext.getEntityId();
	}
	private String getSponsorMsisdnFromBsShareBundle() {
		JsonNode billResp = objectMapper.valueToTree(executionContext.getWorkflowData().get("BSFetchShareBundleDetailsResponseAttributes"));
		if(ObjectUtils.isNotEmpty(billResp) && StringUtils.isNotEmpty(billResp.get("sponsorMsisdn").asText())) {
			return billResp.get("sponsorMsisdn").asText();
		}
		return null;
	}
}
