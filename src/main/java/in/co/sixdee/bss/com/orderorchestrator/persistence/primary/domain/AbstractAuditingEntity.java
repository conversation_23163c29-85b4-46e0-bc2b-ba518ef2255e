package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * Base abstract class for entities which will hold definitions for created, last modified, created
 * by, last modified by attributes.
 */
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class AbstractAuditingEntity implements Serializable {

	private static final long	serialVersionUID	= 1L;

	@CreatedBy
	@Column(name = "CREATED_BY", length = 50, updatable = false)
	@JsonIgnore
	private String				createdBy;

	@CreatedDate
	@Column(name = "CREATED_DATE", columnDefinition = "DATETIME DEFAULT CURRENT_TIMESTAMP", insertable = false, updatable = false)
	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	@JsonIgnore
	private Date				createdDate;

	@LastModifiedBy
	@Column(name = "LAST_MODIFIED_BY", length = 50)
	@JsonIgnore
	private String				lastModifiedBy;

	@LastModifiedDate
	@Column(name = "LAST_MODIFIED_DATE", columnDefinition = "DATETIME DEFAULT CURRENT_TIMESTAMP", insertable = false, updatable = false)
	@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
	@JsonIgnore
	private Date				lastModifiedDate;

	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public String getLastModifiedBy() {
		return lastModifiedBy;
	}

	public void setLastModifiedBy(String lastModifiedBy) {
		this.lastModifiedBy = lastModifiedBy;
	}

	public Date getLastModifiedDate() {
		return lastModifiedDate;
	}

	public void setLastModifiedDate(Date lastModifiedDate) {
		this.lastModifiedDate = lastModifiedDate;
	}
}
