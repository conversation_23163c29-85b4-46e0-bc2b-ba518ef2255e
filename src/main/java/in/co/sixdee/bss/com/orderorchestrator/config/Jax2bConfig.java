package in.co.sixdee.bss.com.orderorchestrator.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class Jax2bConfig {

    @Bean
    public Jaxb2Marshaller jaxb2Marshaller() {

        Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
        jaxb2Marshaller.setPackagesToScan("in.co.sixdee.bss.com.orderorchestrator.model.mnp");
        Map<String, Object> props = new HashMap<>();
        props.put("jaxb.formatted.output", false);
        jaxb2Marshaller.setMarshallerProperties(props);
        return jaxb2Marshaller;

    }
}
