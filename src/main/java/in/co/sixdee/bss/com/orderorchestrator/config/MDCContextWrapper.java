package in.co.sixdee.bss.com.orderorchestrator.config;

import lombok.extern.log4j.Log4j2;
import org.slf4j.MDC;

import java.util.Map;

@Log4j2
public class MDCContextWrapper {

	public static Runnable wrapWithMdcContext(Runnable task) {
		//save the current MDC context
		Map<String, String> contextMap = MDC.getCopyOfContextMap();
		log.info(":::::::::::::: contextMap :: {}", contextMap.entrySet());
		return () -> {
			setMDCContext(contextMap);
			try {
				log.info("Setting MDC :::::::::::::::::::::::::::: contextMap :: {}", MDC.getCopyOfContextMap().entrySet());
				task.run();
			} finally {
				// once the task is complete, clear MDC
				MDC.clear();
			}
		};
	}

	public static void setMDCContext(Map<String, String> contextMap) {
		MDC.clear();
		if (contextMap != null) {
			MDC.setContextMap(contextMap);
		}
	}
}
