package in.co.sixdee.bss.com.orderorchestrator.config;

import in.co.sixdee.bss.com.edr.async.pool.EdrThreadPool;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

@Configuration
@Log4j2
public class EdrConfig {

	private EdrThreadPool edrThreadPool;

	public void addToEdrPool(OrderFlowContext orderFlowContext) {
		edrThreadPool.addTask(orderFlowContext);
	}

	@PostConstruct
	void initiateThreadPools() {
		edrThreadPool = new EdrThreadPool(1, "edrWorker");
	}

	@PreDestroy
	void shutDownThread() {
		edrThreadPool.shutdown();
	}
}
