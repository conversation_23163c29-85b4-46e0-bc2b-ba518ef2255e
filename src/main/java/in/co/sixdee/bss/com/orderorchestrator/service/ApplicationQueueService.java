package in.co.sixdee.bss.com.orderorchestrator.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.ApplicationQueueEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.ApplicationQueueRepository;

@Service
public class ApplicationQueueService {

	@Autowired
	private ApplicationQueueRepository queueRepository;

	public void save(ApplicationQueueEntity entity) {
		queueRepository.saveAndFlush(entity);
	}

	public List<ApplicationQueueEntity> findAll() {
		return queueRepository.findAll();
	}

	public void remove() {
		queueRepository.deleteAll();
	}
	
	public List<ApplicationQueueEntity> findBasedOnInstanceId(String instanceId){
		return queueRepository.findBasedOnInstanceId(instanceId);
	}
	
	

}
