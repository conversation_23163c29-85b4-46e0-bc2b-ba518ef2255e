package in.co.sixdee.bss.com.orderorchestrator.core.uniquenumgen;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
@EnableConfigurationProperties(UniqueNumberGeneratorProperties.class)
public class UniqueNumberGeneratorAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public UniqueNumberGenerator uniqueNumberGenerator(
            UniqueNumberGeneratorProperties properties,
            DataSource dataSource
    ) {
        UniqueNumberGeneratorStrategy strategy;
        strategy = new MySqlUniqueNumberGeneratorStrategy(dataSource, properties);
        return new UniqueNumberGenerator(strategy);
    }
}
