package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import java.util.Arrays;
import java.util.List;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CommonException extends RuntimeException {

	private static final long serialVersionUID = 1L;
	
	private StatusConstants.HttpConstants status;
	private String                        message;
	private List<String> errors;

	public CommonException(StatusConstants.HttpConstants status, String message, List<String> errors) {
		super();
		this.status = status;
		this.message = message;
		this.errors = errors;
	}
	
	public CommonException(StatusConstants.HttpConstants status, String message, String error) {
		super();
		this.status = status;
		this.message = message;
		errors = Arrays.asList(error);
	}
	
	public CommonException(StatusConstants.HttpConstants status, String message) {
		super();
		this.status = status;
		this.message = message;
	}
	
	public CommonException(String msg) {
		super();
		this.message = msg;
	}

	public StatusConstants.HttpConstants getStatus() {
		return status;
	}

	public void setStatus(StatusConstants.HttpConstants status) {
		this.status = status;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public List<String> getErrors() {
		return errors;
	}

	public void setErrors(List<String> errors) {
		this.errors = errors;
	}
	
}
