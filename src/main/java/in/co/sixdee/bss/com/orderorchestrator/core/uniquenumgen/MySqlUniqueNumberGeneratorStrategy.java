package in.co.sixdee.bss.com.orderorchestrator.core.uniquenumgen;

import in.co.sixdee.bss.com.orderorchestrator.config.util.RandomGenerator;
import io.github.resilience4j.core.IntervalBiFunction;
import io.github.resilience4j.core.IntervalFunction;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import lombok.extern.log4j.Log4j2;
import org.springframework.dao.OptimisticLockingFailureException;

import javax.sql.DataSource;
import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

@Log4j2
public class MySqlUniqueNumberGeneratorStrategy implements UniqueNumberGeneratorStrategy {

    private final DataSource dataSource;
    private final UniqueNumberGeneratorProperties properties;

    private static final ReentrantLock lock = new ReentrantLock();
    private static final AtomicBoolean initialized = new AtomicBoolean(false);
    private static final String TABLE_NAME = "COM_UNIQUE_NUMBER_SEQ";
    private final int numberLength;


    private final Retry retry;

    public MySqlUniqueNumberGeneratorStrategy(DataSource dataSource,
                                              UniqueNumberGeneratorProperties properties) {
        this.dataSource = dataSource;
        this.properties = properties;
        //tableName = properties.getMysqlTableName();
        numberLength = properties.getNumberLength();
        initializeTable();
        retry = initializeRetry();

    }

    private Retry initializeRetry() {
        log.info("Initializing retry with interval {} and max retries {}", properties.getRetryInterval(), properties.getMaxRetries());
        IntervalFunction intervalWithExponentialBackoff = IntervalFunction
                .ofExponentialBackoff(properties.getRetryInterval(), 2);
        IntervalBiFunction<Integer> intervalBiFunction = IntervalBiFunction.ofIntervalFunction(intervalWithExponentialBackoff);
        RetryConfig retryConfig = RetryConfig.<Integer>custom()
                .maxAttempts(properties.getMaxRetries())
                .intervalBiFunction(intervalBiFunction)
                .retryExceptions(OptimisticLockingFailureException.class)
                .build();
        return Retry.of("getNextUniqueNumber", retryConfig);
    }

    private void initializeTable() {


        log.info("Initializing DB schema for unigue number generation");
        String sql = "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " (" +
                "ID VARCHAR(50) PRIMARY KEY," +
                "CURRENT_NUMBER INT NOT NULL, " +
                "LAST_RESET_DATE DATE NOT NULL, " +
                "VERSION INT(10) NOT NULL," +
                "INDEX idx_version (VERSION))";

        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute(sql);
        } catch (SQLException e) {
            throw new RuntimeException("Failed to initialize table", e);
        }
    }


    @Override
    public int getNextUniqueNumber() {
        ensureInitialized();

        AtomicInteger attemptCounter = new AtomicInteger(0);

        Supplier<Integer> decoratedSupplier = Retry.decorateSupplier(retry, () -> generateNextNumber(attemptCounter.incrementAndGet()));

        try {
            return decoratedSupplier.get();
        } catch (Exception e) {
            return getNextUniqueNumberFallback(e);
        }
    }

    private void ensureInitialized() {
        if (!initialized.get()) {
            lock.lock();
            try {
                if (!initialized.get()) {
                    initializeSequence();
                    initialized.set(true);
                }
            } finally {
                lock.unlock();
            }
        }
    }

    private void initializeSequence() {
        try (Connection conn = dataSource.getConnection()) {
            conn.setAutoCommit(false);
            try {
                String selectSql = "SELECT COUNT(*) FROM " + TABLE_NAME;
                try (PreparedStatement pstmt = conn.prepareStatement(selectSql)) {
                    try (ResultSet rs = pstmt.executeQuery()) {
                        if (rs.next() && rs.getInt(1) == 0) {
                            String insertSql = "INSERT INTO " + TABLE_NAME +
                                    " (id, current_number, last_reset_date, version) VALUES (?, ?, ?, ?)";
                            try (PreparedStatement insertStmt = conn.prepareStatement(insertSql)) {
                                insertStmt.setString(1, LocalDateTime.now().toString());
                                insertStmt.setInt(2, getMinNumber());
                                insertStmt.setDate(3, Date.valueOf(LocalDate.now()));
                                insertStmt.setInt(4, 1);
                                insertStmt.executeUpdate();
                            }
                            conn.commit();
                        }
                    }
                }
            } catch (SQLException e) {
                conn.rollback();
                throw new RuntimeException("Failed to initialize sequence", e);
            }
        } catch (SQLException e) {
            throw new RuntimeException("Database connection error during initialization", e);
        }
    }


    public int generateNextNumber(int currentAttempt) {
        if (currentAttempt > 1) {
            log.info("Retry execution - attempt {} for thread {}", currentAttempt, Thread.currentThread().getName());
        } else {
            log.info("Normal execution");
        }
        boolean shouldReset = false;

        try (Connection conn = dataSource.getConnection()) {
            conn.setAutoCommit(false);
            try {
                String selectSql = "SELECT id,current_number, last_reset_date, version FROM " + TABLE_NAME;
                try (PreparedStatement pstmt = conn.prepareStatement(selectSql)) {
                    try (ResultSet rs = pstmt.executeQuery()) {
                        if (rs.next()) {
                            int currentNumber = rs.getInt("CURRENT_NUMBER");
                            int version = rs.getInt("VERSION");
                            String id = rs.getString("ID");

                            int nextNumber = currentNumber + 1;
                            if (nextNumber > getMaxNumber()) {
                                nextNumber = getMinNumber();
                                shouldReset = true;
                            }
                            String updateSql = null;
                            int updatedRows = -1;


                            if (shouldReset) {
                                updateSql = "UPDATE " + TABLE_NAME +
                                        " SET current_number = ?, last_reset_date = ?, version = 1 " +
                                        "WHERE id = ? AND version = ?";
                                try (PreparedStatement updateStmt = conn.prepareStatement(updateSql)) {
                                    updateStmt.setInt(1, nextNumber);
                                    updateStmt.setDate(2, Date.valueOf(LocalDate.now()));
                                    updateStmt.setString(3, id);
                                    updateStmt.setInt(4, version);
                                    updatedRows = updateStmt.executeUpdate();
                                }
                            } else {
                                updateSql = "UPDATE " + TABLE_NAME +
                                        " SET current_number = ?, version = version + 1 " +
                                        "WHERE id = ? AND version = ?";
                                try (PreparedStatement updateStmt = conn.prepareStatement(updateSql)) {
                                    updateStmt.setInt(1, nextNumber);
                                    updateStmt.setString(2, id);
                                    updateStmt.setInt(3, version);
                                    updatedRows = updateStmt.executeUpdate();
                                }
                            }

                            if (updatedRows == 0) {
                                throw new OptimisticLockingFailureException("Concurrent modification detected");
                            }

                            conn.commit();
                            return nextNumber;
                        } else {
                            int nextNumber = getMinNumber();
                            String id = LocalDateTime.now().toString();
                            String insertSql = "INSERT INTO " + TABLE_NAME +
                                    " (id,current_number, last_reset_date, version) VALUES (?, ?, ?, ?)";
                            try (PreparedStatement insertStmt = conn.prepareStatement(insertSql)) {
                                insertStmt.setString(1, id);
                                insertStmt.setInt(2, nextNumber);
                                insertStmt.setDate(3, Date.valueOf(LocalDate.now()));
                                insertStmt.setInt(4, 1);
                                insertStmt.executeUpdate();
                            }
                            conn.commit();
                            return nextNumber;
                        }
                    }
                }
            } catch (SQLException e) {
                conn.rollback();
                throw new RuntimeException("Failed to generate unique number", e);
            }
        } catch (SQLException e) {
            throw new RuntimeException("Database connection error", e);
        }
    }


    private int getMinNumber() {
        return (int) Math.pow(10, Math.max(0, numberLength - 1));
    }

    private int getMaxNumber() {
        return (int) Math.pow(10, numberLength) - 1;
    }

    public int getNextUniqueNumberFallback(Throwable t) {
        log.warn("Using fallback method for unique number generation", t);
        return RandomGenerator.getInstance().nextRandom();
    }
}
