package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.WorkflowTaskFailedException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.common.util.CommonUtils;
import in.co.sixdee.bss.om.model.dto.enrichment.OrderEnrichmentContext;
import in.co.sixdee.bss.om.model.dto.order.*;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "fetchSaPlanDetails")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class FetchSaPlanDetails extends AbstractDelegate {
    @Autowired
    private GetDataFromCache getDataFromCache;

    @Override
    protected void execute() throws Exception {
        getSaPlanIdFromConfig(executionContext);
        if (StringUtils.isNotEmpty(executionContext.getAttributes().get("saPlans"))) {
            var callThirdPartyDTO = callThirdParty(null);
            if (callThirdPartyDTO == null) {
                executionContext.setError(true);
                return;
            }
            var response = callThirdPartyDTO.getResponse();
            validateResponse(callThirdPartyDTO);
            if (executionContext.isError())
                return;
            enrichSaPlanDetails(executionContext.getAttributes().get("saPlans"), response);
        }

        modifyWorkflowData(callThirdPartyDTO.getResponse());
        workflowDataUpdated = true;
        log.info("Received order from the message in fetch nsa plan :: {} ", objectMapper.writeValueAsString(executionContext));

    }

    private void enrichSaPlanDetails(String saPlans, String response) throws JsonProcessingException {
        /*List<ProductOffering> productOfferings = objectMapper.readValue(response, new TypeReference<List<ProductOffering>>() {
        });
        log.info("response has success::" + response.contains("success"));
        if (response.contains("success")) {

            // Parse the JSON string into a JsonNode
            JsonNode rootNode = objectMapper.readTree(response);

            // Get the "success" object as a JSON string
            if (rootNode.get(0) != null && rootNode.get(0).has("success")) {
                String successObjectAsString = rootNode.get(0).get("success").toString();
                productOfferings = objectMapper.readValue(successObjectAsString, new TypeReference<>() {
                });
            }

        }*/
        boolean somCallRqd = false;
        boolean esbCallRqd = false;
        List<ProductOffering> productOfferings = parseProductOfferings(response);
        var productOfferingUpc = getRequiredPlanDetails(productOfferings,saPlans);
        log.info("Received order from the message in fetch upc plan :: {} ", objectMapper.writeValueAsString(productOfferingUpc));
        validateUpcResponse(productOfferings, productOfferingUpc, saPlans);
        Subscription saSubscriptionDetails = enrichSubscription(productOfferingUpc);
        if (ObjectUtils.isNotEmpty(saSubscriptionDetails)) {
            executionContext.getObjectAttributes().put("saSubscriptionDetails", saSubscriptionDetails);
        }
    }

    private ProductOffering getRequiredPlanDetails(List<ProductOffering> productOfferings, String saPlans) {
        for(ProductOffering offering:productOfferings){
            log.info("plan code from upc {} and plan code from table {}",offering.getPlanCode(),saPlans);
            if(offering.getPlanCode().equalsIgnoreCase(saPlans)){
                return offering;
            }
        }
        return null;
    }

    public List<ProductOffering> parseProductOfferings(String response) throws JsonProcessingException {
        if (response.contains("success")) {
            JsonNode rootNode = objectMapper.readTree(response);
            if (rootNode.get(0) != null && rootNode.get(0).has("success")) {
                String successObjectAsString = rootNode.get(0).get("success").toString();
                return objectMapper.readValue(successObjectAsString, new TypeReference<>() {
                });
            }
        }
        return objectMapper.readValue(response, new TypeReference<>() {
        });
    }

    private Subscription enrichSubscription(ProductOffering productOffering) {
        var subscription = new Subscription();
        subscription.setPlanId(productOffering.getPlanCode());
        subscription.setUpcPlanId(productOffering.getId());
        subscription.setOcsPlanId(productOffering.getId());
        subscription.setUpcVersion(productOffering.getVersionNo());
        subscription.setPlanName(productOffering.getName());
        subscription.setPlanDesc(productOffering.getInvoiceDescription());
        setProductParameters(productOffering, subscription);

        if (ObjectUtils.isNotEmpty(productOffering.getProductOfferGroup())) {
            subscription.setPlanGroupId(productOffering.getProductOfferGroup().getId());
            subscription.setPlanGroupName(productOffering.getProductOfferGroup().getName());
        }
        subscription.setExpiryDate(findExpiryDate(productOffering, executionContext));

        if (productOffering.getProductSpecifications() != null && !productOffering.getProductSpecifications().isEmpty()) {
            if ("1".equals(productOffering.getProductSpecifications().get(0).getType().getId()))
                subscription.setPlanType("1");
            else {
                subscription.setPlanType("0");
            }
        } else {
            subscription.setPlanType("1");
        }
        if (subscription.getCharges() == null) {
            subscription.setCharges(new ArrayList<>());
        }
        enrichCfssPrssLrss(productOffering, subscription);
        enrichCharges(subscription.getCharges(), productOffering.getProductOfferingPrice());
        findTierBasedFlag(subscription, subscription.getCharges());
        findSomCallRequired(productOffering, subscription);
        findEsbCallRequired(subscription);
        fetchSubscrberStatusFromUpc(subscription);
        if (StringUtils.isEmpty(subscription.getAutoRenewal())) {
            var renewal = findIsAutoRenewal(subscription);
            if (!renewal && ObjectUtils.isNotEmpty(productOffering.getRatingParameters())
                    && ObjectUtils.isNotEmpty(productOffering.getRatingParameters().get(0))
                    && StringUtils.isNotEmpty(productOffering.getRatingParameters().get(0).getRenewal())
                    && "true".equalsIgnoreCase(productOffering.getRatingParameters().get(0).getRenewal())) {
                subscription.setAutoRenewal("1");

            }
        } else
            cfsUPCOptimization(subscription);
        if (ObjectUtils.isNotEmpty(productOffering.getRatingParameters())) {
            checkForShelfLifeAddon(productOffering.getRatingParameters(), subscription);
        }
        return subscription;
    }

    private void checkForShelfLifeAddon(List<ProductOffering.RatingParameters> ratingParameters, Subscription subscription) {
        for (ProductOffering.RatingParameters ratingParam : ratingParameters) {
            if (StringUtils.isNotEmpty(ratingParam.getActivateaddonuponuse())) {
                subscription.setActivateAddOnUponUsage(ratingParam.getActivateaddonuponuse());
            }
        }
    }

    private void cfsUPCOptimization(Subscription subscription) {
        if (subscription.getCfss() != null) {
            for (CFSRef cfs : subscription.getCfss()) {
                if (GenericConstants.CFS_UPC.equalsIgnoreCase(cfs.getName())) {
                    subscription.getCfss().remove(cfs);
                    break;
                }
            }
        }
    }

    private boolean findIsAutoRenewal(Subscription subscription) {
        subscription.setAutoRenewal("0");// default 0-false
        boolean autoRenewal = false;
        if (subscription.getCfss() != null) {
            for (CFSRef cfs : subscription.getCfss()) {
                if (GenericConstants.CFS_UPC.equalsIgnoreCase(cfs.getName())) {
                    for (CFSCharacteristicRef characteristics : cfs.getCharacteristics())
                        if (GenericConstants.CHARACTERISTIC_IS_RENEWAL.equals(characteristics.getName())) {
                            if (StringUtils.isNotEmpty(characteristics.getValue()) && "true".equalsIgnoreCase(characteristics.getValue())) {
                                subscription.setAutoRenewal("1");
                            }
                            autoRenewal = true;
                            break;
                        }

                    subscription.getCfss().remove(cfs);
                    break;
                }
            }
        }
        return autoRenewal;
    }

    private void fetchSubscrberStatusFromUpc(Subscription subscription) {
        if (ObjectUtils.isNotEmpty(subscription.getCfss())) {
            for (CFSRef cfs : subscription.getCfss()) {
                if (GenericConstants.CFSS_SUBSCRIBER_LIFECYCLE.equalsIgnoreCase(cfs.getName())) {
                    for (CFSCharacteristicRef characteristics : cfs.getCharacteristics())
                        if (GenericConstants.CFSS_SUBSCRIBER_STATUS.equals(characteristics.getName())) {
                            if (StringUtils.isNotEmpty(characteristics.getValue())) {
                                subscription.setSubscriberStatusForOcs(characteristics.getValue());
                                break;
                            }
                        }
                    subscription.getCfss().remove(cfs);
                    break;
                }
            }
        }
    }

    private void findEsbCallRequired(Subscription subscription) {
        if (subscription.getCfss() != null) {
            for (CFSRef cfs : subscription.getCfss()) {
                //if (EnrichmentConstants.CFS_M1_Base.equalsIgnoreCase(cfs.getName())) {
                cfs.getCharacteristics().stream()
                        .filter(characteristics -> GenericConstants.CHARACTERISTIC_EXTERNAL_OFFER_ID
                                .equals(characteristics.getName()))
                        .findFirst()
                        .ifPresent(externalOfferIdChar -> subscription.setExternalOfferId(externalOfferIdChar.getValue()));
                subscription.setEsbCallRqd(true);
                // }
            }
        }
    }

    private void findSomCallRequired(ProductOffering productOffering, Subscription subscription) {
        List<CFSRef> somEntities = new ArrayList<>();
        if (subscription.getCfss() != null)
            somEntities.addAll(subscription.getCfss());
        if (subscription.getPrss() != null)
            somEntities.addAll(subscription.getPrss());
        if (subscription.getLrss() != null)
            somEntities.addAll(subscription.getLrss());
        if (somEntities.isEmpty()) {
            subscription.setSomCallRqd(false);
            return;
        }

        var relationships = productOffering.getProductSpecifications().stream()
                .filter(product -> ObjectUtils.isNotEmpty(product.getRelationships()))
                .flatMap(spec -> spec.getRelationships().stream()).collect(Collectors.toList());
        if (!relationships.isEmpty()) {
            for (CFSRef somEntity : somEntities) {
                for (RelationShip relationship : relationships) {
                    if (relationship.getTargetObject() != null
                            && somEntity.getName().equals(relationship.getTargetObject().getName())) {
                        somEntity.setSkipSom(relationship.getSkipSOM() != null ? relationship.getSkipSOM() : "false");
                        break;
                    }
                    somEntity.setSkipSom("false");
                }
                if ("false".equals(somEntity.getSkipSom()) && !subscription.isSomCallRqd()) {
                    // setting som call required true if skipSom is false for at least one entity
                    subscription.setSomCallRqd(true);
                }
            }
        } else {
            subscription.setSomCallRqd(true);
        }

    }

    private void findTierBasedFlag(Subscription subscription, List<Charge> charges) {

        if (charges != null && subscription != null) {
            for (Charge charge : charges) {
                if (StringUtils.isNotEmpty(charge.getTierbased())) {
                    subscription.setTierbased(charge.getTierbased());
                    charge.setTierbased(null);
                }
            }
        }

    }

    protected void enrichCharges(List<Charge> charges, List<ProductOfferingPrice> productOfferingPrices) {
        if (productOfferingPrices != null) {
            List<Charge> newCharges = new ArrayList<>();
            for (ProductOfferingPrice price : productOfferingPrices) {
                newCharges.add(createNewCharge(price));
            }
            if (!newCharges.isEmpty())
                charges.addAll(newCharges);
        }
    }

    protected Charge createNewCharge(ProductOfferingPrice price) {
        var charge = new Charge();
        charge.setUpcChargeId(price.getId());
        charge.setUpcChargeName(price.getName());
        charge.setChargeName(price.getName());
        charge.setChargeDesc(price.getName());
        charge.setAmount(price.getPrice());
        charge.setRate(price.getPrice());
        if (price.getPriceCategory() != null) {
            if (price.getPriceCategory().getId() != null) {
                charge.setUpcPriceCategory(price.getPriceCategory().getId());
            }
            if (price.getPriceCategory().getBillingChargeType() != null
                    && price.getPriceCategory().getBillingChargeType().getId() != null) {
                charge.setChargeCategory(price.getPriceCategory().getBillingChargeType().getId());
            }
            if ("4".equalsIgnoreCase(price.getPriceCategory().getBillingChargeType().getId()) ||
                    "5".equalsIgnoreCase(price.getPriceCategory().getBillingChargeType().getId())) {

                charge.setChargeCategory("5");
                charge.setAttachmentLevel("2");
                charge.setDiscountLevel("1");
                charge.setDiscountValue(price.getPrice());
                charge.setAmount("0.0");
                if (price.getPriceType() != null && price.getPriceType().getId() != null
                        && price.getPriceType().getId().equals("1")) {
                    charge.setDiscountType("P");
                } else {
                    charge.setDiscountType("F");
                }
                try {
                    charge.setChargeStartDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern(GenericConstants.TIMESTAMP_FORMAT)));
                    charge.setChargeEndDate(CommonUtils.getExpiryDate(price.getFrequency().getName(), price.getFactor(), GenericConstants.SYSTEM_TIMESTAMP_FORMATTER));
                } catch (Exception e) {
                    log.info("SimpleDateFormat exception");
                }

            }
            if (price.getPriceCategory().getBillingChargeCategory() != null
                    && price.getPriceCategory().getBillingChargeCategory().getId() != null) {
                if ("1".equalsIgnoreCase(price.getPriceCategory().getBillingChargeCategory().getId())
                        || "2".equalsIgnoreCase(price.getPriceCategory().getBillingChargeCategory().getId())) {
                    charge.setBillingChargeType(price.getPriceCategory().getBillingChargeCategory().getId());
                }

            }

        }
        if (price.getPriceMode() != null) {
            if ("2".equals(price.getPriceMode().getId()))
                charge.setChargeType("2");
            else
                charge.setChargeType("1");
            charge.setChargeRecurranceType(charge.getChargeType());
        }
        if (price.getPriceCode() != null)
            charge.setChargeCode(price.getPriceCode().getCode());

        charge.setIsProrata(BooleanUtils.toBoolean(price.getProrataEnable()) ? "1" : "0");
        charge.setProrationFlag(getProrationFlag(price));
        charge.setChargeVersion(price.getVersionNo());
        if (price.getFrequency() != null)
            charge.setChargeFrequency(price.getFrequency().getId());
        charge.setChargeFactor(price.getFactor());
        if (ObjectUtils.isNotEmpty(price.getTierType()) && StringUtils.isNotEmpty(price.getTierType().getId())) {
            charge.setTierType(price.getTierType().getId());
        }
        if (StringUtils.isNotEmpty(price.getTierbased())) {
            charge.setTierbased(price.getTierbased());
        }
        if (StringUtils.isNotEmpty(price.getTierbased())
                && StringUtils.equalsIgnoreCase("true", price.getTierbased()) && ObjectUtils.isNotEmpty(price.getTiers())) {
            List<ChargeTier> chargeTierDetails = new ArrayList<ChargeTier>();
            for (Tier tier : price.getTiers()) {
                ChargeTier chargeTier = new ChargeTier();
                if (StringUtils.isNotEmpty(tier.getId()))
                    chargeTier.setId(tier.getId());
                if (StringUtils.isNotEmpty(tier.getName()))
                    chargeTier.setName(tier.getName());
                if (StringUtils.isNotEmpty(tier.getEndRange()))
                    chargeTier.setEndRange(tier.getEndRange());
                if (StringUtils.isNotEmpty(tier.getStartRange()))
                    chargeTier.setStartRange(tier.getStartRange());
                if (StringUtils.isNotEmpty(tier.getPrice()))
                    chargeTier.setPrice(tier.getPrice());
                if (StringUtils.isNotEmpty(tier.getFactor()))
                    chargeTier.setFactor(tier.getFactor());
                if (ObjectUtils.isNotEmpty(tier.getFrequency())
                        && StringUtils.isNotEmpty(tier.getFrequency().getId()))
                    chargeTier.setFrequency(tier.getFrequency().getId());
                chargeTierDetails.add(chargeTier);

            }
            if (ObjectUtils.isNotEmpty(chargeTierDetails)) {
                charge.setTiers(chargeTierDetails);

            }

        }
        return charge;
    }

    private String getProrationFlag(ProductOfferingPrice price) {
        var suspend = getIntegerValue(price.getProrataEnabledInSuspension());
        var terminate = getIntegerValue(price.getProrataEnabledInTermination());
        var planChange = getIntegerValue(price.getProrataEnabledInPlanChange());
        var activation = getIntegerValue(price.getProrataEnabledInActivation());


        StringBuilder prorationFlagBuilder = new StringBuilder();
        prorationFlagBuilder.append(suspend).append(terminate).append(planChange).append(activation);

        return prorationFlagBuilder.toString();
    }

    private Integer getIntegerValue(String prorata) {
        return BooleanUtils.toInteger(BooleanUtils.toBoolean(prorata));
    }

    private void enrichCfssPrssLrss(ProductOffering productOffering, Subscription subscription) {
        if (ObjectUtils.isNotEmpty(productOffering.getProductSpecifications())) {
            List<CFSRef> cfss = productOffering.getProductSpecifications().stream()
                    .filter(product -> ObjectUtils.isNotEmpty(product.getCfss())).flatMap(spec -> spec.getCfss().stream())
                    .collect(Collectors.toList());
            List<CFSRef> prss = productOffering.getProductSpecifications().stream()
                    .filter(product -> ObjectUtils.isNotEmpty(product.getPrss())).flatMap(spec -> spec.getPrss().stream())
                    .collect(Collectors.toList());
            List<CFSRef> lrss = productOffering.getProductSpecifications().stream()
                    .filter(product -> ObjectUtils.isNotEmpty(product.getLrss())).flatMap(spec -> spec.getLrss().stream())
                    .collect(Collectors.toList());
            if (!cfss.isEmpty() && (subscription.getCfss() == null || subscription.getCfss().isEmpty())) {
                subscription.setCfss(cfss);
            }
            if (!prss.isEmpty() && (subscription.getPrss() == null || subscription.getPrss().isEmpty())) {
                subscription.setPrss(prss);
            }
            if (!lrss.isEmpty() && (subscription.getLrss() == null || subscription.getLrss().isEmpty())) {
                subscription.setLrss(lrss);
            }
        }
    }

    private String findExpiryDate(ProductOffering productOffering, OrderFlowContext executionContext) {
        String expiryDate = null;
        String frequency = null;
        String factor = null;
        String renewalCount = null;

        boolean isRecurringPlan = false;
        for (ProductOfferingPrice price : productOffering.getProductOfferingPrice()) {
            if (ObjectUtils.isNotEmpty(price.getFrequency()) && ObjectUtils.isNotEmpty(price.getFrequency().getName())) {
                frequency = price.getFrequency().getName();
            }
            if (ObjectUtils.isNotEmpty(price.getFactor())) {
                factor = price.getFactor();
            }
            if (ObjectUtils.isNotEmpty(price.getRenewalCount())) {
                renewalCount = price.getRenewalCount();
            }

            if (price.getPriceMode() != null && "2".equals(price.getPriceMode().getId())) {
                // if the prices is recurring, it will have frequency and factor always.
                isRecurringPlan = true;
                break;
            }
        }
        if (isRecurringPlan) {
            if (frequency != null && factor != null && renewalCount != null) {
                expiryDate = getExpiryDatewithCount(frequency,
                        factor, GenericConstants.SYSTEM_TIMESTAMP_FORMATTER, renewalCount, executionContext);
            } else {
                // add 30 yrs to current date
                expiryDate = setDefaultExpiryDate(executionContext, GenericConstants.SYSTEM_TIMESTAMP_FORMATTER);
            }
        } else {
            if (frequency != null && factor != null) {
                expiryDate = getExpiryDate(frequency,
                        factor, GenericConstants.SYSTEM_TIMESTAMP_FORMATTER, executionContext);
            } else {
                expiryDate = setDefaultExpiryDate(executionContext, GenericConstants.SYSTEM_TIMESTAMP_FORMATTER);
            }
        }

        return expiryDate;
    }

    public static String getExpiryDate(String frequency, String factor, DateTimeFormatter formatter, OrderFlowContext orderEnrichmentContext) {
        String expiryDate = null;
        if (frequency != null && factor != null) {
            Calendar calendar = Calendar.getInstance();
            try {
                int days = Integer.parseInt(factor);
                calendar.setTime(Date.from(getStartDateForExpiryDateCalculation(orderEnrichmentContext, formatter).atZone(ZoneId.systemDefault()).toInstant()));
                switch (frequency) {
                    case "Daily", "daily", "Day", "Days":
                        calendar.add(Calendar.DATE, days);
                        break;
                    case "Week", "Weeks":
                        calendar.add(Calendar.DATE, days * 7);
                        break;
                    case "Month", "Monthly":
                        calendar.add(Calendar.MONTH, days);
                        break;
                    case "Year", "Yearly":
                        calendar.add(Calendar.YEAR, days);
                        break;
                    default:
                        break;
                }
                expiryDate = LocalDateTime.ofInstant(calendar.toInstant(), ZoneId.systemDefault()).format(formatter);
            } catch (Exception e) {
                log.error("Exception occurred in getExpiryDate!!! ", e);
            }
        } else {
            log.info("Unable to find the expiry date, frequency or factor is null");
        }
        return expiryDate;
    }

    private static LocalDateTime getStartDateForExpiryDateCalculation(OrderFlowContext orderEnrichmentContext, DateTimeFormatter formatter) {

        try {
            if (StringUtils.isNotEmpty(orderEnrichmentContext.getOrder().getRequestedStartDate())) {
                String requestedStartDate = orderEnrichmentContext.getOrder().getRequestedStartDate();
                LocalDateTime futureOrderDate = LocalDateTime.parse(requestedStartDate, formatter);
                if (futureOrderDate.isAfter(LocalDateTime.now()))
                    return futureOrderDate;
            }
        } catch (Exception e) {
            log.info("not able to parse RequestedStartDate.. hence returning current date");
            return LocalDateTime.now();
        }
        return LocalDateTime.now();
    }

    private String setDefaultExpiryDate(OrderFlowContext orderEnrichmentContext, DateTimeFormatter dateFormatter) {
        // TODO Auto-generated method stub
        int number = 30;
        var appConfig = cache
                .getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), "EXPIRYDATE_IN_YEARS");
        if (appConfig != null && (appConfig.getNgTableData().get("CONFIG_VALUE") != null)) {
            number = Integer.parseInt(appConfig.getNgTableData().get("CONFIG_VALUE"));
        }

        return getStartDateForExpiryDateCalculation(orderEnrichmentContext, dateFormatter).plusYears(number).format(GenericConstants.SYSTEM_TIMESTAMP_FORMATTER);
    }

    public static String getExpiryDatewithCount(String frequency, String factor, DateTimeFormatter formatter, String renewalCount, OrderFlowContext orderEnrichmentContext) {
        String expiryDate = null;
        if (frequency != null && factor != null) {
            Calendar calendar = Calendar.getInstance();
            try {
                int days = Integer.parseInt(factor);
                int count = Integer.parseInt(renewalCount);
                calendar.setTime(Date.from(getStartDateForExpiryDateCalculation(orderEnrichmentContext, formatter).atZone(ZoneId.systemDefault()).toInstant()));
                switch (frequency) {
                    case "Daily", "daily", "Day", "Days":
                        calendar.add(Calendar.DATE, days * count);
                        break;
                    case "Week", "Weeks":
                        calendar.add(Calendar.DATE, days * 7 * count);
                        break;
                    case "Month", "Monthly":
                        calendar.add(Calendar.MONTH, days * count);
                        break;
                    case "Year", "Yearly":
                        calendar.add(Calendar.YEAR, days * count);
                        break;
                    default:
                        break;
                }
                expiryDate = LocalDateTime.ofInstant(calendar.toInstant(), ZoneId.systemDefault()).format(formatter);
            } catch (Exception e) {
                log.error("Exception occurred in getExpiryDate!!! ", e);
            }
        } else {
            log.info("Unable to find the expiry date, frequency or factor is null");
        }
        return expiryDate;
    }

    private void setProductParameters(ProductOffering productOffering, Subscription subscription) {
        if (productOffering != null && subscription != null && productOffering.getProductOfferGroup() != null) {
            subscription.setProductId(productOffering.getProductOfferGroup().getId());
            subscription.setProductName(productOffering.getProductOfferGroup().getName());
            subscription.setProductDesc(productOffering.getProductOfferGroup().getDescription());
        }
    }

    private void validateUpcResponse(List<ProductOffering> productOfferings, ProductOffering productOfferingUpc, String saPlans) {
        if (productOfferingUpc == null) {
            throw new WorkflowTaskFailedException(activityId, "COM-005", "Unable to get sa plan details from upc for plan id " + saPlans);
        }
        var isBasePlan = productOfferings.stream()
                .filter(productOffering -> ObjectUtils.isNotEmpty(productOffering.getProductSpecifications())
                        && ObjectUtils.isNotEmpty(productOffering.getProductSpecifications().get(0).getType())
                        && StringUtils.isNotEmpty(productOffering.getProductSpecifications().get(0).getType().getId())
                        && productOffering.getProductSpecifications().get(0).getType().getId().equalsIgnoreCase("1"))
                .findAny().orElse(null);
        if (isBasePlan != null)
            throw new WorkflowTaskFailedException(activityId, "COM-005", "sa plan id is not an addon" + saPlans);
    }

    private void getSaPlanIdFromConfig(OrderFlowContext executionContext) {
        CacheTableDataDTO planMappingConfig = cache.getCacheDetailsFromDBMap(
                CacheConstants.CacheKeys.COM_NSA_SA_PLANID_MAPPING_CONFIG.toString(), executionContext.getEntityId());
        if (ObjectUtils.isNotEmpty(planMappingConfig)) {
            executionContext.getAttributes().put("saPlans", planMappingConfig.getNgTableData().get("SA_PLAN_ID"));
        }
    }


}
