package in.co.sixdee.bss.com.orderorchestrator.config.util.constants;

public enum TransportMethodType {
	GET("get"), POST("post"),DELETE("delete");

	private String name;

	private TransportMethodType(String name) {
		this.name = name;
	}

	public static final TransportMethodType nameOf(String name) throws IllegalArgumentException {
		for (TransportMethodType tp : values()) {
			if (tp.getName().equalsIgnoreCase(name)) {
				return tp;
			}
		}
		throw new IllegalArgumentException("No enum const TransportMethodType with name as " + name);
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}
}