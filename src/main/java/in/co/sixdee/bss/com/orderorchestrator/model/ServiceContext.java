package in.co.sixdee.bss.com.orderorchestrator.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Map;

@Getter
@Setter
public class ServiceContext implements Serializable {

    private static final long serialVersionUID = 1L;

    private String msisdn;
    private String iccid;
    private String imsi;
    private String pcrfCfss;
    private boolean isLnpDbTriggerRequired;
    private String mno;
    private Map<String,String> subscriptionIdMap;


}
