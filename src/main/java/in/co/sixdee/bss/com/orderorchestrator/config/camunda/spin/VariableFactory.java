package in.co.sixdee.bss.com.orderorchestrator.config.camunda.spin;

import org.camunda.bpm.engine.delegate.VariableScope;

/**
 * Typed variable factory.
 *
 * @param <T>
 *            type of the factory.
 */
public interface VariableFactory<T> {
	/**
	 * Creates a write adapter for variable scope.
	 *
	 * @param variableScope
	 *            underlying scope to work on.
	 * @return write adapter.
	 */
	ReadWriteAdapter<T> on(VariableScope variableScope);

	/**
	 * Creates a read adapter on variable scope.
	 *
	 * @param variableScope
	 *            underlying scope to work on.
	 * @return read adapter.
	 */
	ReadWriteAdapter<T> from(VariableScope variableScope);
}
