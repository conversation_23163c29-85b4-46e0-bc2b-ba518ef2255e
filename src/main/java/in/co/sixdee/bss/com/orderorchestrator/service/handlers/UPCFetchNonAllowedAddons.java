package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.common.util.CommonUtils;
import in.co.sixdee.bss.om.model.dto.order.CFSRef;
import in.co.sixdee.bss.om.model.dto.order.ProductOffering;
import in.co.sixdee.bss.om.model.dto.order.RelationShip;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.extern.log4j.Log4j2;

@Component(value = "upcFetchNonAllowedAddons")
@Log4j2
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class UPCFetchNonAllowedAddons extends AbstractDelegate {

	@Override
	protected void execute() throws Exception {
		String request = null;
		if (CommonUtils.INSTANCE.validateField(reqSpecKey)) {
			try {

				request = joltUtils.convert(reqSpecKey, executionContext.getOrder().getOrderType(),
						objectMapper.convertValue(executionContext, Map.class), executionContext.getAttributes());
			} catch (Exception e) {
				log.error("Exception occurred. unable to form the tp request {}", e.getMessage());
				executionContext.getErrorDetail().setCode("COM-001");
				executionContext.getErrorDetail().setMessage("Internal Error: error in forming tp request");
				executionContext.getErrorDetail().setSystem("COM");
				executionContext.setError(true);
				return;
			}
		}
		var callThirdPartyDTO = callThirdParty(request);
		if (callThirdPartyDTO == null) {
			executionContext.setError(true);
			return;
		}
		callThirdPartyDTO.getResponse();
		validateResponse(callThirdPartyDTO);
		if (executionContext.isError()) {
			return;
		}
		enrichPlans(callThirdPartyDTO.getResponse());
	}

	protected void enrichPlans(String response) {
		List<ProductOffering> productOfferings;
		try {
			productOfferings = objectMapper.readValue(response, new TypeReference<List<ProductOffering>>() {
			});

			if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData())
					&& executionContext.getWorkflowData().containsKey("nonEligibleAddons")) {
				var subscriptions = objectMapper.convertValue(executionContext.getWorkflowData().get("nonEligibleAddons"),
						new TypeReference<List<Subscription>>() {
						});
				productOfferings.forEach(productOffering -> {
					subscriptions.stream()
							.filter(subscription -> productOffering.getId().equalsIgnoreCase(subscription.getPlanId()))
							.forEach(subscription -> {
								enrichSubscription(subscription, productOffering);
							});
				});
				checkNonEligibleAddonsOperation(subscriptions);
			}
			workflowDataUpdated = true;

		} catch (JsonProcessingException e) {
			log.error("Error in Parsing UPC response of non-renewable ncc addons ", e);
		}

	}

	private void checkNonEligibleAddonsOperation(List<Subscription> subscriptions) {
		boolean smUpdateSub = false;
		Subscription cancelSub = null;
		Subscription stopRenewalSub = null;
		List<Subscription> cancelSubList = new ArrayList<>();
		List<Subscription> stopRenewalSubList = new ArrayList<>();
		List<Subscription> smUpdateSubList = new ArrayList<>();

		for (Subscription subscription : subscriptions) {
			if (subscription.isSomCallRqd()) {
				// If its network addon need to be cancelled in BS, NCC and SM.
				cancelSub = new Subscription();
				cancelSub.setSubscriptionId(subscription.getSubscriptionId());
				cancelSub.setPlanId(subscription.getPlanId());
				cancelSub.setSomCallRqd(subscription.isSomCallRqd());
				cancelSub.setEsbCallRqd(subscription.isEsbCallRqd());
				cancelSub.setExternalOfferId(subscription.getExternalOfferId());			
				cancelSubList.add(cancelSub);
			} else {
				smUpdateSub = true;
				if ("1".equalsIgnoreCase(subscription.getAutoRenewal())) {
					// if non-network addon and auto renewal is enabled, stop auto renewal in NCC and SM and BS
					stopRenewalSub = new Subscription();
					stopRenewalSub.setSubscriptionId(subscription.getSubscriptionId());
					stopRenewalSub.setPlanId(subscription.getPlanId());
					stopRenewalSub.setSomCallRqd(subscription.isSomCallRqd());
					stopRenewalSub.setEsbCallRqd(subscription.isEsbCallRqd());
					stopRenewalSub.setExternalOfferId(subscription.getExternalOfferId());			
					stopRenewalSubList.add(stopRenewalSub);
				} else {
					Subscription smUpdateOffer = new Subscription();
					smUpdateOffer.setSubscriptionId(subscription.getSubscriptionId());
					smUpdateOffer.setPlanId(subscription.getPlanId());
					smUpdateSubList.add(smUpdateOffer);
				}
			}
		}
		execution.setVariable("smUpdateSeqId", smUpdateSub);
		if(!smUpdateSubList.isEmpty())
			executionContext.getWorkflowData().put("smUpdateSubList", smUpdateSubList);
		var stopRenewal = !stopRenewalSubList.isEmpty();
		execution.setVariable("isStopRenewal", stopRenewal);
		if (stopRenewal) {
			var isNCCStopRenewal = stopRenewalSubList.stream().anyMatch(sub -> sub.isEsbCallRqd());
			execution.setVariable("isNCCStopRenewal", isNCCStopRenewal);
			executionContext.getWorkflowData().put("stopRenewalList", stopRenewalSubList);
		} else
			execution.setVariable("isNCCStopRenewal", false);
			
		var cancelList = !cancelSubList.isEmpty();
		execution.setVariable("isCancelSubscription", cancelList);
		if (cancelList) {
			var isNCCCancelSub = cancelSubList.stream().anyMatch(sub -> sub.isEsbCallRqd());
			execution.setVariable("isNCCCancelSub", isNCCCancelSub);
			executionContext.getWorkflowData().put("cancelSubscriptionList", cancelSubList);
		} else
			execution.setVariable("isNCCCancelSub", false);
		executionContext.getWorkflowData().put("nonEligibleAddons", subscriptions);
	}

	protected void enrichSubscription(Subscription subscription, ProductOffering productOffering) {
		findEsbCallRequired(subscription, productOffering);
		findSomCallRequired(productOffering, subscription);

	}

	private void findEsbCallRequired(Subscription subscription, ProductOffering productOffering) {
		var cfss = productOffering.getProductSpecifications().stream().map(ps -> ps.getCfss()).findAny().orElse(null);
		if (ObjectUtils.isNotEmpty(cfss)) {

			for (CFSRef cfs : cfss) {
				if (GenericConstants.CFS_M1_Base.equalsIgnoreCase(cfs.getName())) {
					cfs.getCharacteristics().stream()
							.filter(characteristics -> GenericConstants.CHARACTERISTIC_EXTERNAL_OFFER_ID
									.equals(characteristics.getName()))
							.findFirst().ifPresent(externalOfferIdChar -> subscription
									.setExternalOfferId(externalOfferIdChar.getValue()));
					subscription.setEsbCallRqd(true);
					break;
				}
			}
		}
	}

	private void findSomCallRequired(ProductOffering productOffering, Subscription subscription) {
		List<CFSRef> somEntities = new ArrayList<>();

		var cfss = productOffering.getProductSpecifications().stream()
				.filter(product -> ObjectUtils.isNotEmpty(product.getCfss())).flatMap(spec -> spec.getCfss().stream())
				.collect(Collectors.toList());
		if (ObjectUtils.isNotEmpty(cfss))
			somEntities.addAll(cfss);

		var prss = productOffering.getProductSpecifications().stream()
				.filter(product -> ObjectUtils.isNotEmpty(product.getPrss())).flatMap(spec -> spec.getPrss().stream())
				.collect(Collectors.toList());
		if (ObjectUtils.isNotEmpty(prss))
			somEntities.addAll(prss);

		var lrss = productOffering.getProductSpecifications().stream()
				.filter(product -> ObjectUtils.isNotEmpty(product.getLrss())).flatMap(spec -> spec.getLrss().stream())
				.collect(Collectors.toList());
		if (ObjectUtils.isNotEmpty(lrss))
			somEntities.addAll(lrss);

		if (somEntities.isEmpty()) {
			subscription.setSomCallRqd(false);
			return;
		}

		var relationships = productOffering.getProductSpecifications().stream()
				.filter(product -> ObjectUtils.isNotEmpty(product.getRelationships()))
				.flatMap(spec -> spec.getRelationships().stream()).collect(Collectors.toList());
		if (!relationships.isEmpty()) {
			for (CFSRef somEntity : somEntities) {
				for (RelationShip relationship : relationships) {
					if (relationship.getTargetObject() != null
							&& somEntity.getName().equals(relationship.getTargetObject().getName())) {
						somEntity.setSkipSom(relationship.getSkipSOM() != null ? relationship.getSkipSOM() : "false");
						break;
					}
					somEntity.setSkipSom("false");
				}
				if ("false".equals(somEntity.getSkipSom()) && !subscription.isSomCallRqd()) {
					// setting som call required true if skipSom is false for at least one entity
					subscription.setSomCallRqd(true);
				}
			}
		} else {
			subscription.setSomCallRqd(true);
		}
	}

	protected List<Subscription> getSubscriptions(Object subscription) {
		List<Subscription> subscriptions = new ArrayList<>();
		try {
			var response = in.co.sixdee.bss.common.util.JsonUtils.marshall(subscription, null);
			subscriptions = objectMapper.readValue(response, new TypeReference<List<Subscription>>() {
			});
		} catch (Exception e) {
			log.error(" :::::  Exception occured in getSubscriptions execute method  :::::", e);
		}

		return subscriptions;
	}

}
