package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Log4j2
@Component(value = "fetchOldSubscriptionDetails")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class BSFetchOldSubscriptionDetails extends AbstractDelegate {

    @Override
    protected void execute() throws Exception {
        String request = getRequestFromSpec();
        if (executionContext.isError())
            return;

        var callThirdPartyDTO = callThirdParty(request);
        if (callThirdPartyDTO == null) {
            executionContext.setError(true);
            return;
        }
        var response = callThirdPartyDTO.getResponse();
        validateResponse(callThirdPartyDTO);
        if (executionContext.isError())
            return;
        getSubscriptionIdAndPlanId(response);
        modifyWorkflowData(response);
        workflowDataUpdated = true;
    }

    private void getSubscriptionIdAndPlanId(String response) throws JsonProcessingException {
        List<Subscription> bsSubscriptions = new ArrayList<>();
        LinkedHashMap<String, String> subscriptionIdPlanIdMap = new LinkedHashMap<>();
        var billResp = objectMapper.readTree(response);
        if (billResp.get("data") != null && billResp.get("data").get(0) != null) {
            bsSubscriptions = objectMapper.readValue(objectMapper.writeValueAsString(billResp.get("data")), new TypeReference<List<Subscription>>() {
            });
            for (Subscription subscription : bsSubscriptions) {
                log.info("sdsdsadas" +subscription.getPlanId() +"mmmmmmmm"+subscription.getSubscriptionId());
                if (StringUtils.isNotEmpty(subscription.getPlanId()) && StringUtils.isNotEmpty(subscription.getSubscriptionId())) {
                    subscriptionIdPlanIdMap.put(subscription.getSubscriptionId(), subscription.getPlanId());
                }
            }
        }
        if (ObjectUtils.isNotEmpty(subscriptionIdPlanIdMap)) {
            executionContext.getWorkflowData().put("subscriptionIdPlanIdMap", subscriptionIdPlanIdMap);
        }
    }
}
