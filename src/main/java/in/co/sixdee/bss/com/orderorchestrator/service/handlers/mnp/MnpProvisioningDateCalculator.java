package in.co.sixdee.bss.com.orderorchestrator.service.handlers.mnp;

import in.co.sixdee.bss.com.orderorchestrator.config.util.MNPServiceUtil;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.AppConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Log4j2
@Component(value = "mnpProvisioningDateCalculator")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class MnpProvisioningDateCalculator extends AbstractDelegate {

    private final MNPServiceUtil mnpServiceUtil;

    @Override
    protected void execute() throws Exception {
        if ("MNPDeProvDateCalculator".equals(activityId)) {
            String deprovisioningDate = calculateNextDeProvisioningDateTime();
            execution.setVariable(AppConstants.ProcessVariables.MNP_DEPROVISIONING_DATE.value(), deprovisioningDate);
            executionContext.getAttributes().put(AppConstants.ProcessVariables.MNP_DEPROVISIONING_DATE.value(), deprovisioningDate);
        } else if ("MNPProvDateCalculator".equals(activityId)) {
            String provisioningDate = calculateNextProvisioningDateTime();
            execution.setVariable(AppConstants.ProcessVariables.MNP_PROVISIONING_DATE.value(), provisioningDate);
            executionContext.getAttributes().put(AppConstants.ProcessVariables.MNP_PROVISIONING_DATE.value(), provisioningDate);
        }
        workflowDataUpdated = true;
    }

    public String calculateNextProvisioningDateTime() {
        String provisioningHour = MNPConstants.MNP_PORTIN_MVNO_MVNO_PROV_HOUR;
        CacheTableDataDTO provisioningDateConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.MNP_PORTIN_MVNO_MVNO_PROV_HOUR.toString());
        if (provisioningDateConfig != null) {
            provisioningHour = provisioningDateConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
        }
        LocalDateTime provisioningDateTime = mnpServiceUtil.findTargetDateTime(provisioningHour);
        log.info("Target date for provisioning {}", provisioningDateTime);
        return provisioningDateTime.toString();
    }

    public String calculateNextDeProvisioningDateTime() {
        String deProvisioningHour = MNPConstants.MNP_PORTIN_MVNO_MVNO_DEPROV_HOUR;
        CacheTableDataDTO deProvisioningDateConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.MNP_PORTIN_MVNO_MVNO_DEPROV_HOUR.toString());
        if (deProvisioningDateConfig != null) {
            deProvisioningHour = deProvisioningDateConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
        }
        LocalDateTime deProvisioningDateTime = mnpServiceUtil.findTargetDateTime(deProvisioningHour);
        log.info("Target date for de provisioning {}", deProvisioningDateTime);
        return deProvisioningDateTime.toString();
    }

    public LocalDateTime findTargetDate(int targetHour) {
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalTime targetTime = LocalTime.of(targetHour, 0);
        LocalDateTime targetDateTime = currentDateTime.with(targetTime);
        if (currentDateTime.isAfter(targetDateTime)) {
            targetDateTime = targetDateTime.plusDays(1);
        }
        return targetDateTime;
    }
}
