package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import java.util.List;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import in.co.sixdee.bss.om.model.dto.WorkFlowErrorBean;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SchemaException extends RuntimeException {

	private static final long		serialVersionUID	= 1L;

	private StatusConstants.HttpConstants status;
	private String                        message;
	private List<WorkFlowErrorBean>	errors;

	public SchemaException(StatusConstants.HttpConstants status, String message, List<WorkFlowErrorBean> errors) {
		super();
		this.status = status;
		this.message = message;
		this.errors = errors;
	}

	public SchemaException(StatusConstants.HttpConstants status, String message) {
		super();
		this.status = status;
		this.message = message;
	}

	public SchemaException(String msg) {
		super();
		this.message = msg;
	}

}
