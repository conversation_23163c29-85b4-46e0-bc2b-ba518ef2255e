package in.co.sixdee.bss.com.orderorchestrator;

import lombok.extern.log4j.Log4j2;
import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;

import java.util.Map;

@Log4j2
public class MDCTaskDecorator implements TaskDecorator {
	@Override
	public Runnable decorate(Runnable runnable) {

		Map<String, String> contextMap = MDC.getCopyOfContextMap();
		return () -> {
			try {
				MDC.setContextMap(contextMap);
				runnable.run();
			} finally {
				MDC.clear();
			}
		};

	}
}
