package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.Hibernate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "COM_ORDER_MASTER", uniqueConstraints = @UniqueConstraint(columnNames = {"ORDER_ID"}))
@lombok.Generated
public class OrderEntity extends AbstractAuditingEntity implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ORDER_ID", length = 20, unique = true)
    private Long orderId;

    @Column(name = "PARENT_ORDER_ID", length = 20)
    private Long parentOrderId;

    @Column(name = "EXTERNAL_ID", length = 50)
    @Value("NULL")
    private String externalId;

    @Column(name = "ORDER_TYPE", length = 50)
    @Value("NULL")
    private String type;


    @Column(name = "DESCRIPTION", length = 300)
    @Value("NULL")
    private String description;

    @Column(name = "ORDER_STATE", length = 20)
    private String state;

    @Column(name = "STATE_REASON", length = 50)
    @Value("NULL")
    private String stateReason;

    @Column(name = "CHANNEL", length = 30)
    private String channel;

    @Column(name = "CUSTOMER_ID", length = 50)
    private String customerId;

    @Column(name = "BILLING_ACCOUNT_ID", length = 50)
    private String billingAccountId;

    @Column(name = "SERVICE_ID", length = 80)
    private String serviceId;

    @Column(name = "ORDER_DATE", columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP", insertable = false, updatable = false)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date orderDate;

    @Column(name = "COMPLETION_DATE", columnDefinition = "TIMESTAMP", insertable = false, updatable = false)
    private Date completionDate;

    @Column(name = "REQUESTED_COMPLETION_DATE", columnDefinition = "TIMESTAMP", insertable = false, updatable = false)
    private Date requestedCompletionDate;

    @Column(name = "CANCELLATION_DATE", columnDefinition = "TIMESTAMP", insertable = false, updatable = false)
    private Date cancellationDate;

    @Column(name = "ORDER_CHARGES", length = 20)
    private String orderCharges;

    @Column(name = "PAYMENT_CHANNEL", length = 20)
    private String paymentChannel;

    @Column(name = "CATEGORY", length = 50)
    private String category;

    @Column(name = "ENTITY_ID", length = 50)
    private String entityId;

    @Column(name = "USERNAME", length = 100)
    private String username;
    
    @Column(name="SA_FORM_ID",length=50)
	private String  saFormId;
    
	@Column(name = "CUSTOMER_NAME", length = 50)
	private String customerName;

	@Column(name = "BATCH_ID", length = 20)
	private Long batchId;
	
    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
            return false;
        OrderEntity orderEntity = (OrderEntity) o;

        return Objects.equals(orderId, orderEntity.orderId);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    public OrderEntity(Long orderId, String customerId, String billingAccountId,String state,String type,String entityId) {
        this.orderId = orderId;
        this.customerId = customerId;
        this.billingAccountId = billingAccountId;
        this.state = state;
        this.type = type;
        this.entityId = entityId;
    }
}
