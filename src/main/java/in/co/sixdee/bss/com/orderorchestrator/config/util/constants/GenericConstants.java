package in.co.sixdee.bss.com.orderorchestrator.config.util.constants;

import java.time.format.DateTimeFormatter;

public class GenericConstants {

    public static final String TIMESTAMP_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    public static final String ORDER_ID = "orderId";
    public static final String SUB_ORDER_ID = "subOrderId";
    public static final String CHANNEL = "channel";
    public static final String ORDER_TYPE = "orderType";
    public static final String TIMESTAMP = "timestamp";
    public static final String PROFILE_ID = "profileId";
    public static final String ACCOUNT_ID = "accountId";
    public static final String SERVICE_ID = "serviceId";
    public static final String INTERNAL_SERVICE_ID = "internalServiceId";
    public static final String PLAN_ID = "planId";
    public static final String SUBSCRIPTION_ID = "subscriptionId";
    public static final String PROCESS_ID = "processId";
    public static final String OPERATION_ID = "operationId";
    public static final String ACTION = "action";
    public static final String WORKITEM_REQUEST_ID = "workitemRequestId";
    public static final String CALL_BACK_REQUEST = "callBackRequest";
    public static final String NOTIFICATION_EMAIL = "EMAIL";
    public static final String NOTIFICATION_SMS = "SMS";

    public static final String HTTP_STATUS_SERVICE_UNAVAILABLE = "500";
    public static final String HTTP_STATUS_REQUEST_TIMEOUT = "503";
    public static final String HTTP_STATUS_NOT_FOUND = "404";
    public static final String NCC_CREATE_SERVICE_HANDLER = "NCCCreateService";
    public static final String NCC_SUBSCRIBER_ACTIVATION_HANDLER = "NCC_ServiceActivation";
    public static final String NCC_CREATE_SUBSCRIPTION_HANDLER = "NCCCreateSubscription";
    public static final String SMCreateSubscription = "SMCreateSubscription";
    public static final String ESB_CHARACTERISTIC_TYPE_SUBSCRIPTION_ID = "subscriptionId";
    public static final String NCC_CREATE_WELCOME_SUBSCRIPTION = "NCCCreateWelcomeSubscription";
    public static final String NCC_CONNECTION_MIGRATION = "NCCConnectionMigration";
    public static final String BS_CREATE_SERVICE = "BSCreateService";
    public static final String BS_ADD_SUBSCRIPTION = "BSAddSubscription";
    public static final String ALLOWED_PLAN_IDS = "allowedPlanIds";
    public static final String CHARACTERISTIC_SOM_CALL_REQUIRED = "somCallRequired";
    public static final String PLAN_TYPE_BASE = "1";
    public static final String PLAN_TYPE_ADDON = "0";
    public static final String CFS_M1_Base = "CFS_M1_Base";
    public static final String CFS_UPC = "CFSS_UPC";
    public static final String LRS_PORTIN= "LRS_PORTIN";
    public static final String CHARACTERISTIC_EXTERNAL_OFFER_ID = "OCSOfferID";
    public static final String CHARACTERISTIC_IS_RENEWAL = "IsRenewal";
    public static final String CALLBACK_RETRY_SCHEDULER_ENABLE = "callback.retry.scheduler.enable";
    public static final String CAMUNDA_METRICS_ENABLED = "camunda.bpm.metrics.enabled";
    public static final String NCC_CREATE_WELCOME_SUBSCRIPTION_ORDERTYPE = "Onboarding,AddService,AddServiceToNewAccount";
    public static final String UPDATE_STARTER_PACK_KYC = "UpdateStarterPackKYC";
    public static final String CREATE_SERVICE_ORDER_TYPES = "Onboarding,AddService,AddServiceToNewAccount";
    public static final String PLAN_IDS = "planIds";
    public static final String CANCEL_ORDER_POLLER_ENABLE = "cancelOrder.poller.enable";
    public static final String CHANGEPLAN_ORDER_ID = "changePlanOrderId";
    public static final String CHANGEPLAN_SUB_ORDER_ID = "changePlanSubOrderId";
    public static final String DUNNING_POLLER_CHANNEL_VALUE = "Dunning-poller";

    public static final String ORDER_DATE = "orderDate";
    public static final String SUB_ORDER_COMPLETION_DATE = "subOrderCompletionDate";

    public static final String CONST_UNDERSCORE_ = "_";
    public static final String ACTIVITY_TYPE_SERVICE_TASK = "serviceTask";
    public static final String ACTIVITY_TYPE_SUB_PROCESS = "subProcess";
    public static final String ACTIVITY_TYPE_END_EVENT = "endEvent";
    public static final String NCC_UPDATE_CREDITLIMIT_HANDLER = "NCCUpdateCreditLimit";
    public static final String BS_UPDATE_CREDITLIMIT_HANDLER = "BSUpdateCreditLimit";
    public static final String BS_BOOKDEPOSIT_HANDLER = "BSBookDeposit";
    public static final String USSD_CHANNEL = "USSD";
    public static final String PROFILE_LEVEL_ORDER_ENRICHMENT_TYPES = "UpdateProfile,UpdateProfileAddress,CreateIdentification,DeleteIdentification,UpdateIdentification,AttachContact,DetachContact,CreateDocumentProfile,DeleteDocumentProfile,UpdateDocumentProfile";
    public static final String ROLLBACK_NCC_UPDATE_MA_BALANCE = "RollbackNCCAdjustMABalance";
    public static final String SOM_FETCH_SERVICE_REGISTRY = "SOMFetchServiceRegistry";

    public static final String REFERENCE_ID = "portingReferenceId";
    public static final String CHANGE_SUBSCRIPTION = "ChangeSubscription";
    public static final String BS_VIEW_GROUP_SUBSCRIPTION = "BS_ViewGroupSubscription";
    public static final String MNPPortIn = "MNPPortIn";

    public static final String TAG_OPERATOR = "operator";

    public static final String OPERATOR_HIFEN = "-";

    public static final String OPERATOR_UNDERSCORE = "_";

    public static final String OPERATOR_COMMA = ",";

    public static final String OPERATOR_PIPE = "|";
    public static final String OPERATOR_DOLLAR = "$";
    public static final String OPERATOR_AND = "$";

    public static final String ENTITY_ID    = "entityId";

    public static final String OPERATOR    = "entityId";
    public static final String NMS_FETCH_ASSET = "NMSFetchAsset";

    public static final String ORDER_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String CHANNEL_ID_FOR_CRM="CRM";
	public static final String SOM_DATE_FORMAT = "dd-MM-yyyy HH:mm:ss";

    public static final String CALL_BACK_STATUS_NON_PROCESSED = "0";

    public static final String CALL_BACK_STATUS_PROCESSED = "1";

    public static final String SIM_DELIVERY_STAGE_ID = "SIM_DELIVERY_CALLBACK";

    public static final String CALLBACK_AUDIT_SCHEDULER_ENABLE = "callback.audit.scheduler.enabled";

   public static final String CHARACTERISC_NAME_FOR_SIM_TYPE = "SIM_TYPE";
    
    public static final String CHARACTERISC_VALUE_FOR_ESIM = "E-SIM";
    
    public static final String CHARACTERISC_NAME_FOR_ICCID = "ICCID";

    public static final String ARM_THIRD_PARTY_SYSTEM_NAME = "ARM";
    public static final String ARM_FAILURE_MESSAGE = "Failed";
    public static final String IMSI = "imsi";
    public static final String CALLBACK_EXTENSION_PROPERTY_NAME = "callBackType";

    public static final DateTimeFormatter SYSTEM_TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern(TIMESTAMP_FORMAT);

    public static final String			CFSS_SUBSCRIBER_LIFECYCLE									= "CFSS_SUBSCRIBER_LIFECYCLE";
    public static final String			CFSS_SUBSCRIBER_STATUS             							= "SubscriberStatus";

}
