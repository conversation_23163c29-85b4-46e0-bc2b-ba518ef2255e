package in.co.sixdee.bss.com.orderorchestrator.core.uniquenumgen;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "unique-number-generator")
@Getter
@Setter
public class UniqueNumberGeneratorProperties {

    private String strategy = "mysql";
    private String redisPrefix = "com-unique-number:";
    private String mysqlTableName = "COM_UNIQUE_NUMBER_SEQ";
    private int numberLength = 4;
    private long retryInterval = 100;
    private int maxRetries = 3;
}
