package in.co.sixdee.bss.com.orderorchestrator.config.notification;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;
import in.co.sixdee.bss.com.orderorchestrator.config.connector.rest.WebClientConnector;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.jolt.JoltUtils;
import in.co.sixdee.bss.common.util.ApplicationContextUtils;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.notification.NotificationDTO;
import in.co.sixdee.bss.om.model.dto.notification.NotificationDTO.NotificationToken;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.web.reactive.function.client.WebClientRequestException;

import java.net.URI;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.util.*;

@Log4j2
public class NotificationBO {

	protected GetDataFromCache			cache			= ApplicationContextUtils.getBean(GetDataFromCache.class);

	protected WebClientConnector		restConnector	= ApplicationContextUtils.getBean(WebClientConnector.class);

	protected JoltUtils					joltUtils		= ApplicationContextUtils.getBean(JoltUtils.class);

	protected HashMap<String, String>	thirdPartyCallDetails;

	protected List<NotificationToken>	tokenParams		= null;

	protected ObjectMapper				objectMapper	= ApplicationContextUtils.getBean(ObjectMapper.class);

	public void sendNotification(NotificationDTO notificationDTO) {
		String request = null;
		String specKey = null;
		try {
			setTokensToNotificationDTO(notificationDTO);
			if (ObjectUtils.isNotEmpty(notificationDTO) && MapUtils.isNotEmpty(notificationDTO.getWorkflowData())
					&& ObjectUtils.isNotEmpty(notificationDTO.getWorkflowData().get("keyword"))) {
				var deliveryType = String.valueOf(notificationDTO.getWorkflowData().get("keyword"));

				if (deliveryType.equalsIgnoreCase("SMS")) {
					if (ObjectUtils.isNotEmpty(notificationDTO.getWorkflowData().get("notificationReqSpecKey"))) {
						specKey = String.valueOf(notificationDTO.getWorkflowData().get("notificationReqSpecKey"));
					} else {
						if (notificationDTO.isSpocEnabled())
							specKey = "NGW_SPOC_SMS_Notification";
						else
							specKey = "NGW_SMS_Notification";
					}
					initThirdPartyCallDetails("ngw-sms-notification");

				} else {
					if (ObjectUtils.isNotEmpty(notificationDTO.getWorkflowData().get("notificationReqSpecKey"))) {
						specKey = String.valueOf(notificationDTO.getWorkflowData().get("notificationReqSpecKey"));
					} else {
						if (notificationDTO.isSpocEnabled())
							specKey = "NGW_SPOC_EMAIL_Notification";
						else
							specKey = "NGW_EMAIL_Notification";
					}
					initThirdPartyCallDetails("ngw-email-notification");
				}
			}
			request = joltUtils.convert(specKey, notificationDTO.getOrder().getOrderType(),
					objectMapper.convertValue(notificationDTO, Map.class), notificationDTO.getAttributes());
			var callThirdParty = callThirdParty(request, notificationDTO);
			var response = callThirdParty.getResponse();
			log.info("Received response :: {}", response);

		} catch (Exception e) {
			log.error("Exception occurred. unable to form the tp request {}", e.getMessage());
		}
	}

	protected void setTokensToNotificationDTO(NotificationDTO notificationDTO) {
		tokenParams = new ArrayList<>();
		var tokenList = cache.getCacheDetailsFromDBMapAryList(NGTableConstants.CACHE_KEY_COM_TOKEN_DESCRIPTION_MASTER,
				notificationDTO.getOrder().getOrderType());
		var messageTokens = notificationDTO.getAttributes().get("messageTokens");
		if (StringUtils.isEmpty(messageTokens))
			return;
		var split = StringUtils.split(messageTokens, ",");
		for (String str : split) {
			if (ObjectUtils.isEmpty(tokenList)) {
				log.info(notificationDTO.getTraceId() + " | no tokens are configured for the order type "
						+ notificationDTO.getOrder().getOrderType());
				return;
			}
			for (CacheTableDataDTO tokenDescription : tokenList) {
				var token = tokenDescription.getNgTableData().get("TOKEN");
				if (str.equalsIgnoreCase(token)) {
					var tokenPath = tokenDescription.getNgTableData().get("TOKEN_PATH");
					try {
						if (tokenPath.startsWith("val"))
							resolveValExpression(token, tokenPath, notificationDTO);
						else {
							var tokenValue = JsonPath.read(objectMapper.writeValueAsString(notificationDTO), tokenPath);
							if (tokenValue instanceof String || tokenValue instanceof Integer) {
								notificationDTO.getAttributes().put(token, String.valueOf(tokenValue));
							} else if (tokenValue instanceof List<?>) {
								notificationDTO.getAttributes().put(token, String.valueOf(tokenValue).replace("[", "")
										.replace("]", "").trim());
							} else {
								log.info(" ignoring token {}, as it is not a primitive", token);
							}
						}
					} catch (PathNotFoundException e) {
						log.warn("Warning Path : " + tokenPath + ", not found!!!" + e);
					} catch (JsonProcessingException jpe) {
						log.warn("Exception occured in setTokensToNotificationDTO  : " + jpe);
					}

				}
			}

		}
		parseMessageTokens(notificationDTO);
	}

	private void resolveValExpression(String token, String tokenPath, NotificationDTO notificationDTO) {
		if (token == null || tokenPath == null)
			return;
		var valPath = tokenPath.substring(tokenPath.indexOf("(") + 1, tokenPath.indexOf(")"));
		if (notificationDTO.getAttributes().containsKey(valPath)) {
			notificationDTO.getAttributes().put(token, notificationDTO.getAttributes().get(valPath));
		}else {
			log.info(" ignoring token {}, as it is not found", token);

		}

	}

	protected void parseMessageTokens(NotificationDTO notificationDTO) {
		String messageTokens = null;
		HashMap<String, Object> executionDataMap = null;
		messageTokens = notificationDTO.getAttributes().get("messageTokens");
		if (StringUtils.isNotEmpty(messageTokens)) {
			String[] split = StringUtils.split(messageTokens, ",");
			for (String str : split) {
				if (StringUtils.isNotEmpty(notificationDTO.getAttributes().get(StringUtils.trim(str)))) {
					var tokenParam = new NotificationToken();
					tokenParam.setId(StringUtils.trim(str));
					if (str.equalsIgnoreCase("LANGUAGE")
							&& notificationDTO.getOrder().getOrderType().equalsIgnoreCase(OrderTypes.UPDATE_LANGUAGE)) {
						var language = setLanguageToken(notificationDTO.getAttributes().get(StringUtils.trim(str)),
								notificationDTO);
						tokenParam.setValue(language);
					} else if(str.equalsIgnoreCase("CONNECTION_TYPE")|| str.equalsIgnoreCase("DEST_CONT_TYPE")|| str.equalsIgnoreCase("SOURCE_CONT_TYPE")){ 
						var connectionType = setConnectionTypeToken(notificationDTO.getAttributes().get(StringUtils.trim(str)),
								notificationDTO);
						tokenParam.setValue(connectionType);
					} else {
						var tokenValue = notificationDTO.getAttributes().get(StringUtils.trim(str));
						tokenParam.setValue(tokenValue);
						if (str.equalsIgnoreCase("SERVICE_ID")) {
							var tokens = setMsisdnTokenWithoutCountryCode(tokenValue);
							if (ObjectUtils.isNotEmpty(tokens))
								tokenParams.add(tokens);
						}
					}
					tokenParams.add(tokenParam);
				}

			}
		}

		if (ObjectUtils.isNotEmpty(tokenParams)
				&& MapUtils.isNotEmpty((HashMap<String, Object>) notificationDTO.getWorkflowData())) {
			executionDataMap = (HashMap<String, Object>) notificationDTO.getWorkflowData();
			try {
				executionDataMap.put("notificationParams",
						JsonUtils.jsonToObject(in.co.sixdee.bss.common.util.JsonUtils.marshall(tokenParams, null)));
			} catch (Exception e) {
				throw new CommonException("couldn't obtain notificationDTO");
			}
		}

	}
	
	private NotificationToken setMsisdnTokenWithoutCountryCode(String serviceId) {
		String msisdnCC = null;
		String msisdnLen = null;
		String msisdnPrefix = null;

		var msisdnCcConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
				"MSISDN_COUNTRY_CODE");
		if (ObjectUtils.isNotEmpty(msisdnCcConfig))
			msisdnCC = msisdnCcConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());

		var msisdnLengthConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
				"MSISDN_LENGTH_WITH_COUNTRY_CODE");
		if (ObjectUtils.isNotEmpty(msisdnLengthConfig))
			msisdnLen = msisdnLengthConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());

		var msisdnPrefixConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
				"MSISDN_PREFIX_FOR_NOTIFICATION");
		if (ObjectUtils.isNotEmpty(msisdnPrefixConfig))
			msisdnPrefix = msisdnPrefixConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());

		if (StringUtils.isNotEmpty(msisdnLen) && serviceId.length() >= Integer.valueOf(msisdnLen)) {

			if (StringUtils.isNotEmpty(msisdnCC) && StringUtils.startsWith(serviceId, msisdnCC))
				serviceId = serviceId.substring(msisdnCC.length());

			if (StringUtils.isNotEmpty(msisdnPrefix))
				serviceId = msisdnPrefix + serviceId;
			var param = new NotificationToken();
			param.setId("MSISDN_NO_CC");
			param.setValue(serviceId);
			return param;
		}
		return null;

	}

	private String setConnectionTypeToken(String connectionType, NotificationDTO notificationDTO) {
		var connection = "";
		var orderTypes = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
				"ORDERTYPES_FOR_CONNECTION_TYPE_VALUES");
		var orderType = orderTypes.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
		if (Arrays.asList(orderType.split(",")).contains(notificationDTO.getOrder().getOrderType())) {
			var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
					"CONNECTION_TYPE_" + connectionType);
			connection = appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
			return connection;
		}
		return null;
	}

	protected String setLanguageToken(String languageId, NotificationDTO notificationDTO) {
		var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), languageId);
		var language = appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
		return language;
	}

	protected void initThirdPartyCallDetails(String thirdPartyId) throws CommonException {
		var thirdPartyUrlConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(),
				thirdPartyId);
		if (Objects.isNull(thirdPartyUrlConfig))
			throw new ConfigurationNotValidException("url configuration not found for the third party id :: " + thirdPartyId);
		thirdPartyCallDetails = thirdPartyUrlConfig.getNgTableData();
	}

	protected CallThirdPartyDTO callThirdParty(String request, NotificationDTO notificationDTO) throws URISyntaxException {

		var headerMap = getHeaderMap(thirdPartyCallDetails.get(CacheConstants.CacheFields.HEADERS.name()));
		if (headerMap != null) {
			OrderFlowContext orderFlowContext= new OrderFlowContext();
			orderFlowContext.setEntityId(notificationDTO.getEntityId());
			orderFlowContext.setChannel(notificationDTO.getChannel());
			orderFlowContext.setTraceId(notificationDTO.getTraceId());
			orderFlowContext.setOrder(notificationDTO.getOrder());
			headerMap=transformHeaders(headerMap,orderFlowContext);	
		}
		/*
		 * thirdPartyCallDetails. setHeaderMap(transformHeaders(headerMap));
		 */
		var uri = thirdPartyCallDetails.get(CacheConstants.CacheFields.URL.name());
		var readTimeout = NumberUtils.toInt(thirdPartyCallDetails.get(CacheConstants.CacheFields.READ_TIMEOUT.name()), 3000);
		var connectionTimeout = NumberUtils.toInt(thirdPartyCallDetails.get(CacheConstants.CacheFields.CONNECTION_TIMEOUT.name()),
				3000);
		var transportMethod = thirdPartyCallDetails.get(CacheConstants.CacheFields.TRANSPORT_METHOD.name());
		try {
			return restConnector.service(transportMethod, new URI(uri), readTimeout, connectionTimeout, request, headerMap);
		} catch (WebClientRequestException e) {
			log.info(e.getMessage());
		}
		return null;
	}

	public Map<String, String> getHeaderMap(String headerParams) {
		Map<String, String> headerMap = null;
		try {
			if (StringUtils.isNotEmpty(headerParams)) {
				headerMap = new HashMap<String, String>();
				String[] headerParam = headerParams.split(",");
				for (String header : headerParam) {
					if (header.contains("=")) {
						String name = null, value = null;
						try {
							name = header.split("=")[0];
							value = header.split("=")[1];
							headerMap.put(name, value);
						} catch (Exception e) {
							throw e;
						}
					}
				}
			}
		} catch (Exception e) {
			throw e;
		}
		return headerMap;
	}
	
	protected Map<String, String> transformHeaders(Map<String, String> headerMap, OrderFlowContext orderFlowContext) {
		Set<Map.Entry<String, String>> entrySet = null;
		Map<String, String> newMap = new HashMap<>();
		String path = null;
		String value = null;
		try {
			var docContext = JsonPath.parse(objectMapper.writeValueAsString(orderFlowContext));
			entrySet = headerMap.entrySet();
			for (Map.Entry<String, String> entry : entrySet) {
				if (entry.getValue().startsWith("@(") && entry.getValue().endsWith(")")) {
					path = entry.getValue().substring(2, entry.getValue().length() - 1);
					try {
						value = docContext.read(path);
					} catch (PathNotFoundException e) {
						log.warn("Path {} not found!!!", path);
					}
					if (ObjectUtils.isNotEmpty(value))
						newMap.put(entry.getKey(), value);
				} else if (entry.getValue().startsWith("{") && entry.getValue().endsWith("}")) {
					newMap.put(entry.getKey(),
							orderFlowContext.getAttributes().get(entry.getValue().substring(1, entry.getValue().length() - 1)));
				} else if (entry.getValue().startsWith("date(") && entry.getValue().endsWith(")")) {
					String timestamp = null;
					String format = entry.getValue().substring(5, entry.getValue().length() - 1);
					if (StringUtils.isNotEmpty(format)) {
						if (StringUtils.equalsIgnoreCase(format, "epoch")) {
							timestamp = String.valueOf(System.currentTimeMillis());
						} else {
							SimpleDateFormat sdf = null;
							sdf = new SimpleDateFormat(format);
							timestamp = sdf.format(System.currentTimeMillis());
						}
						newMap.put(entry.getKey(), timestamp);
					}
				} else
					newMap.put(entry.getKey(), entry.getValue());

			}

		} catch (Exception e) {
			log.error("Exception occurred in transformHeaders", e);
		}
		return newMap;
	}

}
