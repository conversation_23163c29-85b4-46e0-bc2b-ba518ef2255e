
package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.beans.factory.annotation.Value;

import java.io.Serializable;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@DynamicUpdate
@Table(name = "COM_ORDER_ATTRIBUTES")
public class OrderAttributeEntity extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "SEQ_ID", length = 20, unique = true)
    private Long seqId;

    @Column(name = "ORDER_ID", length = 20)
    private Long orderId;

    @Column(name = "SUB_ORDER_ID", length = 20)
    @Value("NULL")
    private Long subOrderId;

    @Column(name = "NAME", length = 100)
    @Value("NULL")
    private String key;

    @Column(name = "VALUE")

    @Value("NULL")
    private String value;

    @Column(name = "TYPE", length = 50)
    @Value("NULL")
    private String type;

    @Column(name = "REFERENCE_ID", length = 50)
    @Value("NULL")
    private String referenceId;

}
