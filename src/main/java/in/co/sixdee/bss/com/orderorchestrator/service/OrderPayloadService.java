package in.co.sixdee.bss.com.orderorchestrator.service;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderPayloadEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderPayloadRepository;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

import java.time.Duration;
import java.time.Instant;

@Service
@Log4j2
@RequiredArgsConstructor
public class OrderPayloadService {

    private final OrderPayloadRepository orderPayloadRepository;

    private final ObjectMapper objectMapper;

    public void createOrderPayloadDetails(OrderFlowContext orderFlowContext) throws JsonProcessingException {
        log.info("Saving order payload information in COM_ORDER_PAYLOAD_DETAILS table");
        var orderPayloadDetails = new OrderPayloadEntity();
        orderPayloadDetails.setId(in.co.sixdee.bss.common.util.SequenceGenerator.getSequencerInstance().nextId());
        orderPayloadDetails.setCorrelationId(Long.parseLong(orderFlowContext.getOrder().getOrderId()));
        orderPayloadDetails.setPayload(objectMapper.writeValueAsString(orderFlowContext));
        orderPayloadDetails.setCreatedBy(orderFlowContext.getUserId());
        Instant start = Instant.now();
        orderPayloadRepository.save(orderPayloadDetails);
        log.info("Time taken for saving order payload information : {} ms", Duration.between(start, Instant.now()).toMillis());
    }

    public OrderFlowContext getOrderFlowContext(String orderId) {
        OrderFlowContext orderFlowContext = null;
        try {
            String payload = orderPayloadRepository.getPayloadDetails(Long.parseLong(orderId));
            if (StringUtils.isNotEmpty(payload))
                orderFlowContext = objectMapper.readValue(payload, OrderFlowContext.class);
        } catch (Exception e) {
            log.info("Exception occurred in OrderPayloadService.getOrderFlowContext", e);
        }
        return orderFlowContext;

    }

    public void deletePayloadDetails(String orderId) {
        orderPayloadRepository.deletePayloadDetails(Long.parseLong(orderId));
    }

    public void updatePayloadDetails(OrderFlowContext orderFlowContext, String orderId) throws JsonProcessingException {
        var orderFlowContextString = objectMapper.writeValueAsString(orderFlowContext);
        orderPayloadRepository.updatePayloadDetails(orderFlowContextString, Long.parseLong(orderId));
    }
}
