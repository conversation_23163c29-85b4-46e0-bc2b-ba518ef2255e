package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.ProductSpecification;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "somBarringStarterPackServices")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SOMBarringStarterpackServiceHandler extends AbstractDelegate {

	protected String serviceId = null;

	private Order orderPayload = null;

	protected String request = null;

	protected int index = 0;

	@Autowired
	protected GetDataFromCache cache;

	@Autowired
	protected ObjectMapper objectMapper;

	@Override
	protected void execute() throws Exception {
		try {
			orderPayload = executionContext.getOrder();
			if (StringUtils.equalsAnyIgnoreCase(orderType, OrderTypes.UPDATE_STARTER_PACK_KYC))
				serviceId = orderPayload.getCustomerInfo().getServiceId();
			else
				serviceId = orderPayload.getServiceManagement().getServiceId();
			request = createSOMRequest();
			var callThirdPartyDTO = callThirdParty(request);
			if (callThirdPartyDTO == null) {
				executionContext.setError(true);
				return;
			}
			validateResponse(callThirdPartyDTO);
		} catch (Exception e) {
			log.error("{} In {} .execute Exception occurred ", executionContext.getTraceId(), this.getClass().getName(),
					e);
		}
	}

	protected String createSOMRequest() throws Exception {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		var serviceOrder = new SOMServiceOrderDTO();
		serviceOrder.setExternalId(executionContext.getOrder().getOrderId());
		serviceOrder.setExternalServiceId(serviceId);
		serviceOrder.setDescription(orderPayload.getDescription());
		serviceOrder.setPriority("1");
		serviceOrder.setCategory("DATA");
		serviceOrder.setRequestedStartDate(
				StringUtils.isNotEmpty(orderPayload.getRequestedStartDate()) ? orderPayload.getRequestedStartDate()
						: Instant.now().toString());
		serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
		serviceOrder.setType("ServiceOrder");
		executionContext.getAttributes().put("callbackCorrelationId",
				callbackEvent + ":" + executionContext.getAttributes().get(GenericConstants.SUB_ORDER_ID));
		serviceOrderItemList = createBarringServiceOrderItem();

		
		serviceOrder.setServiceOrderItem(serviceOrderItemList);
		if (ObjectUtils.isNotEmpty(serviceOrder))
			request = objectMapper.writeValueAsString(serviceOrder);
		return request;
	}
	
	private List<ServiceOrderItem> createBarringServiceOrderItem() {
		var cfsConfig = cache.getCacheDetailsFromDBMap(
				CacheConstants.CacheKeys.COM_STARTERPACK_BARRING_CFS_CONFIG.name(), orderType);
		var somServices = getServicesFromSOM();
		if (ObjectUtils.isNotEmpty(cfsConfig) && ObjectUtils.isNotEmpty(somServices)) {
			List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
			List<Characteristic> serviceCharacteristic = new ArrayList<>();

			String[] cfssNames = cfsConfig.getNgTableData().get("NAME").split(",");

			for (var cfssName : cfssNames) {

				somServices.forEach(service -> {
					if (ObjectUtils.isNotEmpty(service.getServiceSpecification())
							&& ObjectUtils.isNotEmpty(service.getServiceSpecification().getName())
							&& ObjectUtils.isNotEmpty(service.getId())
							&& service.getServiceSpecification().getName().equalsIgnoreCase(cfssName)) {

						var serviceOrderItem = new ServiceOrderItem();
						var serviceItem = new SOMService();
						var specification = new ProductSpecification();
						index = index + 1;
						serviceOrderItem.setId(String.valueOf(index));
						serviceOrderItem.setAction("delete");
						serviceOrderItem.setType("ServiceOrderItem");
						specification.setAtType("MobileService");
						specification.setName(cfssName);
						serviceItem.setServiceSpecification(specification);
						serviceItem.setId(service.getId());
						serviceItem.setState("terminated");
						serviceItem.setType("CFS");
						serviceItem.setServiceCharacteristic(serviceCharacteristic);
						serviceOrderItem.setService(serviceItem);
						serviceOrderItemList.add(serviceOrderItem);

					}

				});

			}
			return serviceOrderItemList;
		}
		return null;

	}

	@SuppressWarnings("unused")
	private List<SOMService> getServicesFromSOM() {
		
		List<SOMService> somServices = new ArrayList<>();
		if (executionContext.getWorkflowData().containsKey("SOMFetchServiceRegistryResponseAttributes")) {
			somServices = objectMapper.convertValue(
					executionContext.getWorkflowData().get("SOMFetchServiceRegistryResponseAttributes"),
					new TypeReference<List<SOMService>>() {});
		}
		return somServices;
	}
	
}
