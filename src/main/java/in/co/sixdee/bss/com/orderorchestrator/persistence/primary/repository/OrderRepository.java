package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface OrderRepository extends JpaRepository<OrderEntity, Long> {


    @Modifying(clearAutomatically = true)
    @Query("UPDATE OrderEntity o SET o.state = :state, o.stateReason = :stateReason where o.orderId = :orderId")
    public int orderStatus(@Param("state") String state, @Param("stateReason") String stateReason,
                           @Param("orderId") Long orderId);

    @Modifying(clearAutomatically = true)
    @Query("UPDATE OrderEntity o SET o.state = :state, o.stateReason = :stateReason, o.lastModifiedDate = :lastModifiedDate where o.orderId = :orderId")
    public int orderStatus(@Param("state") String state, @Param("stateReason") String stateReason,
                           @Param("orderId") Long orderId, @Param("lastModifiedDate") Date lastModifiedDate);

    @Query("select o.type from OrderEntity o where o.orderId = :orderId")
    public String findOrderTypeByOrderId(@Param("orderId") Long orderId);

    @Modifying(clearAutomatically = true)
    @Query("UPDATE OrderEntity o SET o.customerId = :customerId where o.orderId = :orderId")
    public int updateCustomerId(@Param("orderId") Long orderId, @Param("customerId") String customerId);

    @Modifying(clearAutomatically = true)
    @Query("UPDATE OrderEntity o SET o.billingAccountId = :billingAccountId where o.orderId = :orderId")
    public int updateAccountId(@Param("orderId") Long orderId, @Param("billingAccountId") String billingAccountId);

    @Modifying(clearAutomatically = true)
    @Query("UPDATE OrderEntity o SET o.serviceId = :serviceId where o.orderId = :orderId")
    public int updateMsisdn(@Param("orderId") Long orderId, @Param("serviceId") String serviceId);


    @Modifying(clearAutomatically = true)
    @Query("UPDATE OrderEntity o SET o.billingAccountId = :billingAccountId, o.customerId = :customerId where o.orderId = :orderId")
    public int updateOrderParams(@Param("orderId") Long orderId, @Param("customerId") String customerId, @Param("billingAccountId") String billingAccountId);

    @Modifying(clearAutomatically = true)
    @Query("UPDATE OrderEntity o SET o.state = 'In-Progress' where o.orderId = :id")
    public void updateOrderToInProgress(Long id);

    @Modifying(clearAutomatically = true)
    @Query("UPDATE OrderEntity o SET o.state = :status where o.orderId = :orderId")
    public void updateOrderStatus(@Param("orderId") Long orderId, @Param("status") String status);

    @Query("select case when count(o)>0 then true else false end from OrderEntity o where o.orderId= :orderId and o.state<> 'Cancelled'")
    public boolean checkOrderNotCancelled(@Param("orderId") Long orderId);

    @Query("select o.state from OrderEntity o where o.orderId = :orderId")
    public String findOrderStatusByOrderId(@Param("orderId") Long orderId);

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query("UPDATE OrderEntity o SET o.state = :state, o.stateReason = :stateReason where o.orderId = :orderId")
    public int updateOrderStatusToCancelled(@Param("state") String state, @Param("stateReason") String stateReason,
                                            @Param("orderId") Long orderId);

    @Modifying(clearAutomatically = true)
    @Query("UPDATE OrderEntity o SET o.state ='PartialSuccess' where o.orderId = :orderId")
    public void updateOrderStatus(@Param("orderId") Long orderId);

    @Query("select o from OrderEntity o where o.orderId in (:orderId) and o.state in ('Failed','PartialSuccess')")
    public List<OrderEntity> findFailedOrdersByOrderId(@Param("orderId") List<Long> orderId);

    @Query("select o from OrderEntity o where o.orderId in (:orderId)")
    public List<OrderEntity> findOrdersByOrderId(@Param("orderId") List<Long> orderId);

    @Query("select case when count(o)>=1 then true else false end from OrderEntity o where o.orderId=:orderId and o.state = 'Completed'")
    public boolean checkIfOrderIsCompleted(@Param("orderId") Long orderId);

    @Query("select o.createdDate from OrderEntity o where o.orderId=:orderId")
    public Date findOrderCreationDate(@Param("orderId") Long orderId);

    @Query("select o from OrderEntity o where o.type in (:orderTypes) and o.orderId in (:orderId) and o.customerId=:customerId")
    public List<OrderEntity> findOrdersByOrderIdAndProfileId(@Param("orderTypes") ArrayList<String> orderTypes, @Param("orderId") List<Long> orderId, @Param("customerId") String customerId);

    @Query("select new OrderEntity (o.orderId,o.customerId,o.billingAccountId,o.state,o.type,o.entityId) from OrderEntity o where o.orderId=:orderId")
    public OrderEntity findProfileIdAndAccountIdByOrderId(@Param("orderId") Long orderId);
    
    @Transactional
    @Modifying(clearAutomatically = true)
    @Query("UPDATE OrderEntity o SET o.state = :state, o.stateReason = :stateReason where o.orderId = :orderId and o.state not in ('In-Progress')")
    public int updateStatusForMNPNotification(@Param("state") String state, @Param("stateReason") String stateReason,
                                            @Param("orderId") Long orderId);
    
}
