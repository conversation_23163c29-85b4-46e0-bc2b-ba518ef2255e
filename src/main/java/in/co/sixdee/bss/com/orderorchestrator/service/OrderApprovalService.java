package in.co.sixdee.bss.com.orderorchestrator.service;

import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.RuntimeService;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.ProcessVariableUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.EntityValidationException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants.WorkFlowProcessVariables;
import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderStageRepository;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.om.model.dto.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
@RequiredArgsConstructor
public class OrderApprovalService {

	protected final OrderStatusManager orderStatusManager;

	protected final OrderPayloadService orderPayloadService;

	protected final ProcessVariableUtils processVariableUtils;

	protected final OrderCallBackService orderCallBackService;

	protected final OrderStageRepository orderStageRepository;

	protected final RuntimeService runtimeService;

	protected final GetDataFromCache cache;

	public Response processApprovalRequest(OrderCallBack callback) {
		callback.setOrderId(callback.getCorrelationId());
		validateApprovalOrder(Long.parseLong(callback.getOrderId()));
		if (WorkFlowConstants.StageStatusConstants.STAGE_STATUS_INPROGRESS.getValue()
				.equalsIgnoreCase(callback.getStatus())
				|| WorkFlowConstants.StageStatusConstants.STAGE_STATUS_PENDING.getValue()
						.equalsIgnoreCase(callback.getStatus())) {
			return orderCallBackService.createResponse(callback);
		} else if (WorkFlowConstants.StageStatusConstants.STAGE_STATUS_APPROVED.getValue()
				.equalsIgnoreCase(callback.getStatus())
				|| WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue()
						.equalsIgnoreCase(callback.getStatus())) {
			executeApprovalCallback(callback);
		} else {
			var stageInfo = getApprovalStageInfo(callback);
			if (StringUtils.isNotEmpty(callback.getStatus())
					&& StringUtils.containsAnyIgnoreCase(callback.getStatus(), stageInfo.getState())) {
				throw new EntityValidationException(StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION,
						"stage : " + stageInfo.getStageCode() + " is already in " + callback.getStatus() + " state");
			}
			var orderFlowContext = orderPayloadService.getOrderFlowContext(callback.getOrderId());
			orderStatusManager.UpdateCallBackOrderStatus(orderFlowContext, callback, stageInfo);
		}
		return orderCallBackService.createResponse(callback);
	}

	private void executeApprovalCallback(OrderCallBack callback) {
		try {
			if (WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue()
					.equalsIgnoreCase(callback.getStatus())
					|| WorkFlowConstants.OrderCallBackTypes.PAYMENT.desc.equals(callback.getCallbackType())
					|| WorkFlowConstants.OrderCallBackTypes.APPROVAL.desc.equals(callback.getCallbackType())) {
				processApprovalOrder(callback.getOrderId(), false);
			}
		} catch (Exception e) {
			log.info("Exception occurred in executeApprovalCallback {}", e.getMessage());
			throw e;
		}
	}

	public void processApprovalOrder(String orderId, boolean isSkipped) throws CommonException {

		try {
			var orderFlowContext = orderPayloadService.getOrderFlowContext(orderId);
			if (!isSkipped)
				orderStatusManager.processStatusUpdates(orderFlowContext, "Approval", "WAIT_EVENT", true, null);
			String orderType = orderFlowContext.getOrder().getOrderType();
			var processId = findProcessId(orderType);
			var variables = processVariableUtils.createProcessVariables(orderType, orderFlowContext);
			variables.put(WorkFlowProcessVariables.PROCESS_ID.toString(), processId);
			runtimeService.startProcessInstanceByKey(processId, orderFlowContext.getOrder().getOrderId(), variables);

		} catch (Exception e) {
			log.info("Exception occurred in processApprovalOrder", e);
		}
	}

	private OrderStageEntity getApprovalStageInfo(OrderCallBack callback) {
		var stageCode = "APPROVAL";
		var stageInfo = orderStageRepository.findStagebyOrderIdAndName(Long.valueOf(callback.getOrderId()), stageCode,
				PageRequest.of(0, 1));
		if (stageInfo == null)
			throw new EntityValidationException(StatusConstants.HttpConstants.UNPROCESSABLE_ENTITY,
					"There is no stage found for the activity: " + stageCode);
		return stageInfo.get(0);

	}

	protected String findProcessId(String orderType) {
		var orderTypeMapping = cache.getCacheDetailsFromDBMap("COM_ORDER_TYPE_CONFIG", orderType);
		return orderTypeMapping != null ? orderTypeMapping.getNgTableData().get("BPMN_PROCESS_ID") : null;
	}

	public void validateApprovalOrder(long orderId) {
		boolean orderStatus = orderStageRepository.findApprovalOrderByStateReason(orderId);
		if (!orderStatus)
			throw new EntityValidationException("Invalid order id : " + orderId
					+ ". The order may not exist in the system or is not waiting for approval callback");
	}

}
