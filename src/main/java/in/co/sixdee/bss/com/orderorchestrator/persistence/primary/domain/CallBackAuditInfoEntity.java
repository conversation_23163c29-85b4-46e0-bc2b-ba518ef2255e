package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "COM_CALLBACK_AUDIT_INFO")
public class CallBackAuditInfoEntity extends AbstractAuditingEntity implements Serializable {

	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "SEQ_ID", nullable = false)
	private Long				seqId;

	@Column(name = "ORDER_ID", length = 20)
	private Long				orderId;

	@Column(name = "SUB_ORDER_ID", length = 20)
	@Value("0L")
	private Long				subOrderId;

	@Column(name = "CHANNEL", length = 20)
	@Value("NULL")
	private String				channel;

	@Column(name = "PAYLOAD", length = 5000)
	private String				payload;

	@Column(name = "END_POINT", length = 200)
	private String				endPoint;

	@Column(name = "LOCK_EXP_TIME")
	public Date lockExpTime;

	@Version
	public Integer version;

	@Column(name = "IS_PROCESSED")
	public String isProcessed;

	@Column(name = "REMAINING_RETRIES")
	public Integer remainingRetries;
	
	@Column(name = "CALLBACK_TYPE")
	public String callbackType;
}
