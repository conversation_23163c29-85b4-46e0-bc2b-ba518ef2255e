package in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener;

import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.DelegateVariableMapping;
import org.camunda.bpm.engine.delegate.VariableScope;
import org.camunda.bpm.engine.variable.VariableMap;

public class DelegateVarMapping implements DelegateVariableMapping {

	@Override
	public void mapInputVariables(DelegateExecution superExecution, VariableMap subVariables) {
		subVariables.put("executionData", superExecution.getVariable("executionData"));
	}

	@Override
	public void mapOutputVariables(DelegateExecution superExecution, VariableScope subInstance) {
		// TODO Auto-generated method stub

	}

}
