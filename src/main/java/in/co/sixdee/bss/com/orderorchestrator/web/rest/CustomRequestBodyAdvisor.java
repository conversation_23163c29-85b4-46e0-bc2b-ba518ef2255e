package in.co.sixdee.bss.com.orderorchestrator.web.rest;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.core.ErrorDetail;
import in.co.sixdee.bss.common.logging.LogService;
import in.co.sixdee.bss.common.vaidation.RequestValidationException;
import in.co.sixdee.bss.common.vaidation.ValidationUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import java.lang.reflect.Type;
import java.util.List;


@ControllerAdvice
@RequiredArgsConstructor
@Log4j2
public class CustomRequestBodyAdvisor extends RequestBodyAdviceAdapter {

	private final LogService loggingService;

	private final HttpServletRequest httpServletRequest;

	private final ValidationUtils validationUtils;

	private final GetDataFromCache cache;

	private final ObjectMapper objectMapper;


	@Override
	public boolean supports(MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
		return true;
	}

	@Override
	public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
		//loggingService.logRequest(httpServletRequest, body);
		validateBody(httpServletRequest.getRequestURI(), body);
		return super.afterBodyRead(body, inputMessage, parameter, targetType, converterType);
	}

	public void validateBody(String uri, Object body) {
		var validationConfig = cache.getCacheDetailsFromDBMap("PAYLOAD_VALIDATION_CONFIG", uri);
		if (validationConfig == null || !BooleanUtils.toBoolean(validationConfig.getNgTableData().get("ENABLE_VALIDATION"))) {
			log.info("Payload validation is not configured or disabled for the uri {}", uri);
			return;
		}
		try {
			List<ErrorDetail> validationErrors = validationUtils.validateRequest(uri, objectMapper.readTree(objectMapper.writeValueAsString(body)));
			if (validationErrors == null || validationErrors.isEmpty()) {
				log.info("Successfully validated request. no errors found");
				return;
			}
			throw new RequestValidationException("Request validation failed!", validationErrors);

		} catch (JsonProcessingException e) {
			throw new RequestValidationException("Invalid Json");
		}

	}

}
