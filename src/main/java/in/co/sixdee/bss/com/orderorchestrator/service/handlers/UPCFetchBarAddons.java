
package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.om.model.dto.order.Charge;
import in.co.sixdee.bss.om.model.dto.order.ProductOffering;
import in.co.sixdee.bss.om.model.dto.order.ProductOfferingPrice;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "upcFetchBarAddons")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class UPCFetchBarAddons extends AbstractDelegate {
	/**
	 * This class will be executed during Barring case
	 */

	@Autowired
	private ObjectMapper objectMapper;
	
	@Autowired
	private GetDataFromCache cache;

	@Override
	protected void execute() throws Exception {

		var callThirdPartyDTO = callThirdParty(null);
		if (callThirdPartyDTO == null) {
			executionContext.setError(true);
			return;
		}
		var response = callThirdPartyDTO.getResponse();
		validateResponse(callThirdPartyDTO);

		if (executionContext.isError()) {
			log.error("{} Response validation failed", activityId);
			return;
		}

		workflowDataUpdated = true;
		enrichPlans(response);
		extractCFSSFromResponse(response);

	}
	

	protected void enrichPlans(String response) {
		List<ProductOffering> productOfferings;
		Subscription enrichSubscription = null;
		try {
			productOfferings = objectMapper.readValue(response, new TypeReference<List<ProductOffering>>() {});
			
				var subscriptions = new ArrayList<Subscription>();
				for (ProductOffering productOffering : productOfferings) {
					enrichSubscription = new Subscription();
					enrichSubscription = enrichSubscription(productOffering);
					subscriptions.add(enrichSubscription);
				}
				executionContext.getOrder().getServiceManagement().setSubscriptions(subscriptions);
			

		} catch (JsonProcessingException e) {
			log.error("Error in Parsing UPC allowed addons response ", e);
		}

	}
	
	protected Subscription enrichSubscription(ProductOffering productOffering) {
		Subscription subscription = new Subscription();
		subscription.setPlanId(productOffering.getId());
		subscription.setUpcVersion(productOffering.getVersionNo());
		subscription.setPlanName(productOffering.getName());
		subscription.setPlanDesc(productOffering.getDescription());
		subscription.setCpId(productOffering.getPartner());
		if (ObjectUtils.isNotEmpty(productOffering.getProductOfferGroup())) {
			subscription.setPlanGroupId(productOffering.getProductOfferGroup().getId());
			subscription.setPlanGroupName(productOffering.getProductOfferGroup().getName());
		}
		if (productOffering.getProductSpecifications() != null && !productOffering.getProductSpecifications().isEmpty()) {
			if ("1".equals(productOffering.getProductSpecifications().get(0).getType().getId()))
				subscription.setPlanType(GenericConstants.PLAN_TYPE_BASE);
			else
				subscription.setPlanType(GenericConstants.PLAN_TYPE_ADDON);

		} else {
			subscription.setPlanType(GenericConstants.PLAN_TYPE_ADDON);
		}
		if (subscription.getCharges() == null)
			subscription.setCharges(new ArrayList<>());
		enrichCharges(subscription.getCharges(), productOffering.getProductOfferingPrice());

		executionContext.getWorkflowData().put("enrichmentResults", executionContext.getEnrichmentResults());
		return subscription;
	}
	protected void enrichCharges(List<Charge> charges, List<ProductOfferingPrice> productOfferingPrices) {
		if (productOfferingPrices != null) {
			List<Charge> newCharges = new ArrayList<>();
			for (ProductOfferingPrice price : productOfferingPrices) {
				boolean presentInOrder = false;
				if (!charges.isEmpty()) {
					for (Charge charge : charges) {
						if (price.getId().equals(charge.getUpcChargeId())) {
							presentInOrder = true;
							enrichExistingCharge(charge);
							break;
						}
					}
				}
				if (!presentInOrder) {
					newCharges.add(createNewCharge(price));
				}
			}
			if (!newCharges.isEmpty())
				charges.addAll(newCharges);
		}
	}

	protected Charge createNewCharge(ProductOfferingPrice price) {
		var charge = new Charge();
		charge.setUpcChargeId(price.getId());
		charge.setUpcChargeName(price.getName());
		charge.setChargeDesc(price.getName());
		charge.setAmount(price.getPrice());
		charge.setRate(price.getPrice());
		if (price.getPriceCategory() != null) {
			if (price.getPriceCategory().getBillingChargeType() != null
					&& price.getPriceCategory().getBillingChargeType().getId() != null) {
				charge.setChargeCategory(price.getPriceCategory().getBillingChargeType().getId());
			}
			if (price.getPriceCategory().getBillingChargeCategory() != null
					&& price.getPriceCategory().getBillingChargeCategory().getId() != null) {
				if ("1".equalsIgnoreCase(price.getPriceCategory().getBillingChargeCategory().getId())
						|| "2".equalsIgnoreCase(price.getPriceCategory().getBillingChargeCategory().getId()))
					charge.setBillingChargeType(price.getPriceCategory().getBillingChargeCategory().getId());
			}
		}
		if (price.getPriceMode() != null) {
			if ("2".equals(price.getPriceMode().getId()))
				charge.setChargeType("1");
			else
				charge.setChargeType("0");
			charge.setChargeRecurranceType(charge.getChargeType());
		}
		if (price.getPriceCode() != null)
			charge.setChargeCode(price.getPriceCode().getCode());
		
		  charge.setIsProrata(BooleanUtils.toBoolean(price.getProrataEnable()) ? "1" : "0");
	        charge.setProrationFlag(getProrationFlag(price));
		
		charge.setChargeVersion(price.getVersionNo());
		if (price.getFrequency() != null)
			charge.setChargeFrequency(price.getFrequency().getId());
		charge.setChargeFactor(price.getFactor());
		return charge;
	}

    private String getProrationFlag(ProductOfferingPrice price) {
     	
    	var suspend=getIntegerValue(price.getProrataEnabledInSuspension());
    	var terminate=getIntegerValue(price.getProrataEnabledInTermination());
    	var planChange=getIntegerValue(price.getProrataEnabledInPlanChange());
    	var activation=getIntegerValue(price.getProrataEnabledInActivation());
    
    	
    	StringBuilder prorationFlagBuilder= new StringBuilder();
    	prorationFlagBuilder.append(suspend).append(terminate).append(planChange).append(activation);

    	return prorationFlagBuilder.toString();
    }
    
    private Integer getIntegerValue (String prorata) {
    	return BooleanUtils.toInteger(BooleanUtils.toBoolean(prorata));
    }
	
	protected void enrichExistingCharge(Charge charge) {
		if (charge.getChargeType() != null) {
			if ("2".equals(charge.getChargeType()))
				charge.setChargeType("1");
			else
				charge.setChargeType("0");
			charge.setChargeRecurranceType(charge.getChargeType());
		}
		if (charge.getBillingChargeType() != null) {
			if (!("1".equals(charge.getBillingChargeType()) || "2".equals(charge.getBillingChargeType())))
				charge.setBillingChargeType(null);				
		}
	}
	
	protected void extractCFSSFromResponse(String response)
			throws JsonMappingException, JsonProcessingException {
		var productOfferings = objectMapper.readValue(response, new TypeReference<List<ProductOffering>>() {
		});
		if (ObjectUtils.isEmpty(productOfferings))
			return;
		var productSpecifications = Objects.requireNonNull(productOfferings.stream().map(offering -> offering.getProductSpecifications())
				.findAny().orElse(null));
		if (ObjectUtils.isNotEmpty(productSpecifications)) {
			var cfss = Objects.requireNonNull(productSpecifications.stream().map(specification -> specification.getCfss()).findAny()
					.orElse(null));
			if (ObjectUtils.isNotEmpty(cfss)) {		
					executionContext.getObjectAttributes().put("CFSSDunning", cfss);
					executionContext.getOrder().getServiceManagement().getSubscriptions().get(0).setAutoRenewal("0");//default 
					var upcCharacteristic = Objects.requireNonNull(cfss.stream()
							.filter(cfs -> cfs.getName().equalsIgnoreCase(GenericConstants.CFS_UPC))
							.map(cfs -> cfs.getCharacteristics()).findAny().orElse(null));
					if (ObjectUtils.isNotEmpty(upcCharacteristic)) {
						var isRenewal = Objects.requireNonNull(upcCharacteristic.stream()
								.filter(character -> character.getName()
										.equalsIgnoreCase(GenericConstants.CHARACTERISTIC_IS_RENEWAL))
								.map(character -> character.getValue()).findAny().orElse(null));
						if ("true".equalsIgnoreCase(isRenewal))
						executionContext.getOrder().getServiceManagement().getSubscriptions().get(0).setAutoRenewal("1");

					
				}
			}
			else
				throw new CommonException("No CFSS present for configured planIds: "+executionContext.getAttributes().get("planIds"));		
			}
		}

}
