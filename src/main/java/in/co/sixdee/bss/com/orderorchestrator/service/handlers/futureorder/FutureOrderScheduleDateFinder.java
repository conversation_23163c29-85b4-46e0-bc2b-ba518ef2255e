package in.co.sixdee.bss.com.orderorchestrator.service.handlers.futureorder;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;


@Log4j2
@Component(value = "futureOrderScheduleDateFinder")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class FutureOrderScheduleDateFinder extends AbstractDelegate {

    @Override
    protected void execute() throws Exception {

        boolean futureOrderReqd=false;
        
        try {
            if (checkMvnoBasedOrderExecutionDateReqd()) {
                futureOrderReqd = findOrderExecutionDateBasedOnMvno();
            } else {
                futureOrderReqd = findOrderExecutionDate();
            }

            execution.setVariable("orderSchedulingRequired", futureOrderReqd);
        } catch (Exception e) {
            log.error("Exception while executing logic for finding order execution date", e);
            executionContext.setError(true);
        }
        workflowDataUpdated = true;

    }

    private boolean findOrderExecutionDateBasedOnMvno() {

        boolean futureOrderReqd = false;
        String daysConfigValue;
        String futureDate;
        try {
            futureOrderReqd = true;
            daysConfigValue = findDaysForPolicyCheck();
            LocalDateTime orderDate = LocalDateTime.parse(executionContext.getAttributes().get(GenericConstants.ORDER_DATE), DateTimeFormatter.ofPattern(GenericConstants.ORDER_DATE_FORMAT));
            LocalDateTime nextBillDate = LocalDateTime.parse(executionContext.getAttributes().get("billingNextBillDate"), DateTimeFormatter.ofPattern(GenericConstants.TIMESTAMP_FORMAT));
            int days = checkDateDifference(orderDate, nextBillDate);
            if (StringUtils.isNotEmpty(daysConfigValue) && days < Integer.parseInt(daysConfigValue)) {
                futureDate = settingFutureDateDetails(nextBillDate, true);
                log.info("setting future date based on days policy {}",futureDate);
            } else {
                futureDate = settingFutureDateDetails(nextBillDate, false);
                log.info("setting future date as next bill date from billing {}", futureDate);
            }

        } catch (Exception e) {
            log.error("Exception occurred in findOrderExecutionDateBasedOnMvno", e);
        }
        return futureOrderReqd;
    }

    private String findDaysForPolicyCheck() {
        String daysConfigValue="7";
        CacheTableDataDTO daysConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.DAYS_REQUIRED_FOR_POLICY_CHECK.toString());
        if (daysConfig != null)
            daysConfigValue = daysConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
        log.info("Number of days required for the policy check {}",daysConfigValue);
        return daysConfigValue;
    }

    private String settingFutureDateDetails(LocalDateTime nextBillDate, boolean daysAdditionRequired) {

        try {
            DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
            DateTimeFormatter inputFormatForOrderTable= DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
            LocalDateTime futureDate = daysAdditionRequired ? nextBillDate.plusMonths(1).minusDays(1).withHour(23).withMinute(0).withSecond(0) : nextBillDate.minusDays(1).withHour(23).withMinute(0).withSecond(0);
            executionContext.getAttributes().put("orderExecutionDate", futureDate.format(inputFormatForOrderTable));
            execution.setVariable("orderExecutionDate", futureDate.format(inputFormat));
        } catch (Exception e) {
            log.error("Exception occurred in settingFutureDateDetails", e);
        }
        return  executionContext.getAttributes().get("orderExecutionDate");
    }

    private int checkDateDifference(LocalDateTime orderDate, LocalDateTime nextBillDate) {
        try {
            return Math.abs((int) Duration.between(nextBillDate, orderDate).toDays());
        }
        catch(Exception e){
            log.error("Exception occurred in checkDateDifference", e);
        }
        return 0;
    }

    private boolean checkMvnoBasedOrderExecutionDateReqd() {
        List<String> mvnoConfigValue = null;
        CacheTableDataDTO mvnoConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.SEVEN_DAYS_POLICY_CHECK_REQD_MVNO.toString());
        if (mvnoConfig != null)
            mvnoConfigValue = Arrays.asList(mvnoConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString()).split(","));
        return ((mvnoConfigValue!= null && !mvnoConfigValue.isEmpty()) && OrderTypes.TERMINATE_SERVICE.equalsIgnoreCase(executionContext.getOrder().getOrderType()) && mvnoConfigValue.contains(executionContext.getEntityId())
        		&& (StringUtils.isEmpty(executionContext.getBatchId())));
    }


	public boolean findOrderExecutionDate() {
        boolean futureOrderRequired = false;
        String requestedStartDate;
        LocalDateTime executionStartDate;
        try {
            requestedStartDate = executionContext.getOrder().getRequestedStartDate();
            if (StringUtils.isNotEmpty(requestedStartDate)) {
                executionStartDate = LocalDateTime.parse(requestedStartDate, DateTimeFormatter.ofPattern(GenericConstants.TIMESTAMP_FORMAT));
                if(validateCallReqdForTerminateService()) {
                    log.info("future date validation is required...");
                    executionStartDate=  validateFutureDateForTerminateService(executionStartDate);
                    requestedStartDate = executionStartDate.format(DateTimeFormatter.ofPattern(GenericConstants.TIMESTAMP_FORMAT));
                    log.info("Execution date changed for terminate service executionStartDate {} requestedStartDate {}",executionStartDate,requestedStartDate);
                }
                if (executionStartDate.isAfter(LocalDateTime.now())) {
                    futureOrderRequired = true;
                    DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
                    execution.setVariable("orderExecutionDate", OffsetDateTime.parse(requestedStartDate).format(inputFormat));
                    executionContext.getAttributes().put("orderExecutionDate", executionStartDate.toString());
                    log.info("scheduling the order for {}", executionStartDate);
                } else {
                    log.info("requested start date already passed the current date.order will be executed immediately");
                }
            }
        } catch (Exception e) {
            futureOrderRequired = false;
            log.error("Exception occurred in findScheduleDate", e);
        }
        return futureOrderRequired;
    }

    private boolean validateCallReqdForTerminateService() {

        if (OrderTypes.TERMINATE_SERVICE.equalsIgnoreCase(executionContext.getOrder().getOrderType())) {
            String configMvnos = getvalueFromConfiguration(CacheConstants.CacheFields.VALIDATION_REQD_MVNOS_FOR_FUTURE_DATE_IN_TERMINATE_SERVICE.toString());
            if (StringUtils.isNotEmpty(configMvnos)) {
                List<String> configEntityIds = Arrays.asList(configMvnos.split(","));
                return (ObjectUtils.isNotEmpty(configEntityIds) && configEntityIds.contains(executionContext.getEntityId()) && !(GenericConstants.CHANNEL_ID_FOR_CRM.equalsIgnoreCase(executionContext.getChannel())));
            }
        }
        return false;

    }

    private LocalDateTime validateFutureDateForTerminateService(LocalDateTime executionStartDate) {

        int defaultvalue = 0;
        LocalDateTime targetDateTime;
        try {
            int hour = executionStartDate.getHour();
            int minute = executionStartDate.getMinute();
            int second = executionStartDate.getSecond();
            if (hour == defaultvalue && minute == defaultvalue && second == defaultvalue) {
                log.info("updating the hour value as the termination will happen only at EOD..");
                String hourConfigValue = getvalueFromConfiguration(CacheConstants.CacheFields.HOUR_VALUE_FOR_FUTURE_DATE_IN_TERMINATE_SERVICE.toString());
                if (StringUtils.isNotEmpty(hourConfigValue)) {
                    targetDateTime = executionStartDate.withHour(Integer.parseInt(hourConfigValue)).withMinute(0).withSecond(0).withNano(0);
                    return targetDateTime;
                }
            }
        } catch (Exception e) {
            log.info("Returning the execution date as the requested start date from request..");
        }
        return executionStartDate;
    }

    private String getvalueFromConfiguration(String key) {
        CacheTableDataDTO configData = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                key);
        if (configData != null)
            return configData.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
        return null;
    }

}
