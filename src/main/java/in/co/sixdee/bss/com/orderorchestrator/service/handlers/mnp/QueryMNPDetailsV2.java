package in.co.sixdee.bss.com.orderorchestrator.service.handlers.mnp;


import com.fasterxml.jackson.databind.JsonNode;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.WorkflowTaskFailedException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.MNPServiceUtil;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.AppConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.common.util.CommonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * \
 * This class will call Singtel's MNP System over  (JSON) and extract the donor operator details
 * from the response.
 * <p>
 */
@Log4j2
@Component(value = "queryMnpDetails")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class QueryMNPDetailsV2 extends AbstractDelegate {


    private final MNPServiceUtil mnpServiceUtil;

    private final MNPAttributeService mnpAttributeService;

    @Override
    protected void execute() throws Exception {
        if (!CommonUtils.INSTANCE.validateField(reqSpecKey)) {
            handleError("COM-001", "Internal Error: error in forming TP request",
                    "Exception occurred. Unable to form the TP request", "COM");
            return;
        }
        mnpServiceUtil.getServiceIdWithoutCC(executionContext);
        String request;
        try {
            request = joltUtils.convert(reqSpecKey, executionContext.getOrder().getOrderType(),
                    objectMapper.convertValue(executionContext, Map.class), executionContext.getAttributes());
        } catch (Exception e) {
            handleError("COM-001", "Internal Error: error in forming TP request", e.getMessage(), "COM");
            return;
        }

        CallThirdPartyDTO callThirdPartyDTO = callThirdParty(request);
        if (callThirdPartyDTO == null) {
            executionContext.setError(true);
            return;
        }
        var response = callThirdPartyDTO.getResponse();
        validateResponse(callThirdPartyDTO);
        if (!parseQueryMNPResponse(response)) {
            handleError("COM-005", "Unable to extract operator details from Query MNP response",
                    "Error while extracting operator details from Query MNP response. Setting error and stopping the flow", "COM");
        } else {
            modifyWorkflowData(response);
        }

        workflowDataUpdated = true;
    }


    private boolean parseQueryMNPResponse(String response) {
        try {
            JsonNode mnpResponse = objectMapper.readTree(response);
            String operator = getNodeTextValue(mnpResponse, "operator");
            String lineType = getNodeTextValue(mnpResponse, "lineType");

            if (StringUtils.isEmpty(operator)) {
                return false;
            }
            executionContext.getAttributes().put(MNPConstants.DONOR_OPERATOR_NAME, operator);
            if (!mnpServiceUtil.findDonorOperatorDetails(executionContext, operator)) {
                throw new WorkflowTaskFailedException(activityId, "COM-005", "Unable to find details for the operator: " + operator);
            }
            String portInType = findPortInType(operator);
            log.info("Identified Port-In type as {}", portInType);
            execution.setVariable(AppConstants.ProcessVariables.PORT_IN_TYPE.value(), portInType);
            if (StringUtils.isNotEmpty(lineType)) {
                executionContext.getAttributes().put(MNPConstants.MNP_LINE_TYPE, lineType);
            }
            mnpAttributeService.createAndSaveMNPAttribute(AppConstants.ProcessVariables.PORT_IN_TYPE.value(), portInType, executionContext);
            mnpAttributeService.saveOperatorCodesInAttributes(executionContext);
            return true;
        } catch (Exception e) {
            log.error("Exception occurred while parsing Query MNP Response", e);
            return false;
        }
    }

    private String findPortInType(String operator) {
        if (mnpServiceUtil.isSingtelOperator(operator)) {
            return MNPConstants.PORT_IN_TYPE_FROM_SINGTEL;
        } else if (mnpServiceUtil.isExternalOperator(operator)) {
            return MNPConstants.PORT_IN_TYPE_FROM_EXTERNAL_TELCO;
        } else {
            throw new WorkflowTaskFailedException(activityId, "COM-005", "Unable to find port-in type. Operator :" + operator + " is not configured as either an external operator or a Singtel operator");
        }
    }

    private String getNodeTextValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return (fieldNode != null && !fieldNode.isNull()) ? fieldNode.textValue() : null;
    }


}
