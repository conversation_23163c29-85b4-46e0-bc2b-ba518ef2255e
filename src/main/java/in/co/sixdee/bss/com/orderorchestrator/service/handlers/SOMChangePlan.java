/**
 *
 */
package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.enrichment.OrderEnrichmentResult;
import in.co.sixdee.bss.om.model.dto.order.CFSCharacteristicRef;
import in.co.sixdee.bss.om.model.dto.order.CFSRef;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.ProductSpecification;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 *
 */
@Log4j2
@Component(value = "somChangePlan")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class SOMChangePlan extends AbstractDelegate {

	protected Order					orderPayload	= null;
	protected String				request			= null;
	protected int					index			= 0;
	protected String				iccid			= null;

	protected String				imsi			= null;
	protected String				planId			= null;
	protected String				basePlanId		= null;

	protected String				ki				= null;
	protected String				msisdn			= null;
	protected String				meName			= null;
	protected String				algoId			= null;
	protected String				kdbId			= null;

	protected Subscription			newBasePlan		= null;
	protected Subscription			oldBasePlan		= null;

	protected List<Subscription>	delCommonAddons	= null;
	protected String				connectionType	= null;

	protected final ObjectMapper	objectMapper;
	protected String				cfsPCRF	= null;
	protected String				policyName=null;
	protected String				expiryDate=null;
	protected String 				startDate=null;
	protected String ocsPlanId = null;
	@Override
	protected void execute() throws Exception {
		try {
			orderPayload = executionContext.getOrder();
			request = createSOMRequest();
			var callThirdPartyDTO = callThirdParty(request);
			if (callThirdPartyDTO == null) {
				executionContext.setError(true);
				return;
			}
			validateResponse(callThirdPartyDTO);
		} catch (Exception e) {
			log.error("{} In {} .execute Exception occurred ", executionContext.getTraceId(), this.getClass().getName(), e);
		}
	}


	/* To Form the SOM request */
	protected String createSOMRequest() throws Exception {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		var serviceOrder = new SOMServiceOrderDTO();
		if (executionContext.getOrder().getOrderType().equalsIgnoreCase(OrderTypes.CHANGE_SUBSCRIPTION))
			serviceOrder.setOrderType("ChangePlan");
		serviceOrder.setExternalId(executionContext.getOrder().getOrderId());
		serviceOrder.setDescription(orderPayload.getDescription());
		serviceOrder.setRequestedStartDate(
				StringUtils.isNotEmpty(orderPayload.getRequestedStartDate()) ? orderPayload.getRequestedStartDate()
						: Instant.now().toString());
		serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
		serviceOrder.setType("ServiceOrder");
		serviceOrder.setRegistryId(executionContext.getEntityId());
		msisdn = orderPayload.getServiceManagement().getServiceId();
		serviceOrder.setExternalServiceId(msisdn);
		if (executionContext.getEnrichmentResults().containsKey("serviceInfo")) {
			var serviceInfo = objectMapper.convertValue(executionContext.getEnrichmentResults().get("serviceInfo"),
					LinkedHashMap.class);
			connectionType = serviceInfo.get("chargingPattern").toString();
			if(!serviceInfo.get("iccId").toString().isEmpty())
			{
			iccid = serviceInfo.get("iccId").toString();
			}
			if(!serviceInfo.get("imsiNumber").toString().isEmpty())
			{
			imsi = serviceInfo.get("imsiNumber").toString();
			}

		}
		 cfsPCRF = getApplicationConfigValue("SOM_PCRF_NAME");
		if (StringUtils.isEmpty(cfsPCRF))
			cfsPCRF = "CFSS_PCRF_ADDON,CFSS_PCRF";
		executionContext.getAttributes().put("callbackCorrelationId",
				callbackEvent + ":" + executionContext.getAttributes().get(GenericConstants.SUB_ORDER_ID));
		var delServiceOrderItemList = createDeleteServiceOrderItem();
		var addServiceOrderItemList = createAddServiceOrderItem();
		var baseServiceOrderItemList = modifyServiceOrderItem(newBasePlan, oldBasePlan);

		if (ObjectUtils.isNotEmpty(addServiceOrderItemList))
			serviceOrderItemList.addAll(addServiceOrderItemList);

		if (ObjectUtils.isNotEmpty(baseServiceOrderItemList))
			serviceOrderItemList.addAll(baseServiceOrderItemList);

		if (ObjectUtils.isNotEmpty(delServiceOrderItemList))
			serviceOrderItemList.addAll(delServiceOrderItemList);

		serviceOrder.setServiceOrderItem(serviceOrderItemList);
		if (ObjectUtils.isNotEmpty(serviceOrder))
			request = objectMapper.writeValueAsString(serviceOrder);
		return request;


	}

	 protected String getApplicationConfigValue(String configName) {
	        var appConfig = cache.getCacheDetailsFromDBMap(
	                NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG, configName);
	        return appConfig != null ? appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()) : null;
	    }


	protected List<ServiceOrderItem> modifyServiceOrderItem(Subscription newPlan, Subscription oldPlan) {
		var somFetchServices = getSOMServiceRegistry();
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		List<SOMService> oldBaseServices = new ArrayList<>();
		if (ObjectUtils.isNotEmpty(somFetchServices) && ObjectUtils.isNotEmpty(oldPlan)
				&& StringUtils.isNotEmpty(oldPlan.getSubscriptionId())) {
			somFetchServices.forEach(service -> {
				if (ObjectUtils.isNotEmpty(service.getServiceCharacteristic())) {
					service.getServiceCharacteristic().forEach(serv -> {
						if (serv.getValue().equalsIgnoreCase(oldPlan.getSubscriptionId()))
							oldBaseServices.add(service);
					});
				}
			});
		}
		if (ObjectUtils.isNotEmpty(oldBaseServices)) {
			if (ObjectUtils.isNotEmpty(newPlan)) {
				planId = newPlan.getPlanId();
				ocsPlanId=newPlan.getOcsPlanId();
				expiryDate = newPlan.getExpiryDate();
				if (ObjectUtils.isNotEmpty(newPlan.getCfss())) {
					newPlan.getCfss().removeIf(cfs -> Boolean.parseBoolean(cfs.getSkipSom()));
					newPlan.getCfss().forEach(cfss -> {
						var modifyList = new ArrayList<ServiceOrderItem>();
						oldBaseServices.stream().filter(service -> cfss.getName().equalsIgnoreCase(service.getName()))
								.forEach(service -> {
									if(!Arrays.asList(cfsPCRF.split(",")).contains(cfss.getName()))
									{
									var serviceOrderItem = createModifyServiceOrderItem(service, cfss, "CFS", newPlan);
									modifyList.add(serviceOrderItem);
									serviceOrderItemList.add(serviceOrderItem);
									}
								});
						if (ObjectUtils.isEmpty(modifyList)) {
							var createServiceOrderItem = createServiceOrderItem(cfss, "CFS");
							serviceOrderItemList.add(createServiceOrderItem);
						}
						if(!Arrays.asList(cfsPCRF.split(",")).contains(cfss.getName()))
						{
						oldBaseServices.removeIf(service -> cfss.getName().equalsIgnoreCase(service.getName()));
						}
					});
				}
				if (ObjectUtils.isNotEmpty(newPlan.getPrss())) {
					newPlan.getPrss().forEach(prss -> {
						var modifyList = new ArrayList<ServiceOrderItem>();
						oldBaseServices.stream().filter(service -> prss.getName().equalsIgnoreCase(service.getName()))
								.forEach(service -> {
									var serviceOrderItem = createModifyServiceOrderItem(service, prss, "PRS", newPlan);
									modifyList.add(serviceOrderItem);
									serviceOrderItemList.add(serviceOrderItem);
								});
						if (ObjectUtils.isEmpty(modifyList)) {
							var createServiceOrderItem = createServiceOrderItem(prss, "PRS");
							serviceOrderItemList.add(createServiceOrderItem);
						}
						oldBaseServices.removeIf(service -> prss.getName().equalsIgnoreCase(service.getName()));

					});
				}
				if (ObjectUtils.isNotEmpty(newPlan.getLrss())) {
					newPlan.getLrss().forEach(lrss -> {
						var modifyList = new ArrayList<ServiceOrderItem>();
						oldBaseServices.stream().filter(service -> lrss.getName().equalsIgnoreCase(service.getName()))
								.forEach(service -> {
									var serviceOrderItem = createModifyServiceOrderItem(service, lrss, "LRS", newPlan);
									modifyList.add(serviceOrderItem);
									serviceOrderItemList.add(serviceOrderItem);
								});
						if (ObjectUtils.isEmpty(modifyList)) {
							var createServiceOrderItem = createServiceOrderItem(lrss, "LRS");
							serviceOrderItemList.add(createServiceOrderItem);
						}
						oldBaseServices.removeIf(service -> lrss.getName().equalsIgnoreCase(service.getName()));

					});
				}
				oldBaseServices.forEach(service -> {
					var deleteServiceOrderItem = createDeleteServiceOrderItem(service);
					serviceOrderItemList.add(deleteServiceOrderItem);
				});
			}
		} else {
			var baseServiceOrderItemList = addServiceOrderItem(newPlan);
			serviceOrderItemList.addAll(baseServiceOrderItemList);
		}

		return serviceOrderItemList;

	}

	private ServiceOrderItem createDeleteServiceOrderItem(SOMService service) {

		ServiceOrderItem serviceOrderItem = new ServiceOrderItem();

		if (ObjectUtils.isNotEmpty(service.getServiceCharacteristic())) {
			service.setState("terminated");
			service.setType(service.getCategory());
			service.getServiceSpecification().setVersion("1");
			service.setName(null);
			service.setCategory(null);
			service.setHref(null);
			service.setServiceCharacteristic(null);
			serviceOrderItem = setServiceOrderItem(service);

		}
		return serviceOrderItem;
	}

	protected ServiceOrderItem createModifyServiceOrderItem(SOMService service, CFSRef cfss, String type, Subscription newPlan) {
		var serviceOrderItem = new ServiceOrderItem();
		index = index + 1;
		serviceOrderItem.setId(String.valueOf(index));
		serviceOrderItem.setAction("modifyUpdateSubscriptionId");
		serviceOrderItem.setType("ServiceOrderItem");
		var serviceItem = createModifyServiceItem(service, type, cfss, newPlan);
		serviceOrderItem.setService(serviceItem);
		return serviceOrderItem;
	}

	private SOMService createModifyServiceItem(SOMService service, String type, CFSRef cfss, Subscription newPlan) {
		service.setState("active");
		service.setType(type);
		/*
		 * var specification = new ProductSpecification(); specification.setName(service.getName());
		 * specification.setId(service.getId()); specification.setVersion("1");
		 * service.setServiceSpecification(specification);
		 */
		var characteristics = modifyServiceCharacteristic(cfss.getCharacteristics(), service.getServiceCharacteristic());
		if (/*type.equalsIgnoreCase("CFS") &&*/ ObjectUtils.isNotEmpty(newPlan) && StringUtils.isNotEmpty(newPlan.getPlanId())) {
			setSubscriptionId(characteristics, newPlan.getPlanId(), true);
		}
		service.setServiceCharacteristic(characteristics);
		return service;

	}

	private List<Characteristic> modifyServiceCharacteristic(List<CFSCharacteristicRef> characteristics,
			List<Characteristic> serviceCharacteristics) {
		List<Characteristic> newCharList = new ArrayList<>();
		boolean matchFound = false;
		for (CFSCharacteristicRef cfsCharacteristic : characteristics) {
			if (!"SUBSCRIPTION_ID".equals(cfsCharacteristic.getName())) {
				var newCharacteristic = new Characteristic();
				matchFound = false;
				for (Characteristic service : serviceCharacteristics) {
					if (service.getName().equalsIgnoreCase(cfsCharacteristic.getName())) {
						matchFound = true;
						if (!service.getValue().equalsIgnoreCase(cfsCharacteristic.getValue())
								&& ObjectUtils.isNotEmpty(service.getValue())) {
							newCharacteristic.setName(cfsCharacteristic.getName());
							newCharacteristic.setValue(service.getValue());
							newCharList.add(newCharacteristic);
						}
					}
				}
				if (!matchFound) {
					newCharacteristic.setName(cfsCharacteristic.getName());
					newCharacteristic.setValue(cfsCharacteristic.getValue());
					newCharList.add(newCharacteristic);
				}
			}
		}
		return newCharList;
	}

	/* To get the delete subscriptions from old cart */
	protected List<ServiceOrderItem> createDeleteServiceOrderItem() {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		List<Subscription> subscriptions = new ArrayList<>();
		List<SOMService> delServiceList = new ArrayList<>();
		List<Subscription> oldBaseSub = null;

		if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData())
				&& executionContext.getWorkflowData().containsKey("oldBaseOffer")) {
			oldBaseSub = getSubscriptions(executionContext.getWorkflowData().get("oldBaseOffer"));
			oldBasePlan = Objects.requireNonNull(oldBaseSub.stream().filter(sub -> StringUtils.isNotEmpty(sub.getPlanType())
					&& sub.getPlanType().equalsIgnoreCase(GenericConstants.PLAN_TYPE_BASE)).findFirst().orElse(null));
		}
		if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData())
				&& executionContext.getWorkflowData().containsKey("CancelSubscriptionCart")) {
			subscriptions = getSubscriptions(executionContext.getWorkflowData().get("CancelSubscriptionCart"));
		}

		if (ObjectUtils.isNotEmpty(subscriptions)) {
			/*
			 * delCommonAddons = subscriptions.stream().filter(sub ->
			 * sub.isCommonAddon()).collect(Collectors.toList()); subscriptions
			 * .removeIf(sub ->
			 * sub.getPlanType().equalsIgnoreCase(GenericConstants.PLAN_TYPE_BASE) ||
			 * sub.isCommonAddon());
			 */
			/*
			 * subscriptions.removeIf( sub ->
			 * sub.getPlanType().equalsIgnoreCase(GenericConstants.PLAN_TYPE_BASE));
			 */
			var somFetchServices = getSOMServiceRegistry();

			for (Subscription delSub : subscriptions) {
				if (ObjectUtils.isNotEmpty(somFetchServices)) {
					somFetchServices.forEach(service -> {
						if (ObjectUtils.isNotEmpty(service.getServiceCharacteristic())) {
							service.getServiceCharacteristic().stream()
									.filter(ch -> ch.getValue().equalsIgnoreCase(delSub.getSubscriptionId()))
									.forEach(serv -> {
										service.setState("terminated");
										service.setType(service.getCategory());
										service.getServiceSpecification().setVersion("1");
										service.setName(null);
										service.setCategory(null);
										service.setHref(null);
										service.setServiceCharacteristic(null);
										var serviceOrderItem = setServiceOrderItem(service);
										delServiceList.add(service);
										serviceOrderItemList.add(serviceOrderItem);
									});
						}

					});
				}
			}
		}
		return serviceOrderItemList;

	}

	protected List<SOMService> getSOMServiceRegistry() {
		List<SOMService> somFetchServices = new ArrayList<>();
		if (executionContext.getWorkflowData().containsKey("SOMFetchServiceRegistryResponseAttributes")) {
			somFetchServices = objectMapper.convertValue(
					executionContext.getWorkflowData().get("SOMFetchServiceRegistryResponseAttributes"),
					new TypeReference<List<SOMService>>() {});
		}
		if (ObjectUtils.isEmpty(somFetchServices)) {
			log.info(" No services found with the serviceId :: {}", msisdn);
			return null;
		}
		somFetchServices = somFetchServices.stream().filter(service -> service.getState().equalsIgnoreCase("active"))
				.collect(Collectors.toList());
		return somFetchServices;
	}

	protected List<ServiceOrderItem> deleteService(List<SOMService> somFetchServices, CFSRef cfss) {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		somFetchServices.stream().filter(service -> cfss.getName().equalsIgnoreCase(service.getName())).forEach(service -> {
			service.setState("terminated");
			service.setType(service.getCategory());
			service.getServiceSpecification().setVersion("1");
			service.setName(null);
			service.setCategory(null);
			service.setHref(null);
			service.setServiceCharacteristic(null);
			var serviceOrderItem = setServiceOrderItem(service);
			serviceOrderItemList.add(serviceOrderItem);
		});
		return serviceOrderItemList;
	}

	private ServiceOrderItem setServiceOrderItem(SOMService service) {
		var serviceOrderItem = new ServiceOrderItem();
		index = index + 1;
		serviceOrderItem.setId(String.valueOf(index));
		serviceOrderItem.setAction("delete_addon");
		serviceOrderItem.setType("ServiceOrderItem");
		serviceOrderItem.setService(service);
		return serviceOrderItem;
	}

	protected List<ServiceOrderItem> createAddServiceOrderItem() {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		List<Subscription> subscriptions = new ArrayList<>();
		getNumberDetails();
		if (ObjectUtils.isNotEmpty(orderPayload.getServiceManagement().getNewCart().getSubscriptions())) {
			subscriptions = orderPayload.getServiceManagement().getNewCart().getSubscriptions();
		}
		if (ObjectUtils.isNotEmpty(subscriptions)) {
			newBasePlan = Objects.requireNonNull(subscriptions.stream().filter(sub -> StringUtils.isNotEmpty(sub.getPlanType())
					&& sub.getPlanType().equalsIgnoreCase(GenericConstants.PLAN_TYPE_BASE)).findFirst().orElse(null));
			subscriptions.removeIf(sub -> sub.getPlanType().equalsIgnoreCase(GenericConstants.PLAN_TYPE_BASE));
			subscriptions.removeIf(sub -> sub.isCommonAddon());
			subscriptions.stream().forEach(subscription -> {
				if (ObjectUtils.isNotEmpty(delCommonAddons)) {
					for (Subscription delSub : delCommonAddons) {
						if (delSub.getPlanId().equalsIgnoreCase(subscription.getPlanId())) {
							var modifyServiceOrderItem = modifyServiceOrderItem(subscription, delSub);
							if (ObjectUtils.isNotEmpty(modifyServiceOrderItem))
								serviceOrderItemList.addAll(modifyServiceOrderItem);
						} else {
							var addServiceOrderItem = addServiceOrderItem(subscription);
							if (ObjectUtils.isNotEmpty(addServiceOrderItem))
								serviceOrderItemList.addAll(addServiceOrderItem);
						}
					}
				} else {
					var addServiceOrderItem = addServiceOrderItem(subscription);
					if (ObjectUtils.isNotEmpty(addServiceOrderItem))
						serviceOrderItemList.addAll(addServiceOrderItem);
				}
			});
		}

		return serviceOrderItemList;

	}

	protected List<ServiceOrderItem> addServiceOrderItem(Subscription subscription) {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		planId = subscription.getPlanId();
		ocsPlanId=subscription.getOcsPlanId();
		if (StringUtils.isNotEmpty(subscription.getExpiryDate()))
		{
			expiryDate = subscription.getExpiryDate();
		}
		if (ObjectUtils.isNotEmpty(subscription.getCfss())) {
			subscription.getCfss().stream().forEach(cfss -> {
				if (ObjectUtils.isNotEmpty(cfss.getSkipSom()) && Boolean.parseBoolean(cfss.getSkipSom())) {

				} else {
					
					var serviceOrderItem = createServiceOrderItem(cfss, "CFS");
					serviceOrderItemList.add(serviceOrderItem);
					
				}

			});
		}
		if (ObjectUtils.isNotEmpty(subscription.getPrss())) {
			subscription.getPrss().stream().forEach(prss -> {
				var serviceOrderItem = createServiceOrderItem(prss, "PRS");
				serviceOrderItemList.add(serviceOrderItem);
			});
		}
		if (ObjectUtils.isNotEmpty(subscription.getLrss())) {
			subscription.getLrss().stream().forEach(lrss -> {
				var serviceOrderItem = createServiceOrderItem(lrss, "LRS");
				serviceOrderItemList.add(serviceOrderItem);
			});
		}
		return serviceOrderItemList;

	}

	protected ServiceOrderItem createServiceOrderItem(CFSRef cfss, String type) {
		var serviceOrderItem = new ServiceOrderItem();
		index = index + 1;
		serviceOrderItem.setId(String.valueOf(index));
		serviceOrderItem.setAction("add_addon");
		serviceOrderItem.setType("ServiceOrderItem");
		var serviceItem = createServiceItem(cfss, type);
		serviceOrderItem.setService(serviceItem);
		return serviceOrderItem;
	}

	protected SOMService createServiceItem(CFSRef cfss, String type) {
		var serviceItem = new SOMService();
		serviceItem.setState("active");
		serviceItem.setType(type);
		var specification = new ProductSpecification();
		specification.setName(cfss.getName());
		specification.setId(cfss.getId());
		serviceItem.setServiceSpecification(specification);
		var characteristics = createServiceCharacteristic(cfss.getCharacteristics(),cfss.getName());
		if (type.equalsIgnoreCase("CFS")) {
			setSubscriptionId(characteristics, planId, false);
		}
		serviceItem.setServiceCharacteristic(characteristics);
		return serviceItem;
	}

	protected void setSubscriptionId(List<Characteristic> characteristics, String planId, boolean ismodifySubscriptionId) {

		if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData().get("subscriptionIdMap"))) {
			@SuppressWarnings("unchecked")
			HashMap<String, String> subscriptionIdMap = (HashMap<String, String>) executionContext.getWorkflowData()
					.get("subscriptionIdMap");
			if (subscriptionIdMap.get(planId) != null) {
				var characteristic = new Characteristic();
				characteristic.setName("SUBSCRIPTION_ID");
				characteristic.setValueType("String");
				characteristic.setValue(subscriptionIdMap.get(planId));
				if (ismodifySubscriptionId)
					characteristic.setFinalValue(subscriptionIdMap.get(planId));
				characteristics.add(characteristic);
			}

		}
	}

	protected List<Characteristic> createServiceCharacteristic(List<CFSCharacteristicRef> characteristics, String cfsName) {
		var characteristicList = new ArrayList<Characteristic>();
		  boolean isPcrfAddon= Arrays.asList(cfsPCRF.split(",")).contains(cfsName);
		if (ObjectUtils.isNotEmpty(characteristics))
			characteristics.stream().forEach(cfsCharacteristic -> {
				var characteristic = new Characteristic();
				if (!"SUBSCRIPTION_ID".equals(cfsCharacteristic.getName())) {
					characteristic.setName(cfsCharacteristic.getName());
					characteristic.setValueType(cfsCharacteristic.getDataType());
					if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("ICCID"))
						characteristic.setValue(iccid);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("IMSI"))
						characteristic.setValue(imsi);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("KI"))
						characteristic.setValue(ki);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("MSISDN"))
						characteristic.setValue(msisdn);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("meName"))
						characteristic.setValue(meName);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("ALGOID"))
						characteristic.setValue(algoId);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("KDBID"))
						characteristic.setValue(kdbId);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("CONNECTION_TYPE"))
						characteristic.setValue(connectionType);
					 else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
	                            && cfsCharacteristic.getName().equalsIgnoreCase("POLICY_NAME"))
	                        characteristic.setValue(ocsPlanId);
	                    else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
	                            && cfsCharacteristic.getName().equalsIgnoreCase("START_DATE")) 
	                        characteristic.setValue(getDate("START_DATE",isPcrfAddon));
	                    else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
	                            && cfsCharacteristic.getName().equalsIgnoreCase("END_DATE")) 
	                        characteristic.setValue(getDate("END_DATE",isPcrfAddon));
	                    else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
	                            && cfsCharacteristic.getName().equalsIgnoreCase("mobileOperator") && 
	                            StringUtils.isNotEmpty( executionContext.getAttributes().get(MNPConstants.RECIPIENT_OPERATOR_NAME)))
	                        characteristic.setValue(executionContext.getAttributes().get(MNPConstants.RECIPIENT_OPERATOR_NAME));		
	                    else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
	                            && cfsCharacteristic.getName().equalsIgnoreCase("ownerTelco") && 
	                            StringUtils.isNotEmpty( executionContext.getAttributes().get(MNPConstants.OWNER_OPERATOR_NAME)))
	                        characteristic.setValue("");
	                    else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
	                            && cfsCharacteristic.getName().equalsIgnoreCase("sourceTelco") && 
	                            StringUtils.isNotEmpty( executionContext.getAttributes().get(MNPConstants.DONOR_OPERATOR_NAME)))
	                        characteristic.setValue("");
	                    else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
	                            && cfsCharacteristic.getName().equalsIgnoreCase("destTelco") && 
	                            StringUtils.isNotEmpty( executionContext.getAttributes().get(MNPConstants.RECIPIENT_OPERATOR_NAME)))
	                        characteristic.setValue("");
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("portIdentifier")) {
						if (StringUtils.equalsAnyIgnoreCase(executionContext.getOrder().getOrderType(), OrderTypes.MNP_PORT_IN, OrderTypes.INTERIM_NUMBER_PORT_IN))
							characteristic.setValue("PORT_IN");
						else
							characteristic.setValue("");
					}
					else
						characteristic.setValue(cfsCharacteristic.getValue());
					characteristicList.add(characteristic);
				}
			});
		return characteristicList;
	}
	 private String getDate(String dateType,boolean isPcrf ) {
	    	
	    	String defaultDateTimeFormat="yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
			String  pcrfDateTimeFormat="dd-MM-yyyy HH:mm:ss";
	    	
	    	String resultDate="";
	    	
	    	if(dateType.equals("START_DATE")) {
	    		if(isPcrf)
	    			resultDate= LocalDateTime.now().format(DateTimeFormatter.ofPattern(pcrfDateTimeFormat));
	    		else
	    			resultDate= LocalDateTime.now().format(DateTimeFormatter.ofPattern(defaultDateTimeFormat));
	    	}
	    	else if (dateType.equals("END_DATE")) {
	    		if(isPcrf) {
	    			var inputDate= LocalDateTime.parse(expiryDate,DateTimeFormatter.ofPattern(defaultDateTimeFormat) );
	    			resultDate= inputDate.format(DateTimeFormatter.ofPattern(pcrfDateTimeFormat));
	    		}
	    		else
	    			resultDate= expiryDate;
	    			
	    	}
	    	
	    	return resultDate;
	    }

	protected void getNumberDetails() {
		try {
			if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData())
					&& executionContext.getWorkflowData().containsKey("SOMFetchServiceRegistryResponseAttributes")) {

				var serviceList = objectMapper.convertValue(
						executionContext.getWorkflowData().get("SOMFetchServiceRegistryResponseAttributes"),
						new TypeReference<List<SOMService>>() {});
				if (ObjectUtils.isNotEmpty(serviceList)) {
					for (SOMService service : serviceList) {
						if (ObjectUtils.isNotEmpty(service.getServiceSpecification())
								&& ObjectUtils.isNotEmpty(service.getServiceSpecification().getName())
								&& StringUtils.equalsIgnoreCase(service.getServiceSpecification().getName(), "PRS_SIM")) {
							var characteristics = service.getServiceCharacteristic();
							for (Characteristic characteristic : characteristics) {
								if (ObjectUtils.isNotEmpty(characteristic.getName())
										&& ObjectUtils.isNotEmpty(characteristic.getValue())
										&& StringUtils.equalsIgnoreCase(characteristic.getName(), "KI"))
									ki = characteristic.getValue();

								else if (ObjectUtils.isNotEmpty(characteristic.getName())
										&& ObjectUtils.isNotEmpty(characteristic.getValue())
										&& StringUtils.equalsIgnoreCase(characteristic.getName(), "IMSI"))
									imsi = characteristic.getValue();
								else if (ObjectUtils.isNotEmpty(characteristic.getName())
										&& ObjectUtils.isNotEmpty(characteristic.getValue())
										&& StringUtils.equalsIgnoreCase(characteristic.getName(), "ICCID"))
									iccid = characteristic.getValue();
								else if (ObjectUtils.isNotEmpty(characteristic.getName())
										&& ObjectUtils.isNotEmpty(characteristic.getValue())
										&& StringUtils.equalsIgnoreCase(characteristic.getName(), "meName"))
									meName = characteristic.getValue();
								else if (ObjectUtils.isNotEmpty(characteristic.getName())
										&& ObjectUtils.isNotEmpty(characteristic.getValue())
										&& StringUtils.equalsIgnoreCase(characteristic.getName(), "ALGOID"))
									algoId = characteristic.getValue();
								else if (ObjectUtils.isNotEmpty(characteristic.getName())
										&& ObjectUtils.isNotEmpty(characteristic.getValue())
										&& StringUtils.equalsIgnoreCase(characteristic.getName(), "KDBID"))
									kdbId = characteristic.getValue();

							}
						}
					}
				}
			}
		} catch (Exception e) {
			log.error(" Exception occurred in getKIDetails ", e);
		}

	}

	protected List<Subscription> getSubscriptions(Object subscription) {
		List<Subscription> subscriptions = new ArrayList<>();
		try {
			var response = in.co.sixdee.bss.common.util.JsonUtils.marshall(subscription, null);
			subscriptions = objectMapper.readValue(response, new TypeReference<List<Subscription>>() {});
		} catch (Exception e) {
			log.error(" :::::  Exception occured in getSubscriptions execute method  :::::", e);
		}

		return subscriptions;
	}
}
