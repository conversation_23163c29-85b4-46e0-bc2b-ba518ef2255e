package in.co.sixdee.bss.com.orderorchestrator.model.mnp.connectService;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import lombok.Data;

import java.io.Serializable;


//@XmlRootElement(name = "connectService")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class ConnectService implements Serializable {

	private static final long serialVersionUID = 1L;

	@XmlElement(name = "requestId",namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private RequestId requestId;
	@XmlElement(name = "ServiceDetailElement", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private ServiceDetailElements serviceDetailElement;
	
}
