package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.jolt.CustomOperations.getExpiryDate;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.OrderItem;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SomUpdateEndDate;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2
@Component(value = "somUpdateSubscription")
@RequiredArgsConstructor
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SOMUpdateSubscription extends AbstractDelegate {

	private Order orderPayload = null;

	protected String request = null;

	protected int index = 0;

	protected final ObjectMapper objectMapper;

	@Override
	protected void execute() throws Exception {
		try {
			orderPayload = executionContext.getOrder();
			request = createSOMRequest();
			var callThirdPartyDTO = callThirdParty(request);
			if (callThirdPartyDTO == null) {
				executionContext.setError(true);
				return;
			}
			validateResponse(callThirdPartyDTO);
		} catch (Exception e) {
			log.error("{} In {} .execute Exception occured ", executionContext.getTraceId(), this.getClass().getName(),
					e);
		}
	}

	protected String createSOMRequest() throws Exception {
		var serviceOrder = new SOMServiceOrderDTO();
		serviceOrder.setRegistryId(executionContext.getEntityId());
		serviceOrder.setExternalServiceId(executionContext.getAttributes().get(GenericConstants.SERVICE_ID));
		getSubscriptionIdAndExpiryDateFromRequest(executionContext);
		String subscriptionId = executionContext.getAttributes().get("requestSubscriptionId");
		String expiryDate = getExpiryDateForSomRequest(executionContext.getAttributes().get("requestExpiryDate"));
		List<String> somIdsList = compareSubscriptionIdFromSomResponseAndRequest(serviceOrder, subscriptionId);
		if (StringUtils.isNotEmpty(expiryDate) && ObjectUtils.isNotEmpty(somIdsList)) {
			serviceOrder.setUpdateEndDate(createUpdateEndDateForSom(expiryDate, somIdsList));
		}
		return objectMapper.writeValueAsString(serviceOrder);
	}

	private List<SomUpdateEndDate> createUpdateEndDateForSom(String expiryDate, List<String> somIdsList) {
		List<SomUpdateEndDate> endDateList = new ArrayList<>();
		for (String somId : somIdsList) {
			SomUpdateEndDate somUpdateEndDate = new SomUpdateEndDate();
			somUpdateEndDate.setEndDate(expiryDate);
			somUpdateEndDate.setServiceId(somId);
			endDateList.add(somUpdateEndDate);
		}
		return endDateList;
	}

	private String getExpiryDateForSomRequest(String expiryDate) {
		if (StringUtils.isNotEmpty(expiryDate)) {
			LocalDateTime requestExpiryDate = LocalDateTime.parse(expiryDate,
					DateTimeFormatter.ofPattern(GenericConstants.TIMESTAMP_FORMAT));
			return requestExpiryDate.format(DateTimeFormatter.ofPattern(GenericConstants.SOM_DATE_FORMAT));
		}
		return null;
	}

	private List<String> compareSubscriptionIdFromSomResponseAndRequest(SOMServiceOrderDTO serviceOrder,
			String subscriptionId) {
		List<String> somIds = new ArrayList<>();
		List<SOMServiceOrderDTO.SOMService> somFetchServices = getSOMServiceRegistry();
		if (ObjectUtils.isNotEmpty(somFetchServices)) {
			for (SOMService somService : somFetchServices) {
				fetchIdFromSomResponse(somService, subscriptionId, somIds);
			}
		} else {
			log.info("Som fetch registry response is empty...not able to form som  request..");
		}
		return somIds;
	}

	private void fetchIdFromSomResponse(SOMService somService, String subscriptionId, List<String> somIds) {
		List<Characteristic> characteristics = somService.getServiceCharacteristic();
		for (Characteristic data : characteristics) {
			if ("SUBSCRIPTION_ID".equalsIgnoreCase(data.getName()) && StringUtils.isNotEmpty(data.getValue())) {
				if (StringUtils.isNotEmpty(subscriptionId) && subscriptionId.equalsIgnoreCase(data.getValue())
						&& StringUtils.isNotEmpty(somService.getId())) {
					somIds.add(somService.getId());
				}
			}
		}
	}

	private void getSubscriptionIdAndExpiryDateFromRequest(OrderFlowContext orderFlowContext) {
		List<Characteristic> characteristic = orderFlowContext.getOrder().getOrderItem().get(0).getItemCharacteristic();
		if (ObjectUtils.isNotEmpty(characteristic)) {
			for (Characteristic chara : characteristic) {
				if (chara.getName().equalsIgnoreCase("SUBSCRIPTION_ID") && ObjectUtils.isNotEmpty(chara.getValue())) {
					orderFlowContext.getAttributes().put("requestSubscriptionId", chara.getValue());
				}
				if (chara.getName().equalsIgnoreCase("expiryDate") && ObjectUtils.isNotEmpty(chara.getValue())) {
					orderFlowContext.getAttributes().put("requestExpiryDate", chara.getValue());
				}
			}
		}
	}

	private List<SOMServiceOrderDTO.SOMService> getSOMServiceRegistry() {
		List<SOMServiceOrderDTO.SOMService> somFetchServices = new ArrayList<>();
		if (executionContext.getWorkflowData().containsKey("SOMFetchServiceRegistryResponseAttributes")) {
			somFetchServices = objectMapper.convertValue(
					executionContext.getWorkflowData().get("SOMFetchServiceRegistryResponseAttributes"),
					new TypeReference<List<SOMServiceOrderDTO.SOMService>>() {
					});
		}
		if (ObjectUtils.isEmpty(somFetchServices)) {
			log.info(" No services found with the serviceId :: {}",
					executionContext.getAttributes().get(GenericConstants.SERVICE_ID));
			return null;
		}

		return somFetchServices;
	}

}