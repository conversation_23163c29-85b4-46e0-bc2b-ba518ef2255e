package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "bsFetchShareBundleDetails")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class BS_FetchShareBundleDetails extends AbstractDelegate {

	@Override
	protected void execute() throws Exception {
		execution.setVariable("isDeleteOwner", false);
		String request = getRequestFromSpec();
		if (executionContext.isError())
			return;

		var callThirdPartyDTO = callThirdParty(request);
		if (callThirdPartyDTO == null) {
			executionContext.setError(true);
			return;
		}
//		var response = callThirdPartyDTO.getResponse();
		String response = "{\"draw\":0,\"recordsFiltered\":1,\"recordsTotal\":1,\"totalPages\":1,\"last\":true,\"data\":[{\"groupSeqId\":\"G_214\",\"sponsorMsisdn\":\"659346170362\",\"status\":1,\"createDate\":\"2025-05-06T15:33:03.000Z\",\"updateDate\":\"2025-05-06T15:33:03.000Z\",\"ocsServiceSeqId\":50000022285718,\"counter\":0,\"typeOfMsisdn\":\"Sponsored\",\"sponsorServiceSeqId\":50000022285718,\"beneficiaryList\":[{\"seqId\":12471,\"sponsorMsisdn\":\"659346170362\",\"beneficiaryMsisdn\":\"659346170366\",\"beneficiaryQuota\":\"100\",\"groupSeqId\":\"G_214\",\"status\":1,\"createDate\":\"2025-05-06T15:33:03.000Z\",\"updateDate\":\"2025-05-06T15:33:03.000Z\",\"ocsServiceSeqId\":50000022285730,\"beneficiaryServiceSeqId\":50000022285730}]}],\"channelId\":\"CRM\",\"entityId\":\"200\",\"requestId\":\"45610\",\"transactionId\":\"139658221511340121\",\"responseTimestamp\":\"2025-05-06T11:02:31.365Z\"}";
//		validateResponse(callThirdPartyDTO);
		if (executionContext.isError())
			return;
		getShareBundleDetails(response);
		modifyWorkflowData(response);
	}

	private void getShareBundleDetails(String response) throws JsonProcessingException {
		var billingResponse = objectMapper.readTree(response);
		if (ObjectUtils.isNotEmpty(billingResponse) && ObjectUtils.isNotEmpty(billingResponse.get("data"))
				&& ObjectUtils.isNotEmpty(billingResponse.get("data").get(0))) {
			JsonNode beneficiaryListNode = billingResponse.get("data").get(0).get("beneficiaryList");
			if (ObjectUtils.isNotEmpty(beneficiaryListNode) && beneficiaryListNode.size() == 0)
				execution.setVariable("isDeleteOwner", true);
			else
				execution.setVariable("isDeleteOwner", false);
		}
	}
}
