package in.co.sixdee.bss.com.orderorchestrator.service.midelegates;

import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderPayloadService;
import in.co.sixdee.bss.com.orderorchestrator.service.ProcessBatchConfig;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.camunda.bpm.engine.variable.Variables;
import org.camunda.bpm.engine.variable.value.ObjectValue;
import org.springframework.stereotype.Service;

import com.bazaarvoice.jolt.JsonUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Log4j2
public class GenerateDataDelegateExtendExpiryDate implements JavaDelegate {

    private final ProcessDataAccessor processDataAccessor;

    private final ProcessBatchConfig processBatchConfig;

    private final OrderPayloadService orderPayloadService;


    @Override
    public void execute(DelegateExecution execution) throws Exception {
        List<String> subOrderIds =null;
        OrderFlowContext orderFlowContext = processDataAccessor.getOrderFlowContext(execution);
        
         subOrderIds = orderFlowContext.getOrder().getOrderItem().stream().map(s -> s.getSubOrderId()).collect(Collectors.toList());
        ObjectValue recordsObj = Variables.objectValue(subOrderIds)
                .serializationDataFormat(Variables.SerializationDataFormats.JSON).create();

        execution.setVariable("records", recordsObj);
        execution.setVariable("createdInstancesCount", 0);
        execution.setVariable("totalInstances", subOrderIds.size());

    }

}
