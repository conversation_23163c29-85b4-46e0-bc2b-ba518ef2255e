package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.AppConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.constants.GenericLogConstants;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Log4j2
@Component(value = "somChangeMsisdn")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SOMChangeMsisdn extends AbstractDelegate {

    private Order orderPayload = null;

    protected String request = null;

    protected int index = 0;

    @Autowired
    private ObjectMapper objectMapper;
    protected String algoId = null;
    protected String kdbId = null;

    @Override
    protected void execute() throws Exception {
        try {
            orderPayload = executionContext.getOrder();

            request = createSOMRequest();
            var callThirdPartyDTO = callThirdParty(request);
            if (callThirdPartyDTO == null) {
                executionContext.setError(true);
                return;
            }
            validateResponse(callThirdPartyDTO);
        } catch (Exception e) {
            log.error("{} In {} .execute Exception occurred ", executionContext.getTraceId(), this.getClass().getName(),
                    e);
        }
    }

    protected String createSOMRequest() throws Exception {
        var serviceOrder = new SOMServiceOrderDTO();
        executionContext.getAttributes().put("callbackCorrelationId",
                callbackEvent + ":" + executionContext.getAttributes().get(GenericConstants.SUB_ORDER_ID));
        serviceOrder.setExternalId(orderPayload.getOrderId());
        serviceOrder.setDescription(orderPayload.getDescription());
        serviceOrder.setPriority("1");
        serviceOrder.setCategory("ChangeMsisdn");
        serviceOrder.setRegistryId(executionContext.getEntityId());
        serviceOrder.setRequestedStartDate(
                StringUtils.isNotEmpty(orderPayload.getRequestedStartDate()) ? orderPayload.getRequestedStartDate()
                        : Instant.now().toString());
        serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
        serviceOrder.setType("ServiceOrder");       
        if(OrderTypes.INTERIM_NUMBER_PORT_IN.equals(orderType)) {
            serviceOrder.setExternalServiceId(executionContext.getOrder().getServiceManagement().getOldMsisdn());
            
        }
        else
        serviceOrder.setExternalServiceId(executionContext.getAttributes().get(GenericConstants.SERVICE_ID));
        
        serviceOrder.setServiceOrderItem(createServiceOrderItem());
        request = objectMapper.writeValueAsString(serviceOrder);
        return request;
    }

    private List<ServiceOrderItem> createServiceOrderItem() {
        List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
        try {
            List<SOMService> services = null;
        	//boolean isMvnoPortin=MNPConstants.PORT_IN_TYPE_MVNO_MVNO.equals(execution.getVariable(AppConstants.ProcessVariables.PORT_IN_TYPE.value()));
			boolean lmpDBcharReqd = shouldPassLmpDbCharacteristics();

            if (executionContext.getWorkflowData().containsKey("SOMFetchservicesResponseAttributes")) {
                services = objectMapper.convertValue(executionContext.getWorkflowData().get("SOMFetchservicesResponseAttributes"), new TypeReference<List<SOMService>>() {
                });
            }
            if (ObjectUtils.isNotEmpty(services)) {
            	for (SOMService service : services) {
            		if (service != null ) { 
            			var serviceOrderItem = new ServiceOrderItem();

        				if( "LRS_MSISDN".equals(service.getServiceSpecification().getName()) ||  "CFSS_LMP_DB_UPDATE".equals(service.getServiceSpecification().getName()))
        				{
        					
        					for (Characteristic serviceCharacteristic : service.getServiceCharacteristic()) {

        						if ("MSISDN".equalsIgnoreCase(serviceCharacteristic.getName())) 
        						{ 
        							serviceCharacteristic.setValue(executionContext.getOrder().getServiceManagement().getNewMsisdn());
        						}

        						if(StringUtils.equalsAnyIgnoreCase(executionContext.getOrder().getOrderType(), OrderTypes.MNP_PORT_IN, OrderTypes.INTERIM_NUMBER_PORT_IN))
        						{
        							if (ObjectUtils.isNotEmpty(serviceCharacteristic.getName())
        									&& serviceCharacteristic.getName().equalsIgnoreCase("ownerTelco") && 
        									StringUtils.isNotEmpty( executionContext.getAttributes().get(MNPConstants.OWNER_OPERATOR_NAME))) {
        								if(!lmpDBcharReqd)
        									serviceCharacteristic.setValue("");
        								else
        									serviceCharacteristic.setValue(findMNOByMvno(executionContext.getAttributes().get(MNPConstants.OWNER_OPERATOR_NAME)));
        							}

        							else if (ObjectUtils.isNotEmpty(serviceCharacteristic.getName())
        									&& serviceCharacteristic.getName().equalsIgnoreCase("sourceTelco") && 
        									StringUtils.isNotEmpty( executionContext.getAttributes().get(MNPConstants.DONOR_OPERATOR_NAME))) {
        								if(!lmpDBcharReqd)
        									serviceCharacteristic.setValue("");
        								else
        									serviceCharacteristic.setValue(findMNOByMvno(executionContext.getAttributes().get(MNPConstants.DONOR_OPERATOR_NAME)));		

        							}
        							else if (ObjectUtils.isNotEmpty(serviceCharacteristic.getName())
        									&& serviceCharacteristic.getName().equalsIgnoreCase("destTelco") && 
        									StringUtils.isNotEmpty( executionContext.getAttributes().get(MNPConstants.RECIPIENT_OPERATOR_NAME))) {
        								if(!lmpDBcharReqd)
        									serviceCharacteristic.setValue("");
        								else
        									serviceCharacteristic.setValue(findMNOByMvno(executionContext.getAttributes().get(MNPConstants.RECIPIENT_OPERATOR_NAME)));		
        							}
        							else if (ObjectUtils.isNotEmpty(serviceCharacteristic.getName())
        									&& serviceCharacteristic.getName().equalsIgnoreCase("portIdentifier"))
        							{
        								serviceCharacteristic.setValue("INTERIM_PORTIN");	
        							}

        						}

        						//break;
        					}
        					if("CFSS_LMP_DB_UPDATE".equals(service.getServiceSpecification().getName()) && OrderTypes.CHANGE_MSISDN.equals(orderType))
        					{
        					}
        					else
        					{
        					service.setType(service.getCategory());
        					serviceOrderItem.setService(service);
        					serviceOrderItem.setId("1");
        					if(OrderTypes.INTERIM_NUMBER_PORT_IN.equals(orderType)||OrderTypes.CHANGE_MSISDN.equals(orderType))
        						serviceOrderItem.setAction("modify_msisdn");
        					else
        						serviceOrderItem.setAction("modifySim"); 
        					serviceOrderItem.setType("ServiceOrderItem");
        					serviceOrderItemList.add(serviceOrderItem);
        					}
        				}
        				
            		}
            	}
            	
            }
        } catch (Exception e) {
            log.error(GenericLogConstants.TAG_APP + " | Exception occured in createServiceOrderItem ", e);
        }

        return serviceOrderItemList;

    
}

	private boolean shouldPassLmpDbCharacteristics() {
		boolean configValue = false;
		CacheTableDataDTO mvnoConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
				CacheConstants.CacheFields.LMP_DB_UPDATE_REQD_FOR_PORTIN_FROM_SINGTEL.toString());
		if (mvnoConfig != null)
			configValue = Boolean.parseBoolean(mvnoConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString()));

		var portInType = execution.getVariable(AppConstants.ProcessVariables.PORT_IN_TYPE.value());
		if (MNPConstants.PORT_IN_TYPE_MVNO_MVNO.equals(portInType)) {
			return false;
		}
		if (MNPConstants.PORT_IN_TYPE_FROM_SINGTEL.equals(portInType))
			return configValue;
		return true;
	}

	private String findMNOByMvno(String mvno) { 
    	//Finding MNO name by MVNO
    	String mno=null;
        CacheTableDataDTO operatorConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_MVNO_MNO_MAPPING_CONFIG.toString(), mvno);
        if (operatorConfig != null) {
        	mno = operatorConfig.getNgTableData().get(CacheConstants.CacheFields.MNO.toString());
            log.info("MNO name {} found from configuration table {} ", mno, CacheConstants.CacheKeys.COM_MVNO_MNO_MAPPING_CONFIG.toString());
           
        }  
        return mno;
    }
}
