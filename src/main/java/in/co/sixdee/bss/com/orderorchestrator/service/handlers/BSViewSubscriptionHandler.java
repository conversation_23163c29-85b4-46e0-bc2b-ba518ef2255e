package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;

import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "BSViewSubscriptionHandler")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class BSViewSubscriptionHandler extends AbstractDelegate {

	@Override
	protected void execute() throws Exception {

		var callThirdPartyDTO = callThirdParty(null);
		if (callThirdPartyDTO == null) {
			executionContext.setError(true);
			return;
		}
		var response = callThirdPartyDTO.getResponse();
		validateResponse(callThirdPartyDTO);
		getGroupSubscriptionId(response,executionContext);
		if (executionContext.isError()) {
			log.error("{} Response validation failed", activityId);
			return;
		}

	}


      private void getGroupSubscriptionId(String response,OrderFlowContext executionContext) throws JsonMappingException, JsonProcessingException {
    	  
    	  var billingResponse=objectMapper.readTree(response);
    	  if(ObjectUtils.isNotEmpty(billingResponse)) {
    		  var subscriptions = objectMapper.convertValue(billingResponse.get("data"), new TypeReference<List<Subscription>>() {
    			});
				  if(ObjectUtils.isNotEmpty(subscriptions))
    		  subscriptions.forEach(subscription->{
    			  if(subscription.getPlanGroupId().equals(executionContext.getOrder().getGroupManagement().getGroupId())) {
    				  executionContext.getAttributes().put("groupPlanSubscriptionId", subscription.getSubscriptionId());
    				  executionContext.getAttributes().put("groupPlanOCSSubscriptionId", subscription.getOcsSubscriptionId());
    			  }
    		  });
    	  }
	
      }
}
