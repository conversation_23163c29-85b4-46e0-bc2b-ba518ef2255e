
package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.WaitingProcessInfoEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface WaitingProcessInfoRepository extends JpaRepository<WaitingProcessInfoEntity, Long> {

    @Query("select wp from WaitingProcessInfoEntity wp where wp.orderId = :orderId and wp.waitType IN (:waitType)")
    public WaitingProcessInfoEntity findProcessInfoByOrderId(@Param("orderId") String orderId, @Param("waitType") List<String> waitType);

    @Transactional
    @Modifying
    @Query("delete from WaitingProcessInfoEntity wp where wp.seqId = :seqId")
    public void deletebySeqId(@Param("seqId") Long seqId);

    @Query("select wp from WaitingProcessInfoEntity wp where wp.orderId = :orderId and wp.subOrderId=:subOrderId and wp.waitType IN (:waitType)")
    public WaitingProcessInfoEntity findProcessInfoByOrderIdAndSubOrderId(@Param("orderId") String orderId,
                                                                          @Param("subOrderId") String subOrderId, @Param("waitType") List<String> waitType);

    @Query(value = "select wpi.SEQ_ID , wpi.ORDER_ID , wpi.SUB_ORDER_ID, wpi.EVENT_NAME, wpi.STAGE_CODE, wpi.PROCESS_INSTANCE_ID, wpi.EXECUTION_ID from WAITING_PROCESS_INFO wpi inner join COM_ORDER_MASTER om on om.ORDER_ID = wpi.ORDER_ID where wpi.EVENT_NAME = :eventName and TIMESTAMPDIFF(hour,wpi.CREATED_ON,now()) >= :maxWaitingHours and om.ORDER_TYPE in :orderTypes and (wpi.LOCK_EXP_TIME is null OR wpi.LOCK_EXP_TIME < now())", nativeQuery = true)
    public List<Object[]> getPaymentWaitingOrders(@Param("maxWaitingHours") int maxWaitingHours, @Param("eventName") String eventName, @Param("orderTypes") List<String> orderTypes);

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public WaitingProcessInfoEntity save(WaitingProcessInfoEntity entity);

    @Transactional
    @Modifying
    @Query("delete from WaitingProcessInfoEntity wpi where wpi.orderId = :orderId and wpi.subOrderId= :subOrderId and wpi.waitType= :waitType")
    public void deletebyOrderIdSubOrderIdAndWaitType(@Param("orderId") String orderId, @Param("subOrderId") String subOrderId, @Param("waitType") String waitType);

    @Query("select o from WaitingProcessInfoEntity o where o.orderId=:orderId and o.subOrderId=:subOrderId and o.eventName=:eventName")
    public WaitingProcessInfoEntity existsByOrderIdSubOrderIdAndEventName(@Param("orderId") String orderId, @Param("subOrderId") String subOrderId, @Param("eventName") String eventName);

}
