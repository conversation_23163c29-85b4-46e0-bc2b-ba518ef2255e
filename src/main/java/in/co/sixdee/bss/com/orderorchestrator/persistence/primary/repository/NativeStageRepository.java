package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import in.co.sixdee.bss.com.orderorchestrator.config.ApplicationProperties;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

@Repository
@RequiredArgsConstructor
@Log4j2
@Transactional
public class NativeStageRepository {


	@PersistenceContext
	protected EntityManager em;

	protected final ApplicationProperties applicationProperties;


	public void save(OrderStageEntity stage) {
		var session = em.unwrap(Session.class);
		final String sql = "INSERT INTO COM_ORDER_STAGES (CREATED_BY, LAST_MODIFIED_BY, DESCRIPTION, EXECUTION_ORDER, NAME, ORDER_ID, STAGE_CODE, STATE, STATE_REASON, SUB_ORDER_ID, STAGE_ID,ENTITY_ID,USERNAME) VALUES (?,?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
		session.doWork(connection -> {
			try (PreparedStatement ps = connection.prepareStatement(sql)) {
				ps.setString(1, stage.getCreatedBy());
				ps.setString(2, stage.getLastModifiedBy());
				ps.setString(3, stage.getDescription());
				ps.setInt(4, stage.getExecutionOrder());
				ps.setString(5, stage.getName());
				ps.setLong(6, stage.getOrderId());
				ps.setString(7, stage.getStageCode());
				ps.setString(8, stage.getState());
				ps.setString(9, stage.getStateReason());
				ps.setLong(10, stage.getSubOrderId());
				ps.setLong(11, stage.getId());
				ps.setString(12, stage.getEntityId());
				ps.setString(13, stage.getUsername());
				ps.executeUpdate();
			} catch (SQLException e) {
				log.error("An exception occurred in NativeStageRepository.save", e);
			}
		});
	}


	public void saveAll(List<OrderStageEntity> orderStages) {
		var session = em.unwrap(Session.class);
		final String sql = "INSERT INTO COM_ORDER_STAGES (CREATED_BY, LAST_MODIFIED_BY, DESCRIPTION, EXECUTION_ORDER, NAME, ORDER_ID, STAGE_CODE, STATE, STATE_REASON, SUB_ORDER_ID, STAGE_ID,ENTITY_ID,USERNAME) VALUES (?,?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
		session.doWork(connection -> {
			try (PreparedStatement ps = connection.prepareStatement(sql)) {
				int i = 1;
				for (OrderStageEntity stage : orderStages) {
					ps.setString(1, stage.getCreatedBy());
					ps.setString(2, stage.getLastModifiedBy());
					ps.setString(3, stage.getDescription());
					ps.setInt(4, stage.getExecutionOrder());
					ps.setString(5, stage.getName());
					ps.setLong(6, stage.getOrderId());
					ps.setString(7, stage.getStageCode());
					ps.setString(8, stage.getState());
					ps.setString(9, stage.getStateReason());
					ps.setLong(10, stage.getSubOrderId());
					ps.setLong(11, stage.getId());
					ps.setString(12, stage.getEntityId());
					ps.setString(13, stage.getUsername());
					ps.addBatch();
					if (i % applicationProperties.getJdbcBatchSize() == 0)
						ps.executeBatch();
					i++;
				}
				ps.executeBatch();
			} catch (SQLException e) {
				log.error("An exception occurred in NativeStageRepository.saveAll", e);
			}
		});
	}
}
