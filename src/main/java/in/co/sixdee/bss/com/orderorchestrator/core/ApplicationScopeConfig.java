package in.co.sixdee.bss.com.orderorchestrator.core;

import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.ApplicationQueueEntity;
import in.co.sixdee.bss.com.orderorchestrator.service.ApplicationQueueService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

@Configuration
public class ApplicationScopeConfig {

	@Autowired
	protected ApplicationQueueService				queueService;

	@Autowired
	private ConcurrentLinkedQueue<OrderCallBack> applicationQueue;

	private List<OrderCallBack>						orderCallbackList;

	@Autowired
	QueueToByteBean									queueBean;

	@PostConstruct
	public void init() {
		boolean flag = false;
		if (ObjectUtils.isNotEmpty(queueService.findBasedOnInstanceId("SERVER1"))) {
			byte[] lob = queueService.findBasedOnInstanceId("SERVER1").get(0).getQueue();
			if (lob != null)
				orderCallbackList = queueBean
						.deserialize(queueService.findBasedOnInstanceId("SERVER1").get(0).getQueue());
			if (ObjectUtils.isNotEmpty(orderCallbackList)) {
				orderCallbackList.forEach(s -> applicationQueue.add(s));
				flag = true;
			}
		}
		if (flag) {
			queueService.remove();
		}
	}

	@PreDestroy
	public void destroy() {
		ApplicationQueueEntity queueEntity = queueBean.getQueueEntity();
		if (queueEntity != null && queueEntity.getQueue() != null)
			queueService.save(queueEntity);
	}

}
