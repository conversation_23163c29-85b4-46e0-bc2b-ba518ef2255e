package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "somAddSubscriptionForBeneficiary")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class SOMAddSubscriptionForBeneficiary extends AbstractDelegate {
	private Order orderPayload = null;

	protected String request = null;

	protected int index = 0;
	protected String msisdn = null;

	@Override
	protected void execute() throws Exception {
		try {
			orderPayload = executionContext.getOrder();
			request = createSOMRequest();
			log.info("Received order som  :: {} ", objectMapper.writeValueAsString(executionContext));
			var callThirdPartyDTO = callThirdParty(request);
			validateResponse(callThirdPartyDTO);
			
		} catch (Exception e) {
			log.error("{} In {} .execute Exception occured ", executionContext.getTraceId(), this.getClass().getName(),
					e);
		}
	}

	private String createSOMRequest() throws JsonProcessingException {
		var serviceOrder = new SOMServiceOrderDTO();
		setRequestBodyParams(serviceOrder);
		List<SOMServiceOrderDTO.ServiceOrderItem> serviceOrderItemList = createOrderItemForAddSubs(serviceOrder);
		serviceOrder.setServiceOrderItem(serviceOrderItemList);
		return objectMapper.writeValueAsString(serviceOrder);
	}

	private List<ServiceOrderItem> createOrderItemForAddSubs(SOMServiceOrderDTO serviceOrder) {
		List<SOMServiceOrderDTO.ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		List<SOMServiceOrderDTO.SOMService> somFetchServices = getSOMServiceRegistry();
		if (ObjectUtils.isNotEmpty(somFetchServices)) {
			for (SOMService somService : somFetchServices) {
				var serviceOrderItem = createServiceOrderItemFromFetch(somService);
				serviceOrderItemList.add(serviceOrderItem);
			}
		} else {
			log.info("Som fetch registry response is empty...not able to form som tos request..");
		}
		return serviceOrderItemList;
	}

	private ServiceOrderItem createServiceOrderItemFromFetch(SOMService somService) {
		var serviceOrderItem = new ServiceOrderItem();
		index = index + 1;
		serviceOrderItem.setId(String.valueOf(index));
		serviceOrderItem.setAction("add_addon");
		serviceOrderItem.setType("ServiceOrderItem");
		SOMService serviceItem = createServiceForServiceOrderItem(somService);
		serviceOrderItem.setService(serviceItem);
		return serviceOrderItem;

	}

	private SOMService createServiceForServiceOrderItem(SOMService somService) {
		somService.setType(somService.getCategory());
		somService.setState("active");
		somService.setId(null);
		somService.setName(null);
		somService.setCategory(null);
		List<Characteristic> characteristics = somService.getServiceCharacteristic();
		createCharacteristic(characteristics);
		return somService;

	}

	private List<Characteristic> createCharacteristic(List<Characteristic> characteristics) {
		for (Characteristic characteristic : characteristics) {
			if ("SUBSCRIPTION_ID".equalsIgnoreCase(characteristic.getName())
					&& StringUtils.isNotEmpty(characteristic.getValue())) {
				characteristic.setValue(getNewSubscriptionIdFromBs(characteristic.getValue()));
			}
		}
		return characteristics;

	}

	private String getNewSubscriptionIdFromBs(String oldSubscriptionId) {
		List<Subscription> bsSubscription = getSubscriptionFromBilling();
		for (Subscription subscription : bsSubscription) {
			if (oldSubscriptionId.equalsIgnoreCase(subscription.getOldSubscriptionId()))
				return subscription.getNewSubscriptionId();
		}

		return null;
	}

	private List<Subscription> getSubscriptionFromBilling() {
		List<Subscription> bsFetchSubscription = new ArrayList<>();

		if (executionContext.getWorkflowData().containsKey("BS_ViewGroupSubscriptionResponseAttributes")) {
			bsFetchSubscription = objectMapper.convertValue(
					executionContext.getWorkflowData().get("BS_ViewGroupSubscriptionResponseAttributes"),
					new TypeReference<>() {
					});
		}
		return bsFetchSubscription;
	}

	private List<SOMServiceOrderDTO.SOMService> getSOMServiceRegistry() {
		List<SOMServiceOrderDTO.SOMService> somFetchServices = new ArrayList<>();
		if (executionContext.getWorkflowData().containsKey("groupPlanCfss")) {
			somFetchServices = objectMapper.convertValue(executionContext.getWorkflowData().get("groupPlanCfss"),
					new TypeReference<List<SOMServiceOrderDTO.SOMService>>() {
					});
		}
		if (ObjectUtils.isEmpty(somFetchServices)) {
			log.info(" No services found with the serviceId :: {}", msisdn);
			return null;
		}
		somFetchServices = somFetchServices.stream().filter(service -> service.getState().equalsIgnoreCase("active"))
				.collect(Collectors.toList());
		return somFetchServices;
	}

	private void setRequestBodyParams(SOMServiceOrderDTO serviceOrder) {

		msisdn = getBeneficiaryMsisdnFromBsShareBundle();
		serviceOrder.setExternalId(orderPayload.getOrderId());
		serviceOrder.setDescription("AddSubscription");
		serviceOrder.setType("ServiceOrder");
		serviceOrder.setRequestedStartDate(
				StringUtils.isNotEmpty(orderPayload.getRequestedStartDate()) ? orderPayload.getRequestedStartDate()
						: Instant.now().toString());
		serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
		serviceOrder.setExternalServiceId(msisdn);
		serviceOrder.setRegistryId(fetchEntityId());
	}
	
	private String fetchEntityId() {
		if(OrderTypes.MNP_PORT_OUT.equalsIgnoreCase(executionContext.getOrder().getOrderType()) )
		return executionContext.getAttributes().get("donorEntityId");
		else
		return executionContext.getEntityId();
	}

	private String getBeneficiaryMsisdnFromBsShareBundle() {
		JsonNode billResp = objectMapper
				.valueToTree(executionContext.getWorkflowData().get("BSFetchShareBundleDetailsResponseAttributes"));
		if (ObjectUtils.isNotEmpty(billResp) && StringUtils.isNotEmpty(billResp.get("beneficiaryMsisdn").asText())) {
			return billResp.get("beneficiaryMsisdn").asText();
		}
		return null;
	}
}
