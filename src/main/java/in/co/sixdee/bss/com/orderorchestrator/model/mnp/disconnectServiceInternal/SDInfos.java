package in.co.sixdee.bss.com.orderorchestrator.model.mnp.disconnectServiceInternal;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class SDInfos {

	@XmlElement(name = "SDInfo", namespace = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private List<SDInfo> sdInfos;

	public List<SDInfo> getSdInfos() {
		return sdInfos;
	}

	public void setSdInfos(List<SDInfo> sdInfos) {
		this.sdInfos = sdInfos;
	}

}
