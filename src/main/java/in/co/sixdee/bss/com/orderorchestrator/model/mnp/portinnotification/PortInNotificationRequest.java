package in.co.sixdee.bss.com.orderorchestrator.model.mnp.portinnotification;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Data;

import java.io.Serializable;

@XmlRootElement(name = "PortInNotificationRequest")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class PortInNotificationRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	@XmlElement(name = "PortInNotification")
	private PortInNotification portInNotification;
	@XmlElement(name = "requestId")
	private String requestId;


}
