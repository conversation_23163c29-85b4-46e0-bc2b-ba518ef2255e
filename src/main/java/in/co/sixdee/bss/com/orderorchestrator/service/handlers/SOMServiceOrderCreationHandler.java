package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.*;
import in.co.sixdee.bss.com.orderorchestrator.model.ServiceContext;
import in.co.sixdee.bss.com.orderorchestrator.model.SubscriptionContext;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderPayloadService;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.order.*;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Log4j2
@Component(value = "somServiceOrderCreation")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SOMServiceOrderCreationHandler extends AbstractDelegate {

    @Autowired
    private OrderPayloadService orderPayloadService;


    @Override
    protected void execute() throws Exception {
        Order orderPayload = executionContext.getOrder();
        Service service = getService(orderPayload);

        if (isOnboardingOrAddService(orderPayload.getOrderType())) {
            setCfssIntoServiceSubscriptions(service);
        }

        String request = createSOMRequest(orderPayload, service);
        var callThirdPartyDTO = callThirdParty(request);
        if (callThirdPartyDTO == null) {
            executionContext.setError(true);
            return;
        }
        validateResponse(callThirdPartyDTO);
    }

    private Service getService(Order orderPayload) {
        if (OrderTypes.MNP_PORT_IN.equals(orderType)) {
            return orderPayload.getProfile().getAccount().getServiceGroups().get(0).getServices().get(0);
        }
        return getService();
    }

    private boolean isOnboardingOrAddService(String orderType) {
        return StringUtils.equalsAnyIgnoreCase(orderType,
                OrderTypes.ONBOARDING, OrderTypes.ADD_SERVICE, OrderTypes.ADD_SERVICE_TO_NEW_ACCOUNT);
    }

    private void setCfssIntoServiceSubscriptions(Service service) {
        var payloadContext = orderPayloadService.getOrderFlowContext(executionContext.getOrder().getOrderId());
        if (payloadContext == null) return;

        var sgId = service.getGroupId();
        if (sgId == null) return;

        ServiceGroups serviceGroup = null;
        List<ServiceGroups> serviceGroups = payloadContext.getOrder().getProfile().getAccount().getServiceGroups();
        for (ServiceGroups sg : serviceGroups) {
            if (sgId.equals(sg.getId())) {
                serviceGroup = sg;
                break;
            }
        }

        if (serviceGroup == null) return;

        for (Subscription subscription : service.getSubscriptions()) {
            setCfssToSubscription(serviceGroup, service, subscription);
        }
    }

    private void setCfssToSubscription(ServiceGroups serviceGroup, Service service, Subscription sub) {
        Subscription subscriptionFromPayload = findSubscriptionFromPayload(serviceGroup, service, sub);
        if (subscriptionFromPayload == null) return;

        sub.setCfss(new ArrayList<>());
        sub.setPrss(new ArrayList<>());
        sub.setLrss(new ArrayList<>());

        sub.setCfss(subscriptionFromPayload.getCfss());
        sub.setPrss(subscriptionFromPayload.getPrss());
        sub.setLrss(subscriptionFromPayload.getLrss());
    }

    private Subscription findSubscriptionFromPayload(ServiceGroups serviceGroup, Service service, Subscription sub) {
        if (serviceGroup.getSubscriptions() != null) {
            List<Subscription> subscriptions = serviceGroup.getSubscriptions();
            for (Subscription subPay : subscriptions) {
                if (subPay.getPlanId().equals(sub.getPlanId())) {
                    return subPay;
                }
            }
        }

        List<Service> services = serviceGroup.getServices();
        Service serviceFromPayload = null;
        for (Service svc : services) {
            if (svc.getServiceId().equals(service.getServiceId())) {
                serviceFromPayload = svc;
                break;
            }
        }

        if (serviceFromPayload == null) {
            return null;
        }

        List<Subscription> subscriptions = serviceFromPayload.getSubscriptions();
        for (Subscription subPay : subscriptions) {
            if (subPay.getPlanId().equals(sub.getPlanId())) {
                return subPay;
            }
        }

        return null;
    }


    private String createSOMRequest(Order orderPayload, Service service) throws Exception {
        SOMServiceOrderDTO serviceOrder = new SOMServiceOrderDTO();
        serviceOrder.setExternalId(orderPayload.getOrderId());
        serviceOrder.setDescription(orderPayload.getDescription());
        serviceOrder.setRegistryId(executionContext.getEntityId());
        serviceOrder.setRequestedStartDate(getRequestedStartDate(orderPayload));
        serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
        serviceOrder.setType("ServiceOrder");
        serviceOrder.setOrderType("onboarding");

        executionContext.getAttributes().put("callbackCorrelationId",
                callbackEvent + ":" + executionContext.getAttributes().get(GenericConstants.SUB_ORDER_ID));

        String msisdn = service.getServiceId();
        serviceOrder.setExternalServiceId(msisdn);
        List<ServiceOrderItem> serviceOrderItems = createServiceOrderItems(service, msisdn);

        serviceOrder.setServiceOrderItem(serviceOrderItems);
        return objectMapper.writeValueAsString(serviceOrder);
    }

    private String getRequestedStartDate(Order orderPayload) {
        return StringUtils.defaultIfEmpty(orderPayload.getRequestedStartDate(), Instant.now().toString());
    }


    private List<ServiceOrderItem> createServiceOrderItems(Service service, String msisdn) {
        List<ServiceOrderItem> serviceOrderItems = new ArrayList<>();
        getIccidAndImsiFromRequest(service);


        ServiceContext serviceContext = createServiceContext(msisdn);

        if (service.getSubscriptions() != null) {
            int itemId = 1;
            for (Subscription subscription : service.getSubscriptions()) {
                SubscriptionContext subscriptionContext = createSubscriptionContext(subscription);
                itemId = addServiceOrderItems(serviceOrderItems, subscription.getCfss(), "CFS", subscriptionContext, serviceContext, itemId);
                itemId = addServiceOrderItems(serviceOrderItems, subscription.getPrss(), "PRS", subscriptionContext, serviceContext, itemId);
                itemId = addServiceOrderItems(serviceOrderItems, subscription.getLrss(), "LRS", subscriptionContext, serviceContext, itemId);
            }
        }

        return serviceOrderItems;
    }

    private ServiceContext createServiceContext(String msisdn) {
        String pcrfCfss = getApplicationConfigValue("SOM_PCRF_NAME");
        ServiceContext serviceContext = new ServiceContext();
        serviceContext.setMsisdn(msisdn);
        serviceContext.setIccid(executionContext.getAttributes().get("newICCID"));
        serviceContext.setImsi(executionContext.getAttributes().get("newIMSI"));
        serviceContext.setPcrfCfss(pcrfCfss);
        if (OrderTypes.MNP_PORT_IN.equals(orderType)) {
            serviceContext.setLnpDbTriggerRequired(isLnpDbTriggerRequired());
            log.info("RECIPIENT_OPERATOR_NAME ::" +executionContext.getAttributes().get(MNPConstants.RECIPIENT_OPERATOR_NAME));
            serviceContext.setMno(findMNOByMvno((executionContext.getAttributes().get(MNPConstants.RECIPIENT_OPERATOR_NAME))));
        }

        serviceContext.setSubscriptionIdMap(initializeSubscriptionIdMap());
        return serviceContext;
    }

    private Map<String, String> initializeSubscriptionIdMap() {
        Map<String, String> subscriptionIdMap = null;
        if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData().get("subscriptionIdMap"))) {
            subscriptionIdMap = (HashMap<String, String>) executionContext.getWorkflowData()
                    .get("subscriptionIdMap");
        }
        return subscriptionIdMap;
    }

    private SubscriptionContext createSubscriptionContext(Subscription subscription) {
        SubscriptionContext subscriptionContext = new SubscriptionContext();
        subscriptionContext.setPlanId(subscription.getPlanId());
        subscriptionContext.setUpcPlanId(subscription.getUpcPlanId());
        subscriptionContext.setExpiryDate(subscription.getExpiryDate());
        return subscriptionContext;
    }

    private void getIccidAndImsiFromRequest(Service service) {
    	
        if (service.getSimDeliveryCallReqd() == null || !"false".equalsIgnoreCase(service.getSimDeliveryCallReqd())) {
        	if(isSimTypeEsim(service))
            return;
        }
        List<Characteristic> characteristics = service.getCharacteristics();
        if (characteristics == null || characteristics.isEmpty()) {
            return;
        }

        for (Characteristic characteristic : characteristics) {
            String name = characteristic.getName();
            if (name == null) {
                continue;
            }
            switch (name) {
                case "ICCID":
                    executionContext.getAttributes().put("newICCID", characteristic.getValue());
                    break;
                
            }
        }
        getImsiValueFromArmResponseAttribute();
    }
    private boolean isSimTypeEsim(Service service) {
    	if(ObjectUtils.isNotEmpty(service.getCharacteristics())) {
    		for(Characteristic chara :service.getCharacteristics()) {
    			if(StringUtils.isNotEmpty(chara.getName())&& (GenericConstants.CHARACTERISC_NAME_FOR_SIM_TYPE.equalsIgnoreCase(chara.getName())) && GenericConstants.CHARACTERISC_VALUE_FOR_ESIM.equalsIgnoreCase((chara.getValue())))
    				return false;
    			
    		}
    	}
		return true;
	}

	private void getImsiValueFromArmResponseAttribute() {
		Map<String, Object>  armResponse = null;
		Map<String, Object> workflowData  = (Map<String, Object>) executionContext.getWorkflowData();
 
		if (ObjectUtils.isNotEmpty(workflowData)&& (ObjectUtils.isNotEmpty(workflowData.get("currentExecution")))) {
			Map<String, Object> currentExecution = (Map<String, Object>) executionContext.getWorkflowData().get("currentExecution");
				if(currentExecution.containsKey("Arm_FetchAssetDetailsResponseAttributes")) {
	            armResponse = objectMapper.convertValue(currentExecution.get("Arm_FetchAssetDetailsResponseAttributes"), LinkedHashMap.class);
				}  
		}
	    if (ObjectUtils.isEmpty(armResponse)) {
	        if (ObjectUtils.isNotEmpty(workflowData) && workflowData.containsKey("Arm_FetchAssetDetailsResponseAttributes")) {
	                armResponse = objectMapper.convertValue(workflowData.get("Arm_FetchAssetDetailsResponseAttributes"), LinkedHashMap.class);
	            } 
	        }
	    
		if ((armResponse != null && !armResponse.isEmpty()) && armResponse.containsKey("imsi") && StringUtils.isNotEmpty(armResponse.get("imsi").toString())) {
			String imsi = armResponse.get("imsi").toString();
			executionContext.getAttributes().put("newIMSI", imsi);
		}
		
	}
	
	
	
	private int addServiceOrderItems(List<ServiceOrderItem> serviceOrderItems, List<CFSRef> cfsList, String type,
                                      SubscriptionContext subscriptionContext, ServiceContext serviceContext, int itemId) {
        if (cfsList != null) {
            for (CFSRef cfs : cfsList) {
                if (!Boolean.parseBoolean(cfs.getSkipSom())) {
                    ServiceOrderItem item = createServiceOrderItem(cfs, type, subscriptionContext, serviceContext);
                    item.setId(String.valueOf(itemId++));
                    serviceOrderItems.add(item);
                }
            }
        }
        return itemId;
    }

    private ServiceOrderItem createServiceOrderItem(CFSRef cfs, String type, SubscriptionContext subscriptionContext, ServiceContext serviceContext) {
        ServiceOrderItem serviceOrderItem = new ServiceOrderItem();
        serviceOrderItem.setAction("add");
        serviceOrderItem.setType("ServiceOrderItem");
        serviceOrderItem.setService(createServiceItem(cfs, type, subscriptionContext, serviceContext));
        return serviceOrderItem;
    }

    private SOMService createServiceItem(CFSRef cfs, String type, SubscriptionContext subscriptionContext, ServiceContext serviceContext) {
        SOMService serviceItem = new SOMService();
        serviceItem.setState("active");
        serviceItem.setType(type);

        ProductSpecification specification = new ProductSpecification();
        specification.setName(cfs.getName());
        specification.setId(cfs.getId());
        serviceItem.setServiceSpecification(specification);

        List<Characteristic> characteristics = createServiceCharacteristics(cfs, subscriptionContext, serviceContext);
        serviceItem.setServiceCharacteristic(characteristics);

        return serviceItem;
    }

    private List<Characteristic> createServiceCharacteristics(CFSRef cfs, SubscriptionContext subscriptionContext, ServiceContext serviceContext) {
        List<Characteristic> characteristicList = new ArrayList<>(cfs.getCharacteristics().size());
        boolean isPcrfCfss = serviceContext.getPcrfCfss() != null && Arrays.asList(serviceContext.getPcrfCfss().split(",")).contains(cfs.getName());
        for (CFSCharacteristicRef cfsCharacteristic : cfs.getCharacteristics()) {
            Characteristic characteristic = new Characteristic();
            characteristic.setName(cfsCharacteristic.getName());
            characteristic.setValueType(cfsCharacteristic.getDataType());
            if ("SUBSCRIPTION_ID".equals(characteristic.getName()) && serviceContext.getSubscriptionIdMap() != null) {
                characteristic.setValue(serviceContext.getSubscriptionIdMap().get(subscriptionContext.getPlanId()));
            } else if (isLnpDbCharacteristic(characteristic.getName())) {
                characteristic.setValue(setLnpDbCharacteristic(characteristic.getName(), serviceContext));
            } else {
                characteristic.setValue(getCharacteristicValue(cfsCharacteristic, subscriptionContext, serviceContext, isPcrfCfss));
            }
            characteristicList.add(characteristic);
        }

        return characteristicList;
    }

    private String setLnpDbCharacteristic(String charName, ServiceContext serviceContext) {
        if (!serviceContext.isLnpDbTriggerRequired()) {
            return "";
        }
        switch (charName) {
            case "mobileOperator":
                return executionContext.getAttributes().get(MNPConstants.RECIPIENT_OPERATOR_NAME);
            case "ownerTelco":
                return executionContext.getAttributes().get(MNPConstants.OWNER_OPERATOR_NAME);
            case "sourceTelco":
                return executionContext.getAttributes().get(MNPConstants.DONOR_OPERATOR_NAME);
            case "destTelco":
                return serviceContext.getMno();
            default:
                return "";
        }
    }

    private boolean isLnpDbCharacteristic(String charName) {
        return StringUtils.equalsAnyIgnoreCase(charName, "mobileOperator", "ownerTelco", "sourceTelco", "destTelco");
    }

    private String getCharacteristicValue(CFSCharacteristicRef cfsCharacteristic, SubscriptionContext subscriptionContext, ServiceContext serviceContext, boolean isPcrfAddon) {
        switch (cfsCharacteristic.getName()) {
            case "ICCID":
                return serviceContext.getIccid();
            case "IMSI":
                return serviceContext.getImsi();
            case "MSISDN":
                return serviceContext.getMsisdn();
            case "POLICY_NAME":
                return subscriptionContext.getUpcPlanId();
            case "START_DATE":
                return getDate("START_DATE", isPcrfAddon, subscriptionContext.getExpiryDate());
            case "END_DATE":
                return getDate("END_DATE", isPcrfAddon, subscriptionContext.getExpiryDate());
            case "portIdentifier":
                if (OrderTypes.MNP_PORT_IN.equals(orderType) || OrderTypes.INTERIM_NUMBER_PORT_IN.equals(orderType))
                    return "PORT_IN";
                else
                    return "";
            default:
                return cfsCharacteristic.getValue();
        }
    }

    private String getDate(String dateType, boolean isPcrf, String expiryDate) {
        String defaultDateTimeFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
        String pcrfDateTimeFormat = "dd-MM-yyyy HH:mm:ss";

        if ("START_DATE".equals(dateType)) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern(isPcrf ? pcrfDateTimeFormat : defaultDateTimeFormat));
        } else if ("END_DATE".equals(dateType)) {
            if (isPcrf) {
                return LocalDateTime.parse(expiryDate, DateTimeFormatter.ofPattern(defaultDateTimeFormat))
                        .format(DateTimeFormatter.ofPattern(pcrfDateTimeFormat));
            } else {
                return expiryDate;
            }
        }
        return "";
    }

    private String getApplicationConfigValue(String configName) {
        CacheTableDataDTO appConfig = cache.getCacheDetailsFromDBMap(
                NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG, configName);
        return appConfig != null ? appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()) : null;
    }

    private boolean isLnpDbTriggerRequired() {
        var portInType = execution.getVariable(AppConstants.ProcessVariables.PORT_IN_TYPE.value());
        if (MNPConstants.PORT_IN_TYPE_MVNO_MVNO.equals(portInType)) {
            return false;
        }
        boolean configValue = false;
        CacheTableDataDTO mvnoConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.LMP_DB_UPDATE_REQD_FOR_PORTIN_FROM_SINGTEL.toString());
        if (mvnoConfig != null)
            configValue = Boolean.parseBoolean(mvnoConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString()));
        if (MNPConstants.PORT_IN_TYPE_FROM_SINGTEL.equals(portInType))
            return configValue;
        return true;
    }

    private String findMNOByMvno(String mvno) {
        CacheTableDataDTO operatorConfig = cache.getCacheDetailsFromDBMap(
                CacheConstants.CacheKeys.COM_MVNO_MNO_MAPPING_CONFIG.toString(), mvno
        );
        if (operatorConfig == null) {
            log.warn("No MNO mapping found for MVNO: {}", mvno);
            return null;
        }
        String mno = operatorConfig.getNgTableData()
                .get(CacheConstants.CacheFields.MNO.toString());
        log.info("MNO name '{}' found from configuration table '{}'",
                mno, CacheConstants.CacheKeys.COM_MVNO_MNO_MAPPING_CONFIG);
        return mno;
    }

}
