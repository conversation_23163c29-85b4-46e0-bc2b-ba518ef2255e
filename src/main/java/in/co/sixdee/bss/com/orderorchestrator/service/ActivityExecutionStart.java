package in.co.sixdee.bss.com.orderorchestrator.service;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.WorkFlowUtil;
import in.co.sixdee.bss.com.orderorchestrator.config.notification.NotificationUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.util.ApplicationProcessContext;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Log4j2
public class ActivityExecutionStart {


	protected final ProcessDataAccessor processDataAccessor;

	protected final GetDataFromCache cache;

	protected final OrderStatusManager orderStatusManager;

	protected final OrderStateService orderUpdateService;

	protected final NotificationUtils notificationUtils;

	protected final WorkFlowUtil workFlowUtil;

	protected final ObjectMapper objectMapper;

	public void startActivity(DelegateExecution execution, String activityType) {
		var orderFlowContext = processDataAccessor.getOrderFlowContext(execution);
		var activityId = execution.getCurrentActivityId();
		if (GenericConstants.ACTIVITY_TYPE_SERVICE_TASK.equals(activityType)) {
			setMdcContext(orderFlowContext, execution);
		} else if (GenericConstants.ACTIVITY_TYPE_SUB_PROCESS.equals(activityType)) {
			setSubProcessExecutionData(orderFlowContext, execution);
		}
	}

	private void setMdcContext(OrderFlowContext orderFlowContext, DelegateExecution delegateExecution) {
		ServiceTask task = delegateExecution.getBpmnModelInstance().getModelElementById(delegateExecution.getCurrentActivityId());
		if (task.isCamundaAsyncBefore() || task.isCamundaAsyncAfter()) {
			var processContext = new ApplicationProcessContext(null,
					orderFlowContext.getTraceId(), orderFlowContext.getRequestId(), orderFlowContext.getChannel(),
					orderFlowContext.getUsername(), orderFlowContext.getEntityId());
			processContext.setMdc();
		}
	}

	private void setSubProcessExecutionData(OrderFlowContext orderFlowContext, DelegateExecution delegateExecution) {
		if (delegateExecution.getVariable("loopCounter") != null) {
			Map<String, Object> currentExecutionMap = null;
			// workitem is part of multi instance loop
			delegateExecution.setVariableLocal(
					WorkFlowConstants.WorkFlowProcessVariables.IS_PART_OF_MULTI_INSTANCE.toString(), "true");
			if (orderFlowContext.getWorkflowData() != null) {
				if (ObjectUtils.isNotEmpty(orderFlowContext.getWorkflowData().get("currentExecution"))) {
					currentExecutionMap = (Map<String, Object>) orderFlowContext.getWorkflowData()
							.get("currentExecution");
				} else {
					currentExecutionMap = new LinkedHashMap<>();
				}
				orderFlowContext.getWorkflowData().put("currentExecution", currentExecutionMap);
				if (StringUtils.containsAny(orderFlowContext.getOrder().getOrderType(), OrderTypes.CONNECTION_MIGRATION, OrderTypes.CHANGE_SUBSCRIPTION, OrderTypes.TERMINATE_SERVICE)
						&& "SMOfferActivationProcess".equals(delegateExecution.getCurrentActivityId())) {
					String loopElement = (String) delegateExecution.getVariable("executionData");
					var subscription = findSubscription(orderFlowContext, loopElement);
					if (subscription != null)
						currentExecutionMap.put("executionData", subscription);
				}
				else if (delegateExecution.getVariableTyped("executionData") != null) {
					JsonNode executionData = null;
					currentExecutionMap.put("executionData", JsonUtils
							.jsonToObject(delegateExecution.getVariableTyped("executionData").getValue().toString()));
					try {
						executionData = objectMapper.readTree(delegateExecution.getVariable("executionData").toString());
						if (delegateExecution.getVariables().containsKey("subOrderExecStart") && "true".equals((String) delegateExecution.getVariable("subOrderExecStart"))) {
							var subOrderId = executionData.get("subOrderId").asText();
							orderFlowContext.getAttributes().put(GenericConstants.SUB_ORDER_ID, subOrderId);
							orderStatusManager.updateSuborderExecutionStart(subOrderId);
							delegateExecution.setVariable("subOrderExecStart", "false");
						}
					} catch (JsonProcessingException e) {
						log.warn("Unable to extract executionData. cause :: {}", e.getMessage());
					}
				} if (Objects.nonNull(delegateExecution.getVariableTyped("execution"))) {
					currentExecutionMap.put("execution", JsonUtils
							.jsonToObject(delegateExecution.getVariableTyped("execution").getValue().toString()));
					orderFlowContext.getWorkflowData().put("currentExecution", currentExecutionMap);
				}
				processDataAccessor.setOrderFlowContext(delegateExecution, orderFlowContext);
			}
		}
	}
	
	private Subscription findSubscription(OrderFlowContext orderFlowContext, String loopElement) {
		var subscriptions = orderFlowContext.getOrder().getServiceManagement().getSubscriptions();
		if (subscriptions == null)
			subscriptions = orderFlowContext.getOrder().getServiceManagement().getNewCart().getSubscriptions();
		if (subscriptions != null)
			return subscriptions.stream().filter(sub -> sub.getPlanId().equals(loopElement)).findFirst().orElse(null);
		return null;

	}

}
