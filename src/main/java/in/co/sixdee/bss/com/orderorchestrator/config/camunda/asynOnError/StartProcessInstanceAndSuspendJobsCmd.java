package in.co.sixdee.bss.com.orderorchestrator.config.camunda.asynOnError;

import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.impl.ProcessInstantiationBuilderImpl;
import org.camunda.bpm.engine.impl.cmd.StartProcessInstanceCmd;
import org.camunda.bpm.engine.impl.interceptor.CommandContext;
import org.camunda.bpm.engine.impl.persistence.entity.JobEntity;
import org.camunda.bpm.engine.impl.persistence.entity.ProcessInstanceWithVariablesImpl;
import org.camunda.bpm.engine.impl.persistence.entity.SuspensionState;
import org.camunda.bpm.engine.runtime.ProcessInstanceWithVariables;

import java.util.List;

@Log4j2
public class StartProcessInstanceAndSuspendJobsCmd extends StartProcessInstanceCmd {

	private static final long serialVersionUID = 1L;

	public StartProcessInstanceAndSuspendJobsCmd(ProcessInstantiationBuilderImpl instantiationBuilder) {
		super((ProcessInstantiationBuilderImpl) instantiationBuilder);
	}

	@Override
	public ProcessInstanceWithVariables execute(CommandContext commandContext) {
		log.info("inside StartProcessInstanceAndSuspendJobsCmd.execute");
		ProcessInstanceWithVariables processInstanceWithVariables = super.execute(commandContext);
		// prevent the job from being executed by the JobExecutor
		List<JobEntity> jobs = ((ProcessInstanceWithVariablesImpl) processInstanceWithVariables).getExecutionEntity().getJobs();
		for (JobEntity job : jobs) {
			commandContext.getJobManager().updateJobSuspensionStateById(job.getId(), SuspensionState.SUSPENDED);
		}
		return processInstanceWithVariables;
	}

}