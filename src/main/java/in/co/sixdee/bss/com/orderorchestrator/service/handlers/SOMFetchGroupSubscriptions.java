package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;

@Log4j2
@Component(value = "somFetchGroupSubscriptions")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SOMFetchGroupSubscriptions extends AbstractDelegate {
	
    @Override
    protected void execute() throws Exception {
        String request = getRequestFromSpec();
        if (executionContext.isError())
            return;

        var callThirdPartyDTO = callThirdParty(request);
        if (callThirdPartyDTO == null) {
            executionContext.setError(true);
            return;
        }

        var response = callThirdPartyDTO.getResponse();
        validateResponse(callThirdPartyDTO);
         if (executionContext.isError())
            return;
        if(StringUtils.isNotEmpty(response)) {
        List<SOMService> groupServiceOrderItems = filterGroupSubscriptionCfss(response);
        if (groupServiceOrderItems != null && !groupServiceOrderItems.isEmpty()) {
			execution.setVariable("somDeleteGroupSubscriptions", true);
			executionContext.getWorkflowData().put("SOMFetchServiceRegistryResponseAttributes", groupServiceOrderItems);          
        } else {
        	execution.setVariable("somDeleteGroupSubscriptions", false);    
        	}
        workflowDataUpdated = true;
         }
    }

	private List<SOMService> filterGroupSubscriptionCfss(String response) throws JsonMappingException, JsonProcessingException {
        List<Subscription> groupSubscriptionsFromBilling = getSubscriptionsFromBsResponse();
        List<SOMService> somServiceOrderItems = getSubscriptionsFromSomResponse(response);
        List<SOMService> somGroupServiceOrderItems = null;
        if (groupSubscriptionsFromBilling != null && somServiceOrderItems != null) {
            somGroupServiceOrderItems = new ArrayList<>();
            for (Subscription subscription : groupSubscriptionsFromBilling) {
                for (SOMService serviceOrderItem : somServiceOrderItems) {
                    String somSubscriptionId = getSomSubscriptionId(serviceOrderItem);
                    if (somSubscriptionId != null && somSubscriptionId.equals(subscription.getSubscriptionId())) {
                        somGroupServiceOrderItems.add(serviceOrderItem);
                    }
                }
            }
        } else {
            log.info("Either Billing subcriptions or SOM service registry entites are null. unable to create the delete list for SOM");
        }
        return somGroupServiceOrderItems;
    }

    private String getSomSubscriptionId(SOMService serviceOrderItem) {
        String subscriptionId = null;
        if (serviceOrderItem != null && serviceOrderItem.getServiceCharacteristic() != null) {
            for (Characteristic characteristic : serviceOrderItem.getServiceCharacteristic()) {
                if ("SUBSCRIPTION_ID".equals(characteristic.getName())) {
                    subscriptionId = characteristic.getValue();
                    break;

                }
            }
        }
        return subscriptionId;
    }


    private List<SOMServiceOrderDTO.SOMService> getSubscriptionsFromSomResponse(String response) throws JsonMappingException, JsonProcessingException {
        return objectMapper.readValue(response,
				new TypeReference<List<SOMServiceOrderDTO.SOMService>>() {
				});
    }


    private List<Subscription> getSubscriptionsFromBsResponse() {
        List<Subscription> bsFetchSubscription = new ArrayList<>();
        if (executionContext.getWorkflowData().containsKey("BS_ViewSubscriptionResponseAttributes")) {
            bsFetchSubscription = objectMapper.convertValue(
                    executionContext.getWorkflowData().get("BS_ViewSubscriptionResponseAttributes"),
                    new TypeReference<>() {
                    });
        }
        return bsFetchSubscription;
    }
}

