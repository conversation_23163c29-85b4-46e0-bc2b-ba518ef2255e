/**
 * 
 */
/**
 * <AUTHOR>
 *
 */
@jakarta.xml.bind.annotation.XmlSchema(namespace = "http://tempuri.org/", xmlns = {
		@jakarta.xml.bind.annotation.XmlNs(prefix = "tem", namespaceURI = "http://tempuri.org/"),
		@jakarta.xml.bind.annotation.XmlNs(prefix = "sel", namespaceURI = "http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition") }, elementFormDefault = jakarta.xml.bind.annotation.XmlNsForm.QUALIFIED)
package in.co.sixdee.bss.com.orderorchestrator.model.mnp.disconnectServiceInternal;

