package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;

import in.co.sixdee.bss.common.util.CommonUtils;
import in.co.sixdee.bss.om.model.dto.order.ServiceDetails;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "fetchAccountDetails")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class BSFetchAccountDetails extends AbstractDelegate {


	@Override
	protected void execute() throws Exception {
		String request = null;
		if (CommonUtils.INSTANCE.validateField(reqSpecKey)) {
			try {
				request = joltUtils.convert(reqSpecKey, executionContext.getOrder().getOrderType(),
						objectMapper.convertValue(executionContext, Map.class), executionContext.getAttributes());
			} catch (Exception e) {
				log.error("Exception occurred. unable to form the tp request {}", e.getMessage());
				executionContext.getErrorDetail().setCode("COM-001");
				executionContext.getErrorDetail().setMessage("Internal Error: error in forming tp request");
				executionContext.getErrorDetail().setSystem("COM");
				executionContext.setError(true);
				return;
			}
		}
		var callThirdPartyDTO = callThirdParty(request);
		if (callThirdPartyDTO == null) {
			executionContext.setError(true);
			return;
		}
		var response = callThirdPartyDTO.getResponse();
		validateResponse(callThirdPartyDTO);
		if (executionContext.isError()) {
			log.error("{} Response validation failed", activityId);
			return;
		}
		checkNccCreditCounterUpdateRequired(response);

	}

	private void checkNccCreditCounterUpdateRequired(String response) throws JsonMappingException, JsonProcessingException {
		String outstandingBalance = null;
		JsonNode billingResp = null;
		boolean nccCreditAdjustment = false;

		try {
			billingResp = objectMapper.readTree(response);
			if (billingResp != null) {
				if (billingResp.get("data").get(0).get("accountBalanceDetails") != null && billingResp.get("data").get(0).get("accountBalanceDetails")
						.get("outstandingBalance") != null) {
					outstandingBalance = billingResp.get("data").get(0).get("accountBalanceDetails")
							.get("outstandingBalance").toString();
				}
			} else {
				log.info("Unable to get the outstanding balance from billing");
				execution.setVariable("nccCreditAdjustment", nccCreditAdjustment);
				return;
			}
		} catch (Exception e) {
			log.error("Exception while trying to get the outstanding balance {}", e.getMessage());
			execution.setVariable("nccCreditAdjustment", nccCreditAdjustment);
			return;
		}
		var excessAmount = getExcessPayment(outstandingBalance, executionContext.getOrder().getPayment().getTotalAmount());
		if (excessAmount > 0) {
			var serviceDetails = objectMapper.convertValue(billingResp.get("data").get(0).get("serviceDetails"),
					new TypeReference<List<ServiceDetails>>() {
					});

			if (serviceDetails != null) {
				List<ServiceDetails> activeService = serviceDetails.stream()
						.filter(service -> "1".equals(service.getStatus()) && "1".equals(service.getNetworkServiceId()))
						.collect(Collectors.toList());

				if (activeService != null && activeService.size() == 1) {
					executionContext.getAttributes().put("ocsServiceSeqId", activeService.get(0).getOcsServiceSeqId());
					executionContext.getAttributes().put("adjustmentAmount", String.valueOf(excessAmount));
					executionContext.getAttributes().put("serviceId", activeService.get(0).getServiceId());
					nccCreditAdjustment = true;
					workflowDataUpdated = true;
				} else {
					log.info("there is no active service or more than one active service present");
				}
			}
		}
		execution.setVariable("nccCreditAdjustment", nccCreditAdjustment);
	}


	private int getExcessPayment(String outstandingBalance, String totalAmount) {
		var osBalance = new BigDecimal(outstandingBalance);
		var totAmount = new BigDecimal(totalAmount);
		if (totAmount.compareTo(osBalance) > 0)
			return totAmount.subtract(osBalance).intValue();
		return 0;
	}
}
