package in.co.sixdee.bss.com.orderorchestrator.model.mnp.disconnectServiceInternal;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
public class SDInfo {

	@XmlElement(name = "ReferenceId" , namespace="http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private String referenceId;
	@XmlElement(name = "SubscriberNumber", namespace="http://schemas.datacontract.org/2004/07/SelcommWebServices.SelcommOSS.Singtel.MNPBSS2.Definition")
	private String subscriberNumber;

	public String getReferenceId() {
		return referenceId;
	}

	public void setReferenceId(String referenceId) {
		this.referenceId = referenceId;
	}

	public String getSubscriberNumber() {
		return subscriberNumber;
	}

	public void setSubscriberNumber(String subscriberNumber) {
		this.subscriberNumber = subscriberNumber;
	}

}
