package in.co.sixdee.bss.com.orderorchestrator.config;

import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.spi.json.JacksonJsonProvider;
import com.jayway.jsonpath.spi.json.JsonProvider;
import com.jayway.jsonpath.spi.mapper.JacksonMappingProvider;
import com.jayway.jsonpath.spi.mapper.MappingProvider;
import jakarta.annotation.PostConstruct;
import org.springframework.context.annotation.Configuration;

import java.util.HashSet;
import java.util.Set;

@Configuration
public class JsonPathConfig {

	@PostConstruct
	public void init() {

		com.jayway.jsonpath.Configuration.setDefaults(new com.jayway.jsonpath.Configuration.Defaults() {

			private final JsonProvider		jsonProvider	= new JacksonJsonProvider();
			private final MappingProvider	mappingProvider	= new JacksonMappingProvider();

			/*	private Set<Option>				options			= new HashSet<>() {
															{
																	add(Option.SUPPRESS_EXCEPTIONS);
																}
															};*/
			private Set<Option>				options			= Set.of(Option.SUPPRESS_EXCEPTIONS);

			@Override
			public JsonProvider jsonProvider() {
				return jsonProvider;
			}

			@Override
			public Set<Option> options() {
				return options;
			}

			@Override
			public MappingProvider mappingProvider() {
				return mappingProvider;
			}
		});
	}

}
