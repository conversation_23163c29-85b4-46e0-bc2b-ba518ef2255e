package in.co.sixdee.bss.com.orderorchestrator.service;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.notification.NotificationUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.WaitingProcessInfoEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderStageRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.SubOrderRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.WaitingProcessInfoRepository;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.util.NGTableColumnConstants;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.spin.plugin.variable.SpinValues;
import org.camunda.spin.plugin.variable.value.JsonValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.*;

@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Service
@Log4j2
@RequiredArgsConstructor
public class CancelOrderService {


    final OrderRepository orderMasterRepo;

    final SubOrderRepository subOrderRepo;

    final OrderStageRepository orderStageRepository;


    final RuntimeService runTimeService;

    final WaitingProcessInfoRepository waitingRepo;

    final NotificationUtils notificationUtils;

    final ObjectMapper objectMapper;

    @Autowired
	private GetDataFromCache getDataFromCache;

    private Map<String,Object> findRollbackVariables(String orderId,String orderType) {
        Map<String,Object> rollbackVariables = new HashMap<>();
        List<OrderStageEntity> stageInfos = orderStageRepository.findCompletedStagesByOrderId(Long.valueOf(orderId));
        rollbackVariables.put(WorkFlowConstants.WorkFlowProcessVariables.STATUS.toString(),
                WorkFlowConstants.WorkFlowProcessVariables.STATUS_SUCCESS.toString());
        if(stageInfos!=null){
            for (OrderStageEntity stageInfo : stageInfos){
            	
            	var processVariable = stageInfo.getStageCode().toLowerCase();
            	rollbackVariables.put(processVariable,"Completed".equalsIgnoreCase(stageInfo.getState()));
            }
            
            List<CacheTableDataDTO> stageConfigs = getDataFromCache.getCacheDetailsFromDBMapAryList(NGTableConstants.CACHE_KEY_COM_ORDER_STAGE_CONFIG,
    				orderType);        	
          
            for (CacheTableDataDTO stageConfig : stageConfigs) 
            {
            	String stageId=stageConfig.getNgTableData().get(NGTableColumnConstants.COLUMN_STAGE_ID).toLowerCase();
            	
            	if( StringUtils.isNotEmpty(stageId) && !rollbackVariables.containsKey(stageId))
            	{
            		rollbackVariables.put(stageId,false);
            	}
    			
            }
            if((OrderTypes.INTERIM_NUMBER_PORT_IN.equalsIgnoreCase(orderType) ||OrderTypes.MNP_PORT_IN.equalsIgnoreCase(orderType)) && ObjectUtils.isNotEmpty(stageInfos)){
                log.info("Checking for cancel submit port in call required...");
               // rollbackVariables.put("cancelSubmitPortInCallReqd",checkMNPCallReqd(stageInfos));
                rollbackVariables.put("cancelSubmitPortInCallReqd",false);
            }
            
        }
        return rollbackVariables;
    }

    private boolean checkMNPCallReqd(List<OrderStageEntity> stageInfos) {
        boolean notificationStageFound = false;
        boolean mnpCallReqd = false;
        boolean submitPortInCompleted=false;
        List<String> mnpNotificationStages = Arrays.asList(MNPConstants.MNP_NOTIFICATION_STAGES.split(","));
        List<String> mnpSuccessNotificationStages = Arrays.asList(MNPConstants.MNP_SUCCESS_NOTIFICATION_STAGES.split(","));
        List<String> submitPortInStages=Arrays.asList(MNPConstants.SUBMIT_PORT_IN_STAGES.split(","));
        for (OrderStageEntity stageInfo : stageInfos) {
            String stageCode = stageInfo.getStageCode();
            String stageState = stageInfo.getState();

            if (submitPortInStages.contains(stageCode) && stageState.equalsIgnoreCase(MNPConstants.MNP_SUCCESS_STAGE_STATUS)) {
                submitPortInCompleted = true;
            }

            if (mnpNotificationStages.contains(stageCode)) {
                notificationStageFound = true;
                if (mnpSuccessNotificationStages.contains(stageCode) && stageState.equalsIgnoreCase(MNPConstants.MNP_SUCCESS_STAGE_STATUS)) {
                    mnpCallReqd = true;
                    return mnpCallReqd;
                }
            }

        }

        if (!notificationStageFound && submitPortInCompleted)
            mnpCallReqd = true;

        return false;
    }

    public OrderFlowContext processCancelOrderRequest(OrderFlowContext orderFlowContext) {

        String orderId = orderFlowContext.getOrder().getOrderId();

        WaitingProcessInfoEntity waitingProcessInfo = waitingRepo.findProcessInfoByOrderId(orderId, Arrays.asList("Callback","Timer"));
        if (ObjectUtils.isNotEmpty(waitingProcessInfo)) {
            waitingRepo.delete(waitingProcessInfo);
            if (ObjectUtils.isNotEmpty(waitingProcessInfo.getExecutionId())) {
                OrderFlowContext orderFlowContextInDB = initOrderFlowContext(waitingProcessInfo.getExecutionId());
                mergeOrderFlowContext(orderFlowContext,orderFlowContextInDB);
                orderFlowContext=orderFlowContextInDB;
                notificationUtils.sendNotification(orderFlowContext, "CANCEL_NOTIFICATION", "ORDER");

                if (ObjectUtils.isNotEmpty(waitingProcessInfo.getProcessInstanceId())) {
                    runTimeService.deleteProcessInstancesAsync(Arrays.asList(waitingProcessInfo.getProcessInstanceId().split(",")), null, "order cancellation", true, true);
                }
                if(StringUtils.isEmpty(orderFlowContext.getAttributes().get("cancelWithoutProcessId"))) {
                    initiateWorkflow(orderFlowContext);
                }
            }
        } else {
            log.info("order {} is not waiting for any events to complete",orderId);
            List<ProcessInstance> piList = runTimeService.createProcessInstanceQuery()
                    .processInstanceBusinessKey(orderId).list();
            if (ObjectUtils.isEmpty(piList))
            		{
            	 piList = runTimeService.createProcessInstanceQuery()
                        .processInstanceBusinessKeyLike(orderId+"_"+"%").list();
            		}
            if (ObjectUtils.isNotEmpty(piList)) {
                for(ProcessInstance pi:piList){
                    var incident = runTimeService.createIncidentQuery().incidentType("failedJob")
                            .processInstanceId(pi.getId()).singleResult();
                    if (ObjectUtils.isNotEmpty(incident)
                            && StringUtils.isNotEmpty(incident.getJobDefinitionId())) {
                        OrderFlowContext ofc = initOrderFlowContext(pi.getProcessInstanceId());
                        mergeOrderFlowContext(orderFlowContext,ofc);
                        orderFlowContext = ofc;
                        if (notificationInitiationReq(ofc))
                            notificationUtils.sendNotification(ofc, "CANCEL_NOTIFICATION", "ORDER");
                        if (ObjectUtils.isNotEmpty(ofc.getOrder())
                                && StringUtils.isNotEmpty(ofc.getOrder().getOrderType())) {
                           initiateWorkflow(ofc);
                        }
                        runTimeService.deleteProcessInstanceIfExists(incident.getProcessInstanceId(),
                                "order cancellation", true, false, true, true);
                        break;
                    }
                }
            } else {
                log.info("no active process instances found for the order {}",orderId);
            }
        }
        /*if(StringUtils.isEmpty(orderFlowContext.getAttributes().get("futureOrderCancellation"))) {
            initiateWorkflow(orderFlowContext);
        }*/

        return orderFlowContext;

    }


    public OrderFlowContext initOrderFlowContext(String executionId) {
        OrderFlowContext orderFlowContext;
        try {
            orderFlowContext = objectMapper.readValue(
                    runTimeService.getVariable(executionId,
                            WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString()).toString(),
                    OrderFlowContext.class);
        } catch (Exception e) {
            throw new CommonException("couldn't obtain order flow context");
        }
        return orderFlowContext;
    }

    private void mergeOrderFlowContext(OrderFlowContext orderFlowContextIn, OrderFlowContext orderFlowContextInDb) {
        var orderType=orderFlowContextIn.getOrder().getOrderType();
        orderFlowContextInDb.getOrder().setOrderType(orderType);
        if (orderFlowContextIn.getAttributes() != null) {
            orderFlowContextInDb.getAttributes().putAll(orderFlowContextIn.getAttributes());
        }
    }



    private boolean notificationInitiationReq(OrderFlowContext orderFlowContext) {
        boolean notificationreq = false;
        if (ObjectUtils.isNotEmpty(orderFlowContext.getAttributes().get("accountId")))
            notificationreq = true;
        else {
            if (ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults())
                    && ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults().get("serviceInfo"))) {
                var serviceInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("serviceInfo"),
                        JsonNode.class);
                if (ObjectUtils.isNotEmpty(serviceInfo) && serviceInfo.has("accountId")
                        && ObjectUtils.isNotEmpty(serviceInfo.get("accountId")))
                    notificationreq = true;
            }
        }
        return notificationreq;
    }

    public void initiateWorkflow(OrderFlowContext orderFlowContext){
        String orderId = orderFlowContext.getOrder().getOrderId();
        String orderType=orderFlowContext.getAttributes().get("actualOrderType");
        Map<String,Object> variables = findRollbackVariables(orderId,orderType);      
        if(!variables.isEmpty() && containsTrueValue(variables)) {
            log.info("initiating the rollback flow for the order {}", orderId);
            variables.put(WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString(),
                    createWorkflowData(orderFlowContext));
            runTimeService.startProcessInstanceByKey(orderFlowContext.getAttributes().get("rollbackProcessId"), orderId,
                    variables);
        } else {
            log.info("rollback is not required for the order {}", orderId);
        }
    }
    public static boolean containsTrueValue(Map<String, Object> varialblesMap) {
        for (Map.Entry<String, Object> entry : varialblesMap.entrySet()) {
            if (entry.getValue() instanceof Boolean && (Boolean) entry.getValue()) {
                return true;
            }
        }
        return false;
    }
	  public JsonValue createWorkflowData(OrderFlowContext orderFlowContext) {
        JsonValue workflowData = null;
        if (orderFlowContext.getWorkflowData() == null)
            orderFlowContext.setWorkflowData(new LinkedHashMap<>());
        try {
            workflowData = SpinValues.jsonValue(objectMapper.writeValueAsString(orderFlowContext)).create();
        } catch (JsonProcessingException e) {
            // handle exception, return the message back to queue or discard message after updating
            // order status
        }
        return workflowData;
    }

}
