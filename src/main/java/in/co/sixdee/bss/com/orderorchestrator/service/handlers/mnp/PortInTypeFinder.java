package in.co.sixdee.bss.com.orderorchestrator.service.handlers.mnp;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.WorkflowTaskFailedException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.MNPServiceUtil;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.*;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.om.model.dto.esb.ESBResponse;
import in.co.sixdee.bss.om.model.dto.esb.ServiceCharacteristic;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

@Log4j2
@Component(value = "portInTypeFinder")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class PortInTypeFinder extends AbstractDelegate {


    private final MNPAttributeService mnpAttributeService;

    private final MNPServiceUtil mnpServiceUtil;


    protected void execute() throws Exception {
        String serviceId = findServiceId();
        boolean mvnoMvnoPortIn = isMvnoMvnoPortIn(serviceId);
        if (mvnoMvnoPortIn) {
            String portInType = MNPConstants.PORT_IN_TYPE_MVNO_MVNO;
            log.info("Identified Port-In Type as {}", portInType);
            String entityId = executionContext.getAttributes().get(MNPConstants.DONOR_ENTITY_ID);
            if (!mnpServiceUtil.findDonorOperatorByMvnoId(executionContext, entityId)) {
            	setError("COM-005","Unable to find operator for the entityId: "+entityId);
                throw new WorkflowTaskFailedException(activityId, "COM-005", "Unable to find operator for the entityId: " + entityId);
            }
            execution.setVariable(AppConstants.ProcessVariables.PORT_IN_TYPE.value(), portInType);
            execution.setVariable(AppConstants.ProcessVariables.QUERY_MNP_REQUIRED.value(), false);
            mnpAttributeService.createAndSaveMNPAttribute(AppConstants.ProcessVariables.PORT_IN_TYPE.value(), portInType, executionContext);
        } else {
            execution.setVariable(AppConstants.ProcessVariables.QUERY_MNP_REQUIRED.value(), true);
        }
        String recipientEntityId = executionContext.getEntityId();
        if (!mnpServiceUtil.findRecipientOperatorByMvnoId(executionContext, recipientEntityId)) {
        	setError("COM-005","Unable to find operator for the entityId: "+recipientEntityId);
            throw new WorkflowTaskFailedException(activityId, "COM-005", "Unable to find operator for the entityId: " + recipientEntityId);
        }
            mnpServiceUtil.findOwnerOperator(executionContext, serviceId);
        if (mvnoMvnoPortIn) {
            log.info("setting attributes to the attribute table");
            mnpAttributeService.saveOperatorCodesInAttributes(executionContext);
        }
        workflowDataUpdated = true;
    }

    private String findServiceId() {
        String serviceId = null;
        if (OrderTypes.MNP_PORT_IN.equals(orderType))
            serviceId = executionContext.getOrder().getProfile().getAccount().getServiceGroups().get(0).getServices().get(0).getServiceId();
        else if (OrderTypes.INTERIM_NUMBER_PORT_IN.equals(orderType))
            serviceId = executionContext.getOrder().getServiceManagement().getNewMsisdn();
        return serviceId;

    }


    private boolean isMvnoMvnoPortIn(String serviceId) {
        executionContext.getAttributes().put("serviceId", serviceId);
        String ocsResponse = callOcs();
        return parseEsbResponse(ocsResponse);
    }

    private String callOcs() {
        String response = null;
        String request = getRequestFromSpec();
        CallThirdPartyDTO callThirdPartyDTO = callThirdParty(request);
        if (callThirdPartyDTO == null) {
            executionContext.setError(true);
        } else {
            response = callThirdPartyDTO.getResponse();
//        	response="{  \"serviceCharacteristic\" : [ {    \"valueType\" : \"string\",    \"@baseType\" : \"string\",    \"name\" : \"externalServiceId\",    \"@schemaLocation\" : \"string\",    \"value\" : \"6558441842\",    \"arrayIndex\" : \"-1\",    \"@type\" : \"string\"  } ]}";
            validateResponse(callThirdPartyDTO);
            if (executionContext.isError()) {
                throw new CommonException("Error while querying service details from OCS : " + executionContext.getErrorDetail().getMessage());
            }
        }
        return response;
    }


    private boolean parseEsbResponse(String response) {
        ESBResponse esbResponse = null;
        try {
            esbResponse = objectMapper.readValue(response, ESBResponse.class);
        } catch (JsonProcessingException e) {
            log.error("Exception while reading ESB response", e);
        }
        if (esbResponse == null) {
            return false;
        }

        if (esbResponse.getResponseHeader() != null) {
            String responseStatus = esbResponse.getResponseHeader().getResponseStatus();
            if ("1039".equals(responseStatus)) {
                log.info("Incoming serviceId doesn't exist in BSS. Not an MVNO-MVNO Port-In.");
                return false;
            }
        }

        CacheTableDataDTO validStatusConfig = cache.getCacheDetailsFromDBMap(
                CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                CacheConstants.CacheFields.VALID_MSISDN_STATUS_IN_OCS.name());

        if (validStatusConfig == null || validStatusConfig.getNgTableData() == null) {
            return false;
        }

        Set<String> validStatuses = new HashSet<>(Arrays.asList(
                validStatusConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()).split(",")));

        List<ServiceCharacteristic> serviceCharacteristics = esbResponse.getServiceCharacteristic();
        if (serviceCharacteristics == null) {
            return false;
        }

        ServiceCharacteristic parentEntityId = serviceCharacteristics.stream()
                .filter(sc -> "parentEntityId".equalsIgnoreCase(sc.getName()))
                .findFirst()
                .orElse(null);

        for (ServiceCharacteristic serviceCharacteristic : serviceCharacteristics) {
            if ("currentState".equalsIgnoreCase(serviceCharacteristic.getName()) && StringUtils.isNotEmpty(serviceCharacteristic.getValue())
                    && validStatuses.contains(serviceCharacteristic.getValue())) {

                log.info("Service id exists in OCS with state {}. Considering as MVNO-MVNO Port-In", serviceCharacteristic.getValue());
                if (parentEntityId != null) {
                	validateRequestAndOcsEntityId(parentEntityId);
                    executionContext.getAttributes().put(MNPConstants.DONOR_ENTITY_ID, parentEntityId.getValue());
                } else {
                	setError("COM-005","Unable to find entity id from OCS.");
                    throw new WorkflowTaskFailedException(activityId, "COM-005", "Unable to find entity id from OCS.");
                }
                return true;
            }
        }

        return false;
    }
    private void validateRequestAndOcsEntityId(ServiceCharacteristic parentEntityId) {
		if(executionContext.getEntityId().equalsIgnoreCase(parentEntityId.getValue())){
			setError("COM-005","Invalid port in request. Source and destination entity id are the same");
    		throw new WorkflowTaskFailedException(activityId, "COM-005", "Invalid port in request. Source and destination entity id are the same");
    	}
	}
    
    private void setError(String errorCode, String errorMessage) {
        executionContext.setError(true);
        executionContext.getErrorDetail().setSystem(callThirdPartyDTO.getThirdPartySystem());
        executionContext.getErrorDetail().setCode(errorCode);
        executionContext.getErrorDetail().setMessage(errorMessage);
    }
}
