package in.co.sixdee.bss.com.orderorchestrator.model.mnp.submitportin;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import lombok.Data;

import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class SDPStatusResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @XmlElement(name = "transactionStatusCode")
    private String transactionStatusCode;
    @XmlElement(name = "serviceVersion")
    private String serviceVersion;

}
