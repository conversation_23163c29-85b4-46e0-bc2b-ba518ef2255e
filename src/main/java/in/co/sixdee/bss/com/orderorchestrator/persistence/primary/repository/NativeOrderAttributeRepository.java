package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;


import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderEntity;
import in.co.sixdee.bss.com.orderorchestrator.config.ApplicationProperties;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderAttributeEntity;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

@Repository
@RequiredArgsConstructor
@Log4j2
@Transactional
public class NativeOrderAttributeRepository {


	protected final ApplicationProperties applicationProperties;

	@PersistenceContext
	protected EntityManager em;

	public void save(OrderAttributeEntity orderAttribute) {
		var session = em.unwrap(Session.class);
		final String sql = "INSERT INTO COM_ORDER_ATTRIBUTES (CREATED_BY, LAST_MODIFIED_BY, NAME, ORDER_ID, SUB_ORDER_ID, TYPE, VALUE, SEQ_ID,REFERENCE_ID) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
		session.doWork(connection -> {
			try (PreparedStatement ps = connection.prepareStatement(sql)) {
				ps.setString(1, orderAttribute.getCreatedBy());
				ps.setString(2, orderAttribute.getLastModifiedBy());
				ps.setString(3, orderAttribute.getKey());
				ps.setLong(4, orderAttribute.getOrderId());
				ps.setLong(5, orderAttribute.getSubOrderId());
				ps.setString(6, orderAttribute.getType());
				ps.setString(7, orderAttribute.getValue());
				ps.setLong(8, orderAttribute.getSeqId());
				ps.setString(9, orderAttribute.getReferenceId());
				ps.executeUpdate();
			} catch (SQLException e) {
				log.error("An exception occurred in NativeOrderAttributeRepository.save", e);
			}
		});
	}


	public void saveAll(List<OrderAttributeEntity> orderAttribute) {
		var session = em.unwrap(Session.class);
		final String sql = "INSERT INTO COM_ORDER_ATTRIBUTES (CREATED_BY, LAST_MODIFIED_BY, NAME, ORDER_ID, SUB_ORDER_ID, TYPE, VALUE, SEQ_ID,REFERENCE_ID) VALUES (?, ?, ?, ?, ?, ?, ?, ?,?)";
		session.doWork(connection -> {
			try (PreparedStatement ps = connection.prepareStatement(sql)) {
				int i = 1;
				for (OrderAttributeEntity attribute : orderAttribute) {
					ps.setString(1, attribute.getCreatedBy());
					ps.setString(2, attribute.getLastModifiedBy());
					ps.setString(3, attribute.getKey());
					ps.setLong(4, attribute.getOrderId());
					ps.setLong(5, attribute.getSubOrderId());
					ps.setString(6, attribute.getType());
					ps.setString(7, attribute.getValue());
					ps.setLong(8, attribute.getSeqId());
					ps.setString(9, attribute.getReferenceId());
					ps.addBatch();
					if (i % applicationProperties.getJdbcBatchSize() == 0)
						ps.executeBatch();
					i++;
				}
				ps.executeBatch();
			} catch (SQLException e) {
				log.error("An exception occurred in NativeOrderAttributeRepository.saveAll", e);
			}
		});
	}
	
	public void save(OrderEntity orderEntity) {
		Session session = em.unwrap(Session.class);
		final String sql = "insert into COM_ORDER_MASTER (CREATED_BY, LAST_MODIFIED_BY, BATCH_ID, BILLING_ACCOUNT_ID, CHANNEL, CUSTOMER_ID, CUSTOMER_NAME, DESCRIPTION, EXTERNAL_ID, SERVICE_ID, ORDER_STATE, STATE_REASON, ORDER_TYPE, ORDER_ID, PARENT_ORDER_ID, ORDER_CHARGES, CATEGORY, PAYMENT_CHANNEL, SA_FORM_ID,ENTITY_ID,USERNAME) values (?,?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
			session.doWork(connection -> {
			try (PreparedStatement ps = connection.prepareStatement(sql)) {
				ps.setString(1, orderEntity.getCreatedBy());
				ps.setString(2, orderEntity.getLastModifiedBy());
				ps.setLong(3, orderEntity.getBatchId() != null ? orderEntity.getBatchId() : 0);
				ps.setString(4, orderEntity.getBillingAccountId());
				ps.setString(5, orderEntity.getChannel());
				ps.setString(6, orderEntity.getCustomerId());
				ps.setString(7, orderEntity.getCustomerName());
				ps.setString(8, orderEntity.getDescription());
				ps.setString(9, orderEntity.getExternalId());
				ps.setString(10, orderEntity.getServiceId());
				ps.setString(11, orderEntity.getState());
				ps.setString(12, orderEntity.getStateReason());
				ps.setString(13, orderEntity.getType());
				ps.setLong(14, orderEntity.getOrderId());
				ps.setLong(15, orderEntity.getParentOrderId() != null ? orderEntity.getParentOrderId() : 0);
				ps.setString(16, orderEntity.getOrderCharges());
				ps.setString(17, orderEntity.getCategory());
				ps.setString(18, orderEntity.getPaymentChannel());
				ps.setString(19, orderEntity.getSaFormId());
				ps.setString(20, orderEntity.getEntityId());
				ps.setString(21, orderEntity.getUsername());
				ps.executeUpdate();
			} catch (SQLException e) {
				log.error("An exception occurred in NativeOrderRepository.save", e);
			}
		});
	}


}
