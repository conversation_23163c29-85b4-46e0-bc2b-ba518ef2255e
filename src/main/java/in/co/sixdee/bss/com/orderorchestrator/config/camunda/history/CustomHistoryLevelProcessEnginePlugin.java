package in.co.sixdee.bss.com.orderorchestrator.config.camunda.history;

import org.camunda.bpm.engine.impl.cfg.AbstractProcessEnginePlugin;
import org.camunda.bpm.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.camunda.bpm.engine.impl.history.HistoryLevel;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class CustomHistoryLevelProcessEnginePlugin extends AbstractProcessEnginePlugin {

	@Override
	public void preInit(ProcessEngineConfigurationImpl processEngineConfiguration) {
		List<HistoryLevel> customHistoryLevels = processEngineConfiguration.getCustomHistoryLevels();
		if (customHistoryLevels == null) {
			customHistoryLevels = new ArrayList<HistoryLevel>();
			processEngineConfiguration.setCustomHistoryLevels(customHistoryLevels);
		}
		customHistoryLevels.add(CustomVariableHistoryLevel.getInstance());
		customHistoryLevels.add(PerProcessHistoryLevel.getInstance());
	}

	@Override
	public void postInit(ProcessEngineConfigurationImpl processEngineConfiguration) {
		PerProcessHistoryLevel.getInstance().addHistoryLevels(processEngineConfiguration.getCustomHistoryLevels());
	}

}