package in.co.sixdee.bss.com.orderorchestrator.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.WorkFlowUtil;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.WorkflowTaskFailedException;
import in.co.sixdee.bss.com.orderorchestrator.config.notification.NotificationUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.*;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants.WorkFlowProcessVariables;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Log4j2
public class ActivityExecutionEnd {

    protected final ProcessDataAccessor processDataAccessor;

    protected final GetDataFromCache cache;

    protected final OrderStatusManager orderStatusManager;

    protected final OrderStateService orderUpdateService;

    protected final NotificationUtils notificationUtils;

    protected final WorkFlowUtil workFlowUtil;

    @Value("${flow.simulation:false}")
    private boolean flowSimulation;

    @Autowired
    private ObjectMapper objectMapper;

    protected Map<String, String> stageConfig;

    public void closeActivity(DelegateExecution execution, OrderFlowContext orderFlowContext, String activityType) {
        if (orderFlowContext == null)
            orderFlowContext = processDataAccessor.getOrderFlowContext(execution);
        var activityId = execution.getCurrentActivityId();
        if (GenericConstants.ACTIVITY_TYPE_SERVICE_TASK.equals(activityType)) {
            handleServiceTaskEnd(orderFlowContext, execution, activityId);
        } else {
            handleBpmnEnd(orderFlowContext, execution);
        }
    }

    private void handleBpmnEnd(OrderFlowContext orderFlowContext, DelegateExecution execution) {
        if (StringUtils.isNotEmpty(execution.getActivityInstanceId())
                && StringUtils.isNotEmpty(execution.getProcessInstanceId())
                && execution.getActivityInstanceId().equals(execution.getProcessInstanceId())) {
            var activityId = execution.getCurrentActivityId();
            var subOrderId = orderFlowContext.getAttributes().get(GenericConstants.SUB_ORDER_ID);
            var status = execution.getVariable(WorkFlowConstants.WorkFlowProcessVariables.STATUS.toString()).toString();
            if (!status.equalsIgnoreCase(WorkFlowConstants.WorkFlowProcessVariables.STATUS_SUCCESS.toString()))
                return;
            if (StringUtils.equalsAnyIgnoreCase(orderFlowContext.getOrder().getOrderType(), OrderTypes.ONBOARDING,
                    OrderTypes.ADD_SERVICE, OrderTypes.ADD_SERVICE_TO_NEW_ACCOUNT, OrderTypes.ADD_SUBSCRIPTION, OrderTypes.EXTEND_EXPIRY_DATE)) {
                if (StringUtils.equalsIgnoreCase(WorkFlowConstants.BpmnConstants.SUBPROCESS_END_EVENT.getStringValue(),
                        activityId))
                    orderStatusManager.updateSubOrderCompletion(orderFlowContext, subOrderId);
            } else if (StringUtils.equalsIgnoreCase(WorkFlowConstants.BpmnConstants.ORDER_EXEC_END.getStringValue(),
                    activityId)) {
                orderStatusManager.updateSubOrderCompletion(orderFlowContext, subOrderId);
            }

        }

    }

    private void handleServiceTaskEnd(OrderFlowContext orderFlowContext, DelegateExecution delegateExecution,
                                      String activityId) {
        String executionStatus = null;
        var stageConfigDto = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.STAGE_CONFIG_BY_ACTIVITY_ID_AND_ORDER_TYPE.toString(), orderFlowContext.getOrder().getOrderType() + "_" + activityId);
        if (stageConfigDto == null)
            throw new ConfigurationNotValidException("Unable to find the stage configurations for activity " + activityId + ". Please configure in COM_ORDER_STAGE_CONFIG table");
        stageConfig = stageConfigDto.getNgTableData();
        var updateKey = stageConfig.get(WorkFlowProcessVariables.UPDATE_KEY.toString());
        if (orderFlowContext.isError()) {
            delegateExecution.setVariable(WorkFlowConstants.WorkFlowProcessVariables.STATUS.toString(),
                    WorkFlowConstants.WorkFlowProcessVariables.STATUS_FAILURE.toString());
            executionStatus = WorkFlowConstants.WorkItemExecutionStatus.STATUS_FAILURE.getValue();
        } else {
            delegateExecution.setVariable(WorkFlowConstants.WorkFlowProcessVariables.STATUS.toString(),
                    WorkFlowConstants.WorkFlowProcessVariables.STATUS_SUCCESS.toString());
            if (StringUtils.isNotEmpty(updateKey)) {
                orderUpdateService.processOrderUpdates(updateKey, orderFlowContext);
            }

            if (isAsyncActivity(orderFlowContext.getOrder().getOrderType(), activityId))
                executionStatus = WorkFlowConstants.WorkItemExecutionStatus.STATUS_INPROGRESS.getValue();
            else
                executionStatus = WorkFlowConstants.WorkItemExecutionStatus.STATUS_COMPLETED.getValue();

            handleNotificationActions(orderFlowContext, activityId);
        }
        var errorCode = orderFlowContext.getErrorDetail().getCode();
        orderStatusManager.processStatusUpdates(orderFlowContext, activityId,
                GenericConstants.ACTIVITY_TYPE_SERVICE_TASK, true, executionStatus);
        if (orderFlowContext.isError()) {
            handleFailureActions(orderFlowContext, activityId, delegateExecution, errorCode);
        }
    }

    private void handleNotificationActions(OrderFlowContext orderFlowContext, String activityId) {
        var creditLimit = 0.00;
        BigDecimal limitDiff = new BigDecimal(0);
        BigDecimal creditLimitChannel = null;
        String messageType = null;

        if (StringUtils.equalsAnyIgnoreCase(orderFlowContext.getOrder().getOrderType(), OrderTypes.ADD_SUBSCRIPTION)) {
            if (ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults())
                    && ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults().get("serviceInfo"))
                    && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getOrderItem().get(0).getCreditLimit())) {
                var serviceInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("serviceInfo"),
                        JsonNode.class);
                if (ObjectUtils.isNotEmpty(serviceInfo) && serviceInfo.has("creditLimit")) {
                    var creditLimitSerInfo = new BigDecimal(serviceInfo.get("creditLimit").asText());
                    creditLimit = Double
                            .parseDouble(orderFlowContext.getOrder().getOrderItem().get(0).getCreditLimit());
                    creditLimitChannel = BigDecimal.valueOf(creditLimit).setScale(2, RoundingMode.HALF_EVEN);

                    if (creditLimitChannel.compareTo(creditLimitSerInfo) >= 0) {
                        limitDiff = creditLimitChannel.subtract(creditLimitSerInfo);

                    } else if (creditLimitChannel.compareTo(creditLimitSerInfo) < 0) {
                        limitDiff = creditLimitSerInfo.subtract(creditLimitChannel);

                    }
                    orderFlowContext.getAttributes().put("creditLimitDifference", limitDiff.toString());

                }
                if (GenericConstants.BS_UPDATE_CREDITLIMIT_HANDLER.equals(activityId)) {

                    notificationUtils.sendNotification(orderFlowContext, "ORDER_COMPLETION_INCR", "ORDER");
                }
            }

            if (GenericConstants.BS_BOOKDEPOSIT_HANDLER.equals(activityId)) {
                notificationUtils.sendNotification(orderFlowContext, "BOOKDEPOSIT_NOTIFICATION", "ORDER");
            }
        } else if (StringUtils.equalsAnyIgnoreCase(orderFlowContext.getOrder().getOrderType(),
                OrderTypes.CONNECTION_MIGRATION)) {

            if (ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults())
                    && ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults().get("serviceInfo"))
                    && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getCreditLimit())) {
                var serviceInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("serviceInfo"),
                        JsonNode.class);
                if (ObjectUtils.isNotEmpty(serviceInfo) && serviceInfo.has("creditLimit")) {
                    var creditLimitSerInfo = new BigDecimal(serviceInfo.get("creditLimit").asText());

                    creditLimit = Double
                            .parseDouble(orderFlowContext.getOrder().getServiceManagement().getCreditLimit());

                    creditLimitChannel = BigDecimal.valueOf(creditLimit).setScale(2, RoundingMode.HALF_EVEN);

                    if (creditLimitChannel.compareTo(creditLimitSerInfo) >= 0) {
                        limitDiff = creditLimitChannel.subtract(creditLimitSerInfo);
                    } else if (creditLimitChannel.compareTo(creditLimitSerInfo) < 0) {
                        limitDiff = creditLimitSerInfo.subtract(creditLimitChannel);

                    }
                    orderFlowContext.getAttributes().put("creditLimitDifference", limitDiff.toString());

                }
                if (GenericConstants.NCC_UPDATE_CREDITLIMIT_HANDLER.equals(activityId)) {

                    notificationUtils.sendNotification(orderFlowContext, "ORDER_COMPLETION_INCR", "ORDER");
                }
                if (GenericConstants.BS_BOOKDEPOSIT_HANDLER.equals(activityId)) {
                    notificationUtils.sendNotification(orderFlowContext, "BOOKDEPOSIT_NOTIFICATION", "ORDER");
                }
            }

        } else if (StringUtils.equalsAnyIgnoreCase(orderFlowContext.getOrder().getOrderType(),
                OrderTypes.CHANGE_SUBSCRIPTION)) {

            if (ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults())
                    && ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults().get("serviceInfo"))
                    && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getCreditLimit())) {
                var serviceInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("serviceInfo"),
                        JsonNode.class);
                if (ObjectUtils.isNotEmpty(serviceInfo) && serviceInfo.has("creditLimit")) {
                    var creditLimitSerInfo = new BigDecimal(serviceInfo.get("creditLimit").asText());
                    creditLimit = Double
                            .parseDouble(orderFlowContext.getOrder().getServiceManagement().getCreditLimit());

                    creditLimitChannel = BigDecimal.valueOf(creditLimit).setScale(2, RoundingMode.HALF_EVEN);

                    if (creditLimitChannel.compareTo(creditLimitSerInfo) >= 0) {
                        limitDiff = creditLimitChannel.subtract(creditLimitSerInfo);
                    } else if (creditLimitChannel.compareTo(creditLimitSerInfo) < 0) {
                        limitDiff = creditLimitSerInfo.subtract(creditLimitChannel);

                    }
                    orderFlowContext.getAttributes().put("creditLimitDifference", limitDiff.toString());

                }
                if (GenericConstants.BS_UPDATE_CREDITLIMIT_HANDLER.equals(activityId)) {

                    notificationUtils.sendNotification(orderFlowContext, "ORDER_COMPLETION_INCR", "ORDER");
                }
            }

            if (GenericConstants.BS_BOOKDEPOSIT_HANDLER.equals(activityId)) {
                notificationUtils.sendNotification(orderFlowContext, "BOOKDEPOSIT_NOTIFICATION", "ORDER");
            }
        } else if (StringUtils.equalsAnyIgnoreCase(orderFlowContext.getOrder().getOrderType(),
                OrderTypes.TRANSFER_OF_SERVICE)) {

            if (GenericConstants.BS_BOOKDEPOSIT_HANDLER.equals(activityId)) {
                notificationUtils.sendNotification(orderFlowContext, "BOOKDEPOSIT_NOTIFICATION", "ORDER");
            }
            if (GenericConstants.NCC_UPDATE_CREDITLIMIT_HANDLER.equals(activityId)) {
                if (ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults())
                        && ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults().get("serviceInfo"))
                        && ObjectUtils
                        .isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getCreditLimit())) {
                    var serviceInfo = objectMapper
                            .convertValue(orderFlowContext.getEnrichmentResults().get("serviceInfo"), JsonNode.class);
                    if (ObjectUtils.isNotEmpty(serviceInfo) && serviceInfo.has("creditLimit")) {
                        var creditLimitSerInfo = new BigDecimal(serviceInfo.get("creditLimit").asText());

                        creditLimit = Double
                                .parseDouble(orderFlowContext.getOrder().getServiceManagement().getCreditLimit());

                        creditLimitChannel = BigDecimal.valueOf(creditLimit).setScale(2, RoundingMode.HALF_EVEN);

                        if (creditLimitChannel.compareTo(creditLimitSerInfo) >= 0) {
                            limitDiff = creditLimitChannel.subtract(creditLimitSerInfo);
                            messageType = "_INCR";
                        } else if (creditLimitChannel.compareTo(creditLimitSerInfo) < 0) {
                            limitDiff = creditLimitSerInfo.subtract(creditLimitChannel);
                            messageType = "_DECR";
                        }
                        orderFlowContext.getAttributes().put("creditLimitDifference", limitDiff.toString());
                        notificationUtils.sendNotification(orderFlowContext, "ORDER_COMPLETION" + messageType, "ORDER");

                    }

                }
            }
        } else if (StringUtils.equalsAnyIgnoreCase(orderFlowContext.getOrder().getOrderType(),
                OrderTypes.UPDATE_CREDIT_LIMIT)) {
            if (GenericConstants.BS_BOOKDEPOSIT_HANDLER.equals(activityId))
                notificationUtils.sendNotification(orderFlowContext, "BOOKDEPOSIT_NOTIFICATION", "ORDER");
        } else if (StringUtils.equals(orderFlowContext.getOrder().getOrderType(), OrderTypes.FREEZE)
                && GenericConstants.ROLLBACK_NCC_UPDATE_MA_BALANCE.equals(activityId)) {
            orderFlowContext.getAttributes().put("NCCFreezeFailureNotifMessage",
                    getErrorCodeFromConfig("NCC_FREEZE_FAILURE_NOTIFICATION_MESSAGE"));
            notificationUtils.sendNotification(orderFlowContext, "NCC_FREEZE_FAILURE", "ORDER");

        } else if (StringUtils.equals(orderFlowContext.getOrder().getOrderType(), OrderTypes.ADD_SB_BENEFICIARY)
                && GenericConstants.ROLLBACK_NCC_UPDATE_MA_BALANCE.equals(activityId)) {
            orderFlowContext.getAttributes().put("NCCAddBeneficiaryFailureNotifMessage",
                    getErrorCodeFromConfig("NCC_ADD_BENEFICIARY_FAILURE_NOTIFICATION_MESSAGE"));
            notificationUtils.sendNotification(orderFlowContext, "NCC_ADD_BENEFICIARY_FAILURE", "ORDER");
        }

    }

    private void handleFailureActions(OrderFlowContext orderFlowContext, String activityId, DelegateExecution delegateExecution, String errorCode) {
        var NCCInsufficientBalanceErrorCode = "00015";
        NCCInsufficientBalanceErrorCode = getErrorCodeFromConfig("NCC_INSUFFICIENT_BALANCE_ERROR_CODE");
        var errorConfigCode = getErrorCodeFromConfig("INDIVIDUAL_ACTIVE_SERVICE_EXCEEDS_ERROR_CODE");
        if (StringUtils.equalsAnyIgnoreCase(orderFlowContext.getOrder().getOrderType(), OrderTypes.ADD_SUBSCRIPTION,
                OrderTypes.GIFTING, OrderTypes.FREEZE)
                && NCCInsufficientBalanceErrorCode.equalsIgnoreCase(errorCode)) {
            notificationUtils.sendNotification(orderFlowContext,
                    AppConstants.NotificationMessageTypes.NCC_INSUFFICIENT_BALANCE.value(), "ORDER");
        } else if (StringUtils.equalsAnyIgnoreCase(orderFlowContext.getOrder().getOrderType(), OrderTypes.ADD_SERVICE,
                OrderTypes.ADD_SERVICE_TO_NEW_ACCOUNT, OrderTypes.UPDATE_STARTER_PACK_KYC, OrderTypes.ONBOARDING)
                && errorConfigCode.equalsIgnoreCase(errorCode)) {
            delegateExecution.setVariable("isNMSRollBackReqd", true);
        } else if (orderFlowContext.isPermanentFailed()) {
            log.info("errorCode {} is marked as permanent failure, so skip/retry will be disabled", errorCode);
        } else if (workFlowUtil.isTaskRetryEnabled(orderFlowContext.getOrder().getOrderType(), activityId)) {
            throw new WorkflowTaskFailedException(activityId, errorCode,
                    orderFlowContext.getErrorDetail().getMessage());
        }
    }

    protected String getErrorCodeFromConfig(String key) {
        var errorCode = "";
        var appConfig = cache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG,
                key);
        if (appConfig != null) {
            errorCode = appConfig.getNgTableData().get("CONFIG_VALUE");
        }
        return errorCode;
    }

    private boolean isAsyncActivity(String orderType, String activityId) {
        var stageConfig = cache.getCacheDetailsFromDBMap(
                CacheConstants.CacheKeys.STAGE_CONFIG_BY_ACTIVITY_ID_AND_ORDER_TYPE.toString(),
                orderType + "_" + activityId);
        if (stageConfig == null)
            return false;
        return BooleanUtils.toBoolean(stageConfig.getNgTableData().get("IS_ASYNC"));
    }

}
