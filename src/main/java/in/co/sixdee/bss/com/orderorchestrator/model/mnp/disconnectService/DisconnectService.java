package in.co.sixdee.bss.com.orderorchestrator.model.mnp.disconnectService;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;



@XmlRootElement(name = "disconnectService")
@XmlAccessorType(XmlAccessType.FIELD)
public class DisconnectService {

	@XmlElement(name = "requestID",namespace = "http://group.singtel.com/manageporting-types/v1")
	private RequestId requestID;
	@XmlElement(name = "serviceDetailElements", namespace = "http://group.singtel.com/manageporting-types/v1")
	private ServiceDetailElements serviceDetailElements;

	public RequestId getRequestID() {
		return requestID;
	}

	public void setRequestID(RequestId requestID) {
		this.requestID = requestID;
	}

	public ServiceDetailElements getServiceDetailElements() {
		return serviceDetailElements;
	}

	public void setServiceDetailElements(
			ServiceDetailElements serviceDetailElements) {
		this.serviceDetailElements = serviceDetailElements;
	}

}
