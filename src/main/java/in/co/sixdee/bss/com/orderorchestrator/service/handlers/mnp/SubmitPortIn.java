package in.co.sixdee.bss.com.orderorchestrator.service.handlers.mnp;


import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderStageService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import in.co.sixdee.bss.com.orderorchestrator.config.util.MNPServiceUtil;
import in.co.sixdee.bss.com.orderorchestrator.config.util.XMLUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.XmlRequestBuilder;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.common.util.CommonUtils;
import lombok.extern.log4j.Log4j2;


@Log4j2
@Component(value = "submitPortIn")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class SubmitPortIn extends AbstractDelegate {


    private final XmlRequestBuilder xmlRequestBuilder;

    private final MNPServiceUtil mnpServiceUtil;

    private final MNPAttributeService mnpAttributeService;
    private final OrderStageService orderStageService;

    @Override
    protected void execute() throws Exception {

        /** mnp port in reference id to be stored order attributes against the order id and sub order id **/
        generateRequestTokens();
        mnpAttributeService.saveMNPReqAndRefIdAsAttributes(executionContext, isRetry());
        String request = getRequestFromXml();
        if (CommonUtils.INSTANCE.validateField(request)) {
            CallThirdPartyDTO callThirdPartyDTO = callThirdParty(request);
            if (callThirdPartyDTO == null) {
                executionContext.setError(true);
                return;
            }
            var response = callThirdPartyDTO.getResponse();
            validateResponse(callThirdPartyDTO);
            if (!executionContext.isError())
                validateSubmitPortRequestResponse(response);
        } else {
            log.error("Unable to form the tp request, setting error");
            executionContext.getErrorDetail().setCode("COM-001");
            executionContext.getErrorDetail().setMessage("Internal Error: error in forming tp request");
            executionContext.getErrorDetail().setSystem("COM");
            executionContext.setError(true);
        }

        workflowDataUpdated = true;
        if (isRetry()) {
            execution.getVariables().remove("retry");
            execution.getVariables().remove("failedActivityId");
            orderStageService.deletePortInNotificationStages(Long.parseLong(executionContext.getAttributes().get("orderId")), Long.parseLong(subOrderId));
        }

    }

    private String getRequestFromXml() {
        String request = null;
        OrderStageEntity failedStage = null;
        if (executionVariables.get("failedActivityId") != null) {
            String failedActivityId = (String) executionVariables.get("failedActivityId");
            failedStage = orderStageService.findLastModifiedDateAndSateReasonByStageCode(Long.parseLong(executionContext.getOrder().getOrderId()), Long.parseLong(subOrderId), failedActivityId);
        }
        String customerType = mnpServiceUtil.findMNPCustomerNameAndId(executionContext, isRetry(), failedStage);
        request = xmlRequestBuilder.buildRequest(orderType + "_SubmitPortRequest_" + customerType, executionContext, null);
        if (StringUtils.isEmpty(request))
            request = xmlRequestBuilder.buildRequest(orderType + "_SubmitPortRequest", executionContext, null);
        return request;
    }

    public void generateRequestTokens() {
        String donorTelcoId = executionContext.getAttributes().get(MNPConstants.DONOR_OPERATOR_CODE);
        String recipientTelcoId = executionContext.getAttributes().get(MNPConstants.RECIPIENT_OPERATOR_CODE);
        executionContext.getAttributes().put(MNPConstants.MNP_REQUEST_ID,
                mnpServiceUtil.generateMNPRequestId(executionContext.getOrder().getOrderId()));
        executionContext.getAttributes().put(MNPConstants.MNP_REFERENCE_ID,
                mnpServiceUtil.generateMNPReferenceId(donorTelcoId, recipientTelcoId));
    }

    private void validateSubmitPortRequestResponse(String response) {
        String errorCode = null;
        String errorMessage = null;


        try {

            if (StringUtils.isNotEmpty(response)) {

                if (response.contains(
                        "transactionStatusCode>SUCCESS")) {
                    log.info("SDP call is success");
                } else if (response.contains("Fault")) {
                    DocumentBuilderFactory dbFactory = XMLUtils.getDocumentBuilderFactory();
                    DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
                    org.w3c.dom.Document doc = null;
                    doc = dBuilder.parse(new ByteArrayInputStream(response.getBytes(StandardCharsets.UTF_8)));

                    if (response.contains("faultcode"))
                        errorCode = doc.getElementsByTagName("faultcode").item(0).getTextContent();
                    if (response.contains("faultstring"))
                        errorMessage = doc.getElementsByTagName("faultstring").item(0).getTextContent();

                    log.info("Got failure response from Singtel SDP");
                    if (errorCode != null) {
                        CacheTableDataDTO errorCodeMappingDTO = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.MNP_ERROR_CODE_MAPPING.toString(), errorCode);
                        if (errorCodeMappingDTO != null) {
                            String errorMessageMapping = errorCodeMappingDTO.getNgTableData().get(CacheConstants.CacheFields.ERROR_DESC.toString());
                            if (StringUtils.isNotEmpty(errorMessageMapping))
                                errorMessage = errorMessageMapping;
                        }
                    }
                    executionContext.getErrorDetail().setSystem("Singtel-SDP");
                    executionContext.setError(true);
                    executionContext.getErrorDetail().setCode(errorCode);
                    executionContext.getErrorDetail().setMessage(errorMessage);
                }

            } else {
                log.info("No response received from SDP. setting error");
                executionContext.getErrorDetail().setSystem("Singtel-SDP");
                executionContext.setError(true);
                executionContext.getErrorDetail().setCode("COM-005");
                executionContext.getErrorDetail().setMessage("No response received from SDP");
            }

        } catch (Exception e) {
            log.error(" Exception occurred in parseSubmitPortRequestResponse ", e);

        }


    }

}
