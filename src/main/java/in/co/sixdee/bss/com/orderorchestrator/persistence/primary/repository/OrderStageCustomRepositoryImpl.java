package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OrderStageCustomRepositoryImpl implements OrderStageCustomRepository{

	@Autowired
	EntityManager entityManager;
	
	@Override
	public List<Long> getSubOrderIdByOrderIdAndStageCodeAndState(Long orderId, String ponrRule) {
		// TODO Auto-generated method stub
		StringBuilder query = new StringBuilder();
		String[] stageStatePairArray = ponrRule.split("\\|");
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		
		query.append("select distinct o.subOrderId from OrderStageEntity o where o.orderId= :orderId and (");
		
		parameterMap.put("orderId", orderId);
		for (int i = 0; i < stageStatePairArray.length; i++) {
			String stageStatePair = stageStatePairArray[i];
			String[] stageAndState = stageStatePair.split("=");
			query.append("(o.stageCode=:" + "stage" + i + " and ");
			query.append("o.state=:" + "state" + i + ")");
			parameterMap.put("stage" + i, stageAndState[0]);
			parameterMap.put("state" + i, stageAndState[1]);

			query.append(" or ");

		}
		query.append(")");
		query.delete(query.length() - 4, query.length() - 1);
		Query jpaQuery = entityManager.createQuery(query.toString());

		for (String mapKey : parameterMap.keySet()) {
			jpaQuery.setParameter(mapKey, parameterMap.get(mapKey));
		}
		return jpaQuery.getResultList();

	}

}
