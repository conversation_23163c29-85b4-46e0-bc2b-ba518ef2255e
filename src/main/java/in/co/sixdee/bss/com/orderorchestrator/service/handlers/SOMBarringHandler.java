package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.om.model.dto.order.*;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

@Log4j2
@Component(value = "somBarring")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class SOMBarringHandler extends AbstractDelegate {

	protected String planId = null;

	protected String addonServiceId = null;

	private Order orderPayload = null;

	protected String request = null;

	protected int index = 0;

	protected final ObjectMapper objectMapper;
	
	protected String connectionType = null;

	@Override
	protected void execute() throws Exception {
		try {
			orderPayload = executionContext.getOrder();
			request = createSOMRequest();
			var callThirdPartyDTO = callThirdParty(request);
			validateResponse(callThirdPartyDTO);
		} catch (Exception e) {
			log.error("{} In {} .execute Exception occured ", executionContext.getTraceId(), this.getClass().getName(), e);
		}
	}

	protected String createSOMRequest() throws Exception {
		//log.info("ofc::"+JsonUtils.toJsonString(executionContext));
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		var serviceOrder = new SOMServiceOrderDTO();
		serviceOrder.setExternalId(executionContext.getOrder().getOrderId());
		serviceOrder.setDescription("Add Service Order Items");
		serviceOrder.setRequestedStartDate(
				StringUtils.isNotEmpty(orderPayload.getRequestedStartDate()) ? orderPayload.getRequestedStartDate()
						: Instant.now().toString());
		serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
		serviceOrder.setType("ServiceOrder");
		executionContext.getAttributes().put("callbackCorrelationId",
				callbackEvent + ":" + executionContext.getAttributes().get(GenericConstants.SUB_ORDER_ID));
		if (executionContext.getEnrichmentResults().containsKey("serviceInfo")) {
			var serviceInfo = objectMapper.convertValue(executionContext.getEnrichmentResults().get("serviceInfo"),
					LinkedHashMap.class);
			connectionType = serviceInfo.get("chargingPattern").toString();
		}
		serviceOrderItemList = createServiceOrderItemBasedOnEnrichedData();
		serviceOrder.setExternalServiceId(addonServiceId);
		serviceOrder.setServiceOrderItem(serviceOrderItemList);
		request = objectMapper.writeValueAsString(serviceOrder);
		return request;
	}

	private List<ServiceOrderItem> createServiceOrderItemBasedOnEnrichedData() {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		List<CFSRef> CFSS = null;
		try {
			// cfss for barring is fetched from upc, same needs to activated in SOM.

			addonServiceId = executionContext.getOrder().getServiceManagement().getServiceId();

			planId = executionContext.getOrder().getServiceManagement().getSubscriptions().get(0).getPlanId();
			var cfsUPCResp = executionContext.getObjectAttributes().get("CFSSDunning");
			if (ObjectUtils.isNotEmpty(cfsUPCResp)) {
				CFSS = objectMapper.convertValue(cfsUPCResp, new TypeReference<List<CFSRef>>() {
				});
				CFSS.forEach(cfs -> {
					if(!GenericConstants.CFS_UPC.equalsIgnoreCase(cfs.getName())) {
						var serviceOrderItem = createServiceOrderItem(cfs, "CFS");
						serviceOrderItemList.add(serviceOrderItem);
					}
				});
			}

			return serviceOrderItemList;

		} catch (Exception e) {
			log.error(" :::::  Exception occurred in parse UPCResponse execute method  :::::", e);
		}
		return null;

	}

	protected ServiceOrderItem createServiceOrderItem(CFSRef cfss, String type) {
		var serviceOrderItem = new ServiceOrderItem();
		index = index + 1;
		serviceOrderItem.setId(String.valueOf(index));
		serviceOrderItem.setAction("add");
		serviceOrderItem.setType("ServiceOrderItem");
		var serviceItem = createServiceItem(cfss, type);
		serviceOrderItem.setService(serviceItem);
		return serviceOrderItem;
	}

	protected SOMService createServiceItem(CFSRef cfss, String type) {
		var serviceItem = new SOMService();
		serviceItem.setState("active");
		serviceItem.setType(type);
		var specification = new ProductSpecification();
		specification.setName(cfss.getName());
//		specification.setType(type);
		specification.setId(cfss.getId());
		serviceItem.setServiceSpecification(specification);
		var characteristics = createServiceCharacteristic(cfss.getCharacteristics());
		if (type.equalsIgnoreCase("CFS"))
			setSubscriptionId(characteristics);
		serviceItem.setServiceCharacteristic(characteristics);
		return serviceItem;
	}

	protected void setSubscriptionId(List<Characteristic> characteristics) {

		if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData().get("subscriptionIdMap"))) {
			@SuppressWarnings("unchecked")
			HashMap<String, String> subscriptionIdMap = (HashMap<String, String>) executionContext.getWorkflowData().get("subscriptionIdMap");
			if (subscriptionIdMap.get(planId) != null) {
				var characteristic = new Characteristic();
				characteristic.setName("SUBSCRIPTION_ID");
				characteristic.setValueType("String");
				characteristic.setValue(subscriptionIdMap.get(planId));
				characteristics.add(characteristic);
			}

		}
	}

	protected List<Characteristic> createServiceCharacteristic(List<CFSCharacteristicRef> characteristics) {
		var characteristicList = new ArrayList<Characteristic>();
		if (ObjectUtils.isNotEmpty(characteristics))
			characteristics.forEach(cfsCharacteristic -> {
				var characteristic = new Characteristic();
				if (!"SUBSCRIPTION_ID".equals(cfsCharacteristic.getName())) {
					characteristic.setName(cfsCharacteristic.getName());
					characteristic.setValueType(cfsCharacteristic.getDataType());
					if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& ObjectUtils.isNotEmpty(connectionType)
							&& cfsCharacteristic.getName().equalsIgnoreCase("CONNECTION_TYPE"))
						characteristic.setValue(connectionType);
					else
						characteristic.setValue(cfsCharacteristic.getValue());
					characteristicList.add(characteristic);
				}
			});
		return characteristicList;
	}

}
