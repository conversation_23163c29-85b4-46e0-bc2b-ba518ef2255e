package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.WorkflowTaskFailedException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.constants.GenericLogConstants;
import in.co.sixdee.bss.common.util.CommonUtils;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Charge;
import in.co.sixdee.bss.om.model.dto.order.ProductOffering;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "planComparision")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PlanComparisionHandler extends AbstractDelegate {

	@Override
	protected void execute() throws Exception {
		List<Subscription> smSubscriptions = new ArrayList<>();
		List<Subscription> channelSubscriptions = new ArrayList<>();
		List<Subscription> newcartaddonList = new ArrayList<>();
		String request = null;
		try {
			
			var callThirdPartyDTO = callThirdParty(null);
			if (callThirdPartyDTO == null) {
				executionContext.setError(true);
				executionContext.getErrorDetail().setMessage("Unable to find the existing subscriptions from SM");
				return;
			}
			var response = callThirdPartyDTO.getResponse();
			validateResponse(callThirdPartyDTO);
			if (executionContext.isError())
				return;
			modifyWorkflowData(response);
			var smRespJson = objectMapper.readTree(response);
			var offers = smRespJson.get("data");
			smSubscriptions = objectMapper.readValue(objectMapper.writeValueAsString(offers), new TypeReference<List<Subscription>>() {
			});

			if (StringUtils.equalsAnyIgnoreCase(executionContext.getOrder().getOrderType(), OrderTypes.CONNECTION_MIGRATION,
					OrderTypes.TERMINATE_SERVICE))
				channelSubscriptions = executionContext.getOrder().getServiceManagement().getSubscriptions();
			else
				channelSubscriptions = executionContext.getOrder().getServiceManagement().getNewCart().getSubscriptions();
			//checkEligibilityCriteria(channelSubscriptions, smSubscriptions);
			compareOldVsNewPlans(channelSubscriptions,smSubscriptions);
			if (StringUtils.equalsAnyIgnoreCase(executionContext.getOrder().getOrderType(),
					OrderTypes.CHANGE_SUBSCRIPTION)) {
				//removing common addons from new plan//
				executionContext.getOrder().getServiceManagement().getNewCart().getSubscriptions()
				.removeIf(sub -> sub.isCommonAddon());
				
				newcartaddonList=executionContext.getOrder().getServiceManagement().getNewCart().getSubscriptions().stream().filter(sub -> "0".equals(sub.getPlanType())).collect(Collectors.toList());
				if(newcartaddonList!=null)
				{
				executionContext.getWorkflowData().put("newSubscriptionAddonCart",newcartaddonList);
				}
			}
			workflowDataUpdated = true;

		} catch (Exception e) {
			log.error(GenericLogConstants.TAG_APP + " :::::  Exception occured in CheckEligibilityCriteria execute method  :::::",
					e);
			executionContext.getErrorDetail().setCode("COM-001");
			if(e instanceof WorkflowTaskFailedException) {
				executionContext.getErrorDetail().setMessage(e.getMessage());
				executionContext.getErrorDetail().setSystem("Billing");
			} else {
				executionContext.getErrorDetail().setMessage("Internal Error: error in forming tp request");
				executionContext.getErrorDetail().setSystem("COM");
			}
			executionContext.setError(true);
			return;
		}
	}

	private void compareOldVsNewPlans(List<Subscription> newSubscriptions, List<Subscription> oldSubscriptions) throws JsonProcessingException {
		boolean billingAddSubReqd = false;
		List<Subscription> subscriptionsToBeCancelled = new ArrayList<>();
		List<Subscription> oldBaseOffer = new ArrayList<>();
		if (oldSubscriptions != null) {
			for (Subscription subscription : oldSubscriptions) {
				if (StringUtils.isNotEmpty(subscription.getLinkedPlanId())) {
					log.info("Plan {} is an included addon customer already subscribed to. adding to the cancellation list", subscription.getPlanName());
					Subscription cancelSub = new Subscription();
					cancelSub.setPlanId(subscription.getPlanId());
					cancelSub.setSubscriptionId(subscription.getSubscriptionId());
					cancelSub.setPackageType(subscription.getPackageType());
					cancelSub.setAutoRenewal(subscription.getAutoRenewal());
					cancelSub.setOcsPlanId(subscription.getOcsPlanId());
					subscriptionsToBeCancelled.add(cancelSub);
				}
				if("1".equals(subscription.getPlanType())){
					oldBaseOffer.add(subscription);

				}
			}
		}
		if (newSubscriptions != null) {
			for (Subscription subscription : newSubscriptions) {
				if ("0".equals(subscription.getPlanType())) {
					billingAddSubReqd = true;
					break;
				}
			}
		}

		execution.setVariable("billingAddSub", billingAddSubReqd);
		executionContext.getWorkflowData().put("BSAddSubscriptionReq", billingAddSubReqd+"");
		if (!subscriptionsToBeCancelled.isEmpty()) {
			executionContext.getWorkflowData().put("CancelSubscriptionCart", subscriptionsToBeCancelled);
			executionContext.getWorkflowData().put("BSCancelSubscriptionReq", "true");
		}
		if (ObjectUtils.isNotEmpty(oldBaseOffer)) {
			executionContext.getWorkflowData().put("oldBaseOffer", oldBaseOffer);
		} else {
			throw new WorkflowTaskFailedException(activityId, "COM-001", "Unable to get base plan details from billing");
		}
		execution.setVariable("upcFecthReq", false);
	}


	protected void checkEligibilityCriteria(List<Subscription> channelSubscriptions,
			List<Subscription> smSubscriptions) {

		List<String> ocsNewPlansList = new ArrayList<>();
		List<String> newCartPlansList = new ArrayList<>();
		boolean smUpdateSub = false;
		List<Subscription> smUpdateSubSeqList = new ArrayList<>();
		Set<String> planIds = new HashSet<>();
		List<Subscription> commonAddonsList = new ArrayList<>();
		List<Subscription> cancelSubList = new ArrayList<>();

		try {
			executionContext.getWorkflowData().put("BSCancelSubscriptionReq", "false");
			executionContext.getWorkflowData().put("BSAddSubscriptionReq","false");

			var existingAddons = smSubscriptions.stream().filter(sub -> "0".equals(sub.getPlanType())).collect(Collectors.toList());
			var oldBaseOffer = smSubscriptions.stream().filter(sub -> "1".equals(sub.getPlanType())).collect(Collectors.toList());
			var mandatory = channelSubscriptions.stream().anyMatch(sub -> sub.isMandatoryAddon());

			if(mandatory)
			{
				if(ObjectUtils.isNotEmpty(existingAddons))
				{
				for (Subscription existingAddon : existingAddons) {
					var newSubscription = channelSubscriptions.stream().filter(channelSub -> channelSub.getPlanId().equals(existingAddon.getPlanId())).findAny().orElse(null);
					if (newSubscription != null) {
						// plan exist in same plan setting as common addon true to remove if exist in new baseplan
						smUpdateSub = true;
						Subscription commonAddon = new Subscription();
						commonAddon.setSubscriptionId(existingAddon.getSubscriptionId());
						commonAddon.setPlanId(existingAddon.getPlanId());
						commonAddonsList.add(commonAddon);
						//setting as common addon to remove from new planId from subscription//
						newSubscription.setCommonAddon(true);
						newSubscription.setSubscriptionId(existingAddon.getSubscriptionId());
					} else {
						//To cancel old base plan addon which are not matching with new base offer addon plans
						
						if(StringUtils.isNotEmpty(existingAddon.getAutoRenewal()) && existingAddon.getAutoRenewal().equalsIgnoreCase("1"))
						{
							planIds.add(existingAddon.getPlanId());
							Subscription cancelSub = new Subscription();
							cancelSub.setPlanId(existingAddon.getPlanId());
							cancelSub.setSubscriptionId(existingAddon.getSubscriptionId());
							cancelSub.setPackageType(existingAddon.getPackageType());
							cancelSub.setAutoRenewal(existingAddon.getAutoRenewal());
							cancelSub.setOcsPlanId(existingAddon.getOcsPlanId());
							cancelSubList.add(cancelSub);
						}
						}
					}
				
				}
				/*checking new subscription cart is having addon to enable billing addsubscription call*/		
				var billingAddSub = channelSubscriptions.stream().anyMatch(sub -> !sub.isCommonAddon() && "0".equals(sub.getPlanType()));
				execution.setVariable("billingAddSub", billingAddSub);	
				executionContext.getWorkflowData().put("BSAddSubscriptionReq",execution.getVariable("billingAddSub").toString());

				if (!cancelSubList.isEmpty()) 
				{
					executionContext.getWorkflowData().put("CancelSubscriptionCart", cancelSubList);
					executionContext.getWorkflowData().put("BSCancelSubscriptionReq", "true");
				}
				if (!commonAddonsList.isEmpty())
				{
					executionContext.getWorkflowData().put("commonAddonsList", commonAddonsList);
				}
			
			}
	
			if (oldBaseOffer != null) 
			{
				executionContext.getWorkflowData().put("oldBaseOffer", oldBaseOffer);
			}
			execution.setVariable("upcFecthReq", false);

		} catch (Exception e) {
			log.error("Exception occurred in checkEligibilityCriteria ", e);
		}

	}

	
	protected void checkEligibilityCriteriaSafaricomOld(List<Subscription> channelSubscriptions,
			List<Subscription> smSubscriptions) {

		List<String> smNewPlansList = new ArrayList<>();
		List<String> nccPlansList = new ArrayList<>();
		boolean smUpdateSub = false;
		List<Subscription> smUpdateSubSeqList = new ArrayList<>();
		Set<String> planIds = new HashSet<>();
		List<Subscription> nonEligibleSubs = new ArrayList<>();
	
		try {
			var existingAddons = smSubscriptions.stream().filter(sub -> "1".equals(sub.getPlanType())).collect(Collectors.toList());
			var oldBaseOffer = smSubscriptions.stream().filter(sub -> "0".equals(sub.getPlanType())).collect(Collectors.toList());

			for (Subscription existingAddon : existingAddons) {
				var newSubscription = channelSubscriptions.stream().filter(channelSub -> channelSub.getPlanId().equals(existingAddon.getPlanId())).findAny().orElse(null);
				if (newSubscription != null) {
					// plan exists in new cart and old cart. since it is a common addon, no need to do any action in BIL and NCC, new serviceSeqId should be updated in SM for common addons
					smUpdateSub = true;
					Subscription smUpdateSubSeq = new Subscription();
					smUpdateSubSeq.setSubscriptionId(existingAddon.getSubscriptionId());
					smUpdateSubSeq.setPlanId(existingAddon.getPlanId());
					smUpdateSubSeqList.add(smUpdateSubSeq);

					newSubscription.setCommonAddon(true);
					newSubscription.setSubscriptionId(existingAddon.getSubscriptionId());
				} else {
					planIds.add(existingAddon.getPlanId());
					Subscription nonEligibleSub = new Subscription();
					nonEligibleSub.setPlanId(existingAddon.getPlanId());
					nonEligibleSub.setSubscriptionId(existingAddon.getSubscriptionId());
					nonEligibleSub.setPackageType(existingAddon.getPackageType());
					nonEligibleSub.setAutoRenewal(existingAddon.getAutoRenewal());
					nonEligibleSubs.add(nonEligibleSub);
				}
			}
			smNewPlansList = channelSubscriptions.stream().filter(sub -> !sub.isCommonAddon()).map(sub -> sub.getPlanId()).collect(Collectors.toList());
			if (StringUtils.equalsAnyIgnoreCase(executionContext.getOrder().getOrderType(), OrderTypes.CHANGE_SUBSCRIPTION)) {
				nccPlansList = channelSubscriptions.stream().filter(sub -> !sub.isCommonAddon() && sub.isEsbCallRqd()).map(sub -> sub.getExternalOfferId()).collect(Collectors.toList());
			} else {
				nccPlansList = channelSubscriptions.stream().filter(sub -> !sub.isCommonAddon() && "1".equals(sub.getPlanType()) && sub.isEsbCallRqd()).map(sub -> sub.getExternalOfferId()).collect(Collectors.toList());
			}
			var billingAddSub = channelSubscriptions.stream().anyMatch(sub -> !sub.isCommonAddon() && "1".equals(sub.getPlanType()));
			execution.setVariable("billingAddSub", billingAddSub);
			var nccAddSub = !nccPlansList.isEmpty();
			execution.setVariable("nccAddSub", nccAddSub);
			
			if (nccAddSub) {
				executionContext.getWorkflowData().put("nccAddSub", nccPlansList);
				
				if (StringUtils.equalsAnyIgnoreCase(executionContext.getOrder().getOrderType(), OrderTypes.CHANGE_SUBSCRIPTION)
						&& (StringUtils.equalsAnyIgnoreCase(executionContext.getChannel(),GenericConstants.USSD_CHANNEL))) {
					 getTotalCreditLimit();
				}
			}
			execution.setVariable("smNewPlansList", smNewPlansList);
			executionContext.getWorkflowData().put("smNewPlansList", smNewPlansList);
			if (oldBaseOffer != null) {
				executionContext.getWorkflowData().put("oldBaseOffer", oldBaseOffer);
			}
			execution.setVariable("smUpdateSeqId", smUpdateSub);
			if (!smUpdateSubSeqList.isEmpty())
				executionContext.getWorkflowData().put("commonAddonsList", smUpdateSubSeqList);
			
			var upcFecthReq = !planIds.isEmpty();
			execution.setVariable("upcFecthReq", upcFecthReq);
			if (upcFecthReq)
				executionContext.getAttributes().put("nonAllowedAddons", String.join(",", planIds));
			if (!nonEligibleSubs.isEmpty())
				executionContext.getWorkflowData().put("nonEligibleAddons", nonEligibleSubs);

			execution.setVariable("isNCCStopRenewal", false);
			execution.setVariable("isNCCCancelSub", false);
			execution.setVariable("isStopRenewal", false);
			execution.setVariable("isCancelSubscription", false);

		} catch (Exception e) {
			log.error("Exception occurred in checkEligibilityCriteria ", e);
		}

	}

	private void getTotalCreditLimit() {
		var creditLimit = "";
		var totalCreditLimit = 0.00;

		if (ObjectUtils.isNotEmpty(executionContext.getEnrichmentResults().get("serviceInfo"))) {
			var serviceInfo = objectMapper.convertValue(executionContext.getEnrichmentResults().get("serviceInfo"),
					HashMap.class);
			if (ObjectUtils.isNotEmpty(serviceInfo) && ObjectUtils.isNotEmpty(serviceInfo.get("creditLimit"))) {
				creditLimit = serviceInfo.get("creditLimit").toString();
				totalCreditLimit = Double.valueOf(creditLimit);
			}
		}

		var subscriptionsList = executionContext.getOrder().getServiceManagement().getNewCart().getSubscriptions();

		if (ObjectUtils.isNotEmpty(subscriptionsList)) {
			for (Subscription subscription : subscriptionsList) {
				if (((subscription.isMandatoryAddon() && subscription.isEsbCallRqd())|| GenericConstants.PLAN_TYPE_BASE.equals(subscription.getPlanType())) &&  ObjectUtils.isNotEmpty(subscription.getCharges())) {
					for (Charge charge : subscription.getCharges()) {
						if (StringUtils.isNotEmpty(charge.getUpcPriceCategory())) {
							var appConfig = cache.getCacheDetailsFromDBMap(
									NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG, "UPC_PRICE_CATEGORY_FOR_CHANGE_PLAN_CL");
							if (appConfig != null) {
								String upcPriceCategoryList = appConfig.getNgTableData().get("CONFIG_VALUE");
								
								Set<String> upcPriceCategory = new HashSet<String>(Arrays.asList(upcPriceCategoryList.split(",")));
								if (upcPriceCategory.contains(charge.getUpcPriceCategory())) {
									totalCreditLimit = Double.valueOf(charge.getAmount()) + totalCreditLimit;
								}
							}
						}
					}
				} 
			}
		}

		log.info("Final totalCreditLimit ..."+totalCreditLimit+" && creditLimit = "+creditLimit);
		if(CommonUtils.INSTANCE.validateField(creditLimit) && totalCreditLimit > Double.valueOf(creditLimit)) {
			executionContext.getAttributes().put("flagForSetCreditLimit", "true");
			executionContext.getAttributes().put("newCreditLimitToBeSet", String.valueOf(totalCreditLimit));
		}else {
			executionContext.getAttributes().put("flagForSetCreditLimit", "false");
		}
	}

	protected void getOldBasePlanInfoFromUPC() {

		var callThirdPartyDTO = callThirdParty(null);
		if (callThirdPartyDTO == null) {
			executionContext.setError(true);
			return;
		}
		var response = callThirdPartyDTO.getResponse();
		validateResponse(callThirdPartyDTO);
		if (executionContext.isError()) {
			return;
		}
		getExternalOfferIdFromUPC(response);
	}
	
	protected void getExternalOfferIdFromUPC(String response) {
		try {
			var productOfferings = objectMapper.readValue(response, new TypeReference<List<ProductOffering>>() {});
			productOfferings.forEach(po -> {
				var cfss = po.getProductSpecifications().stream().map(ps -> ps.getCfss()).findAny().orElse(null);
				if (ObjectUtils.isNotEmpty(cfss)) {
					cfss.forEach(cfs -> {
						var cfsCharacteristic = cfs.getCharacteristics().stream()
								.filter(cfsChar -> StringUtils.isNotEmpty(cfsChar.getName())
										&& StringUtils.isNotEmpty(cfsChar.getValue())
										&& cfsChar.getName().equalsIgnoreCase(GenericConstants.CHARACTERISTIC_EXTERNAL_OFFER_ID))
								.findAny().orElse(null);
						if (ObjectUtils.isNotEmpty(cfsCharacteristic)) {
							var externalOfferId = cfsCharacteristic.getValue();
							if (StringUtils.isNotEmpty(externalOfferId))
								executionContext.getWorkflowData().put("oldBasePlanExternalOfferId", externalOfferId);
						}
					});
				}
			});
		} catch (JsonProcessingException e) {
			log.error("Error in Parsing UPC allowed addons response ", e);
		}
	}


}
