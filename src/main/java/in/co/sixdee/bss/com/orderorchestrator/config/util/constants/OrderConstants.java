package in.co.sixdee.bss.com.orderorchestrator.config.util.constants;

public class OrderConstants {

    private OrderConstants() {

    }

    public static final String ORDER_CODE_ACCESS = "OC_ACCESS";

    public static final String SERVICE_ORDER_TYPE_ACCESS = "ACCESS";

    public static final String SERVICE_ORDER_TYPE_SERVICE = "SERVICE";

    public static final String KEY_SUB_ORDER_INDEX = "subOrderIndex";

    public static final String INSTALLATION_TYPE_STORE_PICKUP = "1";
    public static final String INSTALLATION_TYPE_TECH_INSTALL = "2";
    public static final String INSTALLATION_TYPE_SHIPMENT = "3";

    public static final String RETURN_TYPE_STORE_DROP_OFF = "1";
    public static final String RETURN_TYPE_TECH_PICKUP = "2";
    public static final String RETURN_TYPE_DROP_BOX = "3";

    public static final String SERVICE_TYPE_IPTV = "IPTV";

    public static final String RETRY_STRATEGY_NORETRY = "NO_RETRY";
    public static final String RETRY_STRATEGY_CONNECTION_ERROR = "HTTP_ERROR";
    public static final String RETRY_STRATEGY_API_ERROR = "API_ERROR";
    public static final String RETRY_STRATEGY_BOTH = "ALL";


    

}
