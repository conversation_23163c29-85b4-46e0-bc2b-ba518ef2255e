package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

@Log4j2
@Component(value = "bsFetchService")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class BSFetchService extends AbstractDelegate {

	@Override
	protected void execute() throws Exception {
		String request = getRequestFromSpec();
		if (executionContext.isError())
			return;

		var callThirdPartyDTO = callThirdParty(request);
		if (callThirdPartyDTO == null) {
			executionContext.setError(true);
			return;
		}
		String response = callThirdPartyDTO.getResponse();
		validateResponse(callThirdPartyDTO);
		if (executionContext.isError())
			return;
		processResponseData(response);
		if (executionContext.isError())return;		
		modifyWorkflowData(response);
		workflowDataUpdated = true;
		log.info("Received valid billing service, proceeding with flow. :: {}",
				objectMapper.writeValueAsString(executionContext));
	}

	protected void processResponseData(String response) throws Exception {
		JsonNode billingResponse = objectMapper.readTree(response);
		JsonNode dataNode = billingResponse.get("data");
		if (dataNode==null||dataNode.isEmpty()) {
			log.info("don't have any service inside dataNode"); 					
			execution.setVariable("accountandProfileDeletionReg", true);
			return;
		}
		if (dataNode.size() == 1) {
			var responseServiceId = dataNode.get(0).get("serviceId").asText();
			log.info("Service ID retrieved from billing response: {}", responseServiceId);
			var requestServiceId =  executionContext.getAttributes().get("serviceId");
			log.info("Service ID retrieved from orderFlowContext: {}", requestServiceId);

			if (StringUtils.isNotEmpty(responseServiceId) 
					&& StringUtils.isNotEmpty(requestServiceId)&& responseServiceId.equalsIgnoreCase(requestServiceId)) {
				execution.setVariable("accountandProfileDeletionReg", true);
				log.info("Single matching service found (count: {}, serviceId: {}), setting accountandProfileDeletionReg to true",
						dataNode.size(), responseServiceId);
			} else {
				execution.setVariable("accountandProfileDeletionReg", false);
				log.info("Service ID mismatch. Response serviceId: {}, request serviceId: {}, setting accountandProfileDeletionReg to false", 
						responseServiceId, requestServiceId);
				executionContext.setError(true);
			}
		} else {
			execution.setVariable("accountandProfileDeletionReg", false);
			log.info("Multiple services found: {}, setting accountandProfileDeletionReg to false", dataNode.size());
			executionContext.setError(true);
		}
	}
}
