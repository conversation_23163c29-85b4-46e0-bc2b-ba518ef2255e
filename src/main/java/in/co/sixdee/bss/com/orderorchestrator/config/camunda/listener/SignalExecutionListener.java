package in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener;

import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.config.notification.NotificationUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants.BpmnConstants;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.WaitingProcessInfoEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.WaitingProcessInfoRepository;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderStatusManager;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.core.AppInstanceIdManager;
import in.co.sixdee.bss.common.util.ApplicationProcessContext;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.camunda.bpm.model.bpmn.instance.ReceiveTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
@Log4j2
public class SignalExecutionListener implements ExecutionListener {

	@Autowired
	private WaitingProcessInfoRepository	waitingProcessInfoRepository;

	@Autowired
	private OrderStatusManager				orderStatusManager;

	@Autowired
	private GetDataFromCache				cache;

	@Autowired
	private AppInstanceIdManager			appInstanceSequence;

	protected ApplicationProcessContext		processContext;

	@Autowired
	protected ProcessDataAccessor			processDataAccessor;

	@Autowired
	private NotificationUtils notificationUtils;

	@Override
	public void notify(DelegateExecution execution) {
		try {
			String subOrderId = null;
			var orderFlowContext = processDataAccessor.getOrderFlowContext(execution);
			processContext = new ApplicationProcessContext(appInstanceSequence.getInstanceId(), orderFlowContext.getTraceId(),
					orderFlowContext.getRequestId(), orderFlowContext.getChannel(), orderFlowContext.getChannel(), orderFlowContext.getEntityId());
			processContext.setMdc();
			subOrderId = orderFlowContext.getAttributes().get(GenericConstants.SUB_ORDER_ID);
			var linkedActivityId = execution.getVariables().containsKey("linkedServiceTask")
					? execution.getVariable("linkedServiceTask").toString()
					: execution.getCurrentActivityId();
			if (StringUtils.equals(ExecutionListener.EVENTNAME_START, execution.getEventName())) {
				var linkedStage = cache.getCacheDetailsFromDBMap("STAGE_CONFIG_BY_ACTIVITY_ID_AND_ORDER_TYPE",
						orderFlowContext.getOrder().getOrderType() + "_" + linkedActivityId);
				var stageCode = "";
				if (ObjectUtils.isNotEmpty(linkedStage))
					stageCode = linkedStage.getNgTableData().get("STAGE_ID");
				WaitingProcessInfoEntity waitingProcessInfoEntity = new WaitingProcessInfoEntity();
				waitingProcessInfoEntity.setOrderId(orderFlowContext.getOrder().getOrderId());
				waitingProcessInfoEntity.setSubOrderId(subOrderId);
				if (StringUtils.equals(BpmnConstants.RECEIVE_TASK_ACTIVITY.getStringValue(),
						execution.getBpmnModelElementInstance().getElementType().getTypeName())) {
					ReceiveTask task = execution.getBpmnModelInstance().getModelElementById(execution.getCurrentActivityId());
					waitingProcessInfoEntity.setEventName(task.getMessage().getName());
					/*if (task.getMessage().getName().equalsIgnoreCase("SuspendWait")) {
						if (execution.getVariables().containsKey("level")
								&& execution.getVariable("level").toString().equalsIgnoreCase("order")) {
							waitingProcessInfoEntity.setWaitType("SuspendWait_order");
							waitingProcessInfoEntity.setSubOrderId("0");
						}
					} else*/
					waitingProcessInfoEntity.setWaitType("Callback");
				} else {
					waitingProcessInfoEntity.setEventName(execution.getCurrentActivityName());
				}
				waitingProcessInfoEntity.setExecutionId(execution.getId());
				waitingProcessInfoEntity.setProcessInstanceId(execution.getProcessInstanceId());
				waitingProcessInfoEntity.setStageCode(stageCode);
				waitingProcessInfoRepository.save(waitingProcessInfoEntity);
				if (!execution.getVariables().containsKey("linkedServiceTask")) {
					orderStatusManager.processStatusUpdates(orderFlowContext, linkedActivityId, "WAIT_EVENT", false, null);
				}
				paymentNotification(execution.getCurrentActivityId(), orderFlowContext);
			} else if (StringUtils.equals(ExecutionListener.EVENTNAME_END, execution.getEventName())) {
				if (execution.getVariable("waitEventEntityId") != null) {
					log.info("deleting the waiting process info for orderId : {} and subOrderId : {}",
							orderFlowContext.getOrder().getOrderId(), subOrderId);
					waitingProcessInfoRepository
							.deletebySeqId(Long.parseLong((String) execution.getVariable("waitEventEntityId")));
				} else
					log.info("no information available for the waiting process info entity id");
				if(execution.getVariable("skipEnabled")!=null && execution.getVariable("skipEnabled").equals(true))
				{
					log.info("skipping the order status update from execution listener as the request is a part of skip operation ");
					execution.removeVariable("skipEnabled");
					return;
				}
				orderStatusManager.processStatusUpdates(orderFlowContext, linkedActivityId, "WAIT_EVENT", true, null);
			}
		} catch (Exception e) {
			log.info(" Exception occurred in SignalExecutionListener.notify", e);
			throw e;
		}
	}

	private void paymentNotification(String eventName, OrderFlowContext orderFlowContext) {
		if (StringUtils.isNotEmpty(eventName)) {
			if (StringUtils.equalsIgnoreCase(eventName, "POSCallBack"))
				notificationUtils.sendNotification(orderFlowContext, "PAYMENT_NOTIFICATION", "ORDER");
			else if (StringUtils.equalsIgnoreCase(eventName, "ERPCallBack"))
				notificationUtils.sendNotification(orderFlowContext, "ERP_PAYMENT_NOTIFICATION", "ORDER");

		}
	}
}