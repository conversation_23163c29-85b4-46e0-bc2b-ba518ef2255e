
package in.co.sixdee.bss.com.orderorchestrator.config.notification;

import in.co.sixdee.bss.com.orderorchestrator.config.util.Queue;
import in.co.sixdee.bss.om.model.dto.notification.NotificationDTO;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ThreadInitiator {

    public static Queue<NotificationDTO> notificationQueue = null;

    public static NotificationThreadPool notificationThreadPool = null;

    @Bean
    public NotificationThreadPool getNotificationThreadPool() {
        int maxConnectionsNotifyThread = 1;
        notificationQueue = new Queue<NotificationDTO>("notificationQueue", -1, false);
        notificationThreadPool = new NotificationThreadPool(notificationQueue, maxConnectionsNotifyThread, "notificationQueue");
        return notificationThreadPool;
    }

    //@PostConstruct
    //public void initiateStatusCheckerThreads() {
        //int maxConnectionsNotifyThread = 1;
        //notificationQueue = new Queue<NotificationDTO>("notificationQueue", -1, false);
        //notificationThreadPool = new NotificationThreadPool(notificationQueue, maxConnectionsNotifyThread, "notificationQueue");
   // }

}
