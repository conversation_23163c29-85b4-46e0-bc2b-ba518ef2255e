package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class RestCallFailureException extends RuntimeException {

	private static final long	serialVersionUID	= 933382937208867189L;
	private int					errorCode;
	private String				errorDetail;
	private String				errorSystem;

	public RestCallFailureException() {

	}

	public RestCallFailureException(String message) {
		super(message);
	}

	public RestCallFailureException(int errorCode, String message) {
		super(message);
		setErrorCode(errorCode);
	}

	public RestCallFailureException(Throwable cause) {
		super(cause);
	}

	public RestCallFailureException(int errorCode, Throwable cause) {
		super(cause);
		setErrorCode(errorCode);
	}

	public RestCallFailureException(String message, Throwable cause) {
		super(message, cause);
	}

	public RestCallFailureException(int errorCode, String message, Throwable cause) {
		super(message, cause);
		setErrorCode(errorCode);
	}

	public RestCallFailureException(int errorCode, String message, String errorDetail, String errorSystem) {
		super(message);
		setErrorCode(errorCode);
		setErrorDetail(errorDetail);
		setErrorSystem(errorSystem);

	}
}
