package in.co.sixdee.bss.com.orderorchestrator.config.camunda.spin;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants.WorkFlowProcessVariables;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Service;
import jakarta.validation.constraints.NotNull;

public class ProcessVars {

	/*public static VariableFactory<GenericDTO> genericDTO = customVariable(
			WorkFlowProcessVariables.WORKFLOW_DATA.toString(), GenericDTO.class);*/

	public static VariableFactory<OrderFlowContext> orderFlowContext = customVariable(
			WorkFlowProcessVariables.WORKFLOW_DATA.toString(), OrderFlowContext.class);

	public static VariableFactory<Service> executionData = customVariable("executionData",
			Service.class);

	public static VariableFactory<Boolean> subOrderExecStart = customVariable("subOrderExecStart", Boolean.class);

	public static VariableFactory<String> subOrderId = customVariable("subOrderId", String.class);

	public static VariableFactory<String> serviceImplementation = customVariable(
			"serviceImplementation", String.class);


	public static <T> VariableFactory<T> customVariable(@NotNull String variableName, @NotNull Class<T> clazz) {
		return new BasicVariableFactory<>(variableName, clazz);
	}

}