package in.co.sixdee.bss.com.orderorchestrator.service;

import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.engine.RuntimeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.WaitingProcessInfoEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.WaitingProcessInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

import java.util.Collections;

@Service
@RequiredArgsConstructor
@Log4j2
public class SuspendInvokeHandler {

    private final WaitingProcessInfoRepository waitingProcessInfoRepository;

    private final RuntimeService runtimeService;

    public synchronized void invokeSuspendEvent(String orderId, String subOrderId) {
        var waitEventInfo = getWaitEventInfo(orderId, "");
        if (ObjectUtils.isEmpty(waitEventInfo)) {
            log.info(" no suspend wait events are there to proceed");
            return;
        }
        try {
            var signalAvailable = runtimeService.createExecutionQuery().messageEventSubscriptionName("SuspendWait")
                    .processInstanceId(waitEventInfo.getProcessInstanceId()).count() > 0;
            if (signalAvailable) {
                log.info(" invoking suspend event signal for the suborderId :: {}", subOrderId);
                runtimeService.setVariable(waitEventInfo.getExecutionId(), "suspendWaitEventEntityId",
                        String.valueOf(waitEventInfo.getSeqId()));
                runtimeService.createMessageCorrelation(waitEventInfo.getEventName())
                        .processInstanceId(waitEventInfo.getProcessInstanceId()).correlate();
            }

        } catch (Exception e) {
            log.info("Got Exception in {}, so catching it ", this.getClass().getName(), e);
        }

    }

    public WaitingProcessInfoEntity getWaitEventInfo(String orderId, String subOrderId) {
        WaitingProcessInfoEntity waitingProcessInfo = null;
        /*
         * if (StringUtils.isNotEmpty(orderId) && StringUtils.isNotEmpty(subOrderId))
         * waitingProcessInfo =
         * waitingProcessInfoRepository.findProcessInfoByOrderIdAndSubOrderId(orderId, subOrderId,
         * "SuspendWait"); else
         */
        waitingProcessInfo = waitingProcessInfoRepository.findProcessInfoByOrderId(orderId, Collections.singletonList("SuspendWait_order"));
        return waitingProcessInfo;
    }

}
