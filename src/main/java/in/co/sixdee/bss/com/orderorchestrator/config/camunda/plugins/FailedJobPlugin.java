package in.co.sixdee.bss.com.orderorchestrator.config.camunda.plugins;

import org.camunda.bpm.engine.ProcessEngine;
import org.camunda.bpm.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.camunda.bpm.engine.impl.cfg.ProcessEnginePlugin;
import org.camunda.bpm.engine.impl.interceptor.Command;
import org.camunda.bpm.engine.impl.jobexecutor.FailedJobCommandFactory;
import org.springframework.stereotype.Component;

@Component
public class FailedJobPlugin implements ProcessEnginePlugin {

	@Override
	public void preInit(ProcessEngineConfigurationImpl processEngineConfiguration) {
		processEngineConfiguration.setFailedJobCommandFactory(new FailedJobCommandFactory() {
			@Override
			public Command<Object> getCommand(String jobId, Throwable exception) {
				// TODO Auto-generated method stub
				return new FailedJobCmd(jobId, exception);
			}
		});
	}

	@Override
	public void postInit(ProcessEngineConfigurationImpl processEngineConfiguration) {

	}

	@Override
	public void postProcessEngineBuild(ProcessEngine processEngine) {

	}
}
