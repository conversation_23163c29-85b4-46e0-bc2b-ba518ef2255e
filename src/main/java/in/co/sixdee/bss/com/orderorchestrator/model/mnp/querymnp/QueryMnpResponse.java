package in.co.sixdee.bss.com.orderorchestrator.model.mnp.querymnp;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.Data;

import java.io.Serializable;

@XmlRootElement(name = "queryMnpResponse")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class QueryMnpResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	@XmlElement(name = "interactionId")
	private String interactionId;
	@XmlElement(name = "callerIdentity")
	private String callerIdentity;
	@XmlElement(name = "requestDate")
	private String requestDate;
	@XmlElement(name = "serviceIdentifier")
	private String serviceIdentifier;
	@XmlElement(name = "operator")
	private String operator;
	@XmlElement(name = "lineType")
	private String lineType;

}
