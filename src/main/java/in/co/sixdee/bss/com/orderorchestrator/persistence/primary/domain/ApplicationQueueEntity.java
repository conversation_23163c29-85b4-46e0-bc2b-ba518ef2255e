package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "CALLBACK_RETRY_QUEUE")
public class ApplicationQueueEntity implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Column(name = "SEQ_ID", length = 11)
	@Id
	@GeneratedValue(strategy = GenerationType.SEQUENCE)
	private Long seqId;

	@Column(name = "QUEUE")
	@Lob
	private byte[] queue;

	@Column(name = "INSTANCE_ID")
	private String instanceId;

}
