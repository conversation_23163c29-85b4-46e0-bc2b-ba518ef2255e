package in.co.sixdee.bss.com.orderorchestrator.service;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Objects;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.EnrichmentFailedException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.connector.HttpConnector;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.enrichment.OrderEnrichmentContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

@Service
@Log4j2
@RequiredArgsConstructor
public class OrderEnrichment {

	protected final GetDataFromCache	cache;

	protected final ObjectMapper		objectMapper;

	public void enrichOrder(OrderFlowContext orderFlowContext) {
		findEnrichmentType(orderFlowContext);
		if (!checkOrderEnrichmentRequired(orderFlowContext.getEnrichmentType())) {
			log.info("Enrichment not required or configured for the order type " + orderFlowContext.getOrder().getOrderType());
			return;
		}
		var enrichmentContext = new OrderEnrichmentContext();
		enrichmentContext.setTraceId(orderFlowContext.getTraceId());
		enrichmentContext.setEntityId(orderFlowContext.getEntityId());
		enrichmentContext.setOrder(orderFlowContext.getOrder());
		enrichmentContext.setEnrichmentType(orderFlowContext.getEnrichmentType());
		/*if (ObjectUtils.isNotEmpty(orderFlowContext.getAttributes().get(GenericConstants.PLAN_IDS)))
			enrichmentContext.getAttributes().put(GenericConstants.PLAN_IDS,
					orderFlowContext.getAttributes().get(GenericConstants.PLAN_IDS));*/
		String callerName = "order-orchestrator";
		enrichmentContext.setCallerName(callerName);
		enrichmentContext.setEnrichmentResults(orderFlowContext.getEnrichmentResults());
		OrderEnrichmentContext enrichedData = null;
		try {
			enrichedData = objectMapper.readValue(invokeEnrichmentService(enrichmentContext), OrderEnrichmentContext.class);
			enrichOrderFlowContext(orderFlowContext, enrichedData);
			orderFlowContext.setOrderEnriched(true);


		} catch (EnrichmentFailedException e) {
			log.error("exception occurred :: {}", e.getMessage());
			orderFlowContext.setEnrichmentError(true);
			orderFlowContext.setEnrichmentFailureReason(e.getMessage());
			orderFlowContext.setErrorCode(e.getCode());
		} catch (Exception e) {
			log.error("exception occurred :: {}", e.getMessage(), e);
			orderFlowContext.setEnrichmentError(true);
			orderFlowContext.setEnrichmentFailureReason(e.getMessage());
		}

	}

	private void findEnrichmentType(OrderFlowContext orderFlowContext) {
		switch (orderFlowContext.getOrder().getOrderType()) {
			case OrderTypes.CREATE_DOCUMENT:
			case OrderTypes.UPDATE_DOCUMENT:
			case OrderTypes.DELETE_DOCUMENT:
				if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getDocumentDetails())) {
					switch (orderFlowContext.getOrder().getDocumentDetails().getSubscriberLevel()) {
						case "0":
							orderFlowContext.setEnrichmentType(orderFlowContext.getOrder().getOrderType() + "Profile");
							break;
						case "1":
							orderFlowContext.setEnrichmentType(orderFlowContext.getOrder().getOrderType() + "Account");
							break;
						default:
							orderFlowContext.setEnrichmentType(orderFlowContext.getOrder().getOrderType() + "Service");
					}
				}
				break;
			case OrderTypes.CONNECTION_MIGRATION:
				if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder())
						&& ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile())
						&& ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getAccount())
						&& ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getProfile().getAccount().getAccountId())) {

					orderFlowContext.setEnrichmentType(orderFlowContext.getOrder().getOrderType() + "ExistingAccount");
				} else
					orderFlowContext.setEnrichmentType(orderFlowContext.getOrder().getOrderType());
				break;

			case OrderTypes.UPDATE_STARTER_PACK_KYC:
				if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder())
						&& ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getCustomerInfo())
						&& ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getCustomerInfo().getProfileId())) {

					orderFlowContext.setEnrichmentType(orderFlowContext.getOrder().getOrderType() + "ExistingProfile");
				} else
					orderFlowContext.setEnrichmentType(orderFlowContext.getOrder().getOrderType());
				break;
			case OrderTypes.TRANSFER_OF_SERVICE:
				if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder())
						&& ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement())
						&& ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getDestinationProfileId())) {
					orderFlowContext.setEnrichmentType(orderFlowContext.getOrder().getOrderType() + "_" + "ExistingProfile");
				} else if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder())
						&& ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement())
						&& ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getDestinationAccountId())) {
					orderFlowContext.setEnrichmentType(orderFlowContext.getOrder().getOrderType() + "_" + "ExistingAccount");
				} else
					orderFlowContext.setEnrichmentType(orderFlowContext.getOrder().getOrderType());
				break;
			default:
				orderFlowContext.setEnrichmentType(orderFlowContext.getOrder().getOrderType());
				break;
		}
	}

	private boolean checkOrderEnrichmentRequired(String orderType) {
		var enrichmentConfig = cache.getCacheDetailsFromDBMap("COM_ORCHESTRATOR_ORDER_ENRICHMENT_CONFIG", orderType);
		if (enrichmentConfig == null)
			return false;
		else
			return BooleanUtils.toBoolean(enrichmentConfig.getNgTableData().get("ENRICHMENT_REQD"));
	}

	protected void enrichOrderFlowContext(OrderFlowContext orderFlowContext, OrderEnrichmentContext enrichedData) {
		if (enrichedData != null) {
			if (enrichedData.getAttributes() != null) {
				orderFlowContext.getAttributes().putAll(enrichedData.getAttributes());
			}
			orderFlowContext.setOrder(enrichedData.getOrder());
			orderFlowContext.setEnrichmentError(enrichedData.isError());
			if (orderFlowContext.getEnrichmentResults() != null)
				orderFlowContext.getEnrichmentResults().putAll(enrichedData.getEnrichmentResults());
			else
				orderFlowContext.setEnrichmentResults(enrichedData.getEnrichmentResults());
			if(ObjectUtils.isNotEmpty(enrichedData.getPlanIdPlanCodeMap()))
				orderFlowContext.setPlanIdPlanCodeMap(enrichedData.getPlanIdPlanCodeMap());
			if(ObjectUtils.isNotEmpty(enrichedData.getPlanIdPlanAllowedQuantity()))
				orderFlowContext.setPlanIdPlanAllowedQuantity(enrichedData.getPlanIdPlanAllowedQuantity());
			if (enrichedData.isError()) {
				orderFlowContext.setEnrichmentFailureReason(enrichedData.getMessage());
				orderFlowContext.setErrorCode(enrichedData.getCode());
			}
		} else {
			orderFlowContext.setEnrichmentError(true);
		}
	}

	public String invokeEnrichmentService(OrderEnrichmentContext enrichmentContext)
			throws JsonProcessingException, URISyntaxException {
		HashMap<String, String> enrichmentUrlDetails = getEnrichmentServiceUrlDetails();
		var callThirdPartyDTO = HttpConnector.service(setupCallThirdPartyDto(enrichmentUrlDetails, enrichmentContext));
		if (callThirdPartyDTO.isError()) {
			throw new EnrichmentFailedException(callThirdPartyDTO.getResponseCode(),
					"Failed to enrich the order. error while invoking the enrichment service. "
							+ callThirdPartyDTO.getResponseMessage());
		}
		return callThirdPartyDTO.getResponse();
	}

	public CallThirdPartyDTO setupCallThirdPartyDto(HashMap<String, String> enrichmentUrlDetails,
			OrderEnrichmentContext enrichmentContext) throws JsonProcessingException, URISyntaxException {
		var callThirdPartyDTO = new CallThirdPartyDTO();
		var headers = new HashMap<String, String>();
		headers.put("Content-Type", "application/json");
		headers.put("channel", "com-order-orchestrator");
		headers.put("X-Trace-Id", enrichmentContext.getTraceId());
		headers.put("username", "admin");
		headers.put("entityId", enrichmentContext.getEntityId());
		headers.put("request-id",enrichmentContext.getOrder().getOrderId());
		callThirdPartyDTO.setHeaderMap(headers);
		callThirdPartyDTO.setRespPayloadValidationRqd(BooleanUtils.toBoolean(enrichmentUrlDetails.get("PAYLOAD_VALIDATION")));
		callThirdPartyDTO.setThirdPartyId(enrichmentUrlDetails.get("THIRD_PARTY_ID"));
		callThirdPartyDTO.setUri(new URI(enrichmentUrlDetails.get("URL")));
		callThirdPartyDTO.setRequest(objectMapper.writeValueAsString(enrichmentContext));
		callThirdPartyDTO.setReadTimeout(NumberUtils.toInt(enrichmentUrlDetails.get("READ_TIMEOUT"), 3000));
		callThirdPartyDTO.setConnTimeout(NumberUtils.toInt(enrichmentUrlDetails.get("CONNECTION_TIMEOUT"), 3000));
		callThirdPartyDTO
				.setTransportMethod(CallThirdPartyDTO.TransportMethod.valueOf(enrichmentUrlDetails.get("TRANSPORT_METHOD")));
		return callThirdPartyDTO;
	}

	public HashMap<String, String> getEnrichmentServiceUrlDetails() {
		var thirdPartyUrlConfig = cache.getCacheDetailsFromDBMap("COM_THIRD_PARTY_URL_CONFIG", "order-enrichment");
		if (Objects.isNull(thirdPartyUrlConfig))
			throw new CommonException("COM_THIRD_PARTY_URL_CONFIG is not configured properly");
		return thirdPartyUrlConfig.getNgTableData();
	}
}