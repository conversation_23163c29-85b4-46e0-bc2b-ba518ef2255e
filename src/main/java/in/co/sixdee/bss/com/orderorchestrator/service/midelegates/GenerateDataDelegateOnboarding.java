package in.co.sixdee.bss.com.orderorchestrator.service.midelegates;

import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderPayloadService;
import in.co.sixdee.bss.com.orderorchestrator.service.ProcessBatchConfig;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.ServiceGroups;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.camunda.bpm.engine.variable.Variables;
import org.camunda.bpm.engine.variable.value.ObjectValue;
import org.springframework.stereotype.Service;

import com.bazaarvoice.jolt.JsonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Log4j2
public class GenerateDataDelegateOnboarding implements JavaDelegate {

    private final ProcessDataAccessor processDataAccessor;

    private final OrderPayloadService orderPayloadService;

    private static final int STREAM_THRESHOLD = 10000;


    @Override
    public void execute(DelegateExecution execution) throws Exception {

        OrderFlowContext orderFlowContext = processDataAccessor.getOrderFlowContext(execution);
        var payloadOrderFlowContext = orderPayloadService.getOrderFlowContext(orderFlowContext.getOrder().getOrderId());
        List<String> subOrderIds = null;
        if (payloadOrderFlowContext != null) {
            subOrderIds = collectSubOrderIds(payloadOrderFlowContext);
        } else {
            // for older orders
            subOrderIds = collectSubOrderIds(orderFlowContext);
        }

        ObjectValue recordsObj = Variables.objectValue(subOrderIds)
                .serializationDataFormat(Variables.SerializationDataFormats.JSON).create();

        execution.setVariable("records", recordsObj);
        execution.setVariable("createdInstancesCount", 0);
        execution.setVariable("totalInstances", subOrderIds.size());

    }


    public List<String> collectSubOrderIds(OrderFlowContext context) {
        List<ServiceGroups> serviceGroups = context.getOrder().getProfile().getAccount().getServiceGroups();

        int totalServices = serviceGroups.stream()
                .mapToInt(sg -> sg.getServices().size())
                .sum();

        if (totalServices < STREAM_THRESHOLD) {
            return collectWithLoops(serviceGroups);
        } else {
            return collectWithStreams(serviceGroups);
        }
    }


    private List<String> collectWithLoops(List<ServiceGroups> serviceGroups) {
        List<String> subOrderIds = new ArrayList<>();
        for (ServiceGroups sg : serviceGroups) {
            for (in.co.sixdee.bss.om.model.dto.order.Service s : sg.getServices()) {
                subOrderIds.add(s.getSubOrderId());
            }
        }
        return subOrderIds;
    }

    private List<String> collectWithStreams(List<ServiceGroups> serviceGroups) {
        return serviceGroups.stream()
                .flatMap(sg -> sg.getServices().stream())
                .map(in.co.sixdee.bss.om.model.dto.order.Service::getSubOrderId)
                .toList();
    }

}
