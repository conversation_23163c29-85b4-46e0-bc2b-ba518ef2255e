package in.co.sixdee.bss.com.orderorchestrator.service.handlers.mnp;

import in.co.sixdee.bss.com.orderorchestrator.config.util.MNPServiceUtil;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.AppConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Log4j2
@Component(value = "mnpChangeProductDateCalculator")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class MNPChangeProductDateCalculator extends AbstractDelegate {

    private final MNPServiceUtil mnpServiceUtil;

    @Override
    protected void execute() throws Exception {

        String scheduledTime = calculateChangeProductDateTime();
        execution.setVariable(AppConstants.ProcessVariables.MNP_CHANGE_PRODUCT_DATE.value(), scheduledTime);
        executionContext.getAttributes().put(AppConstants.ProcessVariables.MNP_CHANGE_PRODUCT_DATE.value(), scheduledTime);
        workflowDataUpdated = true;
    }

    public String calculateChangeProductDateTime() {
        String changeProductHour = MNPConstants.MNP_PORTIN_INTERNAL_CHANGE_PRODUCT_HOUR;
        CacheTableDataDTO changeProductHourConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.MNP_PORTIN_INTERNAL_CHANGE_PRODUCT_HOUR.toString());
        try {
            if (changeProductHourConfig != null) {
                changeProductHour = changeProductHourConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
            }
        } catch (Exception e) {
            log.error("Exception occurred while converting MNP Changeproduct hour configuration. considering default value", e);
        }
        LocalDateTime changeProductDateTime = mnpServiceUtil.findTargetDateTime(changeProductHour);
        log.info("Target time for change product status {}", changeProductDateTime);
        return changeProductDateTime.toString();
    }


    public LocalDateTime findTargetDate(int targetHour) {
        LocalDateTime currentDateTime = LocalDateTime.now();
        LocalTime targetTime = LocalTime.of(targetHour, 0);
        LocalDateTime targetDateTime = currentDateTime.with(targetTime);
        if (currentDateTime.isAfter(targetDateTime)) {
            targetDateTime = targetDateTime.plusDays(1);
        }
        return targetDateTime;
    }
}
