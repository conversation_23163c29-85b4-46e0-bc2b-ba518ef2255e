package in.co.sixdee.bss.com.orderorchestrator.service.handlers.mnp;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.WorkflowTaskFailedException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

//This class is for finding the Owner & Recipient Operator details

@Log4j2
@Component(value = "portInOperatorFinder")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class PortInOperatorFinder extends AbstractDelegate {

    private final MNPAttributeService mnpAttributeService;

    @Override
    protected void execute() throws Exception {

        String operatorDataConfigTable = CacheConstants.CacheKeys.COM_OPERATOR_DATA_CONFIG.toString();
        ArrayList<CacheTableDataDTO> operatorDetails = cache.getCacheDetailsFromDBList(operatorDataConfigTable);
        try {
            if (operatorDetails != null) {
                String serviceId = null;
                if (OrderTypes.MNP_PORT_IN.equals(orderType))
                    serviceId = executionContext.getOrder().getProfile().getAccount().getServiceGroups().get(0).getServices().get(0).getServiceId();
                else if (OrderTypes.INTERIM_NUMBER_PORT_IN.equals(orderType))
                    serviceId = executionContext.getOrder().getServiceManagement().getNewMsisdn();

                if (!findDonorOperatorDetails(executionContext.getAttributes().get(MNPConstants.DONOR_OPERATOR_NAME)) ||
                        !findRecipientOperator(operatorDetails)) {
                    throw new WorkflowTaskFailedException(activityId, "COM-005", "Unable to load operator details");
                } else {
                    findOwnerOperator(serviceId, operatorDetails);
                    workflowDataUpdated = true;
                }

            } else {
                log.info("Unable to load configuration for operator details. please check if the data correctly configured in {} table", operatorDataConfigTable);
            }
            mnpAttributeService.saveOperatorCodesInAttributes(executionContext);

        } catch (Exception e) {
            log.error("Exception while executing logic for finding operator details", e);
            executionContext.setError(true);
        }

    }

    private boolean findDonorOperatorDetails(String operator) {
        log.info("operator from query mnp response" +operator);
        CacheTableDataDTO operatorDetails = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_OPERATOR_DATA_CONFIG_BY_OP_NAME.toString(), operator);
        if (operatorDetails != null) {
            Map<String, String> operatorDetailsMap = operatorDetails.getNgTableData();
            executionContext.getAttributes().put(MNPConstants.DONOR_OPERATOR_CODE, operatorDetailsMap.get(CacheConstants.CacheFields.OPERATOR_CODE.toString()));
            executionContext.getAttributes().put(MNPConstants.DONOR_OPERATOR_NAME, operatorDetailsMap.get(CacheConstants.CacheFields.OPERATOR_NAME.toString()));
            executionContext.getAttributes().put(MNPConstants.DONOR_OPERATOR_ROUTE_NUMBER, operatorDetailsMap.get(CacheConstants.CacheFields.ROUTE_NUMBER.toString()));
            return true;
        }
        return false;
    }

    public boolean findOwnerOperator(String serviceId, ArrayList<CacheTableDataDTO> operatorDetails) { // find the owner using the msisdn range.
        boolean operatorFound = false;
        int first4Digits = NumberUtils.toInt(serviceId.substring(0, 4));
        for (CacheTableDataDTO operatorInfo : operatorDetails) {
            String msisdnRanges = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.MSISDN_RANGE.toString());
            if (msisdnRanges != null) {
                // an operator may have multiple msisdn ranges
                String[] rangeList = msisdnRanges.split(GenericConstants.OPERATOR_COMMA);
                for (String range : rangeList) {
                    String[] bounds = range.split(GenericConstants.OPERATOR_HIFEN);
                    if (bounds.length == 2) {
                        int lowerBound = NumberUtils.toInt(bounds[0]);
                        int upperBound = NumberUtils.toInt(bounds[1]);
                        if (first4Digits >= lowerBound && first4Digits <= upperBound) {
                            String operatorCode = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.OPERATOR_CODE.toString());
                            String operatorName = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.OPERATOR_NAME.toString());
                            String routeNumber = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.ROUTE_NUMBER.toString());
                            executionContext.getAttributes().put(MNPConstants.OWNER_OPERATOR_CODE, operatorCode);
                            executionContext.getAttributes().put(MNPConstants.OWNER_OPERATOR_NAME, operatorName);
                            executionContext.getAttributes().put(MNPConstants.OWNER_OPERATOR_ROUTE_NUMBER, routeNumber);
                            log.info("Found owner operator {}",operatorName);
                            operatorFound = true;
                            return operatorFound;
                        }
                    } else {
                        log.info("Invalid range configuration. skipping the range {}", range);
                    }
                }
            } else {
                log.info("Unable to find msisdn range configuration for owner operator {}", operatorInfo.getNgTableData().get(CacheConstants.CacheFields.OPERATOR_NAME.toString()));
            }

        }
        return operatorFound;

    }


    private boolean findRecipientOperator(ArrayList<CacheTableDataDTO> operatorDetails) {  //find the recipient using the entityId got in request.
        boolean operatorFound = false;
        String entityId = executionContext.getEntityId();
        if (entityId != null) {
            for (CacheTableDataDTO operatorInfo : operatorDetails) {
                String mvnoIds = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.MVNO_ID.toString());
                if (mvnoIds != null) {
                    List<String> mvnoList = Arrays.asList(mvnoIds.split(GenericConstants.OPERATOR_COMMA));
                    if (mvnoList.contains(entityId)) {
                        String operatorCode = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.OPERATOR_CODE.toString());
                        String operatorName = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.OPERATOR_NAME.toString());
                        String routeNumber = operatorInfo.getNgTableData().get(CacheConstants.CacheFields.ROUTE_NUMBER.toString());
                        executionContext.getAttributes().put(MNPConstants.RECIPIENT_OPERATOR_CODE, operatorCode);
                        executionContext.getAttributes().put(MNPConstants.RECIPIENT_OPERATOR_NAME, operatorName);
                        executionContext.getAttributes().put(MNPConstants.RECIPIENT_OPERATOR_ROUTE_NUMBER, routeNumber);
                        operatorFound = true;
                        break;
                    }
                } else {
                    log.info("Unable to find mvno id details for Recipient operator {}", operatorInfo.getNgTableData().get(CacheConstants.CacheFields.OPERATOR_NAME.toString()));
                }
            }
        } else {
            log.info("entity id is null, unable to find Recipient operator details");
        }
        if (!operatorFound) {
            log.info("Unable to find Recipient operator details from {} table.", CacheConstants.CacheKeys.COM_OPERATOR_DATA_CONFIG.toString());
        }
        return operatorFound;
    }
}
