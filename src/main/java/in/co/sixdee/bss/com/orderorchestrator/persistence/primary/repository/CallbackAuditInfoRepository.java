
package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.CallBackAuditInfoEntity;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface CallbackAuditInfoRepository extends JpaRepository<CallBackAuditInfoEntity, Long> {

    @SuppressWarnings("unchecked")
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public CallBackAuditInfoEntity save(CallBackAuditInfoEntity entity);

    @Transactional
    @Modifying
    @Query("UPDATE CallBackAuditInfoEntity o SET o.isProcessed = :isProcessed , o.version = o.version+1, o.lockExpTime=:lockExpTime where o.orderId = :orderId and o.subOrderId = :subOrderId and o.version=:version and o.callbackType=:callbackType")
    int updateIsProcessedToPicked(@Param("isProcessed") String isProcessed, @Param("version") int version, @Param("lockExpTime") Date lockExpTime, @Param("orderId") Long orderId, @Param("subOrderId") Long subOrderId, String callbackType);

    @Transactional
    @Modifying
    @Query("UPDATE CallBackAuditInfoEntity o SET o.isProcessed = :isProcessed , o.version = o.version+1, o.lockExpTime=:lockExpTime where o.orderId = :orderId and o.version=:version and o.callbackType=:callbackType")
    int updateIsProcessedToPickedWithOrderId(@Param("isProcessed") String isProcessed, @Param("version") int version, @Param("lockExpTime") Date lockExpTime, @Param("orderId") Long orderId, String callbackType);

    @Query("SELECT om from CallBackAuditInfoEntity om where ( om.isProcessed = '0' and (om.lockExpTime IS NULL OR om.lockExpTime < now ()) and om.remainingRetries > 0) ORDER BY om.createdDate ASC")
    public List<CallBackAuditInfoEntity> getDataforCallBackScheduler(Pageable page);

    @Query("SELECT om from CallBackAuditInfoEntity om where om.isProcessed = :isProcessed  and om.orderId = :orderId and om.subOrderId = :subOrderId and om.callbackType = :callbackType")
    public CallBackAuditInfoEntity getDetailsForCallBackListener(@Param("isProcessed") String isProcessed, @Param("orderId") Long orderId, @Param("subOrderId") Long subOrderId, @Param("callbackType") String callbackType);

    @Query("SELECT om from CallBackAuditInfoEntity om where om.isProcessed = :isProcessed  and om.orderId = :orderId and om.callbackType = :callbackType")
    public CallBackAuditInfoEntity getDetailsForCallBackListenerByOrderId(@Param("isProcessed") String isProcessed, @Param("orderId") Long orderId,@Param("callbackType") String callbackType);

    @Transactional
    @Modifying
    @Query("UPDATE CallBackAuditInfoEntity o SET o.isProcessed = :isProcessed  where o.orderId = :orderId and o.subOrderId=:subOrderId and o.callbackType = :callbackType")
    int updateForCallBackListener(@Param("isProcessed") String isProcessed, @Param("orderId") Long orderId, @Param("subOrderId") Long subOrderId,@Param("callbackType") String callbackType);

    @Transactional
    @Modifying
    @Query("UPDATE CallBackAuditInfoEntity o SET o.isProcessed = :isProcessed  where o.orderId = :orderId and o.callbackType = :callbackType")
    int updateForCallBackListenerByOrderId(@Param("isProcessed") String isProcessed, @Param("orderId") Long orderId,@Param("callbackType") String callbackType);

    @Transactional
    @Modifying
    @Query("UPDATE CallBackAuditInfoEntity o SET o.isProcessed = :isProcessed , o.remainingRetries=:remainingRetries, o.version=:version , o.lockExpTime=:lockExpTime where o.orderId = :orderId and o.subOrderId=:subOrderId")
    void updateAuditEntryForCallBack(@Param("isProcessed") String isProcessed, @Param("remainingRetries") int remainingRetries,@Param("version") int version, @Param("lockExpTime") Date lockExpTime, @Param("orderId") Long orderId,@Param("subOrderId") Long subOrderId);

    @Transactional
    @Modifying
    @Query("UPDATE CallBackAuditInfoEntity o SET o.isProcessed = :isProcessed, o.remainingRetries=:remainingRetries , o.version=:version , o.lockExpTime=:lockExpTime where o.orderId = :orderId")
    void updateAuditEntryForCallBackOrderId(@Param("isProcessed") String isProcessed, @Param("remainingRetries") int remainingRetries,@Param("version") int version, @Param("lockExpTime") Date lockExpTime, @Param("orderId") Long orderId);
}
