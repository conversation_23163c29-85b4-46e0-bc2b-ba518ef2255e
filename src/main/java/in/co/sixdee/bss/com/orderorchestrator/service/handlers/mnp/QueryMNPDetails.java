package in.co.sixdee.bss.com.orderorchestrator.service.handlers.mnp;


import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.util.SOAPMessageUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.XmlRequestBuilder;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.model.mnp.querymnp.QueryMnpResponse;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.common.util.CommonUtils;
import jakarta.xml.soap.SOAPMessage;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * \
 * This class will call Singtel MNP System over and http API (XML) and extract the donor operator details
 * from the response.
 * 
 * Note: Will be executed only if msisdn does'nt exist in BS System
 */
@Log4j2
@Component(value = "queryMnpDetailsXML")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class QueryMNPDetails extends AbstractDelegate {


    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    XmlRequestBuilder xmlRequestBuilder;

    @Autowired
    SOAPMessageUtils soapMessageUtils;
    
    @Autowired
    MNPAttributeService mnpAttributeService;

    @Override
    protected void execute() throws Exception {

        String request = xmlRequestBuilder.buildRequest(orderType+"_QueryMNP", executionContext,null);
        if (CommonUtils.INSTANCE.validateField(request)) {
            CallThirdPartyDTO callThirdPartyDTO = callThirdParty(request);
            if (callThirdPartyDTO == null) {
                executionContext.setError(true);
                return;
            }
//            var response = callThirdPartyDTO.getResponse();
            var response = "{\n"
            		+ "  \"operator\" : \"Singtel\",\n"
            		+ "  \"lineType\" : \"Postpaid\"\n"
            		+ "}";
            if (validateQueryMnpResponse(response)) {
                log.info("Successfully validated Query MNP response, extracting operator details");
                if(!parseQueryMNPResponse(response)){
                    log.error("Error while extracting operator details from Query MNP response. setting error and stopping the flow");
                    executionContext.getErrorDetail().setCode("COM-005");
                    executionContext.getErrorDetail().setMessage("Unable to extract operator details from Query MNP response");
                    executionContext.getErrorDetail().setSystem("COM");
                    executionContext.setError(true);
                }
            }
        } else {
            log.error("Exception occurred. unable to form the tp request");
            executionContext.getErrorDetail().setCode("COM-001");
            executionContext.getErrorDetail().setMessage("Internal Error: error in forming tp request");
            executionContext.getErrorDetail().setSystem("COM");
            executionContext.setError(true);
        }
            workflowDataUpdated=true;
    }


    public boolean validateQueryMnpResponse(String response) {
        boolean isValidResponse = true;
        if (StringUtils.containsAny(response, "Fault", "Service Temporarily Unavailable")) {
            log.info("Getting Fault Response from MNP System. proceeding with incoming Operator !!!");
            isValidResponse = false;
            findOperatorByMvno();
        } else if (response == null || response.isEmpty()) {
            log.info("No response received from MNP System. proceeding with incoming Operator !!!");
            findOperatorByMvno();
            isValidResponse = false;
        }
        return isValidResponse;
    }

    public boolean parseQueryMNPResponse(String response) {
        boolean isParsingSuccess = true;
        try {
            SOAPMessage soapMessage = soapMessageUtils.getSOAPMessageFromXMLString(response, null, null);
            QueryMnpResponse queryMnpResponse = soapMessageUtils.unmarshall(soapMessage, QueryMnpResponse.class);
            if (StringUtils.isNotEmpty(queryMnpResponse.getOperator())) {
                executionContext.getAttributes().put(MNPConstants.DONOR_OPERATOR_NAME, queryMnpResponse.getOperator());
                findOperatorCodeByName(queryMnpResponse.getOperator());
                mnpAttributeService.createAndSaveMNPAttribute(MNPConstants.DONOR_OPERATOR_CODE, executionContext.getAttributes().get(MNPConstants.DONOR_OPERATOR_CODE), executionContext);
            }
            if (StringUtils.isNotEmpty(queryMnpResponse.getLineType())) {
                executionContext.getAttributes().put(MNPConstants.MNP_LINE_TYPE, queryMnpResponse.getLineType());
            }
        } catch (Exception e) {
            isParsingSuccess = false;
            log.error("Exception occurred while parsing Query MNP Response", e);
        }
        return isParsingSuccess;
    }
    
    
    private boolean findOperatorByMvno() { // if QueryMnp was failure, find the donor operator (MNO) using the operatorname got in request (MVNO)
    	log.info("Finding MNO operator using the incoming operator (MVNO)");
        boolean isOperatorFound = false;
        String operatorInRequest = executionContext.getAttributes().get(GenericConstants.OPERATOR); // donor operator got in OM request
        CacheTableDataDTO operatorConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_MVNO_MNO_MAPPING_CONFIG.toString(), operatorInRequest);
        if (operatorConfig != null) {
            String operator = operatorConfig.getNgTableData().get(CacheConstants.CacheFields.MNO.toString());
            log.info("Operator name {} found from configuration table {} ", operator, CacheConstants.CacheKeys.COM_MVNO_MNO_MAPPING_CONFIG.toString());
            if (operator != null && !operator.isEmpty()) {
                executionContext.getAttributes().put(MNPConstants.DONOR_OPERATOR_NAME, operator);
                if(findOperatorCodeByName(operator));
                isOperatorFound = true;
            }
            
        }
        return isOperatorFound;
    }
    
    private boolean findOperatorCodeByName(String operatorName) { 
    	log.info("Finding Operator Code by Operator Name:{} from:{} table",operatorName,CacheConstants.CacheKeys.COM_OPERATOR_DATA_CONFIG.toString());
        boolean isOperatorFound = false;
        CacheTableDataDTO operatorConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_OPERATOR_DATA_CONFIG_BY_OP_NAME.toString(), operatorName);
        if (operatorConfig != null) {
            String operatorCode = operatorConfig.getNgTableData().get(CacheConstants.CacheFields.OPERATOR_CODE.toString());
            log.info("Operator code: {} found from configuration table", operatorCode);
            if (operatorCode != null && !operatorCode.isEmpty()) {
                executionContext.getAttributes().put(MNPConstants.DONOR_OPERATOR_CODE, operatorCode);
                executionContext.getAttributes().put(MNPConstants.DONOR_ENTITY_ID, operatorConfig.getNgTableData().get(CacheConstants.CacheFields.MVNO_ID.toString()));
                isOperatorFound = true;
            }
            else
            	log.info("No Operator code configured for operatorName {}",operatorName);
            
        }
        else
        	log.info("No entry found for operatorName {}",operatorName);
        return isOperatorFound;
    }
}
