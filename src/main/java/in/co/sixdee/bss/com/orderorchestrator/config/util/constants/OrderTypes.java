package in.co.sixdee.bss.com.orderorchestrator.config.util.constants;

public class OrderTypes {

	public static final String	ONBOARDING					= "Onboarding";
	public static final String	ACCOUNT_CREATION			= "AccountCreation";
	public static final String	ADD_SUBSCRIPTION			= "AddSubscription";
	public static final String	CHANGE_SUBSCRIPTION			= "ChangeSubscription";
	public static final String	CANCEL_SUBSCRIPTION			= "CancelSubscription";
	public static final String	SUSPEND_SERVICE				= "SuspendService";
	public static final String	RESUME_SERVICE				= "ResumeService";
	public static final String	TERMINATE_SERVICE			= "TerminateService";
	public static final String	CHANGE_RISK_CATEGORY		= "ChangeRiskCategory";
	public static final String	CREATE_ADJUSTMENT			= "CreateAdjustment";
	public static final String	CREATE_SAFE_CUSTODY			= "CreateSafeCustody";
	public static final String	UPDATE_SAFE_CUSTODY			= "UpdateSafeCustody";
	public static final String	CANCEL_SAFE_CUSTODY			= "CancelSafeCustody";
	public static final String	MAKE_PAYMENT				= "MakePayment";
	public static final String	CHANGE_MSISDN				= "ChangeMsisdn";
	public static final String	TRANSFER_OF_OWNERSHIP		= "TransferOfOwnership";
	public static final String	TRANSFER_OF_SERVICE			= "TransferOfService";
	public static final String	CHANGE_SIM					= "ChangeSim";
	public static final String	UPDATE_ACCOUNT				= "UpdateAccount";
	public static final String	UPDATE_LANGUAGE				= "UpdateLanguage";
	public static final String	UPDATE_SERVICE				= "UpdateService";
	public static final String	BOOK_DEPOSIT				= "BookDeposit";
	public static final String	RESUME_SUBSCRIPTION			= "ResumeSubscription";
	public static final String	SUSPEND_SUBSCRIPTION		= "SuspendSubscription";
	public static final String	UPDATE_PROFILE				= "UpdateProfile";
	public static final String	CREATE_PROFILE				= "CreateProfile";
	public static final String	UPDATE_IDENTIFICATION		= "UpdateIdentification";
	public static final String	DELETE_IDENTIFICATION		= "DeleteIdentification";
	public static final String	UPDATE_PROFILE_ADDRESS		= "UpdateProfileAddress";
	public static final String	UPDATE_ACCOUNT_ADDRESS		= "UpdateAccountAddress";
	public static final String	UPDATE_SERVICE_ADDRESS		= "UpdateServiceAddress";
	public static final String	EXTEND_SUBSCRIBER_VALIDITY	= "ExtendSubscriberValidity";
	public static final String	ADJUST_MAIN_ACCOUNT			= "AdjustMainAccount";
	public static final String	UPDATE_STARTER_PACK_KYC		= "UpdateStarterPackKYC";
	public static final String	CONNECTION_MIGRATION		= "ConnectionMigration";
	public static final String	CREATE_DOCUMENT				= "CreateDocument";
	public static final String	UPDATE_DOCUMENT				= "UpdateDocument";
	public static final String	DELETE_DOCUMENT				= "DeleteDocument";
	public static final String	ADD_SERVICE					= "AddService";
	public static final String	ADD_SERVICE_TO_NEW_ACCOUNT	= "AddServiceToNewAccount";
	public static final String	GIFTING						= "Gifting";
	public static final String	LIFECYCLESYNC_TERMINATION	= "LifecycleSyncTermination";
	public static final String	STARTERPACK_PROVISIONING	= "StarterPackProvisioning";
	public static final String	NUMBER_RECYCLE				= "NumberRecycle";
	public static final String	MANAGE_HLR_SERVICES			= "ManageHlrServices";
	public static final String	SOFT_BARRING				= "SoftBarring";
	public static final String	HARD_BARRING				= "HardBarring";
	public static final String	SOFT_UNBARRING				= "SoftUnbarring";
	public static final String	HARD_UNBARRING				= "HardUnbarring";
	public static final String	UPDATE_CREDIT_LIMIT			= "UpdateCreditLimit";
	public static final String	CREDIT_REFUND				= "CreditRefund";
	public static final String	LINE_BARRING              	= "LineBarring";
	public static final String	LINE_UNBARRING            	= "LineUnBarring";
	public static final String  ADD_MEMBER_TO_GROUP        	= "AddMemberToGroup";
	public static final String  LIFECYCLESYNC            	= "LifecycleSync";
	public static final String  FREEZE 						= "Freeze";
	public static final String 	CREATE_SB_GROUP				= "CreateSBGroup";
	public static final String 	ADD_SB_BENEFICIARY 			= "AddSBBeneficiary";
	public static final String  ACTIVATE_SERVICE            = "ActivateService";
	public static final String  PORT_IN                     = "PortIn";
	public static final String  PORT_OUT                    = "PortOut";
	public static final String RESTORE_SERVICE              = "RestoreService";
	public static final String  MNP_PORT_IN                 = "MNPPortIn";
	public static final String INTERIM_NUMBER_PORT_IN       = "InterimNumberPortIn";
	public static final String MNP_PORT_OUT					= "MNPPortOut";
	public static final String CREATE_PROFILE_AND_ACCOUNT   = "CreateProfileAndAccount";
	public static final String DELETE_SB_GROUP				= "DeleteSBGroup";
	public static final String UPDATE_SUBSCRIPTION          = "UpdateSubscription";
	public static final String EXTEND_EXPIRY_DATE           = "ExtendExpiryDate";
	private OrderTypes() {

	}

}
