package in.co.sixdee.bss.com.orderorchestrator.config.util.constants;

public class CacheConstants {

	public enum CacheKeys {
		 COM_THIRD_PARTY_URL_CONFIG, COM_THIRD_PARTY_URL_TOKEN_CONFIG, OM_APPLICATION_CONFIG, COM_ORDER_STAGE_CONFIG, PAYLOAD_VALIDATION_CONFIG, STAGE_CONFIG_BY_ACTIVITY_ID_AND_ORDER_TYPE,COM_NCC_WELCOM_SUBSCRIPTION_CONFIG,COM_STARTERPACK_BARRING_CFS_CONFIG,STAGE_CONFIG_BY_ORDER_TYPE_AND_STAGE_ID,COM_WORKFLOW_RETRY_CONFIG,COM_ORDER_PRIORITY_CONFIG, COM_ORDER_PRIORITY_CONFIG_BY_ENTITYID,COM_CANCEL_ORDER_CONFIG, COM_TOPUP_CONFIG_DETAILS, PAYMENT_CHANNEL_FOR_ERP_DEPOSIT, COM_EMAIL_NOTIFICATION_DETAILS_BY_CONNECTION_TYPE_NETWORK_TYPE, COM_LANGUAGE_VALUES_BY_LANGUAGE_ID,
		 REQUEST_PARAM_MAPPING_CONFIG, CONFIG_KEY,COM_OPERATOR_DATA_CONFIG,COM_OPERATOR_DATA_CONFIG_BY_OP_NAME, COM_XML_REQUEST_TEMPLATE_CONFIG,COM_XML_REQUEST_TOKEN_CONFIG, COM_MVNO_MNO_MAPPING_CONFIG,
		MNP_ERROR_CODE_MAPPING,COM_FUTURE_ORDER_CONFIG,COM_MNP_PROPERTY_CONFIG,COM_EMAIL_NOTIFICATION_DETAILS_BY_ENTITY_ID, COM_NSA_SA_PLANID_MAPPING_CONFIG;
	}

	public enum CacheFields {
		JSON_SCHEMA, CONFIG_VALUE, ENABLE_VALIDATION, READ_TIMEOUT, CONNECTION_TIMEOUT, HEADERS, TRANSPORT_METHOD, URL, TOKENS, STAGE, QUERY_PARAMS, RETRY_ENABLE,LINKED_ACTIVITY,PROFILE_TYPE, PAYMENT_CHANNEL,EXPIRY_HOURS,WAITING_REASON, ORDER_ERROR_MESSAGE_LENGTH, DEFAULT_PRIORITY_CONFIG,CALLBACK_AUDIT_STRATEGY, THIRD_PARTY_ID, REQ_SPEC_KEY, RESP_SPEC_KEY, RESPONSE_ATTRIBUTES, UPDATE_KEY, CALLBACK_EVENT, LANGUAGE_VALUE, LMS_LANGUAGE_VALUE
		,MSISDN_RANGE,OPERATOR_CODE,OPERATOR_NAME,MVNO_ID,REQUEST_TEMPLATE,TOKEN_PATH, TOKEN_NAME,MVNO,MNO,ROUTE_NUMBER,MNP_REFERENCE_ID_PREFIX,MNP_ID_TYPE_CORPORATE,
		MNP_ID_TYPE,MNP_CUSTOMER_NAME_REPLACEMENT,ERROR_DESC, MNP_PORTIN_MVNO_MVNO_DEPROV_HOUR,MNP_PORTIN_MVNO_MVNO_PROV_HOUR,PAYMENT_GW_ENABLED_CHANNELS_FOR_MAKE_PAYMENT
		,ORDER_TYPE,FUTURE_ORDER_REQD,DATE_OFFSET_FROM_NEXT_BILL_DATE,SINGAPORE_COUNTRY_CODE,MNP_PORTIN_INTERNAL_CHANGE_PRODUCT_HOUR,MNP_PORTIN_INTERNAL_CONNECT_SERVICE_HOUR,
		VALID_MSISDN_STATUS_IN_OCS,SEVEN_DAYS_POLICY_CHECK_REQD_MVNO,DAYS_REQUIRED_FOR_POLICY_CHECK,NMS_SKIP_CHANNEL_IDS,MNP_INSUFFICIENT_QUOTA_ERROR_CODE,LMP_DB_UPDATE_REQD_FOR_PORTIN_FROM_SINGTEL,ID_VALIDATION_REQD_FOR,PORTOUT_ID_VALIDATION_REQD_MVNOS,CONTRACT_PLAN_IDS,HOUR_VALUE_FOR_FUTURE_DATE_IN_TERMINATE_SERVICE,VALIDATION_REQD_MVNOS_FOR_FUTURE_DATE_IN_TERMINATE_SERVICE,DATASET_NOT_REQD_MVNOS,E_SIM_PRODUCT_ID;

	}

}
