package in.co.sixdee.bss.com.orderorchestrator.service.handlers.mnp;

import in.co.sixdee.bss.com.orderorchestrator.config.ApplicationProperties;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.SubOrderEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.NativeOrderAttributeRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.NativeStageRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.NativeSubOrderRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderStageRepository;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.constants.ApiConstants;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.common.util.CommonUtils;
import in.co.sixdee.bss.common.util.NGTableColumnConstants;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.common.util.SequenceGenerator;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.OrderStateType;
import in.co.sixdee.bss.om.model.dto.order.RelatedParty;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
@Log4j2
@Component(value = "mnpPortoutOrderGenaration")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class MNPPortOutOrderGeneration extends AbstractDelegate {
	protected final NativeOrderAttributeRepository nativeOrderRepository;

	protected final ApplicationProperties applicationProperties;

	protected final NativeStageRepository nativeStageRepository;

	private final OrderStageRepository orderStageService;

	protected final NativeSubOrderRepository nativeSubOrderRepository;
	
	protected String profileId=null;
	protected String accountId=null;
	protected String profileType=null;
	protected String customerName=null;
	protected String firstName=null;
	protected String lastName=null;
	protected String familyName=null;
	protected String givenName=null;
	protected String mnpUserName=null;
	

	@Override
	protected void execute() throws Exception {
		getUserName();
		String orderId = String.valueOf(SequenceGenerator.getSequencerInstance().nextId());
		log.info("OrderId For PortOut Order In MVNO-MVNO PortIn is {}",orderId);
		OrderEntity orderEntity = createOrderEntity(executionContext,Long.parseLong(orderId),"MNPPortOutInMVNO");
		saveOrder(orderEntity);

		OrderEntitiesHolder orderEntities = createSubOrders(orderEntity,executionContext);
		if (applicationProperties.isNativeQueryInsertion()) {
			saveOrderNatively(orderEntities);
		} else {
			saveOrderEntities(orderEntities);
		}

	}
	private void getUserName() {	
        var appConfig = cache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG,
                "USER_ID_FOR_MVNO_MVNO_PORT_OUT_ORDER");
        if (ObjectUtils.isNotEmpty(appConfig)&& ObjectUtils.isNotEmpty(appConfig.getNgTableData().get("CONFIG_VALUE"))) {
        	mnpUserName = appConfig.getNgTableData().get("CONFIG_VALUE");
        }
		
	}
	
	private void saveOrder(OrderEntity orderEntity) {
		var start = Instant.now();
		if (applicationProperties.isNativeQueryInsertion()) {
			nativeOrderRepository.save(orderEntity);
			log.info("Time taken for populating order master using native query={} ms",
					Duration.between(start, Instant.now()).toMillis());
		} else {
			orderRepository.save(orderEntity);
			log.info("Time taken for populating order master using jpa={} ms", Duration.between(start, Instant.now()).toMillis());
		}
	}

	protected void saveOrderEntities(OrderEntitiesHolder orderEntitiesHolder) {
		Instant start = Instant.now();
		if (orderEntitiesHolder == null)
			return;
		subOrderRepository.saveAll(orderEntitiesHolder.getSubOrders());
		orderStageService.saveAll(orderEntitiesHolder.getOrderStages());
		log.info("Time taken for db operations using jpa={} ms", Duration.between(start, Instant.now()).toMillis());
	}

	protected void saveOrderNatively(OrderEntitiesHolder orderEntitiesHolder) {
		Instant start = Instant.now();
		if (orderEntitiesHolder == null)
			return;
		if (orderEntitiesHolder.getSubOrders().stream()
				.anyMatch(subOrder -> ObjectUtils.isNotEmpty(subOrder.getChargingPattern())))
			nativeSubOrderRepository.saveAllSubOrder(orderEntitiesHolder.getSubOrders());
		else
			nativeSubOrderRepository.saveAll(orderEntitiesHolder.getSubOrders());
		nativeStageRepository.saveAll(orderEntitiesHolder.getOrderStages());
		log.info("Time taken for db operations using native query={} ms", Duration.between(start, Instant.now()).toMillis());
	}

	protected OrderEntitiesHolder createSubOrders(OrderEntity orderEntity, OrderFlowContext orderFlowContext) {
		var orderEntitiesHolder = new OrderEntitiesHolder();
		var stageConfigs = cache.getCacheDetailsFromDBMapAryList(NGTableConstants.CACHE_KEY_COM_ORDER_STAGE_CONFIG, "MNPPortOutInMVNO");
		var subOrder = new SubOrderEntity();
		subOrder.setOrderId(orderEntity.getOrderId());
		var subOrderId = SequenceGenerator.getSequencerInstance().nextId();
		subOrder.setId(subOrderId);
		subOrder.setEntityId(orderEntity.getEntityId());
		orderFlowContext.getAttributes().put(GenericConstants.SUB_ORDER_ID, String.valueOf(subOrderId));
		setOrderState(subOrder, OrderStateType.COMPLETED, null);
		subOrder.setServiceId(orderEntity.getServiceId());
		subOrder.setCreatedBy(orderEntity.getCreatedBy());
		subOrder.setUsername(mnpUserName);
		createOrderStages(subOrder, orderEntitiesHolder, orderFlowContext, stageConfigs);
		orderEntitiesHolder.getSubOrders().add(subOrder);
		return orderEntitiesHolder;

	}

	protected void createOrderStages(SubOrderEntity subOrder, OrderEntitiesHolder orderEntitiesHolder,
			OrderFlowContext orderFlowContext, List<CacheTableDataDTO> stageConfigs) {
		if (stageConfigs == null) {
			return;
		}
		for (CacheTableDataDTO stageConfig : stageConfigs) {

			OrderStageEntity orderStage = createStage(orderFlowContext, subOrder, stageConfig);

			orderEntitiesHolder.getOrderStages().add(orderStage);

		}
	}

	private OrderStageEntity createStage(OrderFlowContext orderFlowContext, SubOrderEntity subOrder, CacheTableDataDTO stageConfig) {
		Map<String, String> ngTableData = stageConfig.getNgTableData();
		String stageCode = ngTableData.get(NGTableColumnConstants.COLUMN_STAGE_ID);
		String stageStatus = "Completed";
		return buildOrderStage(subOrder, ngTableData, stageCode, stageStatus, null);
	}

	private OrderStageEntity buildOrderStage(SubOrderEntity subOrder, Map<String, String> ngTableData, String stageCode, String stageStatus, String failureReason) {
		OrderStageEntity stage = new OrderStageEntity();
		stage.setId(in.co.sixdee.bss.common.util.SequenceGenerator.getSequencerInstance().nextId());
		stage.setSubOrderId(subOrder.getId());
		stage.setOrderId(subOrder.getOrderId());
		stage.setExecutionOrder(Integer.valueOf(ngTableData.get(NGTableColumnConstants.COLUMN_EXECUTION_ORDER)));
		stage.setState(stageStatus);
		stage.setStateReason(failureReason);
		stage.setDescription(StringUtils.trim(ngTableData.get(NGTableColumnConstants.COLUMN_STAGE_DESCRIPTION)));
		stage.setStageCode(StringUtils.trim(stageCode));
		stage.setName(StringUtils.trim(ngTableData.get(NGTableColumnConstants.COLUMN_STAGE_NAME)));
		stage.setCreatedBy(subOrder.getCreatedBy());
		stage.setUsername(mnpUserName);
		stage.setEntityId(subOrder.getEntityId());
		return stage;
	}
	private void setOrderState(SubOrderEntity subOrder, Enum<?> state, String stateReason) {
		subOrder.setState(state.toString());
		if (stateReason != null) {
			subOrder.setStateReason(stateReason);
		}
	}


	protected OrderEntity createOrderEntity(OrderFlowContext orderFlowContext,
			Long orderId,String orderType) {
		var orderEntity = new OrderEntity();
		var enrichmentResults = orderFlowContext.getEnrichmentResults();
		var order = orderFlowContext.getOrder();
		orderEntity.setOrderId(orderId);
		orderEntity.setState("Completed");
		orderEntity.setDescription(order.getDescription());
		orderEntity.setExternalId(getExternalId(orderFlowContext));
		orderEntity.setOrderDate(new Date());
		if (order.getRequestedCompletionDate() != null)
			orderEntity.setRequestedCompletionDate(Date.from(Instant.parse(order.getRequestedCompletionDate())));
		orderEntity.setType(orderType);
		orderEntity.setCreatedBy(getCreatedBy(order.getRelatedParty(), orderFlowContext.getUserId()));
		orderEntity.setUsername(mnpUserName);
		orderEntity.setEntityId(executionContext.getAttributes().get(MNPConstants.DONOR_ENTITY_ID));
		orderEntity.setServiceId(findServiceId());
		orderEntity.setChannel(orderFlowContext.getChannel());
		orderEntity.setDescription(orderType);
		if (executionContext.getWorkflowData().containsKey("IdentificationIdValidatorResponseAttributes")) {

			getDetails(orderEntity);
		}

		return orderEntity;
	}

	private void getDetails(OrderEntity orderEntity) {
		Map<String, Object> workflowData = (Map<String, Object>) executionContext.getWorkflowData();
		Optional<Object> nccRespParams = Optional.ofNullable(workflowData.get("IdentificationIdValidatorResponseAttributes"));
		nccRespParams.ifPresent(nccParams -> {
			var paramMap = com.bazaarvoice.jolt.JsonUtils.jsonToMap(com.bazaarvoice.jolt.JsonUtils.toJsonString(nccParams));
			Optional.ofNullable(paramMap.get("accountId")).ifPresent(accountId1 -> accountId = accountId1.toString());
			Optional.ofNullable(paramMap.get("profileId")).ifPresent(profileId1 -> profileId = profileId1.toString());
			Optional.ofNullable(paramMap.get("profileType")).ifPresent(profileType1 -> profileType = profileType1.toString());
			Optional.ofNullable(paramMap.get("firstName")).ifPresent(firstName1 -> firstName = firstName1.toString());
			Optional.ofNullable(paramMap.get("lastName")).ifPresent(lastName1 -> lastName = lastName1.toString());
			Optional.ofNullable(paramMap.get("customerName")).ifPresent(customerName1 -> customerName = customerName1.toString());
			Optional.ofNullable(paramMap.get("familyName")).ifPresent(familyName1 -> familyName = familyName1.toString());
			Optional.ofNullable(paramMap.get("givenName")).ifPresent(givenName1 -> givenName = givenName1.toString());

		});
		if(StringUtils.isNotEmpty(profileId))
		{
			orderEntity.setCustomerId(profileId);
		}
		if(StringUtils.isNotEmpty(accountId))
		{
			orderEntity.setBillingAccountId(accountId);
		}
		if (ObjectUtils.isNotEmpty(profileType)) 
		{
			orderEntity.setCategory(profileType);
		}
		if(ObjectUtils.isNotEmpty(customerName))
		{
			orderEntity.setCustomerName(customerName);
		}
		else if(ObjectUtils.isNotEmpty(firstName) && ObjectUtils.isNotEmpty(lastName))
		{
			orderEntity.setCustomerName(firstName.concat(lastName));
		}
		else if(ObjectUtils.isNotEmpty(familyName) && ObjectUtils.isNotEmpty(givenName))
		{
			orderEntity.setCustomerName(familyName.concat(givenName));
		}

	}
	private void getAccountInfo(OrderEntity orderEntity, Object accountInfoObj) {

		if (accountInfoObj == null) {
			return;
		}

		var accountInfo = objectMapper.convertValue(accountInfoObj, LinkedHashMap.class);

		if (StringUtils.isEmpty(orderEntity.getBillingAccountId())
				&& ObjectUtils.isNotEmpty(accountInfo.get("accountId"))) {
			orderEntity.setBillingAccountId(accountInfo.get("accountId").toString());
		}

		if (StringUtils.isEmpty(orderEntity.getCustomerId())
				&& ObjectUtils.isNotEmpty(accountInfo.get("profileId"))) {
			orderEntity.setCustomerId(accountInfo.get("profileId").toString());
		}

	}
	private void getProfileInfo(OrderEntity orderEntity, Object profileInfoObj) {
		if (profileInfoObj == null) {
			return;
		}

		var profileInfo = objectMapper.convertValue(profileInfoObj, LinkedHashMap.class);

		if (StringUtils.isEmpty(orderEntity.getCustomerId())
				&& ObjectUtils.isNotEmpty(profileInfo.get("profileId"))) {
			orderEntity.setCustomerId(profileInfo.get("profileId").toString());
		}
		orderEntity.setCustomerName(findCustomerName(profileInfo));

		if (ObjectUtils.isNotEmpty(profileInfo.get("profileType"))) {
			orderEntity.setCategory(profileInfo.get("profileType").toString());
		}
	}


	private String findCustomerName(LinkedHashMap profileInfo) 
	{
		// TODO Auto-generated method stub
		if(ObjectUtils.isNotEmpty(profileInfo.get("customerName")))
		{
			return(profileInfo.get("customerName").toString());
		}
		else if(ObjectUtils.isNotEmpty(profileInfo.get("firstName")) && ObjectUtils.isNotEmpty(profileInfo.get("lastName")))
		{
			return(profileInfo.get("firstName").toString().concat(profileInfo.get("lastName").toString()));
		}
		else if(ObjectUtils.isNotEmpty(profileInfo.get("familyName")) && ObjectUtils.isNotEmpty(profileInfo.get("givenName")))
		{
			return(profileInfo.get("familyName").toString().concat(profileInfo.get("givenName").toString()));
		}
		return null;	
	}
	protected String getExternalId(OrderFlowContext orderFlowContext) {
		String externalId = MDC.get(ApiConstants.TRACE_ID);
		if (orderFlowContext.getOrder().getExternalId() != null)
			externalId = orderFlowContext.getOrder().getExternalId();
		else if (orderFlowContext.getTraceId() != null)
			externalId = orderFlowContext.getTraceId();
		return externalId;
	}

	protected String getCreatedBy(List<RelatedParty> relatedParties, String ofcUserName) {
		String createdBy = null;
		if (StringUtils.isNotEmpty(MDC.get(ApiConstants.USER_ID))) {
			createdBy = MDC.get(ApiConstants.USER_ID);
		} else if (relatedParties != null) {
			var seller = relatedParties.stream().filter(relatedParty -> "Seller".equalsIgnoreCase(relatedParty.getRole()))
					.findFirst().orElse(null);
			if (seller != null)
				createdBy = seller.getName();
		} else if (ofcUserName != null)
			createdBy = ofcUserName;

		return createdBy;
	}


	private String findServiceId() {
		String serviceId = null;
		if (OrderTypes.MNP_PORT_IN.equals(orderType))
			serviceId = executionContext.getOrder().getProfile().getAccount().getServiceGroups().get(0).getServices().get(0).getServiceId();
		else if (OrderTypes.INTERIM_NUMBER_PORT_IN.equals(orderType))
			serviceId = executionContext.getOrder().getServiceManagement().getNewMsisdn();
		return serviceId;

	}



}
