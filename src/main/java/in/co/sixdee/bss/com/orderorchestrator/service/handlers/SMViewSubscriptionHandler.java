package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.constants.GenericLogConstants;
import in.co.sixdee.bss.common.util.CommonUtils;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "SMViewSubscriptionHandler")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SMViewSubscriptionHandler extends AbstractDelegate {
	/**
	 * This class will be executed during UnBarring, ResumeService 
	 * types
	 */
	@Override
	protected void execute() throws Exception {

		String request = null;
		if (CommonUtils.INSTANCE.validateField(reqSpecKey)) {
			try {
				request = joltUtils.convert(reqSpecKey, orderType,
						objectMapper.convertValue(executionContext, Map.class), executionContext.getAttributes());
			} catch (Exception e) {
				log.error("Exception occurred. unable to form the tp request {}", e.getMessage());
				executionContext.getErrorDetail().setCode("COM-001");
				executionContext.getErrorDetail().setMessage("Internal Error: error in forming tp request");
				executionContext.getErrorDetail().setSystem("COM");
				executionContext.setError(true);
				return;
			}
		}
		var callThirdPartyDTO = callThirdParty(request);
		if (callThirdPartyDTO == null) {
			executionContext.setError(true);
			return;
		}
		var response = callThirdPartyDTO.getResponse();
		validateResponse(callThirdPartyDTO);
		if (StringUtils.equalsAny(orderType, OrderTypes.CREATE_SB_GROUP)) {
			getRenewalSubscriptions(response);
		} else {
			// ResumeService & Unbarring cases
			getBarringPlansSubscriptionId(response, executionContext);
		}
		if (executionContext.isError()) {
			log.error("{} Response validation failed", activityId);
			return;
		} else
			workflowDataUpdated = true;

	}

	private void getBarringPlansSubscriptionId(String response, OrderFlowContext executionContext)
			throws JsonMappingException, JsonProcessingException {
		try {

			List<Subscription> subscriptions = new ArrayList<Subscription>();
			List<String> configuredBarringPlanIds = objectMapper
					.convertValue(executionContext.getObjectAttributes().get("configuredBarringPlanIds"), List.class);
			var smResponse = objectMapper.readTree(response);
			if (ObjectUtils.isNotEmpty(smResponse) && smResponse.has("responseParam")
					&& smResponse.get("responseParam").has("offers")) {
				var smSubscriptions = objectMapper.convertValue(smResponse.get("responseParam").get("offers"),
						new TypeReference<List<Subscription>>() {
						});
				if (ObjectUtils.isNotEmpty(smSubscriptions))
					smSubscriptions.forEach(smSubscription -> {
						configuredBarringPlanIds.forEach(barringPlan -> {
							if (barringPlan.equals(smSubscription.getPlanId())) {
								Subscription sub = new Subscription();
								sub.setPlanId(barringPlan);
								if (StringUtils.isNotEmpty(smSubscription.getSubscriptionId()))
									sub.setSubscriptionId(smSubscription.getSubscriptionId());
								else
									throw new CommonException(
											"SubscriptionId of barring addon not present in SM view subscription");
								subscriptions.add(sub);
							}
						});
					});
				if (ObjectUtils.isEmpty(subscriptions)) {
					executionContext.getAttributes().put("subscriptionCallFlowReqd", "false");
					log.info(
							"Configured barring plans are not present in SM view subscription, hence skipping subscriptionCallFlow");
				}
				executionContext.getOrder().getServiceManagement().setSubscriptions(subscriptions);
			}

		} catch (CommonException e) {
			executionContext.getErrorDetail().setCode("COM-422");
			executionContext.getErrorDetail()
					.setMessage("SubscriptionId of barring addon not present in SM view subscription");
			executionContext.getErrorDetail().setSystem("SM");
			executionContext.setError(true);
			return;
		} catch (Exception e) {
			log.error(GenericLogConstants.TAG_APP
					+ " :::::  Exception occured in getBarringPlanSubscriptionId execute method  :::::", e);
			executionContext.getErrorDetail().setCode("COM-001");
			executionContext.getErrorDetail().setMessage("Internal Error: error while parsing SM response");
			executionContext.getErrorDetail().setSystem("COM");
			executionContext.setError(true);
			return;
		}
	}

	private void getRenewalSubscriptions(String response) {

		try {
			List<Subscription> renewalSubscriptions = new ArrayList<Subscription>();
			var smResponse = objectMapper.readTree(response);
			Set<String> planIdSet = new HashSet<String>();

			if (ObjectUtils.isNotEmpty(smResponse) && smResponse.has("responseParam")
					&& smResponse.get("responseParam").has("offers")) {
				var smSubscriptions = objectMapper.convertValue(smResponse.get("responseParam").get("offers"),
						new TypeReference<List<Subscription>>() {
						});
				if (ObjectUtils.isNotEmpty(smSubscriptions)) {
					smSubscriptions.forEach(smSub -> {
						Subscription sub = new Subscription();
						planIdSet.add(smSub.getPlanId());
						sub.setPlanId(smSub.getPlanId());
						sub.setSubscriptionId(smSub.getSubscriptionId());
						renewalSubscriptions.add(sub);
					});

				}
			}
			var isRenewalSubsPresent = ObjectUtils.isNotEmpty(renewalSubscriptions);
			execution.setVariable("isRenewalSubsPresent", isRenewalSubsPresent);
			if (isRenewalSubsPresent) {
				executionContext.getWorkflowData().put("renewalSubsList", renewalSubscriptions);
				executionContext.getAttributes().put("planIds", String.join(",", planIdSet));
			}

		} catch (Exception e) {
			log.error(
					GenericLogConstants.TAG_APP + " :::::  Exception occured in getRenewalSubscriptions method  :::::",
					e);
			executionContext.getErrorDetail().setCode("COM-001");
			executionContext.getErrorDetail().setMessage("Internal Error: error while parsing SM response");
			executionContext.getErrorDetail().setSystem("COM");
			executionContext.setError(true);
			return;
		}
	}
	
}
