
package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.om.model.dto.order.*;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.*;

@Log4j2
@Component(value = "somStarterPackCreation")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SOMStarterPackCreationHandler extends AbstractDelegate {

	protected String planId = null;

	protected String addonServiceId = null;

	private Order orderPayload = null;

	protected String request = null;

	protected int index = 0;

	protected String ki = null;

	@Autowired
	protected GetDataFromCache cache;

	@Autowired
	protected ObjectMapper objectMapper;

	protected StarterPack starterPack;

	protected String meName = null;

	@Override
	protected void execute() throws Exception {
		try {
			orderPayload = executionContext.getOrder();
			starterPack = orderPayload.getStarterPack();
			request = createSOMRequest();
			var callThirdPartyDTO = callThirdParty(request);
			if (callThirdPartyDTO == null) {
				executionContext.setError(true);
				return;
			}
			validateResponse(callThirdPartyDTO);
		} catch (Exception e) {
			log.error("{} In {} .execute Exception occurred ", executionContext.getTraceId(), this.getClass().getName(), e);
		}
	}

	protected String createSOMRequest() throws Exception {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		var serviceOrder = new SOMServiceOrderDTO();
		serviceOrder.setExternalId(executionContext.getOrder().getOrderId());
		if (StringUtils.equalsAnyIgnoreCase(orderType, OrderTypes.STARTERPACK_PROVISIONING)) {
			serviceOrder.setExternalServiceId(starterPack.getServiceId());
			serviceOrder.setOrderType("startPackCreation");
		}
		else
			serviceOrder.setExternalServiceId(executionContext.getOrder().getServiceManagement().getServiceId());
		serviceOrder.setDescription(orderPayload.getDescription());
		serviceOrder.setRequestedStartDate(
				StringUtils.isNotEmpty(orderPayload.getRequestedStartDate()) ? orderPayload.getRequestedStartDate()
						: Instant.now().toString());
		serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
		serviceOrder.setType("ServiceOrder");
		executionContext.getAttributes().put("callbackCorrelationId",
				callbackEvent + ":" + executionContext.getAttributes().get(GenericConstants.SUB_ORDER_ID));
		// var relatedPartyList = createRelatedParty(orderFlowContext);
		// serviceOrder.setRelatedParty(relatedPartyList);

		var addServiceOrderItemList = createServiceOrderItem();
		var barringServiceOrderItemList = createBarringServiceOrderItem();
		if (ObjectUtils.isNotEmpty(addServiceOrderItemList))
			serviceOrderItemList.addAll(addServiceOrderItemList);
		if (ObjectUtils.isNotEmpty(barringServiceOrderItemList))
			serviceOrderItemList.addAll(barringServiceOrderItemList);
		serviceOrder.setServiceOrderItem(serviceOrderItemList);
		if (ObjectUtils.isNotEmpty(serviceOrder))
			request = objectMapper.writeValueAsString(serviceOrder);
		return request;
	}

	private List<ServiceOrderItem> createBarringServiceOrderItem() {
		var cfsConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_STARTERPACK_BARRING_CFS_CONFIG.name(),
				orderType);
		if (ObjectUtils.isNotEmpty(cfsConfig)) {
			List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
			List<Characteristic> serviceCharacteristic = new ArrayList<>();

			String[] cfssNames = cfsConfig.getNgTableData().get("NAME").split(",");
			for (var cfssName : cfssNames) {

				var serviceOrderItem = new ServiceOrderItem();
				var serviceItem = new SOMService();
				var specification = new ProductSpecification();
				index = index + 1;
				serviceOrderItem.setId(String.valueOf(index));
				serviceOrderItem.setAction("add");
				serviceOrderItem.setType("ServiceOrderItem");
				specification.setAtType("MobileService");
				specification.setName(cfssName);
				serviceItem.setServiceSpecification(specification);
				serviceItem.setState("active");
				serviceItem.setType("CFS");
				serviceItem.setServiceCharacteristic(serviceCharacteristic);
				serviceOrderItem.setService(serviceItem);
				serviceOrderItemList.add(serviceOrderItem);

			}
			return serviceOrderItemList;
		}
		return null;

	}

	private List<ServiceOrderItem> createServiceOrderItem() {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		findKi();
		//getKIDetails();
		if (ObjectUtils.isNotEmpty(starterPack)) {
			var subscriptions = starterPack.getSubscriptions();
			if (ObjectUtils.isNotEmpty(subscriptions)) {
				subscriptions.forEach(subscription -> {
					planId = subscription.getPlanId();
					if (ObjectUtils.isNotEmpty(subscription.getCfss())) {
						subscription.getCfss().forEach(cfss -> {
							if (ObjectUtils.isNotEmpty(cfss.getSkipSom()) && Boolean.parseBoolean(cfss.getSkipSom())) {

							} else {
								var serviceOrderItem = createServiceOrderItem(cfss, "CFS");
								serviceOrderItemList.add(serviceOrderItem);
							}

						});
					}
					if (ObjectUtils.isNotEmpty(subscription.getPrss())) {
						subscription.getPrss().forEach(prss -> {
							var serviceOrderItem = createServiceOrderItem(prss, "PRS");
							serviceOrderItemList.add(serviceOrderItem);
						});
					}
					if (ObjectUtils.isNotEmpty(subscription.getLrss())) {
						subscription.getLrss().forEach(lrss -> {
							var serviceOrderItem = createServiceOrderItem(lrss, "LRS");
							serviceOrderItemList.add(serviceOrderItem);
						});
					}
				});
			}

		}

		return serviceOrderItemList;

	}

	private void findKi() {
		Map<String, Object> workflowData = (Map<String, Object>) executionContext.getWorkflowData();
		Optional<Object> nccRespParams = Optional.ofNullable(workflowData.get("NCCCreateServiceResponseAttributes"));
		//Optional<Object> nmsRespParams = Optional.ofNullable(workflowData.get("NMSGetNumberDetailsResponseAttributes"));
		nccRespParams.ifPresent(nccParams -> {
			var paramMap = com.bazaarvoice.jolt.JsonUtils.jsonToMap(com.bazaarvoice.jolt.JsonUtils.toJsonString(nccParams));
			Optional.ofNullable(paramMap.get("meName")).ifPresent(me -> meName = me.toString());
		});
	/*	nmsRespParams.ifPresent(nmsParams -> {
			var paramMap = com.bazaarvoice.jolt.JsonUtils.jsonToMap(com.bazaarvoice.jolt.JsonUtils.toJsonString(nmsParams));
			Optional.ofNullable(paramMap.get("ki")).ifPresent(o -> ki = o.toString());
		});*/
	}

	/*private void getKIDetails() {
		try {
			if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData())) {
				if (executionContext.getWorkflowData().containsKey("NCCCreateServiceResponseAttributes")) {
					var serviceCharacteristics = objectMapper.convertValue(
							executionContext.getWorkflowData().get("NCCCreateServiceResponseAttributes"), JsonNode.class);
					if (ObjectUtils.isNotEmpty(serviceCharacteristics)) {
						if (serviceCharacteristics.has("meName")) {
							meName = serviceCharacteristics.get("meName").asText();
						}
					}
				}

				if (executionContext.getWorkflowData().containsKey("NMSGetNumberDetailsResponseAttributes")) {
					var nmsResponse = objectMapper.convertValue(
							executionContext.getWorkflowData().get("NMSGetNumberDetailsResponseAttributes"), JsonNode.class);
					if (nmsResponse.has("ki"))
						ki = nmsResponse.get("ki").asText();

				}
			}
		} catch (Exception e) {
			log.error(GenericLogConstants.TAG_APP + " | Exception occurred in getKIDetails ", e);
		}

	}*/

	protected ServiceOrderItem createServiceOrderItem(CFSRef cfss, String type) {
		var serviceOrderItem = new ServiceOrderItem();
		index = index + 1;
		serviceOrderItem.setId(String.valueOf(index));
		serviceOrderItem.setAction("add");
		serviceOrderItem.setType("ServiceOrderItem");
		var serviceItem = createServiceItem(cfss, type);
		serviceOrderItem.setService(serviceItem);
		return serviceOrderItem;
	}

	protected SOMService createServiceItem(CFSRef cfss, String type) {
		var serviceItem = new SOMService();
		serviceItem.setState("active");
		serviceItem.setType(type);
		var specification = new ProductSpecification();
		specification.setName(cfss.getName());
		specification.setAtType(type);
		specification.setId(cfss.getId());
		serviceItem.setServiceSpecification(specification);
		var characteristics = createServiceCharacteristic(cfss.getCharacteristics(), cfss.getName());
		if (type.equalsIgnoreCase("CFS")) {
			setSubscriptionId(characteristics);
		}
		serviceItem.setServiceCharacteristic(characteristics);
		return serviceItem;
	}

	protected void setSubscriptionId(List<Characteristic> characteristics) {

		if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData().get("subscriptionIdMap"))) {
			@SuppressWarnings("unchecked")
			HashMap<String, String> subscriptionIdMap = (HashMap<String, String>) executionContext.getWorkflowData()
					.get("subscriptionIdMap");
			if (subscriptionIdMap.get(planId) != null) {
				var characteristic = new Characteristic();
				characteristic.setName("SUBSCRIPTION_ID");
				characteristic.setValueType("String");
				characteristic.setValue(subscriptionIdMap.get(planId));
				characteristics.add(characteristic);
			}

		}
	}

	protected List<Characteristic> createServiceCharacteristic(List<CFSCharacteristicRef> characteristics, String cfsName) {
		var characteristicList = new ArrayList<Characteristic>();
		if (ObjectUtils.isNotEmpty(characteristics))
			characteristics.forEach(cfsCharacteristic -> {
				var characteristic = new Characteristic();
				// SUBCRIPTION_ID we are getting from enrichment but this we shudnt set, cause
				// subsId is not even generated by esb or sm at the time of enrichment.
				if (!"SUBSCRIPTION_ID".equals(cfsCharacteristic.getName())) {
					characteristic.setName(cfsCharacteristic.getName());
					characteristic.setValueType(cfsCharacteristic.getDataType());
					if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName()) && ObjectUtils.isNotEmpty(starterPack.getIccid())
							&& cfsCharacteristic.getName().equalsIgnoreCase("ICCID"))
						characteristic.setValue(starterPack.getIccid());
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName()) && ObjectUtils.isNotEmpty(starterPack.getImsi())
							&& cfsCharacteristic.getName().equalsIgnoreCase("IMSI"))
						characteristic.setValue(starterPack.getImsi());
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("KI"))
						characteristic.setValue(starterPack.getKi());
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName()) && ObjectUtils.isNotEmpty(starterPack.getServiceId())
							&& cfsCharacteristic.getName().equalsIgnoreCase("MSISDN"))
						characteristic.setValue(starterPack.getServiceId());
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("CONNECTION_TYPE"))
						characteristic.setValue("2");
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("meName"))
						characteristic.setValue(meName);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("ALGOID"))
						characteristic.setValue(starterPack.getAlgoId());
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("KDBID"))
						characteristic.setValue(starterPack.getKdbId());
					else
						characteristic.setValue(cfsCharacteristic.getValue());
					characteristicList.add(characteristic);
				}

			});
		return characteristicList;
	}

}
