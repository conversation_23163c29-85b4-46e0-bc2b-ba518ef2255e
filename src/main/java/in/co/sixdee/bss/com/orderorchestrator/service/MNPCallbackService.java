package in.co.sixdee.bss.com.orderorchestrator.service;


import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.SOAPMessageUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.XmlRequestBuilder;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants.OrderCallBackTypes;
import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import in.co.sixdee.bss.com.orderorchestrator.model.mnp.confirmPortOut.ConfirmPortOutOrderRequest;
import in.co.sixdee.bss.com.orderorchestrator.model.mnp.connectService.ConnectServiceRequest;
import in.co.sixdee.bss.com.orderorchestrator.model.mnp.disconnectService.DisconnectService;
import in.co.sixdee.bss.com.orderorchestrator.model.mnp.disconnectServiceInternal.DisconnectServiceInternal;
import in.co.sixdee.bss.com.orderorchestrator.model.mnp.portinnotification.PortInNotificationRequest;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.WaitingProcessInfoEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.SubOrderRepository;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import jakarta.xml.soap.SOAPMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;


@RequiredArgsConstructor
@Log4j2
@Service
public class MNPCallbackService {

    final SOAPMessageUtils soapMessageUtils;

    final OrderCallBackServiceV2 orderCallBackService;

    final OrderStatusManager orderStatusManager;

    final XmlRequestBuilder xmlRequestBuilder;

    final OrderStageService orderStageService;

    final OrderCallBackService orderCallBackServiceOld;
   
    @Autowired
   	private CancelOrderService cancelOrderService;
       
    @Autowired
   	protected OrderRepository orderRepository;

   	@Autowired
   	protected SubOrderRepository subOrderRepo;


    public String processPortInNotificationRequest(String request) {
        String response = null;
        String callbackRequestId = null;
        OrderCallBack orderCallBack = null;
        try {
            if (StringUtils.isNotEmpty(request)) {
                SOAPMessage soapMessage = soapMessageUtils.getSOAPMessageFromXMLString(request, null, null);
                PortInNotificationRequest portInNotificationRequest = soapMessageUtils.unmarshall(soapMessage, PortInNotificationRequest.class);
                callbackRequestId = portInNotificationRequest.getRequestId();
                orderCallBack = createOrderCallBackFromPortInNotification(portInNotificationRequest);
                orderCallBackServiceOld.validateReferenceIdCallback(orderCallBack);
                WaitingProcessInfoEntity waitingProcessInfo = orderCallBackServiceOld.getWaitEventInfo(orderCallBack.getOrderId(), orderCallBack.getSubOrderId());
                if (waitingProcessInfo == null) {
                    log.info(
                            "Unable to find the wait event information for this order id {}. Perhaps the wait event info is not populated yet",
                            orderCallBack.getOrderId());
                    throw new CommonException("Unable to get the wait event info");
                }
                orderCallBack.setWaitingEventInfo(waitingProcessInfo);
                OrderFlowContext orderFlowContext = orderCallBackServiceOld.getOrderFlowContext(waitingProcessInfo.getProcessInstanceId());
                handlePortInNotification(portInNotificationRequest, orderFlowContext);
            } else {
                return createFailureAckResponse(WorkFlowConstants.OrderCallBackTypes.PORT_IN_NOTIFICATION.desc, orderCallBack);
            }
        } catch (Exception e) {
            log.info("Exception occurred while processing port in notification request", e);
            return createFailureAckResponse(WorkFlowConstants.OrderCallBackTypes.PORT_IN_NOTIFICATION.desc, orderCallBack);
        }
        return createSuccessAckResponse(WorkFlowConstants.OrderCallBackTypes.PORT_IN_NOTIFICATION.desc, orderCallBack);
    }

    public String processConnectServiceRequest(String request) {
        String callbackRequestId = null;
        OrderCallBack orderCallBack = null;
        try {
            if (StringUtils.isNotEmpty(request)) {
                SOAPMessage soapMessage = soapMessageUtils.getSOAPMessageFromXMLString(request, null, null);
                ConnectServiceRequest connectServiceRequest = soapMessageUtils.unmarshall(soapMessage, ConnectServiceRequest.class);
                callbackRequestId = connectServiceRequest.getConnectService().getRequestId().getId();
                orderCallBack = createOrderCallBackFromConnectService(connectServiceRequest);
                orderCallBackService.processCallback(orderCallBack);
            } else {
                return createFailureAckResponse(WorkFlowConstants.OrderCallBackTypes.CONNECT_SERVICE.desc, orderCallBack);
            }
        } catch (Exception e) {
            log.info("Exception occurred while processConnectServiceRequest ", e);
            return createFailureAckResponse(WorkFlowConstants.OrderCallBackTypes.CONNECT_SERVICE.desc, orderCallBack);
        }
        return createSuccessAckResponse(WorkFlowConstants.OrderCallBackTypes.CONNECT_SERVICE.desc, orderCallBack);
    }

    public String processDisconnectServiceIntRequest(String request) {
        String callbackRequestId = null;
        OrderCallBack orderCallBack = null;
        boolean disconnectServiceInternal = false;
        try {
            if (StringUtils.isNotEmpty(request)) {
                disconnectServiceInternal = findTypeOfDisconnect(request);
                SOAPMessage soapMessage = soapMessageUtils.getSOAPMessageFromXMLString(request, null, null);
                DisconnectServiceInternal disConnectServiceIntRequest = soapMessageUtils.unmarshall(soapMessage, DisconnectServiceInternal.class);
                callbackRequestId = disConnectServiceIntRequest.getRequest().getRequestId();
                orderCallBack = createOrderCallBackFromDisconnectServiceInt(disConnectServiceIntRequest,disconnectServiceInternal);
                orderCallBackService.processCallback(orderCallBack);
            } else {
                return createFailureAckResponse(MNPConstants.MNP_DISCONNECT_SERVICE_INT_KEY, orderCallBack);
            }
        } catch (Exception e) {
            log.info("Exception occurred while processDisconnectServiceIntRequest ", e);
            return createFailureAckResponse(MNPConstants.MNP_DISCONNECT_SERVICE_INT_KEY, orderCallBack);
        }
        return createSuccessAckResponse(MNPConstants.MNP_DISCONNECT_SERVICE_INT_KEY, orderCallBack);
    }

    private boolean findTypeOfDisconnect(String request) {
        return request.contains("sel:SDInfos");
    }

    public String processDisconnectServiceRequest(String request) {
        String callbackRequestId = null;
        OrderCallBack orderCallBack = null;
        try {
            if (StringUtils.isNotEmpty(request)) {
                SOAPMessage soapMessage = soapMessageUtils.getSOAPMessageFromXMLString(request, null, null);
                DisconnectService disConnectServiceRequest = soapMessageUtils.unmarshall(soapMessage, DisconnectService.class);
                callbackRequestId = disConnectServiceRequest.getRequestID().getId();
                orderCallBack = createOrderCallBackFromDisconnectService(disConnectServiceRequest);
                orderCallBackService.processCallback(orderCallBack);
            } else {
                return createFailureAckResponse(MNPConstants.MNP_DISCONNECT_SERVICE_INT_KEY, orderCallBack);
            }
        } catch (Exception e) {
            log.info("Exception occurred while processDisconnectServiceRequest ", e);
            return createFailureAckResponse(MNPConstants.MNP_DISCONNECT_SERVICE_INT_KEY, orderCallBack);
        }
        return createSuccessAckResponse(MNPConstants.MNP_DISCONNECT_SERVICE_INT_KEY, orderCallBack);
    }

    public String processConfirmPortOut(String request) {
        String callbackRequestId = null;
        OrderCallBack orderCallBack = null;
        try {
            if (StringUtils.isNotEmpty(request)) {
                SOAPMessage soapMessage = soapMessageUtils.getSOAPMessageFromXMLString(request, null, null);
                ConfirmPortOutOrderRequest confirmPortOutReq = soapMessageUtils.unmarshall(soapMessage, ConfirmPortOutOrderRequest.class);
                callbackRequestId = confirmPortOutReq.getConfirmPortOutOrder().getRequestID().getId();
                orderCallBack = createOrderCallBackFromConfirmPortOut(confirmPortOutReq);
                orderCallBackServiceOld.validateReferenceIdCallback(orderCallBack);
                WaitingProcessInfoEntity waitingProcessInfo = orderCallBackServiceOld.getWaitEventInfo(orderCallBack.getOrderId(), orderCallBack.getSubOrderId());
                if (waitingProcessInfo == null) {
                    log.info(
                            "Unable to find the wait event information for this order id {}. Perhaps the wait event info is not populated yet",
                            orderCallBack.getOrderId());
                    throw new CommonException("Unable to get the wait event info");
                }
                orderCallBack.setWaitingEventInfo(waitingProcessInfo);
                OrderFlowContext orderFlowContext = orderCallBackServiceOld.getOrderFlowContext(waitingProcessInfo.getProcessInstanceId());
                handleConfirmPortOut(orderFlowContext);
            } else {
                return createFailureAckResponse(MNPConstants.MNP_CONFIRM_PORT_OUT_KEY, orderCallBack);
            }
        } catch (Exception e) {
            log.info("Exception occurred while processConfirmPortOut ", e);
            return createFailureAckResponse(MNPConstants.MNP_CONFIRM_PORT_OUT_KEY, orderCallBack);
        }
        return createSuccessAckResponse(MNPConstants.MNP_CONFIRM_PORT_OUT_KEY, orderCallBack);

    }

    public OrderCallBack createOrderCallBackFromPortInNotification(PortInNotificationRequest portInNotificationRequest) {
        OrderCallBack orderCallBack = new OrderCallBack();
        orderCallBack.setReferenceId(portInNotificationRequest.getPortInNotification().getReferenceID().getId());
        orderCallBack.setCallbackType(WorkFlowConstants.OrderCallBackTypes.PORT_IN_NOTIFICATION.desc);
        orderCallBack.getmnpAttributes().put(MNPConstants.MNP_REQUEST_ID, portInNotificationRequest.getPortInNotification().getRequestId().getId());
        orderCallBack.getmnpAttributes().put(MNPConstants.MNP_REFERENCE_ID, portInNotificationRequest.getPortInNotification().getReferenceID().getId());

        return orderCallBack;
    }

    public OrderCallBack createOrderCallBackFromConnectService(ConnectServiceRequest connectServiceRequest) {
        OrderCallBack orderCallBack = new OrderCallBack();
        orderCallBack.setReferenceId(connectServiceRequest.getConnectService().getServiceDetailElement().getReferenceId().getId());
        orderCallBack.setCallbackType(WorkFlowConstants.OrderCallBackTypes.CONNECT_SERVICE.desc);
        orderCallBack.setStatus(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue());
        orderCallBack.getmnpAttributes().put(MNPConstants.MNP_REQUEST_ID, connectServiceRequest.getConnectService().getRequestId().getId());
        orderCallBack.getmnpAttributes().put(MNPConstants.MNP_REFERENCE_ID, connectServiceRequest.getConnectService().getServiceDetailElement().getReferenceId().getId());
        return orderCallBack;
    }


    public OrderCallBack createOrderCallBackFromDisconnectServiceInt(DisconnectServiceInternal disconnectServiceIntRequest, boolean disconnectServiceInternal) {
        OrderCallBack orderCallBack = new OrderCallBack();
        orderCallBack.setReferenceId(disconnectServiceIntRequest.getRequest().getSdInfos().getSdInfos().get(0).getReferenceId());
        orderCallBack.setCallbackType(WorkFlowConstants.OrderCallBackTypes.DISCONNECT_SERVICE_INTERNAL.desc);
        orderCallBack.setStatus(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue());
        orderCallBack.getmnpAttributes().put(MNPConstants.MESSAGE_RECEIVER_TELCO, disconnectServiceIntRequest.getRequest().getMessageReciverTelco());
        orderCallBack.getmnpAttributes().put(MNPConstants.MESSAGE_SENDER_TELCO, disconnectServiceIntRequest.getRequest().getMessageSenderTelco());
        orderCallBack.getmnpAttributes().put(MNPConstants.MNP_REQUEST_ID, disconnectServiceIntRequest.getRequest().getRequestId());
        orderCallBack.getmnpAttributes().put(MNPConstants.MNP_REFERENCE_ID, disconnectServiceIntRequest.getRequest().getSdInfos().getSdInfos().get(0).getReferenceId());
        orderCallBack.getmnpAttributes().put(MNPConstants.MNP_SERVICE_ID, disconnectServiceIntRequest.getRequest().getSdInfos().getSdInfos().get(0).getSubscriberNumber());
        if (disconnectServiceInternal) {
            log.info("Disconnect service call back is for internal with service id {}", orderCallBack.getmnpAttributes().get(MNPConstants.MNP_SERVICE_ID));
            orderCallBack.getmnpAttributes().put(MNPConstants.MNP_CALLBACK_TYPE, MNPConstants.MNP_CALLBACK_TYPE_DISCONNECT_INT);
        } else {
            log.info("Disconnect service call back is for external with reference id {}", orderCallBack.getmnpAttributes().get(MNPConstants.MNP_REFERENCE_ID));
        }

        return orderCallBack;
    }

    public OrderCallBack createOrderCallBackFromDisconnectService(DisconnectService disconnectServiceRequest) {
        OrderCallBack orderCallBack = new OrderCallBack();
        orderCallBack.setReferenceId(disconnectServiceRequest.getServiceDetailElements().getReferenceID().getId());
        orderCallBack.setCallbackType(WorkFlowConstants.OrderCallBackTypes.DISCONNECT_SERVICE.desc);
        orderCallBack.setStatus(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue());
        orderCallBack.getmnpAttributes().put(MNPConstants.MNP_REFERENCE_ID, disconnectServiceRequest.getServiceDetailElements().getReferenceID().getId());
        orderCallBack.getmnpAttributes().put(MNPConstants.MNP_SERVICE_ID, disconnectServiceRequest.getServiceDetailElements().getServiceID());


        return orderCallBack;
    }

    public OrderCallBack createOrderCallBackFromConfirmPortOut(ConfirmPortOutOrderRequest confirmPortOutReq) {
        OrderCallBack orderCallBack = new OrderCallBack();
        orderCallBack.setReferenceId(confirmPortOutReq.getConfirmPortOutOrder().getReferenceId().getId());
        orderCallBack.setCallbackType(WorkFlowConstants.OrderCallBackTypes.CONFIRM_PORT_OUT.desc);
        orderCallBack.setStatus(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue());
        orderCallBack.getmnpAttributes().put(MNPConstants.MNP_REFERENCE_ID, confirmPortOutReq.getConfirmPortOutOrder().getReferenceId().getId());
        orderCallBack.getmnpAttributes().put(MNPConstants.MNP_REQUEST_ID, confirmPortOutReq.getConfirmPortOutOrder().getRequestID().getId());


        return orderCallBack;
    }

    public void handlePortInNotification(PortInNotificationRequest portInNotificationRequest, OrderFlowContext orderFlowContext) {
        String notificationType = portInNotificationRequest.getPortInNotification().getPortingResult().getResponseCode();
        String eventType = null;
        String stageStatus;
        boolean submitPortInStageUpdateRequired = false;
        switch (notificationType) {
            case MNPConstants.PORT_IN_NOTIFICATION_PNVAL:
            case MNPConstants.PORT_IN_NOTIFICATION_PNCON:
                eventType = WorkFlowConstants.OrderCallBackTypes.PORT_IN_NOTIFICATION_SUCCESS.desc;
                updateOrderStatus(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_INPROGRESS.getValue(),"Waiting for ConnectService Callback",Long.valueOf(orderFlowContext.getOrder().getOrderId()));
                break;
            case MNPConstants.PORT_IN_NOTIFICATION_PNCAN:
            	 eventType = WorkFlowConstants.OrderCallBackTypes.PORT_IN_NOTIFICATION_FAILURE.desc;
                 orderFlowContext.getErrorDetail().setCode(portInNotificationRequest.getPortInNotification().getPortingResult().getRejectionCode());
                 orderFlowContext.getErrorDetail().setSystem("SDP");
                 orderFlowContext.getErrorDetail().setMessage(portInNotificationRequest.getPortInNotification().getPortingResult().getReasonText());
                 submitPortInStageUpdateRequired = true;
                 if (StringUtils.containsAnyIgnoreCase(orderFlowContext.getOrder().getOrderType(), "MNPPortIn", "InterimNumberPortIn")) 
                 {      
                	 updateMasterAndSubOrderStatus(Long.valueOf(orderFlowContext.getOrder().getOrderId()));
                	 orderFlowContext.getAttributes().put("actualOrderType",orderFlowContext.getOrder().getOrderType());
                	 orderFlowContext.getAttributes().put("rollbackProcessId",orderFlowContext.getOrder().getOrderType()+"-Rollback");
                	 orderFlowContext.getOrder().setOrderType(orderFlowContext.getOrder().getOrderType() + "-" + "Rollback");
                	 cancelOrderService.processCancelOrderRequest(orderFlowContext);
                 }
                 break;
            case MNPConstants.PORT_IN_NOTIFICATION_PNCNT:
            case MNPConstants.PORT_IN_NOTIFICATION_PNREJ:
            case MNPConstants.PORT_IN_NOTIFICATION_PNINV:
                eventType = WorkFlowConstants.OrderCallBackTypes.PORT_IN_NOTIFICATION_FAILURE.desc;
                orderFlowContext.getErrorDetail().setCode(portInNotificationRequest.getPortInNotification().getPortingResult().getRejectionCode());
                orderFlowContext.getErrorDetail().setSystem("SDP");
                orderFlowContext.getErrorDetail().setMessage(portInNotificationRequest.getPortInNotification().getPortingResult().getReasonText());
                submitPortInStageUpdateRequired = true;
                break;
            default:
        }
        if (WorkFlowConstants.OrderCallBackTypes.PORT_IN_NOTIFICATION_SUCCESS.desc.equals(eventType)) {
            stageStatus = WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue();
        } else {
            stageStatus = WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue();
        }
        orderStatusManager.processStatusUpdates(orderFlowContext, notificationType,
                WorkFlowConstants.OrderCallBackTypes.PORT_IN_NOTIFICATION.desc, true, stageStatus);
        if (submitPortInStageUpdateRequired) {
            // for failure notification, submit port in stage also should be updated as failed
            // ,so that it can be retried to submit the port in request again
            //String stageId = orderStageService.findSubmitPortInStage(Long.parseLong(orderFlowContext.getOrder().getOrderId()));
            // find linked activityId from COM_ORDER_STAGE_CONFIG by stagesId,
          /*  if (stageId != null) {
                orderStatusManager.processStatusUpdates(orderFlowContext, "SDPSubmitPortIn",
                        WorkFlowConstants.OrderCallBackTypes.PORT_IN_NOTIFICATION.desc, true, stageStatus);
            }*/

        }
    }

    private void updateOrderStatus(String orderState, String stateReason, Long orderId) {
    	orderRepository.updateStatusForMNPNotification(orderState,stateReason,orderId);
		subOrderRepo.updateSubOrderStatusForMNPNotification(orderState,stateReason,orderId);
	}

	public void updateMasterAndSubOrderStatus(Long orderId) {

    	orderRepository.updateOrderStatusToCancelled(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_CANCELLED.getValue(),"" , orderId);
        subOrderRepo.updateSubOrderStatusByOrderIdToCancelled(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_CANCELLED.getValue(),"",  orderId);

    }


	private void handleConfirmPortOut(OrderFlowContext orderFlowContext) {
        String notificationType = OrderCallBackTypes.CONFIRM_PORT_OUT.desc;
        String stageStatus = WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue();

        orderStatusManager.processStatusUpdates(orderFlowContext, notificationType,
                WorkFlowConstants.OrderCallBackTypes.PORT_IN_NOTIFICATION.desc, true, stageStatus);

    }

    public String createSuccessAckResponse(String xmlRequestKey, OrderCallBack orderCallback) {
        orderCallback.getmnpAttributes().put(MNPConstants.MNP_TIMESTAMP,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern(MNPConstants.MNP_DATETIME_FORMAT)));
        return xmlRequestBuilder.buildRequest(xmlRequestKey + "_Response", null, orderCallback.getmnpAttributes());
    }

    public String createFailureAckResponse(String xmlRequestKey, OrderCallBack orderCallback) {
        if (orderCallback == null)
            orderCallback = new OrderCallBack();

        orderCallback.getmnpAttributes().put(MNPConstants.MNP_TIMESTAMP,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern(MNPConstants.MNP_DATETIME_FORMAT)));

        return xmlRequestBuilder.buildRequest(xmlRequestKey + "_Failure_Response", null, orderCallback.getmnpAttributes());
    }


}
