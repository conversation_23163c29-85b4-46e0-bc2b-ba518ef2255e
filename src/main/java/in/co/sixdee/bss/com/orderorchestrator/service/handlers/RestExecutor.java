package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.FailedDependencyException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.AppConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.connector.RestConnector;
import in.co.sixdee.bss.common.constants.GenericLogConstants;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.common.jolt.JoltUtils;
import in.co.sixdee.bss.common.util.SequenceGenerator;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.sm.Offer;
import in.co.sixdee.bss.om.model.dto.sm.SMViewSubscriptionResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.URI;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.util.*;

@Log4j2
public class RestExecutor {

    @Autowired
    protected RestConnector restConnector;

    @Autowired
    protected JoltUtils joltUtils;

    @Autowired
    protected GetDataFromCache cache;

    @Autowired
    protected ObjectMapper objectMapper;

    protected in.co.sixdee.bss.common.dto.CallThirdPartyDTO callThirdPartyDTO;

    protected void validateResponse(in.co.sixdee.bss.common.dto.CallThirdPartyDTO callThirdPartyDTO) {
        DocumentContext responseContext = null;
        if (callThirdPartyDTO.getResponse() != null) {
            try {
                responseContext = JsonPath.parse(callThirdPartyDTO.getResponse());
            } catch (Exception e) {
                throw new CommonException(StatusConstants.HttpConstants.INTERNAL_SERVER_ERROR, "Exception while creating the response context " + e.getMessage());
            }
        }
        if (!StringUtils.contains(callThirdPartyDTO.getExpectedHttpCode(), callThirdPartyDTO.getResponseCode())) {
            throw new CommonException(StatusConstants.HttpConstants.UNPROCESSABLE_ENTITY, "Error while executing skip request. " + callThirdPartyDTO.getResponseMessage());
        }

        if (callThirdPartyDTO.isRespPayloadValidationRqd()) {
            validatePayload(callThirdPartyDTO, responseContext);
        } else {
            log.info("Payload validation is not enabled for the third party :: {}", callThirdPartyDTO.getThirdPartyId());
        }
    }

    private void validatePayload(CallThirdPartyDTO callThirdPartyDTO, DocumentContext responseContext) {
        if (responseContext != null) {
            var statusCode = responseContext.read(callThirdPartyDTO.getResponseCodeJsonPath()) + "";
            var statusDesc = responseContext.read(callThirdPartyDTO.getResponseMessageJsonPath()) + "";
            if (!callThirdPartyDTO.getExpectedRespCode().equals(statusCode)) {
                throw new CommonException(StatusConstants.HttpConstants.UNPROCESSABLE_ENTITY, "Error while executing the skip request. " + callThirdPartyDTO.getResponseMessage());
            }
        }
    }

    protected void initThirdPartyCallDetails(String thirdPartyId, String orderType, OrderFlowContext orderFlowContext) throws CommonException {
        var urlTokenConfigs = new HashMap<String, String>();
        var thirdPartyUrlConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(),
                thirdPartyId);
        if (Objects.isNull(thirdPartyUrlConfig))
            throw new ConfigurationNotValidException("url configuration not found for the third party id :: " + thirdPartyId);
        setupCallThirdPartyDto(thirdPartyUrlConfig);
        var urlTokenConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_TOKEN_CONFIG.name(),
                thirdPartyId + "_" + orderType);
        var uri = callThirdPartyDTO.getUrl();
        if (urlTokenConfig != null) {
            urlTokenConfigs = urlTokenConfig.getNgTableData();
            uri = formatUri(uri, resolveUriTokens(urlTokenConfigs.get(AppConstants.CacheFields.TOKENS.name()), orderFlowContext));
            callThirdPartyDTO.setUrl(uri);
        }
        try {
            callThirdPartyDTO.setUri(new URI(uri.trim()));
        } catch (URISyntaxException e) {
            throw new CommonException(null, e.getMessage());
        }

    }

    public void setupCallThirdPartyDto(CacheTableDataDTO thirdPartyUrlConfig) {
        callThirdPartyDTO = new in.co.sixdee.bss.common.dto.CallThirdPartyDTO();
        callThirdPartyDTO.setRespPayloadValidationRqd(BooleanUtils
                .toBoolean(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.PAYLOAD_VALIDATION.name())));
        callThirdPartyDTO
                .setThirdPartyId(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.THIRD_PARTY_ID.name()));
        callThirdPartyDTO.setUrl(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.URL.name()));
        callThirdPartyDTO.setReadTimeout(
                NumberUtils.toInt(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.READ_TIMEOUT.name()), 3000));
        callThirdPartyDTO.setConnTimeout(NumberUtils
                .toInt(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.CONNECTION_TIMEOUT.name()), 3000));
        callThirdPartyDTO.setExpectedHttpCode(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.EXPECTED_HTTP_CODE.name()));
        callThirdPartyDTO.setExpectedRespCode(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.EXPECTED_RESP_CODE.name()));
        callThirdPartyDTO.setHeaders(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.HEADERS.name()));
        callThirdPartyDTO.setTransportMethod(in.co.sixdee.bss.common.dto.CallThirdPartyDTO.TransportMethod
                .valueOf(thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.TRANSPORT_METHOD.name())));
        callThirdPartyDTO.setResponseCodeJsonPath(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.RESP_STATUS_PATH.name()));
        callThirdPartyDTO.setResponseMessageJsonPath(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.RESP_STATUS_DESC_PATH.name()));
        callThirdPartyDTO.setThirdPartySystem(
                thirdPartyUrlConfig.getNgTableData().get(AppConstants.CacheFields.THIRD_PARTY_SYSTEM.name()));
    }

    protected in.co.sixdee.bss.common.dto.CallThirdPartyDTO callThirdParty(String request, OrderFlowContext orderFlowContext) {
        callThirdPartyDTO.setRequest(request);
        var headerMap = getHeaderMap(callThirdPartyDTO.getHeaders());
        if (headerMap != null)
            callThirdPartyDTO.setHeaderMap(transformHeaders(headerMap, orderFlowContext));
        restConnector.service(callThirdPartyDTO);
        return callThirdPartyDTO;
    }

    protected Map<String, String> transformHeaders(Map<String, String> headerMap, OrderFlowContext orderFlowContext) {
        Set<Map.Entry<String, String>> entrySet = null;
        Map<String, String> newMap = new HashMap<>();
        String path = null;
        String value = null;
        try {
            var docContext = getOrderFlowJsonPathContext(orderFlowContext);
            entrySet = headerMap.entrySet();
            for (Map.Entry<String, String> entry : entrySet) {
                if (entry.getValue().startsWith("@(") && entry.getValue().endsWith(")")) {
                    path = entry.getValue().substring(2, entry.getValue().length() - 1);
                    try {
                        value = docContext.read(path);
                        // JsonPath.read(executionContext.asJson(), path);
                    } catch (PathNotFoundException e) {
                        log.warn("Path {} not found!!!", path);
                    }
                    if (ObjectUtils.isNotEmpty(value))
                        newMap.put(entry.getKey(), value);
                } else if (entry.getValue().startsWith("{") && entry.getValue().endsWith("}")) {
                    newMap.put(entry.getKey(),
                            orderFlowContext.getAttributes().get(entry.getValue().substring(1, entry.getValue().length() - 1)));
                } else if (entry.getValue().startsWith("date(") && entry.getValue().endsWith(")")) {
                    String timestamp = null;
                    String format = entry.getValue().substring(5, entry.getValue().length() - 1);
                    if (StringUtils.isNotEmpty(format)) {
                        if (StringUtils.equalsIgnoreCase(format, "epoch")) {
                            timestamp = String.valueOf(System.currentTimeMillis());
                        } else {
                            SimpleDateFormat sdf = null;
                            sdf = new SimpleDateFormat(format);
                            timestamp = sdf.format(System.currentTimeMillis());
                        }
                        newMap.put(entry.getKey(), timestamp);
                    }
                } else if (entry.getValue().startsWith("generateUniqueId(") && entry.getValue().endsWith(")")) {
                    newMap.put(entry.getKey(), String.valueOf(SequenceGenerator.getSequencerInstance().nextId()));

                } else if (entry.getValue().startsWith("esbRequestId(") && entry.getValue().endsWith(")")) {
                    newMap.put(entry.getKey(), orderFlowContext.getTraceId() + "_"
                            + SequenceGenerator.getSequencerInstance().nextId());

                } else
                    newMap.put(entry.getKey(), entry.getValue());

            }

        } catch (Exception e) {
            throw new CommonException(StatusConstants.HttpConstants.INTERNAL_SERVER_ERROR, " Exception occurred in transformHeaders: " + e.getMessage());
        }
        return newMap;
    }

    protected DocumentContext getOrderFlowJsonPathContext(OrderFlowContext orderFlowContext) throws JsonProcessingException {
        return JsonPath.parse(objectMapper.writeValueAsString(orderFlowContext));
    }

    protected AbstractDelegate.Param resolveUriTokens(String tokens, OrderFlowContext orderFlowContext) {
        if (tokens == null)
            return new AbstractDelegate.Param();
        DocumentContext context = null;
        try {
            context = tokens.contains("$") ? getOrderFlowJsonPathContext(orderFlowContext) : null;
        } catch (Exception e) {
            log.info("Exception occurred in resolveUriTokens: {}", e.getMessage());
            return new AbstractDelegate.Param();
        }
        var tokenArray = tokens.split("\\|");
        var uriParams = new AbstractDelegate.Param();
        for (var s : tokenArray) {
            var param = s.split("=", 2);
            if (param.length > 1) {
                var token = param[0];
                var value = param[1];
                if (value.startsWith("$")) {
                    resolveJsonPath(context, token, value, uriParams);
                } else if (value.startsWith("val")) {
                    resolveValExpression(token, value, uriParams, orderFlowContext);
                } else {
                    uriParams.put(token, value);
                }
            }
        }
        return uriParams;
    }

    private void resolveJsonPath(DocumentContext context, String key, String path, AbstractDelegate.Param uriParams) {
        if (context == null || path == null || uriParams == null)
            return;
        var value = context.read(path);
        if (value instanceof String || value instanceof Integer) {
            uriParams.put(key, String.valueOf(value));
        } else if (value instanceof List<?>) {
            uriParams.put(key, String.valueOf(value).replace("[", "").replace("]", "").replaceAll("\\s", ""));
        } else {
            log.info(GenericLogConstants.TAG_APP + "ignoring token {}, as it is not a primitive", key);
        }
    }

    protected void resolveValExpression(String key, String path, AbstractDelegate.Param uriParams, OrderFlowContext orderFlowContext) {
        if (path == null || uriParams == null)
            return;
        var valPath = path.substring(path.indexOf("(") + 1, path.indexOf(")"));
        if (orderFlowContext.getAttributes().containsKey(valPath)) {
            uriParams.put(key, orderFlowContext.getAttributes().get(valPath));
        }
    }

    protected String formatUri(String uri, AbstractDelegate.Param params) {
        return new StringSubstitutor(params).replace(uri);
    }

    public Map<String, String> getHeaderMap(String headerParams) {
        Map<String, String> headerMap = null;
        try {
            if (StringUtils.isNotEmpty(headerParams)) {
                headerMap = new HashMap<String, String>();
                String[] headerParam = headerParams.split(",");
                for (String header : headerParam) {
                    if (header.contains("=")) {
                        String name = null, value = null;
                        try {
                            name = header.split("=")[0];
                            value = header.split("=")[1];
                            headerMap.put(name, value);
                        } catch (Exception e) {
                            throw e;
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw e;
        }
        return headerMap;
    }

    protected String modifyResponse(String respSpecKey, String response, String orderType) {
        try {
            return joltUtils.convert(respSpecKey, orderType, JsonUtils.jsonToObject(response), null);
        } catch (Exception e) {
            throw new CommonException("Error while executing skip request. while transforming the response");
        }
    }
   /* protected void extractSubscriptionIdFromESBResponse(String response, HashMap<String, String> subscriptionIdMap) {
        ESBResponse esbResponse = null;

        try {
            esbResponse = objectMapper.readValue(response, ESBResponse.class);
        } catch (JsonProcessingException e) {
            throw new CommonException("Error while executing skip request. NCC QueryDevice failed while extracting subscription ids");
        }
        if (ObjectUtils.isNotEmpty(esbResponse.getServiceCharacteristic())) {
            var subscriptionIdChars = esbResponse.getServiceCharacteristic().stream()
                    .filter(characteristic -> GenericConstants.ESB_CHARACTERISTIC_TYPE_SUBSCRIPTION_ID
                            .equals(characteristic.getType()))
                    .collect(Collectors.toList());
            subscriptionIdChars.forEach(subscriptionIdChar -> {
                subscriptionIdMap.put(subscriptionIdChar.getName(), subscriptionIdChar.getValue());
            });
        }
    }*/

}
