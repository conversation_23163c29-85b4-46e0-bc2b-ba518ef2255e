package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 *
 */

@Getter
@Setter
@NoArgsConstructor

@Entity
@Table(name = "WAITING_PROCESS_INFO")
@lombok.Generated
public class WaitingProcessInfoEntity implements Serializable {

	/**
	 *
	 */
	private static final long	serialVersionUID	= 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "SEQ_ID", nullable = false)
	private Long				seqId;

	@Column(name = "ORDER_ID", length = 20, nullable = false)
	private String				orderId;

	@Column(name = "SUB_ORDER_ID", length = 20, nullable = false)
	private String				subOrderId;

	@Column(name = "PROCESS_INSTANCE_ID", length = 100)
	@Value("NULL")
	private String				processInstanceId;

	@Column(name = "STAGE_CODE", length = 100)
	@Value("NULL")
	private String				stageCode;

	@Column(name = "EXECUTION_ID", length = 100)
	@Value("NULL")
	private String				executionId;

	@Column(name = "EVENT_NAME", length = 50, nullable = false)
	private String				eventName;

	@Column(name = "CREATED_ON", columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP", insertable = false, updatable = false)
	private Date				createDate;

	@Version
	private int version;

	@Column(name ="lock_exp_time")
	private Date lockExpTime;

	@Column(name = "WAIT_TYPE")
	private String waitType;

}
