package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class EntityValidationException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private StatusConstants.HttpConstants status;
    private String message;

    public EntityValidationException(StatusConstants.HttpConstants status, String message) {
        super();
        this.status = status;
        this.message = message;
    }

    public EntityValidationException(String message) {
        super();
        this.message = message;
    }

    public StatusConstants.HttpConstants getStatus() {
        return status;
    }

    public void setStatus(StatusConstants.HttpConstants status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public synchronized Throwable fillInStackTrace() {
        return this;
    }

}
