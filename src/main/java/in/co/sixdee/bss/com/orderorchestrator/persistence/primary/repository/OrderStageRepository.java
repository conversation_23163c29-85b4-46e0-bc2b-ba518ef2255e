package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;

@Repository
public interface OrderStageRepository extends JpaRepository<OrderStageEntity, Long> {

    @Modifying(clearAutomatically = true)
    @Query("update OrderStageEntity s set s.state = :state, s.stateReason =:stateReason, s.lastModifiedBy = COALESCE(:lastModifiedBy, s.lastModifiedBy) where " +
            "s.subOrderId = :subOrderId and s.stageCode=:stageCode")
    int updateStatusAndReasonBySubOrderId(@Param("state") String state, @Param("stateReason") String stateReason,
                                          @Param("subOrderId") Long subOrderId, @Param("stageCode") String stageCode, @Param("lastModifiedBy") String lastModifiedBy);

    @Modifying(clearAutomatically = true)
    @Query("update OrderStageEntity s set s.state = :state, s.stateReason=:stateReason, s.lastModifiedBy = COALESCE(:lastModifiedBy, s.lastModifiedBy) where s.orderId = :orderId and s.stageCode=:stageCode")
    public int updateStatusAndReasonByOrderId(@Param("state") String state, @Param("stateReason") String stateReason,
                                              @Param("orderId") Long orderId, @Param("stageCode") String stageCode, @Param("lastModifiedBy") String lastModifiedBy);

    @Query("select case when count(o)>0 then true else false end from OrderStageEntity o where o.id=:id and o.subOrderId = :subOrderId and o.state=:state")
    public boolean existsByOrderIdAndStatus(@Param("id") Long id, @Param("subOrderId") Long subOrderId,
                                            @Param("state") String state);

    @Query("select case when count(o)>0 then true else false end from OrderStageEntity o where o.orderId=:orderId and o.name=:name")
    public boolean existsByOrderIdAndName(@Param("orderId") Long orderId, @Param("name") String stageName);

    @Query("select case when count(o)>0 then false else true end from OrderStageEntity o where o.subOrderId=:subOrderId and o.state in ('Failed','In-Progress','Pending','Cancelled')")
    public boolean isAllStagesCompleted(@Param("subOrderId") Long subOrderId);

    @Query("select case when count(o)>0 then false else true end from OrderStageEntity o where o.orderId=:orderId and o.state in ('Failed','In-Progress','Pending','Cancelled')")
    public boolean isAllStagesCompletedForOrder(@Param("orderId") Long orderId);

    @Query("select case when count(o)>0 then true else false end from OrderStageEntity o where o.subOrderId=:subOrderId and o.name =:name")
    public boolean existsBySubOrderIdAndName(@Param("subOrderId") Long subOrderId, @Param("name") String stageName);

    @Modifying(clearAutomatically = true)
    @Query("update OrderStageEntity s set s.state = :state , s.stateReason = :stateReason where s.subOrderId = :subOrderId and s.id=:id")
    public int updateStatusAndReasonBySubOrderIdandStageId(@Param("state") String state, @Param("stateReason") String stateReason,
                                                           @Param("subOrderId") Long subOrderId, @Param("id") Long id);

    @Query("select o from OrderStageEntity o where o.subOrderId = :subOrderId and o.stageCode = :stageCode")
    public OrderStageEntity findStagebySubOrderIdAndName(@Param("subOrderId") Long subOrderId, @Param("stageCode") String stageCode);

    @Modifying(clearAutomatically = true)
    @Query("update OrderStageEntity o set o.state = :state, o.stateReason = :stateReason where o.id = :id")
    public int updateStageStatus(@Param("state") String state, @Param("stateReason") String stateReason, @Param("id") Long id);

    @Modifying(clearAutomatically = true)
    @Query("update OrderStageEntity o set o.state = :state, o.rollbackStatus = :rollbackStatus, o.stateReason = :stateReason where o.id = :id")
    public int updateRollbackStatus(@Param("state") String state, @Param("rollbackStatus") String rollbackStatus,
                                    @Param("stateReason") String stateReason, @Param("id") Long id);

    @Modifying(clearAutomatically = true)
    @Query("update OrderStageEntity o set o.state = :state, o.rollbackStatus = :rollbackStatus where o.id = :id")
    public int updateRollbackStatus(@Param("state") String state, @Param("rollbackStatus") String rollbackStatus,
                                    @Param("id") Long id);

    @Modifying(clearAutomatically = true)
    @Query("update OrderStageEntity o set o.state = :state where o.name = :name and o.subOrderId = :subOrderId")
    public int updateStageStatusByName(@Param("subOrderId") Long subOrderId, @Param("state") String state,
                                       @Param("name") String name);

    @Query("select case when count(o)>0 then true else false end from OrderStageEntity o where o.orderId=:orderId and o.subOrderId = :subOrderId and o.stageCode=:stageCode")
    public boolean existsByOrderIdsubOrderIdandName(@Param("orderId") Long orderId, @Param("subOrderId") Long subOrderId,
                                                    @Param("stageCode") String stageCode);

    @Modifying(clearAutomatically = true)
    @Query("update OrderStageEntity s set s.state = :state, s.stateReason = :stateReason, s.lastModifiedBy =:lastModifiedBy where s.id =:id")
    public int updateStatusAndReasonByStageId(@Param("state") String state, @Param("stateReason") String stateReason,
                                              @Param("id") Long id, @Param("lastModifiedBy") String lastModifiedBy);

    @Modifying(clearAutomatically = true)
    @Query("update OrderStageEntity s set s.state = :state, s.stateReason = :stateReason where s.id = :id")
    public int updateStatusAndReasonByStageId(@Param("state") String state, @Param("stateReason") String stateReason,
                                              @Param("id") Long id);

    @Query("select case when count(o)>1 then false else true end from OrderStageEntity o where o.subOrderId=:subOrderId and o.state <> 'Completed' and o.state <> 'Skipped'")
    public boolean isAllStagesCompletedorSkip(@Param("subOrderId") Long subOrderId);

    @Query("select o from OrderStageEntity o where o.orderId = :orderId and o.state = 'In-Progress'")
    public OrderStageEntity findInProgressStagebyOrderId(@Param("orderId") Long orderId);

    @Query("select o from OrderStageEntity o where o.subOrderId = :subOrderId and o.state in ('In-Progress','Failed') and o.stageCode like :stageCodePrefix%")
    public OrderStageEntity findInProgressStageBySubOrderIdAndStageCodePrefix(@Param("subOrderId") Long subOrderId, @Param("stageCodePrefix") String stageCodePrefix);

    @Query("select o.stageCode from OrderStageEntity o where o.orderId = :orderId and o.subOrderId = :subOrderId and o.state = 'Failed'")
    public String getStageIdByOrderIdandSubOrderId(@Param("orderId") Long orderId, @Param("subOrderId") Long subOrderId);

    @Query("select case when count(o)>0 then true else false end from OrderStageEntity o where o.orderId=:orderId and o.state = 'Failed'")
    public boolean existsByIdandStatus(@Param("orderId") Long orderId);

    @Query("select o from OrderStageEntity o where o.orderId=:orderId and o.subOrderId in(:subOrderId) and o.state = 'Failed'")
    public List<OrderStageEntity> findSubOrdersBySubOrderId(@Param("orderId") Long orderId, @Param("subOrderId") List<Long> subOrderId);

    @Query("select o from OrderStageEntity o where o.orderId=:orderId and o.state = 'Failed'")
    public List<OrderStageEntity> findFailedSubOrdersByOrderIdandState(@Param("orderId") Long orderId);

    @Modifying(clearAutomatically = true)
    @Query("update OrderStageEntity s set s.lastModifiedBy=:lastModifiedBy where s.subOrderId=:subOrderId and s.stageCode=:stageCode")
    public int updateModifiedUserBySubOrderIdandStageId(@Param("lastModifiedBy") String lastModifiedBy, @Param("subOrderId") Long subOrderId, @Param("stageCode") String stageCode);

    @Query("select case when count(s)>=1 then true else false end from OrderStageEntity s where s.orderId = :orderId and s.state = 'In-Progress' and s.stateReason = 'Waiting for Approval'")
    public boolean findApprovalOrderByStateReason(@Param("orderId") Long orderId);

    @Query("select case when count(s)>=1 then true else false end from OrderStageEntity s where s.orderId = :orderId and s.state = 'In-Progress' and s.stateReason in('Waiting for Bank Callback','Waiting for Shop Callback','Waiting for Payment Callback')")
    public boolean findPaymentByOrderIdandStateReason(@Param("orderId") Long orderId);

    @Query("select o from OrderStageEntity o where o.orderId = :orderId and o.stageCode = :stageCode")
    public List<OrderStageEntity> findStagebyOrderIdAndName(@Param("orderId") Long orderId,
                                                            @Param("stageCode") String stageCode, Pageable pageable);

    @Modifying(clearAutomatically = true)
    @Query("update OrderStageEntity o set o.state = :state, o.rollbackStatus = :rollbackStatus where o.orderId = :orderId and o.stageCode = :stageCode")
    public int updateRollbackStatusByOrderId(@Param("state") String state,
                                             @Param("rollbackStatus") String rollbackStatus, @Param("orderId") Long orderId, @Param("stageCode") String stageCode);

    @Modifying(clearAutomatically = true)
    @Query("update OrderStageEntity o set o.state = :state, o.rollbackStatus = :rollbackStatus, o.stateReason = :stateReason where o.orderId = :orderId and o.stageCode = :stageCode")
    public int updateRollbackStatusByOrderId(@Param("state") String state,
                                             @Param("rollbackStatus") String rollbackStatus, @Param("stateReason") String stateReason,
                                             @Param("orderId") Long orderId, @Param("stageCode") String stageCode);


    @Query("select distinct new OrderStageEntity(o.stageCode,o.state) from OrderStageEntity o where o.orderId=:orderId and o.stageCode <> 'ODR_ACK'")
    public List<OrderStageEntity> findCompletedStagesByOrderId(@Param("orderId") Long orderId);

    @Query("select o.stageCode from OrderStageEntity o where o.orderId = :orderId and o.stageCode IN('SUBMIT_PORTIN_REQUEST','SUBMIT_PORTIN_INT_REQUEST')")
    public String findSubmitPortInStageId(@Param("orderId") Long orderId);

    @Modifying(clearAutomatically = true)
    @Query("delete from OrderStageEntity o  where o.orderId = :orderId and o.subOrderId = :subOrderId and o.stageCode IN('PNINV','PNREJ','PNREJ','PNCNT')")
    public void deletePortInNotificationStages(@Param("orderId") Long orderId, @Param("subOrderId") Long subOrderId);

    @Query("select distinct new OrderStageEntity(o.lastModifiedDate,o.stateReason) from OrderStageEntity o where o.orderId=:orderId and o.subOrderId=:subOrderId and o.stageCode=:stageCode")
    public OrderStageEntity findLastModifiedDateAndSateReasonByStageCode(@Param("orderId") Long orderId,@Param("subOrderId") Long subOrderId,@Param("stageCode") String stageCode);

    @Modifying(clearAutomatically = true)
    @Query("update OrderStageEntity s set s.state = :state, s.stateReason=:stateReason, s.lastModifiedBy = COALESCE(:lastModifiedBy, s.lastModifiedBy),s.lastModifiedDate= :lastModifiedDate where s.orderId = :orderId and s.stageCode=:stageCode")
    public int updateStatusAndReasonAndModifiedDateByOrderId(String state, String stateReason, long orderId, String stageCode, String lastModifiedBy, Date lastModifiedDate);
}
