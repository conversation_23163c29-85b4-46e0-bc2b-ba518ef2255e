package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.bazaarvoice.jolt.JsonUtils;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "genericTaskExecutor")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class GenericExecutionDelegate extends AbstractDelegate {

    @Override
    protected void execute() throws Exception {
//    	log.info("Received order from the message queue delegate------------ :: {} ", objectMapper.writeValueAsString(executionContext));  
        String request = getRequestFromSpec();
        if (executionContext.isError())
            return;

        var callThirdPartyDTO = callThirdParty(request);
        if (callThirdPartyDTO == null) {
            executionContext.setError(true);
            return;
        }
        var response = callThirdPartyDTO.getResponse();
//        log.info("Received order from the message queue delegate :: {} ", objectMapper.writeValueAsString(executionContext));    

      log.info("==========activityId============="+activityId);
//        if(activityId.equalsIgnoreCase("Som_FetchServiceRegistry")) {
//        	response="{\"channelId\":\"API\",\"data\":[{\"beneficiaryList\":[{\"beneficiaryMsisdn\":\"************\",\"beneficiaryQuota\":\"0\",\"beneficiaryServiceSeqId\":50000022284487,\"createDate\":\"2024-08-20T00:31:41.000Z\",\"groupSeqId\":\"G_154\",\"ocsServiceSeqId\":50000022284189,\"seqId\":12400,\"sponsorMsisdn\":\"*************\",\"status\":1,\"updateDate\":\"2024-08-20T00:31:41.000Z\"}],\"counter\":0,\"createDate\":\"2024-08-19T23:45:13.000Z\",\"groupSeqId\":\"G_154\",\"ocsServiceSeqId\":5000002228234,\"sponsorMsisdn\":\"*************\",\"sponsorServiceSeqId\":5000002228234,\"status\":1,\"typeOfMsisdn\":\"Sponsored\",\"updateDate\":\"2024-08-19T23:58:28.000Z\"}],\"draw\":0,\"entityId\":\"200\",\"last\":true,\"recordsFiltered\":1,\"recordsTotal\":1,\"requestId\":\"129719597714\",\"responseTimestamp\":\"2024-08-20T00:43:38.090Z\",\"totalPages\":1,\"transactionId\":\"139433786177840082\"}";
//        	log.info("=========response=============="+JsonUtils.toJsonString(response));
//        }
//        if(activityId.equalsIgnoreCase("BSFetchShareBundleDetails")) {
//        	response="{\"channelId\":\"API\",\"data\":[{\"beneficiaryList\":[{\"beneficiaryMsisdn\":\"************\",\"beneficiaryQuota\":\"0\",\"beneficiaryServiceSeqId\":50000022284487,\"createDate\":\"2024-08-20T00:31:41.000Z\",\"groupSeqId\":\"G_154\",\"ocsServiceSeqId\":50000022284189,\"seqId\":12400,\"sponsorMsisdn\":\"*************\",\"status\":1,\"updateDate\":\"2024-08-20T00:31:41.000Z\"}],\"counter\":0,\"createDate\":\"2024-08-19T23:45:13.000Z\",\"groupSeqId\":\"G_154\",\"ocsServiceSeqId\":5000002228234,\"sponsorMsisdn\":\"*************\",\"sponsorServiceSeqId\":5000002228234,\"status\":1,\"typeOfMsisdn\":\"Sponsored\",\"updateDate\":\"2024-08-19T23:58:28.000Z\"}],\"draw\":0,\"entityId\":\"200\",\"last\":true,\"recordsFiltered\":1,\"recordsTotal\":1,\"requestId\":\"129719597714\",\"responseTimestamp\":\"2024-08-20T00:43:38.090Z\",\"totalPages\":1,\"transactionId\":\"139433786177840082\"}";
//        	log.info("=========response=============="+JsonUtils.toJsonString(response));
//        }
//        if(activityId.equalsIgnoreCase("BS_ViewGroupSubscription")) {
//        	response="{\"draw\":0,\"recordsFiltered\":10,\"recordsTotal\":5,\"totalPages\":1,\"last\":true,\"data\":[{\"subscriptionId\":****************,\"serviceSeqId\":****************,\"serviceId\":\"**********\",\"accountId\":\"****************\",\"planId\":\"10105\",\"planName\":\"VIVIFI More - Talktime\",\"upcVersion\":1,\"planGroupId\":1,\"planGroupName\":\"POSTPAID\",\"planDesc\":\"VIVIFI More - Talktime\",\"planType\":0,\"status\":1,\"createUser\":\"icymi\",\"activationDate\":\"2024-08-20T00:34:14.000Z\",\"expiryDate\":\"2054-08-20T00:34:12.000Z\",\"createDate\":\"2024-08-20T00:34:14.000Z\",\"productId\":\"1\",\"productName\":\"POSTPAID\",\"tierbased\":\"true\",\"groupSeqId\":\"G_215\",\"ocsPlanId\":\"10105\",\"autoRenewalFlag\":1,\"orderId\":\"1275130709018222592\",\"charges\":[{\"chargeId\":\"****************\",\"subscriptionId\":****************,\"upcChargeId\":57863,\"upcChargeName\":\"T1\",\"chargeCategory\":6,\"chargeDesc\":\"ICYMI_10105_RC_CHC\",\"chargeVersion\":1,\"chargeFrequency\":2,\"quantity\":1,\"amount\":5.0,\"prorationFlag\":\"0000\",\"createUser\":\"icymi\",\"overridden\":0,\"chargeStartDate\":\"2024-08-20T00:34:14.000Z\",\"chargeEndDate\":\"2054-08-20T00:34:14.000Z\",\"createDate\":\"2024-08-20T00:34:14.000Z\",\"chargeFactor\":1,\"isProrata\":0,\"nextChargeDate\":\"2024-08-20T00:34:14.000Z\",\"chargeType\":2,\"chargeCode\":*********,\"status\":1,\"paymentCollectedUpfront\":\"0\",\"tierType\":3,\"tier1\":0,\"tier2\":12000},{\"chargeId\":\"1020000000001970\",\"subscriptionId\":****************,\"upcChargeId\":57863,\"upcChargeName\":\"T2\",\"chargeCategory\":6,\"chargeDesc\":\"ICYMI_10105_RC_CHC\",\"chargeVersion\":1,\"chargeFrequency\":2,\"quantity\":1,\"amount\":10.0,\"prorationFlag\":\"0000\",\"createUser\":\"icymi\",\"overridden\":0,\"chargeStartDate\":\"2024-08-20T00:34:14.000Z\",\"chargeEndDate\":\"2054-08-20T00:34:14.000Z\",\"createDate\":\"2024-08-20T00:34:14.000Z\",\"chargeFactor\":1,\"isProrata\":0,\"nextChargeDate\":\"2024-08-20T00:34:14.000Z\",\"chargeType\":2,\"chargeCode\":*********,\"status\":1,\"paymentCollectedUpfront\":\"0\",\"tierType\":3,\"tier1\":12001,\"tier2\":30000},{\"chargeId\":\"1020000000001972\",\"subscriptionId\":****************,\"upcChargeId\":57863,\"upcChargeName\":\"T3\",\"chargeCategory\":6,\"chargeDesc\":\"ICYMI_10105_RC_CHC\",\"chargeVersion\":1,\"chargeFrequency\":2,\"quantity\":1,\"amount\":15.0,\"prorationFlag\":\"0000\",\"createUser\":\"icymi\",\"overridden\":0,\"chargeStartDate\":\"2024-08-20T00:34:14.000Z\",\"chargeEndDate\":\"2054-08-20T00:34:14.000Z\",\"createDate\":\"2024-08-20T00:34:14.000Z\",\"chargeFactor\":1,\"isProrata\":0,\"nextChargeDate\":\"2024-08-20T00:34:14.000Z\",\"chargeType\":2,\"chargeCode\":*********,\"status\":1,\"paymentCollectedUpfront\":\"0\",\"tierType\":3,\"tier1\":30001,\"tier2\":60000}]},{\"subscriptionId\":****************,\"serviceSeqId\":****************,\"serviceId\":\"**********\",\"accountId\":\"****************\",\"planId\":\"10104\",\"planName\":\"VIVIFI More - SMS\",\"upcVersion\":1,\"planGroupId\":1,\"planGroupName\":\"POSTPAID\",\"planDesc\":\"VIVIFI More - SMS\",\"planType\":0,\"status\":1,\"createUser\":\"icymi\",\"activationDate\":\"2024-08-20T00:34:14.000Z\",\"expiryDate\":\"2054-08-20T00:34:12.000Z\",\"createDate\":\"2024-08-20T00:34:14.000Z\",\"productId\":\"1\",\"productName\":\"POSTPAID\",\"tierbased\":\"true\",\"groupSeqId\":\"G_215\",\"ocsPlanId\":\"10104\",\"autoRenewalFlag\":1,\"orderId\":\"1275130709018222592\",\"charges\":[{\"chargeId\":\"****************\",\"subscriptionId\":****************,\"upcChargeId\":57864,\"upcChargeName\":\"T1\",\"chargeCategory\":6,\"chargeDesc\":\"ICYMI_10104_RC_CHC\",\"chargeVersion\":1,\"chargeFrequency\":2,\"quantity\":1,\"amount\":1.0,\"prorationFlag\":\"0000\",\"createUser\":\"icymi\",\"overridden\":0,\"chargeStartDate\":\"2024-08-20T00:34:14.000Z\",\"chargeEndDate\":\"2054-08-20T00:34:14.000Z\",\"createDate\":\"2024-08-20T00:34:14.000Z\",\"chargeFactor\":1,\"isProrata\":0,\"nextChargeDate\":\"2024-08-20T00:34:14.000Z\",\"chargeType\":2,\"chargeCode\":*********,\"status\":1,\"paymentCollectedUpfront\":\"0\",\"tierType\":3,\"tier1\":0,\"tier2\":25},{\"chargeId\":\"1020000000001973\",\"subscriptionId\":****************,\"upcChargeId\":57864,\"upcChargeName\":\"T2\",\"chargeCategory\":6,\"chargeDesc\":\"ICYMI_10104_RC_CHC\",\"chargeVersion\":1,\"chargeFrequency\":2,\"quantity\":1,\"amount\":4.0,\"prorationFlag\":\"0000\",\"createUser\":\"icymi\",\"overridden\":0,\"chargeStartDate\":\"2024-08-20T00:34:15.000Z\",\"chargeEndDate\":\"2054-08-20T00:34:15.000Z\",\"createDate\":\"2024-08-20T00:34:15.000Z\",\"chargeFactor\":1,\"isProrata\":0,\"nextChargeDate\":\"2024-08-20T00:34:15.000Z\",\"chargeType\":2,\"chargeCode\":*********,\"status\":1,\"paymentCollectedUpfront\":\"0\",\"tierType\":3,\"tier1\":26,\"tier2\":100}]},{\"subscriptionId\":****************,\"serviceSeqId\":****************,\"serviceId\":\"**********\",\"accountId\":\"****************\",\"planId\":\"10103\",\"planName\":\"VIVIFI More - Data\",\"upcVersion\":1,\"planGroupId\":1,\"planGroupName\":\"POSTPAID\",\"planDesc\":\"VIVIFI More - Data\",\"planType\":0,\"status\":1,\"createUser\":\"icymi\",\"activationDate\":\"2024-08-20T00:34:13.000Z\",\"expiryDate\":\"2054-08-20T00:34:12.000Z\",\"createDate\":\"2024-08-20T00:34:13.000Z\",\"productId\":\"1\",\"productName\":\"POSTPAID\",\"tierbased\":\"true\",\"groupSeqId\":\"G_215\",\"ocsPlanId\":\"10103\",\"autoRenewalFlag\":1,\"orderId\":\"1275130709018222592\",\"charges\":[{\"chargeId\":\"****************\",\"subscriptionId\":****************,\"upcChargeId\":57862,\"upcChargeName\":\"T1\",\"chargeCategory\":6,\"chargeDesc\":\"ICYMI_10103_RC_CHC\",\"chargeVersion\":1,\"chargeFrequency\":2,\"quantity\":1,\"amount\":9.3458,\"prorationFlag\":\"0000\",\"createUser\":\"icymi\",\"overridden\":0,\"chargeStartDate\":\"2024-08-20T00:34:14.000Z\",\"chargeEndDate\":\"2054-08-20T00:34:14.000Z\",\"createDate\":\"2024-08-20T00:34:14.000Z\",\"chargeFactor\":1,\"isProrata\":0,\"nextChargeDate\":\"2024-08-20T00:34:14.000Z\",\"chargeType\":2,\"chargeCode\":*********,\"status\":1,\"paymentCollectedUpfront\":\"0\",\"tierType\":3,\"tier1\":0,\"tier2\":6442450944},{\"chargeId\":\"1020000000001967\",\"subscriptionId\":****************,\"upcChargeId\":57862,\"upcChargeName\":\"T3\",\"chargeCategory\":6,\"chargeDesc\":\"ICYMI_10103_RC_CHC\",\"chargeVersion\":1,\"chargeFrequency\":2,\"quantity\":1,\"amount\":28.0374,\"prorationFlag\":\"0000\",\"createUser\":\"icymi\",\"overridden\":0,\"chargeStartDate\":\"2024-08-20T00:34:14.000Z\",\"chargeEndDate\":\"2054-08-20T00:34:14.000Z\",\"createDate\":\"2024-08-20T00:34:14.000Z\",\"chargeFactor\":1,\"isProrata\":0,\"nextChargeDate\":\"2024-08-20T00:34:14.000Z\",\"chargeType\":2,\"chargeCode\":*********,\"status\":1,\"paymentCollectedUpfront\":\"0\",\"tierType\":3,\"tier1\":19327352833,\"tier2\":32212254720},{\"chargeId\":\"1020000000001968\",\"subscriptionId\":****************,\"upcChargeId\":57862,\"upcChargeName\":\"T2\",\"chargeCategory\":6,\"chargeDesc\":\"ICYMI_10103_RC_CHC\",\"chargeVersion\":1,\"chargeFrequency\":2,\"quantity\":1,\"amount\":18.6916,\"prorationFlag\":\"0000\",\"createUser\":\"icymi\",\"overridden\":0,\"chargeStartDate\":\"2024-08-20T00:34:14.000Z\",\"chargeEndDate\":\"2054-08-20T00:34:14.000Z\",\"createDate\":\"2024-08-20T00:34:14.000Z\",\"chargeFactor\":1,\"isProrata\":0,\"nextChargeDate\":\"2024-08-20T00:34:14.000Z\",\"chargeType\":2,\"chargeCode\":*********,\"status\":1,\"paymentCollectedUpfront\":\"0\",\"tierType\":3,\"tier1\":**********,\"tier2\":***********}]},{\"subscriptionId\":****************,\"serviceSeqId\":****************,\"serviceId\":\"**********\",\"accountId\":\"****************\",\"planId\":\"10854\",\"planName\":\"Promo - 150GB (Promo)\",\"upcVersion\":1,\"planGroupId\":1,\"planGroupName\":\"POSTPAID\",\"planDesc\":\"Promo - 150GB (Promo)\",\"planType\":0,\"status\":1,\"createUser\":\"CRM\",\"activationDate\":\"2024-08-19T23:58:29.000Z\",\"expiryDate\":\"2025-02-19T23:48:55.000Z\",\"createDate\":\"2024-08-19T23:58:29.000Z\",\"productId\":\"1\",\"productName\":\"POSTPAID\",\"tierbased\":\"false\",\"groupSeqId\":\"G_215\",\"ocsPlanId\":\"10854\",\"autoRenewalFlag\":1,\"orderId\":\"1275121667042516992\",\"charges\":[{\"chargeId\":\"****************\",\"subscriptionId\":****************,\"upcChargeId\":41033,\"upcChargeName\":\"ICYMI_10854_RC_CH\",\"chargeCategory\":2,\"chargeDesc\":\"ICYMI_10854_RC_CH\",\"chargeVersion\":1,\"chargeFrequency\":2,\"quantity\":1,\"amount\":0.0,\"prorationFlag\":\"0000\",\"createUser\":\"CRM\",\"overridden\":0,\"chargeStartDate\":\"2024-08-19T23:58:29.000Z\",\"chargeEndDate\":\"2054-08-19T23:48:56.000Z\",\"createDate\":\"2024-08-19T23:58:29.000Z\",\"chargeFactor\":1,\"isProrata\":1,\"nextChargeDate\":\"2024-08-19T23:48:56.000Z\",\"chargeType\":2,\"chargeCode\":*********,\"status\":1,\"paymentCollectedUpfront\":\"0\"}]},{\"subscriptionId\":****************,\"serviceSeqId\":****************,\"serviceId\":\"**********\",\"accountId\":\"****************\",\"planId\":\"10956\",\"planName\":\"VIVIFI Share (CIS) \",\"upcVersion\":1,\"planGroupId\":1,\"planGroupName\":\"POSTPAID\",\"planDesc\":\"220GB, 1000 mins, 100 SMS\",\"planType\":0,\"status\":1,\"createUser\":\"CRM\",\"activationDate\":\"2024-08-19T23:58:29.000Z\",\"expiryDate\":\"2054-08-19T23:45:13.000Z\",\"createDate\":\"2024-08-19T23:58:29.000Z\",\"productId\":\"1\",\"productName\":\"POSTPAID\",\"tierbased\":\"false\",\"groupSeqId\":\"G_215\",\"ocsPlanId\":\"10956\",\"autoRenewalFlag\":1,\"orderId\":\"1275121667042516992\",\"charges\":[{\"chargeId\":\"****************\",\"subscriptionId\":****************,\"upcChargeId\":47107,\"upcChargeName\":\"IC_10956_RC_CH\",\"chargeCategory\":2,\"chargeDesc\":\"IC_10956_RC_CH\",\"chargeVersion\":1,\"chargeFrequency\":2,\"quantity\":1,\"amount\":37.6147,\"prorationFlag\":\"0000\",\"createUser\":\"CRM\",\"overridden\":0,\"chargeStartDate\":\"2024-08-19T23:58:29.000Z\",\"chargeEndDate\":\"2054-08-19T23:45:14.000Z\",\"createDate\":\"2024-08-19T23:58:29.000Z\",\"chargeFactor\":1,\"isProrata\":1,\"nextChargeDate\":\"2024-08-19T23:45:14.000Z\",\"chargeType\":2,\"chargeCode\":100192827,\"status\":1,\"paymentCollectedUpfront\":\"0\"}]}],\"channelId\":\"API\",\"entityId\":\"41016779\",\"requestId\":\"129719597714\",\"transactionId\":\"139433786181430084\",\"responseTimestamp\":\"2024-08-20T00:43:39.301Z\"}";
//        }
//       if(activityId.equalsIgnoreCase("SOMFetchServiceRegistry")) {
//        	response="[{\"id\":\"39010490432e4b2c8cce50adb94b8eea\",\"category\":\"CFS\",\"name\":\"CFSS_DATA\",\"serviceCharacteristic\":[{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"****************\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"id\":\"9\",\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/9\",\"name\":\"CFSS_DATA\"},\"state\":\"active\"},{\"id\":\"7b0973d1f5bd4b158f34ea32b408eec9\",\"category\":\"CFS\",\"name\":\"CFSS_SMS_OUTGOING\",\"serviceCharacteristic\":[{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1030000000000221\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"id\":\"21\",\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/21\",\"name\":\"CFSS_SMS_OUTGOING\"},\"state\":\"active\"},{\"id\":\"d253df52e7494ec789ba1a08e7520d54\",\"category\":\"CFS\",\"name\":\"CFSS_PROFILE\",\"serviceCharacteristic\":[{\"name\":\"baseprofile\",\"value\":\"31\"},{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"****************\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"id\":\"27\",\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/27\",\"name\":\"CFSS_PROFILE\"},\"state\":\"active\"},{\"id\":\"398795de49154e5d993b8cc04db2b372\",\"category\":\"CFS\",\"name\":\"CFSS_VOICE_INCOMING\",\"serviceCharacteristic\":[{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1030000000000221\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"id\":\"23\",\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/23\",\"name\":\"CFSS_VOICE_INCOMING\"},\"state\":\"active\"},{\"id\":\"d206b6b6dfe34f2fbcfe31bf5ebd86fc\",\"category\":\"CFS\",\"name\":\"CFSS_PCRF\",\"serviceCharacteristic\":[{\"name\":\"TENANT_ID\",\"value\":\"41128709\"},{\"name\":\"LANGUAGE\",\"value\":\"Eng\"},{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1030000000000221\"},{\"name\":\"POLICY_NAME\",\"value\":\"10858\"},{\"name\":\"START_DATE\",\"value\":\"09-07-2024 16:33:58\"},{\"name\":\"END_DATE\",\"value\":\"09-07-2054 16:33:35\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"id\":\"4002\",\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/4002\",\"name\":\"CFSS_PCRF\"},\"state\":\"active\"},{\"id\":\"65cd94a5d4f841c080f6e320687abe4e\",\"category\":\"CFS\",\"name\":\"CFSS_VOICE_OUTGOING\",\"serviceCharacteristic\":[{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1030000000000221\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"id\":\"25\",\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/25\",\"name\":\"CFSS_VOICE_OUTGOING\"},\"state\":\"active\"},{\"id\":\"b29360a0bed3461ca140bca50c9465bf\",\"category\":\"CFS\",\"name\":\"CFSS_SMS_INCOMING\",\"serviceCharacteristic\":[{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1030000000000221\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"id\":\"19\",\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/19\",\"name\":\"CFSS_SMS_INCOMING\"},\"state\":\"active\"},{\"id\":\"521ae54e829a465d9e44970e305ba415\",\"category\":\"LRS\",\"name\":\"LRS_MSISDN\",\"serviceCharacteristic\":[{\"name\":\"MSISDN\",\"value\":\"6580909928\"},{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1030000000000221\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"id\":\"6\",\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/6\",\"name\":\"LRS_MSISDN\"},\"state\":\"active\"},{\"id\":\"81c5c3ae432d4693be2d2838e0266c8f\",\"category\":\"PRS\",\"name\":\"PRS_SIM\",\"serviceCharacteristic\":[{\"name\":\"IMSI\",\"value\":\"525016146000035\"},{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1030000000000221\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"id\":\"5\",\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/5\",\"name\":\"PRS_SIM\"},\"state\":\"active\"},{\"id\":\"e4eee108a0184a33889fb6838270bcba\",\"category\":\"CFS\",\"name\":\"CFSS_VOLTE\",\"serviceCharacteristic\":[{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1030000000000221\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"id\":\"17\",\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/17\",\"name\":\"CFSS_VOLTE\"},\"state\":\"active\"},{\"id\":\"ad05360eb50048208392e88710ea8a16\",\"category\":\"CFS\",\"name\":\"CFSS_LMP_DB_UPDATE\",\"serviceCharacteristic\":[{\"name\":\"ownerTelco\",\"valueType\":\"Enum\",\"value\":\"\"},{\"name\":\"sourceTelco\",\"valueType\":\"Enum\",\"value\":\"\"},{\"name\":\"destTelco\",\"valueType\":\"Enum\",\"value\":\"\"},{\"name\":\"portIdentifier\",\"valueType\":\"String\",\"value\":\"\"},{\"name\":\"SUBSCRIPTION_ID\",\"valueType\":\"String\",\"value\":\"1030000000000221\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"id\":\"4008\",\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/4008\",\"name\":\"CFSS_LMP_DB_UPDATE\"},\"state\":\"active\"},{\"id\":\"7db317239f2d46a988d64960f341fb0a\",\"category\":\"CFS\",\"name\":\"CFSS_CNS\",\"serviceCharacteristic\":[{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1030000000000222\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"id\":\"29\",\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/29\",\"name\":\"CFSS_CNS\"},\"state\":\"active\"},{\"id\":\"8f493c24dbc44304929f9f0f6a321f66\",\"category\":\"CFS\",\"name\":\"CFSS_ROAMING\",\"serviceCharacteristic\":[{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1030000000000223\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"id\":\"35\",\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/35\",\"name\":\"CFSS_ROAMING\"},\"state\":\"active\"},{\"id\":\"c0aba1d83a5040e088fc29b39f961b75\",\"category\":\"CFS\",\"name\":\"CFSS_IDD\",\"serviceCharacteristic\":[{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1030000000000224\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"id\":\"4010\",\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/4010\",\"name\":\"CFSS_IDD\"},\"state\":\"active\"}]";
//        	log.info("=========response=============="+JsonUtils.toJsonString(response));
//        	 log.info("Received order from the message queue delegate :: {} ", objectMapper.writeValueAsString(executionContext));    
//        }
    
    if(activityId.equalsIgnoreCase("SOMFetchServiceRegistry")) {
    	response="[{\"category\":\"CFS\",\"id\":\"61a0adbebbe846e280d6629c8ea2e693\",\"name\":\"CFSS_DATA\",\"serviceCharacteristic\":[{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1020000000001906\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/1\",\"id\":\"1\",\"name\":\"CFSS_DATA\"},\"state\":\"active\"},{\"category\":\"CFS\",\"id\":\"90bfbb06445a47bbb1c048df65a964b0\",\"name\":\"CFSS_PROFILE\",\"serviceCharacteristic\":[{\"name\":\"baseprofile\",\"value\":\"34\"},{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"2030000000001194\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/1\",\"id\":\"1\",\"name\":\"CFSS_PROFILE\"},\"state\":\"active\"},{\"category\":\"LRS\",\"id\":\"25bf0123a4954241ae9ca031fff1ee7c\",\"name\":\"LRS_MSISDN\",\"serviceCharacteristic\":[{\"name\":\"MSISDN\",\"value\":\"6593211197\"},{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1020000000001910\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/1\",\"id\":\"1\",\"name\":\"LRS_MSISDN\"},\"state\":\"active\"},{\"category\":\"CFS\",\"id\":\"7665a39ba4184a4f820d2f7577783422\",\"name\":\"CFSS_PCRF\",\"serviceCharacteristic\":[{\"name\":\"TENANT_ID\",\"value\":\"41130792\"},{\"name\":\"LANGUAGE\",\"value\":\"Eng\"},{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"2030000000001194\"},{\"name\":\"POLICY_NAME\",\"value\":\"10754\"},{\"name\":\"START_DATE\",\"value\":\"31-12-2024 17:28:29\"},{\"name\":\"END_DATE\",\"value\":\"31-12-2054 17:26:55\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/1\",\"id\":\"1\",\"name\":\"CFSS_PCRF\"},\"state\":\"active\"},{\"category\":\"PRS\",\"id\":\"d1115b73018249ab8f3a5d730e32c2a7\",\"name\":\"PRS_SIM\",\"serviceCharacteristic\":[{\"name\":\"IMSI\",\"value\":\"525074231345434\"},{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1020000000001906\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/1\",\"id\":\"1\",\"name\":\"PRS_SIM\"},\"state\":\"active\"},{\"category\":\"CFS\",\"id\":\"09ab64dcdc694b2899c6051f4a33ba29\",\"name\":\"CFSS_PCRF_ADDON\",\"serviceCharacteristic\":[{\"name\":\"POLICY_NAME\",\"value\":\"10756\"},{\"name\":\"START_DATE\",\"value\":\"31-12-2024 17:30:38\"},{\"name\":\"END_DATE\",\"value\":\"31-12-2026 17:30:38\"},{\"name\":\"SUBSCRIPTION_ID\",\"value\":\"1020000000001906\"},{\"name\":\"parentalPolicy\",\"value\":\"\"}],\"serviceRelationship\":[],\"serviceSpecification\":{\"href\":\"http://som-canvas-starhub-sg-cmp-int-shcmp-dev-01.apps.ocplab.6d.local/som-servicecatalogue-svc/ServiceSpecificationActive/1\",\"id\":\"1\",\"name\":\"CFSS_PCRF_ADDON\"},\"state\":\"active\"}]";
//    	log.info("=========response=============="+JsonUtils.toJsonString(response));
//    	 log.info("Received order from the message queue delegate :: {} ", objectMapper.writeValueAsString(executionContext));    
    }
//        log.info("Received order from the message queue delegate :: {} ", objectMapper.writeValueAsString(executionContext));    
//        if(activityId.equalsIgnoreCase("Arm_FetchAssetDetails")) {
//        	response="[{\"data\":[{\"assetDetails\":[{\"allowCharacter\":true,\"allowNumber\":false,\"allowSpecialCharacter\":true,\"attributeType\":\"SINGLE_LINE\",\"attributeValue\":\"3457010\",\"id\":5953055,\"mandatory\":true,\"productAttributeId\":72,\"productAttributeName\":\"PIN2\",\"unitField\":false,\"unitFieldValue\":null,\"valueByDefault\":null},{\"allowCharacter\":true,\"allowNumber\":false,\"allowSpecialCharacter\":true,\"attributeType\":\"SINGLE_LINE\",\"attributeValue\":\"6787104\",\"id\":5953057,\"mandatory\":true,\"productAttributeId\":73,\"productAttributeName\":\"PUK1\",\"unitField\":false,\"unitFieldValue\":null,\"valueByDefault\":null},{\"allowCharacter\":true,\"allowNumber\":false,\"allowSpecialCharacter\":true,\"attributeType\":\"SINGLE_LINE\",\"attributeValue\":\"675597000\",\"id\":5953059,\"mandatory\":true,\"productAttributeId\":74,\"productAttributeName\":\"PUK2\",\"unitField\":false,\"unitFieldValue\":null,\"valueByDefault\":null},{\"allowCharacter\":true,\"allowNumber\":false,\"allowSpecialCharacter\":true,\"attributeType\":\"SINGLE_LINE\",\"attributeValue\":\"4789479478\",\"id\":5953061,\"mandatory\":true,\"productAttributeId\":75,\"productAttributeName\":\"KI\",\"unitField\":false,\"unitFieldValue\":null,\"valueByDefault\":null},{\"allowCharacter\":true,\"allowNumber\":false,\"allowSpecialCharacter\":true,\"attributeType\":\"SINGLE_LINE\",\"attributeValue\":\"525074231345496\",\"id\":5953063,\"mandatory\":true,\"productAttributeId\":76,\"productAttributeName\":\"IMSI\",\"unitField\":false,\"unitFieldValue\":null,\"valueByDefault\":null},{\"allowCharacter\":true,\"allowNumber\":false,\"allowSpecialCharacter\":true,\"attributeType\":\"SINGLE_LINE\",\"attributeValue\":\"3457020\",\"id\":14536066,\"mandatory\":true,\"productAttributeId\":544,\"productAttributeName\":\"PIN1\",\"unitField\":false,\"unitFieldValue\":null,\"valueByDefault\":null}],\"assetName\":\"PAIRED_SIM_8978913140876345495\",\"billId\":null,\"createdAt\":\"2024-05-03 18:25:05\",\"createdBy\":\"Admin\",\"customInt1\":0,\"customInt2\":0,\"customInt3\":0,\"customInt4\":0,\"customInt5\":0,\"customString1\":null,\"customString10\":null,\"customString2\":null,\"customString3\":\"1\",\"customString4\":null,\"customString5\":null,\"customString6\":null,\"customString7\":null,\"customString8\":null,\"customString9\":null,\"deliveryOrderNumber\":null,\"description\":\"Lorem Ipsum is simply dummy text of the printing and typesetting industry.\",\"entityId\":\"41130792\",\"expiryDate\":\"2024-12-30 22:00:00\",\"id\":14217019,\"imsi1\":\"525074231345496\",\"imsi10\":null,\"imsi2\":null,\"imsi3\":null,\"imsi4\":null,\"imsi5\":null,\"imsi6\":null,\"imsi7\":null,\"imsi8\":null,\"imsi9\":null,\"ownerId\":1,\"ownerName\":\"kishore12\",\"ownerTypeId\":6,\"ownerTypeName\":\"Store\",\"pairedEntity\":null,\"productAveragePrice\":null,\"productCode\":\"PREPAIRED_SIM\",\"productId\":28,\"productName\":\"PAIRED_SIM\",\"productPurchasePrice\":0.0,\"productSalesPrice\":null,\"productTypeId\":2,\"provisioningStatus\":null,\"purchaseOrderId\":\"0\",\"returnAssetStatus\":null,\"salesOrderId\":null,\"serialnumber\":\"8978913140876345495\",\"serialnumberNumeric\":8978913140876345495,\"statusId\":4,\"statusName\":\"New\",\"updatedBy\":\"Admin\",\"userId\":null,\"vendorId\":2,\"warehouseId\":1}],\"errors\":null,\"linkId\":\"***************\",\"message\":\"SUCCESS\",\"status\":true}]";
//        }
//        if(activityId.equalsIgnoreCase("BSCreateService")) {
//        	response="{\"channelId\":\"zerouser\",\"entityId\":\"1000\",\"requestId\":\"********************\",\"response\":{\"accountId\":\"********\",\"serviceId\":\"**********\",\"serviceSeqId\":****************,\"subscriptions\":[{\"accountId\":\"********\",\"activationDate\":\"2024-08-26T17:39:42.404Z\",\"charges\":[{\"amount\":0.0,\"chargeCategory\":1,\"chargeCode\":13,\"chargeDesc\":\"Registration Fee\",\"chargeEndDate\":\"2054-08-27T16:17:20.859Z\",\"chargeFactor\":1,\"chargeFrequency\":2,\"chargeId\":\"****************\",\"chargeName\":\"Registration Fee\",\"chargeStartDate\":\"2024-08-27T16:17:20.859Z\",\"chargeType\":1,\"chargeVersion\":1,\"createDate\":\"2024-08-27T16:17:20.859Z\",\"createUser\":\"Valerie Tan\",\"isProrata\":0,\"nextChargeDate\":\"2024-08-27T16:17:20.859Z\",\"overridden\":0,\"paymentCollectedUpfront\":\"0\",\"prorationFlag\":\"0000\",\"quantity\":1,\"status\":1,\"subscriptionId\":****************,\"upcChargeId\":24083,\"upcChargeName\":\"Registration Fee\"},{\"amount\":6.4815,\"chargeCategory\":2,\"chargeCode\":1,\"chargeDesc\":\"Monthly Fee\",\"chargeEndDate\":\"2054-08-27T16:17:20.925Z\",\"chargeFactor\":1,\"chargeFrequency\":2,\"chargeId\":\"****************\",\"chargeName\":\"Monthly Fee\",\"chargeStartDate\":\"2024-08-27T16:17:20.925Z\",\"chargeType\":2,\"chargeVersion\":1,\"createDate\":\"2024-08-27T16:17:20.925Z\",\"createUser\":\"Valerie Tan\",\"isProrata\":1,\"nextChargeDate\":\"2024-08-27T16:17:20.925Z\",\"overridden\":0,\"paymentCollectedUpfront\":\"0\",\"prorationFlag\":\"0000\",\"quantity\":1,\"status\":1,\"subscriptionId\":****************,\"upcChargeId\":24085,\"upcChargeName\":\"Monthly Fee\"}],\"createDate\":\"2024-08-27T16:17:20.850Z\",\"createUser\":\"Valerie Tan\",\"expiryDate\":\"2054-08-26T17:39:42.404Z\",\"ocsPlanId\":\"10375\",\"planDesc\":\"Zero1 Starter\",\"planGroupId\":1,\"planGroupName\":\"POSTPAID\",\"planId\":\"10375\",\"planName\":\"Zero1 Starter\",\"planType\":1,\"productId\":\"1\",\"productName\":\"POSTPAID\",\"serviceId\":\"**********\",\"serviceSeqId\":****************,\"status\":1,\"subscriptionId\":****************,\"tierbased\":\"false\",\"upcVersion\":1},{\"accountId\":\"********\",\"activationDate\":\"2024-08-26T17:39:42.405Z\",\"charges\":[{\"amount\":0.0,\"chargeCategory\":2,\"chargeCode\":1,\"chargeDesc\":\"Monthly Fee\",\"chargeEndDate\":\"2054-08-27T16:17:21.082Z\",\"chargeFactor\":1,\"chargeFrequency\":2,\"chargeId\":\"****************\",\"chargeName\":\"Monthly Fee\",\"chargeStartDate\":\"2024-08-27T16:17:21.082Z\",\"chargeType\":2,\"chargeVersion\":1,\"createDate\":\"2024-08-27T16:17:21.082Z\",\"createUser\":\"Valerie Tan\",\"isProrata\":0,\"nextChargeDate\":\"2024-08-27T16:17:21.082Z\",\"overridden\":0,\"paymentCollectedUpfront\":\"0\",\"prorationFlag\":\"0000\",\"quantity\":1,\"status\":1,\"subscriptionId\":****************,\"upcChargeId\":24009,\"upcChargeName\":\"Monthly Fee\"}],\"createDate\":\"2024-08-27T16:17:21.011Z\",\"createUser\":\"Valerie Tan\",\"expiryDate\":\"2054-08-26T17:39:42.405Z\",\"ocsPlanId\":\"811779\",\"planDesc\":\"Caller ID\",\"planGroupId\":1,\"planGroupName\":\"POSTPAID\",\"planId\":\"811779\",\"planName\":\"Caller ID\",\"planType\":0,\"productId\":\"1\",\"productName\":\"POSTPAID\",\"serviceId\":\"**********\",\"serviceSeqId\":****************,\"status\":1,\"subscriptionId\":****************,\"tierbased\":\"false\",\"upcVersion\":1},{\"accountId\":\"********\",\"activationDate\":\"2024-08-26T17:39:42.405Z\",\"charges\":[{\"amount\":0.0,\"chargeCategory\":2,\"chargeCode\":*********,\"chargeDesc\":\"Monthly Fee\",\"chargeEndDate\":\"2054-08-27T16:17:21.188Z\",\"chargeFactor\":1,\"chargeFrequency\":2,\"chargeId\":\"****************\",\"chargeName\":\"Monthly Fee\",\"chargeStartDate\":\"2024-08-27T16:17:21.188Z\",\"chargeType\":2,\"chargeVersion\":1,\"createDate\":\"2024-08-27T16:17:21.188Z\",\"createUser\":\"Valerie Tan\",\"isProrata\":0,\"nextChargeDate\":\"2024-08-27T16:17:21.188Z\",\"overridden\":0,\"paymentCollectedUpfront\":\"0\",\"prorationFlag\":\"0000\",\"quantity\":1,\"status\":1,\"subscriptionId\":****************,\"upcChargeId\":39011,\"upcChargeName\":\"Monthly Fee\"}],\"createDate\":\"2024-08-27T16:17:21.154Z\",\"createUser\":\"Valerie Tan\",\"expiryDate\":\"2054-08-26T17:39:42.405Z\",\"ocsPlanId\":\"811889\",\"planDesc\":\"Global Roaming\",\"planGroupId\":1,\"planGroupName\":\"POSTPAID\",\"planId\":\"811889\",\"planName\":\"Global Roaming\",\"planType\":0,\"productId\":\"1\",\"productName\":\"POSTPAID\",\"serviceId\":\"**********\",\"serviceSeqId\":****************,\"status\":1,\"subscriptionId\":****************,\"tierbased\":\"false\",\"upcVersion\":1},{\"accountId\":\"********\",\"activationDate\":\"2024-08-26T17:39:42.405Z\",\"charges\":[{\"amount\":0.0,\"chargeCategory\":2,\"chargeCode\":*********,\"chargeDesc\":\"Monthly Fee\",\"chargeEndDate\":\"2054-08-27T16:17:21.306Z\",\"chargeFactor\":1,\"chargeFrequency\":2,\"chargeId\":\"****************\",\"chargeName\":\"Monthly Fee\",\"chargeStartDate\":\"2024-08-27T16:17:21.306Z\",\"chargeType\":2,\"chargeVersion\":1,\"createDate\":\"2024-08-27T16:17:21.306Z\",\"createUser\":\"Valerie Tan\",\"isProrata\":0,\"nextChargeDate\":\"2024-08-27T16:17:21.306Z\",\"overridden\":0,\"paymentCollectedUpfront\":\"0\",\"prorationFlag\":\"0000\",\"quantity\":1,\"status\":1,\"subscriptionId\":****************,\"upcChargeId\":39009,\"upcChargeName\":\"Monthly Fee\"}],\"createDate\":\"2024-08-27T16:17:21.238Z\",\"createUser\":\"Valerie Tan\",\"expiryDate\":\"2054-08-26T17:39:42.405Z\",\"ocsPlanId\":\"811887\",\"planDesc\":\"IDD_Calls\",\"planGroupId\":1,\"planGroupName\":\"POSTPAID\",\"planId\":\"811887\",\"planName\":\"IDD_Calls\",\"planType\":0,\"productId\":\"1\",\"productName\":\"POSTPAID\",\"serviceId\":\"**********\",\"serviceSeqId\":****************,\"status\":1,\"subscriptionId\":****************,\"tierbased\":\"false\",\"upcVersion\":1}]},\"responseCode\":200,\"responseMessage\":\"Success\",\"responseTimestamp\":\"2024-08-27T16:17:21.392Z\",\"transactionId\":\"139440394407970192\"}";
//        }
////        if(activityId.equalsIgnoreCase("BSAddSubscription")) {
//        	response="{\"channelId\":\"CRM\",\"entityId\":\"200\",\"requestId\":\"65597\",\"transactionId\":\"139568221454430190\",\"responseTimestamp\":\"2025-01-22T07:02:25.510Z\",\"responseCode\":200,\"responseMessage\":\"Success\",\"response\":{\"accountId\":\"6dbss43735\",\"subscriptions\":[{\"subscriptionId\":****************,\"serviceSeqId\":*************,\"serviceId\":\"*************\",\"accountId\":\"6dbss43735\",\"planId\":\"testfalse\",\"planName\":\"Addon_mutliple_false\",\"upcVersion\":1,\"planGroupId\":1,\"planGroupName\":\"PostPaid\",\"planDesc\":\"Addon_mutliple_false\",\"planType\":0,\"status\":1,\"createUser\":\"admin\",\"activationDate\":\"2025-01-22T12:32:23.242Z\",\"expiryDate\":\"2055-01-22T12:32:23.246Z\",\"createDate\":\"2025-01-22T12:32:25.486Z\",\"productId\":\"1\",\"productName\":\"PostPaid\",\"tierbased\":\"false\",\"ocsPlanId\":\"1249\",\"autoRenewalFlag\":0,\"charges\":[{\"chargeId\":\"****************\",\"subscriptionId\":****************,\"upcChargeId\":43,\"upcChargeName\":\"CMP_4G_3GB_170_REIL\",\"chargeCategory\":1,\"chargeDesc\":\"CMP_4G_3GB_170_REIL\",\"chargeVersion\":1,\"chargeFrequency\":2,\"quantity\":1,\"amount\":170.0,\"prorationFlag\":\"0000\",\"createUser\":\"admin\",\"overridden\":0,\"chargeStartDate\":\"2025-01-22T12:32:25.499Z\",\"chargeEndDate\":\"2055-01-22T12:32:25.499Z\",\"createDate\":\"2025-01-22T12:32:25.500Z\",\"chargeFactor\":1,\"isProrata\":1,\"nextChargeDate\":\"2025-01-22T12:32:25.499Z\",\"chargeType\":2,\"chargeCode\":5680,\"status\":1,\"paymentCollectedUpfront\":\"0\",\"chargeName\":\"CMP_4G_3GB_170_REIL\"}]}],\"serviceSeqId\":*************,\"serviceId\":\"*************\"}}";
//        }
//        if(activityId.equalsIgnoreCase("BSAddSubscription")) {
//        	response="{\"channelId\":\"CRM\",\"entityId\":\"200\",\"requestId\":\"65597\",\"transactionId\":\"139568221454430190\",\"responseTimestamp\":\"2025-01-22T07:02:25.510Z\",\"responseCode\":200,\"responseMessage\":\"Success\",\"response\":{\"accountId\":\"6dbss43735\",\"subscriptions\":[{\"subscriptionId\":****************,\"serviceSeqId\":*************,\"serviceId\":\"*************\",\"accountId\":\"6dbss43735\",\"planId\":\"testfalse\",\"planName\":\"Addon_mutliple_false\",\"upcVersion\":1,\"planGroupId\":1,\"planGroupName\":\"PostPaid\",\"planDesc\":\"Addon_mutliple_false\",\"planType\":0,\"status\":1,\"createUser\":\"admin\",\"activationDate\":\"2025-01-22T12:32:23.242Z\",\"expiryDate\":\"2055-01-22T12:32:23.246Z\",\"createDate\":\"2025-01-22T12:32:25.486Z\",\"productId\":\"1\",\"productName\":\"PostPaid\",\"tierbased\":\"false\",\"ocsPlanId\":\"1249\",\"autoRenewalFlag\":0,\"charges\":[{\"chargeId\":\"****************\",\"subscriptionId\":****************,\"upcChargeId\":43,\"upcChargeName\":\"CMP_4G_3GB_170_REIL\",\"chargeCategory\":1,\"chargeDesc\":\"CMP_4G_3GB_170_REIL\",\"chargeVersion\":1,\"chargeFrequency\":2,\"quantity\":1,\"amount\":170.0,\"prorationFlag\":\"0000\",\"createUser\":\"admin\",\"overridden\":0,\"chargeStartDate\":\"2025-01-22T12:32:25.499Z\",\"chargeEndDate\":\"2055-01-22T12:32:25.499Z\",\"createDate\":\"2025-01-22T12:32:25.500Z\",\"chargeFactor\":1,\"isProrata\":1,\"nextChargeDate\":\"2025-01-22T12:32:25.499Z\",\"chargeType\":2,\"chargeCode\":5680,\"status\":1,\"paymentCollectedUpfront\":\"0\",\"chargeName\":\"CMP_4G_3GB_170_REIL\"}]}],\"serviceSeqId\":*************,\"serviceId\":\"*************\"}}";
//        }
        
//        if(activityId.equalsIgnoreCase("OCS_ChnageOwnership")) {
//        	response="{\"accountBalance\":[],\"accountRelationship\":[],\"contact\":[],\"characteristic\":[{\"name\":\"externalServiceId\",\"value\":\"*************\"},{\"name\":\"resultCode\",\"value\":\"1\"},{\"name\":\"resultParamCode\",\"value\":\"3190\"},{\"name\":\"resultDescription\",\"value\":\" Group Validation Failure\"}],\"paymentPlan\":[],\"relatedParty\":[],\"taxExemption\":[],\"responseHeader\":{\"requestId\":\"09834\",\"responeId\":\"09834\",\"responseMessage\":\" Group Validation Failure\",\"responseStatus\":\"3190\",\"responseTimestamp\":\"2024-08-20T21:18:57.543Z\",\"source\":\"OCS\"}}";
//        }
//        if(activityId.equalsIgnoreCase("OCSUpdateSubscriptionId")) {
//        	response="{\"feature\":[],\"note\":[],\"place\":[],\"relatedEntity\":[],\"relatedParty\":[],\"serviceCharacteristic\":[{\"name\":\"externalServiceId\",\"characteristicRelationship\":[],\"value\":\"************\",\"arrayIndex\":\"-1\"},{\"name\":\"resultParamCode\",\"characteristicRelationship\":[],\"value\":\"3344\"}],\"serviceOrderItem\":[],\"serviceRelationship\":[],\"supportingResource\":[],\"supportingService\":[],\"responseHeader\":{\"requestId\":\"09834\",\"responeId\":\"09834\",\"responseMessage\":\"PlanHistoryNotExists\",\"responseStatus\":\"3344\",\"responseTimestamp\":\"2024-08-20T21:18:59.788Z\",\"source\":\"OCS\"}}";
//        }
        if(activityId.equalsIgnoreCase("BSCreateProfile")) {
        	response="{\"responseCode\":200,\"responseMessage\":\"Success\",\"response\":{\"accountSeqId\":72911,\"accountId\":\"1008889\",\"title\":\"string\",\"name\":\"test account\",\"status\":0,\"profileId\":***********,\"taxCode\":0,\"taxExemptionCode\":0,\"excelFileFlag\":0,\"riskcategoryId\":1,\"dunningExclusion\":0,\"dispatchMode\":0,\"languageId\":1,\"itemizedBillRequired\":\"0\",\"chargingPattern\":1,\"emailNotificationAllowed\":0,\"smsNotificationAllowed\":1,\"createDate\":\"2025-06-19T06:57:48.999Z\",\"activationDate\":\"2025-06-19T06:57:48.999Z\",\"createUser\":\"admin\",\"creditLimit\":\"10\",\"preferredCurrency\":\"string\",\"billingRegion\":\"string\",\"contactEmailId\":\"<EMAIL>\",\"smsNumbers\":\"***********\",\"billcycleId\":165,\"remarks\":\"string\",\"creditClass\":\"B\",\"unicodeName\":\"string\",\"billingOnHold\":0,\"indunning\":0,\"dunningId\":0,\"majorSicCode\":1,\"sicCode\":1,\"writeOffFlag\":0,\"entityId\":200,\"irnEnable\":1,\"addresses\":[{\"addressId\":************,\"subscriber\":\"1008889\",\"subscriberLevel\":\"1\",\"addressType\":\"2\",\"addrLine1\":\"house no 268\",\"addrLine2\":\"1000 ADDIS ABABA\",\"addrLine3\":\"Kebele 1, 1302/29\",\"addrLine4\":\"Addis Ababa\",\"addrLine5\":\"Addis Ababa\",\"addrLine6\":\"Shoa\",\"addrLine7\":\"Achefer\",\"addrLine8\":\"1000\",\"languageId\":\"0\",\"createUser\":\"admin\",\"updateUser\":\"admin\",\"createDate\":\"2025-06-19T06:57:49.095Z\",\"updateDate\":\"2025-06-19T06:57:49.095Z\",\"addrLine9\":\"P.O. Box 1519\",\"addrLine10\":\"8.9806\",\"addrLine11\":\"38.7578\"}],\"taxApplicable\":0,\"autoDebit\":1,\"bankAccountNo\":\"************\",\"bankID\":\"1221\",\"bankAccountName\":\"abc\",\"branchName\":\"south branch\",\"bankAccountType\":\"Current\",\"keyAccountManager\":\"\",\"isStarterPack\":0,\"writeOffDate\":\"2025-06-19T06:57:48.999Z\",\"lateFeeNotApplicable\":0,\"creditManager\":\"string\",\"exciseDutyApplicable\":0,\"exciseDutyPlanGroup\":0},\"channelId\":\"CRM\",\"entityId\":\"200\",\"requestId\":\"**********\",\"transactionId\":\"139696090687390239\",\"responseTimestamp\":\"2025-06-19T06:57:49.321Z\"}\r\n";
        			       }
//        validateResponse(callThirdPartyDTO);
//        if (executionContext.isError())
//            return;
        log.info("Received order from the message queue delegate :: {} ", objectMapper.writeValueAsString(executionContext));    
        checkViewSubscriptionResponseEmptyOrNot(response);
        updateSOMCancelSubProcessVariables(response);
        modifyWorkflowData(response);
        extractSubscriptionIds(response);
        getNMSProcessVariable(response);
        processThirdPartyResponse();

    }

	

}
