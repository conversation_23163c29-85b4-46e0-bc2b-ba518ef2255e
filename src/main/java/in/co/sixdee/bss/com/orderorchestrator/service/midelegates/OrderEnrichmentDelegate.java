package in.co.sixdee.bss.com.orderorchestrator.service.midelegates;

import com.fasterxml.jackson.core.JsonProcessingException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderEnrichment;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderPayloadService;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.AbstractDelegate;
import in.co.sixdee.bss.common.vaidation.RequestValidationException;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.stream.Collectors;

@Log4j2
@Component(value = "orderEnrichmentDelegate")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OrderEnrichmentDelegate extends AbstractDelegate {

    @Autowired
    protected OrderEnrichment orderEnrichment;

    @Autowired
    protected OrderPayloadService orderPayloadService;

    @Override
    protected void execute() throws Exception {
        try {
            orderEnrichment.enrichOrder(executionContext);
            if (executionContext.isEnrichmentError()) {
                executionContext.setError(true);
                executionContext.getErrorDetail().setCode(executionContext.getErrorCode());
                executionContext.getErrorDetail().setMessage(executionContext.getEnrichmentFailureReason());
            } else {
                insertOrUpdate();
                if (GenericConstants.CREATE_SERVICE_ORDER_TYPES.contains(executionContext.getOrder().getOrderType())) {
                    executionContext.getOrder().getProfile().getAccount().setServiceGroups(null);
                }
               
                workflowDataUpdated = true;
            }
        } catch (JsonProcessingException jpe) {
            throw new RequestValidationException("Invalid Json");
        } catch (Exception e) {
            log.error("Exception occurred. unable to form the tp request {}", e.getMessage());
            executionContext.getErrorDetail().setCode(executionContext.getErrorCode());
            executionContext.getErrorDetail().setMessage(executionContext.getEnrichmentFailureReason());
            executionContext.getErrorDetail().setSystem("COM");
            executionContext.setError(true);
            return;
        }
    }

    private void insertOrUpdate() throws JsonProcessingException {
        if (ObjectUtils.isEmpty(orderPayloadService.getOrderFlowContext(executionContext.getOrder().getOrderId()))) {
            orderPayloadService.createOrderPayloadDetails(executionContext);
            return;
        }
        orderPayloadService.updatePayloadDetails(executionContext, executionContext.getOrder().getOrderId());
    }
}
