package in.co.sixdee.bss.com.orderorchestrator.config;

import in.co.sixdee.bss.com.orderorchestrator.MDCTaskDecorator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.concurrent.Executor;

@Configuration
@EnableAsync
public class AsyncConfig {

	@Value("${callback.executor.queue-capacity:100}")
	int queueCapacity;
	@Value("${callback.executor.core-pool-size:5}")
	int corePoolSize;
	@Value("${callback.executor.max-pool-size:10}")
	int maxPoolSize;
	
	@Value("${cancelOrder.executor.queue-capacity:100}")
	int cancelOrderQueueCapacity;
	@Value("${cancelOrder.executor.core-pool-size:5}")
	int cancelOrderCorePoolSize;
	@Value("${cancelOrder.executor.max-pool-size:10}")
	int cancelOrderMaxPoolSize;

	@Value("${callback.audit.executor.queue-capacity:100}")
	int callBackQueueCapacity;
	@Value("${callback.audit.executor.core-pool-size:5}")
	int callBackCorePoolSize;
	@Value("${callback.audit.executor.max-pool-size:10}")
	int callBackMaxPoolSize;
	

	@Bean(name = "callbackRetryThreadPoolTaskExecutor")
	public Executor callbackRetryThreadPoolTaskExecutor() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		executor.setCorePoolSize(corePoolSize);
		executor.setQueueCapacity(queueCapacity);
		executor.setMaxPoolSize(maxPoolSize);
		executor.setThreadNamePrefix("CallbackRetry-");
		executor.setTaskDecorator(new MDCTaskDecorator());
		executor.initialize();
		return executor;
	}
	
	@Bean(name ="cancelOrderThreadPoolTaskExecutor")
	public Executor cancelOrderThreadPoolTaskExecutor() {
		ThreadPoolTaskExecutor executor=new ThreadPoolTaskExecutor();
		executor.setCorePoolSize(cancelOrderCorePoolSize);
		executor.setQueueCapacity(cancelOrderQueueCapacity);
		executor.setMaxPoolSize(cancelOrderMaxPoolSize);
		executor.setThreadNamePrefix("CancelOrder-");
		executor.setTaskDecorator(new MDCTaskDecorator());
		executor.initialize();
		return executor;
	}

	@Bean(name ="callbackAuditThreadPoolTaskExecutor")
	public Executor callbackAuditThreadPoolTaskExecutor() {
		ThreadPoolTaskExecutor executor=new ThreadPoolTaskExecutor();
		executor.setCorePoolSize(callBackCorePoolSize);
		executor.setQueueCapacity(callBackQueueCapacity);
		executor.setMaxPoolSize(callBackMaxPoolSize);
		executor.setThreadNamePrefix("CallBackAudit-");
		executor.setTaskDecorator(new MDCTaskDecorator());
		executor.initialize();
		return executor;
	}

}
