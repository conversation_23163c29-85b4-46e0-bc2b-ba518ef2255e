package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.common.constants.GenericLogConstants;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Log4j2
@Component(value = "somChangeSim")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SOMChangeSim extends AbstractDelegate {

	private Order orderPayload = null;

	protected String request = null;

	protected int index = 0;

	@Autowired
	private ObjectMapper objectMapper;
	protected String algoId = null;
	protected String kdbId = null;

	@Override
	protected void execute() throws Exception {
		try {
			orderPayload = executionContext.getOrder();

			request = createSOMRequest();
			var callThirdPartyDTO = callThirdParty(request);
			if (callThirdPartyDTO == null) {
				executionContext.setError(true);
				return;
			}
			validateResponse(callThirdPartyDTO);
		} catch (Exception e) {
			log.error("{} In {} .execute Exception occurred ", executionContext.getTraceId(), this.getClass().getName(),
					e);
		}
	}

	protected String createSOMRequest() throws Exception {
		var serviceOrder = new SOMServiceOrderDTO();
		executionContext.getAttributes().put("callbackCorrelationId",
				callbackEvent + ":" + executionContext.getAttributes().get(GenericConstants.SUB_ORDER_ID));
		serviceOrder.setExternalId(orderPayload.getOrderId());
		serviceOrder.setDescription(orderPayload.getDescription());
		serviceOrder.setPriority("1");
		serviceOrder.setCategory("Change sim");
		serviceOrder.setRequestedStartDate(
				StringUtils.isNotEmpty(orderPayload.getRequestedStartDate()) ? orderPayload.getRequestedStartDate()
						: Instant.now().toString());
		serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
		serviceOrder.setType("ServiceOrder");
		serviceOrder.setRegistryId(executionContext.getEntityId());
		serviceOrder.setExternalServiceId(executionContext.getAttributes().get(GenericConstants.SERVICE_ID));
		serviceOrder.setServiceOrderItem(createServiceOrderItem());
		request = objectMapper.writeValueAsString(serviceOrder);
		return request;
	}

	private List<ServiceOrderItem> createServiceOrderItem() {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		try {
			List<SOMService> services = null;
			if (executionContext.getWorkflowData().containsKey("SOMFetchServiceRegistryResponseAttributes")) {
				services = objectMapper.convertValue(executionContext.getWorkflowData().get("SOMFetchServiceRegistryResponseAttributes"), new TypeReference<List<SOMService>>() {
				});
			}
			if (ObjectUtils.isNotEmpty(services)) {
				for (SOMService service : services) {
					if (service != null && "PRS".equals(service.getCategory())) {
						var serviceOrderItem = new ServiceOrderItem();
						for (Characteristic serviceCharacteristic : service.getServiceCharacteristic()) {
							if ("KI".equalsIgnoreCase(serviceCharacteristic.getName())) {
								serviceCharacteristic.setValue(getKIDetails());
							} else if ("IMSI".equalsIgnoreCase(serviceCharacteristic.getName())) {
								serviceCharacteristic.setValue(executionContext.getOrder().getServiceManagement().getNewImsi());
							} else if ("ICCID".equals(serviceCharacteristic.getName())) {
								serviceCharacteristic.setValue(executionContext.getOrder().getServiceManagement().getNewIccid());
							}else if ("ALGOID".equals(serviceCharacteristic.getName()) && StringUtils.isNotEmpty(algoId)) {
								serviceCharacteristic.setValue(algoId);
							} else if ("KDBID".equals(serviceCharacteristic.getName()) && StringUtils.isNotEmpty(kdbId)) {
								serviceCharacteristic.setValue(kdbId);
							}
		
						}
						if (service.getServiceSpecification() != null) {
							service.getServiceSpecification().setName(service.getName());
							service.getServiceSpecification().setVersion("1");
						}
						service.setType(service.getCategory());
						serviceOrderItem.setService(service);
						serviceOrderItem.setId("1");
						serviceOrderItem.setAction("modifySim");
						serviceOrderItem.setType("ServiceOrderItem");
						serviceOrderItemList.add(serviceOrderItem);
						break;
					}
				}
			}
		} catch (Exception e) {
			log.error(GenericLogConstants.TAG_APP + " | Exception occured in createServiceOrderItem ", e);
		}

		return serviceOrderItemList;

	}

	private String getKIDetails() {
		String ki = null;
		try {
			if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData())
					&& executionContext.getWorkflowData().containsKey("NMSGetNumberDetailsResponseAttributes")) {
				var nmsResponse = objectMapper.convertValue(
						executionContext.getWorkflowData().get("NMSGetNumberDetailsResponseAttributes"),
						JsonNode.class);
				if (nmsResponse.has("ki"))
					ki = nmsResponse.get("ki").asText();
				if (nmsResponse.has("algoId"))
					algoId = nmsResponse.get("algoId").asText();
				if (nmsResponse.has("kdbId"))
					kdbId = nmsResponse.get("kdbId").asText();
			}
		} catch (Exception e) {
			log.error(GenericLogConstants.TAG_APP + " | Exception occurred in getKIDetails ", e);
		}
		return ki;
	}

}
