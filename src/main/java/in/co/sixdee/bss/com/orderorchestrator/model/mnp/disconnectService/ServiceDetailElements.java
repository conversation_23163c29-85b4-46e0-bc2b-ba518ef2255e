package in.co.sixdee.bss.com.orderorchestrator.model.mnp.disconnectService;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
public class ServiceDetailElements {

	@XmlElement(name = "referenceId",namespace = "http://group.singtel.com/manageporting-types/v1")
	private ReferenceID referenceID;
	@XmlElement(name = "serviceID",namespace = "http://group.singtel.com/manageporting-types/v1")
	private ServiceID serviceID;
	
	
	public ReferenceID getReferenceID() {
		return referenceID;
	}
	
	public void setReferenceID(ReferenceID referenceID) {
		this.referenceID = referenceID;
	}
	
	public ServiceID getServiceID() {
		return serviceID;
	}
	
	public void setServiceID(ServiceID serviceID) {
		this.serviceID = serviceID;
	}
	
}
