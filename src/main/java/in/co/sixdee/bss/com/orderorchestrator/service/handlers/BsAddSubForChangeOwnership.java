package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;

import in.co.sixdee.bss.om.model.dto.order.Subscription;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Log4j2
@Component(value = "bsAddSubForChangeOwnership")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class BsAddSubForChangeOwnership extends AbstractDelegate {

	@Override
	protected void execute() throws Exception {
		String request = getRequestFromSpec();
		if (executionContext.isError())
			return;

		var callThirdPartyDTO = callThirdParty(request);
		if (callThirdPartyDTO == null) {
			executionContext.setError(true);
			return;
		}

		var response = callThirdPartyDTO.getResponse();
		validateResponse(callThirdPartyDTO);
		if (executionContext.isError())
			return;
		getSubscriptionIdAndPlanId(response);
		workflowDataUpdated = true;
	}

	private void getSubscriptionIdAndPlanId(String response) throws JsonProcessingException {
		List<Object> newSubscriptionIdPlanIdMap = new ArrayList<>();
		List<Subscription> viewSubscriptionsFromBilling = getSubscriptionsFromBsResponse();
		List<Subscription> addSubscriptionsResponseFromBilling = getResponseFromAddSubscription(response);
		for (Subscription viewsubs : viewSubscriptionsFromBilling) {
			for (Subscription addsubs : addSubscriptionsResponseFromBilling) {
				if (viewsubs.getPlanId().equalsIgnoreCase(addsubs.getPlanId())
						&& viewsubs.getExpiryDate().equalsIgnoreCase(addsubs.getExpiryDate())) {
					Map<String, String> tempDataList = new HashMap<>();
					tempDataList.put("newSubscriptionId", addsubs.getSubscriptionId());
					tempDataList.put("planId", addsubs.getPlanId());
					tempDataList.put("ocsPlanId", addsubs.getOcsPlanId());
					tempDataList.put("oldSubscriptionId", viewsubs.getSubscriptionId());
					newSubscriptionIdPlanIdMap.add(tempDataList);
					break;
				}
			}
		}

		if (ObjectUtils.isNotEmpty(newSubscriptionIdPlanIdMap)) {
			executionContext.getWorkflowData().put("BS_ViewGroupSubscriptionResponseAttributes", newSubscriptionIdPlanIdMap);
		}
	}

	private List<Subscription> getResponseFromAddSubscription(String response) throws JsonProcessingException {
		List<Subscription> bsAddSubscriptions = new ArrayList<>();

		var billingResponse = objectMapper.readTree(response);
		var bsResponse = billingResponse.get("response").get("subscriptions");
		if (ObjectUtils.isNotEmpty(bsResponse)) {
			bsAddSubscriptions = objectMapper.convertValue(bsResponse, new TypeReference<List<Subscription>>() {
			});
		}
		return bsAddSubscriptions;
	}

	private List<Subscription> getSubscriptionsFromBsResponse() {
		List<Subscription> bsFetchSubscription = new ArrayList<>();
		if (executionContext.getWorkflowData().containsKey("BS_ViewGroupSubscriptionResponseAttributes")) {
			bsFetchSubscription = objectMapper.convertValue(
					executionContext.getWorkflowData().get("BS_ViewGroupSubscriptionResponseAttributes"),
					new TypeReference<List<Subscription>>() {
					});
		}
		return bsFetchSubscription;
	}

}
