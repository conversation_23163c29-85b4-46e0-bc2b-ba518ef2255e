package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain;

import jakarta.persistence.*;
import lombok.Generated;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 */

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "COM_SUB_ORDER_DETAILS", uniqueConstraints = @UniqueConstraint(columnNames = {"SUB_ORDER_ID"}))
@Generated
public class SubOrderEntity extends AbstractAuditingEntity implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name = "SUB_ORDER_ID", length = 20, unique = true)
	private Long id;

	@Column(name = "USERNAME", length = 100)
	private String				username;
	
	@Column(name = "ORDER_ID", length = 20)
	private Long orderId;

	@Column(name = "SUB_ORDER_STATE", length = 200)
	private String state;

	@Column(name = "STATE_REASON", length = 30)
	private String stateReason;

	@Column(name = "SERVICE_ID", length = 50)
	private String serviceId;
	
	@Column(name= "FAILED_STAGE_CODE", length= 100)
	private String failedStageCode;

	@Column(name = "ENTITY_ID", length = 20)
	private String entityId;
	

	@Column(name = "CHARGING_PATTERN")
	private Integer chargingPattern;
	@Override
	public boolean equals(Object o) {
		if (this == o)
			return true;
		if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o))
			return false;
		SubOrderEntity subOrderEntity = (SubOrderEntity) o;

		return Objects.equals(id, subOrderEntity.id);
	}

	@Override
	public int hashCode() {
		return getClass().hashCode();
	}

	public SubOrderEntity(Long orderId,Long id) {
		this.orderId = orderId;
		this.id =id;
	}

}
