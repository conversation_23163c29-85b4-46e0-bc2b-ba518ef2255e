package in.co.sixdee.bss.com.orderorchestrator.config.camunda.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderConstants;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.constants.GenericLogConstants;
import in.co.sixdee.bss.common.util.NGTableColumnConstants;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.WorkItemAuditDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.spin.plugin.variable.SpinValues;
import org.camunda.spin.plugin.variable.value.JsonValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Log4j2
public class WorkFlowUtil {

	protected final GetDataFromCache cache;

	@Autowired
	ObjectMapper objectMapper;

	public static void createWorkItemAudit(OrderFlowContext orderFlowContext, DelegateExecution execution, String executionStatus,
										   String subOrderId) {
		WorkItemAuditDTO workItemAuditDTO = null;
		try {
			workItemAuditDTO = new WorkItemAuditDTO();
			workItemAuditDTO.setWorkItemName(execution.getCurrentActivityId());
			workItemAuditDTO.setOrderId(orderFlowContext.getAttributes().get(GenericConstants.ORDER_ID));
			workItemAuditDTO.setWorkItemStatus(executionStatus);
			workItemAuditDTO.setExecutionId(String.valueOf(execution.getId()));
			workItemAuditDTO.setSubOrderId(subOrderId);
			workItemAuditDTO.setId(execution.getCurrentActivityId());
			orderFlowContext.getWorkItemAudits().add(workItemAuditDTO);

		} catch (Exception e) {
			log.error(orderFlowContext.getTraceId() + " | Exception occurred createWorkItemAudit " + e, e);
		}
	}

	public boolean isTaskRetryEnabled(String orderType, String activityId) {
		CacheTableDataDTO workflowRetryConfigDto = null;
		boolean isRetry = false;
		String retryStrategy = null;
		try {
			workflowRetryConfigDto = cache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_COM_WORKFLOW_RETRY_CONFIG,
					orderType + "_" + activityId);
			if (ObjectUtils.isEmpty(workflowRetryConfigDto))
	        {			
			workflowRetryConfigDto = cache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_COM_WORKFLOW_RETRY_CONFIG,
					orderType+"-Rollback" + "_" + activityId);
			}
			if (workflowRetryConfigDto != null) {
				retryStrategy = workflowRetryConfigDto.getNgTableData().get(NGTableColumnConstants.COLUMN_RETRY_STRATEGY);
				switch (retryStrategy) {
					case OrderConstants.RETRY_STRATEGY_NORETRY:
						log.info("Retry is disabled for task:" + activityId);
						break;
					case OrderConstants.RETRY_STRATEGY_CONNECTION_ERROR:
					case OrderConstants.RETRY_STRATEGY_API_ERROR:
					case OrderConstants.RETRY_STRATEGY_BOTH:
						// retry for all errors
						// not connection error
						// (!ErrorConstants.ec_rest_call_error.equals(orderFlowContext.getErrorDetail().getCode()))
						// connection error
						// if
						// (ErrorConstants.ec_rest_call_error.equals(orderFlowContext.getErrorDetail().getCode()))
						// {
						isRetry = true;
						// }
						break;
					default:
						log.info(GenericLogConstants.TAG_APP + " Invalid retry strategy:" + retryStrategy);

				}
			} else {
				log.info("unable to find the retry configuration for activity {} and order type {}", activityId, orderType);
			}

		} catch (Exception e) {
			log.error("Exception occured in isRetryEnabled. setting retry as false " + e);
		}
		return isRetry;
	}

	public String getOrderStageByActivityId(String activityId) {
		var stageConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_ORDER_STAGE_CONFIG.name(), activityId);
		return stageConfig != null ? stageConfig.getNgTableData().get(CacheConstants.CacheFields.STAGE.name()) : null;
	}

	/**
	 * This method will create the workflow data as a spin variable and return
	 *
	 * @param schemaKey
	 * @return
	 */
	public JsonValue createWorkflowData(String schemaKey, OrderFlowContext orderFlowContext) {
		JsonValue workflowData = null;
		if (orderFlowContext.getWorkflowData() == null)
			orderFlowContext.setWorkflowData(new LinkedHashMap<>());
		try {
			workflowData = SpinValues.jsonValue(objectMapper.writeValueAsString(orderFlowContext)).create();
		} catch (JsonProcessingException e) {
			// handle exception, return the message back to queue or discard message after updating
			// order status
		}
		return workflowData;
	}

}
