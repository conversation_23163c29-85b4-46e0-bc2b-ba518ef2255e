package in.co.sixdee.bss.com.orderorchestrator.model.mnp.confirmPortOut;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "ConfirmPortOutOrderRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class ConfirmPortOutOrderRequest {

	@XmlElement(name = "confirmPortOutOrder")
	private ConfirmPortOutOrder confirmPortOutOrder;

	public ConfirmPortOutOrder getConfirmPortOutOrder() {
		return confirmPortOutOrder;
	}

	public void setConfirmPortOutOrder(ConfirmPortOutOrder confirmPortOutOrder) {
		this.confirmPortOutOrder = confirmPortOutOrder;
	}

}
