package in.co.sixdee.bss.com.orderorchestrator.config.messaging;

import in.co.sixdee.bss.com.orderorchestrator.config.ApplicationProperties;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.common.constants.ApiConstants;
import in.co.sixdee.bss.common.util.SequenceGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.slf4j.MDC;
import org.springframework.amqp.ImmediateAcknowledgeAmqpException;
import org.springframework.integration.config.GlobalChannelInterceptor;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Log4j2
@Service
@GlobalChannelInterceptor(patterns = "*-in-*")
@RequiredArgsConstructor
public class InputChannelInterceptor implements ChannelInterceptor {

	protected final ApplicationProperties applicationProperties;

	@Override
	public Message<?> preSend(Message<?> message, MessageChannel channel) {
		MDC.put(ApiConstants.TRACE_ID, String.valueOf(message.getHeaders().get(ApiConstants.TRACE_ID)));
		String orderId = (String) message.getHeaders().get(GenericConstants.ORDER_ID);
		MDC.put(ApiConstants.REQUEST_ID, orderId != null ? orderId : String.valueOf(SequenceGenerator.getSequencerInstance().nextId()));
		validateRetryAttempts(message);
		return message;
	}

	private void validateRetryAttempts(Message message) {
		if (message.getHeaders().get("x-death") != null) {
			Map<?, ?> death = (Map<?, ?>) ((List<?>) Objects.requireNonNull(message.getHeaders().get("x-death"))).get(0);
			if (death != null && death.get("count") != null) {
				long count = (long) death.get("count");
				log.info("message death count {}", count);
				if (count == -1) {
					log.info("Infinite number of retry attempts are configured for the failed messages");
				} else if (count > applicationProperties.getRabbitMaxRetries()) {
					// giving up - don't send to DLX
					log.info("Max retries exhausted for this message. not sending to DLX and ignoring ");
					throw new ImmediateAcknowledgeAmqpException("Failed after " + applicationProperties.getRabbitMaxRetries() + " attempts");
				}
			}
		}
	}

}
