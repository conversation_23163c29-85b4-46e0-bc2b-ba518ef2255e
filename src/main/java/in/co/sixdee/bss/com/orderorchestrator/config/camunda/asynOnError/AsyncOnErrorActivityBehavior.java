package in.co.sixdee.bss.com.orderorchestrator.config.camunda.asynOnError;

import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.camunda.bpm.engine.delegate.BpmnError;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.camunda.bpm.engine.impl.bpmn.behavior.TaskActivityBehavior;
import org.camunda.bpm.engine.impl.context.Context;
import org.camunda.bpm.engine.impl.jobexecutor.AsyncContinuationJobHandler;
import org.camunda.bpm.engine.impl.persistence.entity.MessageEntity;
import org.camunda.bpm.engine.impl.pvm.delegate.ActivityBehavior;
import org.camunda.bpm.engine.impl.pvm.delegate.ActivityExecution;

import java.io.PrintWriter;
import java.io.StringWriter;



@Log4j2
public class AsyncOnErrorActivityBehavior extends TaskActivityBehavior implements ActivityBehavior {

	protected JavaDelegate delegate;

	public AsyncOnErrorActivityBehavior(JavaDelegate delegate) {
		log.info("inside AsyncOnErrorActivityBehavior");
		this.delegate = delegate;
	}

	public void execute(ActivityExecution execution) throws Exception {
		log.info("inside AsyncOnErrorActivityBehavior.execute");
		if (!Boolean.TRUE.equals(execution.getVariable(getStateVariableName(execution)))) {
			// First try
			try {
				executeBusinessLogic(execution);
				leave(execution);
			} catch (BpmnError e) {
				throw e;
			} catch (Exception e) {
				execution.setVariableLocal(getStateVariableName(execution), true);
				createAsynchronousContinuationJob(execution, e);
			}
		} else {
			// Retries
			log.info("inside AsyncOnErrorActivityBehavior.execute retries");
			executeBusinessLogic(execution);
			execution.setVariableLocal(getStateVariableName(execution), null);
			leave(execution);
		}
	}

	protected void executeBusinessLogic(ActivityExecution execution) throws Exception {
		log.info("inside AsyncOnErrorActivityBehavior.executeBusinessLogic");
		delegate.execute(execution);
	}

	protected String getStateVariableName(ActivityExecution execution) {
		return "AsyncOnError_FIRST_EXCUTION_OF_" + execution.getActivity().getId() + "_FAILED";
	}

	public static void createAsynchronousContinuationJob(DelegateExecution execution, Exception exception) {
		log.info("inside AsyncOnErrorActivityBehavior.createAsynchronousContinuationJob");
		MessageEntity message = new MessageEntity();
		message.setProcessInstanceId(execution.getProcessInstanceId());
		message.setProcessDefinitionId(execution.getProcessDefinitionId());
		message.setExecutionId(execution.getId());
		message.setExclusive(true);
		message.setJobHandlerType(AsyncContinuationJobHandler.TYPE);
		message.setExceptionMessage(exception.getMessage());
		message.setExceptionStacktrace(getExceptionStacktrace(exception));

		Context
				.getCommandContext()
				.getJobManager()
				.send(message);
	}

	public static String getExceptionStacktrace(Exception exception) {
		//StringWriter stringWriter = new StringWriter();
		return ExceptionUtils.getStackTrace(exception);
		//exception.printStackTrace(new PrintWriter(stringWriter));
		//return stringWriter.toString();
	}

}