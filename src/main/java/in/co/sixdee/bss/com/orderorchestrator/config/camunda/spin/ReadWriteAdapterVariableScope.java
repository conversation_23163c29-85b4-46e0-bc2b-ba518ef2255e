package in.co.sixdee.bss.com.orderorchestrator.config.camunda.spin;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.JsonUtils;
import jakarta.validation.constraints.NotNull;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.delegate.VariableScope;
import org.camunda.spin.plugin.variable.SpinValues;

import java.util.Optional;

/**
 * Read-write adapter for variable scope.
 *
 * @param <T> type of value.
 */
@Log4j2
public class ReadWriteAdapterVariableScope<T> implements ReadWriteAdapter<T>, VariableFactory<T> {

	@NotNull
	private final VariableScope variableScope;

	@NotNull
	private final String variableName;

	@NotNull
	private final Class<T> clazz;

	/**
	 * Constructs the adapter.
	 *
	 * @param variableScope variable scope to access.
	 * @param variableName  variable to access.
	 * @param clazz         class of variable value
	 */
	public ReadWriteAdapterVariableScope(VariableScope variableScope, String variableName, Class<T> clazz) {
		this.variableScope = variableScope;
		this.variableName = variableName;
		this.clazz = clazz;
	}

	@Override
	public Optional<T> getOptional() {
		return Optional.ofNullable(getOrNull(variableScope.getVariable(variableName)));
	}

	@Override
	public void set(T value) {
		variableScope.setVariable(variableName, setJsonTypeValue(value));
	}

	@Override
	public T get() {
		return getOptional().orElseThrow(() -> new CommonException("Couldn't find required variable '" + variableName + "'"));
	}

	/**
	 * Retrieves the value or null.
	 *
	 * @param value raw value.
	 * @return value or null.
	 */
	@SuppressWarnings("unchecked")
	protected T getOrNull(Object value) {

		if (value == null) {
			return null;
		}

		if (clazz.isAssignableFrom(value.getClass())) {
			return (T) value;
		} else {
			try {
				return JsonUtils.unmarshall(value.toString(), clazz);
			} catch (Exception e) {
				throw new CommonException(
						"Error reading " + variableName + ": Couldn't read value of " + clazz + " from " + value);
			}
		}
	}

	public Object setJsonTypeValue(Object value) {
		if (value == null) {
			return null;
		} else {

			try {
				return SpinValues.jsonValue(JsonUtils.marshall(value, null)).create();
			} catch (Exception e) {
				throw new CommonException(
						"Error writing " + variableName + ": Couldn't write value into " + clazz + " from " + value);
			}
		}
	}

	@Override
	public ReadWriteAdapter<T> on(VariableScope variableScope) {
		return new ReadWriteAdapterVariableScope<>(variableScope, variableName, clazz);
	}

	@Override
	public ReadWriteAdapter<T> from(VariableScope variableScope) {
		return new ReadWriteAdapterVariableScope<>(variableScope, variableName, clazz);
	}

}