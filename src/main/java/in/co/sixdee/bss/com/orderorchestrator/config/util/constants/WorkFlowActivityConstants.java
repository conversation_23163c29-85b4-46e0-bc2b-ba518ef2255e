package in.co.sixdee.bss.com.orderorchestrator.config.util.constants;

import org.slf4j.helpers.MessageFormatter;

import java.util.Map;
import java.util.Set;

public class WorkFlowActivityConstants {

    private WorkFlowActivityConstants() {

    }

    public static final Map<String, String> activityReasonDescriptionMap = Map.ofEntries(
            Map.entry("approval", "Waiting for Approval"),
            Map.entry("som", "Waiting for SOM Callback"),
            Map.entry("snd", "Waiting for SND Callback"),
            Map.entry("ERPCallBack", "Waiting for Bank Callback"),
            Map.entry("POSSaleOrder", "Waiting for Shop Callback"),
            Map.entry("MNPCallBack", "Waiting for Mnp gateway Callback"),
            Map.entry("PORTOUTMNPCallBack", "Waiting for Mnp gateway Callback"),
            Map.entry("NGCallBack", "Waiting for NG Callback"),
            Map.entry("PaymentCallBack", "Waiting for Payment Callback"),
            Map.entry("SimDeliveryCallback", "Waiting for SimDelivery Callback"),
            Map.entry("SOMCallback", "Waiting for SOM Callback"),
            Map.entry("ConnectServiceCallback", "Waiting for ConnectService Callback"),
            Map.entry("MnpProvisioningTimer", "Provisioning process is scheduled for {}"),
            Map.entry("SOMAddSubCallback", "Waiting for SOM Callback"),
            Map.entry("DisconnectServiceCallback", "Waiting for Disconnectservice Callback"),
            Map.entry("DisconnectServiceIntCallback", "Waiting for Disconnectservice Callback Internal"),
            Map.entry("MNPChangeProductTimer", "Order scheduled for {} to initiate Change Product Status"),
            Map.entry("MNPConnectServiceIntTimer", "Order scheduled for {} to initiate Connect Service Internal trigger"),
            Map.entry("MnpDeProvisioningTimer", "De-provisioning process is scheduled for {}"),
            Map.entry("FutureOrderTimer", "Scheduled for execution on {}"),
            Map.entry("ConnectServiceIntCallback", "Waiting for ConnectService Callback"),
            Map.entry("Esb_blockEsim", "Waiting for Esb Block Esim Callback")
    );

    public static String formatMessage(String messageTemplate, Object... parameters) {
        if (parameters == null || parameters.length == 0) {
            return messageTemplate;
        } else {
            return MessageFormatter.arrayFormat(messageTemplate, parameters).getMessage();
        }
    }

    public static Set<String> failedOrInProgressStatuses = Set.of(
            WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue(),
            WorkFlowConstants.StageStatusConstants.STAGE_STATUS_INPROGRESS.getValue(),
            WorkFlowConstants.StageStatusConstants.STAGE_STATUS_SKIPPED.getValue(),
            WorkFlowConstants.StageStatusConstants.STAGE_STATUS_REJECTED.getValue()
    );


}
