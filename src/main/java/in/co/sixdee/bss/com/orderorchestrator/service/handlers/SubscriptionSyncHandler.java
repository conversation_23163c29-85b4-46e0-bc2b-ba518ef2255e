package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.FailedDependencyException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.AppConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.esb.ESBResponse;
import in.co.sixdee.bss.om.model.dto.esb.ServiceCharacteristic;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import in.co.sixdee.bss.om.model.dto.sm.Offer;
import in.co.sixdee.bss.om.model.dto.sm.SMViewSubscriptionResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Log4j2
public class SubscriptionSyncHandler extends RestExecutor {

    public void fetchSubscriptionId(OrderFlowContext orderFlowContext, String stageCode, Date createdDate) {

        if (stageCode.startsWith("NCC")) {
            NCCFetchSubscriptionId(orderFlowContext, stageCode, createdDate);
        } else if (stageCode.startsWith("SM")) {
            SMFetchSubscriptionId(orderFlowContext, stageCode, createdDate);
        }
    }

    private void SMFetchSubscriptionId(OrderFlowContext orderFlowContext, String stageCode, Date createdDate) {
        var thirdPartyId = "skip-sm-view-subscription";
        var reqSpecKey = "SKIP_SM_ViewSubscription";
        var orderType = orderFlowContext.getOrder().getOrderType();
        var planId = extractPlanIdFromOrder(orderFlowContext, stageCode);
        HashMap<String, String> subscriptionIdMap = orderFlowContext.getWorkflowData().get("subscriptionIdMap") != null
                ? (HashMap<String, String>) orderFlowContext.getWorkflowData().get("subscriptionIdMap")
                : new HashMap<String, String>();
		Instant current = createdDate.toInstant();
		LocalDateTime orderCreationDate = LocalDateTime.ofInstant(current, ZoneId.systemDefault());

        if (subscriptionIdMap.containsKey(planId))
            return;
        initThirdPartyCallDetails(thirdPartyId, orderType, orderFlowContext);
        var request = "";
        try {
            request = joltUtils.convert(reqSpecKey, orderType, objectMapper.convertValue(orderFlowContext, Map.class),
                    orderFlowContext.getAttributes());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        var callThirdPartyDTO = callThirdParty(request, orderFlowContext);
        if (callThirdPartyDTO != null) {
            var response = callThirdPartyDTO.getResponse();
            validateResponse(callThirdPartyDTO);
            var sm_viewSubscription_response = modifyResponse("SKIP_SM_ViewSubscription_Response", response, orderType);
            subscriptionIdMap.putAll(extractSubscriptionIdFromSMResponse(sm_viewSubscription_response, planId, orderCreationDate));
        }
        orderFlowContext.getWorkflowData().put("subscriptionIdMap", subscriptionIdMap);
    }

    protected String extractPlanIdFromOrder(OrderFlowContext orderFlowContext, String stageCode) {
        var planId = "";
        var order = orderFlowContext.getOrder();
        switch (order.getOrderType()) {
            case OrderTypes.ONBOARDING:
            case OrderTypes.ADD_SERVICE:
            case OrderTypes.ADD_SERVICE_TO_NEW_ACCOUNT:
                planId = getPlanIdForServiceProvisioningOrder(orderFlowContext, stageCode);
                break;
            case OrderTypes.STARTERPACK_PROVISIONING:
                planId = order.getStarterPack().getSubscriptions().get(0).getPlanId();
                break;
            case OrderTypes.ADD_SUBSCRIPTION:
            case OrderTypes.GIFTING:
                planId = order.getOrderItem().get(0).getProductOffering().getSubscription().getPlanId();
                break;
            case OrderTypes.SUSPEND_SERVICE:
            case OrderTypes.SOFT_BARRING:
            case OrderTypes.HARD_BARRING:
                planId = orderFlowContext.getOrder().getServiceManagement().getSubscriptions().get(0).getPlanId();
                break;
            case OrderTypes.ADD_MEMBER_TO_GROUP:
                planId = order.getGroupManagement().getSubscriptions().get(0).getPlanId();
                break;
            case OrderTypes.CHANGE_SUBSCRIPTION:
            case OrderTypes.CONNECTION_MIGRATION:
            case OrderTypes.TERMINATE_SERVICE:
                var currentExecution = (Map<String, Object>) orderFlowContext.getWorkflowData().get("currentExecution");
                var sub = objectMapper.convertValue(currentExecution.get("executionData"), Subscription.class);
                planId = sub != null ? sub.getPlanId() : null;
                break;
            case OrderTypes.UPDATE_STARTER_PACK_KYC:
                planId = orderFlowContext.getAttributes().get("welcomePlanId");
                break;
            default:
                break;
        }
        return planId;
    }

    protected String getPlanIdForServiceProvisioningOrder(OrderFlowContext orderFlowContext, String stageCode) {
        var offerId = "";
        var currentExecutionMap = (Map<String, Object>) orderFlowContext.getWorkflowData().get("currentExecution");
        var subscription = objectMapper.convertValue(currentExecutionMap.get("execution"), Subscription.class);
        offerId = subscription != null ? subscription.getPlanId() : null;
        if (stageCode.equalsIgnoreCase("SM_DEFAULT_SUBSCRIPTIONS"))
            offerId = orderFlowContext.getAttributes().get("welcomePlanId");
        return offerId;
    }

    private void NCCFetchSubscriptionId(OrderFlowContext orderFlowContext, String stageCode, Date createdDate) {
        var thirdPartyId = "skip-ncc-view-subscription";
        var orderType = orderFlowContext.getOrder().getOrderType();
		Instant current = createdDate.toInstant();
		LocalDateTime orderCreationDate = LocalDateTime.ofInstant(current, ZoneId.systemDefault());
        initThirdPartyCallDetails(thirdPartyId, orderType, orderFlowContext);

        var callThirdPartyDTO = callThirdParty("", orderFlowContext);
        if (callThirdPartyDTO != null) {
            var response = callThirdPartyDTO.getResponse();
            validateResponse(callThirdPartyDTO);
            var nccViewSubscriptionResponse = modifyResponse("SKIP_NCC_ViewSubscription", response, orderType);
            extractExternalOfferIdFromOrder(nccViewSubscriptionResponse, orderFlowContext, stageCode, orderCreationDate);
        }
    }

    protected void extractExternalOfferIdFromOrder(String nccViewSubscriptionResponse, OrderFlowContext orderFlowContext,
            String stageCode, LocalDateTime createdDate) {
        var externalOfferId = "";
        HashMap<String, String> subscriptionIdMap = orderFlowContext.getWorkflowData().get("subscriptionIdMap") != null
                ? (HashMap<String, String>) orderFlowContext.getWorkflowData().get("subscriptionIdMap")
                : new HashMap<String, String>();
        switch (orderFlowContext.getOrder().getOrderType()) {
            case OrderTypes.ONBOARDING:
            case OrderTypes.ADD_SERVICE:
            case OrderTypes.ADD_SERVICE_TO_NEW_ACCOUNT:
                if (stageCode.equalsIgnoreCase("NCC_DEFAULT_SUBSCRIPTIONS")) {
                    externalOfferId = String.valueOf(orderFlowContext.getWorkflowData().get("welcomeExternalOfferId"));
                    extractSubscriptionId(externalOfferId, nccViewSubscriptionResponse, subscriptionIdMap, createdDate);
                } else {
                    var subscription = extractSubscriptionFromOrder(orderFlowContext);
                    setSubscriptionId(subscription, nccViewSubscriptionResponse, subscriptionIdMap, createdDate);
                }
                break;
            case OrderTypes.STARTERPACK_PROVISIONING:
                setSubscriptionId(orderFlowContext.getOrder().getStarterPack().getSubscriptions(), nccViewSubscriptionResponse,
                        subscriptionIdMap, createdDate);
                break;
            case OrderTypes.ADD_SUBSCRIPTION:
            case OrderTypes.GIFTING:
                externalOfferId = orderFlowContext.getOrder().getOrderItem().get(0).getProductOffering().getSubscription()
                        .getExternalOfferId();
                extractSubscriptionId(externalOfferId, nccViewSubscriptionResponse, subscriptionIdMap, createdDate);
                break;
            case OrderTypes.ADD_MEMBER_TO_GROUP:
                externalOfferId = orderFlowContext.getOrder().getGroupManagement().getSubscriptions().get(0).getExternalOfferId();
                extractSubscriptionId(externalOfferId, nccViewSubscriptionResponse, subscriptionIdMap, createdDate);
                break;
            case OrderTypes.CHANGE_SUBSCRIPTION:
            case OrderTypes.CONNECTION_MIGRATION:
            case OrderTypes.TERMINATE_SERVICE:
                var externalOfferIdList = getExternalOfferIdListFromOrder(orderFlowContext, stageCode);
                if (ObjectUtils.isNotEmpty(externalOfferIdList))
                    extractSubscriptionIdFromList(externalOfferIdList, nccViewSubscriptionResponse, subscriptionIdMap, createdDate);
                break;
            case OrderTypes.UPDATE_STARTER_PACK_KYC:
                externalOfferId = String.valueOf(orderFlowContext.getWorkflowData().get("welcomeExternalOfferId"));
                extractSubscriptionId(externalOfferId, nccViewSubscriptionResponse, subscriptionIdMap, createdDate);
                break;
            default:
                break;
        }
        orderFlowContext.getWorkflowData().put("subscriptionIdMap", subscriptionIdMap);
    }

    private List<Subscription> extractSubscriptionFromOrder(OrderFlowContext orderFlowContext) {
        List<Subscription> subscriptions = new ArrayList<>();
        LinkedHashMap<String, Object> currentExecution = (LinkedHashMap<String, Object>) orderFlowContext.getWorkflowData()
                .get("currentExecution");
        if (currentExecution != null) {
            try {
                var service = objectMapper.readValue(objectMapper.writeValueAsString(currentExecution.get("executionData")),
                        in.co.sixdee.bss.om.model.dto.order.Service.class);
                subscriptions = service.getSubscriptions();
            } catch (Exception e) {
                log.error("Exception occurred while getting service", e);
            }
        }
        return subscriptions;
    }

    private List<String> getExternalOfferIdListFromOrder(OrderFlowContext orderFlowContext, String stageCode) {
        List<String> externalOfferIdList = new ArrayList<>();
        try {
            if (StringUtils.equalsAnyIgnoreCase(stageCode, "NCC_ADD_SUBSCRIPTION") && ObjectUtils.isNotEmpty(orderFlowContext.getWorkflowData().get("nccAddSub")))
                externalOfferIdList.addAll((ArrayList<String>) orderFlowContext.getWorkflowData().get("nccAddSub"));

            if (StringUtils.equalsAnyIgnoreCase(stageCode, "NCC_CONNECTION_MIGRATION") && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement())
                    && ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getSubscriptions())) {
                //CM & Terminate case
                var subscriptions = orderFlowContext.getOrder().getServiceManagement().getSubscriptions();
                var basePlanSubsObj = subscriptions.stream().filter(sub -> "0".equals(sub.getPlanType()) && StringUtils.isNotEmpty(sub.getExternalOfferId())).findFirst().orElse(null);
                if (basePlanSubsObj != null)
                    externalOfferIdList.add(basePlanSubsObj.getExternalOfferId());
            }
        } catch (Exception e) {
            log.error(" :::::  Exception occured in getExternalOfferIdListFromOrder method  :::::", e);
        }
        return externalOfferIdList;
    }

    protected HashMap<String, String> setSubscriptionId(List<Subscription> subscriptions, String nccViewSubscriptionResponse,
            HashMap<String, String> subscriptionIdMap, LocalDateTime createdDate) {
        if (ObjectUtils.isNotEmpty(subscriptions)) {
            List<String> externalOfferIdList = subscriptions.stream().map(sub -> sub.getExternalOfferId())
                    .collect(Collectors.toList());
            for (String offerId : externalOfferIdList) {
                extractSubscriptionId(offerId, nccViewSubscriptionResponse, subscriptionIdMap, createdDate);
            }
        }
        return subscriptionIdMap;
    }

    protected void extractSubscriptionId(String externalOfferId, String nccViewSubscriptionResponse,
            HashMap<String, String> subscriptionIdMap, LocalDateTime createdDate) {
        ESBResponse esbResponse = null;
        try {
            esbResponse = objectMapper.readValue(nccViewSubscriptionResponse, ESBResponse.class);
        } catch (JsonProcessingException e) {
            throw new CommonException(StatusConstants.HttpConstants.INTERNAL_SERVER_ERROR,
                    "Exception occurred while extracting subscription ids" + e.getMessage());
        }
        if (ObjectUtils.isNotEmpty(esbResponse.getServiceCharacteristic()) && StringUtils.isNotEmpty(externalOfferId)) {
            var serviceCharacteristic = Objects.requireNonNull(esbResponse.getServiceCharacteristic().stream()
                    .filter(characteristic -> GenericConstants.ESB_CHARACTERISTIC_TYPE_SUBSCRIPTION_ID
                            .equals(characteristic.getType()) && externalOfferId.equalsIgnoreCase(characteristic.getName())
                            && isValidSubscriptionId(createdDate, characteristic.getActivationDate(), "NCC"))
                    .sorted(Comparator.comparing(ServiceCharacteristic::getActivationDate, Comparator.reverseOrder())).findFirst()
                    .orElse(null));
            if (ObjectUtils.isEmpty(serviceCharacteristic))
                throw new FailedDependencyException(StatusConstants.HttpConstants.FAILED_DEPENDENCY,
                        "unable to find the subscription id for external offer id : " + externalOfferId);
            if (ObjectUtils.isNotEmpty(serviceCharacteristic))
            subscriptionIdMap.put(serviceCharacteristic.getName(), serviceCharacteristic.getValue());
        }
    }

    protected void extractSubscriptionIdFromList(List<String> externalOfferIdList, String nccViewSubscriptionResponse,
            HashMap<String, String> subscriptionIdMap, LocalDateTime createdDate) {
        ESBResponse esbResponse = null;
        try {
            esbResponse = objectMapper.readValue(nccViewSubscriptionResponse, ESBResponse.class);
        } catch (JsonProcessingException e) {
            throw new CommonException(StatusConstants.HttpConstants.INTERNAL_SERVER_ERROR,
                    "Exception occurred while extracting subscription ids" + e.getMessage());
        }
        if (ObjectUtils.isNotEmpty(esbResponse.getServiceCharacteristic()) && ObjectUtils.isNotEmpty(externalOfferIdList)) {
            var serviceCharacteristic = esbResponse.getServiceCharacteristic().stream()
                    .filter(characteristic -> GenericConstants.ESB_CHARACTERISTIC_TYPE_SUBSCRIPTION_ID
                            .equals(characteristic.getType()) && externalOfferIdList.contains(characteristic.getName())
                            && isValidSubscriptionId(createdDate, characteristic.getActivationDate(), "NCC"))
                    .sorted(Comparator.comparing(ServiceCharacteristic::getActivationDate, Comparator.reverseOrder())).collect(Collectors.toList());
            if (ObjectUtils.isEmpty(serviceCharacteristic))
                throw new FailedDependencyException(StatusConstants.HttpConstants.FAILED_DEPENDENCY,
                        "unable to find the subscription id for external offer id : " + externalOfferIdList);
            serviceCharacteristic.forEach(serviceChar -> {
                if (!subscriptionIdMap.containsKey(serviceChar.getName())) {
                    subscriptionIdMap.put(serviceChar.getName(), serviceChar.getValue());
                }
            });

        }
    }
    
    protected HashMap<String, String> extractSubscriptionIdFromSMResponse(String response, String planId,
    		LocalDateTime createdDate) {
		SMViewSubscriptionResponse smResponse = null;
		var subscriptionIdMap = new HashMap<String, String>();
		try {
			smResponse = objectMapper.readValue(response, SMViewSubscriptionResponse.class);
		} catch (JsonProcessingException e) {
			throw new CommonException(
					"Error while executing skip request. SM queryPlan failed while extracting subscription ids");
		}
		if (ObjectUtils.isNotEmpty(smResponse.getResponseParam().getOffers()) && StringUtils.isNotEmpty(planId)) {
            var smOffer = Objects.requireNonNull(smResponse.getResponseParam().getOffers().stream()
                    .filter(offer -> StringUtils.isNotEmpty(offer.getSubscriptionId())
                            && planId.equalsIgnoreCase(offer.getId())
                            && isValidSubscriptionId(createdDate, offer.getActivationDate(), "SM"))
                    .sorted(Comparator.comparing(Offer::getActivationDate, Comparator.reverseOrder())).findFirst()
                    .orElse(null));
			if (ObjectUtils.isEmpty(smOffer))
				throw new FailedDependencyException(StatusConstants.HttpConstants.FAILED_DEPENDENCY,
						"unable to find the subscription id for planId : " + planId);
            if (ObjectUtils.isNotEmpty(smOffer))
			subscriptionIdMap.put(planId, smOffer.getSubscriptionId());
		}
		return subscriptionIdMap;
	}
	
	private boolean isValidSubscriptionId(LocalDateTime createdDate, String activationDate, String stage) {
		LocalDateTime actvnDate = null;
		if (StringUtils.equalsAnyIgnoreCase(stage, "NCC"))
			actvnDate = LocalDateTime.parse(activationDate, DateTimeFormatter.ofPattern(AppConstants.SYSTEM_DATE_FORMAT));
		else
			actvnDate = LocalDateTime.parse(activationDate, DateTimeFormatter.ofPattern(AppConstants.SM_DATE_FORMAT));
		if (createdDate.isBefore(actvnDate))
			return true;
		return false;
	}
}
