package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Log4j2
@Component(value = "fetchNewSubscriptionDetails")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class BSFetchNewSubscriptionDetails extends AbstractDelegate {

    @Override
    protected void execute() throws Exception {

        String request = getRequestFromSpec();
        if (executionContext.isError())
            return;

        var callThirdPartyDTO = callThirdParty(request);
        if (callThirdPartyDTO == null) {
            executionContext.setError(true);
            return;
        }
        String response = "{\"channelId\":\"CRM\",\"data\":[{\"beneficiaryList\":[],\"counter\":0,\"createDate\":\"2024-08-21T02:34:37.000Z\",\"groupSeqId\":\"G_154\",\"ocsServiceSeqId\":5000002228959,\"sponsorMsisdn\":\"9567801372122\",\"status\":1,\"typeOfMsisdn\":\"Sponsored\",\"updateDate\":\"2024-08-21T02:34:37.000Z\"}],\"draw\":0,\"entityId\":\"200\",\"last\":true,\"recordsFiltered\":1,\"recordsTotal\":1,\"requestId\":\"09834\",\"responseTimestamp\":\"2024-08-20T15:48:49.803Z\",\"totalPages\":1,\"transactionId\":\"139434617297760003\"}";
//        var response = callThirdPartyDTO.getResponse();
        validateResponse(callThirdPartyDTO);
        if (executionContext.isError())
            return;
        modifySubscriptionIdPlanIdMap(response);
        modifyWorkflowData(response);
    }

    private void modifySubscriptionIdPlanIdMap(String response) throws JsonProcessingException {

        List<Subscription> bsSubscriptions = new ArrayList<>();
        String planId = null;
        String newSubscriptionId = null;
        HashMap<String, String> subscriptionIdPlanIdMap = getSubscriptionIdPlanIdMap();

        JsonNode billResp = objectMapper.readTree(response);
        if (billResp.get("data") != null && billResp.get("data").get(0) != null && ObjectUtils.isNotEmpty(subscriptionIdPlanIdMap)) {
            bsSubscriptions = objectMapper.readValue(objectMapper.writeValueAsString(billResp.get("data")), new TypeReference<List<Subscription>>() {
            });
            for (Subscription subscription : bsSubscriptions) {
                if (StringUtils.isNotEmpty(subscription.getPlanId()) && StringUtils.isNotEmpty(subscription.getSubscriptionId())) {
                    planId = subscription.getPlanId();
                    newSubscriptionId = subscription.getSubscriptionId();
                    getNewSubscriptionIdFromPlanId(subscriptionIdPlanIdMap, planId, newSubscriptionId);
                }
            }
            if (ObjectUtils.isNotEmpty(subscriptionIdPlanIdMap)) {
                executionContext.getWorkflowData().put("subscriptionIdPlanIdMap", subscriptionIdPlanIdMap);
            }
        }

    }

    private void getNewSubscriptionIdFromPlanId(HashMap<String, String> subscriptionIdPlanIdMap, String planId, String newSubscriptionId) {
        String key = null;
        for (Map.Entry<String, String> existingMapEntry : subscriptionIdPlanIdMap.entrySet()) {
            if (StringUtils.isNotEmpty(existingMapEntry.getValue()) && existingMapEntry.getValue().equalsIgnoreCase(planId)) {
                key = existingMapEntry.getKey();
                break;
            }
        }
        if (key != null)
            subscriptionIdPlanIdMap.put(key, newSubscriptionId);
    }

    private HashMap<String, String> getSubscriptionIdPlanIdMap() {
        HashMap<String, String> subscriptionIdPlanIdMap = executionContext.getWorkflowData().get("subscriptionIdPlanIdMap") != null
                ? (HashMap<String, String>) executionContext.getWorkflowData().get("subscriptionIdPlanIdMap")
                : new HashMap<>();
        return subscriptionIdPlanIdMap;
    }
}
