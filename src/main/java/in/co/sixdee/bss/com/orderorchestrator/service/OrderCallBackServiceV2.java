package in.co.sixdee.bss.com.orderorchestrator.service;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CallbackProcessingException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.EntityValidationException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.WorkflowTaskFailedException;
import in.co.sixdee.bss.com.orderorchestrator.config.notification.NotificationUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.*;
import in.co.sixdee.bss.com.orderorchestrator.model.CallBackHeader;
import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.*;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.*;
import in.co.sixdee.bss.com.orderorchestrator.service.handlers.ARMFetchAssetDetails;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.constants.ApiConstants;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.Response;
import in.co.sixdee.bss.om.model.dto.order.Dataset;
import in.co.sixdee.bss.om.model.dto.order.Param;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.OptimisticLockingException;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.spin.plugin.variable.SpinValues;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Log4j2
@RequiredArgsConstructor
public class OrderCallBackServiceV2 {

    private final OrderStageRepository orderStageRepository;

    private final OrderAttributeRepository orderAttributeRepository;

    private final OrderStatusManager orderStatusManager;

    private final ObjectMapper objectMapper;

    private final RuntimeService runtimeService;

    private final GetDataFromCache getDataFromCache;

    private final WaitingProcessInfoRepository waitingProcessInfoRepository;

    private final OrderRepository orderRepository;

    private final CallbackAuditService callbackAuditService;

    @Autowired
    protected NotificationUtils notificationUtils;

    @Autowired
    protected ARMFetchAssetDetails armFetchDetails;

    private final SubOrderRepository subOrderRepository;

    public Response processCallback(OrderCallBack callback) {

        createCallBackObject(callback);
        if (callback.getType() != null && callback.getType().toUpperCase().contains(WorkFlowConstants.OrderCallBackTypes.SIM_DELIVERY.desc)) {
            validateRequestParams(callback);
            callback.setCallbackType(WorkFlowConstants.OrderCallBackTypes.SIM_DELIVERY.desc);
            callback.setCallbackHeader(setHeadersToCallBack());
            armFetchDetails.getDetailsForCallBack(callback);
        }
        WaitingProcessInfoEntity waitingProcessInfo = getWaitingProcessInfoEntry(callback);

        if (ObjectUtils.isEmpty(waitingProcessInfo)) {
            log.info("Unable to find the wait event information for this order id {}. Perhaps the wait event info is not populated yet..inserting call back to audit info", callback.getOrderId());
            insertToCallBackAudit(callback);
            return callBackResponse(callback);
        }
        callback.setWaitingEventInfo(waitingProcessInfo);
        OrderFlowContext orderFlowContext = createOrderFlowContext(callback);
        if (isSuccessCallBack(callback)) {
            if (processSuccessCallBack(callback)) {
                sendNotificationForCallBack(callback, orderFlowContext);
                return callBackResponse(callback);
            }
        } else {
            OrderStageEntity stageInfo = validateStageInfoForFailureCallBack(callback);
            if (ObjectUtils.isNotEmpty(stageInfo) && ObjectUtils.isNotEmpty(orderFlowContext))
                orderStatusManager.UpdateCallBackOrderStatus(orderFlowContext, callback, stageInfo);
            return callBackResponse(callback);
        }

        return null;
    }

    private void validateRequestParams(OrderCallBack callback) {
        if(callback.getNewICCID().isEmpty()){
            throw new EntityValidationException("invalid iccid");
        }
    }

    private CallBackHeader setHeadersToCallBack() {
        CallBackHeader callBackHeader = new CallBackHeader();
        callBackHeader.setTraceId(MDC.get(ApiConstants.TRACE_ID));
        callBackHeader.setChannel(MDC.get(ApiConstants.CHANNEL));
        callBackHeader.setUsername(MDC.get(ApiConstants.USER_NAME));
        callBackHeader.setEntityId(MDC.get(ApiConstants.ENTITYID));
        callBackHeader.setUserId(MDC.get(ApiConstants.USER_ID));
        if(StringUtils.isNotEmpty(MDC.get(ApiConstants.LEGACY_API)))
            callBackHeader.setLegacyApi(MDC.get(ApiConstants.LEGACY_API));
        return callBackHeader;

    }

    private boolean processSuccessCallBack(OrderCallBack callback) {
        try {
            if (!isServiceTaskReadyForCallBack(callback)) {
                log.info("Process instance {} for order {} is not ready to receive the callback event",
                        callback.getWaitingEventInfo().getProcessInstanceId(), callback.getOrderId());
                insertToCallBackAudit(callback);
                return true;
            }
            log.info("Process instance {} for order {} is ready to receive callback event",
                    callback.getWaitingEventInfo().getProcessInstanceId(), callback.getOrderId());
            //need to ask


            log.info(" invoking call back signal. orderId: {}, subOrderId: {}", callback.getOrderId(), callback.getSubOrderId());
            runtimeService.setVariable(callback.getWaitingEventInfo().getExecutionId(), "waitEventEntityId",
                    String.valueOf(callback.getWaitingEventInfo().getSeqId()));
            runtimeService.createMessageCorrelation(callback.getWaitingEventInfo().getEventName())
                    .processInstanceId(callback.getWaitingEventInfo().getProcessInstanceId()).correlate();
        } catch (OptimisticLockingException e) {
            log.info("Got OptimisticLockingException. adding to call back audit info");
            insertToCallBackAudit(callback);
        } catch (Exception e) {
            log.info("Exception occurred in validateSuccessCallBack method", e);
            throw new CallbackProcessingException(e.getMessage());
        }
        return true;
    }

    private void sendNotificationForCallBack(OrderCallBack callback, OrderFlowContext orderFlowContext) {
        if (WorkFlowConstants.OrderCallBackTypes.SOM.desc.equals(callback.getCallbackType())) {
            notificationUtils.sendNotification(orderFlowContext,
                    AppConstants.NotificationMessageTypes.WELCOME_NOTIFICATION.value(), "SUB_ORDER");
        }
    }

    private OrderStageEntity validateStageInfoForFailureCallBack(OrderCallBack callback) throws CommonException {

        OrderStageEntity stageInfo = null;
        callback.setStatus("Failed");
        if (callback.getCorrelationId() != null && callback.getCorrelationId().startsWith("NG")) {
            stageInfo = getStageInfoBySubOrderIdAndStageCode(callback.getSubOrderId(), "NG");
        } else if (callback.getCorrelationId() != null && callback.getCorrelationId().startsWith("MNP")) {
            stageInfo = getStageInfoBySubOrderIdAndStageCode(callback.getSubOrderId(), "MNP");
        } else if (callback.getCorrelationId() != null && callback.getCorrelationId().startsWith("SOM")) {
            stageInfo = getStageInfoBySubOrderIdAndStageCode(callback.getSubOrderId(), "SOM");
        }
        if (stageInfo == null)
            throw new CommonException("Unable to get the stage info ");
        if (callback.isRollbacked()) {
            if (Boolean.parseBoolean(stageInfo.getRollbackStatus()))
                throw new EntityValidationException(StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION,
                        "stage : " + stageInfo.getStageCode() + " is already marked as rollback complete");
        }
        return stageInfo;
    }

    private OrderStageEntity getStageInfoBySubOrderIdAndStageCode(String subOrderId, String stageCode) {
        var stageInfo = orderStageRepository.findInProgressStageBySubOrderIdAndStageCodePrefix(Long.valueOf(subOrderId), stageCode);
        if (stageInfo == null)
            throw new EntityValidationException(StatusConstants.HttpConstants.UNPROCESSABLE_ENTITY,
                    "No stages are waiting for the" + stageCode + " callback for the sub order id: " + subOrderId);
        return stageInfo;
    }

    private OrderFlowContext createOrderFlowContext(OrderCallBack callback) {

        OrderFlowContext orderFlowContext = null;
        try {
            orderFlowContext = objectMapper.readValue(
                    runtimeService.getVariable(callback.getWaitingEventInfo().getProcessInstanceId(),
                            WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString()).toString(),
                    OrderFlowContext.class);
            if (WorkFlowConstants.OrderCallBackTypes.SIM_DELIVERY.desc.equals(callback.getCallbackType()) || WorkFlowConstants.OrderCallBackTypes.PAYMENT.desc.equals(callback.getCallbackType())) {
                setCallBackRequestToOrderFlowContext(orderFlowContext, callback);
                var spinValue = SpinValues.jsonValue(objectMapper.writeValueAsString(orderFlowContext)).create();
                runtimeService.setVariable(callback.getWaitingEventInfo().getExecutionId(),
                        WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString(), spinValue);
            }
        } catch (Exception e) {
            log.info("Exception occurred in createOrderFlowContext method", e);
            throw new CallbackProcessingException(e.getMessage());
        }
        return orderFlowContext;
    }

    private void setCallBackRequestToOrderFlowContext(OrderFlowContext orderFlowContext, OrderCallBack callbackRequest) {
        Map<String, Object> callBackDetailMap = new HashMap<>();
        Map<String, Object> requestMap = new HashMap<>();
        List<Map<String, Object>> callBackDetailsList;
        OrderCallBack callback;
        try {
            callback = objectMapper.readValue(objectMapper.writeValueAsString(callbackRequest), OrderCallBack.class);
        } catch (JsonProcessingException e) {
            throw new WorkflowTaskFailedException("setCallBackRequestToOrderFlowContext", "COM-005", "Unable to clone callback request");
        }
        callback.setWaitingEventInfo(null);
        callBackDetailMap.put("request", JsonUtils.jsonToObject(JsonUtils.toJsonString(callback)));
        requestMap.put("callBackRequest", callBackDetailMap);
        if (MapUtils.isNotEmpty(orderFlowContext.getWorkflowData())
                && ObjectUtils.isNotEmpty(orderFlowContext.getWorkflowData().get("callBackRequest"))) {
            callBackDetailsList = (List<Map<String, Object>>) (orderFlowContext.getWorkflowData()
                    .get(GenericConstants.CALL_BACK_REQUEST));
        } else {
            callBackDetailsList = new ArrayList<>();
        }
        callBackDetailsList.add(requestMap);
        orderFlowContext.getWorkflowData().put("callBackDetails", callBackDetailsList);
        orderFlowContext.getAttributes().put("newIMSI", callbackRequest.getNewIMSI());
        orderFlowContext.getAttributes().put("newICCID", callbackRequest.getNewICCID());
    }

    private boolean isSuccessCallBack(OrderCallBack callback) {
        return WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue()
                .equalsIgnoreCase(callback.getStatus())
                || WorkFlowConstants.OrderCallBackTypes.PAYMENT.desc.equals(callback.getCallbackType())
                || WorkFlowConstants.OrderCallBackTypes.APPROVAL.desc.equals(callback.getCallbackType())
                || WorkFlowConstants.OrderCallBackTypes.SIM_DELIVERY.desc.equals(callback.getCallbackType());
    }

    private boolean isServiceTaskReadyForCallBack(OrderCallBack callback) {
        return runtimeService.createExecutionQuery().messageEventSubscription()
                .processInstanceId(callback.getWaitingEventInfo().getProcessInstanceId()).count() > 0;
    }

    private Response callBackResponse(OrderCallBack callback) {
        var response = new Response();
        createResponseBody(response, callback);
        if (shouldCreateDataSet(callback.getEntityId(),callback.getCallbackHeader())) {
            createDataset(response, callback);
        }
        return response;
    }

    private void createDataset(Response response, OrderCallBack callback) {
        List<Param> params = new ArrayList<>();
        addParamIfPresent(callback.getProfileId(), "profileId", params);
        addParamIfPresent(callback.getProfileId(), "accountId", params);
        if (ObjectUtils.isNotEmpty(params)) {
            Dataset dataset = new Dataset();
            dataset.setParam(params);
            response.setDataset(dataset);
        }
    }

    private void addParamIfPresent(String value, String key, List<Param> params) {
        if (StringUtils.isNotEmpty(value)) {
            Param param = new Param();
            param.setValue(value);
            param.setId(key);
            params.add(param);
        }
    }

    private void createResponseBody(Response response, OrderCallBack callback) {
        response.setRequestId(StringUtils.isNotEmpty(MDC.get(ApiConstants.TRACE_ID)) ? MDC.get(ApiConstants.TRACE_ID)
                : MDC.get(ApiConstants.REQUEST_ID));
        response.setStatus("Success");
        response.setCode("200");
        response.setMessage("Callback initiated successfully");
        response.setOrderId(callback.getOrderId());
    }

    private boolean shouldCreateDataSet(String entityId, CallBackHeader callbackHeader) {
        if (ObjectUtils.isNotEmpty(callbackHeader) && StringUtils.isNotEmpty(callbackHeader.getLegacyApi()) && "false".equalsIgnoreCase(callbackHeader.getLegacyApi())) {
            log.info("is legacy api tag is false..no need to create dataset");
            return false;
        } else if (ObjectUtils.isNotEmpty(callbackHeader) && StringUtils.isNotEmpty(callbackHeader.getLegacyApi()) && "true".equalsIgnoreCase(callbackHeader.getLegacyApi())) {
            log.info("is legacy api tag is true.need to create dataset");
            return true;
        }else {
            var orderConfigurations = getDataFromCache.getCacheDetailsFromDBMap("COM_ORDER_TYPE_CONFIG",
                    "UpdateOrder");
            if (ObjectUtils.isNotEmpty(orderConfigurations)) {
                String datasetReqdMvnos = orderConfigurations.getNgTableData().get("LEGACY_RESPONSE_REQD_MVNOS");
                return (StringUtils.isNotEmpty(datasetReqdMvnos)) && Arrays.asList(datasetReqdMvnos.split(",")).contains(entityId);
            }
        }
        return false;
    }

    private void insertToCallBackAudit(OrderCallBack callback) {
        log.info("inserting call back data to call back audit info table..");
        if(StringUtils.isEmpty(callback.getSubOrderId()))
            getSubOrderIdForAuditTable(callback);
        callbackAuditService.createTableAuditForCallBack(callback);
    }

    private void getSubOrderIdForAuditTable(OrderCallBack callback) {
        Long subOrderId = subOrderRepository.getSubOrderIdForAudit(Long.parseLong(callback.getOrderId()));
        if (subOrderId != null) {
            callback.setSubOrderId(String.valueOf(subOrderId));
        }
    }

    private void createCallBackObject(OrderCallBack callback) {

        try {
            if (callback.getOrderId() == null) {
                setParamsFromMNPCallBack(callback);
            }
            OrderEntity orderEntity = validateOrder(callback.getOrderId());
            setOrderEntityParams(orderEntity, callback);
            getSubOrderIdFromCorrelationId(callback);
        } catch (Exception e) {
            log.info("Exception occurred while createCallBackObject method", e);
            throw new CallbackProcessingException(e.getMessage());
        }
    }

    public WaitingProcessInfoEntity getWaitingProcessInfoEntry(OrderCallBack callback) {
        WaitingProcessInfoEntity waitingProcessInfo;
        String orderId = callback.getOrderId();
        String subOrderId = callback.getSubOrderId();
        if (StringUtils.isNotEmpty(orderId) && StringUtils.isNotEmpty(subOrderId))
            waitingProcessInfo = waitingProcessInfoRepository.findProcessInfoByOrderIdAndSubOrderId(orderId, subOrderId, Arrays.asList("Callback", "Timer"));
        else
            waitingProcessInfo = waitingProcessInfoRepository.findProcessInfoByOrderId(orderId, Arrays.asList("Callback", "Timer"));
        if (ObjectUtils.isEmpty(waitingProcessInfo)) {
            return waitingProcessInfo;
        }
        return waitingProcessInfo;
    }

    private void getSubOrderIdFromCorrelationId(OrderCallBack callback) {

        if (StringUtils.isEmpty(callback.getSubOrderId()) && StringUtils.isNotEmpty(callback.getCorrelationId())) {
            try {
                if (StringUtils.isNotEmpty(callback.getCorrelationId())) {
                    callback.setSubOrderId(callback.getCorrelationId().split(":" )[1]);
                    if(StringUtils.isEmpty(callback.getCallbackType())){
                        callback.setCallbackType(callback.getCorrelationId().split(":" )[0]);
                        log.info("setting value of call back type as {} ",callback.getCallbackType());
                    }
                }
            } catch (Exception e) {
                throw new EntityValidationException("Invalid correlation id : " + callback.getCorrelationId());
            }
        }
    }

    private void setOrderEntityParams(OrderEntity orderEntity, OrderCallBack callback) {
        callback.setProfileId(orderEntity.getCustomerId());
        callback.setAccountId(orderEntity.getBillingAccountId());
        callback.setOrderType(orderEntity.getType());
        callback.setEntityId(orderEntity.getEntityId());
    }

    private OrderEntity validateOrder(String orderId) {
        OrderEntity orderEntity = orderRepository.findProfileIdAndAccountIdByOrderId(Long.parseLong(orderId));
        if (ObjectUtils.isEmpty(orderEntity))
            throw new EntityValidationException("Invalid order id : " + orderId + ". Perhaps this order doesn't exist in the system");
        if (WorkFlowConstants.StageStatusConstants.STAGE_STATUS_CANCELLED.getValue().equalsIgnoreCase(orderEntity.getState()) || WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue().equalsIgnoreCase(orderEntity.getState()))
            throw new EntityValidationException(StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION, "call back is already processed for order id " + orderEntity.getOrderId());
        return orderEntity;
    }

    private void setParamsFromMNPCallBack(OrderCallBack callback) {

        if (ObjectUtils.isNotEmpty(callback.getmnpAttributes()) && ObjectUtils.isNotEmpty(callback.getmnpAttributes().get(MNPConstants.MNP_CALLBACK_TYPE)) && MNPConstants.MNP_CALLBACK_TYPE_DISCONNECT_INT.equalsIgnoreCase(callback.getmnpAttributes().get(MNPConstants.MNP_CALLBACK_TYPE).toString()) && ObjectUtils.isNotEmpty(callback.getmnpAttributes().get(MNPConstants.MNP_SERVICE_ID))) {
            log.info("call back received is for  disconnect service internal with service id {}", callback.getmnpAttributes().get(MNPConstants.MNP_SERVICE_ID).toString());
            SubOrderEntity subOrderEntity = fetchOrderDetailsForDisconnectServiceInternal(callback);
            if (ObjectUtils.isEmpty(subOrderEntity)) {
                throw new EntityValidationException("Unable to find the order when searching with service id " + subOrderEntity.getServiceId());
            }
            setCallBackAttributes(subOrderEntity.getOrderId(), subOrderEntity.getId(), callback);
        } else {
            log.info("call back received is for reference id {}", callback.getReferenceId());
            OrderAttributeEntity orderAttributeEntity = fetchDetailsFromOrderAttributes(callback.getReferenceId());
            if (ObjectUtils.isEmpty(orderAttributeEntity)) {
                throw new EntityValidationException("Unable to find the order when searching with reference id " + orderAttributeEntity.getReferenceId());
            }
            setCallBackAttributes(orderAttributeEntity.getOrderId(), orderAttributeEntity.getSubOrderId(), callback);
        }
    }

    private void setCallBackAttributes(Long orderId, Long id, OrderCallBack callback) {
        if (StringUtils.isEmpty(callback.getOrderId())) {
            callback.setOrderId(String.valueOf(orderId));
        }
        if (StringUtils.isEmpty(callback.getSubOrderId())) {
            callback.setSubOrderId(String.valueOf(id));
        }
    }

    private OrderAttributeEntity fetchDetailsFromOrderAttributes(String referenceId) {
        OrderAttributeEntity orderAttributes = null;
        try {
            orderAttributes = orderAttributeRepository.getAttributesByReferenceId(referenceId);
            if (ObjectUtils.isEmpty(orderAttributes)) {
                log.info("Unable to find the order when searching with referenceId column. checking if the order exist with mnp requestId as {}", referenceId);
                orderAttributes = orderAttributeRepository.getAttributesByRequestId(referenceId);
                if (orderAttributes == null)
                    throw new EntityValidationException("Unable to find any order matching with referenceId/requestId : " + referenceId);
            }
        } catch (Exception e) {
            log.info("Exception occurred", e);
            throw new CallbackProcessingException(e.getMessage());

        }
        return orderAttributes;
    }

    private SubOrderEntity fetchOrderDetailsForDisconnectServiceInternal(OrderCallBack callback) {
        String serviceId = null;
        SubOrderEntity orderAttributes = null;
        try {
            serviceId = getServiceIdWithCC(callback.getmnpAttributes().get(MNPConstants.MNP_SERVICE_ID).toString());
            orderAttributes = subOrderRepository.getDetailsForDisconnectServiceInternal(serviceId, WorkFlowConstants.WorkItemExecutionStatus.STATUS_INPROGRESS.getValue(), WorkFlowActivityConstants.activityReasonDescriptionMap.get("DisconnectServiceIntCallback"));
            if (ObjectUtils.isEmpty(orderAttributes)) {
                log.info("Unable to find the order when searching with service id {}", serviceId);
                throw new EntityValidationException("Unable to find the order when searching with service id " + serviceId);
            }
        } catch (Exception e) {
            log.info("Exception occurred", e);
            throw new CallbackProcessingException(e.getMessage());

        }
        return orderAttributes;
    }

    private String getServiceIdWithCC(String serviceId) {

        CacheTableDataDTO countryCodeConfig = getDataFromCache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG, "COUNTRY_CODE");
        if (countryCodeConfig == null) {
            log.info("Unable to find COUNTRY_CODE configuration from OM_APPLICATION_CONFIG");
            return serviceId;
        }
        String countryCode = countryCodeConfig.getNgTableData().get("CONFIG_VALUE");
        if (StringUtils.isEmpty(countryCode)) {
            log.info("COUNTRY_CODE configuration value is empty in OM_APPLICATION_CONFIG");
            return serviceId;
        }
        if (StringUtils.isNotEmpty(serviceId) && !serviceId.startsWith(countryCode)) {
            return countryCode + serviceId;
        }
        return serviceId;
    }

    public void processCallbackFromScheduler(CallBackAuditInfoEntity auditInfoEntity , WaitingProcessInfoEntity waitingProcessInfoEntity) {
        OrderCallBack callback = null;
        if (ObjectUtils.isNotEmpty(auditInfoEntity.getPayload())) {
            try {
                callback = objectMapper.readValue(auditInfoEntity.getPayload(), OrderCallBack.class);
            } catch (JsonProcessingException e) {
                log.error("Exception while creating sub order context ", e);
                throw new WorkflowTaskFailedException("CreateInstancesDelegateOnboarding", "COM-005", "Unable to clone callback request");
            }
        }
		if (callback != null && ObjectUtils.isNotEmpty(callback)) {
			// boolean isWaitingProcessInfoFound =
			// checkWaitingProcessInfoInCallBackObject(callback);
			// if (!isWaitingProcessInfoFound) {
			// if (!checkAndInsertWaitingProcessInfo(callback, auditInfoEntity)) {
			// return;
			// }
			if (ObjectUtils.isNotEmpty(waitingProcessInfoEntity)) {
				log.info("setting waiting process info data to callback");
				callback.setWaitingEventInfo(waitingProcessInfoEntity);
			}
			OrderFlowContext orderFlowContext = getOrderFlowContextForScheduler(callback, auditInfoEntity);
			if (isSuccessCallBack(callback)) {
				processSuccessCallBackFromScheduler(callback, auditInfoEntity);
			} else {
				processFailureCallBackFromScheduler(orderFlowContext, callback, auditInfoEntity);
			}
			/*
			 * } else { processSuccessCallBackFromScheduler(callback, auditInfoEntity); }
			 */
		}
    }

    private void processFailureCallBackFromScheduler(OrderFlowContext orderFlowContext, OrderCallBack callback, CallBackAuditInfoEntity auditInfoEntity) {
        if (ObjectUtils.isNotEmpty(orderFlowContext)) {
            OrderStageEntity stageInfo = validateStageInfoForFailureCallBack(callback);
            if (ObjectUtils.isNotEmpty(stageInfo))
                orderStatusManager.UpdateCallBackOrderStatus(orderFlowContext, callback, stageInfo);
        } else {
            log.info("orderflow context is null.. inserting to audit table");
            UpdateCallBackAuditFromScheduler(auditInfoEntity);
        }
    }

    private void processSuccessCallBackFromScheduler(OrderCallBack callback, CallBackAuditInfoEntity auditInfoEntity) {
        try {
            if (!isServiceTaskReadyForCallBack(callback)) {
                log.info("Process instance {} for order {} is not ready to receive the callback event from scheduler",
                        callback.getWaitingEventInfo().getProcessInstanceId(), callback.getOrderId());
                UpdateCallBackAuditFromScheduler(auditInfoEntity);
                return;
            }
            log.info("Process instance {} for order {} is ready to receive callback event from scheduler",
                    callback.getWaitingEventInfo().getProcessInstanceId(), callback.getOrderId());
            //need to ask


            log.info(" invoking call back signal. orderId: {}, subOrderId: {} from scheduler", callback.getOrderId(), callback.getSubOrderId());
            runtimeService.setVariable(callback.getWaitingEventInfo().getExecutionId(), "waitEventEntityId",
                    String.valueOf(callback.getWaitingEventInfo().getSeqId()));
            runtimeService.createMessageCorrelation(callback.getWaitingEventInfo().getEventName())
                    .processInstanceId(callback.getWaitingEventInfo().getProcessInstanceId()).correlate();
        } catch (OptimisticLockingException e) {
            log.info("Got OptimisticLockingException. adding to call back audit info from scheduler");
            UpdateCallBackAuditFromScheduler(auditInfoEntity);
        } catch (Exception e) {
            log.info("Exception occurred in validateSuccessCallBack method", e);
            throw new CallbackProcessingException(e.getMessage());
        }
    }

    private boolean checkWaitingProcessInfoInCallBackObject(OrderCallBack callback) {

        return ObjectUtils.isNotEmpty(callback.getWaitingEventInfo());
    }

    private OrderFlowContext getOrderFlowContextForScheduler(OrderCallBack orderCallBack, CallBackAuditInfoEntity auditInfoEntity) {
        OrderFlowContext orderFlowContext = null;
        if (ObjectUtils.isNotEmpty(orderCallBack)) {
            orderFlowContext = createOrderFlowContext(orderCallBack);
        }
        return orderFlowContext;
    }

    private OrderFlowContext getOrderFlowContext(OrderCallBack orderCallBack) {
        try {
            return objectMapper.readValue(
                    runtimeService.getVariable(orderCallBack.getWaitingEventInfo().getProcessInstanceId(),
                            WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString()).toString(),
                    OrderFlowContext.class);
        } catch (Exception e) {
            log.info("Exception occurred in createOrderFlowContext method", e);
            throw new CallbackProcessingException(e.getMessage());
        }
    }

    private boolean checkAndInsertWaitingProcessInfo(OrderCallBack callback, CallBackAuditInfoEntity auditInfoEntity) {
        if (ObjectUtils.isEmpty(callback.getWaitingEventInfo())) {
            log.info("Waiting process info is empty in call back object...inserting the same to callback object");
            WaitingProcessInfoEntity waitingProcessInfo = getWaitingProcessInfoEntry(callback);
            if (ObjectUtils.isEmpty(waitingProcessInfo)) {
                log.info("Unable to find the wait event information for this order id {}. Perhaps the wait event info is not populated yet..updating call back to audit info from scheduler", callback.getOrderId());
                UpdateCallBackAuditFromScheduler(auditInfoEntity);
                return false;
            }
            callback.setWaitingEventInfo(waitingProcessInfo);
        }
        return true;
    }

    private void insertToCallBackAuditFromScheduler(OrderCallBack callback, CallBackAuditInfoEntity auditInfoEntity) {
        log.info("inserting call back data to call back audit info table from scheduler..");
        callbackAuditService.createTableAuditForCallBackFromScheduler(callback, auditInfoEntity);
    }

    private void UpdateCallBackAuditFromScheduler(CallBackAuditInfoEntity auditInfoEntity) {
        log.info("updating call back data to call back audit info table from scheduler..");
        callbackAuditService.updateCallBackEntryFromScheduler(auditInfoEntity.getOrderId(),auditInfoEntity.getSubOrderId());
    }
}
