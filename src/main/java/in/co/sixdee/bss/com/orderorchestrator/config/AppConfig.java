/**
 *
 */
package in.co.sixdee.bss.com.orderorchestrator.config;

import in.co.sixdee.bss.common.cache.CacheManager;
import in.co.sixdee.bss.common.connector.RestConnector;
import in.co.sixdee.bss.common.core.AppInstanceIdManager;
import in.co.sixdee.bss.common.logging.LoggingInterceptor;
import in.co.sixdee.bss.common.logging.MDCFilter;
import in.co.sixdee.bss.common.vaidation.ValidationUtils;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 *
 */
@Configuration
@RequiredArgsConstructor
public class AppConfig implements WebMvcConfigurer {

	private final LoggingInterceptor loggingInterceptor;

	protected final CacheManager cacheManager;

	protected final AppInstanceIdManager appInstanceIdManager;
	
	private final ValidationUtils		validationUtils;

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(loggingInterceptor);
	}

	@PostConstruct
	public void initApp() {
		cacheManager.initCache();
	}


	@Bean
	public FilterRegistrationBean<MDCFilter> mdcFilter() {
		FilterRegistrationBean<MDCFilter> registrationBean = new FilterRegistrationBean<>();
		registrationBean.setFilter(new MDCFilter(appInstanceIdManager, validationUtils));
		registrationBean.addUrlPatterns("/*");
		return registrationBean;
	}


	@Bean
	public RestConnector restConnector(WebClient.Builder webClientBuilder) {
		return new RestConnector(webClientBuilder);
	}

}
