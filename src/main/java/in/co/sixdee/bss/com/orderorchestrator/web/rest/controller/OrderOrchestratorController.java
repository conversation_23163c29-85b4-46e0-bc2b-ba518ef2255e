/**
 *
 */

package in.co.sixdee.bss.com.orderorchestrator.web.rest.controller;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants;
import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import in.co.sixdee.bss.com.orderorchestrator.service.*;
import in.co.sixdee.bss.com.orderorchestrator.web.rest.api.OrderOrchestratorAPI;
import in.co.sixdee.bss.om.model.dto.Response;
import in.co.sixdee.bss.om.model.dto.WorkflowRequest;
import in.co.sixdee.bss.om.model.dto.order.CancelOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Lookup;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 *
 */
@RestController
public class OrderOrchestratorController implements OrderOrchestratorAPI {

	@Autowired
	private OrderCallBackService orderCallBackService;

	@Autowired
	private CancelOrderService cancelOrderService;

	@Lookup
	public SkipRetryService getSkipRetryService() {
		return null;
	}
	
	@Autowired
	private OrderApprovalService orderApprovalService;

	@Autowired
	private OrderCallBackServiceV2 orderCallBackServiceV2;

	@Override
	public ResponseEntity<Response> updateOrderStatus(@RequestBody OrderCallBack request) {
		return new ResponseEntity<Response>(orderCallBackServiceV2.processCallback(request), HttpStatus.OK);
	}
	

	@Override
	public ResponseEntity<Response> processInstanceModification(@RequestBody WorkflowRequest request) {
		return new ResponseEntity<Response>(getSkipRetryService().processModifyRequest(request), HttpStatus.OK);
	}

	@Override
	public ResponseEntity<Response> paymentCallBack(OrderCallBack request) {
		request.setCallbackType(WorkFlowConstants.OrderCallBackTypes.PAYMENT.desc);
		return new ResponseEntity<Response>(orderCallBackService.processPaymentRequest(request), HttpStatus.OK);
	}

	@Override
	public ResponseEntity<Response> approve(OrderCallBack request) {
		request.setCallbackType(WorkFlowConstants.OrderCallBackTypes.APPROVAL.desc);
		return new ResponseEntity<Response>(orderApprovalService.processApprovalRequest(request), HttpStatus.OK);
	}

	@Override
	public ResponseEntity<Response> processBulkSkipRetryOrder(@RequestBody WorkflowRequest request) {
		return new ResponseEntity<Response>(getSkipRetryService().processBulkSkipRetryRequest(request), HttpStatus.OK);
	}
}
