package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import java.util.List;
import java.util.Set;

import in.co.sixdee.bss.common.core.ErrorDetail;
import in.co.sixdee.bss.om.model.dto.WorkflowRequest.Orders;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class PartialErrorResponse {

	private final String traceId;
	private final String requestId;
	private final String timestamp;
	private final String code;
	private final String status;
	private final String message;
	private final Set<Orders> orders;
	private final List<ErrorDetail> errors;

	public PartialErrorResponse(Builder builder) {
		this.requestId = builder.requestId;
		this.timestamp = builder.timestamp;
		this.code = builder.code;
		this.status = builder.status;
		this.message = builder.message;
		this.errors = builder.errors;
		this.traceId = builder.traceId;
		this.orders = builder.orders;
	}

	public static class Builder {

		private String requestId;
		private String timestamp;
		private String code;
		private String status;
		private String message;
		private String traceId;
		private Set<Orders> orders;
		private List<ErrorDetail> errors;

		public static Builder getInstance() {
			return new Builder();
		}

		private Builder() {
		}

		public Builder setRequestId(String requestId) {
			this.requestId = requestId;
			return this;
		}

		public Builder setTraceId(String traceId) {
			this.traceId = traceId;
			return this;
		}

		public Builder setTimestamp(String timestamp) {
			this.timestamp = timestamp;
			return this;
		}

		public Builder setCode(String code) {
			this.code = code;
			return this;
		}

		public Builder setStatus(String status) {
			this.status = status;
			return this;
		}

		public Builder setMessage(String message) {
			this.message = message;
			return this;
		}

		public Builder setErrors(List<ErrorDetail> errors) {
			this.errors = errors;
			return this;
		}

		public Builder setOrders(Set<Orders> orders) {
			this.orders = orders;
			return this;
		}

		public PartialErrorResponse build() {
			return new PartialErrorResponse(this);
		}

	}

}
