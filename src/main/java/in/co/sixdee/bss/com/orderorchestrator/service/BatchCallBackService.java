package in.co.sixdee.bss.com.orderorchestrator.service;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.ConfigurationNotValidException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.connector.RestConnector;
import in.co.sixdee.bss.common.constants.GenericLogConstants;
import in.co.sixdee.bss.common.dto.CallThirdPartyDTO;
import in.co.sixdee.bss.om.model.dto.BatchCallBackRequestDTO;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Log4j2
@Service
public class BatchCallBackService {

	@Autowired
	protected GetDataFromCache cache;

	@Autowired
	private RestConnector restConnector;

	@Autowired
	private ObjectMapper objectMapper;


	public void sendCallBackRequest(OrderFlowContext executionContext) {

		try {
			var request = createCallBackRequest(executionContext);
			var callThirdPartyDTO = initThirdPartyCallDetails(request);
			callThirdParty(executionContext, callThirdPartyDTO);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error(GenericLogConstants.TAG_APP + "{} | Exception occurred in sendCallBackRequest: ",
					executionContext.getTraceId(), e);
		}

	}

	protected String createCallBackRequest(OrderFlowContext executionContext) {
		BatchCallBackRequestDTO batchReq = new BatchCallBackRequestDTO();
		var failureReason="";
		batchReq.setBatchId(executionContext.getBatchId());
		batchReq.setBatchRowSeqId(executionContext.getBatchRowSeqId());
		batchReq.setOrderId(executionContext.getOrder().getOrderId() != null ? executionContext.getOrder().getOrderId() : null);
		batchReq.setStatus(executionContext.getOrder().getState().name());
		if(executionContext.getErrorDetail().getSystem() != null && executionContext.getErrorDetail().getMessage()!=null)
			failureReason=executionContext.getErrorDetail().getSystem() + ":" + executionContext.getErrorDetail().getMessage();
		else if(executionContext.getErrorDetail().getMessage()!=null)
			failureReason= executionContext.getErrorDetail().getMessage();

		batchReq.setFailureReason(failureReason);

		return JsonUtils.toJsonString(batchReq);
	}

	protected CallThirdPartyDTO initThirdPartyCallDetails(String request)
			throws CommonException, IllegalStateException {

		var thirdPartyId = "batch-service";
		var thirdPartyUrlConfig = cache
				.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_THIRD_PARTY_URL_CONFIG.name(), thirdPartyId);
		if (Objects.isNull(thirdPartyUrlConfig))
			throw new ConfigurationNotValidException(
					"url configuration not found for the third party id :: " + thirdPartyId);
		return setupCallThirdPartyDto(thirdPartyUrlConfig, request);
	}

	protected void callThirdParty(OrderFlowContext executionContext, CallThirdPartyDTO callThirdPartyDTO) {
		var headerMap = getHeaderMap(callThirdPartyDTO.getHeaders());
		if (headerMap != null)
			callThirdPartyDTO.setHeaderMap(transformHeaders(headerMap, executionContext));
		log.info("Sending CallBack to Batch MS");
		var response = restConnector.postRequestAsync(callThirdPartyDTO);
	}

	protected Map<String, String> getHeaderMap(String headerParams) {
		Map<String, String> headerMap = null;
		try {
			if (StringUtils.isNotEmpty(headerParams)) {
				headerMap = new HashMap<String, String>();
				String[] headerParam = headerParams.split(",");
				for (String header : headerParam) {
					if (header.contains("=")) {
						String name = null, value = null;
						try {
							name = header.split("=")[0];
							value = header.split("=")[1];
							headerMap.put(name, value);
						} catch (Exception e) {
							throw e;
						}
					}
				}
			}
		} catch (Exception e) {
			throw e;
		}
		return headerMap;
	}

	protected Map<String, String> transformHeaders(Map<String, String> headerMap, OrderFlowContext executionContext) {
		Set<Map.Entry<String, String>> entrySet = null;
		Map<String, String> newMap = new HashMap<>();
		String path = null;
		String value = null;
		try {
			var docContext = JsonPath.parse(objectMapper.writeValueAsString(executionContext));
			entrySet = headerMap.entrySet();
			for (Map.Entry<String, String> entry : entrySet) {
				if (entry.getValue().startsWith("@(") && entry.getValue().endsWith(")")) {
					path = entry.getValue().substring(2, entry.getValue().length() - 1);
					try {
						value = docContext.read(path);
					} catch (PathNotFoundException e) {
						log.warn("Path {} not found!!!", path);
					}
					if (ObjectUtils.isNotEmpty(value))
						newMap.put(entry.getKey(), value);
				} else if (entry.getValue().startsWith("{") && entry.getValue().endsWith("}")) {
					newMap.put(entry.getKey(), executionContext.getAttributes()
							.get(entry.getValue().substring(1, entry.getValue().length() - 1)));
				} else
					newMap.put(entry.getKey(), entry.getValue());

			}

		} catch (Exception e) {
			log.info(GenericLogConstants.TAG_APP + "{} | Exception occurred in transformHeaders: ",
					executionContext.getTraceId(), e);
		}
		return newMap;
	}

	protected CallThirdPartyDTO setupCallThirdPartyDto(CacheTableDataDTO thirdPartyUrlConfig, String request) {
		CallThirdPartyDTO callThirdPartyDTO = new CallThirdPartyDTO();
		callThirdPartyDTO.setThirdPartyId(thirdPartyUrlConfig.getNgTableData().get("THIRD_PARTY_ID"));
		callThirdPartyDTO.setUrl(thirdPartyUrlConfig.getNgTableData().get(CacheConstants.CacheFields.URL.name()));
		callThirdPartyDTO.setReadTimeout(NumberUtils
				.toInt(thirdPartyUrlConfig.getNgTableData().get(CacheConstants.CacheFields.READ_TIMEOUT.name()), 3000));
		callThirdPartyDTO.setConnTimeout(NumberUtils.toInt(
				thirdPartyUrlConfig.getNgTableData().get(CacheConstants.CacheFields.CONNECTION_TIMEOUT.name()), 3000));
		callThirdPartyDTO.setRequest(request);
		callThirdPartyDTO
				.setHeaders(thirdPartyUrlConfig.getNgTableData().get(CacheConstants.CacheFields.HEADERS.name()));
		callThirdPartyDTO.setTransportMethod(CallThirdPartyDTO.TransportMethod
				.valueOf(thirdPartyUrlConfig.getNgTableData().get(CacheConstants.CacheFields.TRANSPORT_METHOD.name())));
		return callThirdPartyDTO;
	}

}
