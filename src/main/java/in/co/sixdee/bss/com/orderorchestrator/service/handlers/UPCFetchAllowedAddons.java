package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.common.util.CommonUtils;
import in.co.sixdee.bss.om.model.dto.order.CFSRef;
import in.co.sixdee.bss.om.model.dto.order.Charge;
import in.co.sixdee.bss.om.model.dto.order.ProductOffering;
import in.co.sixdee.bss.om.model.dto.order.ProductOfferingPrice;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.extern.log4j.Log4j2;

@Component(value = "allowedAddonsEnrichment")
@Log4j2
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class UPCFetchAllowedAddons extends AbstractDelegate {

	@Override
	protected void execute() throws Exception {
		String request = null;
		if (CommonUtils.INSTANCE.validateField(reqSpecKey)) {
			try {
				/*
				 * request = joltUtils.convert(reqSpecKey,
				 * executionContext.getOrder().getOrderType(),
				 * JsonUtils.cloneJson(executionContext), executionContext.getAttributes());
				 */

				request = joltUtils.convert(reqSpecKey, executionContext.getOrder().getOrderType(),
						objectMapper.convertValue(executionContext, Map.class), executionContext.getAttributes());
			} catch (Exception e) {
				log.error("Exception occurred. unable to form the tp request {}", e.getMessage());
				executionContext.getErrorDetail().setCode("COM-001");
				executionContext.getErrorDetail().setMessage("Internal Error: error in forming tp request");
				executionContext.getErrorDetail().setSystem("COM");
				executionContext.setError(true);
				return;
			}
		}
		var callThirdPartyDTO = callThirdParty(request);
		if (callThirdPartyDTO == null) {
			executionContext.setError(true);
			return;
		}
		callThirdPartyDTO.getResponse();
		validateResponse(callThirdPartyDTO);
		if (executionContext.isError()) {
			return;
		}
		enrichPlans(callThirdPartyDTO.getResponse());
	}

	protected void enrichPlans(String response) {
		List<ProductOffering> productOfferings;
		Subscription enrichSubscription = null;
		try {
			productOfferings = objectMapper.readValue(response, new TypeReference<List<ProductOffering>>() {});
			if (ObjectUtils.isNotEmpty(executionContext.getOrder().getServiceManagement().getSubscriptions())) {
				var subscriptions = executionContext.getOrder().getServiceManagement().getSubscriptions();
				for (ProductOffering productOffering : productOfferings) {
					enrichSubscription = new Subscription();
					enrichSubscription = enrichSubscription(productOffering);
					subscriptions.add(enrichSubscription);
				}
				executionContext.getWorkflowData().put("newSubscriptionCart", subscriptions);
			}

		} catch (JsonProcessingException e) {
			log.error("Error in Parsing UPC allowed addons response ", e);
		}

	}

	protected void validatePlanStatus(ProductOffering productOffering) {
		if (productOffering.getPoStatus() != null && !"0".equals(productOffering.getPoStatus().getId())) {
			throw new CommonException("Plan {} is not active " + productOffering.getPoId());

		}
	}

	protected Subscription enrichSubscription(ProductOffering productOffering) {
		Subscription subscription = new Subscription();
		subscription.setPlanId(productOffering.getId());
		subscription.setUpcVersion(productOffering.getVersionNo());
		subscription.setPlanName(productOffering.getName());
		subscription.setPlanDesc(productOffering.getDescription());
		subscription.setCpId(productOffering.getPartner());
		if (ObjectUtils.isNotEmpty(productOffering.getProductOfferGroup())) {
			subscription.setPlanGroupId(productOffering.getProductOfferGroup().getId());
			subscription.setPlanGroupName(productOffering.getProductOfferGroup().getName());
		}
		if (productOffering.getProductSpecifications() != null && !productOffering.getProductSpecifications().isEmpty()) {
			if ("1".equals(productOffering.getProductSpecifications().get(0).getType().getId()))
				subscription.setPlanType(GenericConstants.PLAN_TYPE_BASE);
			else
				subscription.setPlanType(GenericConstants.PLAN_TYPE_ADDON);

		} else {
			subscription.setPlanType(GenericConstants.PLAN_TYPE_ADDON);
		}
		if (subscription.getCharges() == null)
			subscription.setCharges(new ArrayList<>());
		enrichCharges(subscription.getCharges(), productOffering.getProductOfferingPrice());
		enrichCfssPrssLrss(productOffering, subscription);
		findSOMCallRequired(productOffering, subscription);
		findEsbCallRequired(subscription);
		if (StringUtils.isEmpty(subscription.getAutoRenewal()))
			findIsAutoRenewal(subscription);
		if (subscription.isSomCallRqd()) {
			executionContext.getEnrichmentResults().put("somCallRqd", true);
		}
		if (subscription.isEsbCallRqd()) {
			executionContext.getEnrichmentResults().put("esbCallRqd", true);
		}
		return subscription;
	}

	private void findEsbCallRequired(Subscription subscription) {
		if (subscription.getCfss() != null) {
			for (CFSRef cfs : subscription.getCfss()) {
				if (GenericConstants.CFS_M1_Base.equalsIgnoreCase(cfs.getName())) {
					if (cfs.getCharacteristics() != null) {
						cfs.getCharacteristics().stream()
								.filter(characteristics -> GenericConstants.CHARACTERISTIC_EXTERNAL_OFFER_ID
										.equals(characteristics.getName()))
								.findFirst().ifPresent(
										externalOfferIdChar -> subscription.setExternalOfferId(externalOfferIdChar.getValue()));
					}
					subscription.setEsbCallRqd(true);
					break;
				}
			}
		}
	}

	private void findIsAutoRenewal(Subscription subscription) {
		subscription.setAutoRenewal("0");// default 0-false
		if (subscription.getCfss() != null) {
			for (CFSRef cfs : subscription.getCfss()) {
				if (GenericConstants.CFS_UPC.equalsIgnoreCase(cfs.getName())) {
					cfs.getCharacteristics().stream().filter(
							characteristics -> GenericConstants.CHARACTERISTIC_IS_RENEWAL.equals(characteristics.getName()))
							.findFirst().ifPresent(isRenewalCharObj -> {
								if (StringUtils.isNotEmpty(isRenewalCharObj.getValue())) {
									if ("true".equalsIgnoreCase(isRenewalCharObj.getValue()))
										subscription.setAutoRenewal("1");

								}
							});
					break;
				}
			}
		}
	}

	private void findSOMCallRequired(ProductOffering productOffer, Subscription subscription) {
		List<CFSRef> somEntities = new ArrayList<>();
		if (subscription.getCfss() != null)
			somEntities.addAll(subscription.getCfss());
		if (subscription.getPrss() != null)
			somEntities.addAll(subscription.getPrss());
		if (subscription.getLrss() != null)
			somEntities.addAll(subscription.getLrss());
		boolean somCallRequired = true;
		if (!somEntities.isEmpty()) {
			for (CFSRef somEntity : somEntities) {
				if (somEntity.getCharacteristics() != null) {
					var somCallCharacteristic = somEntity.getCharacteristics().stream()
							.filter(characteristic -> characteristic.getName()
									.equalsIgnoreCase(GenericConstants.CHARACTERISTIC_SOM_CALL_REQUIRED))
							.findFirst().orElse(null);
					if (somCallCharacteristic != null && "false".equalsIgnoreCase(somCallCharacteristic.getValue())) {
						somCallRequired = false;
						break;
					}
				}
			}
		} else {
			somCallRequired = false;
		}
		subscription.setSomCallRqd(somCallRequired);

		if (subscription.getCfss() != null)
			subscription.getCfss().forEach(cfs -> {
				var cfsName = cfs.getName();
				if (productOffer.getProductSpecifications().get(0).getRelationships() != null)
					productOffer.getProductSpecifications().get(0).getRelationships().forEach(relation -> {
						if (relation.getTargetObject() != null && relation.getTargetObject().getName() != null
								&& cfsName.equalsIgnoreCase(relation.getTargetObject().getName())
								&& Boolean.parseBoolean(relation.getSkipSOM())) {
							cfs.setSkipSom(relation.getSkipSOM());
						}
					});

			});
	}

	protected void enrichCfssPrssLrss(ProductOffering productOffering, Subscription subscription) {
		if (ObjectUtils.isNotEmpty(productOffering.getProductSpecifications())) {
			List<CFSRef> cfss = productOffering.getProductSpecifications().stream()
					.filter(product -> ObjectUtils.isNotEmpty(product.getCfss())).flatMap(spec -> spec.getCfss().stream())
					.collect(Collectors.toList());
			List<CFSRef> prss = productOffering.getProductSpecifications().stream()
					.filter(product -> ObjectUtils.isNotEmpty(product.getPrss())).flatMap(spec -> spec.getPrss().stream())
					.collect(Collectors.toList());
			List<CFSRef> lrss = productOffering.getProductSpecifications().stream()
					.filter(product -> ObjectUtils.isNotEmpty(product.getLrss())).flatMap(spec -> spec.getLrss().stream())
					.collect(Collectors.toList());
			if (!cfss.isEmpty()) {
				if (subscription.getCfss() == null || subscription.getCfss().isEmpty()) {
					subscription.setCfss(cfss);
				} else {
					enrichCfss(cfss, subscription);
				}
			}
			if (!prss.isEmpty()) {
				if (subscription.getPrss() == null || subscription.getPrss().isEmpty()) {
					subscription.setPrss(prss);
				} else {
					enrichPrss(prss, subscription);
				}
			}
			if (!lrss.isEmpty()) {
				if (subscription.getLrss() == null || subscription.getLrss().isEmpty()) {
					subscription.setLrss(lrss);
				} else {
					enrichLrss(lrss, subscription);
				}
			}

		}
	}

	private void enrichCfss(List<CFSRef> cfss, Subscription subscription) {
		List<CFSRef> newCfss = new ArrayList<>();
		for (CFSRef cfs : cfss) {
			boolean isCfsPresent = false;
			for (CFSRef subCfs : subscription.getCfss()) {
				if (cfs.getName().equalsIgnoreCase(subCfs.getName())) {
					isCfsPresent = true;
					break;
				}
			}
			if (!isCfsPresent) {
				newCfss.add(cfs);
			}
		}
		if (!newCfss.isEmpty())
			subscription.getCfss().addAll(newCfss);
	}

	private void enrichPrss(List<CFSRef> prss, Subscription subscription) {
		List<CFSRef> newPrss = new ArrayList<>();
		for (CFSRef prs : prss) {
			boolean isCfsPresent = false;
			for (CFSRef subPrs : subscription.getPrss()) {
				if (prs.getName().equalsIgnoreCase(subPrs.getName())) {
					isCfsPresent = true;
					break;
				}
			}
			if (!isCfsPresent) {
				newPrss.add(prs);
			}
		}
		if (!newPrss.isEmpty())
			subscription.getPrss().addAll(newPrss);

	}

	private void enrichLrss(List<CFSRef> lrss, Subscription subscription) {
		List<CFSRef> newLrss = new ArrayList<>();
		for (CFSRef lrs : lrss) {
			boolean isLrsPresent = false;
			for (CFSRef subLrs : subscription.getLrss()) {
				if (lrs.getName().equalsIgnoreCase(subLrs.getName())) {
					isLrsPresent = true;
					break;
				}
			}
			if (!isLrsPresent) {
				newLrss.add(lrs);
			}
		}
		if (!newLrss.isEmpty())
			subscription.getLrss().addAll(newLrss);

	}

	protected void enrichCharges(List<Charge> charges, List<ProductOfferingPrice> productOfferingPrices) {
		if (productOfferingPrices != null) {
			List<Charge> newCharges = new ArrayList<>();
			for (ProductOfferingPrice price : productOfferingPrices) {
				boolean presentInOrder = false;
				if (!charges.isEmpty()) {
					for (Charge charge : charges) {
						if (price.getId().equals(charge.getUpcChargeId())) {
							presentInOrder = true;
							enrichExistingCharge(charge);
							break;
						}
					}
				}
				if (!presentInOrder) {
					newCharges.add(createNewCharge(price));
				}
			}
			if (!newCharges.isEmpty())
				charges.addAll(newCharges);
		}
	}

	protected Charge createNewCharge(ProductOfferingPrice price) {
		var charge = new Charge();
		charge.setUpcChargeId(price.getId());
		charge.setUpcChargeName(price.getName());
		charge.setChargeDesc(price.getName());
		charge.setAmount(price.getPrice());
		charge.setRate(price.getPrice());
		if (price.getPriceCategory() != null) {
			if (price.getPriceCategory().getBillingChargeType() != null
					&& price.getPriceCategory().getBillingChargeType().getId() != null) {
				charge.setChargeCategory(price.getPriceCategory().getBillingChargeType().getId());
			}
			if (price.getPriceCategory().getBillingChargeCategory() != null
					&& price.getPriceCategory().getBillingChargeCategory().getId() != null) {
				if ("1".equalsIgnoreCase(price.getPriceCategory().getBillingChargeCategory().getId())
						|| "2".equalsIgnoreCase(price.getPriceCategory().getBillingChargeCategory().getId()))
					charge.setBillingChargeType(price.getPriceCategory().getBillingChargeCategory().getId());
			}
		}
		if (price.getPriceMode() != null) {
			if ("2".equals(price.getPriceMode().getId()))
				charge.setChargeType("1");
			else
				charge.setChargeType("0");
			charge.setChargeRecurranceType(charge.getChargeType());
		}
		if (price.getPriceCode() != null)
			charge.setChargeCode(price.getPriceCode().getCode());
		
		  charge.setIsProrata(BooleanUtils.toBoolean(price.getProrataEnable()) ? "1" : "0");
	      charge.setProrationFlag(getProrationFlag(price));
		
		
		charge.setChargeVersion(price.getVersionNo());
		if (price.getFrequency() != null)
			charge.setChargeFrequency(price.getFrequency().getId());
		charge.setChargeFactor(price.getFactor());
		return charge;
	}

    private String getProrationFlag(ProductOfferingPrice price) {
     	
    	var suspend=getIntegerValue(price.getProrataEnabledInSuspension());
    	var terminate=getIntegerValue(price.getProrataEnabledInTermination());
    	var planChange=getIntegerValue(price.getProrataEnabledInPlanChange());
    	var activation=getIntegerValue(price.getProrataEnabledInActivation());
    
    	
    	StringBuilder prorationFlagBuilder= new StringBuilder();
    	prorationFlagBuilder.append(suspend).append(terminate).append(planChange).append(activation);

    	return prorationFlagBuilder.toString();
    }
    
    private Integer getIntegerValue (String prorata) {
    	return BooleanUtils.toInteger(BooleanUtils.toBoolean(prorata));
    }

	protected void enrichExistingCharge(Charge charge) {
		if (charge.getChargeType() != null) {
			if ("2".equals(charge.getChargeType()))
				charge.setChargeType("1");
			else
				charge.setChargeType("0");
			charge.setChargeRecurranceType(charge.getChargeType());
		}
		if (charge.getBillingChargeType() != null) {
			if (!("1".equals(charge.getBillingChargeType()) || "2".equals(charge.getBillingChargeType())))
				charge.setBillingChargeType(null);				
		}
	}

}
