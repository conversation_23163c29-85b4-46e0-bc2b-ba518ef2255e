
/**
 * 
 */
package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.om.model.dto.order.CFSCharacteristicRef;
import in.co.sixdee.bss.om.model.dto.order.CFSRef;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.ProductSpecification;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.ServiceOrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 *
 */
@Log4j2
@Component(value = "somCreateService")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@RequiredArgsConstructor
public class SOMCreateService extends AbstractDelegate {

	protected Order					orderPayload	= null;
	protected String				request			= null;
	protected int					index			= 0;
	protected String				iccid			= null;

	protected String				imsi			= null;
	protected String				planId			= null;
	protected String				basePlanId		= null;
	protected String				meName			= null;
	protected String				algoId			= null;
	protected String				kdbId			= null;

	protected String				ki				= null;
	protected String				msisdn			= null;

	protected final ObjectMapper	objectMapper;

	@Override
	protected void execute() throws Exception {
		try {
			orderPayload = executionContext.getOrder();
			request = createSOMRequest();
			var callThirdPartyDTO = callThirdParty(request);
			if (callThirdPartyDTO == null) {
				executionContext.setError(true);
				return;
			}
			validateResponse(callThirdPartyDTO);
		} catch (Exception e) {
			log.error("{} In {} .execute Exception occurred ", executionContext.getTraceId(), this.getClass().getName(), e);
		}
	}

	/* To Form the SOM request */
	protected String createSOMRequest() throws Exception {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		var serviceOrder = new SOMServiceOrderDTO();
		serviceOrder.setExternalId(executionContext.getOrder().getOrderId());
		serviceOrder.setDescription(orderPayload.getDescription());
		serviceOrder.setRequestedStartDate(
				StringUtils.isNotEmpty(orderPayload.getRequestedStartDate()) ? orderPayload.getRequestedStartDate()
						: Instant.now().toString());
		serviceOrder.setRequestedCompletionDate(orderPayload.getRequestedCompletionDate());
		serviceOrder.setType("ServiceOrder");
		msisdn = orderPayload.getServiceManagement().getServiceId();
		serviceOrder.setExternalServiceId(msisdn);
		executionContext.getAttributes().put("callbackCorrelationId",
				callbackEvent + ":" + executionContext.getAttributes().get(GenericConstants.SUB_ORDER_ID));
		var addServiceOrderItemList = createAddServiceOrderItem();

		if (ObjectUtils.isNotEmpty(addServiceOrderItemList))
			serviceOrderItemList.addAll(addServiceOrderItemList);

		serviceOrder.setServiceOrderItem(serviceOrderItemList);
		if (ObjectUtils.isNotEmpty(serviceOrder))
			request = objectMapper.writeValueAsString(serviceOrder);
		return request;
	}

	protected List<SOMService> getSOMServiceRegistry() {
		List<SOMService> somFetchServices = new ArrayList<>();
		if (executionContext.getWorkflowData().containsKey("SOMFetchServiceRegistryResponseAttributes")) {
			somFetchServices = objectMapper.convertValue(
					executionContext.getWorkflowData().get("SOMFetchServiceRegistryResponseAttributes"),
					new TypeReference<List<SOMService>>() {});
		}
		if (ObjectUtils.isEmpty(somFetchServices)) {
			log.info(" No services found with the serviceId :: {}", msisdn);
			return null;
		}
		somFetchServices = somFetchServices.stream().filter(service -> service.getState().equalsIgnoreCase("active"))
				.collect(Collectors.toList());
		return somFetchServices;
	}

	protected List<ServiceOrderItem> createAddServiceOrderItem() {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		List<Subscription> subscriptions = new ArrayList<>();
		getNumberDetails();
		if (ObjectUtils.isNotEmpty(orderPayload.getServiceManagement().getSubscriptions())) {
			subscriptions = orderPayload.getServiceManagement().getSubscriptions();
		}
		if (ObjectUtils.isNotEmpty(subscriptions)) {
			// newBasePlan = subscriptions.stream().filter(sub ->
			// StringUtils.isNotEmpty(sub.getPlanType())
			// &&
			// sub.getPlanType().equalsIgnoreCase(GenericConstants.PLAN_TYPE_BASE)).findFirst().get();
			// subscriptions.removeIf(sub ->
			// sub.getPlanType().equalsIgnoreCase(GenericConstants.PLAN_TYPE_BASE));//remove
			subscriptions.stream().forEach(subscription -> {
					var addServiceOrderItem = addServiceOrderItem(subscription); //here is where the add items will be added, seems like both new base plan and retainable will come here.test once
					if (ObjectUtils.isNotEmpty(addServiceOrderItem))
						serviceOrderItemList.addAll(addServiceOrderItem);
			});
		}

		return serviceOrderItemList;

	}

	protected List<ServiceOrderItem> addServiceOrderItem(Subscription subscription) {
		List<ServiceOrderItem> serviceOrderItemList = new ArrayList<>();
		planId = subscription.getPlanId();
		if (ObjectUtils.isNotEmpty(subscription.getCfss())) {
			subscription.getCfss().stream().forEach(cfss -> {
				if (ObjectUtils.isNotEmpty(cfss.getSkipSom()) && Boolean.parseBoolean(cfss.getSkipSom())) {

				} else {
					var serviceOrderItem = createServiceOrderItem(cfss, "CFS");
					serviceOrderItemList.add(serviceOrderItem);
				}

			});
		}
		if (ObjectUtils.isNotEmpty(subscription.getPrss())) {
			subscription.getPrss().stream().forEach(prss -> {
				var serviceOrderItem = createServiceOrderItem(prss, "PRS");
				serviceOrderItemList.add(serviceOrderItem);
			});
		}
		if (ObjectUtils.isNotEmpty(subscription.getLrss())) {
			subscription.getLrss().stream().forEach(lrss -> {
				var serviceOrderItem = createServiceOrderItem(lrss, "LRS");
				serviceOrderItemList.add(serviceOrderItem);
			});
		}
		return serviceOrderItemList;

	}

	protected ServiceOrderItem createServiceOrderItem(CFSRef cfss, String type) {
		var serviceOrderItem = new ServiceOrderItem();
		index = index + 1;
		serviceOrderItem.setId(String.valueOf(index));
		serviceOrderItem.setAction("add");
		serviceOrderItem.setType("ServiceOrderItem");
		var serviceItem = createServiceItem(cfss, type);
		serviceOrderItem.setService(serviceItem);
		return serviceOrderItem;
	}

	protected SOMService createServiceItem(CFSRef cfss, String type) {
		var serviceItem = new SOMService();
		serviceItem.setState("active");
		serviceItem.setType(type);
		var specification = new ProductSpecification();
		specification.setName(cfss.getName());
		specification.setId(cfss.getId());
		serviceItem.setServiceSpecification(specification);
		var characteristics = createServiceCharacteristic(cfss.getCharacteristics());
		//if (type.equalsIgnoreCase("CFS")) {
			setSubscriptionId(characteristics, planId, false);
		//}
		serviceItem.setServiceCharacteristic(characteristics);
		return serviceItem;
	}

	protected void setSubscriptionId(List<Characteristic> characteristics, String planId, boolean ismodifySubscriptionId) {

		if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData().get("subscriptionIdMap"))) {
			@SuppressWarnings("unchecked")
			HashMap<String, String> subscriptionIdMap = (HashMap<String, String>) executionContext.getWorkflowData()
					.get("subscriptionIdMap");
			if (subscriptionIdMap.get(planId) != null) {
				var characteristic = new Characteristic();
				characteristic.setName("SUBSCRIPTION_ID");
				characteristic.setValueType("String");
				characteristic.setValue(subscriptionIdMap.get(planId));
				if (ismodifySubscriptionId)
					characteristic.setFinalValue(subscriptionIdMap.get(planId));
				characteristics.add(characteristic);
			}

		}
	}

	protected List<Characteristic> createServiceCharacteristic(List<CFSCharacteristicRef> characteristics) {
		var characteristicList = new ArrayList<Characteristic>();
		if (ObjectUtils.isNotEmpty(characteristics))
			characteristics.stream().forEach(cfsCharacteristic -> {
				var characteristic = new Characteristic();
				if (!"SUBSCRIPTION_ID".equals(cfsCharacteristic.getName())) {
					characteristic.setName(cfsCharacteristic.getName());
					characteristic.setValueType(cfsCharacteristic.getDataType());
					if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("ICCID"))
						characteristic.setValue(iccid);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("IMSI"))
						characteristic.setValue(imsi);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("KI"))
						characteristic.setValue(ki);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("MSISDN"))
						characteristic.setValue(msisdn);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("meName"))
						characteristic.setValue(meName);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("ALGOID"))
						characteristic.setValue(algoId);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& cfsCharacteristic.getName().equalsIgnoreCase("KDBID"))
						characteristic.setValue(kdbId);
					else if (ObjectUtils.isNotEmpty(cfsCharacteristic.getName())
							&& ObjectUtils.isNotEmpty(orderPayload.getServiceManagement().getDestinationConnectionType())
							&& cfsCharacteristic.getName().equalsIgnoreCase("CONNECTION_TYPE"))
						characteristic.setValue(orderPayload.getServiceManagement().getDestinationConnectionType());
					else
						characteristic.setValue(cfsCharacteristic.getValue());
					characteristicList.add(characteristic);
				}
			});
		return characteristicList;
	}

	protected void getNumberDetails() {
		try {
			if (ObjectUtils.isNotEmpty(executionContext.getWorkflowData())
					&& executionContext.getWorkflowData().containsKey("SOMFetchServiceRegistryResponseAttributes")) {

				var serviceList = objectMapper.convertValue(
						executionContext.getWorkflowData().get("SOMFetchServiceRegistryResponseAttributes"),
						new TypeReference<List<SOMService>>() {});
				if (ObjectUtils.isNotEmpty(serviceList)) {
					for (SOMService service : serviceList) {
						if (ObjectUtils.isNotEmpty(service.getServiceSpecification())
								&& ObjectUtils.isNotEmpty(service.getServiceSpecification().getName())
								&& StringUtils.equalsIgnoreCase(service.getServiceSpecification().getName(), "PRS_SIM")) {
							var characteristics = service.getServiceCharacteristic();
							for (Characteristic characteristic : characteristics) {
								if (ObjectUtils.isNotEmpty(characteristic.getName())
										&& ObjectUtils.isNotEmpty(characteristic.getValue())
										&& StringUtils.equalsIgnoreCase(characteristic.getName(), "KI"))
									ki = characteristic.getValue();

								else if (ObjectUtils.isNotEmpty(characteristic.getName())
										&& ObjectUtils.isNotEmpty(characteristic.getValue())
										&& StringUtils.equalsIgnoreCase(characteristic.getName(), "IMSI"))
									imsi = characteristic.getValue();
								else if (ObjectUtils.isNotEmpty(characteristic.getName())
										&& ObjectUtils.isNotEmpty(characteristic.getValue())
										&& StringUtils.equalsIgnoreCase(characteristic.getName(), "ICCID"))
									iccid = characteristic.getValue();
								else if (ObjectUtils.isNotEmpty(characteristic.getName())
										&& ObjectUtils.isNotEmpty(characteristic.getValue())
										&& StringUtils.equalsIgnoreCase(characteristic.getName(), "meName"))
									meName = characteristic.getValue();
								else if (ObjectUtils.isNotEmpty(characteristic.getName())
										&& ObjectUtils.isNotEmpty(characteristic.getValue())
										&& StringUtils.equalsIgnoreCase(characteristic.getName(), "ALGOID"))
									algoId = characteristic.getValue();
								else if (ObjectUtils.isNotEmpty(characteristic.getName())
										&& ObjectUtils.isNotEmpty(characteristic.getValue())
										&& StringUtils.equalsIgnoreCase(characteristic.getName(), "KDBID"))
									kdbId = characteristic.getValue();

							}
						}
					}
				}
			}
		} catch (Exception e) {
			log.error(" Exception occurred in getKIDetails ", e);
		}

	}

	protected List<Subscription> getSubscriptions(Object subscription) {
		List<Subscription> subscriptions = new ArrayList<>();
		try {
			var response = in.co.sixdee.bss.common.util.JsonUtils.marshall(subscription, null);
			subscriptions = objectMapper.readValue(response, new TypeReference<List<Subscription>>() {});
		} catch (Exception e) {
			log.error(" :::::  Exception occured in getSubscriptions execute method  :::::", e);
		}

		return subscriptions;
	}
}
