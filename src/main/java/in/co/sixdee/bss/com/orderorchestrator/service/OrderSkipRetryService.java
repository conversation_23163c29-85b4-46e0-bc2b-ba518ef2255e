
package in.co.sixdee.bss.com.orderorchestrator.service;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.WaitingProcessInfoRepository;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.RuntimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Log4j2
public class OrderSkipRetryService {

	@Autowired
	private RuntimeService runtimeService;

	@Autowired
	WaitingProcessInfoRepository waitingProcessInfoRepository;




}
