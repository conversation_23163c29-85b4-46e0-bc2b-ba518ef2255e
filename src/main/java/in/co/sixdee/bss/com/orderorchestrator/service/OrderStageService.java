/**
 *
 */
package in.co.sixdee.bss.com.orderorchestrator.service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import in.co.sixdee.bss.com.orderorchestrator.config.ApplicationProperties;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.NativeStageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.EsbRetryRequestIdEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.EsbRetryRequestIdRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderStageRepository;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.event.StageStatusChangedEvent;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class OrderStageService {


    private final EsbRetryRequestIdRepository esbRetryRequestIdRepository;

    private final OrderStageRepository orderStageRepository;

    private final GetDataFromCache cache;

    private final ApplicationProperties applicationProperties;

    private final NativeStageRepository nativeStageRepository;


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveStages(List<OrderStageEntity> stages) {
        if (applicationProperties.isNativeQueryInsertion())
            nativeStageRepository.saveAll(stages);
        else
            orderStageRepository.saveAll(stages);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateRetryRequestId(OrderFlowContext executionContext, StageStatusChangedEvent event) {

        var stageConfigs = cache.getCacheDetailsFromDBMap(NGTableConstants.COM_ESB_RETRY_REQUIRED_STAGES, executionContext.getOrder().getOrderType());

        if (ObjectUtils.isNotEmpty(stageConfigs)
                && ObjectUtils.isNotEmpty(stageConfigs.getNgTableData().get("STAGE_ID"))) {
            List<String> stageIds = Arrays.asList(stageConfigs.getNgTableData().get("STAGE_ID").split(","));
            boolean stageExists = stageIds.stream().anyMatch(event.getStageCode()::equalsIgnoreCase);

            if (stageExists) {
                String retryRequestId = null;
                Long orderId = Long.valueOf(executionContext.getOrder().getOrderId());
                Long subOrderId = Long.valueOf(executionContext.getAttributes().get(GenericConstants.SUB_ORDER_ID));

                String retryRequestIdExisting = esbRetryRequestIdRepository.getAttributesByOrderIdAndAttribute(
                        Long.valueOf(executionContext.getOrder().getOrderId()), subOrderId);
                if (!"failed".equalsIgnoreCase(event.getStageStatus())) {
                    if (StringUtils.isNotEmpty(retryRequestIdExisting))
                        esbRetryRequestIdRepository.deleteRetryRequestIdByOrderId(orderId, subOrderId);
                } else {

                    if (StringUtils.isNotEmpty(executionContext.getRetryRequestId())) {
                        retryRequestId = executionContext.getRetryRequestId();

                        if (StringUtils.isNotEmpty(retryRequestIdExisting)) {
                            esbRetryRequestIdRepository.updateRetryRequestIdByOrderId(orderId, subOrderId,
                                    retryRequestId);
                        } else {
                            if (StringUtils.isNotEmpty(executionContext.getRetryRequestId()))
                                if (StringUtils.isNotEmpty(retryRequestId)) {
                                    var esbRetryRequestEntity = new EsbRetryRequestIdEntity();
                                    esbRetryRequestEntity.setRetryRequestId(retryRequestId);
                                    esbRetryRequestEntity.setOrderId(orderId);
                                    esbRetryRequestEntity.setSubOrderId(subOrderId);
                                    if (ObjectUtils.isNotEmpty(esbRetryRequestEntity)) {
                                        esbRetryRequestIdRepository.save(esbRetryRequestEntity);

                                    }
                                }
                        }
                    }
                }
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void deleteRetryRequestIdAttributes(String orderId, String subOrderId) {
        esbRetryRequestIdRepository.deleteRetryRequestIdByOrderId(Long.parseLong(orderId), Long.parseLong(subOrderId));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateStatusAndReasonByOrderIdForApproval(String stageStatus, String stateReason, long orderId,
                                                          String stageCode, Date lastModifiedDate) {
        orderStageRepository.updateStatusAndReasonByOrderId(stageStatus, stateReason, orderId, stageCode, null);

    }

    public String findSubmitPortInStage(long orderId) {
        return orderStageRepository.findSubmitPortInStageId(orderId);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void deletePortInNotificationStages(long orderId, long subOrderId) {
        log.info("Deleting port-in notification stages");
        orderStageRepository.deletePortInNotificationStages(orderId, subOrderId);
    }

    @Transactional
    public void updateStatusAndReasonByOrderId(String state, String stateReason, Long orderId, String stageCode, String lastModifiedBy) {
        orderStageRepository.updateStatusAndReasonByOrderId(state, stateReason,
                orderId, stageCode, lastModifiedBy);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateStatusAndReasonByOrderIdInNewTxn(String state, String stateReason, Long orderId, String stageCode, String lastModifiedBy) {
        orderStageRepository.updateStatusAndReasonByOrderId(state, stateReason,
                orderId, stageCode, lastModifiedBy);
    }

    @Transactional
    public void updateStatusAndReasonBySubOrderId(String state, String stateReason, Long subOrderId, String stageCode, String lastModifiedBy) {
        orderStageRepository.updateStatusAndReasonBySubOrderId(state, stateReason,
                subOrderId, stageCode, lastModifiedBy);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateStatusAndReasonBySubOrderIdInNewTxn(String state, String stateReason, Long subOrderId, String stageCode, String lastModifiedBy) {
        orderStageRepository.updateStatusAndReasonBySubOrderId(state, stateReason,
                subOrderId, stageCode, lastModifiedBy);
    }


    public boolean existsByOrderIdAndName(Long orderId, String stageName) {
        return orderStageRepository.existsByOrderIdAndName(orderId,
                stageName);
    }

    public boolean existsBySubOrderIdAndName(Long subOrderId, String stageName) {
        return orderStageRepository.existsBySubOrderIdAndName(subOrderId,
                stageName);
    }


    public OrderStageEntity findStageBySubOrderIdAndName(Long subOrderId, String stageCode) {
        return orderStageRepository.findStagebySubOrderIdAndName(subOrderId, stageCode);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int updateRollbackStatus(String state, String rollbackStatus, String stateReason, Long id) {
        return orderStageRepository.updateRollbackStatus(state, rollbackStatus, stateReason, id);
    }

    @Transactional
    public int updateRollbackStatusByOrderId(String state, String rollbackStatus, String stateReason,
                                             Long orderId, String stageCode) {
        return orderStageRepository.updateRollbackStatusByOrderId(state, rollbackStatus,
                stateReason, orderId, stageCode);

    }

    public OrderStageEntity findStagebySubOrderIdAndName(Long subOrderId, String stageCode) {
        return orderStageRepository.findStagebySubOrderIdAndName(subOrderId, stageCode);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int updateModifiedUser(String userName, String suborderId, String stageId) {
        return orderStageRepository.updateModifiedUserBySubOrderIdandStageId(userName, Long.valueOf(suborderId),
                stageId);
    }

    public OrderStageEntity findLastModifiedDateAndSateReasonByStageCode(Long orderId, Long subOrderId, String stageCode) {
        return orderStageRepository.findLastModifiedDateAndSateReasonByStageCode(orderId, subOrderId, stageCode);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateStatusAndReasonAndModifiedDateByOrderId(String state, String stateReason, long orderId, String stageCode, String lastModifiedBy, Date lastModifiedDate) {
        orderStageRepository.updateStatusAndReasonAndModifiedDateByOrderId(state, stateReason,
                orderId, stageCode, lastModifiedBy,lastModifiedDate);
    }
}
