package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.SubOrderEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Repository
public interface SubOrderRepository extends JpaRepository<SubOrderEntity, Long> {

	@Modifying(clearAutomatically = true)
	@Query("UPDATE SubOrderEntity so SET so.state = :state, so.stateReason = :stateReason, so.failedStageCode = :failedStageCode where so.id = :id")
	public int updateSubOrderStatus(@Param("state") String state, @Param("stateReason") String stateReason, @Param("id") Long id, String failedStageCode);


	@Modifying(clearAutomatically = true)
	@Query("UPDATE SubOrderEntity so SET so.state = :state, so.stateReason = :stateReason, so.failedStageCode = :failedStageCode where so.orderId = :orderId")
	public int updateSubOrderStatusByOrderId(@Param("state") String state, @Param("stateReason") String stateReason,
			@Param("orderId") Long orderId, String failedStageCode);

	/*@Query("select case when count(so)>0 then false else true end from SubOrderEntity so where so.orderId=:orderId and so.state <> 'Completed'")
	public boolean isAllSubOrdersCompleted(@Param("orderId") Long orderId);
*/
	@Query("select s.state from SubOrderEntity s where s.orderId=:orderId")
	public List<String> getSuborderStatusByOrderId(@Param("orderId") Long orderId);

	@Query("select case when count(so)>=1 then true else false end from SubOrderEntity so where so.orderId=:orderId and so.state = 'Completed'")
	public boolean checkIfSubOrderCompleted(@Param("orderId") Long orderId);

	@Query("select case when count(so)>=1 then true else false end from SubOrderEntity so where so.orderId=:orderId and so.state = 'Failed'")
	public boolean checkIfSubOrderFailedOrInProgress(@Param("orderId") Long orderId);

	@Query("select case when count(so)>=1 then false else true end from SubOrderEntity so where so.orderId=:orderId and so.state<>'Failed'")
	public boolean checkIfAllSubOrdersFailed(@Param("orderId") Long orderId);


	@Query("select case when count(so)>1 then false else true end from SubOrderEntity so where so.orderId=:orderId and so.state in ('Failed','In-Progress','Pending','Cancelled')")
	public boolean isAllSubOrdersCompletedForSkip(@Param("orderId") Long orderId);

	@Query("select case when count(so)>0 then false else true end from SubOrderEntity so where so.orderId=:orderId and so.state in ('Failed','In-Progress','Pending','Cancelled')")
	public boolean isAllSubOrdersCompleted(@Param("orderId") Long orderId);

	@Query("select s.id from SubOrderEntity s where s.orderId=:orderId")
	public List<Long> getSubordersByOrderId(@Param("orderId") Long orderId);

	@Modifying(clearAutomatically = true)
	@Query("UPDATE SubOrderEntity s SET s.serviceId = :serviceId  where s.id = :id")
	public int updateServiceId(Long id, String serviceId);

	@Modifying(clearAutomatically = true)
	@Query("UPDATE SubOrderEntity s SET s.state = 'In-Progress' where s.id = :id")
	public void updateSubOrderToInProgress(Long id);

	/*
	 * @Query("select count(so.id) from SubOrder so where so.state<>'Completed' and so.orderId= :orderId"
	 * ) public int getIncompletedSubOrderCount(@Param("orderId") Long orderId);
	 */

	@Transactional
	@Modifying(clearAutomatically = true)
	@Query("UPDATE SubOrderEntity so SET so.state = :state, so.stateReason = :stateReason where so.orderId = :orderId")
	public int updateSubOrderStatusByOrderIdToCancelled(@Param("state") String state, @Param("stateReason") String stateReason,
			@Param("orderId") Long orderId);


	@Query("select count(so.id) from SubOrderEntity so where so.serviceId=:serviceId")
	public int getOrderIdsCountByServiceId(@Param("serviceId") String serviceId);
	
	@Query("select count(so.id) from SubOrderEntity so where so.serviceId=:serviceId and so.state in ('Failed')")
	public int getOrderIdsCountByServiceIdAndState(@Param("serviceId") String serviceId);
	
	@Query("select case when count(so)>1 then true else false end from SubOrderEntity so where so.serviceId=:serviceId and so.state in ('Failed')")
	public boolean isAnyOrderIdsByServiceId(@Param("serviceId") String serviceId);
	
	@Query("select case when count(so)>1 then true else false end from SubOrderEntity so where so.serviceId=:serviceId and so.state in ('Failed') and so.stateReason not like '%Service Id is not%'")
	public boolean isAnyOrderIdsByServiceIdAndReason(@Param("serviceId") String serviceId);
	
	@Query("select count(so) from SubOrderEntity so where so.serviceId=:serviceId and so.state in ('Failed') and so.stateReason not like '%Service Id is not%'")
	public int getOrderIdsCountByServiceIdAndReason(@Param("serviceId") String serviceId);
	
	@Query("select s.id from SubOrderEntity s where s.orderId=:orderId and s.state = 'Failed'")
	public List<Long> getRetrySubOrderIds(@Param("orderId") Long orderId);
	
	@Query("select so.orderId FROM SubOrderEntity so where so.state = 'Failed' and so.serviceId = :serviceId and so.stateReason not like '%Service Id is not%'")
	public List<Long> findOrderIdsByServiceId(@Param("serviceId") String serviceId);

	@Transactional
	@Modifying(clearAutomatically = true)
	@Query("UPDATE SubOrderEntity so SET so.state = :state, so.stateReason = :stateReason where so.orderId = :orderId and so.state not in ('In-Progress')")
	public int updateSubOrderStatusForMNPNotification(@Param("state") String state, @Param("stateReason") String stateReason,
	                                        @Param("orderId") Long orderId);

	@Query("select new SubOrderEntity (o.orderId,o.id) from SubOrderEntity o where o.serviceId = :serviceId and o.state=:state and o.stateReason=:stateReason order by o.createdDate desc limit 1")
	public SubOrderEntity getDetailsForDisconnectServiceInternal(@Param("serviceId")String serviceId,@Param("state")String state, @Param("stateReason")String stateReason);

	@Query("select s.id from SubOrderEntity s where s.orderId=:orderId")
	public Long getSubOrderIdForAudit(@Param("orderId") Long orderId);
}
