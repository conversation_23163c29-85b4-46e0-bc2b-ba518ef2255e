package in.co.sixdee.bss.com.orderorchestrator.service;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.WaitingProcessInfoRepository;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;

@Service
@Log4j2
public class OrderStateService {

	@Autowired
	private WaitingProcessInfoRepository	waitingProcessInfoRepository;

	@Autowired
	private OrderStatusManager		orderStatusManager;

	public void processOrderUpdates(String updateKey, OrderFlowContext orderFlowContext) {
		String[] keys = null;
		try {
			keys = updateKey.split(",");
			for (String key : keys) {
				if (StringUtils.isNotEmpty(key)) {
					switch (key) {
						case "ORDER_PROFILE":
							if (orderFlowContext.getAttributes().get(GenericConstants.PROFILE_ID) != null) {
								orderStatusManager.updateProfileId(orderFlowContext.getOrder().getOrderId(),
										orderFlowContext.getAttributes().get(GenericConstants.PROFILE_ID));
							}
							break;
						case "ORDER_ACCOUNT":
							if (orderFlowContext.getAttributes().get(GenericConstants.ACCOUNT_ID) != null) {
								orderStatusManager.updateAccountId(orderFlowContext.getOrder().getOrderId(),
										orderFlowContext.getAttributes().get(GenericConstants.ACCOUNT_ID));
							}
							break;
						case "ORDER_SERVICE":
							if (orderFlowContext.getAttributes().get(GenericConstants.SERVICE_ID) != null) {
								orderStatusManager.updateMsisdn(orderFlowContext.getOrder().getOrderId(),
										orderFlowContext.getAttributes().get(GenericConstants.SERVICE_ID));
							}
							break;
						/*
						 * case "SUBORDER_SERVICE_INTERNAL": if
						 * (orderFlowContext.getAttributes().get(GenericConstants.SERVICE_ID) !=
						 * null) { orderStatusUpdateService.updateServiceId(
						 * orderFlowContext.getAttributes().get(GenericConstants.SUB_ORDER_ID),
						 * orderFlowContext.getAttributes().get(GenericConstants.SERVICE_ID),
						 * orderFlowContext.getAttributes().get(GenericConstants.INTERNAL_SERVICE_ID
						 * )); } break;
						 */
						case "SUBORDER_SUBSCRIPTION":
							populateSubOrderAttributes(orderFlowContext);
							break;
						default:
							break;
					}
				}
			}

		} catch (Exception e) {
			log.error(" Exception occured in processOrderUpdates" + e, e);
			orderFlowContext.setError(true);
		}
	}

	/*public boolean validatePaymentRequest(OrderFlowContext orderFlowContext) {
		var orderPayload = orderFlowContext.getContinueRequest();
		var orderId = orderPayload.getOrderId();
		var waitingInfo = waitingProcessInfoRepository.findProcessInfoByOrderId(orderId,"Callback");
		if (waitingInfo == null) {
			throw new CommonException(StatusConstants.HttpConstants.CUSTOM_FIELD_VALIDATION,
					"No record found for the order id : " + orderId);
		}
		return true;
	}*/

	protected void populateSubOrderAttributes(OrderFlowContext orderFlowContext) {
		var attributes = orderFlowContext.getAttributes();
		var subscriptionIdList = new ArrayList<HashMap<String, String>>();
		if (MapUtils.isNotEmpty(attributes) && StringUtils.isNotEmpty(attributes.get(GenericConstants.SUBSCRIPTION_ID))) {
			if (ObjectUtils.isNotEmpty(orderFlowContext.getSubOrderAttributes().get("subscriptionIdMap")))
				orderFlowContext.getSubOrderAttributes().get("subscriptionIdMap").add(attributes);
			else {
				subscriptionIdList.add(attributes);
				orderFlowContext.getSubOrderAttributes().put("subscriptionIdMap", subscriptionIdList);
			}
			orderFlowContext.getWorkflowData().put("subOrderAttributes", orderFlowContext.getSubOrderAttributes());
		}
	}
}
