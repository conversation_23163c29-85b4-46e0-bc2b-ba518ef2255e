package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.MNPConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.enrichment.ProfileInfo;
import in.co.sixdee.bss.om.model.dto.order.IndividualIdentification;
import in.co.sixdee.bss.om.model.dto.order.Order;
import in.co.sixdee.bss.om.model.dto.order.ProfileRef;
import in.co.sixdee.bss.om.model.dto.order.ServiceManagement;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

@Log4j2
@Component(value = "identificationIdValidator")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class IdentificationIdValidator extends AbstractDelegate {

    private JsonNode billResp;
    String donorEntityId = null;
    private final String errorCode = "643";
    private final String errorMessage = "Incoming identification id is not matching with identification id in the system";
    String IncomingIdentificationId = null;

    @Override
    protected void execute() throws Exception {
        var callThirdPartyDTO = callThirdParty(null);
        if (callThirdPartyDTO == null) {
            executionContext.setError(true);
            return;
        }
        validateResponse(callThirdPartyDTO);
        if (executionContext.isError())
            return;
        checkForDataObjectInResponse(callThirdPartyDTO.getResponse());
        if (executionContext.isError())
            return;
        modifyWorkflowData(callThirdPartyDTO.getResponse());
        String bsBillingIdentificationId = parseBasicDetailsResponse();
        if (!validateIncomingIdenticationId(bsBillingIdentificationId)) {
            log.info("Incoming identifcation id is not matching with id present in BSS..");
            setError(errorCode, errorMessage);
        }
    }

    private boolean validateIncomingIdenticationId(String bsBillingIdentifcationId) {
        String orderType = executionContext.getOrder().getOrderType();
        List<IndividualIdentification> individualIdentifications = null;
        if (OrderTypes.INTERIM_NUMBER_PORT_IN.equalsIgnoreCase(orderType) || OrderTypes.MNP_PORT_IN.equalsIgnoreCase(orderType)) {
            individualIdentifications = getIdentificationIdForPortInCases(executionContext);
            if (individualIdentifications != null && bsBillingIdentifcationId != null && !bsBillingIdentifcationId.isEmpty()) {
                for (IndividualIdentification individualIdentification : individualIdentifications) {
                    if (bsBillingIdentifcationId.equalsIgnoreCase(individualIdentification.getIdentificationId()))
                        return true;
                }
            }
        }
        log.info("unable to validate identification id from BSS ..{}", bsBillingIdentifcationId);
        return false;
    }

    private List<IndividualIdentification> getIdentificationIdForPortInCases(OrderFlowContext executionContext) {
        Order order = executionContext.getOrder();
        ServiceManagement serviceManagement = order.getServiceManagement();
        if (OrderTypes.INTERIM_NUMBER_PORT_IN.equalsIgnoreCase(orderType)) {
            return serviceManagement.getIndividualIdentification();
        } else {
            return findIdDetailsForPortIn(executionContext, order);

        }
    }

    private List<IndividualIdentification> findIdDetailsForPortIn(OrderFlowContext executionContext, Order order) {
        ProfileRef profile = order.getProfile();
        boolean mnpAddService = StringUtils.isNotEmpty(profile.getProfileId());

        if (mnpAddService) {
            if (ObjectUtils.isNotEmpty(profile) && ObjectUtils.isNotEmpty(profile.getIndividualIdentification())) {
                return profile.getIndividualIdentification();

            }
            if (ObjectUtils.isNotEmpty(executionContext.getEnrichmentResults())
                    && ObjectUtils.isNotEmpty(executionContext.getEnrichmentResults().get("profileInfo"))) {
                ProfileInfo profileInfo = objectMapper.convertValue(
                        executionContext.getEnrichmentResults().get("profileInfo"), ProfileInfo.class);
                return profileInfo.getIndividualIdentification();
            }
        } else {
            return profile.getIndividualIdentification();
        }
        return Collections.emptyList();
    }

    private void checkForDataObjectInResponse(String response) throws JsonProcessingException {
        billResp = objectMapper.readTree(response);
        if (ObjectUtils.isEmpty(billResp) || ObjectUtils.isEmpty(billResp.get("data").get(0))) {
            setError("644", "identification id not found from billing");
        }
    }

    private String parseBasicDetailsResponse() {

        String bsIdentificationId = null;
        if (billResp.get("data").get(0).get("identificationId") != null)
            bsIdentificationId = billResp.get("data").get(0).get("identificationId").asText();
        return bsIdentificationId;
    }

    private void setError(String errorCode, String errorMessage) {
        executionContext.setError(true);
        executionContext.getErrorDetail().setSystem(callThirdPartyDTO.getThirdPartySystem());
        executionContext.getErrorDetail().setCode(errorCode);
        executionContext.getErrorDetail().setMessage(errorMessage);
    }
}
