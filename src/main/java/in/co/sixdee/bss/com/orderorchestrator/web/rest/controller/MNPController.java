package in.co.sixdee.bss.com.orderorchestrator.web.rest.controller;

import in.co.sixdee.bss.com.orderorchestrator.service.MNPCallbackService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/MNP")
public class MNPController {


    final MNPCallbackService mnpCallbackService;

    @PostMapping("/portIn/Notifications")
    public ResponseEntity<String> mnpCallback(@RequestBody String request) {
        String response = mnpCallbackService.processPortInNotificationRequest(request);
        return new ResponseEntity<String>(response, HttpStatus.OK);
    }

    @PostMapping("/portIn/connectService")
    public ResponseEntity<String> connectService(@RequestBody String request) {
        String response = mnpCallbackService.processConnectServiceRequest(request);
        return new ResponseEntity<String>(response, HttpStatus.OK);
    }
    
    @PostMapping("/portOut/disconnectServiceInternal")
    public ResponseEntity<String> disconnectServiceInternal(@RequestBody String request) {
        String response = mnpCallbackService.processDisconnectServiceIntRequest(request);
        return new ResponseEntity<String>(response, HttpStatus.OK);
    }
    
    @PostMapping("/portOut/disconnectService")
    public ResponseEntity<String> disconnectService(@RequestBody String request) {
        String response = mnpCallbackService.processDisconnectServiceRequest(request);
        return new ResponseEntity<String>(response, HttpStatus.OK);
    }
    
    @PostMapping("/portOut/confirmPortOut")
    public ResponseEntity<String> confirmPortOut(@RequestBody String request){
    	String response= mnpCallbackService.processConfirmPortOut(request);
    	return new ResponseEntity<String>(response,HttpStatus.OK);
    }

 /*   @GetMapping("/getXVNECustomer")
    public ResponseEntity<String> connectService(@RequestBody String request) {
        String response = mnpCallbackService.processConnectServiceRequest(request);
        return new ResponseEntity<String>(response, HttpStatus.OK);
    }

    @GetMapping("/getXVNECustomerByPortingRequestID")
    public ResponseEntity<String> connectService(@RequestBody String request) {
        String response = mnpCallbackService.processConnectServiceRequest(request);
        return new ResponseEntity<String>(response, HttpStatus.OK);
    }

    @GetMapping("/getXVNECustomerByPortingReferenceID")
    public ResponseEntity<String> connectService(@RequestBody String request) {
        String response = mnpCallbackService.processConnectServiceRequest(request);
        return new ResponseEntity<String>(response, HttpStatus.OK);
    }*/

}
