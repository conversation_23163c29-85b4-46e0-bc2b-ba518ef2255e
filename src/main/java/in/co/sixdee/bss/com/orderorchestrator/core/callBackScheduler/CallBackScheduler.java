package in.co.sixdee.bss.com.orderorchestrator.core.callBackScheduler;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.CallBackAuditInfoEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.WaitingProcessInfoEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.WaitingProcessInfoRepository;
import in.co.sixdee.bss.com.orderorchestrator.service.CallbackAuditService;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderCallBackServiceV2;
import jakarta.persistence.OptimisticLockException;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Component
@Log4j2
@EnableScheduling
@ConditionalOnProperty(value = GenericConstants.CALLBACK_AUDIT_SCHEDULER_ENABLE)
@RequiredArgsConstructor
public class CallBackScheduler {

    private final CallbackAuditService callbackAuditService;

    private final OrderCallBackServiceV2 orderCallBackService;

    @Autowired
    private WaitingProcessInfoRepository waitingProcessInfoRepository;

    @Value("${callback.audit.scheduler.polling-limit:10}")
    int pollingSize;
    @Value("${callback.audit.scheduler.row-limit:0}")
    int rowLimit;
    @Value("${callback.audit.scheduler.lock-duration:5}")
    int lockDuration;

    @Scheduled(fixedDelayString = "${callback.audit.scheduler.polling-interval}", initialDelayString = "${callback.audit.scheduler.startup-delay}")
    public void pollandProcesscallBackAuditData() {
        log.info("getting data from callback audit info table..");
        List<CallBackAuditInfoEntity> callBackAuditInfoEntityList = callbackAuditService.getDataForCallBackScheduler(PageRequest.of(rowLimit, pollingSize));
        if (ObjectUtils.isNotEmpty(callBackAuditInfoEntityList)) {
            log.info("getting data from callback audit info table..");
            updateAndProcessData(callBackAuditInfoEntityList);
        }
    }

    private void updateAndProcessData(List<CallBackAuditInfoEntity> callBackAuditInfoEntityList) {
        for (CallBackAuditInfoEntity auditInfoEntity : callBackAuditInfoEntityList) {
            Long orderId = auditInfoEntity.getOrderId();
            Long subOrderId = auditInfoEntity.getSubOrderId();
            String eventName = auditInfoEntity.getCallbackType();
            WaitingProcessInfoEntity waitingProcessInfoEntity = checkDataInWaitingProcessInfo(String.valueOf(orderId), String.valueOf(subOrderId), eventName);
            if (ObjectUtils.isNotEmpty(waitingProcessInfoEntity)) {
                Date targetTime = DateUtils.addMinutes(Calendar.getInstance().getTime(), lockDuration);
                int rowUpdated = 1;
                if (ObjectUtils.isNotEmpty(orderId) && ObjectUtils.isNotEmpty(subOrderId)) {
                    rowUpdated = callbackAuditService.updateDataForCallBackScheduler(GenericConstants.CALL_BACK_STATUS_PROCESSED, auditInfoEntity.getVersion(), targetTime, orderId, subOrderId,eventName);
                } else {
                    rowUpdated = callbackAuditService.updateDataForCallBackSchedulerOrderId(GenericConstants.CALL_BACK_STATUS_PROCESSED, auditInfoEntity.getVersion(), targetTime, orderId,eventName);
                }
                try {
                    if (rowUpdated == 0)
                        throw new OptimisticLockException("Row has already picked by another transaction");
                    else
                        log.info("Successfully update the record status to {},{}", GenericConstants.CALL_BACK_STATUS_PROCESSED, "Processed");
                } catch (OptimisticLockException p) {
                    log.warn("Optimistic locking occurred while updating the status to Processed for the record with sequence id {}. The call back audit info entry trying to process has already been fetched and locked by another scheduler instance.", auditInfoEntity.getSeqId());
                    continue;
                }
                log.info("Submitting the record for processing with seq id {}", auditInfoEntity.getSeqId());
                try {
                	processCallback(auditInfoEntity,waitingProcessInfoEntity);
                } catch (Exception e) {
                    //need to insert or update entry to be picked again due to some exception or thread pool exceptions??
                }
            }
        }
    }

    private WaitingProcessInfoEntity checkDataInWaitingProcessInfo(String orderId, String subOrderId, String eventName) {

        log.info("checking for data in waiting process info for orderId {} suborderId {} eventName {} ",orderId,subOrderId,eventName);
        return waitingProcessInfoRepository.existsByOrderIdSubOrderIdAndEventName(orderId,subOrderId,eventName);

    }

    @Async("callbackAuditThreadPoolTaskExecutor")
    public void processCallback(CallBackAuditInfoEntity auditInfoEntity, WaitingProcessInfoEntity waitingProcessInfoEntity) {
        orderCallBackService.processCallbackFromScheduler(auditInfoEntity,waitingProcessInfoEntity);
    }


}
