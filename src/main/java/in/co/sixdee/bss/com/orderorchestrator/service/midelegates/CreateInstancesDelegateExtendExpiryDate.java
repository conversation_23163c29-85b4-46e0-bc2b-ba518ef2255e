package in.co.sixdee.bss.com.orderorchestrator.service.midelegates;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.ProcessVariableUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.WorkflowTaskFailedException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderPayloadService;
import in.co.sixdee.bss.com.orderorchestrator.service.ProcessBatchConfig;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.ContactMedium;
import in.co.sixdee.bss.om.model.dto.order.OrderItem;
import in.co.sixdee.bss.om.model.dto.order.ServiceGroups;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.context.ProcessEngineContext;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.camunda.spin.plugin.variable.SpinValues;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;
import java.util.stream.Collectors;
@Service
@RequiredArgsConstructor
@Log4j2
public class CreateInstancesDelegateExtendExpiryDate implements JavaDelegate {

    private final ProcessDataAccessor processDataAccessor;

    private final ProcessBatchConfig processBatchConfig;

    private final ObjectMapper objectMapper;

    private final GetDataFromCache cache;

    private final ProcessVariableUtils processVariableUtils;

    private final OrderPayloadService orderPayloadService;

    @Override
    public void execute(DelegateExecution execution) throws Exception {
        boolean isNullifySubscriptionParams;
        OrderFlowContext orderFlowContext = processDataAccessor.getOrderFlowContext(execution);
        var payloadOrderFlowContext = orderPayloadService.getOrderFlowContext(orderFlowContext.getOrder().getOrderId());
        if (payloadOrderFlowContext != null) {
            isNullifySubscriptionParams = true;
                orderFlowContext.getOrder().setOrderItem(orderFlowContext.getOrder().getOrderItem());
        } else {
            isNullifySubscriptionParams = false;
        }
        List<String> subOrderIds = (List<String>) execution.getVariable("records");
        Integer createdInstancesCount = (Integer) execution.getVariable("createdInstancesCount");
        log.info("Created instances count: {} ", createdInstancesCount);
        Integer batchNumber = Math.min(subOrderIds.size() - createdInstancesCount, processBatchConfig.getBlockSize())
                + createdInstancesCount;
        String processDefinitionKey = (String) execution.getVariable("childProcess");
        IntStream.range(createdInstancesCount, batchNumber)
                .forEachOrdered(index -> createInstance(Long.parseLong(subOrderIds.get(index)), index, execution,
                        orderFlowContext, processDefinitionKey, isNullifySubscriptionParams));

        createdInstancesCount = (Integer) execution.getVariable("createdInstancesCount");

        log.info("Created instances count: {} ", createdInstancesCount);
        log.info("Batch number: {} ", batchNumber);

        execution.setVariable("allCreated", createdInstancesCount == subOrderIds.size());
    }
    private void createInstance(long subOrderId, int index, DelegateExecution execution, OrderFlowContext orderFlowContext,
                                String processDefinitionKey, boolean isNullifySubscriptionParams) {
        try {
        	
        	 var orderItem =  orderFlowContext.getOrder().getOrderItem().stream().filter(subs -> subOrderId == Long.parseLong(subs.getSubOrderId()))
                     .findFirst().orElse(null);
             var subOrderContext = createSubOrderContext(orderFlowContext, orderItem, isNullifySubscriptionParams);

            Map<String, Object> variables = new HashMap<String, Object>();
            try {
                var workflowData = SpinValues.jsonValue(objectMapper.writeValueAsString(subOrderContext)).create();
                variables.put("parentBusinessKey", execution.getBusinessKey());
                variables.put("isPartOfMultiInstance", "true");
                variables.put("Status", "0");
                variables.put("ocsCallReqd",execution.getVariable("ocsCallReqd"));
                variables.put("priority", execution.getVariable("priority"));
                variables.put("isLegacy",execution.getVariable("isLegacy"));
                if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment()) &&
                        StringUtils.isNotEmpty(orderFlowContext.getOrder().getPayment().getUpfrontPayment()) &&
                        orderFlowContext.getOrder().getPayment().getUpfrontPayment().equalsIgnoreCase("true")) {
                	variables.put("paymentCallReqd", true);
                }else {
                	variables.put("paymentCallReqd", false);
                }
                variables.put("futureOrderRequired", findFutureOrderRequired(orderFlowContext.getOrder().getOrderType(), orderFlowContext.getEntityId()));
                variables.put(WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString(), workflowData);
            } catch (Exception e) {
                log.error("Exception occurred while creating workflowData");
            }
            ProcessEngineContext.requiresNew();
            var deploymentId = execution.getProcessEngine().getRepositoryService()
                    .getProcessDefinition(execution.getProcessDefinitionId()).getDeploymentId();
            var processDefinition = execution.getProcessEngine().getRepositoryService().createProcessDefinitionQuery()
                    .deploymentId(deploymentId).processDefinitionKey(processDefinitionKey).list().get(0);
            execution.getProcessEngine().getRuntimeService().createProcessInstanceById(processDefinition.getId())
                    .setVariables(variables).businessKey(execution.getBusinessKey() + "_" + subOrderId).execute();
            execution.setVariable("createdInstancesCount", index + 1);
        } finally {
            ProcessEngineContext.clear();
        }
    }
    public boolean findFutureOrderRequired(String orderType,String entityId) {
        boolean futureOrderRequired = false;
        try {
            CacheTableDataDTO futureOrderConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_FUTURE_ORDER_CONFIG.name(), orderType + GenericConstants.OPERATOR_UNDERSCORE + entityId);
            if (futureOrderConfig != null) {
                futureOrderRequired = BooleanUtils.toBoolean(futureOrderConfig.getNgTableData().get(CacheConstants.CacheFields.FUTURE_ORDER_REQD.name()));
            }
        } catch (Exception e) {
            log.error("Exception occurred in findFutureOrderRequired", e);
        }
        return futureOrderRequired;
    }


    private OrderFlowContext createSubOrderContext(OrderFlowContext orderFlowContext,
                                                   OrderItem orderItem, boolean isNullifySubscriptionParams) {
        OrderFlowContext subOrderContext = null;
        try {
            subOrderContext = objectMapper.readValue(objectMapper.writeValueAsString(orderFlowContext), OrderFlowContext.class);
        } catch (JsonProcessingException e) {
            log.error("Exception while creating sub order context ", e);
            throw new WorkflowTaskFailedException("CreateInstancesDelegateOnboarding", "COM-005", "Unable to generate sub order context");
        }
        subOrderContext.setRequestId(orderItem.getSubOrderId());
      
        subOrderContext.getAttributes().put(GenericConstants.SUB_ORDER_ID, orderItem.getSubOrderId());
     
        var currentExecutionMap = new LinkedHashMap<String, Object>();
        try {
            currentExecutionMap.put("executionData",
                    com.bazaarvoice.jolt.JsonUtils.jsonToObject(objectMapper.writeValueAsString(orderItem)));
        } catch (JsonProcessingException e) {
            log.error("Exception occurred while creating executionData", e);
        }
        subOrderContext.getWorkflowData().put("currentExecution", currentExecutionMap);
        return subOrderContext;
    }
}
