
package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderAttributeEntity;


@Repository
public interface OrderAttributeRepository extends JpaRepository<OrderAttributeEntity, Long> {

    @Query("select o from OrderAttributeEntity o where o.orderId = :orderId")
    public List<OrderAttributeEntity> getAttributesByOrderId(@Param("orderId") Long orderId);

    @Query("select o from OrderAttributeEntity o where o.referenceId = :referenceId")
    public OrderAttributeEntity getAttributesByReferenceId(@Param("referenceId") String referenceId);

    @Query("select o from OrderAttributeEntity o where o.key = 'mnpRequestId' AND o.value=:requestId")
    public OrderAttributeEntity getAttributesByRequestId(@Param("requestId") String requestId);


    @Query("select o from OrderAttributeEntity o where o.orderId = :orderId and o.subOrderId = :subOrderId and o.key='retryRequestId'")
    public List<OrderAttributeEntity> getAttributesByOrderIdAndAttribute(@Param("orderId") Long orderId, @Param("subOrderId") Long subOrderId);

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query("UPDATE OrderAttributeEntity o SET o.value =:value where o.orderId = :orderId and o.subOrderId = :subOrderId and o.key= :name")
    public int updateAttributeByOrderId(@Param("orderId") Long orderId, @Param("subOrderId") Long subOrderId, @Param("name") String name, @Param("value") String value);

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query("UPDATE OrderAttributeEntity o SET o.value =:value, o.referenceId=:value where o.orderId = :orderId and o.subOrderId = :subOrderId and o.key= :name")
    public int updateReferenceIdByOrderId(@Param("orderId") Long orderId, @Param("subOrderId") Long subOrderId, @Param("name") String name, @Param("value") String value);

    @Transactional
    @Modifying(clearAutomatically = true)
    @Query("DELETE from OrderAttributeEntity o where o.orderId =:orderId and o.subOrderId = :subOrderId and o.key='retryRequestId'")
    public void deleteRetryRequestIdByOrderId(@Param("orderId") Long orderId, @Param("subOrderId") Long subOrderId);


}
