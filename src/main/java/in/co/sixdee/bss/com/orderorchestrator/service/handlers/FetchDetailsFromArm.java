package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import in.co.sixdee.bss.common.util.NGTableConstants;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.Service;
import in.co.sixdee.bss.om.model.dto.order.ServiceGroups;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "fetchDetailsFromArm")
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class FetchDetailsFromArm extends AbstractDelegate {

    protected String request = null;

    @Override
    protected void execute() throws Exception {
        var callThirdPartyDTO = callThirdParty(request);
        if (callThirdPartyDTO == null) {
            executionContext.setError(true);
            return;
        }
        var response = callThirdPartyDTO.getResponse();
        validateResponse(callThirdPartyDTO);
        if (executionContext.isError()) {
            log.error("{} Response validation failed", activityId);
            return;
        }
        findSimType(executionContext, execution);
        if ((OrderTypes.CHANGE_SIM).equalsIgnoreCase(executionContext.getOrder().getOrderType())) {
        	execution.setVariable("activateSaAddon", false);
            checkIfSimIs5g(response,execution);
            fetchImsiValue(executionContext, response);
        }
        modifyWorkflowData(response);
    }

    private void checkIfSimIs5g(String response, DelegateExecution execution) throws JsonProcessingException {
        String key = get5gDetailsFromConfig("KEY_FOR_FIVEG_IDENTIFIER");
        String value = get5gDetailsFromConfig("VALUE_FOR_FIVEG_IDENTIFIER");
        execution.setVariable("fivegSimFound", false);
        var armResponse = objectMapper.readTree(response);
        if (armResponse.has("data") && ObjectUtils.isNotEmpty(armResponse.get("data").get(0)) && StringUtils.isNotEmpty(key) && StringUtils.isNotEmpty(value) && (armResponse.get("data").get(0).has(key) && Arrays.asList(value.split(",")).contains(armResponse.get("data").get(0).get(key).asText()))) {
            execution.setVariable("fivegSimFound", true);
        }
    }

    private String get5gDetailsFromConfig(String key) {
        var appConfig = cache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG, key);
        if (ObjectUtils.isNotEmpty(appConfig)) {
            return appConfig.getNgTableData().get("CONFIG_VALUE");
        }
        return null;
    }

    private void fetchImsiValue(OrderFlowContext executionContext, String response) {
        if (ObjectUtils.isEmpty(executionContext.getOrder().getServiceManagement().getNewImsi())) {
            getNewImsiFromArmResponse(executionContext, response);
        }
        if (ObjectUtils.isEmpty(executionContext.getOrder().getServiceManagement().getCurrentImsi())) {
            getCurrentImsiFromBilling(executionContext);
        }
    }

    private void getCurrentImsiFromBilling(OrderFlowContext executionContext) {
        if (ObjectUtils.isNotEmpty(executionContext.getEnrichmentResults())
                && ObjectUtils.isNotEmpty(executionContext.getEnrichmentResults().get("serviceInfo"))) {
            var serviceInfo = objectMapper.convertValue(executionContext.getEnrichmentResults().get("serviceInfo"),
                    LinkedHashMap.class);
            if (!serviceInfo.get("imsiNumber").toString().isEmpty()) {
                String imsi = serviceInfo.get("imsiNumber").toString();
                executionContext.getOrder().getServiceManagement().setCurrentImsi(imsi);
            }
        }
    }

    public void getNewImsiFromArmResponse(OrderFlowContext executionContext, String response) {
        List<Map<String, Object>> assetDetails = new ArrayList<>();
        try {
            var armResponse = objectMapper.readTree(response);
            if (armResponse.has("data") && armResponse.get("data").get(0).has("assetDetails")) {
                assetDetails = objectMapper.convertValue(armResponse.get("data").get(0).get("assetDetails"),
                        new TypeReference<List<Map<String, Object>>>() {
                        });
                for (Map<String, Object> asset : assetDetails) {
                    String productAttributeName = (String) asset.get("productAttributeName");
                    if (GenericConstants.IMSI.equalsIgnoreCase(productAttributeName)) {
                        String attributeValue = (String) asset.get("attributeValue");
                        if (ObjectUtils.isNotEmpty(executionContext.getOrder())
                                && ObjectUtils.isNotEmpty(executionContext.getOrder().getServiceManagement())) {
                            executionContext.getOrder().getServiceManagement().setNewImsi(attributeValue);
                        }
                    }
                }
            }
        } catch (JsonProcessingException e) {
            log.info("Exception occurred while processing Arm response", e);
        }
    }

    public void findSimType(OrderFlowContext executionContext, DelegateExecution execution)
            throws JsonMappingException, JsonProcessingException {

        if (StringUtils.isEmpty(executionContext.getBatchId())) {
            if (StringUtils.isNotEmpty(executionContext.getOrder().getOrderType())
                    && (OrderTypes.ONBOARDING.equalsIgnoreCase(executionContext.getOrder().getOrderType())
                    || OrderTypes.ADD_SERVICE.equalsIgnoreCase(executionContext.getOrder().getOrderType())))
                execution.setVariable("isEsimCall", isSimTypeIsEsim(executionContext));
            else if (StringUtils.isNotEmpty(executionContext.getOrder().getOrderType())
                    && OrderTypes.MNP_PORT_IN.equalsIgnoreCase(executionContext.getOrder().getOrderType()))
                execution.setVariable("isEsimCall", isSimTypeIsEsimForMnpPortin(executionContext));
        } else {
            execution.setVariable("isEsimCall", isSimTypeIsEsimForBatch(callThirdPartyDTO.getResponse()));

        }

    }

    private boolean isSimTypeIsEsimForMnpPortin(OrderFlowContext executionContext) {
        if (ObjectUtils.isNotEmpty(executionContext) && ObjectUtils.isNotEmpty(executionContext.getOrder())
                && ObjectUtils.isNotEmpty(executionContext.getOrder().getProfile().getAccount())
                && ObjectUtils.isNotEmpty(executionContext.getOrder().getProfile().getAccount().getServiceGroups())) {
            var serviceGroups = executionContext.getOrder().getProfile().getAccount().getServiceGroups();
            if (ObjectUtils.isNotEmpty(serviceGroups)) {
                for (ServiceGroups serviceGroup : serviceGroups) {
                    var services = serviceGroup.getServices();
                    if (ObjectUtils.isNotEmpty(services)) {
                        for (Service service : services) {
                            var characteristics = service.getCharacteristics();
                            if (ObjectUtils.isNotEmpty(characteristics)) {
                                for (Characteristic characteristic : characteristics) {
                                    if (StringUtils.isNotEmpty(characteristic.getName())
                                            && StringUtils.isNotEmpty(characteristic.getValue())
                                            && GenericConstants.CHARACTERISC_NAME_FOR_SIM_TYPE.trim()
                                            .equalsIgnoreCase(characteristic.getName().trim())
                                            && GenericConstants.CHARACTERISC_VALUE_FOR_ESIM.trim()
                                            .equalsIgnoreCase(characteristic.getValue().trim())) {
                                        return true;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return false;

    }

    private boolean isSimTypeIsEsim(OrderFlowContext executionContext) {
        Map<String, Object> workflowData = (Map<String, Object>) executionContext.getWorkflowData();
        if (ObjectUtils.isNotEmpty(workflowData) && (ObjectUtils.isNotEmpty(workflowData.get("currentExecution")))) {
            Map<String, Object> currentExecution = (Map<String, Object>) executionContext.getWorkflowData()
                    .get("currentExecution");
            if (ObjectUtils.isNotEmpty(currentExecution)) {
                Map<String, Object> executionData = (Map<String, Object>) currentExecution.get("executionData");
                if (ObjectUtils.isNotEmpty(executionData)) {
                    List<Map<String, Object>> characteristics = (List<Map<String, Object>>) executionData
                            .get("characteristics");
                    if (ObjectUtils.isNotEmpty(characteristics)) {
                        for (Map<String, Object> characteristic : characteristics) {
                            if (GenericConstants.CHARACTERISC_NAME_FOR_SIM_TYPE.trim()
                                    .equalsIgnoreCase((String) characteristic.get("name"))
                                    && GenericConstants.CHARACTERISC_VALUE_FOR_ESIM.trim()
                                    .equalsIgnoreCase((String) characteristic.get("value"))) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    private boolean isSimTypeIsEsimForBatch(String response) throws JsonMappingException, JsonProcessingException {
        var armResponse = objectMapper.readTree(response);
        if (ObjectUtils.isNotEmpty(armResponse) && ObjectUtils.isNotEmpty(armResponse.get("data"))
                && ObjectUtils.isNotEmpty(armResponse.get("data").get(0))) {
            String productId = armResponse.get("data").get(0).get("productId").toString();
            if (ObjectUtils.isNotEmpty(productId)) {
                String eSimProductId = getEsimProductId();
                if (StringUtils.isNotEmpty(eSimProductId)
                        && Arrays.asList(eSimProductId.split(",")).contains(productId))
                    return true;
            }
        }
        return false;
    }

    private String getEsimProductId() {
        var eSimConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.toString(),
                CacheConstants.CacheFields.E_SIM_PRODUCT_ID.toString());
        var configuredProductId = eSimConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.toString());
        if (eSimConfig != null && ObjectUtils.isNotEmpty(configuredProductId)) {
            return configuredProductId;
        }
        return null;
    }
}
