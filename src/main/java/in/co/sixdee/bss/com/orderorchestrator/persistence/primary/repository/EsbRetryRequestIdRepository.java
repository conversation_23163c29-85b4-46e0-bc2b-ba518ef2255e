
package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.EsbRetryRequestIdEntity;


@Repository
public interface EsbRetryRequestIdRepository extends JpaRepository<EsbRetryRequestIdEntity, Long> {

	@Query("select o.retryRequestId from EsbRetryRequestIdEntity o where o.orderId = :orderId and o.subOrderId = :subOrderId")
	public String getAttributesByOrderIdAndAttribute(@Param("orderId") Long orderId, @Param("subOrderId") Long subOrderId);
	
	@Transactional
	@Modifying(clearAutomatically = true)
	@Query("UPDATE EsbRetryRequestIdEntity o SET o.retryRequestId =:retryRequestId where o.orderId = :orderId and o.subOrderId = :subOrderId")
	public int updateRetryRequestIdByOrderId(@Param("orderId") Long orderId, @Param("subOrderId") Long subOrderId, @Param("retryRequestId") String retryRequestId);
	
	@Transactional
	@Modifying(clearAutomatically = true)
	@Query("DELETE from EsbRetryRequestIdEntity o where o.orderId =:orderId and o.subOrderId = :subOrderId")
	public void deleteRetryRequestIdByOrderId(@Param("orderId") Long orderId, @Param("subOrderId") Long subOrderId);
	
	
}
