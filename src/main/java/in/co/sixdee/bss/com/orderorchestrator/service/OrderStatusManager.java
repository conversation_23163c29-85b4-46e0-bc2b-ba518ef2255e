package in.co.sixdee.bss.com.orderorchestrator.service;

import camundajar.impl.scala.collection.mutable.StringBuilder;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.EdrConfig;
import in.co.sixdee.bss.com.orderorchestrator.config.notification.NotificationUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.*;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants.WorkFlowProcessVariables;
import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.NativeOrderAttributeRepository;
import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.SubOrderService;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.common.util.NGTableColumnConstants;
import in.co.sixdee.bss.common.util.SequenceGenerator;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.BasicInfo;
import in.co.sixdee.bss.om.model.dto.order.OrderStateType;
import in.co.sixdee.bss.om.model.dto.order.event.StageStatusChangedEvent;
import lombok.RequiredArgsConstructor;
import lombok.Synchronized;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Log4j2
@RequiredArgsConstructor
public class OrderStatusManager {

    private final OrderMasterService orderMasterService;

    private final OrderStageService orderStageService;

    private final GetDataFromCache cache;

    private final EdrConfig edrConfig;

    private final BatchCallBackService batchService;

    private final NotificationUtils notificationUtils;


    @Value("${notificationEnableTags}")
    private String notificationEnableTags;

    @Value("${updateProfileNotificationEnabledTags}")
    private String profileUpdateNotificationEnableTags;

    private final ObjectMapper objectMapper;

    private final SubOrderService subOrderService;

    private final DunningCallbackService dunningService;

    protected final NativeOrderAttributeRepository nativeOrderAttributeRepository;

    public void processStatusUpdates(OrderFlowContext orderFlowContext, String activityId, String eventType,
                                     boolean isExecuted, String executionStatus) {
        if (orderFlowContext == null) {
            return;
        }

        String subOrderId = orderFlowContext.getAttributes().get(GenericConstants.SUB_ORDER_ID);
        var linkedStage = cache.getCacheDetailsFromDBMap("STAGE_CONFIG_BY_ACTIVITY_ID_AND_ORDER_TYPE",
                orderFlowContext.getOrder().getOrderType() + GenericConstants.CONST_UNDERSCORE_ + activityId);

        if (linkedStage == null) {
            log.info("No linked stages found for the activity: {}", activityId);
            return;
        }

        if (StringUtils.isEmpty(executionStatus)) {
            executionStatus = findStageStatus(eventType, isExecuted);
        }

        if (StringUtils.isEmpty(executionStatus)) {
            log.info("Unable to find the execution status for stage {}", linkedStage);
            return;
        }

        var event = createStageStatusChangedEvent(orderFlowContext, subOrderId, linkedStage, executionStatus, eventType, activityId);

        if (WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue().equals(executionStatus)) {
            orderFlowContext.getAttributes().put("EdrIntermediateFailedStageName", event.getStageName());
        }

        handleStageStatusChangedEvent(orderFlowContext, event);
    }

    private StageStatusChangedEvent createStageStatusChangedEvent(OrderFlowContext orderFlowContext, String subOrderId,
                                                                  CacheTableDataDTO linkedStage, String executionStatus,
                                                                  String eventType, String activityId) {
        var event = new StageStatusChangedEvent();
        event.setStageStatus(executionStatus);

        log.info("Starting order status checking for activity {}, new status :: {}", activityId, executionStatus);

        event.setIsCommonTask(BooleanUtils.toBoolean(linkedStage.getNgTableData().get(NGTableColumnConstants.COLUMN_IS_COMMON)));
        event.setSubOrderId(subOrderId);
        event.setStageName(linkedStage.getNgTableData().get(NGTableColumnConstants.COLUMN_STAGE_NAME));
        event.setStageCode(linkedStage.getNgTableData().get(NGTableColumnConstants.COLUMN_STAGE_ID));
        event.setStageId(linkedStage.getNgTableData().get(NGTableColumnConstants.COLUMN_STAGE_ID));
        event.setStageDescription(linkedStage.getNgTableData().get(NGTableColumnConstants.COLUMN_STAGE_DESCRIPTION));
        event.setStageExecutionOrder(linkedStage.getNgTableData().get(NGTableColumnConstants.COLUMN_EXECUTION_ORDER));
        event.setOrderId(orderFlowContext.getOrder().getOrderId());
        event.setEntityId(orderFlowContext.getEntityId());
        event.setUserId(orderFlowContext.getUserId());
        event.setUsername(orderFlowContext.getUsername());

        if (isFailedOrPortInNotification(executionStatus, eventType)) {
            event.setErrorCode(orderFlowContext.getErrorDetail().getCode());
            event.setErrorDesc(orderFlowContext.getErrorDetail().getMessage());
        } else if (WorkFlowConstants.StageStatusConstants.STAGE_STATUS_INPROGRESS.getValue().equals(executionStatus) ||
                WorkFlowConstants.StageStatusConstants.STAGE_STATUS_HELD.getValue().equals(executionStatus)) {
            setStateReasonForCallbacks(event, activityId, orderFlowContext);
        }

        return event;
    }

    private boolean isFailedOrPortInNotification(String executionStatus, String eventType) {
        return WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue().equals(executionStatus) ||
                WorkFlowConstants.OrderCallBackTypes.PORT_IN_NOTIFICATION.desc.equals(eventType);
    }

    private void setStateReasonForCallbacks(StageStatusChangedEvent stageStatusUpdateEvent, String workflowActivity, OrderFlowContext orderFlowContext) {

        String reasonDesc = null;
        for (Map.Entry<String, String> entry : WorkFlowActivityConstants.activityReasonDescriptionMap.entrySet()) {
            if (StringUtils.startsWithIgnoreCase(workflowActivity, entry.getKey())) {
                reasonDesc = entry.getValue();
                break;
            }
        }
        if ("MNPChangeProductTimer".equalsIgnoreCase(workflowActivity)) {
            reasonDesc = WorkFlowActivityConstants.formatMessage(reasonDesc, orderFlowContext.getAttributes().get(AppConstants.ProcessVariables.MNP_CHANGE_PRODUCT_DATE.value()));
        } else if ("MNPConnectServiceIntTimer".equalsIgnoreCase(workflowActivity)) {
            reasonDesc = WorkFlowActivityConstants.formatMessage(reasonDesc, orderFlowContext.getAttributes().get(AppConstants.ProcessVariables.MNP_CONNECT_SERVICE_INT_DATE.value()));
        } else if ("MnpDeProvisioningTimer".equalsIgnoreCase(workflowActivity)) {
            reasonDesc = WorkFlowActivityConstants.formatMessage(reasonDesc, orderFlowContext.getAttributes().get(AppConstants.ProcessVariables.MNP_DEPROVISIONING_DATE.value()));
        } else if ("MnpProvisioningTimer".equalsIgnoreCase(workflowActivity)) {
            reasonDesc = WorkFlowActivityConstants.formatMessage(reasonDesc, orderFlowContext.getAttributes().get(AppConstants.ProcessVariables.MNP_PROVISIONING_DATE.value()));
        } else if ("FutureOrderTimer".equalsIgnoreCase(workflowActivity)) {
            reasonDesc = WorkFlowActivityConstants.formatMessage(reasonDesc, orderFlowContext.getAttributes().get("orderExecutionDate"));
        }
        stageStatusUpdateEvent.setErrorDesc(reasonDesc);
    }

    private String findStageStatus(String eventType, boolean isExecuted) {
        if (isExecuted) {
            return WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue();
        }
        return switch (eventType.toUpperCase()) {
            case "WAIT_EVENT" -> WorkFlowConstants.StageStatusConstants.STAGE_STATUS_INPROGRESS.getValue();
            case "TIMER_EVENT" -> WorkFlowConstants.StageStatusConstants.STAGE_STATUS_HELD.getValue();
            default -> null;
        };
    }

    public void handleStageStatusChangedEvent(OrderFlowContext executionContext,
                                              StageStatusChangedEvent stageStatusChangedEvent) {
        long orderId = Long.parseLong(stageStatusChangedEvent.getOrderId());
        List<Long> subOrderIds = subOrderService.getSubordersByOrderId(orderId);
        boolean isStageInserted = isStageExists(stageStatusChangedEvent);
        handleStageStatusUpdates(executionContext, stageStatusChangedEvent, isStageInserted, subOrderIds);
    }


    private void handleStageStatusUpdates(OrderFlowContext executionContext, StageStatusChangedEvent stageStatusChangedEvent,
                                          boolean isStageInserted, List<Long> subOrderIds) {
        boolean runInNewtTxn = isFailedOrderInProgress(stageStatusChangedEvent.getStageStatus());

        if (isStageInserted) {
            updateStage(stageStatusChangedEvent, executionContext, runInNewtTxn);
        } else {
            insertNewStage(stageStatusChangedEvent, subOrderIds, executionContext, runInNewtTxn);
        }

        if (!isStageCompleted(stageStatusChangedEvent)) {
            var statusReason = updateSubOrderStatus(executionContext, stageStatusChangedEvent);
            updateMainOrderStatus(executionContext, Long.valueOf(stageStatusChangedEvent.getOrderId()), statusReason);
        }
    }

    private boolean isStageCompleted(StageStatusChangedEvent stageStatusChangedEvent) {
        return WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue()
                .equalsIgnoreCase(stageStatusChangedEvent.getStageStatus());
    }

    private boolean isFailedOrderInProgress(String status) {
        return WorkFlowActivityConstants.failedOrInProgressStatuses.contains(status);
    }

    public void createEdr(OrderFlowContext orderFlowContext, String edrType, String orderStatus, String edrLevel) {
        String cacheKey = orderFlowContext.getOrder().getOrderType() + "_" + edrType;
        var edrDataConfig = cache.getCacheDetailsFromDBMap("EDR_CONFIG", cacheKey);

        if (edrDataConfig == null || !"true".equals(edrDataConfig.getNgTableData().get("EDR_GENERATION_REQD"))) {
            return;
        }

        var edrContext = SerializationUtils.clone(orderFlowContext);
        var attributes = edrContext.getAttributes();

        if ("ORDER".equals(edrLevel)) {
            if (WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue().equals(orderStatus)) {
                attributes.put("edrType", WorkFlowProcessVariables.EDR_INTERMEDIATE.toString());
            } else {
                attributes.remove("EdrIntermediateFailedStageName");
                attributes.put("edrType", WorkFlowProcessVariables.EDR_FINAL.toString());
                attributes.put("orderCompletionDate", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
        } else if (WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue().equals(orderStatus)) {
            // level is sub order and status is completed
            checkSubOrderCompletionTimeTaken(edrContext);
            attributes.remove("EdrIntermediateFailedStageName");
        }

        edrContext.setEdrType(edrType);
        attributes.put("orderStatus", orderStatus);
        edrConfig.addToEdrPool(edrContext);
    }


    protected void checkSubOrderCompletionTimeTaken(OrderFlowContext orderFlowContext) {
        var orderDate = orderFlowContext.getAttributes().get(GenericConstants.ORDER_DATE);
        var oldFormat = LocalDateTime.parse(orderDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        var duration = Duration.between(oldFormat, LocalDateTime.now()).getSeconds();
        orderFlowContext.getAttributes().put(GenericConstants.SUB_ORDER_COMPLETION_DATE, String.valueOf(duration));
    }


    protected void insertNewStage(StageStatusChangedEvent event, List<Long> subOrderIds, OrderFlowContext executionContext, boolean runInNexTxn) {
        List<OrderStageEntity> stages = new ArrayList<>();

        String username = StringUtils.defaultIfEmpty(event.getUsername(), "Admin");
        String createdBy = StringUtils.defaultIfEmpty(event.getUserId(), "Admin");
        String entityId = event.getEntityId();
        Long orderId = Long.parseLong(event.getOrderId());
        String stateReason = getStateReason(event,executionContext.getEntityId());

        if (event.getIsCommonTask()) {
            for (Long subOrderId : subOrderIds) {
                stages.add(createStageEntity(event, subOrderId, orderId, stateReason, username, createdBy, entityId));
            }
        } else {
            stages.add(createStageEntity(event, Long.parseLong(event.getSubOrderId()), orderId, stateReason, username, createdBy, entityId));
        }

        if (!stages.isEmpty()) {
            orderStageService.saveStages(stages);
        }

        var stageNotificationConfigs = cache.getCacheDetailsFromDBMapAryList("COM_STAGE_NOTIFICATION_CONFIG",
                executionContext.getOrder().getOrderType() + GenericConstants.CONST_UNDERSCORE_ + event.getStageCode());
        if (stageNotificationConfigs != null) {
            initStageNotifications(stageNotificationConfigs, event, executionContext);
        }
    }

    private OrderStageEntity createStageEntity(StageStatusChangedEvent event, Long subOrderId, Long orderId,
                                               String stateReason, String username, String createdBy, String entityId) {
        OrderStageEntity stage = new OrderStageEntity();
        stage.setName(event.getStageName());
        stage.setDescription(event.getStageDescription());
        stage.setState(event.getStageStatus());
        stage.setStageCode(event.getStageCode());
        stage.setStateReason(stateReason);
        stage.setSubOrderId(subOrderId);
        stage.setOrderId(orderId);
        stage.setUsername(username);
        stage.setCreatedBy(createdBy);
        stage.setEntityId(entityId);
        stage.setExecutionOrder(Integer.parseInt(event.getStageExecutionOrder()));
        stage.setId(SequenceGenerator.getSequencerInstance().nextId());
        return stage;
    }


    public void updateStage(StageStatusChangedEvent event, OrderFlowContext executionContext, boolean runInNexTxn) {
        String stateReason = getStateReason(event,executionContext.getEntityId());
        Date lastModifiedDate = createTimezoneInstant(new Date());

        var stageNotificationConfigs = cache.getCacheDetailsFromDBMapAryList(
                "COM_STAGE_NOTIFICATION_CONFIG",
                executionContext.getOrder().getOrderType() + GenericConstants.CONST_UNDERSCORE_ + event.getStageCode()
        );

        if (stageNotificationConfigs != null) {
            initStageNotifications(stageNotificationConfigs, event, executionContext);
        }

        if (StringUtils.equalsIgnoreCase(event.getStageCode(), "APPROVAL")) {
            orderStageService.updateStatusAndReasonByOrderIdForApproval(
                    event.getStageStatus(), stateReason, Long.parseLong(event.getOrderId()), event.getStageCode(), lastModifiedDate
            );
        } else {
            String lastModifiedBy = getLastModifiedBy(executionContext);
            updateStatusAndReason(event, stateReason, lastModifiedBy, runInNexTxn);
        }

    }

    private String getLastModifiedBy(OrderFlowContext executionContext) {
        return ObjectUtils.isNotEmpty(executionContext.getAttributes().get("skipUser"))
                ? executionContext.getAttributes().get("skipUser")
                : null;
    }

    private void updateStatusAndReason(StageStatusChangedEvent event, String stateReason, String lastModifiedBy, boolean runInNewTxn) {
        log.info("in updateStatusAndReason :: runInNewTxn {} event.getIsCommonTask {}", runInNewTxn,event.getIsCommonTask());
        if (event.getIsCommonTask()) {
            if (runInNewTxn) {
                orderStageService.updateStatusAndReasonByOrderIdInNewTxn(
                        event.getStageStatus(), stateReason, Long.parseLong(event.getOrderId()), event.getStageCode(), lastModifiedBy
                );
            } else {
                    orderStageService.updateStatusAndReasonByOrderId(
                            event.getStageStatus(), stateReason, Long.parseLong(event.getOrderId()), event.getStageCode(), lastModifiedBy
                    );
            }
        } else {
            if (runInNewTxn) {
                orderStageService.updateStatusAndReasonBySubOrderIdInNewTxn(
                        event.getStageStatus(), stateReason, Long.parseLong(event.getSubOrderId()), event.getStageCode(), lastModifiedBy);
            } else {
                if (Arrays.asList(MNPConstants.SUBMIT_PORT_IN_STAGES.split(",")).contains(event.getStageCode())) {
                    log.info("submit port in stage found... need to update last modified date");
                    orderStageService.updateStatusAndReasonAndModifiedDateByOrderId(
                            event.getStageStatus(), stateReason, Long.parseLong(event.getOrderId()), event.getStageCode(), lastModifiedBy, Date.from(Instant.now())
                    );
                } else {
                    orderStageService.updateStatusAndReasonBySubOrderId(
                            event.getStageStatus(), stateReason, Long.parseLong(event.getSubOrderId()), event.getStageCode(), lastModifiedBy);
                }
            }
        }
    }

	private String getStateReason(StageStatusChangedEvent event, String entityId) {
		if(getStatusCodeBasedOnEntityId(event, entityId) && StringUtils.isNotEmpty(event.getErrorDesc()))
			return event.getErrorDesc();
		if (StringUtils.isNotEmpty(event.getErrorCode()) && StringUtils.isNotEmpty(event.getErrorDesc())) {
			return event.getErrorCode() + ":" + event.getErrorDesc();
		} else if (StringUtils.isNotEmpty(event.getErrorDesc())) {
			return event.getErrorDesc();
		} else {
			return null;
		}

	}

	private boolean getStatusCodeBasedOnEntityId(StageStatusChangedEvent event, String entityId) {
		List<String> mvnos = new ArrayList<>();
		try {
			var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
					"ERROR_CODE_IN_ORDER_STATE_NOT_REQUIRED_MVNOS");
			if (appConfig != null && ObjectUtils
					.isNotEmpty(appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()))) {
				mvnos = Arrays.asList(
						appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name()).split(","));
				if (ObjectUtils.isNotEmpty(mvnos) && mvnos.contains(entityId)) {
					return true;
				}
			}
		} catch (Exception e) {
			log.error("Exception Occured in getStatusCodeBasedOnEntityId", e);
		}
		return false;
	}

	private void initStageNotifications(ArrayList<CacheTableDataDTO> stageNotificationConfigs, StageStatusChangedEvent event, OrderFlowContext executionContext) {
        String stageStatus = event.getStageStatus();
        boolean isCompleted = stageStatus.equalsIgnoreCase(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue());
        boolean isFailed = stageStatus.equalsIgnoreCase(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue());

        for (CacheTableDataDTO stageNotificationConfig : stageNotificationConfigs) {
            Map<String, String> ngTableData = stageNotificationConfig.getNgTableData();
            if (isCompleted) {
                handleNotification(executionContext, ngTableData, "COMPLETE_NOTIFICATION_REQD", "ON_COMPLETE_MESSAGE_TYPE");
            } else if (isFailed) {
                handleFailedNotification(executionContext, event, ngTableData);
            }
        }
    }

    private void handleNotification(OrderFlowContext executionContext, Map<String, String> ngTableData, String notificationRequiredKey, String messageTypeKey) {
        if (BooleanUtils.toBoolean(ngTableData.get(notificationRequiredKey))) {
            String messageType = ngTableData.get(messageTypeKey);
            notificationUtils.sendNotification(executionContext, messageType, "ORDER");
        }
    }

    private void handleFailedNotification(OrderFlowContext executionContext, StageStatusChangedEvent event, Map<String, String> ngTableData) {
        if (BooleanUtils.toBoolean(ngTableData.get("FAILED_NOTIFICATION_REQD"))) {
            String messageType = ngTableData.get("ON_FAILED_MESSAGE_TYPE");
            String configuredFailedErrorCodes = ngTableData.get("ON_FAILED_ERROR_CODES");

            if (StringUtils.isNotEmpty(configuredFailedErrorCodes)) {
                var failedErrorCodeList = Arrays.asList(configuredFailedErrorCodes.split(","));
                if (failedErrorCodeList.contains(event.getErrorCode())) {
                    notificationUtils.sendNotification(executionContext, messageType, "ORDER");
                }
            } else {
                log.info("Error codes for failed stage notifications are not configured");
                notificationUtils.sendNotification(executionContext, messageType, "ORDER");
            }
        }
    }


    public String updateSubOrderStatus(OrderFlowContext executionContext, StageStatusChangedEvent event) {
        var status = WorkFlowConstants.StageStatusConstants.STAGE_STATUS_SKIPPED.getValue().equalsIgnoreCase(
                event.getStageStatus()) ? WorkFlowConstants.StageStatusConstants.STAGE_STATUS_INPROGRESS.getValue()
                : event.getStageStatus();
        String statusReason = null;
        String failedStageCode = null;
        Date lastModifiedDate = createTimezoneInstant(new Date());
        if (WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue().equalsIgnoreCase(status)
                || WorkFlowConstants.StageStatusConstants.STAGE_STATUS_REJECTED.getValue().equalsIgnoreCase(status)
                || WorkFlowConstants.StageStatusConstants.STAGE_STATUS_INPROGRESS.getValue().equalsIgnoreCase(status)
                || WorkFlowConstants.StageStatusConstants.STAGE_STATUS_HELD.getValue().equalsIgnoreCase(status)) {
            var reasonBuilder = new StringBuilder();
           
            if(getStatusCodeBasedOnEntityId(event, executionContext.getEntityId()) && StringUtils.isNotEmpty(event.getErrorDesc())) {
            	 reasonBuilder.append(event.getErrorDesc());
            }
            else if (event.getErrorCode() != null && event.getErrorDesc() != null) {
                reasonBuilder.append(event.getErrorCode()).append(" :: ").append(event.getErrorDesc());
            } else if (event.getErrorCode() != null) {
                reasonBuilder.append(event.getErrorCode());
            } else {
                reasonBuilder.append(event.getErrorDesc());
            }
            
            statusReason = reasonBuilder.toString();
            
        }
        if (WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue().equalsIgnoreCase(status)) {
            failedStageCode = event.getStageCode();
        }

        if (event.getIsCommonTask()) {
            subOrderService.updateSubOrderStatusByOrderId(status, statusReason, Long.parseLong(event.getOrderId()),
                    lastModifiedDate, failedStageCode);
        } else {
            subOrderService.updateSubOrderStatus(status, statusReason, Long.parseLong(event.getSubOrderId()),
                    lastModifiedDate, failedStageCode);
        }
        if (WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue().equalsIgnoreCase(status)
                || WorkFlowConstants.StageStatusConstants.STAGE_STATUS_REJECTED.getValue().equalsIgnoreCase(status)) {
            createEdr(executionContext, WorkFlowProcessVariables.EDR_FINAL.toString(), event.getStageStatus(),
                    "ORDER");
        }
        return statusReason;
    }

    public void updateSubOrderCompletion(OrderFlowContext executionContext, String subOrderId) {
        Date lastModifiedDate = createTimezoneInstant(new Date());
        subOrderService.updateSubOrderStatus(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue(),
                null, Long.valueOf(subOrderId), lastModifiedDate, null);
        createEdr(executionContext, WorkFlowProcessVariables.EDR_FINAL.toString(),
                WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue(), "ORDER");
        //notificationUtils.sendNotification(executionContext, "SUB_ORDER_COMPLETION", "SUB_ORDER");
        updateMainOrderStatus(executionContext, Long.parseLong(executionContext.getOrder().getOrderId()), "");
    }

    @Synchronized
    protected void updateMainOrderStatus(OrderFlowContext executionContext, Long orderId, String statusReason) {
        if (orderMasterService.checkIfOrderIsCompleted(orderId))
            return;

        var suborderStatusList = subOrderService.getSuborderStatusByOrderId(orderId);
        if (suborderStatusList.isEmpty())
            return;

        boolean isSingleSubOrder = suborderStatusList.size() == 1;
        String allMatchStatus = checkAllMatchStatus(suborderStatusList);

        if (allMatchStatus != null) {
            handleAllMatchStatus(allMatchStatus, executionContext, isSingleSubOrder, statusReason);
        } else if (suborderStatusList.stream().anyMatch(status -> StringUtils.equalsIgnoreCase(status,
                WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue()))) {
            orderMasterService.orderStatus(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_PARTIAL_SUCCESS.getValue(),
                    null, orderId);
        }
    }

    private String checkAllMatchStatus(List<String> suborderStatusList) {
        if (suborderStatusList.stream().allMatch(status -> status
                .equalsIgnoreCase(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue()))) {
            return WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue();
        }
        if (suborderStatusList.stream().allMatch(status -> status
                .equalsIgnoreCase(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue()))) {
            return WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue();
        }
        if (suborderStatusList.stream().allMatch(status -> status
                .equalsIgnoreCase(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_REJECTED.getValue()))) {
            return WorkFlowConstants.StageStatusConstants.STAGE_STATUS_REJECTED.getValue();
        }
        if (suborderStatusList.stream().allMatch(status -> status
                .equalsIgnoreCase(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_INPROGRESS.getValue()))) {
            return WorkFlowConstants.StageStatusConstants.STAGE_STATUS_INPROGRESS.getValue();
        }
        if (suborderStatusList.stream().allMatch(status -> status
                .equalsIgnoreCase(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_HELD.getValue()))) {
            return WorkFlowConstants.StageStatusConstants.STAGE_STATUS_HELD.getValue();
        }
        return null;
    }

    private void handleAllMatchStatus(String status, OrderFlowContext executionContext, boolean isSingleSubOrder, String statusReason) {
        if (!isSingleSubOrder && (status.equals(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue())
                || status.equals(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_REJECTED.getValue())
                || status.equals(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_INPROGRESS.getValue()))) {
            executionContext.getErrorDetail().setCode(null);
            executionContext.getErrorDetail().setMessage(null);
            executionContext.getAttributes().remove("EdrIntermediateFailedStageName");
            statusReason = null;
        }
        updateOrderStatus(executionContext, status, statusReason);
    }

    public void updateOrderStatus(OrderFlowContext orderFlowContext, String status, String statusReason) {
        //Date lastModifiedDate =  createTimezoneInstant(new Date()) ;
        var orderId = Long.parseLong(orderFlowContext.getOrder().getOrderId());
        orderMasterService.orderStatus(status, statusReason, orderId);
        if (orderFlowContext.getOrder().getOrderType().equalsIgnoreCase(OrderTypes.TRANSFER_OF_SERVICE)) {
            if (StringUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getDestinationProfileId())) {
                updateProfileId(String.valueOf(orderId), orderFlowContext.getOrder().getServiceManagement().getDestinationProfileId());
            }
        }
        if (orderFlowContext.getOrder().getOrderType().equalsIgnoreCase(OrderTypes.PORT_IN)) {
            if (StringUtils.isNotEmpty(orderFlowContext.getOrder().getServiceManagement().getNewMsisdn())) {
                updateMsisdn(String.valueOf(orderId), orderFlowContext.getOrder().getServiceManagement().getNewMsisdn());
            }
        }


        if ((WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue().equals(status)
                || WorkFlowConstants.StageStatusConstants.STAGE_STATUS_REJECTED.getValue().equals(status)
                || WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue().equals(status))) {
            //createEdr(orderFlowContext, "FINAL", status, "ORDER");
            if (WorkFlowConstants.StageStatusConstants.STAGE_STATUS_COMPLETED.getValue().equals(status)) {
                orderFlowContext.getOrder().setState(OrderStateType.COMPLETED);
                notificationUtils.sendNotification(orderFlowContext, "ORDER_COMPLETION", "ORDER");
                if (OrderTypes.UPDATE_ACCOUNT.equalsIgnoreCase(orderFlowContext.getOrder().getOrderType())) {
                    var basicInfoList = orderFlowContext.getOrder().getAccountManagement().getBasicInfo();
                    for (BasicInfo basicInfo : basicInfoList) {
                        if (notificationEnableTags.contains(basicInfo.getId())) {
                            notificationUtils.sendNotification(orderFlowContext,
                                    AppConstants.NotificationMessageTypes.NOTIFICATION_FOR_UPDATE_TAGS.value(),
                                    "ORDER");
                        }
                    }
                } else if (StringUtils.equalsAny(orderFlowContext.getOrder().getOrderType(), OrderTypes.UPDATE_PROFILE,
                        OrderTypes.UPDATE_PROFILE_ADDRESS)) {
                    profileUpdateNotification(orderFlowContext, orderFlowContext.getEnrichmentType());
                }
            } else {
                orderFlowContext.getOrder().setState(OrderStateType.FAILED);
            }
            if (StringUtils.isNotEmpty(orderFlowContext.getBatchId())) {
                batchService.sendCallBackRequest(orderFlowContext);
            }
            if (GenericConstants.DUNNING_POLLER_CHANNEL_VALUE.equalsIgnoreCase(orderFlowContext.getChannel())) {
                dunningService.sendCallBackRequest(orderFlowContext);
            }
        }
    }

    private void profileUpdateNotification(OrderFlowContext orderFlowContext, String enrichmentType) {

        boolean updateNotifReqd = false;

        // default phoneNumber value considered from enrichment
        if (ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults())
                && ObjectUtils.isNotEmpty(orderFlowContext.getEnrichmentResults().get("profileInfo"))) {
            var profileInfo = objectMapper.convertValue(orderFlowContext.getEnrichmentResults().get("profileInfo"),
                    JsonNode.class);
            if (ObjectUtils.isNotEmpty(profileInfo) && profileInfo.has("phoneNumber")) {
                orderFlowContext.getAttributes().put("phoneNumber", profileInfo.get("phoneNumber").asText());
            }
        }

        if (OrderTypes.UPDATE_PROFILE.equals(enrichmentType)) {
            // get phoneNumber coming in UpdateProfile request if present
            var basicInfoList = orderFlowContext.getOrder().getProfileManagement().getBasicInfo();
            basicInfoList.stream()
                    .filter(basicInfo -> "phoneNumber".equals(basicInfo.getId())
                            && StringUtils.isNotEmpty(basicInfo.getValue()))
                    .findFirst().ifPresent(phoneNumberBasicInfo -> {

                        orderFlowContext.getAttributes().put("phoneNumber", phoneNumberBasicInfo.getValue());
                    });
            var updateProfNotifEnableTagsPresent = basicInfoList.stream()
                    .anyMatch(basicInfo -> profileUpdateNotificationEnableTags.contains(basicInfo.getId())
                            && StringUtils.isNotEmpty(basicInfo.getValue()));

            if (updateProfNotifEnableTagsPresent
                    && StringUtils.isNotEmpty(orderFlowContext.getAttributes().get("phoneNumber")))
                updateNotifReqd = true;
            else
                log.info(
                        "unable to initiate update notification, either phoneNumber is empty OR incoming tags are not configured in 'updateProfileNotificationEnabledTags'");

        } else if (StringUtils.isNotEmpty(orderFlowContext.getAttributes().get("phoneNumber"))) {
            // UpdateProfileAddress case
            updateNotifReqd = true;
        }

        if (updateNotifReqd)
            notificationUtils.sendNotification(orderFlowContext,
                    AppConstants.NotificationMessageTypes.NOTIFICATION_FOR_UPDATE_TAGS.value(), "ORDER");

    }

    private boolean isStageExists(StageStatusChangedEvent event) {

        if (event.getIsCommonTask()) {
            return orderStageService.existsByOrderIdAndName(Long.parseLong(event.getOrderId()),
                    event.getStageName());
        } else {
            return orderStageService.existsBySubOrderIdAndName(Long.parseLong(event.getSubOrderId()),
                    event.getStageName());
        }
    }

    public void updateSuborderExecutionStart(String subOrderId) {
        subOrderService.updateSubOrderToInProgress(Long.parseLong(subOrderId));
    }

    public void updateOrderExecutionStart(String orderId) {
        orderMasterService.updateOrderToInProgress(Long.parseLong(orderId));
    }

    public int updateAccountId(String id, String accountId) {
        return orderMasterService.updateAccountId(Long.valueOf(id), accountId);
    }


    public int updateProfileId(String id, String profileId) {
        return orderMasterService.updateProfileId(Long.valueOf(id), profileId);
    }


    public int updateMsisdn(String id, String serviceId) {
        return orderMasterService.updateMsisdn(Long.valueOf(id), serviceId);
    }


    public void updateRollBackStatusForSOM(String subOrderId, String skipRetryStage) {
        var orderStage = orderStageService.findStageBySubOrderIdAndName(Long.valueOf(subOrderId), skipRetryStage);
        if (Objects.isNull(orderStage))
            return;
        orderStageService.updateRollbackStatus("", "", "", orderStage.getId());
    }

    @Transactional
    public void UpdateCallBackOrderStatus(OrderFlowContext orderFlowContext, OrderCallBack callback,
                                          OrderStageEntity stageInfo) {
        String configValue = null;
        try {
            var appConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(),
                    "MNP_FAILURE_REASON_CODE_" + callback.getFailureReason());
            configValue = appConfig.getNgTableData().get(CacheConstants.CacheFields.CONFIG_VALUE.name());
        } catch (Exception e) {
            configValue = "Failed";
        }


        var status = callback.getStatus();
        if (callback.getStatus()
                .equalsIgnoreCase(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_REJECTED.getValue())) {
            status = WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue();
        }
        if (WorkFlowConstants.OrderCallBackTypes.APPROVAL.desc.equalsIgnoreCase(callback.getCallbackType())) {
            if (callback.getStatus()
                    .equalsIgnoreCase(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_REJECTED.getValue())) {
                status = WorkFlowConstants.StageStatusConstants.STAGE_STATUS_REJECTED.getValue();
            }
            if (StringUtils.isNotEmpty(callback.getComments()))
                orderStageService.updateRollbackStatusByOrderId(status, String.valueOf(callback.isRollbacked()),
                        callback.getComments(), stageInfo.getOrderId(), stageInfo.getStageCode());
            else
                orderStageService.updateRollbackStatusByOrderId(status, String.valueOf(callback.isRollbacked()), "",
                        stageInfo.getOrderId(), stageInfo.getStageCode());

        } else {
            if (StringUtils.isNotEmpty(callback.getFailureReason()))
                orderStageService.updateRollbackStatus(status, String.valueOf(callback.isRollbacked()),
                        callback.getFailureReason() + ":" + configValue, stageInfo.getId());
            else
                orderStageService.updateRollbackStatus(status, String.valueOf(callback.isRollbacked()), "",
                        stageInfo.getId());
        }
        StageStatusChangedEvent stageStatusChangedEvent = new StageStatusChangedEvent();
        stageStatusChangedEvent.setStageName(stageInfo.getName());
        stageStatusChangedEvent.setSubOrderId(String.valueOf(stageInfo.getSubOrderId()));
        stageStatusChangedEvent.setOrderId(String.valueOf(stageInfo.getOrderId()));
        stageStatusChangedEvent.setStageStatus(status);
        stageStatusChangedEvent.setStageCode(stageInfo.getStageCode());
        if (StringUtils.isNotEmpty(callback.getFailureReason()))
            stageStatusChangedEvent.setErrorCode(callback.getFailureReason());
        if (StringUtils.isNotEmpty(configValue)) {
            stageStatusChangedEvent.setErrorDesc(configValue);
            if (!StringUtils.equalsIgnoreCase(configValue, "Failed")) {
                stageStatusChangedEvent.setStageStatus(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_REJECTED.getValue());
            }
        }
        if (StringUtils.equalsAnyIgnoreCase(callback.getCallbackType(),
                WorkFlowConstants.OrderCallBackTypes.APPROVAL.desc,
                WorkFlowConstants.OrderCallBackTypes.PAYMENT.desc)) {
            stageStatusChangedEvent.setIsCommonTask(true);
            if (StringUtils.isNotEmpty(callback.getComments()) && StringUtils.isEmpty(callback.getFailureReason())) {
                stageStatusChangedEvent.setErrorDesc(callback.getComments());
                callback.setFailureReason(callback.getComments());
            }
        } else
            stageStatusChangedEvent.setIsCommonTask(false);
        callback.setFailureReason(callback.getFailureReason() + ":" + configValue);
        updateSubOrderStatus(orderFlowContext, stageStatusChangedEvent);
        updateMainOrderStatus(orderFlowContext, stageInfo.getOrderId(), callback.getFailureReason());
    }


    public int updateOrderParams(String id, String profileId, String accountId) {
        return orderMasterService.updateOrderParams(Long.valueOf(id), profileId, accountId);
    }


    public void updateStageStatusToSkipped(String subOrderId, String skipRetryStage) {
        var orderStage = orderStageService.findStagebySubOrderIdAndName(Long.valueOf(subOrderId), skipRetryStage);
        if (Objects.isNull(orderStage))
            return;
        orderStageService.updateRollbackStatus(
                WorkFlowConstants.StageStatusConstants.STAGE_STATUS_SKIPPED.getValue(), "", "",
                orderStage.getId());
    }

    public static Date createTimezoneInstant(Date date) {
        TimeZone systemTimeZone = TimeZone.getDefault();
        Calendar calendar = Calendar.getInstance(systemTimeZone);
        Date currentDate = calendar.getTime();
        return Date.from(Instant.now());
    }


}
