
package in.co.sixdee.bss.com.orderorchestrator.config.camunda.spin;

import jakarta.validation.constraints.NotNull;
import org.camunda.bpm.engine.delegate.VariableScope;

/**
 * Variable factory of a base type(non parametrized).
 *
 * @param <T>
 * 			type of the factory.
 */

public class BasicVariableFactory<T> implements VariableFactory<T> {

	@NotNull
	private final String	name;

	@NotNull
	private final Class<T>	clazz;

	/**
	 * Creates variable factory for a given type and name.
	 *
	 * @param name
	 *            name of the variable.
	 * @param clazz
	 *            class of the type.
	 */
	public BasicVariableFactory(@NotNull String name, @NotNull Class<T> clazz) {
		this.name = name;
		this.clazz = clazz;
	}

	@Override
	public ReadWriteAdapter<T> on(VariableScope variableScope) {
		return new ReadWriteAdapterVariableScope<>(variableScope, name, clazz);
	}

	@Override
	public ReadWriteAdapter<T> from(VariableScope variableScope) {
		return new ReadWriteAdapterVariableScope<>(variableScope, name, clazz);
	}

}
