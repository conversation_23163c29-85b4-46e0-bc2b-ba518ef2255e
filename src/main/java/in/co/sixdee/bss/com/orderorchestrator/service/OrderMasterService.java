package in.co.sixdee.bss.com.orderorchestrator.service;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class OrderMasterService {

    private final OrderRepository orderRepository;

    public boolean checkIfOrderIsCompleted(Long orderId) {
        return orderRepository.checkIfOrderIsCompleted(orderId);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int orderStatus(String state, String stateReason, Long orderId) {
        return orderRepository.orderStatus(state,
                stateReason, orderId);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int updateProfileId(Long orderId, String profileId) {
        return orderRepository.updateCustomerId(orderId, profileId);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int updateAccountId(Long id, String accountId) {
        return orderRepository.updateAccountId(id, accountId);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateOrderToInProgress(Long orderId) {
        orderRepository.updateOrderToInProgress(orderId);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int updateMsisdn(Long id, String serviceId) {
        return orderRepository.updateMsisdn(id, serviceId);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int updateOrderParams(Long id, String profileId, String accountId) {
        return orderRepository.updateOrderParams(id, profileId, accountId);
    }
}

