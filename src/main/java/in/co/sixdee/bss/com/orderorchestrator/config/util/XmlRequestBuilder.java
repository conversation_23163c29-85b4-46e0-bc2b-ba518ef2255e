package in.co.sixdee.bss.com.orderorchestrator.config.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.JsonPath;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.enrichment.OrderEnrichmentContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Log4j2
@RequiredArgsConstructor
public class XmlRequestBuilder {


    final GetDataFromCache cache;

    final ObjectMapper objectMapper;

    public String buildRequest(String orderType, OrderFlowContext executionContext, Map<String, Object> vairablesMap) {
        String request = null;
        try {
        	if(vairablesMap==null)
        		vairablesMap=new HashMap<String, Object>();
        	
            CacheTableDataDTO reqTemplateConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_XML_REQUEST_TEMPLATE_CONFIG.toString(), orderType);
            if (reqTemplateConfig != null) {
                String requestTemplate = reqTemplateConfig.getNgTableData().get(CacheConstants.CacheFields.REQUEST_TEMPLATE.toString());
                if (executionContext != null) {
                    Map<String, Object> requestKeysMap = Objects.requireNonNull(buildReplacementMap(orderType, executionContext));
                    if (requestKeysMap!=null && !requestKeysMap.isEmpty()) {
                        vairablesMap.putAll(requestKeysMap);
                    }
                }
                if (vairablesMap != null && !vairablesMap.isEmpty()) {
                    Pattern pattern = Pattern.compile(vairablesMap.keySet().stream().map(Pattern::quote).collect(Collectors.joining(GenericConstants.OPERATOR_PIPE)));
                    StringBuilder sb = new StringBuilder(requestTemplate.length());
                    Matcher matcher = pattern.matcher(requestTemplate);
                    int prev = 1;
                    while (matcher.find()) {
                        sb.append(requestTemplate, prev, matcher.start());
                        sb.append(vairablesMap.get(matcher.group()));
                        prev = matcher.end();
                    }
                    sb.append(requestTemplate, prev, requestTemplate.length() - 1);
                    request = sb.toString();
                } else {
                    request = requestTemplate;
                }
            } else {
                log.info("Unable to find request template configuration for order type {}", orderType);
            }

        } catch (Exception e) {
            log.error("Exception occurred while creating the xml request", e);
        }
        return request;
    }

    private Map<String, Object> buildReplacementMap(String orderType, OrderFlowContext executionContext) {
        Map<String, Object> replacements = new HashMap<>();
        try {
            if (executionContext != null) {
                List<CacheTableDataDTO> tokenConfigs = cache.getCacheDetailsFromDBMapAryList(CacheConstants.CacheKeys.COM_XML_REQUEST_TOKEN_CONFIG.toString(), orderType);
                if (tokenConfigs == null) {
                    log.info("No token configuration found for the order type {}", orderType);
                    return null;
                }
                var docContext = JsonPath.parse(objectMapper.writeValueAsString(executionContext));
                String tokenPath = null;
                String tokenName = null;
                for (CacheTableDataDTO entry : tokenConfigs) {
                    tokenPath = entry.getNgTableData().get(CacheConstants.CacheFields.TOKEN_PATH.toString());
                    tokenName = entry.getNgTableData().get(CacheConstants.CacheFields.TOKEN_NAME.toString());
                    if (tokenPath.startsWith(GenericConstants.OPERATOR_DOLLAR)) {
                        var value = docContext.read(tokenPath);
                        replacements.put(tokenName, (String) value);
                    } else {
                        replacements.put(tokenName, tokenPath);
                    }
                }
            }

        } catch (
                JsonProcessingException e) {
            log.error("Exception occurred in buildReplacementMap", e);
        }

        return replacements;
    }


    public void validateXMLResponse(String response, OrderEnrichmentContext executionContext) {

        String statusCode = null;
        String statusMessage = null;

        try {
            if (response != null) {
                DocumentBuilderFactory factory = XMLUtils.getDocumentBuilderFactory();
                DocumentBuilder builder = factory.newDocumentBuilder();
                ByteArrayInputStream input = new ByteArrayInputStream(response.getBytes("UTF-8"));
                Document document = builder.parse(input);

                //
                try {
                    NodeList statusList = document.getElementsByTagName("StatusMessage");
                    if (statusList.getLength() > 0) {
                        Element statusElement = (Element) statusList.item(0);
                        log.info("StatusMessage:" + statusElement.getTextContent());
                        statusMessage = statusElement.getTextContent();
                    }

                } catch (Exception e) {
                    log.info("Exception in getting status Message",e);
                }

                // Extract the value of the <Status> element
                NodeList statusList = document.getElementsByTagName("Status");
                if (statusList.getLength() > 0) {
                    Element statusElement = (Element) statusList.item(0);
                    if (statusElement.getTextContent().equals("9")) {
                        log.info("success status code : " + statusElement.getTextContent());
                    } else {
                        statusCode = statusElement.getTextContent();
                        throw new IllegalArgumentException("Invalid status code found in the XML response");
                    }
                    log.info("statusElementContent:" + statusElement.getTextContent());
                } else {
                    throw new IllegalArgumentException("No <Status> element found in the XML response");
                }
            } else {
                executionContext.setError(true);
				/*executionContext.getErrorDetail().setSystem(callThirdPartyDTO.getThirdPartySystem());
				executionContext.getErrorDetail().setCode("500");
				executionContext.getErrorDetail().setMessage("Response not available");*/
            }

        } catch (IOException | org.xml.sax.SAXException | IllegalArgumentException
                 | javax.xml.parsers.ParserConfigurationException e) {
            executionContext.setError(true);
			/*executionContext.getErrorDetail().setSystem(callThirdPartyDTO.getThirdPartySystem());
			executionContext.getErrorDetail().setCode(statusCode!=null?statusCode:"500");
			executionContext.getErrorDetail().setMessage(statusMessage!=null?statusMessage:"Response not available");*/
            log.info("Exception occurred in validateXMLResponse",e);
        }


    }
}
