package in.co.sixdee.bss.com.orderorchestrator.core;

import java.util.concurrent.ConcurrentLinkedQueue;

import in.co.sixdee.bss.com.orderorchestrator.model.OrderCallBack;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;


@Configuration
public class ConcurrentQueue {

	/**
	 * 
	 * @return ConcurrentLinkedQueue and maintains application level.
	 */
	@Bean
	@Scope("application")
	public ConcurrentLinkedQueue<OrderCallBack> getQueue() {
		return new ConcurrentLinkedQueue<OrderCallBack>();
	}

}
