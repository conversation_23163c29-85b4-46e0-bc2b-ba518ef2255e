package in.co.sixdee.bss.com.orderorchestrator.config.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.gson.JsonParser;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Collection;

public final class JsonUtils {
	/**
	 * Protected constructor for a utility class.
	 */
    private JsonUtils() {
	}

	/**
	 * Convert a Java object to a JSON string.
	 *
	 * @param value The Java object to marshall
	 * @return The JSON string
	 * @throws Exception For any marshalling exception
	 */
	public static String marshall(final Object value, PropertyNamingStrategy namingStrategy) throws Exception {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL);
			objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
			if (namingStrategy != null)
				objectMapper.setPropertyNamingStrategy(namingStrategy);
			return objectMapper.writeValueAsString(value);
		} catch (final JsonProcessingException jpe) {
			throw new Exception("Failed to marshall object", jpe);
		}
	}

	/**
	 * Convert a JSON string of a collection back to a Java object.
	 *
	 * @param source        The JSON string
	 * @param typeReference The type reference of the collection to unmarshall to
	 * @param <T>           The type of the collection ie Set of String
	 * @return The Java object
	 * @throws Exception For any exception during unmarshalling
	 */
	public static <T extends Collection<?>> T unmarshall(final String source, final TypeReference<T> typeReference) throws Exception {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			objectMapper.setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL);
			if (StringUtils.isNotBlank(source)) {
				return objectMapper.readValue(source, typeReference);
			} else {
				return objectMapper.readValue("[]", typeReference);
			}
		} catch (final IOException ioe) {
			throw new Exception("Failed to read JSON value", ioe);
		}
	}

	public static <T extends Object> T unmarshall(final String source, final Class<T> clazz) throws Exception {
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			objectMapper.setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL);
			if (StringUtils.isNotBlank(source)) {
				return objectMapper.readValue(source, clazz);
			} else {
				return null;
			}
		} catch (final IOException ioe) {
			throw new Exception("Failed to read JSON value", ioe);
		}
	}

	public static boolean isJson(final String source) {
		if (source == null)
			return false;
		try {
			JsonParser.parseString(source);
		} catch (Exception e) {
			return false;
		}
		return true;
	}

}
