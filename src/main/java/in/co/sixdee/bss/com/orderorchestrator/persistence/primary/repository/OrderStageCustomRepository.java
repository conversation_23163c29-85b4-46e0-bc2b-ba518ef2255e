package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.repository;

import java.util.List;

import org.springframework.data.repository.Repository;
import org.springframework.transaction.annotation.Transactional;

import in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain.OrderStageEntity;

@org.springframework.stereotype.Repository
@Transactional
public interface OrderStageCustomRepository extends Repository<OrderStageEntity, Long> {
	
	public List<Long> getSubOrderIdByOrderIdAndStageCodeAndState(Long orderId,String ponrRule);

}
