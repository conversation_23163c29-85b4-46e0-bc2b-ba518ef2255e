package in.co.sixdee.bss.com.orderorchestrator.persistence.primary.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.domain.Persistable;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

@NoArgsConstructor
@Entity
@Table(name = "COM_ORDER_PAYLOAD_DETAILS")
@lombok.Generated
@Getter
@Setter
public class OrderPayloadEntity extends AbstractAuditingEntity implements Serializable, Persistable<Long> {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name = "SEQ_ID", length = 20, unique = true)
	private Long id;

	@Column(name = "CORRELATION_ID", length = 20)
	private Long correlationId;

	@Column(name = "PAYLOAD")
	private String payload;

	@Override
	public boolean isNew() {
		return true;
	}
}

