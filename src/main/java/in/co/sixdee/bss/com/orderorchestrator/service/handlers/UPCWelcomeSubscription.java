package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.om.model.dto.order.Charge;
import in.co.sixdee.bss.om.model.dto.order.ProductOffering;
import in.co.sixdee.bss.om.model.dto.order.ProductOfferingPrice;
import in.co.sixdee.bss.om.model.dto.order.ProductSpecification;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "upcWelcomeSubscription")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class UPCWelcomeSubscription extends AbstractDelegate {

	@Autowired
	private ObjectMapper objectMapper;

	@Override
	protected void execute() throws Exception {
		var callThirdPartyDTO = callThirdParty(null);
		if (callThirdPartyDTO == null) {
			executionContext.setError(true);
			return;
		}
		var response = callThirdPartyDTO.getResponse();
		validateResponse(callThirdPartyDTO);
		if (executionContext.isError()) {
			log.error("{} Response validation failed", activityId);
			return;
		}
		enrichWelcomeSubscription(response);
		workflowDataUpdated = true;

	}

	protected void enrichWelcomeSubscription(String response) throws JsonProcessingException {
		var productOfferings = objectMapper.readValue(response, new TypeReference<List<ProductOffering>>() {
		});
		if (ObjectUtils.isEmpty(productOfferings))
			return;

		if (ObjectUtils.isNotEmpty(executionContext.getAttributes().get("welcomePlanId"))) {
			var planId = executionContext.getAttributes().get("welcomePlanId");
			var productOfferingUpc = productOfferings.stream()
					.filter(productOffering -> planId.equals(productOffering.getId())).findFirst().orElse(null);

			if (ObjectUtils.isNotEmpty(productOfferingUpc)) {
				var welcomeSubscription = setSubscription(productOfferingUpc);
				if (ObjectUtils.isNotEmpty(welcomeSubscription))
					executionContext.getWorkflowData().put("welcomeSubscription", welcomeSubscription);
			}
		}
	}

	protected Subscription setSubscription(ProductOffering productOffering)
			throws JsonMappingException, JsonProcessingException {
		var subscription = new Subscription();
		subscription.setPlanId(productOffering.getId());
		subscription.setPlanName(productOffering.getName());
		subscription.setPlanDesc(productOffering.getDescription());
		subscription.setMarketingDescription(productOffering.getMarketingDescription());
		subscription.setUpcVersion(productOffering.getVersionNo());
		subscription.setCpId(productOffering.getPartner());
		if (ObjectUtils.isNotEmpty(productOffering.getProductOfferGroup())) {
			subscription.setPlanGroupId(productOffering.getProductOfferGroup().getId());
			subscription.setPlanGroupName(productOffering.getProductOfferGroup().getName());
		}
		if (productOffering.getProductSpecifications() != null
				&& !productOffering.getProductSpecifications().isEmpty()) {
			if ("2".equals(productOffering.getProductSpecifications().get(0).getType().getId())) {
				subscription.setPlanType(GenericConstants.PLAN_TYPE_ADDON);
				getAutoRenewalAndExternalOfferIdfromUPC(productOffering.getProductSpecifications(), subscription);
			} else
				throw new CommonException("Welcome Offer is a base plan : " + productOffering.getId());

		} else {
			throw new CommonException("Product Specification is empty for welcome offer : " + productOffering.getId());
		}
		enrichCharges(subscription, productOffering.getProductOfferingPrice());
		return subscription;
	}

	protected void getAutoRenewalAndExternalOfferIdfromUPC(List<ProductSpecification> productSpecifications,
			Subscription subscription) {

		if (ObjectUtils.isNotEmpty(productSpecifications)) {
			var cfss = Objects.requireNonNull(productSpecifications.stream().map(specification -> specification.getCfss()).findAny()
					.orElse(null));
			if (ObjectUtils.isNotEmpty(cfss)) {
				var nccCharacteristic = Objects.requireNonNull(cfss.stream()
						.filter(cfs -> cfs.getName().equalsIgnoreCase(GenericConstants.CFS_M1_Base))
						.map(cfs -> cfs.getCharacteristics()).findAny().orElse(null));
				if (ObjectUtils.isNotEmpty(nccCharacteristic)) {
					var welcomeExternalOfferId = Objects.requireNonNull(nccCharacteristic.stream()
							.filter(character -> character.getName()
									.equalsIgnoreCase(GenericConstants.CHARACTERISTIC_EXTERNAL_OFFER_ID))
							.map(character -> character.getValue()).findAny().orElse(null));
					if (StringUtils.isEmpty(welcomeExternalOfferId))
						return;
					executionContext.getWorkflowData().put("welcomeExternalOfferId", welcomeExternalOfferId);
					subscription.setOcsPlanId(welcomeExternalOfferId);
				}

				var upcCharacteristic = Objects.requireNonNull(cfss.stream()
						.filter(cfs -> cfs.getName().equalsIgnoreCase(GenericConstants.CFS_UPC))
						.map(cfs -> cfs.getCharacteristics()).findAny().orElse(null));
				if (ObjectUtils.isNotEmpty(upcCharacteristic)) {
					var welcomeIsRenewal = Objects.requireNonNull(upcCharacteristic.stream()
							.filter(character -> character.getName()
									.equalsIgnoreCase(GenericConstants.CHARACTERISTIC_IS_RENEWAL))
							.map(character -> character.getValue()).findAny().orElse(null));
					executionContext.getWorkflowData().put("welcomeIsRenewal", welcomeIsRenewal);
					if ("true".equalsIgnoreCase(welcomeIsRenewal))
						subscription.setAutoRenewal("1");
					else
						subscription.setAutoRenewal("0");
				}
			}
		}
	}

	protected void enrichCharges(Subscription subscription, List<ProductOfferingPrice> productOfferingPrices) {
		if (productOfferingPrices != null) {
			List<Charge> charges = new ArrayList<>();
			for (ProductOfferingPrice price : productOfferingPrices)
				charges.add(createNewCharge(price));
			if (!charges.isEmpty())
				subscription.setCharges(charges);
		}
	}

	protected Charge createNewCharge(ProductOfferingPrice price) {
		var charge = new Charge();
		charge.setUpcChargeId(price.getId());
		charge.setUpcChargeName(price.getName());
		charge.setChargeDesc(price.getName());
		charge.setAmount(price.getPrice());
		charge.setRate(price.getPrice());
		if (price.getPriceCategory() != null) {
			if (price.getPriceCategory().getId() != null) {
				charge.setUpcPriceCategory(price.getPriceCategory().getId());
			}
			if (price.getPriceCategory().getBillingChargeType() != null
					&& price.getPriceCategory().getBillingChargeType().getId() != null) {
				charge.setChargeCategory(price.getPriceCategory().getBillingChargeType().getId());
			}
			if (price.getPriceCategory().getBillingChargeCategory() != null
					&& price.getPriceCategory().getBillingChargeCategory().getId() != null) {
				if ("1".equalsIgnoreCase(price.getPriceCategory().getBillingChargeCategory().getId())
						|| "2".equalsIgnoreCase(price.getPriceCategory().getBillingChargeCategory().getId()))
					charge.setBillingChargeType(price.getPriceCategory().getBillingChargeCategory().getId());
			}
		}
		if (price.getPriceMode() != null) {
			if ("2".equals(price.getPriceMode().getId()))
				charge.setChargeType("1");
			else
				charge.setChargeType("0");
			charge.setChargeRecurranceType(charge.getChargeType());
		}
		if (price.getPriceCode() != null)
			charge.setChargeCode(price.getPriceCode().getCode());
		
		  charge.setIsProrata(BooleanUtils.toBoolean(price.getProrataEnable()) ? "1" : "0");
	      charge.setProrationFlag(getProrationFlag(price));
		
		
		charge.setChargeVersion(price.getVersionNo());
		if (price.getFrequency() != null)
			charge.setChargeFrequency(price.getFrequency().getId());
		charge.setChargeFactor(price.getFactor());
		return charge;
	}

    private String getProrationFlag(ProductOfferingPrice price) {
     	
    	var suspend=getIntegerValue(price.getProrataEnabledInSuspension());
    	var terminate=getIntegerValue(price.getProrataEnabledInTermination());
    	var planChange=getIntegerValue(price.getProrataEnabledInPlanChange());
    	var activation=getIntegerValue(price.getProrataEnabledInActivation());
    
    	
    	StringBuilder prorationFlagBuilder= new StringBuilder();
    	prorationFlagBuilder.append(suspend).append(terminate).append(planChange).append(activation);

    	return prorationFlagBuilder.toString();
    }
    
    private Integer getIntegerValue (String prorata) {
    	return BooleanUtils.toInteger(BooleanUtils.toBoolean(prorata));
    }


}
