package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ConfigurationNotValidException extends RuntimeException {

    private static final long serialVersionUID = 933382937208867189L;
    private int errorCode;
    private String errorDetail;
    private String errorSystem;

    public ConfigurationNotValidException() {

    }

    public ConfigurationNotValidException(String message) {
        super(message);
    }

    public ConfigurationNotValidException(int errorCode, String message) {
        super(message);
        setErrorCode(errorCode);
    }

    public ConfigurationNotValidException(Throwable cause) {
        super(cause);
    }

    public ConfigurationNotValidException(int errorCode, Throwable cause) {
        super(cause);
        setErrorCode(errorCode);
    }

    public ConfigurationNotValidException(String message, Throwable cause) {
        super(message, cause);
    }

    public ConfigurationNotValidException(int errorCode, String message, Throwable cause) {
        super(message, cause);
        setErrorCode(errorCode);
    }

    public ConfigurationNotValidException(int errorCode, String message, String errorDetail, String errorSystem) {
        super(message);
        setErrorCode(errorCode);
        setErrorDetail(errorDetail);
        setErrorSystem(errorSystem);
    }

    @Override
    public synchronized Throwable fillInStackTrace() {
        return this;
    }
}
