package in.co.sixdee.bss.com.orderorchestrator.config.exception;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import in.co.sixdee.bss.om.model.dto.WorkflowRequest.Orders;

public class BulkRetryInvalidOrderException extends RuntimeException {

	private static final long serialVersionUID = 1L;

	private StatusConstants.HttpConstants status;
	private String message;
	private List<String> errors;
	private Set<Orders> orders;

	public BulkRetryInvalidOrderException(StatusConstants.HttpConstants status, String message, List<String> errors) {
		super();
		this.status = status;
		this.message = message;
		this.errors = errors;
	}

	public BulkRetryInvalidOrderException(StatusConstants.HttpConstants status, String message, String error) {
		super();
		this.status = status;
		this.message = message;
		errors = Arrays.asList(error);
	}

	public BulkRetryInvalidOrderException(StatusConstants.HttpConstants status, String message) {
		super();
		this.status = status;
		this.message = message;
	}

	public BulkRetryInvalidOrderException(String msg) {
		super();
		this.message = msg;
	}

	public BulkRetryInvalidOrderException(StatusConstants.HttpConstants status, String message, Set<Orders> orders) {
		super();
		this.status = status;
		this.message = message;
		this.orders = orders;
	}

	public StatusConstants.HttpConstants getStatus() {
		return status;
	}

	public void setStatus(StatusConstants.HttpConstants status) {
		this.status = status;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public List<String> getErrors() {
		return errors;
	}

	public void setErrors(List<String> errors) {
		this.errors = errors;
	}

	public Set<Orders> getOrders() {
		return orders;
	}

	public void setOrders(Set<Orders> orders) {
		this.orders = orders;
	}

}
