package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.bazaarvoice.jolt.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.Characteristic;
import in.co.sixdee.bss.om.model.dto.order.OrderItem;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO;
import in.co.sixdee.bss.om.model.dto.order.som.SOMServiceOrderDTO.SOMService;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "somFetchSubscriptionIdDetails")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SOMFetchSubscriptionIdDetails extends AbstractDelegate {

	@Override
	protected void execute() throws Exception {
		String request = getRequestFromSpec();
		if (executionContext.isError())
			return;

		var callThirdPartyDTO = callThirdParty(request);
		if (callThirdPartyDTO == null) {
			executionContext.setError(true);
			return;
		}

		var response = callThirdPartyDTO.getResponse();
		validateResponse(callThirdPartyDTO);
		if (executionContext.isError())
			return;
		if (StringUtils.isNotEmpty(response)) {
			if (filterSubscriptionId(response)) {
				execution.setVariable("shouldCallSom", true);
			} else {
				execution.setVariable("shouldCallSom", false);
			}
			modifyWorkflowData(response);
		}
	}

	private boolean filterSubscriptionId(String response) throws JsonMappingException, JsonProcessingException {
		String SubscriptionIdFromRequest = getSubscriptionIdFromRequest(executionContext);
		List<SOMService> somServiceOrderItems = getSubscriptionIdFromSomResponse(response);
		List<SOMService> somMatchingSubscrptionId = null;

		if (SubscriptionIdFromRequest != null && somServiceOrderItems != null) {
			for (SOMService serviceOrderItem : somServiceOrderItems) {
				String somSubscriptionId = getSomSubscriptionId(serviceOrderItem);
				if (somSubscriptionId != null && somSubscriptionId.equals(SubscriptionIdFromRequest)) {
					return true;
				}
			}
		} else {
			log.info("SubscriptionId from request and SubscriptionId from som are not matching with each other");
		}
		return false;
	}

	private String getSomSubscriptionId(SOMService serviceOrderItem) {
		String subscriptionId = null;
		if (serviceOrderItem != null && serviceOrderItem.getServiceCharacteristic() != null) {
			for (Characteristic characteristic : serviceOrderItem.getServiceCharacteristic()) {
				if ("SUBSCRIPTION_ID".equals(characteristic.getName())) {
					subscriptionId = characteristic.getValue();
					break;

				}
			}
		}
		return subscriptionId;
	}

	private List<SOMService> getSubscriptionIdFromSomResponse(String response)
			throws JsonMappingException, JsonProcessingException {
		return objectMapper.readValue(response, new TypeReference<List<SOMServiceOrderDTO.SOMService>>() {
		});
	}

	private String getSubscriptionIdFromRequest(OrderFlowContext executionContext) {

		Map<String, Object> workflowData = (Map<String, Object>) executionContext.getWorkflowData();
		if (ObjectUtils.isNotEmpty(workflowData) && (ObjectUtils.isNotEmpty(workflowData.get("currentExecution")))) {
			Map<String, Object> currentExecution = (Map<String, Object>) executionContext.getWorkflowData()
					.get("currentExecution");
			if (ObjectUtils.isNotEmpty(currentExecution)) {
				Map<String, Object> executionData = (Map<String, Object>) currentExecution.get("executionData");
				if (ObjectUtils.isNotEmpty(executionData)) {
					List<Map<String, Object>> characteristics = (List<Map<String, Object>>) executionData
							.get("itemCharacteristic");
					if (ObjectUtils.isNotEmpty(characteristics)) {
						for (Map<String, Object> characteristic : characteristics) {
							if ("SUBSCRIPTION_ID".equalsIgnoreCase((String) characteristic.get("name"))
									&& StringUtils.isNotEmpty((String) characteristic.get("value"))) {
								return (String) characteristic.get("value");
							}
						}
					}
				}
			}
		}
		return null;

	}
}