package in.co.sixdee.bss.com.orderorchestrator.config.util;

import java.util.LinkedList;
import java.util.ListIterator;
import java.util.concurrent.RejectedExecutionException;

import lombok.extern.log4j.Log4j2;



/**
* <AUTHOR>
* @version *******
* @since Mar 15, 2012
*
* Development History
*
* Date Author Description ------------------------------------------------
* ------------------------------------------ Mar 15, 2012 Mano<PERSON> Kumar ----
* -------------------------------------------------------------------- ------------------
*/
@Log4j2
public class Queue<T> {

	private int maxQueueSize = -1;

	protected LinkedList<T> queueData = null;

	protected int size = 0;

	private boolean flag = true;

	private final Object object;

	private final String name;

	private boolean isWait = true;

	public Queue() {
		this("Queue");
	}

	public Queue(String name) {
		this(name, -1);
	}

	public Queue(int maxSize) {
		this("Queue", maxSize);
	}

	public Queue(String name, int maxSize) {
		this(name, maxSize, true);
	}

	public Queue(String name, int maxSize, boolean isWait) {
		this.name = name;
		maxQueueSize = maxSize;
		this.isWait = isWait;
		object = new Object();
		queueData = new LinkedList<T>();
		log.info(name + " -> maxQueueSize = " + maxQueueSize + " | isWait = " + isWait);
	}

	/**
	 * Current count of the elements in the queue.
	 */
	public int size() {
		synchronized (queueData) {
			return size;
		}
	}

	/**
	 * If there is no element in the queue.
	 */
	public boolean isEmpty() {
		synchronized (queueData) {
			return queueData.isEmpty();
		}
	}

	/**
	 * Removes first element form the queue and returns it. If the queue is
	 * empty, then wait.
	 */
	public T dequeue() {
		T first = null;
		try {
			synchronized (queueData) {
				while (flag) {
					if (!queueData.isEmpty()) {
						first = queueData.removeFirst();
						--size;
						break;
					} else {
						try {
							queueData.wait();
						} catch (InterruptedException e) {
							Thread.currentThread().interrupt();
							break;
						}
					}
				}
			}

			if (size() < maxQueueSize) {
				synchronized (object) {
					object.notifyAll();
				}
			}
		} finally {
			/*
			 * if (log.isDebugEnabled()) log.debug(name +
			 * " -> Inside pop::\tQueueSize=" + size() + "(" + maxQueueSize +
			 * ")");
			 */
		}
		return first;
	}

	/**
	 * Tries to find the provided element in the queue and if found, removes it
	 * from the queue and returns it. If the element is not found returns null.
	 */
	public T dequeue(T obj) {
		T found = null;
		synchronized (queueData) {
			found = find(obj);
			if (found != null) {
				queueData.remove(found);
				--size;
			}
		}
		return found;
	}

	/**
	 * Appends an element to the end of the queue. If the queue has set limit on
	 * maximum elements and there is already specified max count of elements in
	 * the queue throws IndexOutOfBoundsException.
	 */
	public void enqueue(T obj) throws RejectedExecutionException {
		try {
			while (flag) {
				try {
					if (maxQueueSize > 0 && size() >= maxQueueSize) {
						throw new RejectedExecutionException("Queue is full. Element not added.");
					}
					synchronized (queueData) {
						queueData.addLast(obj);
						++size;
						queueData.notifyAll();
						break;
					}
				} catch (RejectedExecutionException e) {
					if (isWait) {
						// log.debug(name + " -> " + e.getMessage() +
						// " Waiting for Release.\tQueueSize=" + size() + "(" +
						// maxQueueSize + ")");
						synchronized (object) {
							try {
								if (size() >= maxQueueSize) {
									object.wait();
								}
							} catch (InterruptedException ie) {
								// TODO Auto-generated catch block
								log.error(ie.getMessage(), ie);
								Thread.currentThread().interrupt();
							}
						}
					} else {
						throw e;
					}
				}
			}
		} finally {
			/*if (log.isDebugEnabled())
				log.debug(name + " -> Inside push::\tQueueSize=" + size() + "(" + maxQueueSize + ")");*/
		}
	}

	/**
	 * Searches the queue to find the provided element. Uses <code>equals</code>
	 * method to compare elements.
	 */
	public T find(T obj) {
		synchronized (queueData) {
			T current;
			ListIterator<T> iter = queueData.listIterator(0);
			while (iter.hasNext()) {
				current = iter.next();
				if (current.equals(obj)) {
					return current;
				}
			}
		}
		return null;
	}

	public void shutdown() {
		flag = false;
		synchronized (queueData) {
			queueData.notifyAll();
		}
	}

}
