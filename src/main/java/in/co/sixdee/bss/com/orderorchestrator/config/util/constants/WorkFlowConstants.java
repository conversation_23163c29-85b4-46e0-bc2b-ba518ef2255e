package in.co.sixdee.bss.com.orderorchestrator.config.util.constants;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * <AUTHOR>
 */

public class WorkFlowConstants {

    public enum WorkItemExecutionStatus {
        STATUS_COMPLETED("Completed"), STATUS_FAILURE("Failed"), STATUS_PENDING("Pending"), STATUS_INPROGRESS(
                "In-Progress"), STATUS_SKIPPED("Skipped");

        private final String value;

        WorkItemExecutionStatus(String newValue) {
            value = newValue;
        }

        public String getValue() {
            return value;
        }

    }

    public enum WorkFlowProcessVariables {
        GENERIC_DTO("GenericDTO"), STATUS("Status"), PROCESS_ID("processId"), SERVICE_ORDER("serviceOrder"), WORKFLOW_DATA(
                "workflowData"), THIRD_PARTY_ID("THIRD_PARTY_ID"), REQ_SPEC_KEY("REQ_SPEC_KEY"), IS_ASYNC_TASK(
                "isAsyncTask"), IS_COMMON_HANDLER("isCommonHandler"), EXECUTION_DATA("executionData"), CURRENT_EXECUTION(
                "currentExecution"), IS_PART_OF_MULTI_INSTANCE("isPartOfMultiInstance"), RESPONSE_ATTRIBUTES(
                "RESPONSE_ATTRIBUTES"), PROCESS_VARIABLES(
                "processVariables"), UPDATE_KEY("UPDATE_KEY"), STATUS_SUCCESS("0"), STATUS_FAILURE(
                "1"), EDR_TYPE("edrType"), EDR_INITIAL(
                "INITIAL"), EDR_INTERMEDIATE("INTERMEDIATE"), EDR_FINAL("FINAL"), RESP_SPEC_KEY("RESP_SPEC_KEY"), CALLBACK_EVENT("CALLBACK_EVENT");

        private final String value;

        WorkFlowProcessVariables(String newValue) {
            value = newValue;
        }

        public String toString() {
            return String.valueOf(this.value);
        }
    }

    public enum BpmnConstants {
        INTERMEDIATE_CATCH_EVENT_ACTIVITY("intermediateCatchEvent"), SERVICE_TASK_ACTIVITY("serviceTask"), EVENT_NAME_START(
                "start"), EVENT_NAME_END("end"), TIMER_START_EVENT("startEvent"), RECEIVE_TASK_ACTIVITY(
                "receiveTask"), INTERMEDIATE_THROW_EVENT_ACTIVITY("intermediateThrowEvent"), END_EVENT(
                "endEvent"), ORDER_EXEC_END("orderExecEnd"), ORDER_EXEC_START("orderExecStart"), CALL_ACTIVITY(
                "callActivity"), SUBPROCESS_END_EVENT("subProcessEndEvent");

        private final String value;

        BpmnConstants(String newValue) {
            value = newValue;
        }

        public String getStringValue() {
            return String.valueOf(value);
        }
    }

    public enum ProcessConstants {

        ACTION_SKIP("skip"), ACTION_RETRY("retry"), KEY_ACTIVITY_ID("activityId"), KEY_IS_SKIP_HANDLER(
                "isSkipHandler"), KEY_IS_COMMON("isCommon");

        public String desc;

        private ProcessConstants(String desc) {
            this.desc = desc;
        }

    }

    @Getter
    public enum StageStatusConstants {
        STAGE_STATUS_COMPLETED("Completed"), STAGE_STATUS_FAILED("Failed"), STAGE_STATUS_PENDING(
                "Pending"), STAGE_STATUS_INPROGRESS("In-Progress"), STAGE_STATUS_SKIPPED("Skipped"), STAGE_STATUS_REJECTED(
                "Rejected"), STAGE_STATUS_CANCELLED(
                "Cancelled"), STAGE_STATUS_APPROVED("approved"), STAGE_STATUS_PARTIAL_SUCCESS("PartialSuccess"),
        STAGE_STATUS_HELD("Held");;

        private final String value;

        StageStatusConstants(String newValue) {
            value = newValue;
        }

    }

    public enum OrderCallBackTypes {

        SOM("SOM"), PAYMENT("PAYMENT"), APPROVAL("APPROVAL"), MNP("MNP"), SIM_DELIVERY("SIM"),
        PORT_IN_NOTIFICATION("PORT_IN_NOTIFICATION"), PORT_IN_NOTIFICATION_SUCCESS("PORT_IN_NOTIFICATION_SUCESS"),
        PORT_IN_NOTIFICATION_FAILURE("PORT_IN_NOTIFICATION_FAILURE"), CONNECT_SERVICE("CONNECT_SERVICE"),
        CONFIRM_PORT_OUT("CONFIRM_PORT_OUT"), DISCONNECT_SERVICE_INTERNAL("DISCONNECT_SERVICE_INTERNAL"),
        DISCONNECT_SERVICE("DISCONNECT_SERVICE");

        public final String desc;

        private OrderCallBackTypes(String desc) {
            this.desc = desc;
        }

    }


    public static final ArrayList<String> orderStatusAllowedForCancellation = new ArrayList<String>(
            Arrays.asList(WorkFlowConstants.StageStatusConstants.STAGE_STATUS_PENDING.getValue(),
                    WorkFlowConstants.StageStatusConstants.STAGE_STATUS_FAILED.getValue(),
                    WorkFlowConstants.StageStatusConstants.STAGE_STATUS_INPROGRESS.getValue()));


}
