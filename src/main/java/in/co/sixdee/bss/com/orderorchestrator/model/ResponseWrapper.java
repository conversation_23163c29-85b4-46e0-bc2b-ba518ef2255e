package in.co.sixdee.bss.com.orderorchestrator.model;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import in.co.sixdee.bss.om.model.dto.WorkFlowErrorBean;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Validated
@jakarta.annotation.Generated(value = "io.swagger.codegen.languages.SpringCodegen", date = "2021-03-29T20:22:51.232+05:30")
public class ResponseWrapper implements Serializable {

	/**
	 *
	 */
	private static final long                          serialVersionUID = 1L;
	private              Integer                       code;
	private              String                        reason;
	private              String                        message;
	private              StatusConstants.HttpConstants status;

	private List<WorkFlowErrorBean> errors;

}
