package in.co.sixdee.bss.com.orderorchestrator.service.wfdatamodifier;

import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.common.core.AppInstanceIdManager;
import in.co.sixdee.bss.common.util.ApplicationProcessContext;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import lombok.extern.log4j.Log4j2;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class PaymentInfoModifier implements ExecutionListener {

    protected ApplicationProcessContext processContext;

    @Autowired
    protected ProcessDataAccessor processDataAccessor;

    @Autowired
    private AppInstanceIdManager appInstanceSequence;


    @Override
    public void notify(DelegateExecution execution) throws Exception {
        var orderFlowContext = processDataAccessor.getOrderFlowContext(execution);
        createApplicationProcessContext(orderFlowContext);
        orderFlowContext.getOrder().setPayment(null);
        processDataAccessor.setOrderFlowContext(execution, orderFlowContext);
    }


    private void createApplicationProcessContext(OrderFlowContext orderFlowContext) {
        processContext = new ApplicationProcessContext(appInstanceSequence.getInstanceId(), orderFlowContext.getTraceId(),
                orderFlowContext.getRequestId(), orderFlowContext.getChannel(), orderFlowContext.getChannel(),orderFlowContext.getEntityId());
        processContext.setMdc();
    }

}
