package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import in.co.sixdee.bss.com.orderorchestrator.config.exception.CommonException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.StatusConstants;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

@Log4j2
@Component(value = "taxCalculationHandler")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class TaxCalculationHandler extends AbstractDelegate {

    @Override
    protected void execute() throws Exception {

        workflowDataUpdated = true;
        var taxRate = fetchTaxRate();
        if (executionContext.isError())
            return;
        var configChargeType = getApplicationConfigValue("TAX_CALCULATION_REQD_CHARGE_TYPES");
        var chargeTypes = Set.of(configChargeType.split(","));
        chargeTypes.forEach(chargeType -> {
            var chargeAmount = executionContext.getOrder().getPayment().getAdditionalCharges().stream().filter(charge ->
                    StringUtils.isNotEmpty(charge.getChargeType()) && chargeType.equalsIgnoreCase(charge.getChargeType())).map(charge -> charge.getAmount()).findAny().orElse(null);
            if (StringUtils.isEmpty(chargeAmount))
                return;
            if (taxRate > 0) {
                var taxAmount = calculateTax(taxRate, Double.valueOf(chargeAmount));
                if (taxAmount != 0) {
                    executionContext.getAttributes().put(chargeType + "_dutyFreeAmount", String.valueOf(taxAmount));
                    executionContext.getAttributes().put(chargeType + "_taxRate", String.valueOf(taxRate));
                }
            }
        });
    }

    private Double fetchTaxRate() throws Exception {
        var taxRate = 0.0;
        if (checkTaxCalculationReqd()) {
            var callThirdPartyDTO = callThirdParty(null);
            if (callThirdPartyDTO == null) {
                executionContext.setError(true);
                return taxRate;
            }
            var response = callThirdPartyDTO.getResponse();
            validateResponse(callThirdPartyDTO);
            var billResp = objectMapper.readTree(response);
            if (ObjectUtils.isNotEmpty(billResp.get("response")) && ObjectUtils.isNotEmpty(billResp.get("response").get("tableData").get(0)))
                taxRate = billResp.get("response").get("tableData").get(0).get("tax_rate").asDouble();
            else
                taxRate = Double.valueOf(getApplicationConfigValue("DEFAULT_TAX_RATE"));
        } else {
            taxRate = Double.valueOf(getApplicationConfigValue("DEFAULT_TAX_RATE"));
        }
        return taxRate;
    }


    protected boolean checkTaxCalculationReqd() {
        if (ObjectUtils.isNotEmpty(executionContext.getOrder().getProfile()) &&
                ObjectUtils.isNotEmpty(executionContext.getOrder().getProfile().getAccount()) &&
                StringUtils.isNotEmpty(executionContext.getOrder().getProfile().getAccount().getTaxApplicable())) {
            return "1".equalsIgnoreCase(executionContext.getOrder().getProfile().getAccount().getTaxApplicable());
        } else {
            if (ObjectUtils.isNotEmpty(executionContext.getEnrichmentResults()) &&
                    executionContext.getEnrichmentResults().containsKey("accountInfo")) {
                var accountInfo = objectMapper.convertValue(executionContext.getEnrichmentResults().get("accountInfo"), LinkedHashMap.class);
                if (ObjectUtils.isNotEmpty(accountInfo.get("taxApplicable")))
                    return "1".equalsIgnoreCase(accountInfo.get("taxApplicable").toString());
            }
        }
        return false;
    }

    protected Double calculateTax(Double taxRate, Double chargeAmount) {
        var dutyFreeAmount = 0.0;
        executionContext.getAttributes().put("taxRate", String.valueOf(taxRate));
        if (chargeAmount != 0) {
            var taxAmount = chargeAmount * (taxRate / 100);
            dutyFreeAmount = chargeAmount - taxAmount;
        }
        return dutyFreeAmount;
    }

    protected String getApplicationConfigValue(String configName) {
        var appConfig = cache.getCacheDetailsFromDBMap(
                CacheConstants.CacheKeys.OM_APPLICATION_CONFIG.name(), configName);
        if (appConfig == null)
            throw new CommonException(StatusConstants.HttpConstants.INTERNAL_SERVER_ERROR, configName + " configuration is not found ");
        return appConfig.getNgTableData().get("CONFIG_VALUE");
    }
}
