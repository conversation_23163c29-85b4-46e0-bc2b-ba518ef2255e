package in.co.sixdee.bss.com.orderorchestrator.config.util.constants;

import lombok.Getter;

public class MNPConstants {

    public static final String PORT_IN_TYPE_MVNO_MVNO = "PortInFromMvnoToMvno";

    public static final String PORT_IN_TYPE_FROM_EXTERNAL_TELCO = "PortInFromExternalTelco";

    public static final String PORT_IN_TYPE_FROM_SINGTEL = "PortInFromSingtel";

    public static final String DONOR_OPERATOR_CODE = "donorOperatorCode";
    public static final String DONOR_OPERATOR_NAME = "donorOperatorName";
    public static final String DONOR_OPERATOR_ROUTE_NUMBER = "donorOperatorRouteNumber";
    public static final String DONOR_ENTITY_ID= "donorEntityId";
    
    public static final String OWNER_OPERATOR_CODE = "ownerOperatorCode";
    public static final String OWNER_OPERATOR_NAME = "ownerOperatorName";
    public static final String OWNER_OPERATOR_ROUTE_NUMBER = "ownerOperatorRouteNumber";

    public static final String RECIPIENT_OPERATOR_NAME = "recipientOperatorName";
    public static final String RECIPIENT_OPERATOR_ROUTE_NUMBER = "recipientOperatorRouteNumber";
    public static final String RECIPIENT_OPERATOR_CODE = "recipientOperatorCode";

    public static final String MESSAGE_RECEIVER_TELCO= "messageReceiverTelco";
    public static final String MESSAGE_SENDER_TELCO= "messageSenderTelco";
    public static final String MNP_OWNER_ID= "mnpOwnerId";
    public static final String MNP_OWNER_NAME= "mnpOwnerName";
    public static final String MNP_SERVICE_ID= "mnpServiceId";
    public static final String MNP_SERVICE_TYPE= "mnpServiceType";
    public static final String MNP_SERVICE_WITH_COUNTRY_CODE= "mnpServiceIdWithCC";
    
    public static final String MNP_DISCONNECT_SERVICE_INT_KEY= "disconnectServiceInternal";
    public static final String MNP_DISCONNECT_SERVICE_KEY= "disconnectService";
    public static final String MNP_CONFIRM_PORT_OUT_KEY= "confirmPortOut";

    public static final String MNP_LINE_TYPE = "lineType";
    public static final String MNP_REFERENCE_ID = "mnpReferenceId";
    public static final String MNP_REQUEST_ID = "mnpRequestId";
    public static final String MNP_CUSTOMER_NAME = "mnpCustomerName";
    public static final String MNP_ID_TYPE = "mnpIdType";
    public static final String MNP_ID_NUMBER = "mnpIdNumber";
    public static final String MNP_PORTING_TIME = "mnpPortinTimestamp";

    public static final String MNP_DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'+08:00'";
    public static final String MNP_TIMESTAMP = "mnpTimestamp";

    public static final String PORT_IN_NOTIFICATION_PNINV = "PNINV";
    public static final String PORT_IN_NOTIFICATION_PNVAL = "PNVAL";
    public static final String PORT_IN_NOTIFICATION_PNCON = "PNCON";
    public static final String PORT_IN_NOTIFICATION_PNREJ = "PNREJ";
    public static final String PORT_IN_NOTIFICATION_PNCAN = "PNCAN";
    public static final String PORT_IN_NOTIFICATION_PNCNT = "PNCNT";

    public static final String SDP_RESP_TRANSACTION_STATUS_CODE = "sdpAckStatusCode";
    public static final String SDP_RESP_TRANSACTION_STATUS_CODE_SUCCESS = "SUCCESS";

    public static final String SDP_RESP_TRANSACTION_STATUS_CODE_FAILURE = "FAILURE";
    public static final String MNP_PORTIN_MVNO_MVNO_PROV_HOUR = "0";
    public static final String MNP_PORTIN_MVNO_MVNO_DEPROV_HOUR = "23";
    public static final String MNP_PORTIN_INTERNAL_CHANGE_PRODUCT_HOUR = "23";
    public static final String MNP_PORTIN_INTERNAL_CONNECT_SERVICE_HOUR = "23";
    public static final String MNP_NOTIFICATION_STAGES = "PNVAL,PNCON,PNCNT,PNCAN,PNREJ,PNINV";
    public static final String MNP_SUCCESS_NOTIFICATION_STAGES = "PNCON,PNVAL";
    public static final String MNP_SUCCESS_STAGE_STATUS = "Completed";
    public static final String SUBMIT_PORT_IN_STAGES = "SUBMIT_PORTIN_INT_REQUEST,SUBMIT_PORTIN_REQUEST";
    public static final String MNP_CALLBACK_TYPE = "MNPCallBackType";
    public static final String MNP_CALLBACK_TYPE_DISCONNECT_INT = "disconnectServiceInternal";




    @Getter
    public enum PortInOrderStages {

        PNINV("PNINV"), PNCAN("PNCAN"), PNREJ(
                "PNREJ"), PNCON("PNCON"), PNCNT("PNCNT"),PNVAL("PNVAL"),SUBMIT_PORT_IN("SUBMIT_PORTIN_REQUEST"),
        SUBMIT_PORTIN_INT_REQUEST("SUBMIT_PORTIN_INT_REQUEST");

        private final String value;

        PortInOrderStages(String newValue) {
            value = newValue;
        }

    }


}
