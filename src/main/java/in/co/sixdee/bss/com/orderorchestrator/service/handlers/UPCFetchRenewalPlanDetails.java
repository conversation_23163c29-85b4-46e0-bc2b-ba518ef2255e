package in.co.sixdee.bss.com.orderorchestrator.service.handlers;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.common.util.NGTableConstants;
import in.co.sixdee.bss.om.model.dto.order.CFSCharacteristicRef;
import in.co.sixdee.bss.om.model.dto.order.CFSRef;
import in.co.sixdee.bss.om.model.dto.order.ProductOffering;
import in.co.sixdee.bss.om.model.dto.order.Subscription;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Component(value = "upcFetchRenewalPlanDetails")
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class UPCFetchRenewalPlanDetails extends AbstractDelegate {
	

	@Autowired
	private ObjectMapper objectMapper;

	@Override
	protected void execute() throws Exception {
		
		var callThirdPartyDTO = callThirdParty(null);
		if (callThirdPartyDTO == null) {
			executionContext.setError(true);
			return;
		}
		var upcResponse = callThirdPartyDTO.getResponse();
		validateResponse(callThirdPartyDTO);

		if (executionContext.isError()) {
			log.error("{} Response validation failed", activityId);
			return;
		}

		List<Subscription> smSubscriptions = objectMapper.convertValue(
				executionContext.getWorkflowData().get("renewalSubsList"), new TypeReference<List<Subscription>>() {
				});
		
		
		
		 if(OrderTypes.CREATE_SB_GROUP.equals(orderType))
			findDataPlansAndESBCallReqd(smSubscriptions, upcResponse);

		workflowDataUpdated = true;

	}

	private void findEsbCallRequired(List<Subscription> smSubscriptions,String response) {
		try {
			var productOfferings = objectMapper.readValue(response, new TypeReference<List<ProductOffering>>() {
			});
			for (Subscription smSub : smSubscriptions) {
				productOfferings.forEach(po -> {
					if (StringUtils.equals(smSub.getPlanId(), po.getId())) {
						var cfss = po.getProductSpecifications().stream().map(ps -> ps.getCfss()).findAny()
								.orElse(null);
						if (ObjectUtils.isNotEmpty(cfss)) {
							for (CFSRef cfs : cfss) {
								if (GenericConstants.CFS_M1_Base.equalsIgnoreCase(cfs.getName())) {
									smSub.setEsbCallRqd(true);
									break;
								}
							}
						}
					}
				});

			}
			
		} catch (Exception e) {
			log.error("Error in findEsbCallRequired ", e);
		}
		executionContext.getWorkflowData().put("renewalSubsList",smSubscriptions);
	}
	
	private void findDataPlansAndESBCallReqd(List<Subscription> smSubscriptions, String response) {
		var dataSubscriptions = new ArrayList<Subscription>();

		try {
			var productOfferings = objectMapper.readValue(response, new TypeReference<List<ProductOffering>>() {
			});

			var dataPlanServiceType = "3";

			var appConfig = cache.getCacheDetailsFromDBMap(NGTableConstants.CACHE_KEY_OM_APPLICATION_CONFIG,
					"UPC_DATA_PLAN_SERVICE_TYPE");
			if (appConfig != null)
				dataPlanServiceType = appConfig.getNgTableData().get("CONFIG_VALUE");

			for (Subscription smSub : smSubscriptions) {
				for (ProductOffering po : productOfferings) {
					if (StringUtils.equals(smSub.getPlanId(), po.getId())) {
						var cfss = po.getProductSpecifications().stream().map(ps -> ps.getCfss()).findAny()
								.orElse(null);
						if (ObjectUtils.isNotEmpty(cfss)) {
							for (CFSRef cfs : cfss) {
								if (GenericConstants.CFS_M1_Base.equalsIgnoreCase(cfs.getName())) {
									smSub.setEsbCallRqd(true);
								}
								 if (GenericConstants.CFS_UPC.equalsIgnoreCase(cfs.getName())) {
									if(ObjectUtils.isNotEmpty(cfs.getCharacteristics())) {
										for(CFSCharacteristicRef cfsChar: cfs.getCharacteristics()) {
											if("ServiceType".equalsIgnoreCase(cfsChar.getName()) && dataPlanServiceType.equals(cfsChar.getValue()))
													dataSubscriptions.add(smSub);
										}
									}
								}
							}
						}

					}
				}

			}

		} catch (Exception e) {
			log.error("Error in findDataPlansAndESBCallReqd ", e);
		}

		var isDataPlansPresent = ObjectUtils.isNotEmpty(dataSubscriptions);
		execution.setVariable("isDataPlansPresent", isDataPlansPresent);
		executionContext.getWorkflowData().put("renewalDataSubsList", dataSubscriptions);

	}

}
