package in.co.sixdee.bss.com.orderorchestrator.service.midelegates;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.ProcessDataAccessor;
import in.co.sixdee.bss.com.orderorchestrator.config.camunda.util.ProcessVariableUtils;
import in.co.sixdee.bss.com.orderorchestrator.config.exception.WorkflowTaskFailedException;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.CacheConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.GenericConstants;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.OrderTypes;
import in.co.sixdee.bss.com.orderorchestrator.config.util.constants.WorkFlowConstants;
import in.co.sixdee.bss.com.orderorchestrator.service.OrderPayloadService;
import in.co.sixdee.bss.com.orderorchestrator.service.ProcessBatchConfig;
import in.co.sixdee.bss.common.cache.CacheTableDataDTO;
import in.co.sixdee.bss.common.cache.GetDataFromCache;
import in.co.sixdee.bss.om.model.dto.OrderFlowContext;
import in.co.sixdee.bss.om.model.dto.order.ContactMedium;
import in.co.sixdee.bss.om.model.dto.order.OrderItem;
import in.co.sixdee.bss.om.model.dto.order.ServiceGroups;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.context.ProcessEngineContext;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.camunda.spin.plugin.variable.SpinValues;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;
import java.util.stream.Collectors;
@Service
@RequiredArgsConstructor
@Log4j2
public class CreateInstancesDelegateAddSubscription implements JavaDelegate {

    private final ProcessDataAccessor processDataAccessor;

    private final ProcessBatchConfig processBatchConfig;

    private final ObjectMapper objectMapper;

    private final GetDataFromCache cache;

    private final ProcessVariableUtils processVariableUtils;

    private final OrderPayloadService orderPayloadService;

    @Override
    public void execute(DelegateExecution execution) throws Exception {
        boolean isNullifySubscriptionParams;
        OrderFlowContext orderFlowContext = processDataAccessor.getOrderFlowContext(execution);
        var payloadOrderFlowContext = orderPayloadService.getOrderFlowContext(orderFlowContext.getOrder().getOrderId());
        if (payloadOrderFlowContext != null) {
            isNullifySubscriptionParams = true;
            if(OrderTypes.ADD_SUBSCRIPTION.equalsIgnoreCase(orderFlowContext.getOrder().getOrderType()))
                orderFlowContext.getOrder().setOrderItem(fetchOrderItem(orderFlowContext.getOrder().getOrderItem()));
                else {
                orderFlowContext.getOrder().setOrderItem(orderFlowContext.getOrder().getOrderItem());
            }
        } else {
            isNullifySubscriptionParams = false;
        }
       // setGroupParametersForEachService(orderFlowContext);
        List<String> subOrderIds = (List<String>) execution.getVariable("records");
        Integer createdInstancesCount = (Integer) execution.getVariable("createdInstancesCount");
        log.info("Created instances count: {} ", createdInstancesCount);
        Integer batchNumber = Math.min(subOrderIds.size() - createdInstancesCount, processBatchConfig.getBlockSize())
                + createdInstancesCount;
        String processDefinitionKey = (String) execution.getVariable("childProcess");
        IntStream.range(createdInstancesCount, batchNumber)
                .forEachOrdered(index -> createInstance(Long.parseLong(subOrderIds.get(index)), index, execution,
                        orderFlowContext, processDefinitionKey, isNullifySubscriptionParams));

        createdInstancesCount = (Integer) execution.getVariable("createdInstancesCount");

        log.info("Created instances count: {} ", createdInstancesCount);
        log.info("Batch number: {} ", batchNumber);

        execution.setVariable("allCreated", createdInstancesCount == subOrderIds.size());
    }

    private List<OrderItem> fetchOrderItem(List<OrderItem> orderItem) {
        return orderItem.stream().filter(item->(item.getSubscriptionAllowed() == null || item.getSubscriptionAllowed().isEmpty() || item.getSubscriptionAllowed().equalsIgnoreCase("true"))).collect(Collectors.toList());
    }

    private void createInstance(long subOrderId, int index, DelegateExecution execution, OrderFlowContext orderFlowContext,
                                String processDefinitionKey, boolean isNullifySubscriptionParams) {
        try {
        	
        	 var orderItem =  orderFlowContext.getOrder().getOrderItem().stream().filter(subs -> subOrderId == Long.parseLong(subs.getSubOrderId()))
                     .findFirst().orElse(null);
         /*   var service = orderFlowContext.getOrder().getServiceManagement().getServiceGroups().stream()
                    .flatMap(sg -> sg.getServices().stream().filter(serv -> subOrderId == Long.parseLong(serv.getSubOrderId())))
                    .findFirst().orElse(null);*/
            var subOrderContext = createSubOrderContext(orderFlowContext, orderItem, isNullifySubscriptionParams);
            // subOrderContext.getAttributes().put("processDefinitionKey", processDefinitionKey);
         //   Map<String, Object> variables = processVariableUtils.createSPProcessVariables(subOrderContext, orderItem);
            Map<String, Object> variables = new HashMap<String, Object>();
            try {
                var workflowData = SpinValues.jsonValue(objectMapper.writeValueAsString(subOrderContext)).create();
                variables.put("parentBusinessKey", execution.getBusinessKey());
              //  variables.put("isPrepaidAccount", "2".equals(orderFlowContext.getOrder().getProfile().getAccount().getChargingPattern()));
               // variables.put("isESIM", orderFlowContext.getOrder().getProfile().getAccount().getServiceGroups().get(0).getServices().get(index).getIsESim().equalsIgnoreCase("true"));
                variables.put("isPartOfMultiInstance", "true");
                variables.put("Status", "0");
                variables.put("ocsCallReqd",execution.getVariable("ocsCallReqd"));
                variables.put("priority", execution.getVariable("priority"));
                variables.put("isLegacy",execution.getVariable("isLegacy"));
                if (ObjectUtils.isNotEmpty(orderFlowContext.getOrder().getPayment()) &&
                        StringUtils.isNotEmpty(orderFlowContext.getOrder().getPayment().getUpfrontPayment()) &&
                        orderFlowContext.getOrder().getPayment().getUpfrontPayment().equalsIgnoreCase("true")) {
                	variables.put("paymentCallReqd", true);
                }else {
                	variables.put("paymentCallReqd", false);
                }
                variables.put("futureOrderRequired", findFutureOrderRequired(orderFlowContext.getOrder().getOrderType(), orderFlowContext.getEntityId()));
                if (OrderTypes.ADD_SUBSCRIPTION.equalsIgnoreCase(orderFlowContext.getOrder().getOrderType())) {
                    variables.put("somCallReqd", orderItem.isSomCallRqd());
                    log.info("som call required for add subscription {}", variables.get("somCallReqd"));
                }
                try {
                var itemchar = orderFlowContext.getOrder().getOrderItem().get(0).getItemCharacteristic().stream()
        	   			.filter(party -> StringUtils.isNotEmpty(party.getName())				
        	   			&& StringUtils.equalsIgnoreCase(party.getName(), "loyaltyRedemptionOffer")).findAny().orElse(null);
                if (itemchar != null && StringUtils.isNotEmpty(itemchar.getValue()))
                {
                	log.info("itemchar " + itemchar);
                	variables.put("loyaltyRedemptionOffer", "true".equals(itemchar.getValue()));
                }else {
                	variables.put("loyaltyRedemptionOffer", false);
                }
                }catch(Exception ex) {
                	log.info("item characteristic not present loyaltyRedemptionOffer");
                	variables.put("loyaltyRedemptionOffer", false);
                }
               // variables.put("erpDeposit", execution.getVariable("erpDeposit"));
               // variables.put("hasContract", ObjectUtils.isNotEmpty(service.getContract()));
              //  variables.put("isPrepaid", "2".equals(service.getChargingPattern()));
              //  variables.put("hasDeposit", ObjectUtils.isNotEmpty(service.getDeposit()));
                variables.put("eKycCall", "ekyc".equalsIgnoreCase(orderFlowContext.getChannel()));
                variables.put(WorkFlowConstants.WorkFlowProcessVariables.WORKFLOW_DATA.toString(), workflowData);
            } catch (Exception e) {
                log.error("Exception occurred while creating workflowData");
            }
            ProcessEngineContext.requiresNew();
            var deploymentId = execution.getProcessEngine().getRepositoryService()
                    .getProcessDefinition(execution.getProcessDefinitionId()).getDeploymentId();
            var processDefinition = execution.getProcessEngine().getRepositoryService().createProcessDefinitionQuery()
                    .deploymentId(deploymentId).processDefinitionKey(processDefinitionKey).list().get(0);
            execution.getProcessEngine().getRuntimeService().createProcessInstanceById(processDefinition.getId())
                    .setVariables(variables).businessKey(execution.getBusinessKey() + "_" + subOrderId).execute();
            execution.setVariable("createdInstancesCount", index + 1);
        } finally {
            ProcessEngineContext.clear();
        }
    }
    public boolean findFutureOrderRequired(String orderType,String entityId) {
        boolean futureOrderRequired = false;
        try {
            CacheTableDataDTO futureOrderConfig = cache.getCacheDetailsFromDBMap(CacheConstants.CacheKeys.COM_FUTURE_ORDER_CONFIG.name(), orderType + GenericConstants.OPERATOR_UNDERSCORE + entityId);
            if (futureOrderConfig != null) {
                futureOrderRequired = BooleanUtils.toBoolean(futureOrderConfig.getNgTableData().get(CacheConstants.CacheFields.FUTURE_ORDER_REQD.name()));
            }
        } catch (Exception e) {
            log.error("Exception occurred in findFutureOrderRequired", e);
        }
        return futureOrderRequired;
    }
    private void setGroupParametersForEachService(OrderFlowContext orderFlowContext) {
        for (ServiceGroups group : orderFlowContext.getOrder().getProfile().getAccount().getServiceGroups()) {
            if (ObjectUtils.isNotEmpty(group.getSubscriptions())) {
                group.getServices().forEach(service -> {
                    if (ObjectUtils.isNotEmpty(service.getSubscriptions())) {
                        service.getSubscriptions().addAll(group.getSubscriptions());
                    } else {
                        service.setSubscriptions(group.getSubscriptions());
                    }
                });
            }
            if (group.getPrimaryNumber() != null) {
                group.getServices().forEach(service -> {
                    service.setPrimaryNumber(group.getPrimaryNumber());
                });
            }
        }
    }

    private OrderFlowContext createSubOrderContext(OrderFlowContext orderFlowContext,
                                                   OrderItem orderItem, boolean isNullifySubscriptionParams) {
        OrderFlowContext subOrderContext = null;
        try {
            subOrderContext = objectMapper.readValue(objectMapper.writeValueAsString(orderFlowContext), OrderFlowContext.class);
        } catch (JsonProcessingException e) {
            log.error("Exception while creating sub order context ", e);
            throw new WorkflowTaskFailedException("CreateInstancesDelegateOnboarding", "COM-005", "Unable to generate sub order context");
        }
        subOrderContext.setRequestId(orderItem.getSubOrderId());
       // subOrderContext.getOrder().getProfile().getAccount().setServiceGroups(null);
        //subOrderContext.getOrder().setRelatedParty(null);
       // var profile = subOrderContext.getOrder().getProfile();
      /*  if (ObjectUtils.isNotEmpty(profile.getIndividualIdentification())) {
            var individualIdentification = profile.getIndividualIdentification();
            if (StringUtils.isNotEmpty(individualIdentification.get(0).getIdentificationId()))
                subOrderContext.getAttributes().put("identificationId", individualIdentification.get(0).getIdentificationId());
            if (StringUtils.isNotEmpty(individualIdentification.get(0).getIdentificationType()))
                subOrderContext.getAttributes().put("identificationType",
                        individualIdentification.get(0).getIdentificationType());
            if (ObjectUtils.isNotEmpty(individualIdentification.get(0).getIssuingDate()))
                subOrderContext.getAttributes().put("issuingDate", individualIdentification.get(0).getIssuingDate());
            if (ObjectUtils.isNotEmpty(individualIdentification.get(0).getValidFor())
                    && (ObjectUtils.isNotEmpty(individualIdentification.get(0).getValidFor().getEndDateTime())))
                subOrderContext.getAttributes().put("idExpiryDate",
                        individualIdentification.get(0).getValidFor().getEndDateTime());
            if (ObjectUtils.isNotEmpty(individualIdentification.get(0).getIsExpired()))
                subOrderContext.getAttributes().put("statusFlag",
                        individualIdentification.get(0).getIsExpired().equalsIgnoreCase("true") ? "1" : "0");
        }
        if (ObjectUtils.isNotEmpty(profile.getContactMedium())) {
            Iterator<ContactMedium> it = profile.getContactMedium().iterator();
            while (it.hasNext()) {
                var contactMedium = it.next();
                if (!"emailAddress".equalsIgnoreCase(contactMedium.getMediumType())) {
                    it.remove();
                }
            }
        }
        profile.setIndividualIdentification(null);
        profile.setAttachments(null);
        profile.setRelatedParty(null);
        profile.setLanguageAbility(null);
        subOrderContext.getOrder().getProfile().setAccount(null);*/
        subOrderContext.getAttributes().put(GenericConstants.SUB_ORDER_ID, orderItem.getSubOrderId());
      /*  if (isNullifySubscriptionParams) {
            service.getSubscriptions().forEach(subscription -> {
                subscription.setCharges(null);
                subscription.setCfss(null);
                subscription.setPrss(null);
                subscription.setLrss(null);
            });
        }*/
        var currentExecutionMap = new LinkedHashMap<String, Object>();
        try {
            currentExecutionMap.put("executionData",
                    com.bazaarvoice.jolt.JsonUtils.jsonToObject(objectMapper.writeValueAsString(orderItem)));
        } catch (JsonProcessingException e) {
            log.error("Exception occurred while creating executionData", e);
        }
        subOrderContext.getWorkflowData().put("currentExecution", currentExecutionMap);
        return subOrderContext;
    }
}
