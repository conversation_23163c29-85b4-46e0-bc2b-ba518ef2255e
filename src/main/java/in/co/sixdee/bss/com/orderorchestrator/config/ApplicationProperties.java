package in.co.sixdee.bss.com.orderorchestrator.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
@Setter
@ConfigurationProperties(ApplicationProperties.PREFIX)
public class ApplicationProperties {


	@Value("${spring.jpa.properties.hibernate.jdbc.batch_size}")
	private int jdbcBatchSize;

	public static final String PREFIX = "com.application";

	private boolean nativeQueryInsertion;

	@Value("${com.application.rabbit.failed-messages.max-attempts:3}")
	private long rabbitMaxRetries;

}
