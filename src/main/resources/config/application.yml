server:
  port: 8088
  servlet:
    context-path: /order-orchestrator
management:
  #  server:
  #    port: 8084
  metrics:
    tags:
      application: ${spring.application.name}-dev
  endpoints:
    web:
      exposure:
        include: metrics,prometheus,health
spring:
  application:
    name: order-orchestrator
    #jersey:
    #application-path: rest
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
  cloud:
    kubernetes:
      enabled: true
      discovery:
        all-namespaces: false
      reload:
        enabled: true
      secrets:
        name: com-dev-db-user-pass
    function:
      definition: receiveOrderMessages
    stream:
      rabbit:
        bindings:
          receiveOrderMessages-in-0:
            consumer:
              #              binding-routing-key: StarterPackProvisioning
              #              exchange-type: topic
              #              queue-name-group-only: true
              auto-bind-dlq: false
              dlq-ttl: 5000
              dlq-dead-letter-exchange:
      bindings:
        receiveOrderMessages-in-0:
          consumer:
            concurrency: 1
            max-attempts: 2
          destination: x.order-created
          group: q.order-created
  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
      indent-output: false
  datasource:
    url: *********************************************************************
    #driver-class-name: com.mysql.cj.jdbc.Driver
    #url: jdbc:mysql://**********:6446/WKN_COM_DEV?rewriteBatchedStatements=true
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: root
    #username: bssuser
    #password: bssuser@6Dtech
    hikari:
      maximum-pool-size: 20
      minimumIdle: 20
      auto-commit: false
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
        useLocalSessionState: true
        rewriteBatchedStatements: true
        cacheResultSetMetadata: true
        cacheServerConfiguration: true
        elideSetAutoCommits: true
        maintainTimeStats: false
  jpa:
    database-platform: org.hibernate.dialect.MySQLDialect
    generate-ddl: false
    properties:
      hibernate:
        connection:
          provider_disables_autocommit: true
        jdbc:
          batch_size: 30
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
          #session:
          #events:
          #log:
        #LOG_QUERIES_SLOWER_THAN_MS: 200
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
#camunda configuration
camunda:
  bpm:
    metrics:
      enabled: false
    admin-user:
      id: akhil
      password: akhil
      firstName: Akhil
    filter:
      create: All tasks
    history-level: none
    deployment-resource-pattern: classpath:/config/camunda/processes/*.bpmn
    job-execution:
        deployment-aware: true
        #backoff-time-in-millis: 200
        #max-backoff: 1000
        max-wait: 30000
        wait-time-in-millis: 5000
        queue-capacity: 500
        core-pool-size: 60
        maxPoolSize: 60
        max-jobs-per-acquisition: 100
    generic-properties:
      properties:
        jobExecutorAcquireByPriority: true
        jobExecutorPriorityRangeMin: 0
        jobExecutorPriorityRangeMax: 5
        failed-job-retry-time-cycle: R1/PT1M
        historyCleanupEnabled: true
        historyTimeToLive: P1D
        historyRemovalTimeStrategy: end
        sundayHistoryCleanupBatchWindowStartTime: 07:00
        sundayHistoryCleanupBatchWindowEndTime: 07:30
app-cache:
  reload-interval: 3000
app:
  logging:
    request:
      print-original: false

logging:
  level:
    root: INFO
    org:
      camunda:
        bpm:
          engine:
            jobexecutor: INFO
            cmd: INFO
            impl:
              persistence:
                entity:
                  JobEntity: INFO

    #    org:
    #     hibernate:
    #       SQL: DEBUG
    #       type: TRACE
    netty:
      http:
        client: DEBUG

flow:
  simulation: true
decorator:
  datasource:
    datasource-proxy:
      query.enable-logging: false
      query.log-level: info

#Retry CallBack Configuration
callback:
  audit:
    scheduler:

  retry:
    scheduler:
      enable: false
    max-retries-allowed: 5
    queue-watcher:
      min-queue-size-to-poll: 1
      polling-interval: 10000
      startup-delay: 5000
  executor:
    queue-capacity: 100
    core-pool-size: 5
    max-pool-size: 10
com:
  application:
    nativeQueryInsertion: true
cancelOrder:
  poller:
    enable: false
updateProfileNotificationEnabledTags: tinNumber
notificationEnableTags: contactEmailId
unique-number-generator:
  strategy: mysql
  mysql-table-name: COM_UNIQUE_NUMBER_SEQ
  retry-interval: 50
  max-retries: 5
CallBackExecutionListenerEnabled: true