{"definitions": {}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://example.com/object1641466665.json", "title": "Root", "type": "object", "required": ["orderId", "subOrderId", "stageId", "action"], "properties": {"orderId": {"$id": "#root/orderId", "title": "Orderid", "type": "string", "default": "", "examples": ["928589360439984128"], "pattern": "^.*$"}, "subOrderId": {"$id": "#root/subOrderId", "title": "Suborderid", "type": "string", "default": "", "examples": ["928589361320787968"], "pattern": "^.*$"}, "stageId": {"$id": "#root/stageId", "title": "Stageid", "type": "string", "default": "", "examples": ["SOM_ACTIVATE_SUBSCRIPTION"], "pattern": "^.*$"}, "action": {"$id": "#root/action", "title": "Action", "type": "string", "default": "", "examples": ["retry"], "pattern": "^.*$"}}}