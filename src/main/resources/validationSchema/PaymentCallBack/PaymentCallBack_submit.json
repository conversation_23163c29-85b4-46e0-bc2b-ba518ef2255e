{"definitions": {}, "$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://example.com/object1633607794.json", "title": "Root", "type": "object", "required": ["orderId", "externalId", "payment", "deposit"], "properties": {"orderId": {"$id": "#root/orderId", "title": "Orderid", "type": "string", "default": "", "examples": ["12332324234"], "pattern": "^.*$"}, "externalId": {"$id": "#root/externalId", "title": "<PERSON><PERSON>", "type": "string", "default": "", "examples": ["PO-456"], "pattern": "^.*$"}, "payment": {"$id": "#root/payment", "title": "Payment", "type": "object", "required": ["amount", "totalAmount", "collectionSourceType", "collectionId", "comment", "currencyCode", "transactionId", "paymentDetail"], "properties": {"accounId": {"$id": "#root/payment/accounId", "title": "Accounid", "type": "string", "default": "", "examples": ["35435435"], "pattern": "^.*$"}, "upfrontPayment": {"$id": "#root/payment/upfrontPayment", "title": "Upfrontpayment", "type": "string", "default": "", "examples": ["true"], "pattern": "^.*$"}, "isPaymentCollected": {"$id": "#root/payment/isPaymentCollected", "title": "Ispaymentcollected", "type": "string", "default": "", "examples": ["true"], "pattern": "^.*$"}, "amount": {"$id": "#root/payment/amount", "title": "Amount", "type": "string", "default": "", "examples": ["60.0"], "pattern": "^.*$"}, "totalAmount": {"$id": "#root/payment/totalAmount", "title": "Totalamount", "type": "string", "default": "", "examples": ["60.0"], "pattern": "^.*$"}, "collectionSourceType": {"$id": "#root/payment/collectionSourceType", "title": "Collectionsourcetype", "type": "string", "default": "", "examples": ["NORMAL"], "pattern": "^.*$"}, "collectionId": {"$id": "#root/payment/collectionId", "title": "Collectionid", "type": "string", "default": "", "examples": ["12"], "pattern": "^.*$"}, "comment": {"$id": "#root/payment/comment", "title": "Comment", "type": "string", "default": "", "examples": ["order payment"], "pattern": "^.*$"}, "currencyCode": {"$id": "#root/payment/currencyCode", "title": "Currencycode", "type": "string", "default": "", "examples": ["USD"], "pattern": "^.*$"}, "invoiceIds": {"$id": "#root/payment/invoiceIds", "title": "Invoiceids", "type": "string", "default": "", "examples": ["1001"], "pattern": "^.*$"}, "invoiceAmounts": {"$id": "#root/payment/invoiceAmounts", "title": "Invoiceamounts", "type": "string", "default": "", "examples": ["11"], "pattern": "^.*$"}, "collectionDate": {"$id": "#root/payment/collectionDate", "title": "Collectiondate", "type": "string", "default": "", "examples": ["2021-10-07T11:56:06.620Z"], "pattern": "^.*$"}, "transactionId": {"$id": "#root/payment/transactionId", "title": "Transactionid", "type": "string", "default": "", "examples": ["13213234234"], "pattern": "^.*$"}, "paymentDetail": {"$id": "#root/payment/paymentDetail", "title": "Paymentdetail", "type": "array", "default": [], "items": {"$id": "#root/payment/paymentDetail/items", "title": "Items", "type": "object", "required": ["paymentMode", "amountPaid"], "properties": {"paymentMode": {"$id": "#root/payment/paymentDetail/items/paymentMode", "title": "Paymentmode", "type": "string", "default": "", "examples": ["1"], "pattern": "^.*$"}, "referenceExternalId": {"$id": "#root/payment/paymentDetail/items/referenceExternalId", "title": "Referenceexternalid", "type": "string", "default": "", "examples": ["4604"], "pattern": "^.*$"}, "amountPaid": {"$id": "#root/payment/paymentDetail/items/amountPaid", "title": "Amountpaid", "type": "string", "default": "", "examples": ["11.0"], "pattern": "^.*$"}, "cardNumber": {"$id": "#root/payment/paymentDetail/items/cardNumber", "title": "Cardnumber", "type": "string", "default": "", "examples": ["4453"], "pattern": "^.*$"}, "chequeNo": {"$id": "#root/payment/paymentDetail/items/chequeNo", "title": "Chequeno", "type": "string", "default": "", "examples": ["1321323232"], "pattern": "^.*$"}, "ddNo": {"$id": "#root/payment/paymentDetail/items/ddNo", "title": "Ddno", "type": "string", "default": "", "examples": ["232324343434"], "pattern": "^.*$"}, "chequeDate": {"$id": "#root/payment/paymentDetail/items/chequeDate", "title": "Chequedate", "type": "string", "default": "", "examples": ["2021-10-07T11:56:06.620Z"], "pattern": "^.*$"}, "certificateNumber": {"$id": "#root/payment/paymentDetail/items/certificateNumber", "title": "Certificatenumber", "type": "string", "default": "", "examples": ["21424234234324"], "pattern": "^.*$"}}}}}}, "deposit": {"$id": "#root/deposit", "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "array", "default": [], "items": {"$id": "#root/deposit/items", "title": "Items", "type": "object", "required": ["serviceId", "bookingId", "status", "actualAmount", "depositAmount", "paymentDetails"], "properties": {"serviceId": {"$id": "#root/deposit/items/serviceId", "title": "Serviceid", "type": "string", "default": "", "examples": ["8765432186"], "pattern": "^.*$"}, "comment": {"$id": "#root/deposit/items/comment", "title": "Comment", "type": "string", "default": "", "examples": ["deposit"], "pattern": "^.*$"}, "currencyCode": {"$id": "#root/deposit/items/currencyCode", "title": "Currencycode", "type": "string", "default": "", "examples": ["USD"], "pattern": "^.*$"}, "bookingId": {"$id": "#root/deposit/items/bookingId", "title": "Bookingid", "type": "string", "default": "", "examples": ["1"], "pattern": "^.*$"}, "status": {"$id": "#root/deposit/items/status", "title": "Status", "type": "string", "default": "", "examples": ["1"], "pattern": "^.*$"}, "actualAmount": {"$id": "#root/deposit/items/actualAmount", "title": "Actualamount", "type": "string", "default": "", "examples": ["50.00"], "pattern": "^.*$"}, "depositAmount": {"$id": "#root/deposit/items/depositAmount", "title": "Depositamount", "type": "string", "default": "", "examples": ["30.00"], "pattern": "^.*$"}, "paymentDetails": {"$id": "#root/deposit/items/paymentDetails", "title": "Paymentdetails", "type": "array", "default": [], "items": {"$id": "#root/deposit/items/paymentDetails/items", "title": "Items", "type": "object", "required": ["paymentMode"], "properties": {"paymentMode": {"$id": "#root/deposit/items/paymentDetails/items/paymentMode", "title": "Paymentmode", "type": "string", "default": "", "examples": ["1"], "pattern": "^.*$"}}}}}}}}}