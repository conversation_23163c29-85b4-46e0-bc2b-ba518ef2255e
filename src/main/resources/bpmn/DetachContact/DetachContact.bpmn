<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1lsckuo" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="DetachContact" name="DetachContact" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1ybz3m3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1ybz3m3" sourceRef="orderExecStart" targetRef="BSDetachContact" />
    <bpmn:sequenceFlow id="Flow_160y6h2" sourceRef="BSDetachContact" targetRef="orderExecEnd" />
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_160y6h2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSDetachContact" name="Billing Detach Contact" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1ybz3m3</bpmn:incoming>
      <bpmn:outgoing>Flow_160y6h2</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="DetachContact">
      <bpmndi:BPMNEdge id="Flow_160y6h2_di" bpmnElement="Flow_160y6h2">
        <di:waypoint x="400" y="117" />
        <di:waypoint x="482" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ybz3m3_di" bpmnElement="Flow_1ybz3m3">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="300" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="179" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0hdm895_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="482" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0oe96hx_di" bpmnElement="BSDetachContact">
        <dc:Bounds x="300" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
