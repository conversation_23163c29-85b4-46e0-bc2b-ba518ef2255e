<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0gb1dy0" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="UpdateBucket" isExecutable="true" camunda:jobPriority="${priority}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0o0h2ih</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0o0h2ih" sourceRef="orderExecStart" targetRef="NCCUpdateBucket" />
    <bpmn:serviceTask id="NCCUpdateBucket" name="Update Bucket in NCC" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0o0h2ih</bpmn:incoming>
      <bpmn:outgoing>Flow_1lez9bu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1lez9bu</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1lez9bu" sourceRef="NCCUpdateBucket" targetRef="orderExecEnd" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="UpdateBucket">
      <bpmndi:BPMNEdge id="Flow_1lez9bu_di" bpmnElement="Flow_1lez9bu">
        <di:waypoint x="360" y="117" />
        <di:waypoint x="442" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o0h2ih_di" bpmnElement="Flow_0o0h2ih">
        <di:waypoint x="188" y="117" />
        <di:waypoint x="260" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0kiechg_di" bpmnElement="NCCUpdateBucket">
        <dc:Bounds x="260" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_04vkznn_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="442" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
