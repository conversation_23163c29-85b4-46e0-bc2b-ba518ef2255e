<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_18pr9mp" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.5.1">
  <bpmn:process id="ConnectionMigration" name="ConnectionMigration" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:sequenceFlow id="Flow_0jbov6p" name="Success" sourceRef="Gateway_0dobmvn" targetRef="Gateway_0qzc76r" />
    <bpmn:sequenceFlow id="Flow_1ynl656" name="Failure" sourceRef="Gateway_0dobmvn" targetRef="Event_0fmmmpo">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1hdsvt7" sourceRef="CheckEligibilityCriteria" targetRef="Gateway_0dobmvn" />
    <bpmn:sequenceFlow id="Flow_07e3hxg" name="Failure" sourceRef="Gateway_0im6ftz" targetRef="Event_0xcyvwl">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0dobmvn" default="Flow_0jbov6p">
      <bpmn:incoming>Flow_1hdsvt7</bpmn:incoming>
      <bpmn:outgoing>Flow_1ynl656</bpmn:outgoing>
      <bpmn:outgoing>Flow_0jbov6p</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0xcyvwl">
      <bpmn:incoming>Flow_07e3hxg</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_0fmmmpo">
      <bpmn:incoming>Flow_1ynl656</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="CheckEligibilityCriteria" name="Check Eligibility Criteria" camunda:asyncBefore="true" camunda:delegateExpression="${planComparision}">
      <bpmn:incoming>Flow_056nn0p</bpmn:incoming>
      <bpmn:outgoing>Flow_1hdsvt7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0im6ftz" default="Flow_1nxn8n6">
      <bpmn:incoming>Flow_00zuoss</bpmn:incoming>
      <bpmn:outgoing>Flow_07e3hxg</bpmn:outgoing>
      <bpmn:outgoing>Flow_1nxn8n6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_15fcocz</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="NCCConnectionMigration" name="Connection Migration in NCC" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1yg9x7l</bpmn:incoming>
      <bpmn:outgoing>Flow_00zuoss</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_00zuoss" sourceRef="NCCConnectionMigration" targetRef="Gateway_0im6ftz" />
    <bpmn:exclusiveGateway id="Gateway_1o7in37" default="Flow_0mndqqx">
      <bpmn:incoming>Flow_1o58x6u</bpmn:incoming>
      <bpmn:outgoing>Flow_0n8qv44</bpmn:outgoing>
      <bpmn:outgoing>Flow_0mndqqx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_133liv9">
      <bpmn:incoming>Flow_0n8qv44</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="NCCCreateSubscription" name="Subsciption Activation in NCC" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0qoyks8</bpmn:incoming>
      <bpmn:outgoing>Flow_1o58x6u</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1hxzn46" name="Ncc specific new addons are present" default="Flow_0bcs3d7">
      <bpmn:incoming>Flow_0ip5wxn</bpmn:incoming>
      <bpmn:outgoing>Flow_0qoyks8</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bcs3d7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0d0gr9y">
      <bpmn:incoming>Flow_0mndqqx</bpmn:incoming>
      <bpmn:incoming>Flow_0bcs3d7</bpmn:incoming>
      <bpmn:outgoing>Flow_1b62dhz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1o58x6u" sourceRef="NCCCreateSubscription" targetRef="Gateway_1o7in37" />
    <bpmn:sequenceFlow id="Flow_0n8qv44" name="Failure" sourceRef="Gateway_1o7in37" targetRef="Event_133liv9">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0mndqqx" name="Success" sourceRef="Gateway_1o7in37" targetRef="Gateway_0d0gr9y" />
    <bpmn:sequenceFlow id="Flow_0qoyks8" name="yes" sourceRef="Gateway_1hxzn46" targetRef="NCCCreateSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${nccAddSub}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0bcs3d7" sourceRef="Gateway_1hxzn46" targetRef="Gateway_0d0gr9y" />
    <bpmn:serviceTask id="BSConnectionMigration" name="Billing connectionMigration" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0973vev</bpmn:incoming>
      <bpmn:outgoing>Flow_0nvtktl</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSFetchContract" name="Billing Fetch Contract" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0h7mknn</bpmn:incoming>
      <bpmn:outgoing>Flow_0zvb8pf</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0d9wdga">
      <bpmn:incoming>Flow_003kweh</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0puqsoc">
      <bpmn:incoming>Flow_1p3wqyd</bpmn:incoming>
      <bpmn:incoming>Flow_1ja2znm</bpmn:incoming>
      <bpmn:outgoing>Flow_04elrz6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1cc6nnf" default="Flow_1p3wqyd">
      <bpmn:incoming>Flow_1y9cxw0</bpmn:incoming>
      <bpmn:outgoing>Flow_003kweh</bpmn:outgoing>
      <bpmn:outgoing>Flow_1p3wqyd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="BSCancelContract" name="Billing Cancel Contract" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_010cjgt</bpmn:incoming>
      <bpmn:outgoing>Flow_1y9cxw0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_04fqf8n" name="check if contracts exists for old base plan" default="Flow_1ja2znm">
      <bpmn:incoming>Flow_1pi0c86</bpmn:incoming>
      <bpmn:outgoing>Flow_1ja2znm</bpmn:outgoing>
      <bpmn:outgoing>Flow_010cjgt</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0efnn1s" default="Flow_1pi0c86">
      <bpmn:incoming>Flow_0zvb8pf</bpmn:incoming>
      <bpmn:outgoing>Flow_1pi0c86</bpmn:outgoing>
      <bpmn:outgoing>Flow_0xapoyt</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1ptcyvw">
      <bpmn:incoming>Flow_0xapoyt</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_003kweh" name="Failure" sourceRef="Gateway_1cc6nnf" targetRef="Event_0d9wdga">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1p3wqyd" name="Success" sourceRef="Gateway_1cc6nnf" targetRef="Gateway_0puqsoc" />
    <bpmn:sequenceFlow id="Flow_1ja2znm" name="no active contracts" sourceRef="Gateway_04fqf8n" targetRef="Gateway_0puqsoc" />
    <bpmn:sequenceFlow id="Flow_1y9cxw0" sourceRef="BSCancelContract" targetRef="Gateway_1cc6nnf" />
    <bpmn:sequenceFlow id="Flow_010cjgt" name="yes" sourceRef="Gateway_04fqf8n" targetRef="BSCancelContract">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.workflowData").element().hasProp('BSFetchContractResponseAttributes') &amp;&amp;  workflowData.jsonPath("$.workflowData.BSFetchContractResponseAttributes").element().hasProp('contract')}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1pi0c86" name="Success" sourceRef="Gateway_0efnn1s" targetRef="Gateway_04fqf8n" />
    <bpmn:sequenceFlow id="Flow_0xapoyt" name="Failure" sourceRef="Gateway_0efnn1s" targetRef="Event_1ptcyvw">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSCreateContract" name="Billing Create Contract" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0whuv2u</bpmn:incoming>
      <bpmn:outgoing>Flow_0wumaza</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1872b0n" default="Flow_0imyhsl">
      <bpmn:incoming>Flow_0wumaza</bpmn:incoming>
      <bpmn:outgoing>Flow_0imyhsl</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ghk51n</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0h9ns6e" name="check if contract exists for new base plan" default="Flow_0z316eb">
      <bpmn:incoming>Flow_1gi67jd</bpmn:incoming>
      <bpmn:outgoing>Flow_0whuv2u</bpmn:outgoing>
      <bpmn:outgoing>Flow_0z316eb</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1uha48z">
      <bpmn:incoming>Flow_0imyhsl</bpmn:incoming>
      <bpmn:incoming>Flow_0z316eb</bpmn:incoming>
      <bpmn:outgoing>Flow_0x0smh8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1egxgd5">
      <bpmn:incoming>Flow_1ghk51n</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0whuv2u" name="yes" sourceRef="Gateway_0h9ns6e" targetRef="BSCreateContract">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${hasContract}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0imyhsl" name="Success" sourceRef="Gateway_1872b0n" targetRef="Gateway_1uha48z" />
    <bpmn:sequenceFlow id="Flow_1ghk51n" name="Failure" sourceRef="Gateway_1872b0n" targetRef="Event_1egxgd5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0z316eb" name="no" sourceRef="Gateway_0h9ns6e" targetRef="Gateway_1uha48z" />
    <bpmn:serviceTask id="LMSConnectionMigration" name="Connection Migration in LMS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1ip5169</bpmn:incoming>
      <bpmn:outgoing>Flow_0ws81qh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSActivateService" name="Service Activation in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0ribmwf</bpmn:incoming>
      <bpmn:outgoing>Flow_0pobzk0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1ijf1pi" default="Flow_0jo15c7">
      <bpmn:incoming>Flow_0pobzk0</bpmn:incoming>
      <bpmn:outgoing>Flow_12r6qjf</bpmn:outgoing>
      <bpmn:outgoing>Flow_0jo15c7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_07tm15o">
      <bpmn:incoming>Flow_12r6qjf</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="USSDUpdateCustomer" name="USSD Update customer details" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0en120i</bpmn:incoming>
      <bpmn:outgoing>Flow_1sghn9v</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1sghn9v</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_13fsb0v" default="Flow_0en120i">
      <bpmn:incoming>Flow_0ws81qh</bpmn:incoming>
      <bpmn:outgoing>Flow_0en120i</bpmn:outgoing>
      <bpmn:outgoing>Flow_0mmxl51</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ws81qh" sourceRef="LMSConnectionMigration" targetRef="Gateway_13fsb0v" />
    <bpmn:sequenceFlow id="Flow_0pobzk0" sourceRef="BSActivateService" targetRef="Gateway_1ijf1pi" />
    <bpmn:sequenceFlow id="Flow_12r6qjf" name="Failure" sourceRef="Gateway_1ijf1pi" targetRef="Event_07tm15o">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0en120i" name="Success" sourceRef="Gateway_13fsb0v" targetRef="USSDUpdateCustomer" />
    <bpmn:sequenceFlow id="Flow_0jo15c7" name="Success" sourceRef="Gateway_1ijf1pi" targetRef="Gateway_02dl7c9" />
    <bpmn:endEvent id="Event_0e9l5j3">
      <bpmn:incoming>Flow_0mmxl51</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0mmxl51" name="Failure" sourceRef="Gateway_13fsb0v" targetRef="Event_0e9l5j3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_15fcocz" sourceRef="orderExecStart" targetRef="OrderEnrichment" />
    <bpmn:serviceTask id="OrderEnrichment" name="Order Enrichment" camunda:asyncBefore="true" camunda:delegateExpression="${orderEnrichmentDelegate}">
      <bpmn:incoming>Flow_15fcocz</bpmn:incoming>
      <bpmn:outgoing>Flow_1wad9gc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1wad9gc" sourceRef="OrderEnrichment" targetRef="Gateway_1qv0h9y" />
    <bpmn:exclusiveGateway id="Gateway_1qv0h9y" default="Flow_056nn0p">
      <bpmn:incoming>Flow_1wad9gc</bpmn:incoming>
      <bpmn:outgoing>Flow_07h6ess</bpmn:outgoing>
      <bpmn:outgoing>Flow_056nn0p</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0ybupuw">
      <bpmn:incoming>Flow_07h6ess</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_07h6ess" name="Failure" sourceRef="Gateway_1qv0h9y" targetRef="Event_0ybupuw">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0660bjz" name="check if account details are present or not" default="Flow_1ooik7v">
      <bpmn:incoming>Flow_0mlmz98</bpmn:incoming>
      <bpmn:outgoing>Flow_1ooik7v</bpmn:outgoing>
      <bpmn:outgoing>Flow_0xateuc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_09p2aic">
      <bpmn:incoming>SequenceFlow_0yc8zdo</bpmn:incoming>
      <bpmn:incoming>Flow_1a5bx3l</bpmn:incoming>
      <bpmn:outgoing>Flow_1yg9x7l</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1cyeg3a">
      <bpmn:incoming>Flow_08hfui2</bpmn:incoming>
      <bpmn:incoming>Flow_0aw53gz</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0osryl2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_05mftie" name="check if sndWorkflowTrigger is required or not" default="Flow_08hfui2">
      <bpmn:incoming>SequenceFlow_1e9ieh8</bpmn:incoming>
      <bpmn:outgoing>Flow_08hfui2</bpmn:outgoing>
      <bpmn:outgoing>Flow_1lc6dcx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1ety2ln">
      <bpmn:incoming>Flow_14j83he</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_02wjzys" default="Flow_14j83he">
      <bpmn:incoming>Flow_1g0ro5n</bpmn:incoming>
      <bpmn:outgoing>Flow_0aw53gz</bpmn:outgoing>
      <bpmn:outgoing>Flow_14j83he</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="BSCreateAccount" name="Create Billing Account" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0xateuc</bpmn:incoming>
      <bpmn:outgoing>Flow_1kcjs3m</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0lnrtsq">
      <bpmn:incoming>Flow_14dpjpm</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_046u6f1" default="Flow_1qcts75">
      <bpmn:incoming>Flow_1kcjs3m</bpmn:incoming>
      <bpmn:outgoing>Flow_14dpjpm</bpmn:outgoing>
      <bpmn:outgoing>Flow_1qcts75</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:callActivity id="PaymentWorkflow" name="Payment Workflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow">
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:out variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1lc6dcx</bpmn:incoming>
      <bpmn:outgoing>Flow_1g0ro5n</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:serviceTask id="ERPCreateAccount" name="ERP Create Account" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1163n22</bpmn:incoming>
      <bpmn:outgoing>Flow_1yydgpz</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1uxuslt" default="Flow_1a5bx3l">
      <bpmn:incoming>Flow_1yydgpz</bpmn:incoming>
      <bpmn:outgoing>Flow_1a5bx3l</bpmn:outgoing>
      <bpmn:outgoing>Flow_04n99hf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0kzckum">
      <bpmn:incoming>Flow_04n99hf</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="ExclusiveGateway_186wb1c">
      <bpmn:incoming>Flow_1ooik7v</bpmn:incoming>
      <bpmn:incoming>Flow_1qcts75</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1e9ieh8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0z4ojqa" name="connection type is postpaid and new account creation" default="SequenceFlow_0yc8zdo">
      <bpmn:incoming>SequenceFlow_0osryl2</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0yc8zdo</bpmn:outgoing>
      <bpmn:outgoing>Flow_1163n22</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1a5bx3l" name="Success" sourceRef="Gateway_1uxuslt" targetRef="Gateway_09p2aic" />
    <bpmn:sequenceFlow id="Flow_1ooik7v" name="existing account" sourceRef="Gateway_0660bjz" targetRef="ExclusiveGateway_186wb1c" />
    <bpmn:sequenceFlow id="Flow_0xateuc" name="new account" sourceRef="Gateway_0660bjz" targetRef="BSCreateAccount">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isNewAccount}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0yc8zdo" name="existing account" sourceRef="ExclusiveGateway_0z4ojqa" targetRef="Gateway_09p2aic" />
    <bpmn:sequenceFlow id="Flow_08hfui2" name="no" sourceRef="Gateway_05mftie" targetRef="Gateway_1cyeg3a" />
    <bpmn:sequenceFlow id="Flow_0aw53gz" name="Success" sourceRef="Gateway_02wjzys" targetRef="Gateway_1cyeg3a">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0osryl2" sourceRef="Gateway_1cyeg3a" targetRef="ExclusiveGateway_0z4ojqa" />
    <bpmn:sequenceFlow id="SequenceFlow_1e9ieh8" sourceRef="ExclusiveGateway_186wb1c" targetRef="Gateway_05mftie" />
    <bpmn:sequenceFlow id="Flow_1lc6dcx" name="yes" sourceRef="Gateway_05mftie" targetRef="PaymentWorkflow">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${sndWorkflowTrigger}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_14j83he" name="Failure" sourceRef="Gateway_02wjzys" targetRef="Event_1ety2ln" />
    <bpmn:sequenceFlow id="Flow_1g0ro5n" sourceRef="PaymentWorkflow" targetRef="Gateway_02wjzys" />
    <bpmn:sequenceFlow id="Flow_1kcjs3m" sourceRef="BSCreateAccount" targetRef="Gateway_046u6f1" />
    <bpmn:sequenceFlow id="Flow_14dpjpm" name="Failure" sourceRef="Gateway_046u6f1" targetRef="Event_0lnrtsq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1qcts75" name="Success" sourceRef="Gateway_046u6f1" targetRef="ExclusiveGateway_186wb1c" />
    <bpmn:sequenceFlow id="Flow_1yydgpz" sourceRef="ERPCreateAccount" targetRef="Gateway_1uxuslt" />
    <bpmn:sequenceFlow id="Flow_04n99hf" name="Failure" sourceRef="Gateway_1uxuslt" targetRef="Event_0kzckum">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1163n22" sourceRef="ExclusiveGateway_0z4ojqa" targetRef="ERPCreateAccount">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${erpAccountCreation}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1a4bp93" name="check if destination connection type is" default="Flow_13z2xbt">
      <bpmn:incoming>Flow_0x0smh8</bpmn:incoming>
      <bpmn:outgoing>Flow_06ju4qo</bpmn:outgoing>
      <bpmn:outgoing>Flow_13z2xbt</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="BSCreditAdjustment" name="Credit Adjustment in billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_06ju4qo</bpmn:incoming>
      <bpmn:outgoing>Flow_1x2ukfx</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0iwvnp5" default="Flow_1spwxrz">
      <bpmn:incoming>Flow_1x2ukfx</bpmn:incoming>
      <bpmn:outgoing>Flow_1spwxrz</bpmn:outgoing>
      <bpmn:outgoing>Flow_0mcpsgv</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1vwh6iz">
      <bpmn:incoming>Flow_13z2xbt</bpmn:incoming>
      <bpmn:incoming>Flow_1spwxrz</bpmn:incoming>
      <bpmn:outgoing>Flow_0ribmwf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1pwcgtn">
      <bpmn:incoming>Flow_0mcpsgv</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_06ju4qo" name="postpaid" sourceRef="Gateway_1a4bp93" targetRef="BSCreditAdjustment">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isPostpaidMigration&amp;&amp; workflowData.jsonPath("$.workflowData").element().hasProp('NCCConnectionMigrationResponseAttributes') &amp;&amp; workflowData.jsonPath("$.workflowData.NCCConnectionMigrationResponseAttributes").element().hasProp('MainAccountBalance') &amp;&amp; workflowData.jsonPath("$.workflowData.NCCConnectionMigrationResponseAttributes.MainAccountBalance").numberValue() &gt; 0}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_13z2xbt" name="prepaid" sourceRef="Gateway_1a4bp93" targetRef="Gateway_1vwh6iz" />
    <bpmn:sequenceFlow id="Flow_1x2ukfx" sourceRef="BSCreditAdjustment" targetRef="Gateway_0iwvnp5" />
    <bpmn:sequenceFlow id="Flow_1spwxrz" name="Success" sourceRef="Gateway_0iwvnp5" targetRef="Gateway_1vwh6iz" />
    <bpmn:sequenceFlow id="Flow_0mcpsgv" name="Failure" sourceRef="Gateway_0iwvnp5" targetRef="Event_1pwcgtn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0x0smh8" sourceRef="Gateway_1uha48z" targetRef="Gateway_1a4bp93" />
    <bpmn:serviceTask id="BSBookDeposit" name="Book Deposit in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0y3ni4e</bpmn:incoming>
      <bpmn:outgoing>Flow_048dwd7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1srjb5t" default="Flow_1753sm0">
      <bpmn:incoming>Flow_048dwd7</bpmn:incoming>
      <bpmn:outgoing>Flow_1753sm0</bpmn:outgoing>
      <bpmn:outgoing>Flow_0kfzzdd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0be2qxg">
      <bpmn:incoming>Flow_0kfzzdd</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1bh2u35" default="Flow_02oqrsw">
      <bpmn:incoming>Flow_16rchyl</bpmn:incoming>
      <bpmn:outgoing>Flow_02oqrsw</bpmn:outgoing>
      <bpmn:outgoing>Flow_0f45zn7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0wdy143">
      <bpmn:incoming>Flow_0f45zn7</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0fcssqw">
      <bpmn:incoming>Flow_0zzgqoe</bpmn:incoming>
      <bpmn:incoming>Flow_1cltfre</bpmn:incoming>
      <bpmn:outgoing>Flow_1ip5169</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_02dl7c9" name="check if deposit details are present or not" default="Flow_0zzgqoe">
      <bpmn:incoming>Flow_0jo15c7</bpmn:incoming>
      <bpmn:outgoing>Flow_0y3ni4e</bpmn:outgoing>
      <bpmn:outgoing>Flow_0zzgqoe</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1igl22o" name="check if the channel is configured for the ERPDeposit" default="Flow_0bl1xjk">
      <bpmn:incoming>Flow_1753sm0</bpmn:incoming>
      <bpmn:outgoing>Flow_13nxt9u</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bl1xjk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1iivd2w">
      <bpmn:incoming>Flow_02oqrsw</bpmn:incoming>
      <bpmn:incoming>Flow_0bl1xjk</bpmn:incoming>
      <bpmn:outgoing>Flow_1cltfre</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0y3ni4e" name="yes" sourceRef="Gateway_02dl7c9" targetRef="BSBookDeposit">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${bookDeposit}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_048dwd7" sourceRef="BSBookDeposit" targetRef="Gateway_1srjb5t" />
    <bpmn:sequenceFlow id="Flow_1753sm0" name="Success" sourceRef="Gateway_1srjb5t" targetRef="Gateway_1igl22o" />
    <bpmn:sequenceFlow id="Flow_0kfzzdd" name="Failure" sourceRef="Gateway_1srjb5t" targetRef="Event_0be2qxg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_13nxt9u" name="yes" sourceRef="Gateway_1igl22o" targetRef="ERPDeposit">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${erpDeposit}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_16rchyl" sourceRef="ERPDeposit" targetRef="Gateway_1bh2u35" />
    <bpmn:sequenceFlow id="Flow_02oqrsw" name="Success" sourceRef="Gateway_1bh2u35" targetRef="Gateway_1iivd2w" />
    <bpmn:sequenceFlow id="Flow_0f45zn7" name="Failure" sourceRef="Gateway_1bh2u35" targetRef="Event_0wdy143">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0zzgqoe" name="no" sourceRef="Gateway_02dl7c9" targetRef="Gateway_0fcssqw" />
    <bpmn:sequenceFlow id="Flow_1cltfre" sourceRef="Gateway_1iivd2w" targetRef="Gateway_0fcssqw" />
    <bpmn:sequenceFlow id="Flow_0bl1xjk" name="no" sourceRef="Gateway_1igl22o" targetRef="Gateway_1iivd2w" />
    <bpmn:serviceTask id="ERPDeposit" name="Deposit in ERP" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_13nxt9u</bpmn:incoming>
      <bpmn:outgoing>Flow_16rchyl</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1ip5169" sourceRef="Gateway_0fcssqw" targetRef="LMSConnectionMigration" />
    <bpmn:sequenceFlow id="Flow_1yg9x7l" sourceRef="Gateway_09p2aic" targetRef="NCCConnectionMigration" />
    <bpmn:serviceTask id="SOMDeleteService" name="SOM Delete Service" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1hy5d5x</bpmn:incoming>
      <bpmn:outgoing>Flow_0j3x7n8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:receiveTask id="SOMConMigDeleteCallback" name="SOM Delete Callback Wait Signal" messageRef="Message_1m2g4r3">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOMDeleteService</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOMChangePlan" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1c5budh</bpmn:incoming>
      <bpmn:outgoing>Flow_09jvk06</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_0v6zyru" default="Flow_1c5budh">
      <bpmn:incoming>Flow_0j3x7n8</bpmn:incoming>
      <bpmn:outgoing>Flow_1c5budh</bpmn:outgoing>
      <bpmn:outgoing>Flow_0zq7p5m</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0hcsxt6">
      <bpmn:incoming>Flow_0zq7p5m</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1h2noc9" default="Flow_00mrgtj">
      <bpmn:incoming>Flow_0z7u7qd</bpmn:incoming>
      <bpmn:outgoing>Flow_00mrgtj</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ribv5t</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_03yojyd">
      <bpmn:incoming>Flow_0ribv5t</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="SOMCreateService" name="SOM Create Service" camunda:asyncBefore="true" camunda:delegateExpression="${somCreateService}">
      <bpmn:incoming>Flow_11s9dz5</bpmn:incoming>
      <bpmn:outgoing>Flow_0z7u7qd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:receiveTask id="SOMConMigCreateCallback" name="SOM Create Callback Wait Signal" messageRef="Message_0feghnz">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOMCreateService</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOMCreateService" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_00mrgtj</bpmn:incoming>
      <bpmn:outgoing>Flow_1qpmdtk</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch Service " camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0w5b2bh</bpmn:incoming>
      <bpmn:outgoing>Flow_1eycpfy</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1fe7uc3" default="Flow_1hy5d5x">
      <bpmn:incoming>Flow_1eycpfy</bpmn:incoming>
      <bpmn:outgoing>Flow_1hy5d5x</bpmn:outgoing>
      <bpmn:outgoing>Flow_1oz79jz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1x6pmfc">
      <bpmn:incoming>Flow_1oz79jz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1hy5d5x" name="Success" sourceRef="Gateway_1fe7uc3" targetRef="SOMDeleteService" />
    <bpmn:sequenceFlow id="Flow_0j3x7n8" sourceRef="SOMDeleteService" targetRef="Gateway_0v6zyru" />
    <bpmn:sequenceFlow id="Flow_1c5budh" name="Success" sourceRef="Gateway_0v6zyru" targetRef="SOMConMigDeleteCallback" />
    <bpmn:sequenceFlow id="Flow_0zq7p5m" name="Failure" sourceRef="Gateway_0v6zyru" targetRef="Event_0hcsxt6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0z7u7qd" sourceRef="SOMCreateService" targetRef="Gateway_1h2noc9" />
    <bpmn:sequenceFlow id="Flow_00mrgtj" name="Success" sourceRef="Gateway_1h2noc9" targetRef="SOMConMigCreateCallback" />
    <bpmn:sequenceFlow id="Flow_0ribv5t" name="Failure" sourceRef="Gateway_1h2noc9" targetRef="Event_03yojyd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1eycpfy" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_1fe7uc3" />
    <bpmn:sequenceFlow id="Flow_1oz79jz" name="Failure" sourceRef="Gateway_1fe7uc3" targetRef="Event_1x6pmfc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1o9oxjw" name="if non retainable subscriptions are present" default="Flow_189v49n">
      <bpmn:incoming>Flow_142m60g</bpmn:incoming>
      <bpmn:outgoing>Flow_1j9aoka</bpmn:outgoing>
      <bpmn:outgoing>Flow_189v49n</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_140rtjb" default="Flow_0s2eh1f">
      <bpmn:incoming>Flow_0qwewlg</bpmn:incoming>
      <bpmn:outgoing>Flow_0s2eh1f</bpmn:outgoing>
      <bpmn:outgoing>Flow_1tmuop9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0m0292x">
      <bpmn:incoming>Flow_189v49n</bpmn:incoming>
      <bpmn:incoming>Flow_0s2eh1f</bpmn:incoming>
      <bpmn:outgoing>Flow_13p0evq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1nvh757">
      <bpmn:incoming>Flow_1tmuop9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1ojpmld" default="Flow_0wuf5ha">
      <bpmn:incoming>Flow_1ues1ex</bpmn:incoming>
      <bpmn:outgoing>Flow_1jc63i1</bpmn:outgoing>
      <bpmn:outgoing>Flow_0wuf5ha</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1y87bxh">
      <bpmn:incoming>Flow_1jc63i1</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1p7bl8d" default="Flow_142m60g">
      <bpmn:incoming>Flow_0417ter</bpmn:incoming>
      <bpmn:outgoing>Flow_142m60g</bpmn:outgoing>
      <bpmn:outgoing>Flow_0iqj1q3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_07yvbsn">
      <bpmn:incoming>Flow_0iqj1q3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:callActivity id="SMOfferActivationProcess" name="Subscription creation in SM" camunda:asyncBefore="true" calledElement="SMOfferActivationProcess" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0wuf5ha</bpmn:incoming>
      <bpmn:outgoing>Flow_1f5dqz7</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${smNewPlansList}" camunda:elementVariable="executionData">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:serviceTask id="SMUpdateProfile" name="Update subscriber type (connection type) in SM" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_02kk1o2</bpmn:incoming>
      <bpmn:outgoing>Flow_1ues1ex</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:callActivity id="SMStopAutoRenewal" name="Stop Auto Renewal in SM" camunda:asyncBefore="true" calledElement="StopAutoRenewalSMAddon">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1j9aoka</bpmn:incoming>
      <bpmn:outgoing>Flow_0qwewlg</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.stopRenewalList&#34;).elementList()}" camunda:elementVariable="executionData">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:serviceTask id="SMDeactivateBasePlan" name="Deactivate the base plan in SM" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1ayfb6h</bpmn:incoming>
      <bpmn:outgoing>Flow_0417ter</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_142m60g" name="Success" sourceRef="Gateway_1p7bl8d" targetRef="Gateway_1o9oxjw" />
    <bpmn:sequenceFlow id="Flow_1j9aoka" name="yes" sourceRef="Gateway_1o9oxjw" targetRef="SMStopAutoRenewal">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isStopRenewal}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_189v49n" name="no" sourceRef="Gateway_1o9oxjw" targetRef="Gateway_0m0292x" />
    <bpmn:sequenceFlow id="Flow_0qwewlg" sourceRef="SMStopAutoRenewal" targetRef="Gateway_140rtjb" />
    <bpmn:sequenceFlow id="Flow_0s2eh1f" name="Success" sourceRef="Gateway_140rtjb" targetRef="Gateway_0m0292x" />
    <bpmn:sequenceFlow id="Flow_1tmuop9" name="Failure" sourceRef="Gateway_140rtjb" targetRef="Event_1nvh757">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ues1ex" sourceRef="SMUpdateProfile" targetRef="Gateway_1ojpmld" />
    <bpmn:sequenceFlow id="Flow_1jc63i1" name="Failure" sourceRef="Gateway_1ojpmld" targetRef="Event_1y87bxh">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0wuf5ha" name="Success" sourceRef="Gateway_1ojpmld" targetRef="SMOfferActivationProcess" />
    <bpmn:sequenceFlow id="Flow_0417ter" sourceRef="SMDeactivateBasePlan" targetRef="Gateway_1p7bl8d" />
    <bpmn:sequenceFlow id="Flow_0iqj1q3" name="Failure" sourceRef="Gateway_1p7bl8d" targetRef="Event_07yvbsn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0vnnme5" default="Flow_0b44n7g">
      <bpmn:incoming>Flow_1f5dqz7</bpmn:incoming>
      <bpmn:outgoing>Flow_0b44n7g</bpmn:outgoing>
      <bpmn:outgoing>Flow_0rpe7ig</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1f5dqz7" sourceRef="SMOfferActivationProcess" targetRef="Gateway_0vnnme5" />
    <bpmn:sequenceFlow id="Flow_0b44n7g" name="Sucess" sourceRef="Gateway_0vnnme5" targetRef="Gateway_02ovrna" />
    <bpmn:endEvent id="Event_0bzsire">
      <bpmn:incoming>Flow_0rpe7ig</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0rpe7ig" name="Failure" sourceRef="Gateway_0vnnme5" targetRef="Event_0bzsire">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0zvb8pf" sourceRef="BSFetchContract" targetRef="Gateway_0efnn1s" />
    <bpmn:sequenceFlow id="Flow_0wumaza" sourceRef="BSCreateContract" targetRef="Gateway_1872b0n" />
    <bpmn:sequenceFlow id="Flow_09jvk06" sourceRef="SOMConMigDeleteCallback" targetRef="Gateway_15ufrsk" />
    <bpmn:sequenceFlow id="Flow_1qpmdtk" sourceRef="SOMConMigCreateCallback" targetRef="Gateway_0piviw0" />
    <bpmn:sequenceFlow id="Flow_056nn0p" sourceRef="Gateway_1qv0h9y" targetRef="CheckEligibilityCriteria" />
    <bpmn:exclusiveGateway id="Gateway_15ufrsk" default="Flow_11s9dz5">
      <bpmn:incoming>Flow_09jvk06</bpmn:incoming>
      <bpmn:outgoing>Flow_11s9dz5</bpmn:outgoing>
      <bpmn:outgoing>Flow_0gxffaw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_11s9dz5" name="Success" sourceRef="Gateway_15ufrsk" targetRef="SOMCreateService" />
    <bpmn:endEvent id="Event_0adlufs">
      <bpmn:incoming>Flow_0gxffaw</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0gxffaw" name="Failure" sourceRef="Gateway_15ufrsk" targetRef="Event_0adlufs">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0piviw0" default="Flow_09qfp6x">
      <bpmn:incoming>Flow_1qpmdtk</bpmn:incoming>
      <bpmn:outgoing>Flow_1z0ek2h</bpmn:outgoing>
      <bpmn:outgoing>Flow_09qfp6x</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0ksbqkm">
      <bpmn:incoming>Flow_1z0ek2h</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1z0ek2h" name="Failure" sourceRef="Gateway_0piviw0" targetRef="Event_0ksbqkm">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1o1p2ee" default="Flow_1ayfb6h">
      <bpmn:incoming>Flow_0nvtktl</bpmn:incoming>
      <bpmn:outgoing>Flow_1ayfb6h</bpmn:outgoing>
      <bpmn:outgoing>Flow_06wyr0d</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0nvtktl" sourceRef="BSConnectionMigration" targetRef="Gateway_1o1p2ee" />
    <bpmn:sequenceFlow id="Flow_1ayfb6h" name="Success" sourceRef="Gateway_1o1p2ee" targetRef="SMDeactivateBasePlan" />
    <bpmn:endEvent id="Event_06ksrrs">
      <bpmn:incoming>Flow_06wyr0d</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_06wyr0d" name="Failure" sourceRef="Gateway_1o1p2ee" targetRef="Event_06ksrrs">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1sghn9v" sourceRef="USSDUpdateCustomer" targetRef="orderExecEnd" />
    <bpmn:exclusiveGateway id="Gateway_11zjzkf" default="Flow_1pgpx4q">
      <bpmn:incoming>Flow_082z39y</bpmn:incoming>
      <bpmn:outgoing>Flow_1pgpx4q</bpmn:outgoing>
      <bpmn:outgoing>Flow_09vfk1k</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1pgpx4q" name="Success" sourceRef="Gateway_11zjzkf" targetRef="Gateway_038bbkq" />
    <bpmn:endEvent id="Event_09veif0">
      <bpmn:incoming>Flow_09vfk1k</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_09vfk1k" name="Failure" sourceRef="Gateway_11zjzkf" targetRef="Event_09veif0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="SMUpdateServiceSeqId" name="Update SeqId for subscriptions  in SM" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0yydj07</bpmn:incoming>
      <bpmn:outgoing>Flow_082z39y</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_082z39y" sourceRef="SMUpdateServiceSeqId" targetRef="Gateway_11zjzkf" />
    <bpmn:exclusiveGateway id="Gateway_02ovrna" name="Update seqId req" default="Flow_12p2czv">
      <bpmn:incoming>Flow_0b44n7g</bpmn:incoming>
      <bpmn:outgoing>Flow_0yydj07</bpmn:outgoing>
      <bpmn:outgoing>Flow_12p2czv</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0yydj07" name="yes" sourceRef="Gateway_02ovrna" targetRef="SMUpdateServiceSeqId">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${smUpdateSeqId}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_038bbkq">
      <bpmn:incoming>Flow_1pgpx4q</bpmn:incoming>
      <bpmn:incoming>Flow_12p2czv</bpmn:incoming>
      <bpmn:outgoing>Flow_0w5b2bh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0w5b2bh" sourceRef="Gateway_038bbkq" targetRef="SOMFetchServiceRegistry" />
    <bpmn:sequenceFlow id="Flow_12p2czv" name="no" sourceRef="Gateway_02ovrna" targetRef="Gateway_038bbkq" />
    <bpmn:sequenceFlow id="Flow_1b62dhz" sourceRef="Gateway_0d0gr9y" targetRef="Gateway_1btv5i3" />
    <bpmn:sequenceFlow id="Flow_04elrz6" sourceRef="Gateway_0puqsoc" targetRef="Gateway_055p9sp" />
    <bpmn:serviceTask id="BSCreateDocument" name="Create Document in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_15ao3cd</bpmn:incoming>
      <bpmn:outgoing>Flow_1vptqt5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1vptqt5" sourceRef="BSCreateDocument" targetRef="Gateway_18md2ez" />
    <bpmn:exclusiveGateway id="Gateway_18md2ez" default="Flow_0fasjsf">
      <bpmn:incoming>Flow_1vptqt5</bpmn:incoming>
      <bpmn:outgoing>Flow_0fasjsf</bpmn:outgoing>
      <bpmn:outgoing>Flow_1jpeve2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0fasjsf" name="Success" sourceRef="Gateway_18md2ez" targetRef="Gateway_116tyq7" />
    <bpmn:exclusiveGateway id="Gateway_116tyq7">
      <bpmn:incoming>Flow_0fasjsf</bpmn:incoming>
      <bpmn:incoming>Flow_0jzd450</bpmn:incoming>
      <bpmn:outgoing>Flow_0mlmz98</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0mlmz98" sourceRef="Gateway_116tyq7" targetRef="Gateway_0660bjz" />
    <bpmn:endEvent id="Event_1xwzed1">
      <bpmn:incoming>Flow_1jpeve2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1jpeve2" name="Failure" sourceRef="Gateway_18md2ez" targetRef="Event_1xwzed1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_03fw1le" name="document creation req" default="Flow_0jzd450">
      <bpmn:incoming>Flow_0cc92wo</bpmn:incoming>
      <bpmn:outgoing>Flow_15ao3cd</bpmn:outgoing>
      <bpmn:outgoing>Flow_0jzd450</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_15ao3cd" name="yes" sourceRef="Gateway_03fw1le" targetRef="BSCreateDocument">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${hasAttachment}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0jzd450" name="no" sourceRef="Gateway_03fw1le" targetRef="Gateway_116tyq7" />
    <bpmn:serviceTask id="UPCPlanFetch" name="UPC Plan Fetch for non-eligible addons" camunda:asyncBefore="true" camunda:delegateExpression="${upcFetchNonAllowedAddons}">
      <bpmn:incoming>Flow_01cfehs</bpmn:incoming>
      <bpmn:outgoing>Flow_13w7vn0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_13w7vn0" sourceRef="UPCPlanFetch" targetRef="Gateway_18vh7yf" />
    <bpmn:exclusiveGateway id="Gateway_18vh7yf" default="Flow_0yr1tk1">
      <bpmn:incoming>Flow_13w7vn0</bpmn:incoming>
      <bpmn:outgoing>Flow_0yr1tk1</bpmn:outgoing>
      <bpmn:outgoing>Flow_00d6k6u</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0yr1tk1" name="Success" sourceRef="Gateway_18vh7yf" targetRef="Gateway_188mwfp" />
    <bpmn:endEvent id="Event_1wuufgs">
      <bpmn:incoming>Flow_00d6k6u</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_00d6k6u" name="Failure" sourceRef="Gateway_18vh7yf" targetRef="Event_1wuufgs">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0vv2nza" name="if there are non retainable recurring ncc addons" default="Flow_1v7eb9r">
      <bpmn:incoming>Flow_1nxn8n6</bpmn:incoming>
      <bpmn:outgoing>Flow_03jq6sw</bpmn:outgoing>
      <bpmn:outgoing>Flow_1v7eb9r</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1j23i34">
      <bpmn:incoming>Flow_1v7eb9r</bpmn:incoming>
      <bpmn:incoming>Flow_10ej3ua</bpmn:incoming>
      <bpmn:outgoing>Flow_0csfpgc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0tkx80t" name="if source connection type is" default="Flow_13dfm5d">
      <bpmn:incoming>Flow_03jq6sw</bpmn:incoming>
      <bpmn:outgoing>Flow_13dfm5d</bpmn:outgoing>
      <bpmn:outgoing>Flow_1dfqfqe</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="NCCChangeSubscription" name="NCC Change Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${nccChangePlan}">
      <bpmn:incoming>Flow_1dfqfqe</bpmn:incoming>
      <bpmn:outgoing>Flow_01ki6tl</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0xe87ng" default="Flow_10ej3ua">
      <bpmn:incoming>Flow_01ki6tl</bpmn:incoming>
      <bpmn:incoming>Flow_1iof2ps</bpmn:incoming>
      <bpmn:outgoing>Flow_10ej3ua</bpmn:outgoing>
      <bpmn:outgoing>Flow_0rhjei9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1h1vpo3">
      <bpmn:incoming>Flow_0rhjei9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0yq0my2" name="is Cancel Subscription req" default="Flow_17pls6n">
      <bpmn:incoming>Flow_0csfpgc</bpmn:incoming>
      <bpmn:outgoing>Flow_00meamf</bpmn:outgoing>
      <bpmn:outgoing>Flow_17pls6n</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1l33gek">
      <bpmn:incoming>Flow_17pls6n</bpmn:incoming>
      <bpmn:incoming>Flow_1vwz0ak</bpmn:incoming>
      <bpmn:outgoing>Flow_0ip5wxn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0tykb9h" default="Flow_1vwz0ak">
      <bpmn:incoming>Flow_0vydzb9</bpmn:incoming>
      <bpmn:outgoing>Flow_1vwz0ak</bpmn:outgoing>
      <bpmn:outgoing>Flow_04yqox9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1eup2eh">
      <bpmn:incoming>Flow_04yqox9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_03jq6sw" name="yes" sourceRef="Gateway_0vv2nza" targetRef="Gateway_0tkx80t">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isNCCStopRenewal}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1v7eb9r" name="no" sourceRef="Gateway_0vv2nza" targetRef="Gateway_1j23i34" />
    <bpmn:sequenceFlow id="Flow_10ej3ua" name="Success" sourceRef="Gateway_0xe87ng" targetRef="Gateway_1j23i34" />
    <bpmn:sequenceFlow id="Flow_0csfpgc" sourceRef="Gateway_1j23i34" targetRef="Gateway_0yq0my2" />
    <bpmn:sequenceFlow id="Flow_13dfm5d" name="prepaid" sourceRef="Gateway_0tkx80t" targetRef="StopAutoRenewalNCCAddon" />
    <bpmn:sequenceFlow id="Flow_1dfqfqe" name="postpaid" sourceRef="Gateway_0tkx80t" targetRef="NCCChangeSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.hasProp('enrichmentResults') &amp;&amp; workflowData.jsonPath("$.enrichmentResults.serviceInfo.chargingPattern").stringValue() == '1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0rhjei9" name="Failure" sourceRef="Gateway_0xe87ng" targetRef="Event_1h1vpo3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_00meamf" name="yes" sourceRef="Gateway_0yq0my2" targetRef="DeactivateNonEligibleNCCNetworkAddon">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isNCCCancelSub}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_17pls6n" name="no" sourceRef="Gateway_0yq0my2" targetRef="Gateway_1l33gek" />
    <bpmn:sequenceFlow id="Flow_1vwz0ak" name="Success" sourceRef="Gateway_0tykb9h" targetRef="Gateway_1l33gek" />
    <bpmn:sequenceFlow id="Flow_04yqox9" name="Failure" sourceRef="Gateway_0tykb9h" targetRef="Event_1eup2eh">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ip5wxn" sourceRef="Gateway_1l33gek" targetRef="Gateway_1hxzn46" />
    <bpmn:sequenceFlow id="Flow_1nxn8n6" name="Success" sourceRef="Gateway_0im6ftz" targetRef="Gateway_0vv2nza" />
    <bpmn:exclusiveGateway id="Gateway_0qzc76r" name="is UPC non allowed addons fetch req" default="Flow_0qz2g2f">
      <bpmn:incoming>Flow_0jbov6p</bpmn:incoming>
      <bpmn:outgoing>Flow_01cfehs</bpmn:outgoing>
      <bpmn:outgoing>Flow_0qz2g2f</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_01cfehs" name="yes" sourceRef="Gateway_0qzc76r" targetRef="UPCPlanFetch">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${upcFecthReq}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_188mwfp">
      <bpmn:incoming>Flow_0yr1tk1</bpmn:incoming>
      <bpmn:incoming>Flow_0qz2g2f</bpmn:incoming>
      <bpmn:outgoing>Flow_0cc92wo</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0cc92wo" sourceRef="Gateway_188mwfp" targetRef="Gateway_03fw1le" />
    <bpmn:sequenceFlow id="Flow_0qz2g2f" name="no" sourceRef="Gateway_0qzc76r" targetRef="Gateway_188mwfp" />
    <bpmn:sequenceFlow id="Flow_01ki6tl" sourceRef="NCCChangeSubscription" targetRef="Gateway_0xe87ng" />
    <bpmn:callActivity id="StopAutoRenewalNCCAddon" name="Stop Auto Renewal NCCAddons" camunda:asyncBefore="true" calledElement="StopAutoRenewalNCCAddon" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_13dfm5d</bpmn:incoming>
      <bpmn:outgoing>Flow_1iof2ps</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.stopRenewalList&#34;).elementList()}" camunda:elementVariable="executionData">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_1iof2ps" sourceRef="StopAutoRenewalNCCAddon" targetRef="Gateway_0xe87ng" />
    <bpmn:callActivity id="DeactivateNonEligibleNCCNetworkAddon" name="Deactivate non-eligible NCCAddons" camunda:asyncBefore="true" calledElement="DeactivateNonEligibleNCCNetworkAddon" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_00meamf</bpmn:incoming>
      <bpmn:outgoing>Flow_0vydzb9</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.cancelSubscriptionList&#34;).elementList()}" camunda:elementVariable="executionData">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0vydzb9" sourceRef="DeactivateNonEligibleNCCNetworkAddon" targetRef="Gateway_0tykb9h" />
    <bpmn:exclusiveGateway id="Gateway_1exnsbg" name="if non retainable network addons preasent" default="Flow_18at53x">
      <bpmn:incoming>Flow_13p0evq</bpmn:incoming>
      <bpmn:outgoing>Flow_0293e49</bpmn:outgoing>
      <bpmn:outgoing>Flow_18at53x</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_10vgj8g">
      <bpmn:incoming>Flow_18at53x</bpmn:incoming>
      <bpmn:incoming>Flow_0j54kog</bpmn:incoming>
      <bpmn:outgoing>Flow_02kk1o2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0wxbkts" default="Flow_0j54kog">
      <bpmn:incoming>Flow_1b0k2i0</bpmn:incoming>
      <bpmn:outgoing>Flow_0j54kog</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ncgh1b</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0ffkaxh">
      <bpmn:incoming>Flow_1ncgh1b</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:callActivity id="DeactivateNonEligibleSMNetworkAddon" name="Deactivate Network Addon in SM" camunda:asyncBefore="true" calledElement="DeactivateNonEligibleSMNetworkAddon">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0293e49</bpmn:incoming>
      <bpmn:outgoing>Flow_1b0k2i0</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.cancelSubscriptionList&#34;).elementList()}" camunda:elementVariable="executionData">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0293e49" name="yes" sourceRef="Gateway_1exnsbg" targetRef="DeactivateNonEligibleSMNetworkAddon">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isCancelSubscription}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_18at53x" name="no" sourceRef="Gateway_1exnsbg" targetRef="Gateway_10vgj8g" />
    <bpmn:sequenceFlow id="Flow_0j54kog" name="Success" sourceRef="Gateway_0wxbkts" targetRef="Gateway_10vgj8g" />
    <bpmn:sequenceFlow id="Flow_1b0k2i0" sourceRef="DeactivateNonEligibleSMNetworkAddon" targetRef="Gateway_0wxbkts" />
    <bpmn:sequenceFlow id="Flow_1ncgh1b" name="Failure" sourceRef="Gateway_0wxbkts" targetRef="Event_0ffkaxh">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_13p0evq" sourceRef="Gateway_0m0292x" targetRef="Gateway_1exnsbg" />
    <bpmn:sequenceFlow id="Flow_02kk1o2" sourceRef="Gateway_10vgj8g" targetRef="SMUpdateProfile" />
    <bpmn:endEvent id="Event_0iuvcq7">
      <bpmn:incoming>Flow_0pxogh1</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1pvveok" default="Flow_0kjw1j8">
      <bpmn:incoming>Flow_0rktuxu</bpmn:incoming>
      <bpmn:outgoing>Flow_0pxogh1</bpmn:outgoing>
      <bpmn:outgoing>Flow_0kjw1j8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_06yy2kr" name="is Stop Renewal req" default="Flow_1tq0swz">
      <bpmn:incoming>Flow_0bj88hi</bpmn:incoming>
      <bpmn:outgoing>Flow_03u0m17</bpmn:outgoing>
      <bpmn:outgoing>Flow_1tq0swz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0ntjp2f">
      <bpmn:incoming>Flow_0kjw1j8</bpmn:incoming>
      <bpmn:incoming>Flow_1tq0swz</bpmn:incoming>
      <bpmn:outgoing>Flow_15oxvxx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0vcsj7a">
      <bpmn:incoming>Flow_0nl6jc1</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1e8ctk9" default="Flow_0a2oapg">
      <bpmn:incoming>Flow_1iwk0ch</bpmn:incoming>
      <bpmn:outgoing>Flow_0nl6jc1</bpmn:outgoing>
      <bpmn:outgoing>Flow_0a2oapg</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0aolky8" name="is Cancel Subscription req" default="Flow_01rseiq">
      <bpmn:incoming>Flow_15oxvxx</bpmn:incoming>
      <bpmn:outgoing>Flow_1r3svw6</bpmn:outgoing>
      <bpmn:outgoing>Flow_01rseiq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1rgwvol">
      <bpmn:incoming>Flow_0a2oapg</bpmn:incoming>
      <bpmn:incoming>Flow_01rseiq</bpmn:incoming>
      <bpmn:outgoing>Flow_07wcrkq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:callActivity id="BillingStopAutoRenewal" name="Stop Auto Renewal in Billing" camunda:asyncBefore="true" calledElement="BillingStopAutoRenewal">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_03u0m17</bpmn:incoming>
      <bpmn:outgoing>Flow_0rktuxu</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.stopRenewalList&#34;).elementList()}" camunda:elementVariable="executionData">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:callActivity id="BSCancelSubscripionforNetworkAddons" name="Cancel Subscripion for Network Addons in BS" camunda:asyncBefore="true" calledElement="BSCancelSubscripionforNetworkAddons">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1r3svw6</bpmn:incoming>
      <bpmn:outgoing>Flow_1iwk0ch</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.cancelSubscriptionList&#34;).elementList()}" camunda:elementVariable="executionData">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0pxogh1" name="Failure" sourceRef="Gateway_1pvveok" targetRef="Event_0iuvcq7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0rktuxu" sourceRef="BillingStopAutoRenewal" targetRef="Gateway_1pvveok" />
    <bpmn:sequenceFlow id="Flow_0kjw1j8" name="success" sourceRef="Gateway_1pvveok" targetRef="Gateway_0ntjp2f" />
    <bpmn:sequenceFlow id="Flow_03u0m17" name="yes" sourceRef="Gateway_06yy2kr" targetRef="BillingStopAutoRenewal">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isStopRenewal}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1tq0swz" name="no" sourceRef="Gateway_06yy2kr" targetRef="Gateway_0ntjp2f" />
    <bpmn:sequenceFlow id="Flow_15oxvxx" sourceRef="Gateway_0ntjp2f" targetRef="Gateway_0aolky8" />
    <bpmn:sequenceFlow id="Flow_0nl6jc1" name="Failure" sourceRef="Gateway_1e8ctk9" targetRef="Event_0vcsj7a">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1iwk0ch" sourceRef="BSCancelSubscripionforNetworkAddons" targetRef="Gateway_1e8ctk9" />
    <bpmn:sequenceFlow id="Flow_0a2oapg" name="success" sourceRef="Gateway_1e8ctk9" targetRef="Gateway_1rgwvol" />
    <bpmn:sequenceFlow id="Flow_1r3svw6" name="yes" sourceRef="Gateway_0aolky8" targetRef="BSCancelSubscripionforNetworkAddons">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isCancelSubscription}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_01rseiq" name="no" sourceRef="Gateway_0aolky8" targetRef="Gateway_1rgwvol" />
    <bpmn:exclusiveGateway id="Gateway_0aoz0bj" name="if source is postpaid" default="Flow_17huacz">
      <bpmn:incoming>Flow_09qfp6x</bpmn:incoming>
      <bpmn:outgoing>Flow_0bj88hi</bpmn:outgoing>
      <bpmn:outgoing>Flow_17huacz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0xavanl" name="false">
      <bpmn:incoming>Flow_07wcrkq</bpmn:incoming>
      <bpmn:incoming>Flow_17huacz</bpmn:incoming>
      <bpmn:outgoing>Flow_0dghesd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_07wcrkq" sourceRef="Gateway_1rgwvol" targetRef="Gateway_0xavanl" />
    <bpmn:sequenceFlow id="Flow_0dghesd" sourceRef="Gateway_0xavanl" targetRef="BSChangeSubscription" />
    <bpmn:sequenceFlow id="Flow_0bj88hi" name="true" sourceRef="Gateway_0aoz0bj" targetRef="Gateway_06yy2kr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.hasProp('enrichmentResults') &amp;&amp; workflowData.jsonPath("$.enrichmentResults.serviceInfo.chargingPattern").stringValue() == '1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_17huacz" name="false" sourceRef="Gateway_0aoz0bj" targetRef="Gateway_0xavanl" />
    <bpmn:serviceTask id="BSChangeSubscription" name=" Change subscription in Billing System" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0dghesd</bpmn:incoming>
      <bpmn:outgoing>Flow_0ken8t2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0nsy8xr" default="Flow_1m8szmv">
      <bpmn:incoming>Flow_0ken8t2</bpmn:incoming>
      <bpmn:outgoing>Flow_1o1aif0</bpmn:outgoing>
      <bpmn:outgoing>Flow_1m8szmv</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_18a8cxx">
      <bpmn:incoming>Flow_1o1aif0</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1o1aif0" name="Failure" sourceRef="Gateway_0nsy8xr" targetRef="Event_18a8cxx">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_09qfp6x" name="success" sourceRef="Gateway_0piviw0" targetRef="Gateway_0aoz0bj" />
    <bpmn:sequenceFlow id="Flow_0ken8t2" sourceRef="BSChangeSubscription" targetRef="Gateway_0nsy8xr" />
    <bpmn:sequenceFlow id="Flow_0ribmwf" sourceRef="Gateway_1vwh6iz" targetRef="BSActivateService" />
    <bpmn:serviceTask id="BSAddSubscription" name="BS AddSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_11krkqg</bpmn:incoming>
      <bpmn:outgoing>Flow_00lftyb</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1cuber9">
      <bpmn:incoming>Flow_0vm48gu</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0qqu6i6" default="Flow_0eb3u69">
      <bpmn:incoming>Flow_00lftyb</bpmn:incoming>
      <bpmn:outgoing>Flow_0vm48gu</bpmn:outgoing>
      <bpmn:outgoing>Flow_0eb3u69</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_12oq4gs" name="is billing AddSub req" default="Flow_0vsyntd">
      <bpmn:incoming>Flow_1m8szmv</bpmn:incoming>
      <bpmn:outgoing>Flow_11krkqg</bpmn:outgoing>
      <bpmn:outgoing>Flow_0vsyntd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1okjl1g">
      <bpmn:incoming>Flow_0eb3u69</bpmn:incoming>
      <bpmn:incoming>Flow_0vsyntd</bpmn:incoming>
      <bpmn:outgoing>Flow_1gi67jd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_11krkqg" name="yes" sourceRef="Gateway_12oq4gs" targetRef="BSAddSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isPostpaidMigration &amp;&amp; billingAddSub}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_00lftyb" sourceRef="BSAddSubscription" targetRef="Gateway_0qqu6i6" />
    <bpmn:sequenceFlow id="Flow_0vm48gu" name="Failure" sourceRef="Gateway_0qqu6i6" targetRef="Event_1cuber9">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0eb3u69" name="success" sourceRef="Gateway_0qqu6i6" targetRef="Gateway_1okjl1g" />
    <bpmn:sequenceFlow id="Flow_0vsyntd" name="no" sourceRef="Gateway_12oq4gs" targetRef="Gateway_1okjl1g" />
    <bpmn:sequenceFlow id="Flow_1m8szmv" name="success" sourceRef="Gateway_0nsy8xr" targetRef="Gateway_12oq4gs" />
    <bpmn:sequenceFlow id="Flow_1gi67jd" sourceRef="Gateway_1okjl1g" targetRef="Gateway_0h9ns6e" />
    <bpmn:exclusiveGateway id="Gateway_1btv5i3" name="if source connection type is" default="Flow_08t57n2">
      <bpmn:incoming>Flow_1b62dhz</bpmn:incoming>
      <bpmn:outgoing>Flow_0h7mknn</bpmn:outgoing>
      <bpmn:outgoing>Flow_08t57n2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0h7mknn" name="postpaid" sourceRef="Gateway_1btv5i3" targetRef="BSFetchContract">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.hasProp('enrichmentResults') &amp;&amp; workflowData.jsonPath("$.enrichmentResults.serviceInfo.chargingPattern").stringValue() == '1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_055p9sp">
      <bpmn:incoming>Flow_04elrz6</bpmn:incoming>
      <bpmn:incoming>Flow_08t57n2</bpmn:incoming>
      <bpmn:outgoing>Flow_0973vev</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0973vev" sourceRef="Gateway_055p9sp" targetRef="BSConnectionMigration" />
    <bpmn:sequenceFlow id="Flow_08t57n2" name="prepaid" sourceRef="Gateway_1btv5i3" targetRef="Gateway_055p9sp" />
  </bpmn:process>
  <bpmn:message id="Message_1m2g4r3" name="SOMConMigDeleteCallback" />
  <bpmn:message id="Message_0sal37a" name="MakePaymentCallback" />
  <bpmn:message id="Message_1njfe2e" name="MakePaymentCallback" />
  <bpmn:message id="Message_0d5grp9" name="SOMDeactivateCallBack" />
  <bpmn:message id="Message_0feghnz" name="SOMConMigCreateCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ConnectionMigration">
      <bpmndi:BPMNShape id="Gateway_0dobmvn_di" bpmnElement="Gateway_0dobmvn" isMarkerVisible="true">
        <dc:Bounds x="795" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0xcyvwl_di" bpmnElement="Event_0xcyvwl">
        <dc:Bounds x="4102" y="457" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0fmmmpo_di" bpmnElement="Event_0fmmmpo">
        <dc:Bounds x="802" y="457" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1f7l07o_di" bpmnElement="CheckEligibilityCriteria">
        <dc:Bounds x="610" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0im6ftz_di" bpmnElement="Gateway_0im6ftz" isMarkerVisible="true">
        <dc:Bounds x="4095" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_176607v_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="317" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0p7ezk0" bpmnElement="NCCConnectionMigration">
        <dc:Bounds x="3880" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_044avc3" bpmnElement="Gateway_1o7in37" isMarkerVisible="true">
        <dc:Bounds x="6055" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1jqh0f8" bpmnElement="Event_133liv9">
        <dc:Bounds x="6062" y="455" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0cbdfb3" bpmnElement="NCCCreateSubscription">
        <dc:Bounds x="5880" y="293" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0xhz8qn" bpmnElement="Gateway_1hxzn46" isMarkerVisible="true">
        <dc:Bounds x="5755" y="308" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5739" y="365" width="83" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1es5il2" bpmnElement="Gateway_0d0gr9y" isMarkerVisible="true">
        <dc:Bounds x="6185" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1jk0xzn" bpmnElement="BSConnectionMigration">
        <dc:Bounds x="7400" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16lnpw9" bpmnElement="BSFetchContract">
        <dc:Bounds x="6440" y="293" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1b6d2s4" bpmnElement="Event_0d9wdga">
        <dc:Bounds x="7072" y="439" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_18imkhy" bpmnElement="Gateway_0puqsoc" isMarkerVisible="true">
        <dc:Bounds x="7195" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1idgi9u" bpmnElement="Gateway_1cc6nnf" isMarkerVisible="true">
        <dc:Bounds x="7065" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0dqdmlj" bpmnElement="BSCancelContract">
        <dc:Bounds x="6890" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ywqf1h" bpmnElement="Gateway_04fqf8n" isMarkerVisible="true">
        <dc:Bounds x="6725" y="308" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6710" y="365" width="89" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1teq1nk" bpmnElement="Gateway_0efnn1s" isMarkerVisible="true">
        <dc:Bounds x="6595" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1e7nwoc" bpmnElement="Event_1ptcyvw">
        <dc:Bounds x="6602" y="455" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1oauhc9" bpmnElement="BSCreateContract">
        <dc:Bounds x="14250" y="293" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1m1g9iy" bpmnElement="Gateway_1872b0n" isMarkerVisible="true">
        <dc:Bounds x="14455" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0yaq9b5" bpmnElement="Gateway_0h9ns6e" isMarkerVisible="true">
        <dc:Bounds x="14085" y="308" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="14073" y="365" width="81" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0f91ooq" bpmnElement="Gateway_1uha48z" isMarkerVisible="true">
        <dc:Bounds x="14585" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0vjg4x5" bpmnElement="Event_1egxgd5">
        <dc:Bounds x="14462" y="435" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_10e9c5i" bpmnElement="LMSConnectionMigration">
        <dc:Bounds x="16650" y="293" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1is0jtu" bpmnElement="BSActivateService">
        <dc:Bounds x="15290" y="293" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0zjtgko" bpmnElement="Gateway_1ijf1pi" isMarkerVisible="true">
        <dc:Bounds x="15445" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0my4p3b" bpmnElement="Event_07tm15o">
        <dc:Bounds x="15452" y="433" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1oncrhz" bpmnElement="USSDUpdateCustomer">
        <dc:Bounds x="16970" y="293" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ze4b7k" bpmnElement="orderExecEnd">
        <dc:Bounds x="17162" y="315" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ehe2rz" bpmnElement="Gateway_13fsb0v" isMarkerVisible="true">
        <dc:Bounds x="16835" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0e9l5j3_di" bpmnElement="Event_0e9l5j3">
        <dc:Bounds x="16842" y="435" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01wyi3h_di" bpmnElement="OrderEnrichment">
        <dc:Bounds x="280" y="295" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1qv0h9y_di" bpmnElement="Gateway_1qv0h9y" isMarkerVisible="true">
        <dc:Bounds x="455" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ybupuw_di" bpmnElement="Event_0ybupuw">
        <dc:Bounds x="462" y="457" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0660bjz_di" bpmnElement="Gateway_0660bjz" isMarkerVisible="true">
        <dc:Bounds x="2115" y="310" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2101" y="367" width="80" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_09p2aic_di" bpmnElement="Gateway_09p2aic" isMarkerVisible="true">
        <dc:Bounds x="3715" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1cyeg3a_di" bpmnElement="Gateway_1cyeg3a" isMarkerVisible="true">
        <dc:Bounds x="3185" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_05mftie_di" bpmnElement="Gateway_05mftie" isMarkerVisible="true">
        <dc:Bounds x="2785" y="310" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2768" y="370" width="85" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ety2ln_di" bpmnElement="Event_1ety2ln">
        <dc:Bounds x="3072" y="464" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_02wjzys_di" bpmnElement="Gateway_02wjzys" isMarkerVisible="true">
        <dc:Bounds x="3065" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0w2lxlk_di" bpmnElement="BSCreateAccount">
        <dc:Bounds x="2290" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0lnrtsq_di" bpmnElement="Event_0lnrtsq">
        <dc:Bounds x="2492" y="457" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_046u6f1_di" bpmnElement="Gateway_046u6f1" isMarkerVisible="true">
        <dc:Bounds x="2485" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ax5tzr_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="2900" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1j90ev8_di" bpmnElement="ERPCreateAccount">
        <dc:Bounds x="3430" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1uxuslt_di" bpmnElement="Gateway_1uxuslt" isMarkerVisible="true">
        <dc:Bounds x="3585" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0kzckum_di" bpmnElement="Event_0kzckum">
        <dc:Bounds x="3592" y="457" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_186wb1c_di" bpmnElement="ExclusiveGateway_186wb1c" isMarkerVisible="true">
        <dc:Bounds x="2655" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0z4ojqa_di" bpmnElement="ExclusiveGateway_0z4ojqa" isMarkerVisible="true">
        <dc:Bounds x="3315" y="310" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3301" y="367" width="78" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1a4bp93_di" bpmnElement="Gateway_1a4bp93" isMarkerVisible="true">
        <dc:Bounds x="14705" y="308" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="14687" y="365" width="89" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1fr66xm" bpmnElement="BSCreditAdjustment">
        <dc:Bounds x="14870" y="293" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1u4v655" bpmnElement="Gateway_0iwvnp5" isMarkerVisible="true">
        <dc:Bounds x="15035" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_128uzm7" bpmnElement="Gateway_1vwh6iz" isMarkerVisible="true">
        <dc:Bounds x="15175" y="308" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5939" y="339" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06ma6bv" bpmnElement="Event_1pwcgtn">
        <dc:Bounds x="15042" y="435" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0yhf99l" bpmnElement="BSBookDeposit">
        <dc:Bounds x="15700" y="293" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1nk6pkk" bpmnElement="Gateway_1srjb5t" isMarkerVisible="true">
        <dc:Bounds x="15855" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_07e276l" bpmnElement="Event_0be2qxg">
        <dc:Bounds x="15862" y="435" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1reobrs" bpmnElement="Gateway_1bh2u35" isMarkerVisible="true">
        <dc:Bounds x="16275" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1jbz66r" bpmnElement="Event_0wdy143">
        <dc:Bounds x="16302" y="439" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ub7emi" bpmnElement="Gateway_0fcssqw" isMarkerVisible="true">
        <dc:Bounds x="16505" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1duar6v" bpmnElement="Gateway_02dl7c9" isMarkerVisible="true">
        <dc:Bounds x="15555" y="308" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="15542" y="365" width="77" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_109fsb9" bpmnElement="Gateway_1igl22o" isMarkerVisible="true">
        <dc:Bounds x="15985" y="308" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="15973" y="365" width="79" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06wjg6n" bpmnElement="Gateway_1iivd2w" isMarkerVisible="true">
        <dc:Bounds x="16405" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0lwyfkb_di" bpmnElement="ERPDeposit">
        <dc:Bounds x="16120" y="293" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16284yj" bpmnElement="SOMDeleteService">
        <dc:Bounds x="10700" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1mdaza8" bpmnElement="SOMConMigDeleteCallback">
        <dc:Bounds x="11000" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0005uwk" bpmnElement="Gateway_0v6zyru" isMarkerVisible="true">
        <dc:Bounds x="10885" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13u9a1f" bpmnElement="Event_0hcsxt6">
        <dc:Bounds x="10892" y="440" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1vo1sxc" bpmnElement="Gateway_1h2noc9" isMarkerVisible="true">
        <dc:Bounds x="11415" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ujre5u" bpmnElement="Event_03yojyd">
        <dc:Bounds x="11422" y="439" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1k4f2dz" bpmnElement="SOMCreateService">
        <dc:Bounds x="11250" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0yheyjj" bpmnElement="SOMConMigCreateCallback">
        <dc:Bounds x="11550" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_142i6yl" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="10370" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_04dqxu4" bpmnElement="Gateway_1fe7uc3" isMarkerVisible="true">
        <dc:Bounds x="10545" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1tshd7n" bpmnElement="Event_1x6pmfc">
        <dc:Bounds x="10552" y="439" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0mvl1sj" bpmnElement="Gateway_1o9oxjw" isMarkerVisible="true">
        <dc:Bounds x="7965" y="310" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7948" y="370" width="84" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0zgm80u" bpmnElement="Gateway_140rtjb" isMarkerVisible="true">
        <dc:Bounds x="8265" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0yefoog" bpmnElement="Gateway_0m0292x" isMarkerVisible="true">
        <dc:Bounds x="8425" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0pumy7k" bpmnElement="Event_1nvh757">
        <dc:Bounds x="8272" y="439" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1reqxfk" bpmnElement="Gateway_1ojpmld" isMarkerVisible="true">
        <dc:Bounds x="9355" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1010oze" bpmnElement="Event_1y87bxh">
        <dc:Bounds x="9362" y="437" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0traha0" bpmnElement="Gateway_1p7bl8d" isMarkerVisible="true">
        <dc:Bounds x="7835" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1bj9dpt" bpmnElement="Event_07yvbsn">
        <dc:Bounds x="7842" y="459" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1h6p8m6" bpmnElement="SMOfferActivationProcess">
        <dc:Bounds x="9500" y="295" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1bdzsyw" bpmnElement="SMUpdateProfile">
        <dc:Bounds x="9190" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_050ti8v" bpmnElement="SMStopAutoRenewal">
        <dc:Bounds x="8100" y="295" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_08mk7tg" bpmnElement="SMDeactivateBasePlan">
        <dc:Bounds x="7680" y="295" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vnnme5_di" bpmnElement="Gateway_0vnnme5" isMarkerVisible="true">
        <dc:Bounds x="9695" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0bzsire_di" bpmnElement="Event_0bzsire">
        <dc:Bounds x="9702" y="441" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_15ufrsk_di" bpmnElement="Gateway_15ufrsk" isMarkerVisible="true">
        <dc:Bounds x="11145" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0adlufs_di" bpmnElement="Event_0adlufs">
        <dc:Bounds x="11152" y="439" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0piviw0_di" bpmnElement="Gateway_0piviw0" isMarkerVisible="true">
        <dc:Bounds x="11685" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ksbqkm_di" bpmnElement="Event_0ksbqkm">
        <dc:Bounds x="11692" y="437" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1o1p2ee_di" bpmnElement="Gateway_1o1p2ee" isMarkerVisible="true">
        <dc:Bounds x="7565" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_06ksrrs_di" bpmnElement="Event_06ksrrs">
        <dc:Bounds x="7572" y="459" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_11zjzkf_di" bpmnElement="Gateway_11zjzkf" isMarkerVisible="true">
        <dc:Bounds x="10145" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09veif0_di" bpmnElement="Event_09veif0">
        <dc:Bounds x="10152" y="439" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="SMUpdateServiceSeqId_di" bpmnElement="SMUpdateServiceSeqId">
        <dc:Bounds x="9970" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_02ovrna_di" bpmnElement="Gateway_02ovrna" isMarkerVisible="true">
        <dc:Bounds x="9835" y="310" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="9818" y="367" width="85" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_038bbkq_di" bpmnElement="Gateway_038bbkq" isMarkerVisible="true">
        <dc:Bounds x="10265" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1aa1voj_di" bpmnElement="BSCreateDocument">
        <dc:Bounds x="1670" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_18md2ez_di" bpmnElement="Gateway_18md2ez" isMarkerVisible="true">
        <dc:Bounds x="1825" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_116tyq7_di" bpmnElement="Gateway_116tyq7" isMarkerVisible="true">
        <dc:Bounds x="1985" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1xwzed1_di" bpmnElement="Event_1xwzed1">
        <dc:Bounds x="1832" y="457" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_03fw1le_di" bpmnElement="Gateway_03fw1le" isMarkerVisible="true">
        <dc:Bounds x="1515" y="310" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1512" y="367" width="59" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ybx91b" bpmnElement="UPCPlanFetch">
        <dc:Bounds x="1060" y="295" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_18vh7yf_di" bpmnElement="Gateway_18vh7yf" isMarkerVisible="true">
        <dc:Bounds x="1245" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1wuufgs_di" bpmnElement="Event_1wuufgs">
        <dc:Bounds x="1252" y="457" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vv2nza_di" bpmnElement="Gateway_0vv2nza" isMarkerVisible="true">
        <dc:Bounds x="4315" y="310" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4303" y="370" width="74" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1j23i34_di" bpmnElement="Gateway_1j23i34" isMarkerVisible="true">
        <dc:Bounds x="5075" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0tkx80t_di" bpmnElement="Gateway_0tkx80t" isMarkerVisible="true">
        <dc:Bounds x="4465" y="310" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4527" y="325" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="NCCChangeSubscription_di" bpmnElement="NCCChangeSubscription">
        <dc:Bounds x="4660" y="434" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0xe87ng_di" bpmnElement="Gateway_0xe87ng" isMarkerVisible="true">
        <dc:Bounds x="4925" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1h1vpo3_di" bpmnElement="Event_1h1vpo3">
        <dc:Bounds x="4822" y="402" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0yq0my2_di" bpmnElement="Gateway_0yq0my2" isMarkerVisible="true">
        <dc:Bounds x="5215" y="308" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5202" y="365" width="80" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1l33gek_di" bpmnElement="Gateway_1l33gek" isMarkerVisible="true">
        <dc:Bounds x="5645" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0tykb9h_di" bpmnElement="Gateway_0tykb9h" isMarkerVisible="true">
        <dc:Bounds x="5525" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1eup2eh_di" bpmnElement="Event_1eup2eh">
        <dc:Bounds x="5532" y="446" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0qzc76r_di" bpmnElement="Gateway_0qzc76r" isMarkerVisible="true">
        <dc:Bounds x="915" y="310" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="903" y="367" width="77" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_188mwfp_di" bpmnElement="Gateway_188mwfp" isMarkerVisible="true">
        <dc:Bounds x="1395" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StopAutoRenewalNCCAddon_di" bpmnElement="StopAutoRenewalNCCAddon">
        <dc:Bounds x="4660" y="150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="DeactivateNonEligibleNCCAddon_di" bpmnElement="DeactivateNonEligibleNCCNetworkAddon">
        <dc:Bounds x="5370" y="293" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1exnsbg_di" bpmnElement="Gateway_1exnsbg" isMarkerVisible="true">
        <dc:Bounds x="8575" y="310" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8561" y="370" width="79" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_10vgj8g_di" bpmnElement="Gateway_10vgj8g" isMarkerVisible="true">
        <dc:Bounds x="9035" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0wxbkts_di" bpmnElement="Gateway_0wxbkts" isMarkerVisible="true">
        <dc:Bounds x="8905" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ffkaxh_di" bpmnElement="Event_0ffkaxh">
        <dc:Bounds x="8912" y="448" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0n18lwo_di" bpmnElement="DeactivateNonEligibleSMNetworkAddon">
        <dc:Bounds x="8720" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0iuvcq7_di" bpmnElement="Event_0iuvcq7">
        <dc:Bounds x="12242" y="440" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1pvveok_di" bpmnElement="Gateway_1pvveok" isMarkerVisible="true">
        <dc:Bounds x="12235" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_06yy2kr_di" bpmnElement="Gateway_06yy2kr" isMarkerVisible="true">
        <dc:Bounds x="11955" y="310" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="11940" y="367" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ntjp2f_di" bpmnElement="Gateway_0ntjp2f" isMarkerVisible="true">
        <dc:Bounds x="12345" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0vcsj7a_di" bpmnElement="Event_0vcsj7a">
        <dc:Bounds x="12742" y="440" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1e8ctk9_di" bpmnElement="Gateway_1e8ctk9" isMarkerVisible="true">
        <dc:Bounds x="12735" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0aolky8_di" bpmnElement="Gateway_0aolky8" isMarkerVisible="true">
        <dc:Bounds x="12455" y="310" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="12442" y="367" width="80" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1rgwvol_di" bpmnElement="Gateway_1rgwvol" isMarkerVisible="true">
        <dc:Bounds x="12845" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BillingStopAutoRenewal_di" bpmnElement="BillingStopAutoRenewal">
        <dc:Bounds x="12070" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BSCancelSubscripionforNetworkAddons_di" bpmnElement="BSCancelSubscripionforNetworkAddons">
        <dc:Bounds x="12580" y="295" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0aoz0bj_di" bpmnElement="Gateway_0aoz0bj" isMarkerVisible="true">
        <dc:Bounds x="11815" y="310" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="11815" y="367" width="54" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0xavanl_di" bpmnElement="Gateway_0xavanl" isMarkerVisible="true">
        <dc:Bounds x="12945" y="308" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="12959" y="365" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0qxpb6y" bpmnElement="BSChangeSubscription">
        <dc:Bounds x="13050" y="293" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0nsy8xr_di" bpmnElement="Gateway_0nsy8xr" isMarkerVisible="true">
        <dc:Bounds x="13255" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_18a8cxx_di" bpmnElement="Event_18a8cxx">
        <dc:Bounds x="13262" y="437" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0t26hdt" bpmnElement="BSAddSubscription">
        <dc:Bounds x="13600" y="293" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1fvlcp4" bpmnElement="Event_1cuber9">
        <dc:Bounds x="13772" y="438" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_010pdlp" bpmnElement="Gateway_0qqu6i6" isMarkerVisible="true">
        <dc:Bounds x="13765" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_12oq4gs_di" bpmnElement="Gateway_12oq4gs" isMarkerVisible="true">
        <dc:Bounds x="13485" y="308" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="13471" y="365" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1okjl1g_di" bpmnElement="Gateway_1okjl1g" isMarkerVisible="true">
        <dc:Bounds x="13875" y="308" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1btv5i3_di" bpmnElement="Gateway_1btv5i3" isMarkerVisible="true">
        <dc:Bounds x="6305" y="308" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6289" y="365" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_055p9sp_di" bpmnElement="Gateway_055p9sp" isMarkerVisible="true">
        <dc:Bounds x="7295" y="310" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0jbov6p_di" bpmnElement="Flow_0jbov6p">
        <di:waypoint x="845" y="335" />
        <di:waypoint x="915" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="845" y="312" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ynl656_di" bpmnElement="Flow_1ynl656">
        <di:waypoint x="820" y="360" />
        <di:waypoint x="820" y="457" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="824" y="404" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hdsvt7_di" bpmnElement="Flow_1hdsvt7">
        <di:waypoint x="710" y="335" />
        <di:waypoint x="795" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07e3hxg_di" bpmnElement="Flow_07e3hxg">
        <di:waypoint x="4120" y="360" />
        <di:waypoint x="4120" y="457" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4124" y="396" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00zuoss_di" bpmnElement="Flow_00zuoss">
        <di:waypoint x="3980" y="335" />
        <di:waypoint x="4095" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0ffsxxa" bpmnElement="Flow_1o58x6u">
        <di:waypoint x="5980" y="333" />
        <di:waypoint x="6055" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_10rwa6e" bpmnElement="Flow_0n8qv44">
        <di:waypoint x="6080" y="358" />
        <di:waypoint x="6080" y="455" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6093" y="390" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1k1llrv" bpmnElement="Flow_0mndqqx">
        <di:waypoint x="6105" y="333" />
        <di:waypoint x="6185" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6123" y="312" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1kefce4" bpmnElement="Flow_0qoyks8">
        <di:waypoint x="5805" y="333" />
        <di:waypoint x="5880" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5829" y="310" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1voysz3" bpmnElement="Flow_0bcs3d7">
        <di:waypoint x="5780" y="308" />
        <di:waypoint x="5780" y="240" />
        <di:waypoint x="6210" y="240" />
        <di:waypoint x="6210" y="308" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4839" y="394" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0rwuxgv" bpmnElement="Flow_003kweh">
        <di:waypoint x="7090" y="360" />
        <di:waypoint x="7090" y="439" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7095" y="392" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0gvhisc" bpmnElement="Flow_1p3wqyd">
        <di:waypoint x="7115" y="335" />
        <di:waypoint x="7195" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7131" y="317" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1b77vhs" bpmnElement="Flow_1ja2znm">
        <di:waypoint x="6750" y="308" />
        <di:waypoint x="6750" y="239" />
        <di:waypoint x="7220" y="239" />
        <di:waypoint x="7220" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6994" y="205" width="46" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0lv0ykq" bpmnElement="Flow_1y9cxw0">
        <di:waypoint x="6990" y="335" />
        <di:waypoint x="7065" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0znvasa" bpmnElement="Flow_010cjgt">
        <di:waypoint x="6773" y="335" />
        <di:waypoint x="6890" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6814" y="317" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0jkva2f" bpmnElement="Flow_1pi0c86">
        <di:waypoint x="6645" y="333" />
        <di:waypoint x="6725" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6663" y="314" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0k73se5" bpmnElement="Flow_0xapoyt">
        <di:waypoint x="6620" y="358" />
        <di:waypoint x="6620" y="455" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6625" y="376" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1a62hax" bpmnElement="Flow_0whuv2u">
        <di:waypoint x="14135" y="333" />
        <di:waypoint x="14250" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="14181" y="315" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1emh47h" bpmnElement="Flow_0imyhsl">
        <di:waypoint x="14505" y="333" />
        <di:waypoint x="14585" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="14525" y="315" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_16g661v" bpmnElement="Flow_1ghk51n">
        <di:waypoint x="14480" y="358" />
        <di:waypoint x="14480" y="435" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="14490" y="389" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_03pbwol" bpmnElement="Flow_0z316eb">
        <di:waypoint x="14110" y="308" />
        <di:waypoint x="14110" y="233" />
        <di:waypoint x="14610" y="233" />
        <di:waypoint x="14610" y="308" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="14322" y="217" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0j34dxh" bpmnElement="Flow_0ws81qh">
        <di:waypoint x="16750" y="333" />
        <di:waypoint x="16835" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0747zky" bpmnElement="Flow_0pobzk0">
        <di:waypoint x="15390" y="333" />
        <di:waypoint x="15445" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0qyxwej" bpmnElement="Flow_12r6qjf">
        <di:waypoint x="15470" y="358" />
        <di:waypoint x="15470" y="433" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="15474" y="381" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0euj1in" bpmnElement="Flow_0en120i">
        <di:waypoint x="16885" y="333" />
        <di:waypoint x="16970" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="16898" y="313" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jo15c7_di" bpmnElement="Flow_0jo15c7">
        <di:waypoint x="15495" y="333" />
        <di:waypoint x="15555" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="15580" y="1446" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mmxl51_di" bpmnElement="Flow_0mmxl51">
        <di:waypoint x="16860" y="358" />
        <di:waypoint x="16860" y="435" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="16864" y="382" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15fcocz_di" bpmnElement="Flow_15fcocz">
        <di:waypoint x="188" y="335" />
        <di:waypoint x="280" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wad9gc_di" bpmnElement="Flow_1wad9gc">
        <di:waypoint x="380" y="335" />
        <di:waypoint x="455" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07h6ess_di" bpmnElement="Flow_07h6ess">
        <di:waypoint x="480" y="360" />
        <di:waypoint x="480" y="457" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="483" y="394" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a5bx3l_di" bpmnElement="Flow_1a5bx3l">
        <di:waypoint x="3635" y="335" />
        <di:waypoint x="3715" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3654" y="317" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ooik7v_di" bpmnElement="Flow_1ooik7v">
        <di:waypoint x="2140" y="310" />
        <di:waypoint x="2140" y="225" />
        <di:waypoint x="2680" y="225" />
        <di:waypoint x="2680" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2372" y="207" width="80" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xateuc_di" bpmnElement="Flow_0xateuc">
        <di:waypoint x="2165" y="335" />
        <di:waypoint x="2290" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2179" y="313" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0yc8zdo_di" bpmnElement="SequenceFlow_0yc8zdo">
        <di:waypoint x="3340" y="310" />
        <di:waypoint x="3340" y="228" />
        <di:waypoint x="3740" y="228" />
        <di:waypoint x="3740" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3469" y="215" width="80" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08hfui2_di" bpmnElement="Flow_08hfui2">
        <di:waypoint x="2810" y="310" />
        <di:waypoint x="2810" y="228" />
        <di:waypoint x="3210" y="228" />
        <di:waypoint x="3210" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2984" y="211" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0aw53gz_di" bpmnElement="Flow_0aw53gz">
        <di:waypoint x="3115" y="335" />
        <di:waypoint x="3185" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3130" y="317" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0osryl2_di" bpmnElement="SequenceFlow_0osryl2">
        <di:waypoint x="3235" y="335" />
        <di:waypoint x="3315" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1e9ieh8_di" bpmnElement="SequenceFlow_1e9ieh8">
        <di:waypoint x="2705" y="335" />
        <di:waypoint x="2785" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lc6dcx_di" bpmnElement="Flow_1lc6dcx">
        <di:waypoint x="2835" y="335" />
        <di:waypoint x="2900" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2850" y="313" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14j83he_di" bpmnElement="Flow_14j83he">
        <di:waypoint x="3090" y="360" />
        <di:waypoint x="3090" y="464" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3095" y="396" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g0ro5n_di" bpmnElement="Flow_1g0ro5n">
        <di:waypoint x="3000" y="335" />
        <di:waypoint x="3065" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kcjs3m_di" bpmnElement="Flow_1kcjs3m">
        <di:waypoint x="2390" y="335" />
        <di:waypoint x="2485" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14dpjpm_di" bpmnElement="Flow_14dpjpm">
        <di:waypoint x="2510" y="360" />
        <di:waypoint x="2510" y="457" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2518" y="405" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qcts75_di" bpmnElement="Flow_1qcts75">
        <di:waypoint x="2535" y="335" />
        <di:waypoint x="2655" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2568" y="313" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yydgpz_di" bpmnElement="Flow_1yydgpz">
        <di:waypoint x="3530" y="335" />
        <di:waypoint x="3585" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04n99hf_di" bpmnElement="Flow_04n99hf">
        <di:waypoint x="3610" y="360" />
        <di:waypoint x="3610" y="457" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3614" y="396" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1163n22_di" bpmnElement="Flow_1163n22">
        <di:waypoint x="3365" y="335" />
        <di:waypoint x="3430" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06ju4qo_di" bpmnElement="Flow_06ju4qo">
        <di:waypoint x="14755" y="333" />
        <di:waypoint x="14870" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="14785" y="315" width="42" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13z2xbt_di" bpmnElement="Flow_13z2xbt">
        <di:waypoint x="14730" y="308" />
        <di:waypoint x="14730" y="226" />
        <di:waypoint x="15200" y="226" />
        <di:waypoint x="15200" y="308" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="14947" y="208" width="37" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0h8vqb2" bpmnElement="Flow_1x2ukfx">
        <di:waypoint x="14970" y="333" />
        <di:waypoint x="15035" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0bv0c6m" bpmnElement="Flow_1spwxrz">
        <di:waypoint x="15085" y="333" />
        <di:waypoint x="15175" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="15108" y="315" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0wrrqpq" bpmnElement="Flow_0mcpsgv">
        <di:waypoint x="15060" y="358" />
        <di:waypoint x="15060" y="435" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="15066" y="389" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x0smh8_di" bpmnElement="Flow_0x0smh8">
        <di:waypoint x="14635" y="333" />
        <di:waypoint x="14705" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0px0v9u" bpmnElement="Flow_0y3ni4e">
        <di:waypoint x="15605" y="333" />
        <di:waypoint x="15700" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="15645" y="315" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0gtnbln" bpmnElement="Flow_048dwd7">
        <di:waypoint x="15800" y="333" />
        <di:waypoint x="15855" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1k9rjqz" bpmnElement="Flow_1753sm0">
        <di:waypoint x="15905" y="333" />
        <di:waypoint x="15985" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="15923" y="311" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0cy2461" bpmnElement="Flow_0kfzzdd">
        <di:waypoint x="15880" y="358" />
        <di:waypoint x="15880" y="435" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="15884" y="382" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1sp4idw" bpmnElement="Flow_13nxt9u">
        <di:waypoint x="16035" y="333" />
        <di:waypoint x="16120" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="16071" y="315" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1dzzau9" bpmnElement="Flow_16rchyl">
        <di:waypoint x="16220" y="333" />
        <di:waypoint x="16275" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0qfp4eb" bpmnElement="Flow_02oqrsw">
        <di:waypoint x="16325" y="333" />
        <di:waypoint x="16405" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="16341" y="311" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0weiv0n" bpmnElement="Flow_0f45zn7">
        <di:waypoint x="16300" y="358" />
        <di:waypoint x="16300" y="398" />
        <di:waypoint x="16320" y="398" />
        <di:waypoint x="16320" y="439" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="16305" y="364" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_102fsa3" bpmnElement="Flow_0zzgqoe">
        <di:waypoint x="15580" y="308" />
        <di:waypoint x="15580" y="220" />
        <di:waypoint x="16530" y="220" />
        <di:waypoint x="16530" y="308" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="16044" y="193" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_01p2zgd" bpmnElement="Flow_1cltfre">
        <di:waypoint x="16455" y="333" />
        <di:waypoint x="16505" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1nwuh8m" bpmnElement="Flow_0bl1xjk">
        <di:waypoint x="16010" y="308" />
        <di:waypoint x="16010" y="243" />
        <di:waypoint x="16430" y="243" />
        <di:waypoint x="16430" y="308" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="16214" y="225" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ip5169_di" bpmnElement="Flow_1ip5169">
        <di:waypoint x="16555" y="333" />
        <di:waypoint x="16650" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yg9x7l_di" bpmnElement="Flow_1yg9x7l">
        <di:waypoint x="3765" y="335" />
        <di:waypoint x="3880" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1ff2wj4" bpmnElement="Flow_1hy5d5x">
        <di:waypoint x="10595" y="335" />
        <di:waypoint x="10700" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="10628" y="317" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_07axys3" bpmnElement="Flow_0j3x7n8">
        <di:waypoint x="10800" y="335" />
        <di:waypoint x="10885" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_120azn4" bpmnElement="Flow_1c5budh">
        <di:waypoint x="10935" y="335" />
        <di:waypoint x="11000" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="10947" y="317" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1gqzill" bpmnElement="Flow_0zq7p5m">
        <di:waypoint x="10910" y="360" />
        <di:waypoint x="10910" y="440" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="10913" y="390" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_15390q0" bpmnElement="Flow_0z7u7qd">
        <di:waypoint x="11350" y="335" />
        <di:waypoint x="11415" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0caif4j" bpmnElement="Flow_00mrgtj">
        <di:waypoint x="11465" y="335" />
        <di:waypoint x="11550" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="11488" y="339" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0eitsbn" bpmnElement="Flow_0ribv5t">
        <di:waypoint x="11440" y="360" />
        <di:waypoint x="11440" y="439" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="11444" y="394" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0ve133u" bpmnElement="Flow_1eycpfy">
        <di:waypoint x="10470" y="335" />
        <di:waypoint x="10545" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1mb22uy" bpmnElement="Flow_1oz79jz">
        <di:waypoint x="10570" y="360" />
        <di:waypoint x="10570" y="439" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="10603" y="405" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1rcood5" bpmnElement="Flow_142m60g">
        <di:waypoint x="7885" y="335" />
        <di:waypoint x="7965" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7904" y="317" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0fprp6y" bpmnElement="Flow_1j9aoka">
        <di:waypoint x="8015" y="335" />
        <di:waypoint x="8100" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8050" y="317" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1eaj5o0" bpmnElement="Flow_189v49n">
        <di:waypoint x="7990" y="310" />
        <di:waypoint x="7990" y="232" />
        <di:waypoint x="8450" y="232" />
        <di:waypoint x="8450" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8214" y="214" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1dziola" bpmnElement="Flow_0qwewlg">
        <di:waypoint x="8200" y="335" />
        <di:waypoint x="8265" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1tmlut4" bpmnElement="Flow_0s2eh1f">
        <di:waypoint x="8315" y="335" />
        <di:waypoint x="8425" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8350" y="317" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1u5xrpr" bpmnElement="Flow_1tmuop9">
        <di:waypoint x="8290" y="360" />
        <di:waypoint x="8290" y="439" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8303" y="379" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_11s74r2" bpmnElement="Flow_1ues1ex">
        <di:waypoint x="9290" y="335" />
        <di:waypoint x="9355" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_08deilv" bpmnElement="Flow_1jc63i1">
        <di:waypoint x="9380" y="360" />
        <di:waypoint x="9380" y="437" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="9393" y="399" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1tofc18" bpmnElement="Flow_0wuf5ha">
        <di:waypoint x="9405" y="335" />
        <di:waypoint x="9500" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="9416" y="303" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_18y7uay" bpmnElement="Flow_0417ter">
        <di:waypoint x="7780" y="335" />
        <di:waypoint x="7835" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0rvf1p6" bpmnElement="Flow_0iqj1q3">
        <di:waypoint x="7860" y="360" />
        <di:waypoint x="7860" y="459" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7883" y="414" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1f5dqz7_di" bpmnElement="Flow_1f5dqz7">
        <di:waypoint x="9600" y="335" />
        <di:waypoint x="9695" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b44n7g_di" bpmnElement="Flow_0b44n7g">
        <di:waypoint x="9745" y="335" />
        <di:waypoint x="9835" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="9786" y="314" width="37" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rpe7ig_di" bpmnElement="Flow_0rpe7ig">
        <di:waypoint x="9720" y="360" />
        <di:waypoint x="9720" y="441" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="9743" y="399" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zvb8pf_di" bpmnElement="Flow_0zvb8pf">
        <di:waypoint x="6540" y="333" />
        <di:waypoint x="6595" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wumaza_di" bpmnElement="Flow_0wumaza">
        <di:waypoint x="14350" y="333" />
        <di:waypoint x="14455" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09jvk06_di" bpmnElement="Flow_09jvk06">
        <di:waypoint x="11100" y="335" />
        <di:waypoint x="11145" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qpmdtk_di" bpmnElement="Flow_1qpmdtk">
        <di:waypoint x="11650" y="335" />
        <di:waypoint x="11685" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_056nn0p_di" bpmnElement="Flow_056nn0p">
        <di:waypoint x="505" y="335" />
        <di:waypoint x="610" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11s9dz5_di" bpmnElement="Flow_11s9dz5">
        <di:waypoint x="11195" y="335" />
        <di:waypoint x="11250" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="11201" y="317" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gxffaw_di" bpmnElement="Flow_0gxffaw">
        <di:waypoint x="11170" y="360" />
        <di:waypoint x="11170" y="439" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="11168" y="397" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1z0ek2h_di" bpmnElement="Flow_1z0ek2h">
        <di:waypoint x="11710" y="360" />
        <di:waypoint x="11710" y="437" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="11708" y="396" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nvtktl_di" bpmnElement="Flow_0nvtktl">
        <di:waypoint x="7500" y="335" />
        <di:waypoint x="7565" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ayfb6h_di" bpmnElement="Flow_1ayfb6h">
        <di:waypoint x="7615" y="335" />
        <di:waypoint x="7680" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7626" y="317" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06wyr0d_di" bpmnElement="Flow_06wyr0d">
        <di:waypoint x="7590" y="360" />
        <di:waypoint x="7590" y="459" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7603" y="406" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sghn9v_di" bpmnElement="Flow_1sghn9v">
        <di:waypoint x="17070" y="333" />
        <di:waypoint x="17162" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pgpx4q_di" bpmnElement="Flow_1pgpx4q">
        <di:waypoint x="10195" y="335" />
        <di:waypoint x="10265" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="10208" y="314" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09vfk1k_di" bpmnElement="Flow_09vfk1k">
        <di:waypoint x="10170" y="360" />
        <di:waypoint x="10170" y="439" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="10183" y="390" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_082z39y_di" bpmnElement="Flow_082z39y">
        <di:waypoint x="10070" y="335" />
        <di:waypoint x="10145" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yydj07_di" bpmnElement="Flow_0yydj07">
        <di:waypoint x="9885" y="335" />
        <di:waypoint x="9970" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="9919" y="317" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0w5b2bh_di" bpmnElement="Flow_0w5b2bh">
        <di:waypoint x="10315" y="335" />
        <di:waypoint x="10370" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12p2czv_di" bpmnElement="Flow_12p2czv">
        <di:waypoint x="9860" y="310" />
        <di:waypoint x="9860" y="222" />
        <di:waypoint x="10290" y="222" />
        <di:waypoint x="10290" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="10069" y="204" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b62dhz_di" bpmnElement="Flow_1b62dhz">
        <di:waypoint x="6235" y="333" />
        <di:waypoint x="6305" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04elrz6_di" bpmnElement="Flow_04elrz6">
        <di:waypoint x="7245" y="335" />
        <di:waypoint x="7295" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vptqt5_di" bpmnElement="Flow_1vptqt5">
        <di:waypoint x="1770" y="335" />
        <di:waypoint x="1825" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fasjsf_di" bpmnElement="Flow_0fasjsf">
        <di:waypoint x="1875" y="335" />
        <di:waypoint x="1985" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1910" y="317" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mlmz98_di" bpmnElement="Flow_0mlmz98">
        <di:waypoint x="2035" y="335" />
        <di:waypoint x="2115" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jpeve2_di" bpmnElement="Flow_1jpeve2">
        <di:waypoint x="1850" y="360" />
        <di:waypoint x="1850" y="457" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1849" y="406" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15ao3cd_di" bpmnElement="Flow_15ao3cd">
        <di:waypoint x="1565" y="335" />
        <di:waypoint x="1670" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1609" y="317" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jzd450_di" bpmnElement="Flow_0jzd450">
        <di:waypoint x="1540" y="310" />
        <di:waypoint x="1540" y="230" />
        <di:waypoint x="2010" y="230" />
        <di:waypoint x="2010" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1769" y="212" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13w7vn0_di" bpmnElement="Flow_13w7vn0">
        <di:waypoint x="1160" y="335" />
        <di:waypoint x="1245" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yr1tk1_di" bpmnElement="Flow_0yr1tk1">
        <di:waypoint x="1295" y="335" />
        <di:waypoint x="1395" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1309" y="313" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00d6k6u_di" bpmnElement="Flow_00d6k6u">
        <di:waypoint x="1270" y="360" />
        <di:waypoint x="1270" y="457" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1268" y="428" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03jq6sw_di" bpmnElement="Flow_03jq6sw">
        <di:waypoint x="4365" y="335" />
        <di:waypoint x="4465" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4407" y="317" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1v7eb9r_di" bpmnElement="Flow_1v7eb9r">
        <di:waypoint x="4340" y="310" />
        <di:waypoint x="4340" y="112" />
        <di:waypoint x="5100" y="112" />
        <di:waypoint x="5100" y="308" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4714" y="94" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10ej3ua_di" bpmnElement="Flow_10ej3ua">
        <di:waypoint x="4975" y="333" />
        <di:waypoint x="5075" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4998" y="315" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0csfpgc_di" bpmnElement="Flow_0csfpgc">
        <di:waypoint x="5125" y="333" />
        <di:waypoint x="5215" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13dfm5d_di" bpmnElement="Flow_13dfm5d">
        <di:waypoint x="4490" y="310" />
        <di:waypoint x="4490" y="190" />
        <di:waypoint x="4660" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4487" y="247" width="37" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dfqfqe_di" bpmnElement="Flow_1dfqfqe">
        <di:waypoint x="4490" y="360" />
        <di:waypoint x="4490" y="474" />
        <di:waypoint x="4660" y="474" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4509" y="415" width="42" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rhjei9_di" bpmnElement="Flow_0rhjei9">
        <di:waypoint x="4925" y="333" />
        <di:waypoint x="4840" y="333" />
        <di:waypoint x="4840" y="402" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4863" y="313" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00meamf_di" bpmnElement="Flow_00meamf">
        <di:waypoint x="5265" y="333" />
        <di:waypoint x="5370" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5295" y="315" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17pls6n_di" bpmnElement="Flow_17pls6n">
        <di:waypoint x="5240" y="308" />
        <di:waypoint x="5240" y="202" />
        <di:waypoint x="5670" y="202" />
        <di:waypoint x="5670" y="308" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5449" y="184" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vwz0ak_di" bpmnElement="Flow_1vwz0ak">
        <di:waypoint x="5575" y="333" />
        <di:waypoint x="5645" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5564" y="312" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04yqox9_di" bpmnElement="Flow_04yqox9">
        <di:waypoint x="5550" y="358" />
        <di:waypoint x="5550" y="446" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5549" y="399" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ip5wxn_di" bpmnElement="Flow_0ip5wxn">
        <di:waypoint x="5695" y="333" />
        <di:waypoint x="5755" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nxn8n6_di" bpmnElement="Flow_1nxn8n6">
        <di:waypoint x="4145" y="335" />
        <di:waypoint x="4315" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4188" y="312" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01cfehs_di" bpmnElement="Flow_01cfehs">
        <di:waypoint x="965" y="335" />
        <di:waypoint x="1060" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1004" y="317" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cc92wo_di" bpmnElement="Flow_0cc92wo">
        <di:waypoint x="1445" y="335" />
        <di:waypoint x="1515" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qz2g2f_di" bpmnElement="Flow_0qz2g2f">
        <di:waypoint x="940" y="310" />
        <di:waypoint x="940" y="230" />
        <di:waypoint x="1420" y="230" />
        <di:waypoint x="1420" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1174" y="212" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01ki6tl_di" bpmnElement="Flow_01ki6tl">
        <di:waypoint x="4760" y="474" />
        <di:waypoint x="4950" y="474" />
        <di:waypoint x="4950" y="358" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1iof2ps_di" bpmnElement="Flow_1iof2ps">
        <di:waypoint x="4760" y="190" />
        <di:waypoint x="4950" y="190" />
        <di:waypoint x="4950" y="308" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vydzb9_di" bpmnElement="Flow_0vydzb9">
        <di:waypoint x="5470" y="333" />
        <di:waypoint x="5525" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0293e49_di" bpmnElement="Flow_0293e49">
        <di:waypoint x="8625" y="335" />
        <di:waypoint x="8720" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8665" y="317" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18at53x_di" bpmnElement="Flow_18at53x">
        <di:waypoint x="8600" y="310" />
        <di:waypoint x="8600" y="245" />
        <di:waypoint x="9060" y="245" />
        <di:waypoint x="9060" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8824" y="227" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0j54kog_di" bpmnElement="Flow_0j54kog">
        <di:waypoint x="8955" y="335" />
        <di:waypoint x="9035" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8968" y="315" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b0k2i0_di" bpmnElement="Flow_1b0k2i0">
        <di:waypoint x="8820" y="335" />
        <di:waypoint x="8905" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ncgh1b_di" bpmnElement="Flow_1ncgh1b">
        <di:waypoint x="8930" y="360" />
        <di:waypoint x="8930" y="448" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8933" y="390" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13p0evq_di" bpmnElement="Flow_13p0evq">
        <di:waypoint x="8475" y="335" />
        <di:waypoint x="8575" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02kk1o2_di" bpmnElement="Flow_02kk1o2">
        <di:waypoint x="9085" y="335" />
        <di:waypoint x="9190" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pxogh1_di" bpmnElement="Flow_0pxogh1">
        <di:waypoint x="12260" y="360" />
        <di:waypoint x="12260" y="440" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="12263" y="402" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rktuxu_di" bpmnElement="Flow_0rktuxu">
        <di:waypoint x="12170" y="335" />
        <di:waypoint x="12235" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kjw1j8_di" bpmnElement="Flow_0kjw1j8">
        <di:waypoint x="12285" y="335" />
        <di:waypoint x="12345" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="12279" y="317" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03u0m17_di" bpmnElement="Flow_03u0m17">
        <di:waypoint x="12005" y="335" />
        <di:waypoint x="12070" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="12015" y="317" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tq0swz_di" bpmnElement="Flow_1tq0swz">
        <di:waypoint x="11980" y="310" />
        <di:waypoint x="11980" y="254" />
        <di:waypoint x="12370" y="254" />
        <di:waypoint x="12370" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="12169" y="236" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15oxvxx_di" bpmnElement="Flow_15oxvxx">
        <di:waypoint x="12395" y="335" />
        <di:waypoint x="12455" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nl6jc1_di" bpmnElement="Flow_0nl6jc1">
        <di:waypoint x="12760" y="360" />
        <di:waypoint x="12760" y="440" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="12763" y="402" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1iwk0ch_di" bpmnElement="Flow_1iwk0ch">
        <di:waypoint x="12680" y="335" />
        <di:waypoint x="12735" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a2oapg_di" bpmnElement="Flow_0a2oapg">
        <di:waypoint x="12785" y="335" />
        <di:waypoint x="12845" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="12779" y="317" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r3svw6_di" bpmnElement="Flow_1r3svw6">
        <di:waypoint x="12505" y="335" />
        <di:waypoint x="12580" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="12516" y="317" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01rseiq_di" bpmnElement="Flow_01rseiq">
        <di:waypoint x="12480" y="310" />
        <di:waypoint x="12480" y="254" />
        <di:waypoint x="12870" y="254" />
        <di:waypoint x="12870" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="12669" y="236" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07wcrkq_di" bpmnElement="Flow_07wcrkq">
        <di:waypoint x="12895" y="335" />
        <di:waypoint x="12947" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dghesd_di" bpmnElement="Flow_0dghesd">
        <di:waypoint x="12995" y="333" />
        <di:waypoint x="13050" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bj88hi_di" bpmnElement="Flow_0bj88hi">
        <di:waypoint x="11865" y="335" />
        <di:waypoint x="11955" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="11901" y="317" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17huacz_di" bpmnElement="Flow_17huacz">
        <di:waypoint x="11840" y="310" />
        <di:waypoint x="11840" y="210" />
        <di:waypoint x="12970" y="210" />
        <di:waypoint x="12970" y="308" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="12394" y="192" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o1aif0_di" bpmnElement="Flow_1o1aif0">
        <di:waypoint x="13280" y="358" />
        <di:waypoint x="13280" y="437" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="13281" y="395" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09qfp6x_di" bpmnElement="Flow_09qfp6x">
        <di:waypoint x="11735" y="335" />
        <di:waypoint x="11815" y="335" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="11749" y="312" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ken8t2_di" bpmnElement="Flow_0ken8t2">
        <di:waypoint x="13150" y="333" />
        <di:waypoint x="13255" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ribmwf_di" bpmnElement="Flow_0ribmwf">
        <di:waypoint x="15225" y="333" />
        <di:waypoint x="15290" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11krkqg_di" bpmnElement="Flow_11krkqg">
        <di:waypoint x="13535" y="333" />
        <di:waypoint x="13600" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="13559" y="315" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0s95718" bpmnElement="Flow_00lftyb">
        <di:waypoint x="13700" y="333" />
        <di:waypoint x="13765" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1koykwn" bpmnElement="Flow_0vm48gu">
        <di:waypoint x="13790" y="358" />
        <di:waypoint x="13790" y="438" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="13793" y="400" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0wbardj" bpmnElement="Flow_0eb3u69">
        <di:waypoint x="13815" y="333" />
        <di:waypoint x="13875" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="13809" y="315" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vsyntd_di" bpmnElement="Flow_0vsyntd">
        <di:waypoint x="13510" y="308" />
        <di:waypoint x="13510" y="252" />
        <di:waypoint x="13900" y="252" />
        <di:waypoint x="13900" y="308" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="13699" y="234" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m8szmv_di" bpmnElement="Flow_1m8szmv">
        <di:waypoint x="13305" y="333" />
        <di:waypoint x="13485" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="13376" y="315" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gi67jd_di" bpmnElement="Flow_1gi67jd">
        <di:waypoint x="13925" y="333" />
        <di:waypoint x="14085" y="333" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0h7mknn_di" bpmnElement="Flow_0h7mknn">
        <di:waypoint x="6355" y="333" />
        <di:waypoint x="6440" y="333" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6377" y="315" width="42" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0973vev_di" bpmnElement="Flow_0973vev">
        <di:waypoint x="7345" y="335" />
        <di:waypoint x="7400" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08t57n2_di" bpmnElement="Flow_08t57n2">
        <di:waypoint x="6330" y="308" />
        <di:waypoint x="6330" y="170" />
        <di:waypoint x="7320" y="170" />
        <di:waypoint x="7320" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6808" y="152" width="37" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
