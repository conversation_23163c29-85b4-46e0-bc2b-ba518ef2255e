<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.6.0">
  <bpmn:process id="AddService" name="AddService" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:extensionElements />
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0hlk96z</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0hlk96z" sourceRef="orderExecStart" targetRef="OrderEnrichment" />
    <bpmn:serviceTask id="GenerateDataDelegateTask" name="Generate Data Delegate" camunda:delegateExpression="${generateDataDelegateOnboarding}">
      <bpmn:incoming>Flow_1lfo7gs</bpmn:incoming>
      <bpmn:outgoing>Flow_01jguy9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="CreateInstancesTask" name="Create Instances" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${createInstancesDelegateOnboarding}">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="childProcess">Onboarding-ServiceProvisioning</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_01jguy9</bpmn:incoming>
      <bpmn:incoming>Flow_0n547gn</bpmn:incoming>
      <bpmn:outgoing>Flow_0ae2gv8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1nrrv7h" name="All instances created ?">
      <bpmn:incoming>Flow_0ae2gv8</bpmn:incoming>
      <bpmn:outgoing>Flow_1kqxjia</bpmn:outgoing>
      <bpmn:outgoing>Flow_0n547gn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1kqxjia</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_01jguy9" sourceRef="GenerateDataDelegateTask" targetRef="CreateInstancesTask" />
    <bpmn:sequenceFlow id="Flow_0ae2gv8" sourceRef="CreateInstancesTask" targetRef="Gateway_1nrrv7h" />
    <bpmn:sequenceFlow id="Flow_1kqxjia" name="Yes" sourceRef="Gateway_1nrrv7h" targetRef="orderExecEnd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${allCreated}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0n547gn" name="no" sourceRef="Gateway_1nrrv7h" targetRef="CreateInstancesTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${not allCreated}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0fuzl5a" default="Flow_0wqmtl8">
      <bpmn:incoming>Flow_0vvv1ml</bpmn:incoming>
      <bpmn:outgoing>Flow_1pgh3s3</bpmn:outgoing>
      <bpmn:outgoing>Flow_0wqmtl8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1fi99c8">
      <bpmn:incoming>Flow_1pgh3s3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="OrderEnrichment" name="Order Enrichment Delegate" camunda:asyncBefore="true" camunda:delegateExpression="${orderEnrichmentDelegate}">
      <bpmn:incoming>Flow_0hlk96z</bpmn:incoming>
      <bpmn:outgoing>Flow_0vvv1ml</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0vvv1ml" sourceRef="OrderEnrichment" targetRef="Gateway_0fuzl5a" />
    <bpmn:sequenceFlow id="Flow_1pgh3s3" name="Failure" sourceRef="Gateway_0fuzl5a" targetRef="Event_1fi99c8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1qlzup1" default="Flow_1lfo7gs">
      <bpmn:incoming>Flow_0labfo8</bpmn:incoming>
      <bpmn:outgoing>Flow_14srxzz</bpmn:outgoing>
      <bpmn:outgoing>Flow_1lfo7gs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0mtkrbm">
      <bpmn:incoming>Flow_14srxzz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:callActivity id="PaymentWorkflow" name="Payment and future order wokrflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0wqmtl8</bpmn:incoming>
      <bpmn:outgoing>Flow_0labfo8</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0labfo8" sourceRef="PaymentWorkflow" targetRef="Gateway_1qlzup1" />
    <bpmn:sequenceFlow id="Flow_14srxzz" name="Failure" sourceRef="Gateway_1qlzup1" targetRef="Event_0mtkrbm">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0wqmtl8" sourceRef="Gateway_0fuzl5a" targetRef="PaymentWorkflow" />
    <bpmn:sequenceFlow id="Flow_1lfo7gs" sourceRef="Gateway_1qlzup1" targetRef="GenerateDataDelegateTask" />
  </bpmn:process>
  <bpmn:message id="Message_061isi2" name="MakePaymentCallback" />
  <bpmn:message id="Message_3ven5fh" name="SuspendWait" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="AddService">
      <bpmndi:BPMNEdge id="Flow_0wqmtl8_di" bpmnElement="Flow_0wqmtl8">
        <di:waypoint x="555" y="190" />
        <di:waypoint x="670" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lfo7gs_di" bpmnElement="Flow_1lfo7gs">
        <di:waypoint x="1005" y="190" />
        <di:waypoint x="1118" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hlk96z_di" bpmnElement="Flow_0hlk96z">
        <di:waypoint x="188" y="190" />
        <di:waypoint x="300" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vvv1ml_di" bpmnElement="Flow_0vvv1ml">
        <di:waypoint x="400" y="190" />
        <di:waypoint x="505" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pgh3s3_di" bpmnElement="Flow_1pgh3s3">
        <di:waypoint x="530" y="215" />
        <di:waypoint x="530" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="533" y="235" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0labfo8_di" bpmnElement="Flow_0labfo8">
        <di:waypoint x="770" y="190" />
        <di:waypoint x="955" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14srxzz_di" bpmnElement="Flow_14srxzz">
        <di:waypoint x="980" y="215" />
        <di:waypoint x="980" y="332" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="980" y="271" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01jguy9_di" bpmnElement="Flow_01jguy9">
        <di:waypoint x="1218" y="190" />
        <di:waypoint x="1348" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n547gn_di" bpmnElement="Flow_0n547gn">
        <di:waypoint x="1558" y="215" />
        <di:waypoint x="1558" y="290" />
        <di:waypoint x="1398" y="290" />
        <di:waypoint x="1398" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1472" y="272" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ae2gv8_di" bpmnElement="Flow_0ae2gv8">
        <di:waypoint x="1448" y="190" />
        <di:waypoint x="1533" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kqxjia_di" bpmnElement="Flow_1kqxjia">
        <di:waypoint x="1583" y="190" />
        <di:waypoint x="1690" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1628" y="172" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_1x7dsnm_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="172" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fuzl5a_di" bpmnElement="Gateway_0fuzl5a" isMarkerVisible="true">
        <dc:Bounds x="505" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1fi99c8_di" bpmnElement="Event_1fi99c8">
        <dc:Bounds x="512" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01wyi3h_di" bpmnElement="OrderEnrichment">
        <dc:Bounds x="300" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1qlzup1_di" bpmnElement="Gateway_1qlzup1" isMarkerVisible="true">
        <dc:Bounds x="955" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0mtkrbm_di" bpmnElement="Event_0mtkrbm">
        <dc:Bounds x="962" y="332" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0cmifwx_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="670" y="150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1my2bgd_di" bpmnElement="GenerateDataDelegateTask">
        <dc:Bounds x="1118" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1r0bov3_di" bpmnElement="CreateInstancesTask">
        <dc:Bounds x="1348" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1nrrv7h_di" bpmnElement="Gateway_1nrrv7h" isMarkerVisible="true">
        <dc:Bounds x="1533" y="165" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1527" y="126" width="62" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0gn54oi_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="1690" y="172" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
