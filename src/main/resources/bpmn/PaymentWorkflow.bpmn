<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_13klrq4" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.6.0">
  <bpmn:process id="PaymentWorkflow" name="Payment Workflow" isExecutable="true" camunda:jobPriority="${priority}">
    <bpmn:startEvent id="StartEvent_1" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_1vs4xwr</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:receiveTask id="PaymentCallbackWait" name="Waiyt for payment callback" camunda:asyncBefore="true" messageRef="Message_0n8g0zw">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_00o8y8p</bpmn:incoming>
      <bpmn:outgoing>Flow_1bfz3rf</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_1bfz3rf" sourceRef="PaymentCallbackWait" targetRef="Gateway_0femecy" />
    <bpmn:endEvent id="EndEvent1">
      <bpmn:incoming>Flow_00asmfb</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1vs4xwr" sourceRef="StartEvent_1" targetRef="Gateway_17bzio7" />
    <bpmn:exclusiveGateway id="Gateway_0ifwh4m" name="check if future order required for the order">
      <bpmn:incoming>Flow_178wong</bpmn:incoming>
      <bpmn:outgoing>Flow_0u3s4mq</bpmn:outgoing>
      <bpmn:outgoing>Flow_1sqw42q</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0u3s4mq" name="yes" sourceRef="Gateway_0ifwh4m" targetRef="FutureOrderDateFinder">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${futureOrderRequired}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_02t5a58" sourceRef="FutureOrderTimer" targetRef="Gateway_1ymz09h" />
    <bpmn:sequenceFlow id="Flow_121rjsp" sourceRef="FutureOrderDateFinder" targetRef="Gateway_1or0lo9" />
    <bpmn:exclusiveGateway id="Gateway_1or0lo9" name="check if scheduling is required">
      <bpmn:incoming>Flow_121rjsp</bpmn:incoming>
      <bpmn:outgoing>Flow_0l2w6kp</bpmn:outgoing>
      <bpmn:outgoing>Flow_1m2labl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0l2w6kp" sourceRef="Gateway_1or0lo9" targetRef="FutureOrderTimer">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${orderSchedulingRequired}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:intermediateCatchEvent id="FutureOrderTimer" name="FutureOrderTimer" camunda:asyncBefore="true">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.FutureOrderExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.FutureOrderExecutionListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0l2w6kp</bpmn:incoming>
      <bpmn:outgoing>Flow_02t5a58</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_0u4urau">
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${orderExecutionDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:exclusiveGateway id="Gateway_1ymz09h">
      <bpmn:incoming>Flow_02t5a58</bpmn:incoming>
      <bpmn:incoming>Flow_11kl38b</bpmn:incoming>
      <bpmn:outgoing>Flow_00asmfb</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_00asmfb" sourceRef="Gateway_1ymz09h" targetRef="EndEvent1" />
    <bpmn:sequenceFlow id="Flow_1m2labl" sourceRef="Gateway_1or0lo9" targetRef="Gateway_0vvpmnf">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${!orderSchedulingRequired}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="FutureOrderDateFinder" name="order execution date finder" camunda:asyncBefore="true" camunda:delegateExpression="${futureOrderScheduleDateFinder}">
      <bpmn:incoming>Flow_0u3s4mq</bpmn:incoming>
      <bpmn:outgoing>Flow_121rjsp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0vvpmnf">
      <bpmn:incoming>Flow_1m2labl</bpmn:incoming>
      <bpmn:incoming>Flow_1sqw42q</bpmn:incoming>
      <bpmn:outgoing>Flow_11kl38b</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_11kl38b" sourceRef="Gateway_0vvpmnf" targetRef="Gateway_1ymz09h" />
    <bpmn:sequenceFlow id="Flow_1sqw42q" sourceRef="Gateway_0ifwh4m" targetRef="Gateway_0vvpmnf">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${!futureOrderRequired}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_17bzio7" name="check if upfront payment applicable">
      <bpmn:incoming>Flow_1vs4xwr</bpmn:incoming>
      <bpmn:outgoing>Flow_00o8y8p</bpmn:outgoing>
      <bpmn:outgoing>Flow_10upwhl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_00o8y8p" sourceRef="Gateway_17bzio7" targetRef="PaymentCallbackWait">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${paymentCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0femecy">
      <bpmn:incoming>Flow_1bfz3rf</bpmn:incoming>
      <bpmn:incoming>Flow_10upwhl</bpmn:incoming>
      <bpmn:outgoing>Flow_178wong</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_178wong" sourceRef="Gateway_0femecy" targetRef="Gateway_0ifwh4m" />
    <bpmn:sequenceFlow id="Flow_10upwhl" sourceRef="Gateway_17bzio7" targetRef="Gateway_0femecy">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${!paymentCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:textAnnotation id="TextAnnotation_0nf2t33">
      <bpmn:text>write and configure start and end listener classes to update the order status</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1fpk2fe" sourceRef="FutureOrderTimer" targetRef="TextAnnotation_0nf2t33" />
  </bpmn:process>
  <bpmn:message id="Message_0n8g0zw" name="paymentCallBack" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="PaymentWorkflow">
      <bpmndi:BPMNEdge id="Flow_10upwhl_di" bpmnElement="Flow_10upwhl">
        <di:waypoint x="290" y="235" />
        <di:waypoint x="290" y="160" />
        <di:waypoint x="610" y="160" />
        <di:waypoint x="610" y="235" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_178wong_di" bpmnElement="Flow_178wong">
        <di:waypoint x="635" y="260" />
        <di:waypoint x="735" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00o8y8p_di" bpmnElement="Flow_00o8y8p">
        <di:waypoint x="315" y="260" />
        <di:waypoint x="390" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sqw42q_di" bpmnElement="Flow_1sqw42q">
        <di:waypoint x="760" y="235" />
        <di:waypoint x="760" y="80" />
        <di:waypoint x="1170" y="80" />
        <di:waypoint x="1170" y="135" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11kl38b_di" bpmnElement="Flow_11kl38b">
        <di:waypoint x="1195" y="160" />
        <di:waypoint x="1310" y="160" />
        <di:waypoint x="1310" y="235" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m2labl_di" bpmnElement="Flow_1m2labl">
        <di:waypoint x="1070" y="235" />
        <di:waypoint x="1070" y="160" />
        <di:waypoint x="1145" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00asmfb_di" bpmnElement="Flow_00asmfb">
        <di:waypoint x="1335" y="260" />
        <di:waypoint x="1412" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0l2w6kp_di" bpmnElement="Flow_0l2w6kp">
        <di:waypoint x="1095" y="260" />
        <di:waypoint x="1182" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_121rjsp_di" bpmnElement="Flow_121rjsp">
        <di:waypoint x="970" y="260" />
        <di:waypoint x="1045" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02t5a58_di" bpmnElement="Flow_02t5a58">
        <di:waypoint x="1218" y="260" />
        <di:waypoint x="1285" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u3s4mq_di" bpmnElement="Flow_0u3s4mq">
        <di:waypoint x="785" y="260" />
        <di:waypoint x="870" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="819" y="242" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vs4xwr_di" bpmnElement="Flow_1vs4xwr">
        <di:waypoint x="188" y="260" />
        <di:waypoint x="265" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bfz3rf_di" bpmnElement="Flow_1bfz3rf">
        <di:waypoint x="490" y="260" />
        <di:waypoint x="585" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="242" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0nu605d_di" bpmnElement="PaymentCallbackWait">
        <dc:Bounds x="390" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0bqwyn6_di" bpmnElement="EndEvent1">
        <dc:Bounds x="1412" y="242" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ifwh4m_di" bpmnElement="Gateway_0ifwh4m" isMarkerVisible="true">
        <dc:Bounds x="735" y="235" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="717" y="292" width="87" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1or0lo9_di" bpmnElement="Gateway_1or0lo9" isMarkerVisible="true">
        <dc:Bounds x="1045" y="235" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1038" y="292" width="65" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_11zkf3t_di" bpmnElement="FutureOrderTimer">
        <dc:Bounds x="1182" y="242" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1161" y="285" width="89" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ymz09h_di" bpmnElement="Gateway_1ymz09h" isMarkerVisible="true">
        <dc:Bounds x="1285" y="235" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1lywktw_di" bpmnElement="FutureOrderDateFinder">
        <dc:Bounds x="870" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vvpmnf_di" bpmnElement="Gateway_0vvpmnf" isMarkerVisible="true">
        <dc:Bounds x="1145" y="135" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_17bzio7_di" bpmnElement="Gateway_17bzio7" isMarkerVisible="true">
        <dc:Bounds x="265" y="235" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="252" y="292" width="76" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0femecy_di" bpmnElement="Gateway_0femecy" isMarkerVisible="true">
        <dc:Bounds x="585" y="235" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0nf2t33_di" bpmnElement="TextAnnotation_0nf2t33">
        <dc:Bounds x="1210" y="320" width="100" height="98" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_1fpk2fe_di" bpmnElement="Association_1fpk2fe">
        <di:waypoint x="1211" y="274" />
        <di:waypoint x="1248" y="320" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
