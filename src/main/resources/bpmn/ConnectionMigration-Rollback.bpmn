<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="Definitions_0xt6om3" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="ConnectionMigration-Rollback" name="ConnectionMigration-Rollback" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>SequenceFlow_1cul665</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0vgw1x3" sourceRef="BSDeleteAccount" targetRef="EndEvent_17dzzi8" />
    <bpmn:endEvent id="EndEvent_17dzzi8">
      <bpmn:incoming>SequenceFlow_0vgw1x3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSDeleteAccount" name="BS Delete Account" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_1cul665</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0vgw1x3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1cul665" sourceRef="StartEvent_1" targetRef="BSDeleteAccount" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ConnectionMigration-Rollback">
      <bpmndi:BPMNEdge id="SequenceFlow_1cul665_di" bpmnElement="SequenceFlow_1cul665">
        <di:waypoint x="192" y="121" />
        <di:waypoint x="253" y="121" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0vgw1x3_di" bpmnElement="SequenceFlow_0vgw1x3">
        <di:waypoint x="353" y="121" />
        <di:waypoint x="396" y="121" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="156" y="103" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_17dzzi8_di" bpmnElement="EndEvent_17dzzi8">
        <dc:Bounds x="396" y="103" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1wbapye_di" bpmnElement="BSDeleteAccount">
        <dc:Bounds x="253" y="81" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
