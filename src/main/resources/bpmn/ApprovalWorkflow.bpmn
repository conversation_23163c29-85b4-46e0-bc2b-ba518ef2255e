<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_13klrq4" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.5.1">
  <bpmn:process id="ApprovalWorkflow" name="Approval Workflow" isExecutable="true" camunda:jobPriority="${priority}">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0whcyxa</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="subProcessEndEvent_1">
      <bpmn:incoming>Flow_1ef17hy</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0whcyxa" sourceRef="StartEvent_1" targetRef="Approval" />
    <bpmn:serviceTask id="Approval" name="Approval Handler" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0whcyxa</bpmn:incoming>
      <bpmn:outgoing>Flow_1ef17hy</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1ef17hy" sourceRef="Approval" targetRef="subProcessEndEvent_1" />
  </bpmn:process>
  <bpmn:message id="Message_0n8g0zw" name="ApprovalCallBack" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ApprovalWorkflow">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="156" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0bqwyn6_di" bpmnElement="subProcessEndEvent_1">
        <dc:Bounds x="482" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0t1i0hn_di" bpmnElement="Approval">
        <dc:Bounds x="270" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0whcyxa_di" bpmnElement="Flow_0whcyxa">
        <di:waypoint x="192" y="117" />
        <di:waypoint x="270" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ef17hy_di" bpmnElement="Flow_1ef17hy">
        <di:waypoint x="370" y="117" />
        <di:waypoint x="482" y="117" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
