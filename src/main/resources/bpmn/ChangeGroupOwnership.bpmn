<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.28.0">
  <bpmn:process id="ChangeGroupOwnership" name="Change Group Ownership" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="Event_1hjhhcv">
      <bpmn:outgoing>Flow_125fo0m</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1ogj2lu" sourceRef="BSFetchShareBundleDetails" targetRef="Gateway_1ymtcmy" />
    <bpmn:exclusiveGateway id="Gateway_0vvd1dw">
      <bpmn:incoming>Flow_116r5g8</bpmn:incoming>
      <bpmn:outgoing>Flow_0ety70b</bpmn:outgoing>
      <bpmn:outgoing>Flow_0dc73kp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_116r5g8" sourceRef="BS_ChangeOwnership" targetRef="Gateway_0vvd1dw" />
    <bpmn:endEvent id="Event_1c6l7ml">
      <bpmn:incoming>Flow_0ety70b</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0ety70b" name="Failure" sourceRef="Gateway_0vvd1dw" targetRef="Event_1c6l7ml">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="OCS_ChnageOwnership" name="OCS_Chnage_Ownership" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0dc73kp</bpmn:incoming>
      <bpmn:outgoing>Flow_0x790bn</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BS_ChangeOwnership" name="BS_Change_Ownership" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_09pvrgw</bpmn:incoming>
      <bpmn:outgoing>Flow_116r5g8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSFetchShareBundleDetails" name="BS_FetchShareBundleDetails" camunda:asyncBefore="true" camunda:delegateExpression="${bsFetchShareBundleDetails}">
      <bpmn:incoming>Flow_125fo0m</bpmn:incoming>
      <bpmn:outgoing>Flow_1ogj2lu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="subProcessEndEvent_2">
      <bpmn:incoming>Flow_1xxwjom</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_125fo0m" sourceRef="Event_1hjhhcv" targetRef="BSFetchShareBundleDetails" />
    <bpmn:endEvent id="Event_052uwjo">
      <bpmn:incoming>Flow_1x6vcfz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0x790bn" sourceRef="OCS_ChnageOwnership" targetRef="Gateway_0eb5jrb" />
    <bpmn:exclusiveGateway id="Gateway_0eb5jrb">
      <bpmn:incoming>Flow_0x790bn</bpmn:incoming>
      <bpmn:outgoing>Flow_1x6vcfz</bpmn:outgoing>
      <bpmn:outgoing>Flow_00hamqj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1x6vcfz" name="Failure" sourceRef="Gateway_0eb5jrb" targetRef="Event_052uwjo">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_01h3u99">
      <bpmn:incoming>Flow_04nht6c</bpmn:incoming>
      <bpmn:incoming>Flow_00hamqj</bpmn:incoming>
      <bpmn:outgoing>Flow_0atxhyi</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0atxhyi" sourceRef="Gateway_01h3u99" targetRef="Gateway_12v1urn" />
    <bpmn:exclusiveGateway id="Gateway_1ymtcmy">
      <bpmn:incoming>Flow_1ogj2lu</bpmn:incoming>
      <bpmn:outgoing>Flow_0b0mp0u</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bmqatp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_01g9025">
      <bpmn:incoming>Flow_0b0mp0u</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0b0mp0u" name="Failure" sourceRef="Gateway_1ymtcmy" targetRef="Event_01g9025">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1dlju0d" name="TypeOfMsisdn-sponsered" default="Flow_04nht6c">
      <bpmn:incoming>Flow_0bmqatp</bpmn:incoming>
      <bpmn:outgoing>Flow_04nht6c</bpmn:outgoing>
      <bpmn:outgoing>Flow_09pvrgw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0bmqatp" sourceRef="Gateway_1ymtcmy" targetRef="Gateway_1dlju0d">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_04nht6c" sourceRef="Gateway_1dlju0d" targetRef="Gateway_01h3u99" />
    <bpmn:serviceTask id="BSDeleteShareBundle" name="BS_DeleteShareBundle" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_00ujxfr</bpmn:incoming>
      <bpmn:outgoing>Flow_0b55e0b</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0b55e0b" sourceRef="BSDeleteShareBundle" targetRef="Gateway_0sli9hc" />
    <bpmn:exclusiveGateway id="Gateway_12v1urn" name="is beneficiaryListEmpty=true" default="Flow_17r0nmk">
      <bpmn:incoming>Flow_0atxhyi</bpmn:incoming>
      <bpmn:outgoing>Flow_00ujxfr</bpmn:outgoing>
      <bpmn:outgoing>Flow_17r0nmk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_00ujxfr" sourceRef="Gateway_12v1urn" targetRef="BSDeleteShareBundle">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isDeleteOwner}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0sli9hc">
      <bpmn:incoming>Flow_0b55e0b</bpmn:incoming>
      <bpmn:outgoing>Flow_1b6n7a9</bpmn:outgoing>
      <bpmn:outgoing>Flow_1t0bgkp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1b6n7a9" sourceRef="Gateway_0sli9hc" targetRef="Gateway_1f6qf8e">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1f6qf8e">
      <bpmn:incoming>Flow_1b6n7a9</bpmn:incoming>
      <bpmn:incoming>Flow_17r0nmk</bpmn:incoming>
      <bpmn:outgoing>Flow_1xxwjom</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1xxwjom" sourceRef="Gateway_1f6qf8e" targetRef="subProcessEndEvent_2" />
    <bpmn:endEvent id="Event_1fo929c">
      <bpmn:incoming>Flow_1t0bgkp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1t0bgkp" name="Failure" sourceRef="Gateway_0sli9hc" targetRef="Event_1fo929c">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_17r0nmk" sourceRef="Gateway_12v1urn" targetRef="Gateway_1f6qf8e" />
    <bpmn:sequenceFlow id="Flow_09pvrgw" sourceRef="Gateway_1dlju0d" targetRef="BS_ChangeOwnership">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.workflowData").element().hasProp('BSFetchShareBundleDetailsResponseAttributes') &amp;&amp; workflowData.jsonPath("$.workflowData.BSFetchShareBundleDetailsResponseAttributes").element().hasProp('typeOfMsisdn') &amp;&amp; workflowData.jsonPath("$.workflowData.BSFetchShareBundleDetailsResponseAttributes.typeOfMsisdn").element().value() == 'Sponsored' &amp;&amp; workflowData.jsonPath("$.workflowData.BSFetchShareBundleDetailsResponseAttributes").element().hasProp('beneficiaryMsisdn')}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0dc73kp" sourceRef="Gateway_0vvd1dw" targetRef="OCS_ChnageOwnership">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_00hamqj" sourceRef="Gateway_0eb5jrb" targetRef="Gateway_01h3u99">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_18lupa4" name="MNPCallBack" />
  <bpmn:message id="Message_0cxkag2" />
  <bpmn:message id="Message_0uyo7lb" />
  <bpmn:message id="Message_0z0mmot" name="MNPCallBack" />
  <bpmn:message id="Message_14c63bs" name="SOM_Callback" />
  <bpmn:message id="Message_0qgbj08" name="ConnectServiceCallback" />
  <bpmn:message id="Message_1fyqyx3" name="SOMCancelSubCallback" />
  <bpmn:message id="Message_08uiznb" name="SOMAddSubCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ChangeGroupOwnership">
      <bpmndi:BPMNShape id="Event_1hjhhcv_di" bpmnElement="Event_1hjhhcv">
        <dc:Bounds x="152" y="272" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vvd1dw_di" bpmnElement="Gateway_0vvd1dw" isMarkerVisible="true">
        <dc:Bounds x="1015" y="265" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1c6l7ml_di" bpmnElement="Event_1c6l7ml">
        <dc:Bounds x="1022" y="422" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ldqej4_di" bpmnElement="OCS_ChnageOwnership">
        <dc:Bounds x="1240" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0cll812_di" bpmnElement="BS_ChangeOwnership">
        <dc:Bounds x="790" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_06ca3dv_di" bpmnElement="BSFetchShareBundleDetails">
        <dc:Bounds x="280" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1bzcfnf" bpmnElement="subProcessEndEvent_2">
        <dc:Bounds x="2912" y="272" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_052uwjo_di" bpmnElement="Event_052uwjo">
        <dc:Bounds x="1472" y="422" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0eb5jrb_di" bpmnElement="Gateway_0eb5jrb" isMarkerVisible="true">
        <dc:Bounds x="1465" y="265" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0cz0lsl" bpmnElement="Gateway_01h3u99" isMarkerVisible="true">
        <dc:Bounds x="1695" y="265" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1jk8h0w" bpmnElement="Gateway_1ymtcmy" isMarkerVisible="true">
        <dc:Bounds x="485" y="265" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0do1n1c" bpmnElement="Event_01g9025">
        <dc:Bounds x="492" y="422" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0j4fupi" bpmnElement="Gateway_1dlju0d" isMarkerVisible="true">
        <dc:Bounds x="615" y="265" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="605" y="322" width="75" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1c8m9uh" bpmnElement="Gateway_12v1urn" isMarkerVisible="true">
        <dc:Bounds x="1945" y="265" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1932" y="322" width="87" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1oi5gb8" bpmnElement="Gateway_0sli9hc" isMarkerVisible="true">
        <dc:Bounds x="2405" y="265" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0o5eho5" bpmnElement="Gateway_1f6qf8e" isMarkerVisible="true">
        <dc:Bounds x="2645" y="265" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ge9fvv" bpmnElement="Event_1fo929c">
        <dc:Bounds x="2412" y="432" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0q60l6v" bpmnElement="BSDeleteShareBundle">
        <dc:Bounds x="2150" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1ogj2lu_di" bpmnElement="Flow_1ogj2lu">
        <di:waypoint x="380" y="290" />
        <di:waypoint x="485" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_116r5g8_di" bpmnElement="Flow_116r5g8">
        <di:waypoint x="890" y="290" />
        <di:waypoint x="1015" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ety70b_di" bpmnElement="Flow_0ety70b">
        <di:waypoint x="1040" y="315" />
        <di:waypoint x="1040" y="422" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1063" y="362" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_125fo0m_di" bpmnElement="Flow_125fo0m">
        <di:waypoint x="188" y="290" />
        <di:waypoint x="280" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x790bn_di" bpmnElement="Flow_0x790bn">
        <di:waypoint x="1340" y="290" />
        <di:waypoint x="1465" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1x6vcfz_di" bpmnElement="Flow_1x6vcfz">
        <di:waypoint x="1490" y="315" />
        <di:waypoint x="1490" y="422" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1503" y="362" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0atxhyi_di" bpmnElement="Flow_0atxhyi">
        <di:waypoint x="1745" y="290" />
        <di:waypoint x="1945" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b0mp0u_di" bpmnElement="Flow_0b0mp0u">
        <di:waypoint x="510" y="315" />
        <di:waypoint x="510" y="422" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="509" y="368" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bmqatp_di" bpmnElement="Flow_0bmqatp">
        <di:waypoint x="535" y="290" />
        <di:waypoint x="615" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04nht6c_di" bpmnElement="Flow_04nht6c">
        <di:waypoint x="640" y="265" />
        <di:waypoint x="640" y="80" />
        <di:waypoint x="1720" y="80" />
        <di:waypoint x="1720" y="265" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b55e0b_di" bpmnElement="Flow_0b55e0b">
        <di:waypoint x="2250" y="290" />
        <di:waypoint x="2405" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00ujxfr_di" bpmnElement="Flow_00ujxfr">
        <di:waypoint x="1995" y="290" />
        <di:waypoint x="2150" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b6n7a9_di" bpmnElement="Flow_1b6n7a9">
        <di:waypoint x="2455" y="290" />
        <di:waypoint x="2645" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xxwjom_di" bpmnElement="Flow_1xxwjom">
        <di:waypoint x="2695" y="290" />
        <di:waypoint x="2912" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t0bgkp_di" bpmnElement="Flow_1t0bgkp">
        <di:waypoint x="2430" y="315" />
        <di:waypoint x="2430" y="432" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2442" y="361" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17r0nmk_di" bpmnElement="Flow_17r0nmk">
        <di:waypoint x="1970" y="265" />
        <di:waypoint x="1970" y="140" />
        <di:waypoint x="2670" y="140" />
        <di:waypoint x="2670" y="265" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09pvrgw_di" bpmnElement="Flow_09pvrgw">
        <di:waypoint x="665" y="290" />
        <di:waypoint x="790" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dc73kp_di" bpmnElement="Flow_0dc73kp">
        <di:waypoint x="1065" y="290" />
        <di:waypoint x="1240" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00hamqj_di" bpmnElement="Flow_00hamqj">
        <di:waypoint x="1515" y="290" />
        <di:waypoint x="1695" y="290" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
