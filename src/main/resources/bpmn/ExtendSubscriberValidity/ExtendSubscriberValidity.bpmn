<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1pxbtg1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="ExtendSubscriberValidity" name="Extend Subscriber Validity" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:serviceTask id="NCCExtendSubscriberValidity" name="Extend Subscriber Validity in NCC" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0y8mect</bpmn:incoming>
      <bpmn:outgoing>Flow_155dtmw</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0y8mect</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_155dtmw</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_155dtmw" sourceRef="NCCExtendSubscriberValidity" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_0y8mect" sourceRef="orderExecStart" targetRef="NCCExtendSubscriberValidity" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ExtendSubscriberValidity">
      <bpmndi:BPMNEdge id="Flow_0y8mect_di" bpmnElement="Flow_0y8mect">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="270" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_155dtmw_di" bpmnElement="Flow_155dtmw">
        <di:waypoint x="370" y="120" />
        <di:waypoint x="442" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0f14l3b_di" bpmnElement="NCCExtendSubscriberValidity">
        <dc:Bounds x="270" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_188qqt3_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0avp6f6_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="442" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
