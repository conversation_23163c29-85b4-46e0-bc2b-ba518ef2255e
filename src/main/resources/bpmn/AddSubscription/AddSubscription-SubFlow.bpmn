<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_077tjnv" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.8.0">
  <bpmn:process id="AddSubscription-SubFlow" name="AddSubscription-SubFlow" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="start" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_12egj9s</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="OCSAddSubscription" name="OCS AddSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1kpmf4j</bpmn:incoming>
      <bpmn:outgoing>Flow_0xei7i4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1waufjf" default="Flow_1e2ou4m">
      <bpmn:incoming>Flow_135peys</bpmn:incoming>
      <bpmn:outgoing>Flow_0yut1v0</bpmn:outgoing>
      <bpmn:outgoing>Flow_1e2ou4m</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="SOMAddSubscription" name="SOM Add Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${somAddSubscription}">
      <bpmn:incoming>Flow_1pamgg0</bpmn:incoming>
      <bpmn:outgoing>Flow_1va6qqc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0obp3bm" default="Flow_0e1eccy">
      <bpmn:incoming>Flow_1va6qqc</bpmn:incoming>
      <bpmn:outgoing>Flow_0e1eccy</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ffbngx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:receiveTask id="SOMAddSubCallback" name="SOMAddSubCallback" camunda:asyncBefore="true" messageRef="Message_08uiznb">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOMAddSubscription</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOMAddSubscription" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0806fyj</bpmn:incoming>
      <bpmn:outgoing>Flow_1t4a0mn</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_0qq1wmn" default="Flow_0qrl0oj">
      <bpmn:incoming>Flow_1t4a0mn</bpmn:incoming>
      <bpmn:outgoing>Flow_0qrl0oj</bpmn:outgoing>
      <bpmn:outgoing>Flow_1nr4i1e</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_012z3x6">
      <bpmn:incoming>Flow_1nr4i1e</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_15t7ncl" name="check som call required or not&#10;&#10;" default="Flow_1rosmmj">
      <bpmn:incoming>Flow_1ai4fjf</bpmn:incoming>
      <bpmn:outgoing>Flow_1pamgg0</bpmn:outgoing>
      <bpmn:outgoing>Flow_1rosmmj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0rqbep1">
      <bpmn:incoming>Flow_1rosmmj</bpmn:incoming>
      <bpmn:incoming>Flow_1xwqjca</bpmn:incoming>
      <bpmn:outgoing>Flow_1c8oe0p</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1uv713u">
      <bpmn:incoming>Flow_0ffbngx</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1pamgg0" name="yes&#10;" sourceRef="Gateway_15t7ncl" targetRef="SOMAddSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${somCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1va6qqc" sourceRef="SOMAddSubscription" targetRef="Gateway_0obp3bm" />
    <bpmn:sequenceFlow id="Flow_0e1eccy" name="Success" sourceRef="Gateway_0obp3bm" targetRef="Gateway_1nfkizz" />
    <bpmn:sequenceFlow id="Flow_0ffbngx" name="Failure" sourceRef="Gateway_0obp3bm" targetRef="Event_1uv713u">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1t4a0mn" sourceRef="SOMAddSubCallback" targetRef="Gateway_0qq1wmn" />
    <bpmn:sequenceFlow id="Flow_0qrl0oj" name="Success" sourceRef="Gateway_0qq1wmn" targetRef="Gateway_1y7skge" />
    <bpmn:sequenceFlow id="Flow_1nr4i1e" name="Failure" sourceRef="Gateway_0qq1wmn" targetRef="Event_012z3x6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1rosmmj" name="no" sourceRef="Gateway_15t7ncl" targetRef="Gateway_0rqbep1" />
    <bpmn:serviceTask id="BSAddSubscription" name="AddSubscription in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0mb01dr</bpmn:incoming>
      <bpmn:outgoing>Flow_135peys</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_135peys" sourceRef="BSAddSubscription" targetRef="Gateway_1waufjf" />
    <bpmn:endEvent id="Event_0n98exd">
      <bpmn:incoming>Flow_0yut1v0</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0yut1v0" name="Failure" sourceRef="Gateway_1waufjf" targetRef="Event_0n98exd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="subProcessEndEvent">
      <bpmn:incoming>Flow_0d0f5ur</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0xei7i4" sourceRef="OCSAddSubscription" targetRef="Gateway_1q6ay9z" />
    <bpmn:serviceTask id="UpdateSubscriptionStatus" name="Update Subscription Status in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1qwks43</bpmn:incoming>
      <bpmn:outgoing>Flow_0ztpxi8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1q6ay9z" default="Flow_164qirw">
      <bpmn:incoming>Flow_0xei7i4</bpmn:incoming>
      <bpmn:outgoing>Flow_164qirw</bpmn:outgoing>
      <bpmn:outgoing>Flow_0hoh8k3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_164qirw" sourceRef="Gateway_1q6ay9z" targetRef="Gateway_0n2a28d" />
    <bpmn:endEvent id="Event_12acb6n">
      <bpmn:incoming>Flow_0hoh8k3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0hoh8k3" sourceRef="Gateway_1q6ay9z" targetRef="Event_12acb6n">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0hwlalo" default="Flow_1q8v5yn">
      <bpmn:incoming>Flow_1ncorv6</bpmn:incoming>
      <bpmn:outgoing>Flow_1kpmf4j</bpmn:outgoing>
      <bpmn:outgoing>Flow_1q8v5yn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0n2a28d">
      <bpmn:incoming>Flow_164qirw</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0e380ig</bpmn:incoming>
      <bpmn:outgoing>Flow_1qwks43</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1qwks43" sourceRef="Gateway_0n2a28d" targetRef="UpdateSubscriptionStatus" />
    <bpmn:sequenceFlow id="Flow_1kpmf4j" name="ocs call reqd" sourceRef="Gateway_0hwlalo" targetRef="OCSAddSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${ocsCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1q8v5yn" sourceRef="Gateway_0hwlalo" targetRef="OCSUpdateSubscription" />
    <bpmn:sequenceFlow id="SequenceFlow_0se8e7m" sourceRef="OCSUpdateSubscription" targetRef="ExclusiveGateway_13uwpup" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_13uwpup" default="SequenceFlow_0e380ig">
      <bpmn:incoming>SequenceFlow_0se8e7m</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0e380ig</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1atlue2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0e380ig" sourceRef="ExclusiveGateway_13uwpup" targetRef="Gateway_0n2a28d" />
    <bpmn:endEvent id="EndEvent_0i96d52">
      <bpmn:incoming>SequenceFlow_1atlue2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_1atlue2" sourceRef="ExclusiveGateway_13uwpup" targetRef="EndEvent_0i96d52">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="OCSUpdateSubscription" name="OCS Update Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1q8v5yn</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0se8e7m</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1mgeoz9">
      <bpmn:incoming>Flow_1e2ou4m</bpmn:incoming>
      <bpmn:incoming>Flow_1syvvi7</bpmn:incoming>
      <bpmn:outgoing>Flow_1ai4fjf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1e2ou4m" sourceRef="Gateway_1waufjf" targetRef="Gateway_1mgeoz9" />
    <bpmn:sequenceFlow id="Flow_1ai4fjf" sourceRef="Gateway_1mgeoz9" targetRef="Gateway_15t7ncl" />
    <bpmn:exclusiveGateway id="Gateway_01qbs7n" default="Flow_0mb01dr">
      <bpmn:incoming>Flow_12egj9s</bpmn:incoming>
      <bpmn:outgoing>Flow_0mb01dr</bpmn:outgoing>
      <bpmn:outgoing>Flow_1syvvi7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_12egj9s" sourceRef="start" targetRef="Gateway_01qbs7n" />
    <bpmn:sequenceFlow id="Flow_0mb01dr" sourceRef="Gateway_01qbs7n" targetRef="BSAddSubscription" />
    <bpmn:sequenceFlow id="Flow_1syvvi7" sourceRef="Gateway_01qbs7n" targetRef="Gateway_1mgeoz9">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isLegacy}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_15flrgg">
      <bpmn:incoming>Flow_0ztpxi8</bpmn:incoming>
      <bpmn:incoming>Flow_1r08t9f</bpmn:incoming>
      <bpmn:outgoing>Flow_0g9yr3n</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ztpxi8" sourceRef="UpdateSubscriptionStatus" targetRef="Gateway_15flrgg" />
    <bpmn:sequenceFlow id="Flow_0g9yr3n" sourceRef="Gateway_15flrgg" targetRef="Gateway_0ao8uuu" />
    <bpmn:exclusiveGateway id="Gateway_01ffe4k" default="Flow_1ncorv6">
      <bpmn:incoming>Flow_1c8oe0p</bpmn:incoming>
      <bpmn:outgoing>Flow_1ncorv6</bpmn:outgoing>
      <bpmn:outgoing>Flow_1r08t9f</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1c8oe0p" sourceRef="Gateway_0rqbep1" targetRef="Gateway_01ffe4k" />
    <bpmn:sequenceFlow id="Flow_1ncorv6" sourceRef="Gateway_01ffe4k" targetRef="Gateway_0hwlalo" />
    <bpmn:sequenceFlow id="Flow_1r08t9f" sourceRef="Gateway_01ffe4k" targetRef="Gateway_15flrgg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isLegacy}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0ao8uuu" name="IsEkycCallreqd =true" default="Flow_1puz5d6">
      <bpmn:incoming>Flow_0g9yr3n</bpmn:incoming>
      <bpmn:outgoing>Flow_1puz5d6</bpmn:outgoing>
      <bpmn:outgoing>Flow_1roie8i</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1sad128">
      <bpmn:incoming>Flow_1ecbqza</bpmn:incoming>
      <bpmn:incoming>Flow_1puz5d6</bpmn:incoming>
      <bpmn:outgoing>Flow_0d0f5ur</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0d0f5ur" sourceRef="Gateway_1sad128" targetRef="subProcessEndEvent" />
    <bpmn:exclusiveGateway id="Gateway_0806vak" default="Flow_1ecbqza">
      <bpmn:incoming>Flow_0dgi606</bpmn:incoming>
      <bpmn:outgoing>Flow_1ecbqza</bpmn:outgoing>
      <bpmn:outgoing>Flow_0xmbsas</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ecbqza" sourceRef="Gateway_0806vak" targetRef="Gateway_1sad128" />
    <bpmn:serviceTask id="ESBEkycUpdate" name="ESB_EkycUpdate" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1roie8i</bpmn:incoming>
      <bpmn:outgoing>Flow_0dgi606</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0dgi606" sourceRef="ESBEkycUpdate" targetRef="Gateway_0806vak" />
    <bpmn:sequenceFlow id="Flow_1puz5d6" sourceRef="Gateway_0ao8uuu" targetRef="Gateway_1sad128" />
    <bpmn:endEvent id="Event_0r4l8sp">
      <bpmn:incoming>Flow_0xmbsas</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0xmbsas" name="Failure" sourceRef="Gateway_0806vak" targetRef="Event_0r4l8sp">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1roie8i" sourceRef="Gateway_0ao8uuu" targetRef="ESBEkycUpdate">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.workflowData").element().hasProp('currentExecution')&amp;&amp; workflowData.jsonPath("$.workflowData.currentExecution.executionData.productOffering").element().hasProp('isKycRequired') &amp;&amp; workflowData.jsonPath("$.workflowData.currentExecution.executionData.productOffering.isKycRequired").stringValue() == 'true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1nfkizz" camunda:asyncBefore="true" default="Flow_1ryhx4c">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.CallBackExecutionListener" event="start" />
        <camunda:properties>
          <camunda:property name="callBackType" value="SOMCallback" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0e1eccy</bpmn:incoming>
      <bpmn:outgoing>Flow_0806fyj</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ryhx4c</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0806fyj" sourceRef="Gateway_1nfkizz" targetRef="SOMAddSubCallback">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callBackProcessReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1y7skge">
      <bpmn:incoming>Flow_0qrl0oj</bpmn:incoming>
      <bpmn:incoming>Flow_1ryhx4c</bpmn:incoming>
      <bpmn:outgoing>Flow_1xwqjca</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1xwqjca" sourceRef="Gateway_1y7skge" targetRef="Gateway_0rqbep1" />
    <bpmn:sequenceFlow id="Flow_1ryhx4c" sourceRef="Gateway_1nfkizz" targetRef="Gateway_1y7skge" />
  </bpmn:process>
  <bpmn:message id="Message_08uiznb" name="SOMAddSubCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="AddSubscription-SubFlow">
      <bpmndi:BPMNShape id="Event_1ccvmjb_di" bpmnElement="subProcessEndEvent">
        <dc:Bounds x="3382" y="459" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_17gebup" bpmnElement="Gateway_0ao8uuu" isMarkerVisible="true">
        <dc:Bounds x="2815" y="452" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2803" y="509" width="74" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1wdwwyz" bpmnElement="Gateway_1sad128" isMarkerVisible="true">
        <dc:Bounds x="3265" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0wqi23f" bpmnElement="Gateway_0806vak" isMarkerVisible="true">
        <dc:Bounds x="3145" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0mllxzq" bpmnElement="ESBEkycUpdate">
        <dc:Bounds x="2960" y="437" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0yjxalb" bpmnElement="Event_0r4l8sp">
        <dc:Bounds x="3152" y="584" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_15flrgg_di" bpmnElement="Gateway_15flrgg" isMarkerVisible="true">
        <dc:Bounds x="2705" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ahjyqs_di" bpmnElement="UpdateSubscriptionStatus">
        <dc:Bounds x="2540" y="437" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0n2a28d_di" bpmnElement="Gateway_0n2a28d" isMarkerVisible="true">
        <dc:Bounds x="2415" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1q6ay9z_di" bpmnElement="Gateway_1q6ay9z" isMarkerVisible="true">
        <dc:Bounds x="2295" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_12acb6n_di" bpmnElement="Event_12acb6n">
        <dc:Bounds x="2302" y="572" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0i96d52_di" bpmnElement="EndEvent_0i96d52">
        <dc:Bounds x="2302" y="348" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_13uwpup_di" bpmnElement="ExclusiveGateway_13uwpup" isMarkerVisible="true">
        <dc:Bounds x="2295" y="245" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_03ahm73_di" bpmnElement="OCSAddSubscription">
        <dc:Bounds x="2150" y="437" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1hs4f61_di" bpmnElement="OCSUpdateSubscription">
        <dc:Bounds x="2150" y="230" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0hwlalo_di" bpmnElement="Gateway_0hwlalo" isMarkerVisible="true">
        <dc:Bounds x="2025" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_01ffe4k_di" bpmnElement="Gateway_01ffe4k" isMarkerVisible="true">
        <dc:Bounds x="1925" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="start">
        <dc:Bounds x="152" y="459" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_01qbs7n_di" bpmnElement="Gateway_01qbs7n" isMarkerVisible="true">
        <dc:Bounds x="265" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0mkfgdb_di" bpmnElement="BSAddSubscription">
        <dc:Bounds x="390" y="437" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1waufjf_di" bpmnElement="Gateway_1waufjf" isMarkerVisible="true">
        <dc:Bounds x="565" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0n98exd_di" bpmnElement="Event_0n98exd">
        <dc:Bounds x="572" y="572" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1mgeoz9_di" bpmnElement="Gateway_1mgeoz9" isMarkerVisible="true">
        <dc:Bounds x="705" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_15t7ncl_di" bpmnElement="Gateway_15t7ncl" isMarkerVisible="true">
        <dc:Bounds x="815" y="452" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="813" y="521" width="73" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1blgwyz_di" bpmnElement="SOMAddSubscription">
        <dc:Bounds x="930" y="437" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0obp3bm_di" bpmnElement="Gateway_0obp3bm" isMarkerVisible="true">
        <dc:Bounds x="1085" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1uv713u_di" bpmnElement="Event_1uv713u">
        <dc:Bounds x="1092" y="549" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06r9clw" bpmnElement="Gateway_1nfkizz" isMarkerVisible="true">
        <dc:Bounds x="1245" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0jy41bt_di" bpmnElement="SOMAddSubCallback">
        <dc:Bounds x="1360" y="437" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0qq1wmn_di" bpmnElement="Gateway_0qq1wmn" isMarkerVisible="true">
        <dc:Bounds x="1515" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0rqbep1_di" bpmnElement="Gateway_0rqbep1" isMarkerVisible="true">
        <dc:Bounds x="1775" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_012z3x6_di" bpmnElement="Event_012z3x6">
        <dc:Bounds x="1522" y="549" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1x2vt1e" bpmnElement="Gateway_1y7skge" isMarkerVisible="true">
        <dc:Bounds x="1655" y="452" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0d0f5ur_di" bpmnElement="Flow_0d0f5ur">
        <di:waypoint x="3315" y="477" />
        <di:waypoint x="3382" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0g9yr3n_di" bpmnElement="Flow_0g9yr3n">
        <di:waypoint x="2755" y="477" />
        <di:waypoint x="2815" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1puz5d6_di" bpmnElement="Flow_1puz5d6">
        <di:waypoint x="2840" y="452" />
        <di:waypoint x="2840" y="380" />
        <di:waypoint x="3290" y="380" />
        <di:waypoint x="3290" y="452" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1roie8i_di" bpmnElement="Flow_1roie8i">
        <di:waypoint x="2865" y="477" />
        <di:waypoint x="2960" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ecbqza_di" bpmnElement="Flow_1ecbqza">
        <di:waypoint x="3195" y="477" />
        <di:waypoint x="3265" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dgi606_di" bpmnElement="Flow_0dgi606">
        <di:waypoint x="3060" y="477" />
        <di:waypoint x="3145" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xmbsas_di" bpmnElement="Flow_0xmbsas">
        <di:waypoint x="3170" y="502" />
        <di:waypoint x="3170" y="584" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3168" y="540" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ztpxi8_di" bpmnElement="Flow_0ztpxi8">
        <di:waypoint x="2640" y="477" />
        <di:waypoint x="2705" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r08t9f_di" bpmnElement="Flow_1r08t9f">
        <di:waypoint x="1950" y="452" />
        <di:waypoint x="1950" y="190" />
        <di:waypoint x="2730" y="190" />
        <di:waypoint x="2730" y="452" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qwks43_di" bpmnElement="Flow_1qwks43">
        <di:waypoint x="2465" y="477" />
        <di:waypoint x="2540" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_164qirw_di" bpmnElement="Flow_164qirw">
        <di:waypoint x="2345" y="477" />
        <di:waypoint x="2415" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0e380ig_di" bpmnElement="SequenceFlow_0e380ig">
        <di:waypoint x="2345" y="270" />
        <di:waypoint x="2440" y="270" />
        <di:waypoint x="2440" y="452" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xei7i4_di" bpmnElement="Flow_0xei7i4">
        <di:waypoint x="2250" y="477" />
        <di:waypoint x="2295" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hoh8k3_di" bpmnElement="Flow_0hoh8k3">
        <di:waypoint x="2320" y="502" />
        <di:waypoint x="2320" y="572" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1atlue2_di" bpmnElement="SequenceFlow_1atlue2">
        <di:waypoint x="2320" y="295" />
        <di:waypoint x="2320" y="348" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0se8e7m_di" bpmnElement="SequenceFlow_0se8e7m">
        <di:waypoint x="2250" y="270" />
        <di:waypoint x="2295" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kpmf4j_di" bpmnElement="Flow_1kpmf4j">
        <di:waypoint x="2075" y="477" />
        <di:waypoint x="2150" y="477" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2084" y="459" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1q8v5yn_di" bpmnElement="Flow_1q8v5yn">
        <di:waypoint x="2050" y="452" />
        <di:waypoint x="2050" y="270" />
        <di:waypoint x="2150" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ncorv6_di" bpmnElement="Flow_1ncorv6">
        <di:waypoint x="1975" y="477" />
        <di:waypoint x="2025" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c8oe0p_di" bpmnElement="Flow_1c8oe0p">
        <di:waypoint x="1825" y="477" />
        <di:waypoint x="1925" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12egj9s_di" bpmnElement="Flow_12egj9s">
        <di:waypoint x="188" y="477" />
        <di:waypoint x="265" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mb01dr_di" bpmnElement="Flow_0mb01dr">
        <di:waypoint x="315" y="477" />
        <di:waypoint x="390" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1syvvi7_di" bpmnElement="Flow_1syvvi7">
        <di:waypoint x="290" y="452" />
        <di:waypoint x="290" y="350" />
        <di:waypoint x="730" y="350" />
        <di:waypoint x="730" y="452" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_135peys_di" bpmnElement="Flow_135peys">
        <di:waypoint x="490" y="477" />
        <di:waypoint x="565" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yut1v0_di" bpmnElement="Flow_0yut1v0">
        <di:waypoint x="590" y="502" />
        <di:waypoint x="590" y="572" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="602" y="513" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1e2ou4m_di" bpmnElement="Flow_1e2ou4m">
        <di:waypoint x="615" y="477" />
        <di:waypoint x="705" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ai4fjf_di" bpmnElement="Flow_1ai4fjf">
        <di:waypoint x="755" y="477" />
        <di:waypoint x="815" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pamgg0_di" bpmnElement="Flow_1pamgg0">
        <di:waypoint x="865" y="477" />
        <di:waypoint x="930" y="477" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="863" y="463" width="18" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rosmmj_di" bpmnElement="Flow_1rosmmj">
        <di:waypoint x="840" y="452" />
        <di:waypoint x="840" y="300" />
        <di:waypoint x="1800" y="300" />
        <di:waypoint x="1800" y="452" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1246" y="85" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1va6qqc_di" bpmnElement="Flow_1va6qqc">
        <di:waypoint x="1030" y="477" />
        <di:waypoint x="1085" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e1eccy_di" bpmnElement="Flow_0e1eccy">
        <di:waypoint x="1135" y="477" />
        <di:waypoint x="1245" y="477" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1158" y="459" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ffbngx_di" bpmnElement="Flow_0ffbngx">
        <di:waypoint x="1110" y="502" />
        <di:waypoint x="1110" y="549" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1129" y="513" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0806fyj_di" bpmnElement="Flow_0806fyj">
        <di:waypoint x="1295" y="477" />
        <di:waypoint x="1360" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ryhx4c_di" bpmnElement="Flow_1ryhx4c">
        <di:waypoint x="1270" y="452" />
        <di:waypoint x="1270" y="380" />
        <di:waypoint x="1680" y="380" />
        <di:waypoint x="1680" y="452" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t4a0mn_di" bpmnElement="Flow_1t4a0mn">
        <di:waypoint x="1460" y="477" />
        <di:waypoint x="1515" y="477" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qrl0oj_di" bpmnElement="Flow_0qrl0oj">
        <di:waypoint x="1565" y="477" />
        <di:waypoint x="1655" y="477" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1578" y="459" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nr4i1e_di" bpmnElement="Flow_1nr4i1e">
        <di:waypoint x="1540" y="502" />
        <di:waypoint x="1540" y="549" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1548" y="510" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xwqjca_di" bpmnElement="Flow_1xwqjca">
        <di:waypoint x="1705" y="477" />
        <di:waypoint x="1775" y="477" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
