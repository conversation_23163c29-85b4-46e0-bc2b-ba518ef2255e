<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0">
  <bpmn:process id="AddSubscription" name="AddSubscription" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:serviceTask id="GenerateDataDelegateTask" name="Generate Data Delegate" camunda:delegateExpression="${generateDataDelegateAddSubscription}">
      <bpmn:incoming>Flow_006dlpa</bpmn:incoming>
      <bpmn:outgoing>Flow_16hv1kt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="CreateInstancesTask" name="Create Instances" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${createInstancesDelegateAddSubscription}">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="childProcess">AddSubscription-SubFlow</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0f89hcs</bpmn:incoming>
      <bpmn:incoming>Flow_16hv1kt</bpmn:incoming>
      <bpmn:outgoing>Flow_0npos96</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0r0djc8" name="All instances created ?">
      <bpmn:incoming>Flow_0npos96</bpmn:incoming>
      <bpmn:outgoing>Flow_0xrnaaw</bpmn:outgoing>
      <bpmn:outgoing>Flow_0f89hcs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0xrnaaw</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_16hv1kt" sourceRef="GenerateDataDelegateTask" targetRef="CreateInstancesTask" />
    <bpmn:sequenceFlow id="Flow_0xrnaaw" name="Yes" sourceRef="Gateway_0r0djc8" targetRef="orderExecEnd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${allCreated}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0npos96" sourceRef="CreateInstancesTask" targetRef="Gateway_0r0djc8" />
    <bpmn:sequenceFlow id="Flow_0f89hcs" name="No" sourceRef="Gateway_0r0djc8" targetRef="CreateInstancesTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${not allCreated}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="PaymentWorkflow" name="Payment and future order wokrflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ql8qn8</bpmn:incoming>
      <bpmn:outgoing>Flow_1h5mbos</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_1h5mbos" sourceRef="PaymentWorkflow" targetRef="Gateway_1kuelgu" />
    <bpmn:exclusiveGateway id="Gateway_1kuelgu" default="Flow_006dlpa">
      <bpmn:incoming>Flow_1h5mbos</bpmn:incoming>
      <bpmn:outgoing>Flow_006dlpa</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ollj5c</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_006dlpa" sourceRef="Gateway_1kuelgu" targetRef="GenerateDataDelegateTask" />
    <bpmn:endEvent id="Event_02szt1s">
      <bpmn:incoming>Flow_1ollj5c</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1ollj5c" sourceRef="Gateway_1kuelgu" targetRef="Event_02szt1s">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:startEvent id="orderExecStart" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_1ak4unt</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_0g06el8" default="Flow_0xasp95">
      <bpmn:incoming>Flow_1ak4unt</bpmn:incoming>
      <bpmn:outgoing>Flow_1iyf5u0</bpmn:outgoing>
      <bpmn:outgoing>Flow_0xasp95</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ak4unt" sourceRef="orderExecStart" targetRef="Gateway_0g06el8" />
    <bpmn:sequenceFlow id="Flow_1iyf5u0" name="yes" sourceRef="Gateway_0g06el8" targetRef="BookServiceFee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${additionalCharges}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0lowam6" default="Flow_0uubqtt">
      <bpmn:incoming>Flow_056pf58</bpmn:incoming>
      <bpmn:outgoing>Flow_0uubqtt</bpmn:outgoing>
      <bpmn:outgoing>Flow_1trjl3j</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_056pf58" sourceRef="BookServiceFee" targetRef="Gateway_0lowam6" />
    <bpmn:exclusiveGateway id="Gateway_07bbisn">
      <bpmn:incoming>Flow_0uubqtt</bpmn:incoming>
      <bpmn:incoming>Flow_0xasp95</bpmn:incoming>
      <bpmn:outgoing>Flow_0ql8qn8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0uubqtt" name="Success" sourceRef="Gateway_0lowam6" targetRef="Gateway_07bbisn" />
    <bpmn:sequenceFlow id="Flow_0ql8qn8" sourceRef="Gateway_07bbisn" targetRef="PaymentWorkflow" />
    <bpmn:endEvent id="Event_1032p3a">
      <bpmn:incoming>Flow_1trjl3j</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1trjl3j" name="Failure" sourceRef="Gateway_0lowam6" targetRef="Event_1032p3a">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0xasp95" name="no" sourceRef="Gateway_0g06el8" targetRef="Gateway_07bbisn" />
    <bpmn:serviceTask id="BookServiceFee" name="BookServiceFee" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1iyf5u0</bpmn:incoming>
      <bpmn:outgoing>Flow_056pf58</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmn:message id="Message_18lupa4" name="FlowOneAddSubCallback" />
  <bpmn:message id="Message_0kspzvs" name="${orderId}" />
  <bpmn:message id="Message_1ub3bvp" name="${orderId}" />
  <bpmn:message id="Message_0jnn3x7" name="${orderId}" />
  <bpmn:message id="Message_0uvxpri" name="multiCart" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="AddSubscription">
      <bpmndi:BPMNShape id="Activity_1xs5z3a_di" bpmnElement="GenerateDataDelegateTask">
        <dc:Bounds x="1210" y="159" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0z51h5s_di" bpmnElement="CreateInstancesTask">
        <dc:Bounds x="1420" y="159" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0r0djc8_di" bpmnElement="Gateway_0r0djc8" isMarkerVisible="true">
        <dc:Bounds x="1615" y="174" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1609" y="144" width="62" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_044dwef_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="1752" y="181" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0u4fviu_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="850" y="157" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1kuelgu_di" bpmnElement="Gateway_1kuelgu" isMarkerVisible="true">
        <dc:Bounds x="1035" y="172" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_02szt1s_di" bpmnElement="Event_02szt1s">
        <dc:Bounds x="1042" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1dykz4f_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0g06el8_di" bpmnElement="Gateway_0g06el8" isMarkerVisible="true">
        <dc:Bounds x="245" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0lowam6_di" bpmnElement="Gateway_0lowam6" isMarkerVisible="true">
        <dc:Bounds x="525" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_07bbisn_di" bpmnElement="Gateway_07bbisn" isMarkerVisible="true">
        <dc:Bounds x="645" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1032p3a_di" bpmnElement="Event_1032p3a">
        <dc:Bounds x="532" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0hwe7s7_di" bpmnElement="BookServiceFee">
        <dc:Bounds x="360" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_16hv1kt_di" bpmnElement="Flow_16hv1kt">
        <di:waypoint x="1310" y="199" />
        <di:waypoint x="1420" y="199" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xrnaaw_di" bpmnElement="Flow_0xrnaaw">
        <di:waypoint x="1665" y="199" />
        <di:waypoint x="1752" y="199" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1700" y="181" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0npos96_di" bpmnElement="Flow_0npos96">
        <di:waypoint x="1520" y="199" />
        <di:waypoint x="1615" y="199" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f89hcs_di" bpmnElement="Flow_0f89hcs">
        <di:waypoint x="1640" y="224" />
        <di:waypoint x="1640" y="290" />
        <di:waypoint x="1470" y="290" />
        <di:waypoint x="1470" y="240" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1548" y="272" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h5mbos_di" bpmnElement="Flow_1h5mbos">
        <di:waypoint x="950" y="197" />
        <di:waypoint x="1035" y="197" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_006dlpa_di" bpmnElement="Flow_006dlpa">
        <di:waypoint x="1084" y="198" />
        <di:waypoint x="1210" y="199" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ollj5c_di" bpmnElement="Flow_1ollj5c">
        <di:waypoint x="1060" y="222" />
        <di:waypoint x="1060" y="292" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ak4unt_di" bpmnElement="Flow_1ak4unt">
        <di:waypoint x="188" y="200" />
        <di:waypoint x="245" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1iyf5u0_di" bpmnElement="Flow_1iyf5u0">
        <di:waypoint x="295" y="200" />
        <di:waypoint x="360" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="319" y="182" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_056pf58_di" bpmnElement="Flow_056pf58">
        <di:waypoint x="460" y="200" />
        <di:waypoint x="525" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0uubqtt_di" bpmnElement="Flow_0uubqtt">
        <di:waypoint x="575" y="200" />
        <di:waypoint x="645" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="589" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ql8qn8_di" bpmnElement="Flow_0ql8qn8">
        <di:waypoint x="695" y="200" />
        <di:waypoint x="850" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1trjl3j_di" bpmnElement="Flow_1trjl3j">
        <di:waypoint x="550" y="225" />
        <di:waypoint x="550" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="548" y="256" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xasp95_di" bpmnElement="Flow_0xasp95">
        <di:waypoint x="270" y="175" />
        <di:waypoint x="270" y="100" />
        <di:waypoint x="670" y="100" />
        <di:waypoint x="670" y="175" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="464" y="82" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
