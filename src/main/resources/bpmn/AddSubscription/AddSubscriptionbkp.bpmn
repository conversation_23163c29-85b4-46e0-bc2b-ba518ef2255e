<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_077tjnv" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.6.0">
  <bpmn:process id="AddSubscriptionbkpold" name="AddSubscription" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_0dfguwk</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="OCSAddSubscription" name="OCS AddSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1hi1ee9</bpmn:incoming>
      <bpmn:outgoing>Flow_0xei7i4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1waufjf" default="Flow_0yldbe4">
      <bpmn:incoming>Flow_135peys</bpmn:incoming>
      <bpmn:outgoing>Flow_0yldbe4</bpmn:outgoing>
      <bpmn:outgoing>Flow_0yut1v0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="SOMAddSubscription" name="SOM Add Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${somAddSubscription}">
      <bpmn:incoming>Flow_1pamgg0</bpmn:incoming>
      <bpmn:outgoing>Flow_1va6qqc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0obp3bm" default="Flow_0e1eccy">
      <bpmn:incoming>Flow_1va6qqc</bpmn:incoming>
      <bpmn:outgoing>Flow_0e1eccy</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ffbngx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:receiveTask id="SOMAddSubCallback" name="SOMAddSubCallback" camunda:asyncBefore="true" messageRef="Message_08uiznb">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOMAddSubscription</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOMAddSubscription" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0e1eccy</bpmn:incoming>
      <bpmn:outgoing>Flow_1t4a0mn</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_0qq1wmn" default="Flow_0qrl0oj">
      <bpmn:incoming>Flow_1t4a0mn</bpmn:incoming>
      <bpmn:outgoing>Flow_0qrl0oj</bpmn:outgoing>
      <bpmn:outgoing>Flow_1nr4i1e</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_012z3x6">
      <bpmn:incoming>Flow_1nr4i1e</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_15t7ncl" name="check som call required or not&#10;&#10;" default="Flow_1rosmmj">
      <bpmn:incoming>Flow_0yldbe4</bpmn:incoming>
      <bpmn:outgoing>Flow_1pamgg0</bpmn:outgoing>
      <bpmn:outgoing>Flow_1rosmmj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0rqbep1">
      <bpmn:incoming>Flow_0qrl0oj</bpmn:incoming>
      <bpmn:incoming>Flow_1rosmmj</bpmn:incoming>
      <bpmn:outgoing>Flow_1hi1ee9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1uv713u">
      <bpmn:incoming>Flow_0ffbngx</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1pamgg0" name="yes&#10;" sourceRef="Gateway_15t7ncl" targetRef="SOMAddSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.hasProp('enrichmentResults')&amp;&amp; workflowData.jsonPath("$.enrichmentResults.somCallRqd").boolValue() == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1va6qqc" sourceRef="SOMAddSubscription" targetRef="Gateway_0obp3bm" />
    <bpmn:sequenceFlow id="Flow_0e1eccy" name="Success" sourceRef="Gateway_0obp3bm" targetRef="SOMAddSubCallback" />
    <bpmn:sequenceFlow id="Flow_0ffbngx" name="Failure" sourceRef="Gateway_0obp3bm" targetRef="Event_1uv713u">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1t4a0mn" sourceRef="SOMAddSubCallback" targetRef="Gateway_0qq1wmn" />
    <bpmn:sequenceFlow id="Flow_0qrl0oj" name="Success" sourceRef="Gateway_0qq1wmn" targetRef="Gateway_0rqbep1" />
    <bpmn:sequenceFlow id="Flow_1nr4i1e" name="Failure" sourceRef="Gateway_0qq1wmn" targetRef="Event_012z3x6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1rosmmj" name="no" sourceRef="Gateway_15t7ncl" targetRef="Gateway_0rqbep1" />
    <bpmn:sequenceFlow id="Flow_0yldbe4" sourceRef="Gateway_1waufjf" targetRef="Gateway_15t7ncl" />
    <bpmn:sequenceFlow id="Flow_1hi1ee9" sourceRef="Gateway_0rqbep1" targetRef="OCSAddSubscription" />
    <bpmn:serviceTask id="BSAddSubscription" name="AddSubscription in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1b1wnr0</bpmn:incoming>
      <bpmn:outgoing>Flow_135peys</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_135peys" sourceRef="BSAddSubscription" targetRef="Gateway_1waufjf" />
    <bpmn:callActivity id="PaymentWorkflow" name="Payment and future order wokrflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0dfguwk</bpmn:incoming>
      <bpmn:outgoing>Flow_0f8rhm0</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_1oh41uf" default="Flow_1b1wnr0">
      <bpmn:incoming>Flow_0f8rhm0</bpmn:incoming>
      <bpmn:outgoing>Flow_1b1wnr0</bpmn:outgoing>
      <bpmn:outgoing>Flow_0u85d5k</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0zlf6lq">
      <bpmn:incoming>Flow_0u85d5k</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_0n98exd">
      <bpmn:incoming>Flow_0yut1v0</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0yut1v0" name="Failure" sourceRef="Gateway_1waufjf" targetRef="Event_0n98exd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_13lmtew</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0xei7i4" sourceRef="OCSAddSubscription" targetRef="Gateway_14vy8vn" />
    <bpmn:exclusiveGateway id="Gateway_14vy8vn">
      <bpmn:incoming>Flow_0xei7i4</bpmn:incoming>
      <bpmn:outgoing>Flow_0183tqo</bpmn:outgoing>
      <bpmn:outgoing>Flow_0mramtp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0183tqo" sourceRef="Gateway_14vy8vn" targetRef="UpdateSubscriptionStatus">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="UpdateSubscriptionStatus" name="Update Subscription Status in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0183tqo</bpmn:incoming>
      <bpmn:outgoing>Flow_13lmtew</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1gzx3dy">
      <bpmn:incoming>Flow_0mramtp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0mramtp" name="Failure" sourceRef="Gateway_14vy8vn" targetRef="Event_1gzx3dy">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_13lmtew" sourceRef="UpdateSubscriptionStatus" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_1b1wnr0" name="Success" sourceRef="Gateway_1oh41uf" targetRef="BSAddSubscription" />
    <bpmn:sequenceFlow id="Flow_0dfguwk" sourceRef="orderExecStart" targetRef="PaymentWorkflow" />
    <bpmn:sequenceFlow id="Flow_0f8rhm0" sourceRef="PaymentWorkflow" targetRef="Gateway_1oh41uf" />
    <bpmn:sequenceFlow id="Flow_0u85d5k" sourceRef="Gateway_1oh41uf" targetRef="Event_0zlf6lq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_08uiznb" name="SOMAddSubCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="AddSubscriptionbkpold">
      <bpmndi:BPMNEdge id="Flow_0u85d5k_di" bpmnElement="Flow_0u85d5k">
        <di:waypoint x="790" y="412" />
        <di:waypoint x="790" y="512" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f8rhm0_di" bpmnElement="Flow_0f8rhm0">
        <di:waypoint x="500" y="387" />
        <di:waypoint x="765" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dfguwk_di" bpmnElement="Flow_0dfguwk">
        <di:waypoint x="218" y="390" />
        <di:waypoint x="400" y="390" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b1wnr0_di" bpmnElement="Flow_1b1wnr0">
        <di:waypoint x="815" y="387" />
        <di:waypoint x="1090" y="387" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="932" y="369" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13lmtew_di" bpmnElement="Flow_13lmtew">
        <di:waypoint x="2700" y="387" />
        <di:waypoint x="2822" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mramtp_di" bpmnElement="Flow_0mramtp">
        <di:waypoint x="2520" y="412" />
        <di:waypoint x="2520" y="482" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2520" y="444" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0183tqo_di" bpmnElement="Flow_0183tqo">
        <di:waypoint x="2545" y="387" />
        <di:waypoint x="2600" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xei7i4_di" bpmnElement="Flow_0xei7i4">
        <di:waypoint x="2420" y="387" />
        <di:waypoint x="2495" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yut1v0_di" bpmnElement="Flow_0yut1v0">
        <di:waypoint x="1330" y="412" />
        <di:waypoint x="1330" y="494" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1354" y="450" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_135peys_di" bpmnElement="Flow_135peys">
        <di:waypoint x="1190" y="387" />
        <di:waypoint x="1305" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hi1ee9_di" bpmnElement="Flow_1hi1ee9">
        <di:waypoint x="2245" y="387" />
        <di:waypoint x="2320" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yldbe4_di" bpmnElement="Flow_0yldbe4">
        <di:waypoint x="1355" y="387" />
        <di:waypoint x="1475" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rosmmj_di" bpmnElement="Flow_1rosmmj">
        <di:waypoint x="1500" y="362" />
        <di:waypoint x="1500" y="297" />
        <di:waypoint x="2220" y="297" />
        <di:waypoint x="2220" y="362" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1803" y="82" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nr4i1e_di" bpmnElement="Flow_1nr4i1e">
        <di:waypoint x="2059" y="412" />
        <di:waypoint x="2059" y="459" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2073" y="434" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qrl0oj_di" bpmnElement="Flow_0qrl0oj">
        <di:waypoint x="2084" y="387" />
        <di:waypoint x="2195" y="387" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2119" y="342" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t4a0mn_di" bpmnElement="Flow_1t4a0mn">
        <di:waypoint x="1990" y="387" />
        <di:waypoint x="2034" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ffbngx_di" bpmnElement="Flow_0ffbngx">
        <di:waypoint x="1800" y="412" />
        <di:waypoint x="1800" y="459" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1819" y="423" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e1eccy_di" bpmnElement="Flow_0e1eccy">
        <di:waypoint x="1825" y="387" />
        <di:waypoint x="1890" y="387" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1832" y="363" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1va6qqc_di" bpmnElement="Flow_1va6qqc">
        <di:waypoint x="1700" y="387" />
        <di:waypoint x="1775" y="387" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pamgg0_di" bpmnElement="Flow_1pamgg0">
        <di:waypoint x="1525" y="387" />
        <di:waypoint x="1600" y="387" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1557" y="369" width="17" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="182" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_03ahm73_di" bpmnElement="OCSAddSubscription">
        <dc:Bounds x="2320" y="347" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1waufjf_di" bpmnElement="Gateway_1waufjf" isMarkerVisible="true">
        <dc:Bounds x="1305" y="362" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1blgwyz_di" bpmnElement="SOMAddSubscription">
        <dc:Bounds x="1600" y="347" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0obp3bm_di" bpmnElement="Gateway_0obp3bm" isMarkerVisible="true">
        <dc:Bounds x="1775" y="362" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0jy41bt_di" bpmnElement="SOMAddSubCallback">
        <dc:Bounds x="1890" y="347" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0qq1wmn_di" bpmnElement="Gateway_0qq1wmn" isMarkerVisible="true">
        <dc:Bounds x="2034" y="362" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_012z3x6_di" bpmnElement="Event_012z3x6">
        <dc:Bounds x="2041" y="459" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_15t7ncl_di" bpmnElement="Gateway_15t7ncl" isMarkerVisible="true">
        <dc:Bounds x="1475" y="362" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1473" y="431" width="73" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0rqbep1_di" bpmnElement="Gateway_0rqbep1" isMarkerVisible="true">
        <dc:Bounds x="2195" y="362" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1uv713u_di" bpmnElement="Event_1uv713u">
        <dc:Bounds x="1782" y="459" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0mkfgdb_di" bpmnElement="BSAddSubscription">
        <dc:Bounds x="1090" y="347" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0pm68tr_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="400" y="347" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1oh41uf_di" bpmnElement="Gateway_1oh41uf" isMarkerVisible="true">
        <dc:Bounds x="765" y="362" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0zlf6lq_di" bpmnElement="Event_0zlf6lq">
        <dc:Bounds x="772" y="512" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0n98exd_di" bpmnElement="Event_0n98exd">
        <dc:Bounds x="1312" y="494" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ccvmjb_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2822" y="369" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_02d9qip" bpmnElement="Gateway_14vy8vn" isMarkerVisible="true">
        <dc:Bounds x="2495" y="362" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1hh1kva" bpmnElement="UpdateSubscriptionStatus">
        <dc:Bounds x="2600" y="347" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0bdrpxr" bpmnElement="Event_1gzx3dy">
        <dc:Bounds x="2502" y="482" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
