<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.8.0">
  <bpmn:process id="AddMemberToGroup" name="AddMemberToGroup" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1hqx0zz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="NCCCreateSubscription" name="NCC Add Member To Group " camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0vjc7yu</bpmn:incoming>
      <bpmn:outgoing>Flow_0b2o8mt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1o6wrwh" default="Flow_0ao566t">
      <bpmn:incoming>Flow_09hnuuv</bpmn:incoming>
      <bpmn:outgoing>Flow_0ao566t</bpmn:outgoing>
      <bpmn:outgoing>Flow_1s9h88n</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0lc2rx8">
      <bpmn:incoming>Flow_1s9h88n</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0wr58fk" default="Flow_129jst2">
      <bpmn:incoming>Flow_0b2o8mt</bpmn:incoming>
      <bpmn:outgoing>Flow_1vg6o6w</bpmn:outgoing>
      <bpmn:outgoing>Flow_129jst2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1qkyhbv">
      <bpmn:incoming>Flow_1vg6o6w</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSAddSubscription" name="BS AddSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0o7c615</bpmn:incoming>
      <bpmn:outgoing>Flow_09hnuuv</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ao566t" name="Success" sourceRef="Gateway_1o6wrwh" targetRef="BSAddMemberToGroup" />
    <bpmn:sequenceFlow id="Flow_0b2o8mt" sourceRef="NCCCreateSubscription" targetRef="Gateway_0wr58fk" />
    <bpmn:sequenceFlow id="Flow_09hnuuv" sourceRef="BSAddSubscription" targetRef="Gateway_1o6wrwh" />
    <bpmn:sequenceFlow id="Flow_1s9h88n" name="Failure" sourceRef="Gateway_1o6wrwh" targetRef="Event_0lc2rx8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1vg6o6w" name="Failure" sourceRef="Gateway_0wr58fk" targetRef="Event_1qkyhbv">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0vjc7yu</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0vjc7yu" sourceRef="orderExecStart" targetRef="NCCCreateSubscription" />
    <bpmn:serviceTask id="BSAddMemberToGroup" name="Billing Add Member To Group " camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0ao566t</bpmn:incoming>
      <bpmn:outgoing>Flow_1hqx0zz</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1hqx0zz" sourceRef="BSAddMemberToGroup" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_129jst2" name="Success" sourceRef="Gateway_0wr58fk" targetRef="SMCreateSubscription" />
    <bpmn:exclusiveGateway id="Gateway_1wlgnpy" default="Flow_0o7c615">
      <bpmn:incoming>Flow_1vpubcs</bpmn:incoming>
      <bpmn:outgoing>Flow_0o7c615</bpmn:outgoing>
      <bpmn:outgoing>Flow_06wib1e</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0o7c615" name="Success" sourceRef="Gateway_1wlgnpy" targetRef="BSAddSubscription" />
    <bpmn:endEvent id="Event_0dr4o82">
      <bpmn:incoming>Flow_06wib1e</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_06wib1e" name="Failure" sourceRef="Gateway_1wlgnpy" targetRef="Event_0dr4o82">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="SMCreateSubscription" name="Subscription creation in SM" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_129jst2</bpmn:incoming>
      <bpmn:outgoing>Flow_1vpubcs</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1vpubcs" sourceRef="SMCreateSubscription" targetRef="Gateway_1wlgnpy" />
  </bpmn:process>
  <bpmn:message id="Message_18lupa4" name="SOMAddSubCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="AddMemberToGroup">
      <bpmndi:BPMNShape id="Activity_0mjtezg_di" bpmnElement="NCCCreateSubscription">
        <dc:Bounds x="270" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0wr58fk_di" bpmnElement="Gateway_0wr58fk" isMarkerVisible="true">
        <dc:Bounds x="485" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1qkyhbv_di" bpmnElement="Event_1qkyhbv">
        <dc:Bounds x="492" y="222" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_17u7fdp_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1wlgnpy_di" bpmnElement="Gateway_1wlgnpy" isMarkerVisible="true">
        <dc:Bounds x="865" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0dr4o82_di" bpmnElement="Event_0dr4o82">
        <dc:Bounds x="872" y="222" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ikg6tg_di" bpmnElement="SMCreateSubscription">
        <dc:Bounds x="650" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0xqurlr_di" bpmnElement="BSAddSubscription">
        <dc:Bounds x="1020" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1o6wrwh_di" bpmnElement="Gateway_1o6wrwh" isMarkerVisible="true">
        <dc:Bounds x="1215" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0lc2rx8_di" bpmnElement="Event_0lc2rx8">
        <dc:Bounds x="1222" y="222" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1k0cjlc_di" bpmnElement="BSAddMemberToGroup">
        <dc:Bounds x="1380" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0prrphu_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="1552" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0ao566t_di" bpmnElement="Flow_0ao566t">
        <di:waypoint x="1265" y="120" />
        <di:waypoint x="1380" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1288" y="103" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b2o8mt_di" bpmnElement="Flow_0b2o8mt">
        <di:waypoint x="370" y="120" />
        <di:waypoint x="485" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09hnuuv_di" bpmnElement="Flow_09hnuuv">
        <di:waypoint x="1120" y="120" />
        <di:waypoint x="1215" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s9h88n_di" bpmnElement="Flow_1s9h88n">
        <di:waypoint x="1240" y="145" />
        <di:waypoint x="1240" y="222" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1243" y="173" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vg6o6w_di" bpmnElement="Flow_1vg6o6w">
        <di:waypoint x="510" y="145" />
        <di:waypoint x="510" y="222" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="513" y="173" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vjc7yu_di" bpmnElement="Flow_0vjc7yu">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="270" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hqx0zz_di" bpmnElement="Flow_1hqx0zz">
        <di:waypoint x="1480" y="120" />
        <di:waypoint x="1552" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_129jst2_di" bpmnElement="Flow_129jst2">
        <di:waypoint x="535" y="120" />
        <di:waypoint x="650" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="568" y="103" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o7c615_di" bpmnElement="Flow_0o7c615">
        <di:waypoint x="915" y="120" />
        <di:waypoint x="1020" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="939" y="103" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06wib1e_di" bpmnElement="Flow_06wib1e">
        <di:waypoint x="890" y="145" />
        <di:waypoint x="890" y="222" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="893" y="173" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vpubcs_di" bpmnElement="Flow_1vpubcs">
        <di:waypoint x="750" y="120" />
        <di:waypoint x="865" y="120" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
