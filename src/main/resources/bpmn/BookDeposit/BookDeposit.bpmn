<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.6.0">
  <bpmn:process id="BookDeposit" name="BookDeposit" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:serviceTask id="BSBookDeposit" name="Billing BookDeposit" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0qg0oae</bpmn:incoming>
      <bpmn:outgoing>Flow_0w4w6ik</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_102d702</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_102d702" sourceRef="orderExecStart" targetRef="Gateway_1rr371q" />
    <bpmn:callActivity id="PaymentWorkflow" name="Payment Workflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow">
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:out variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_05r3dm5</bpmn:incoming>
      <bpmn:outgoing>Flow_0lbds8g</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_1i18jht" default="Flow_1lheqa0">
      <bpmn:incoming>Flow_0lbds8g</bpmn:incoming>
      <bpmn:outgoing>Flow_1t5tgzs</bpmn:outgoing>
      <bpmn:outgoing>Flow_1lheqa0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0lbds8g" sourceRef="PaymentWorkflow" targetRef="Gateway_1i18jht" />
    <bpmn:sequenceFlow id="Flow_1t5tgzs" name="Success" sourceRef="Gateway_1i18jht" targetRef="Gateway_0hfj9ge">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_184o5hn">
      <bpmn:incoming>Flow_1lheqa0</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1lheqa0" name="Failure" sourceRef="Gateway_1i18jht" targetRef="Event_184o5hn" />
    <bpmn:exclusiveGateway id="Gateway_1rr371q" name="is upfrontPayment?" default="Flow_0g91kj9">
      <bpmn:incoming>Flow_102d702</bpmn:incoming>
      <bpmn:outgoing>Flow_05r3dm5</bpmn:outgoing>
      <bpmn:outgoing>Flow_0g91kj9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_05r3dm5" name="true" sourceRef="Gateway_1rr371q" targetRef="PaymentWorkflow">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order").element().hasProp('deposit') &amp;&amp; workflowData.jsonPath("$.order.deposit").element().hasProp('upfrontPayment') &amp;&amp; workflowData.jsonPath("$.order.deposit.upfrontPayment").stringValue() == 'true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0hfj9ge">
      <bpmn:incoming>Flow_1t5tgzs</bpmn:incoming>
      <bpmn:incoming>Flow_0g91kj9</bpmn:incoming>
      <bpmn:outgoing>Flow_0qg0oae</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0qg0oae" sourceRef="Gateway_0hfj9ge" targetRef="BSBookDeposit" />
    <bpmn:sequenceFlow id="Flow_0g91kj9" name="false" sourceRef="Gateway_1rr371q" targetRef="Gateway_0hfj9ge" />
    <bpmn:exclusiveGateway id="Gateway_1vjkocm" default="Flow_0o1i5ha">
      <bpmn:incoming>Flow_0w4w6ik</bpmn:incoming>
      <bpmn:outgoing>Flow_010hc6j</bpmn:outgoing>
      <bpmn:outgoing>Flow_0o1i5ha</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0w4w6ik" sourceRef="BSBookDeposit" targetRef="Gateway_1vjkocm" />
    <bpmn:endEvent id="Event_1ltwhuy">
      <bpmn:incoming>Flow_010hc6j</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_010hc6j" name="Failure" sourceRef="Gateway_1vjkocm" targetRef="Event_1ltwhuy">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0o1i5ha</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0o1i5ha" sourceRef="Gateway_1vjkocm" targetRef="orderExecEnd" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="BookDeposit">
      <bpmndi:BPMNEdge id="Flow_010hc6j_di" bpmnElement="Flow_010hc6j">
        <di:waypoint x="1130" y="225" />
        <di:waypoint x="1130" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1129" y="261" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0w4w6ik_di" bpmnElement="Flow_0w4w6ik">
        <di:waypoint x="1020" y="200" />
        <di:waypoint x="1105" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0g91kj9_di" bpmnElement="Flow_0g91kj9">
        <di:waypoint x="290" y="175" />
        <di:waypoint x="290" y="100" />
        <di:waypoint x="750" y="100" />
        <di:waypoint x="750" y="175" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="508" y="82" width="24" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qg0oae_di" bpmnElement="Flow_0qg0oae">
        <di:waypoint x="775" y="200" />
        <di:waypoint x="920" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05r3dm5_di" bpmnElement="Flow_05r3dm5">
        <di:waypoint x="315" y="200" />
        <di:waypoint x="410" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="355" y="182" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lheqa0_di" bpmnElement="Flow_1lheqa0">
        <di:waypoint x="610" y="225" />
        <di:waypoint x="610" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="608" y="260" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t5tgzs_di" bpmnElement="Flow_1t5tgzs">
        <di:waypoint x="635" y="200" />
        <di:waypoint x="725" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="659" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lbds8g_di" bpmnElement="Flow_0lbds8g">
        <di:waypoint x="510" y="200" />
        <di:waypoint x="585" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_102d702_di" bpmnElement="Flow_102d702">
        <di:waypoint x="188" y="200" />
        <di:waypoint x="265" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o1i5ha_di" bpmnElement="Flow_0o1i5ha">
        <di:waypoint x="1155" y="200" />
        <di:waypoint x="1502" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0oi7fzj_di" bpmnElement="BSBookDeposit">
        <dc:Bounds x="920" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1k9x0cy_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1bn2vfd_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="410" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1i18jht_di" bpmnElement="Gateway_1i18jht" isMarkerVisible="true">
        <dc:Bounds x="585" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_184o5hn_di" bpmnElement="Event_184o5hn">
        <dc:Bounds x="592" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1rr371q_di" bpmnElement="Gateway_1rr371q" isMarkerVisible="true">
        <dc:Bounds x="265" y="175" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="249" y="232" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0hfj9ge_di" bpmnElement="Gateway_0hfj9ge" isMarkerVisible="true">
        <dc:Bounds x="725" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1vjkocm_di" bpmnElement="Gateway_1vjkocm" isMarkerVisible="true">
        <dc:Bounds x="1105" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ltwhuy_di" bpmnElement="Event_1ltwhuy">
        <dc:Bounds x="1112" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0xd0ly0_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="1502" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
