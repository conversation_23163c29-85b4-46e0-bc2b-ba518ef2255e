<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1lsckuo" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="DeleteDocument" name="DeleteDocument" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1ybz3m3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1ybz3m3" sourceRef="orderExecStart" targetRef="Gateway_0uhzbw4" />
    <bpmn:serviceTask id="PMDeleteDocument" name="PM Delete Document" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1sqnvpe</bpmn:incoming>
      <bpmn:outgoing>Flow_14v9aoj</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0uhzbw4" name="if isIdDocument true OR false" default="Flow_1h742ci">
      <bpmn:incoming>Flow_1ybz3m3</bpmn:incoming>
      <bpmn:outgoing>Flow_1sqnvpe</bpmn:outgoing>
      <bpmn:outgoing>Flow_1h742ci</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1sqnvpe" name="true" sourceRef="Gateway_0uhzbw4" targetRef="PMDeleteDocument">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order.documentDetails.isIdDocument").stringValue() == 'true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSDeleteDocument" name="BS Delete Document" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1h742ci</bpmn:incoming>
      <bpmn:outgoing>Flow_0gy4u06</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1h742ci" sourceRef="Gateway_0uhzbw4" targetRef="BSDeleteDocument" />
    <bpmn:exclusiveGateway id="Gateway_1ee3w7t">
      <bpmn:incoming>Flow_0gy4u06</bpmn:incoming>
      <bpmn:incoming>Flow_14v9aoj</bpmn:incoming>
      <bpmn:outgoing>Flow_0o21mka</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0gy4u06" sourceRef="BSDeleteDocument" targetRef="Gateway_1ee3w7t" />
    <bpmn:sequenceFlow id="Flow_14v9aoj" sourceRef="PMDeleteDocument" targetRef="Gateway_1ee3w7t" />
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0o21mka</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0o21mka" sourceRef="Gateway_1ee3w7t" targetRef="orderExecEnd" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="DeleteDocument">
      <bpmndi:BPMNEdge id="Flow_0o21mka_di" bpmnElement="Flow_0o21mka">
        <di:waypoint x="745" y="210" />
        <di:waypoint x="812" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14v9aoj_di" bpmnElement="Flow_14v9aoj">
        <di:waypoint x="600" y="287" />
        <di:waypoint x="720" y="287" />
        <di:waypoint x="720" y="235" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gy4u06_di" bpmnElement="Flow_0gy4u06">
        <di:waypoint x="600" y="130" />
        <di:waypoint x="720" y="130" />
        <di:waypoint x="720" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h742ci_di" bpmnElement="Flow_1h742ci">
        <di:waypoint x="310" y="262" />
        <di:waypoint x="310" y="130" />
        <di:waypoint x="500" y="130" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="218" y="176" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sqnvpe_di" bpmnElement="Flow_1sqnvpe">
        <di:waypoint x="335" y="287" />
        <di:waypoint x="500" y="287" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="401" y="256" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ybz3m3_di" bpmnElement="Flow_1ybz3m3">
        <di:waypoint x="188" y="287" />
        <di:waypoint x="285" y="287" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="269" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0oe96hx_di" bpmnElement="PMDeleteDocument">
        <dc:Bounds x="500" y="247" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0uhzbw4_di" bpmnElement="Gateway_0uhzbw4" isMarkerVisible="true">
        <dc:Bounds x="285" y="262" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="272" y="319" width="77" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1222hl4_di" bpmnElement="BSDeleteDocument">
        <dc:Bounds x="500" y="90" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ee3w7t_di" bpmnElement="Gateway_1ee3w7t" isMarkerVisible="true">
        <dc:Bounds x="695" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09y2gqk_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="812" y="192" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
