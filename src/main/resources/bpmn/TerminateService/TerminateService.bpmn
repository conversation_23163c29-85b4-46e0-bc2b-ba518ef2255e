<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1xap55q" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.23.0">
  <bpmn:process id="TerminateService" name="Terminate Service" isExecutable="true" camunda:jobPriority="${priority}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0zgx359</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_0j4rk40" default="Flow_0qsyxmk">
      <bpmn:incoming>Flow_1ffttxw</bpmn:incoming>
      <bpmn:outgoing>Flow_0wmm778</bpmn:outgoing>
      <bpmn:outgoing>Flow_0qsyxmk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_164xuyz">
      <bpmn:incoming>Flow_0wmm778</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0wmm778" name="Failure" sourceRef="Gateway_0j4rk40" targetRef="Event_164xuyz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0dtk7rr" default="Flow_0rpkbfh">
      <bpmn:incoming>Flow_0nuphds</bpmn:incoming>
      <bpmn:outgoing>Flow_0rpkbfh</bpmn:outgoing>
      <bpmn:outgoing>Flow_0wnikr9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0rpkbfh" name="Success" sourceRef="Gateway_0dtk7rr" targetRef="SOMTerminateService" />
    <bpmn:endEvent id="Event_0trewhl">
      <bpmn:incoming>Flow_0wnikr9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0wnikr9" name="Failure" sourceRef="Gateway_0dtk7rr" targetRef="Event_0trewhl">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ewwdhg" sourceRef="SOMTerminateService" targetRef="Gateway_1c9g6wi" />
    <bpmn:serviceTask id="SOMTerminateService" name="SOM Terminate Service" camunda:asyncBefore="true" camunda:delegateExpression="${somTerminateService}">
      <bpmn:incoming>Flow_0rpkbfh</bpmn:incoming>
      <bpmn:outgoing>Flow_0ewwdhg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1c9g6wi" default="Flow_00q7o4z">
      <bpmn:incoming>Flow_0ewwdhg</bpmn:incoming>
      <bpmn:outgoing>Flow_00q7o4z</bpmn:outgoing>
      <bpmn:outgoing>Flow_1b8tggi</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_00q7o4z" name="Success" sourceRef="Gateway_1c9g6wi" targetRef="SOMLifeCycleTerminationCallback" />
    <bpmn:endEvent id="Event_0suk5rs">
      <bpmn:incoming>Flow_1b8tggi</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1b8tggi" name="Failure" sourceRef="Gateway_1c9g6wi" targetRef="Event_0suk5rs">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="SOMLifeCycleTerminationCallback" name="SOM Callback" messageRef="Message_02m47bb">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOMTerminateService</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOMTerminateService" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_00q7o4z</bpmn:incoming>
      <bpmn:outgoing>Flow_1ffttxw</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch Service Registry to get Subscriptions" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1s6souu</bpmn:incoming>
      <bpmn:outgoing>Flow_0nuphds</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1ffttxw" sourceRef="SOMLifeCycleTerminationCallback" targetRef="Gateway_0j4rk40" />
    <bpmn:sequenceFlow id="Flow_0nuphds" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_0dtk7rr" />
    <bpmn:serviceTask id="OCSTerminateService" name="Terminate Service in OCS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0qsyxmk</bpmn:incoming>
      <bpmn:outgoing>Flow_0ma2f9v</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:callActivity id="ChangeGroupOwnership" name="ChangeGroupOwnership" camunda:asyncBefore="true" calledElement="ChangeGroupOwnership" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1udd6y9</bpmn:incoming>
      <bpmn:outgoing>Flow_0mi1bjg</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0mi1bjg" sourceRef="ChangeGroupOwnership" targetRef="Gateway_1qdermh" />
    <bpmn:exclusiveGateway id="Gateway_1qdermh" default="Flow_1s6souu">
      <bpmn:incoming>Flow_0mi1bjg</bpmn:incoming>
      <bpmn:outgoing>Flow_1s6souu</bpmn:outgoing>
      <bpmn:outgoing>Flow_1xxzemy</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1s6souu" name="Success" sourceRef="Gateway_1qdermh" targetRef="SOMFetchServiceRegistry" />
    <bpmn:endEvent id="Event_16dqmm3">
      <bpmn:incoming>Flow_1xxzemy</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0ma2f9v" sourceRef="OCSTerminateService" targetRef="Gateway_0tu0jc5" />
    <bpmn:serviceTask id="BSTerminateService" name="Terminate Service in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0mjhrnw</bpmn:incoming>
      <bpmn:outgoing>Flow_1se0tuw</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0tu0jc5" default="Flow_0mjhrnw">
      <bpmn:incoming>Flow_0ma2f9v</bpmn:incoming>
      <bpmn:outgoing>Flow_0mjhrnw</bpmn:outgoing>
      <bpmn:outgoing>Flow_0v9upod</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0mjhrnw" sourceRef="Gateway_0tu0jc5" targetRef="BSTerminateService" />
    <bpmn:endEvent id="Event_0q80t2t">
      <bpmn:incoming>Flow_0v9upod</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0v9upod" name="Failure" sourceRef="Gateway_0tu0jc5" targetRef="Event_0q80t2t">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0qsyxmk" sourceRef="Gateway_0j4rk40" targetRef="OCSTerminateService" />
    <bpmn:callActivity id="PaymentWorkflow" name="Payment and future order wokrflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0eoiwh1</bpmn:incoming>
      <bpmn:outgoing>Flow_1wdt83j</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_11mztas" default="Flow_1udd6y9">
      <bpmn:incoming>Flow_1wdt83j</bpmn:incoming>
      <bpmn:outgoing>Flow_1y3v84t</bpmn:outgoing>
      <bpmn:outgoing>Flow_1udd6y9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0q5v83z">
      <bpmn:incoming>Flow_1y3v84t</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1wdt83j" sourceRef="PaymentWorkflow" targetRef="Gateway_11mztas" />
    <bpmn:sequenceFlow id="Flow_1y3v84t" sourceRef="Gateway_11mztas" targetRef="Event_0q5v83z">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1udd6y9" sourceRef="Gateway_11mztas" targetRef="ChangeGroupOwnership" />
    <bpmn:exclusiveGateway id="Gateway_1agwemf" default="Flow_1pzg93e">
      <bpmn:incoming>Flow_1se0tuw</bpmn:incoming>
      <bpmn:outgoing>Flow_1pzg93e</bpmn:outgoing>
      <bpmn:outgoing>Flow_1qqqw71</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1se0tuw" sourceRef="BSTerminateService" targetRef="Gateway_1agwemf" />
    <bpmn:sequenceFlow id="Flow_1pzg93e" sourceRef="Gateway_1agwemf" targetRef="NmsTerminateService" />
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0zsqyjc</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0zsqyjc" sourceRef="NmsTerminateService" targetRef="orderExecEnd" />
    <bpmn:endEvent id="Event_1idi5ya">
      <bpmn:incoming>Flow_1qqqw71</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1qqqw71" name="Failure" sourceRef="Gateway_1agwemf" targetRef="Event_1idi5ya">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="NmsTerminateService" name="Terminate Service in NMS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1pzg93e</bpmn:incoming>
      <bpmn:outgoing>Flow_0zsqyjc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1xxzemy" name="Failure" sourceRef="Gateway_1qdermh" targetRef="Event_16dqmm3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0dj2bmt" default="Flow_0on7zlu">
      <bpmn:incoming>Flow_0zgx359</bpmn:incoming>
      <bpmn:outgoing>Flow_1oxyag6</bpmn:outgoing>
      <bpmn:outgoing>Flow_0on7zlu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0zgx359" sourceRef="orderExecStart" targetRef="Gateway_0dj2bmt" />
    <bpmn:sequenceFlow id="Flow_1oxyag6" name="yes" sourceRef="Gateway_0dj2bmt" targetRef="BookServiceFee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${additionalCharges}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0yqusjg" default="Flow_15ymgaj">
      <bpmn:incoming>Flow_0nrpe64</bpmn:incoming>
      <bpmn:outgoing>Flow_15ymgaj</bpmn:outgoing>
      <bpmn:outgoing>Flow_11wics5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0nrpe64" sourceRef="BookServiceFee" targetRef="Gateway_0yqusjg" />
    <bpmn:exclusiveGateway id="Gateway_0hw9v4b">
      <bpmn:incoming>Flow_15ymgaj</bpmn:incoming>
      <bpmn:incoming>Flow_0on7zlu</bpmn:incoming>
      <bpmn:outgoing>Flow_10jcte8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_15ymgaj" name="Suceess" sourceRef="Gateway_0yqusjg" targetRef="Gateway_0hw9v4b" />
    <bpmn:serviceTask id="BookServiceFee" name="BookServiceFee" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1oxyag6</bpmn:incoming>
      <bpmn:outgoing>Flow_0nrpe64</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0on7zlu" name="no" sourceRef="Gateway_0dj2bmt" targetRef="Gateway_0hw9v4b" />
    <bpmn:endEvent id="Event_0dcg74r">
      <bpmn:incoming>Flow_11wics5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_11wics5" name="Failure" sourceRef="Gateway_0yqusjg" targetRef="Event_0dcg74r">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_10l2yu5" default="Flow_0va021r">
      <bpmn:incoming>Flow_1wm52qp</bpmn:incoming>
      <bpmn:outgoing>Flow_1ndd6xc</bpmn:outgoing>
      <bpmn:outgoing>Flow_0va021r</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0n7aeec">
      <bpmn:incoming>Flow_1ndd6xc</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1ndd6xc" sourceRef="Gateway_10l2yu5" targetRef="Event_0n7aeec">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BillingFetchAccount" name="BillingFetchAccount" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_14m9uch</bpmn:incoming>
      <bpmn:outgoing>Flow_1wm52qp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1wm52qp" sourceRef="BillingFetchAccount" targetRef="Gateway_10l2yu5" />
    <bpmn:exclusiveGateway id="Gateway_1xgl0xd" default="Flow_0dacc7b">
      <bpmn:incoming>Flow_10jcte8</bpmn:incoming>
      <bpmn:outgoing>Flow_14m9uch</bpmn:outgoing>
      <bpmn:outgoing>Flow_0dacc7b</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_10jcte8" sourceRef="Gateway_0hw9v4b" targetRef="Gateway_1xgl0xd" />
    <bpmn:sequenceFlow id="Flow_14m9uch" sourceRef="Gateway_1xgl0xd" targetRef="BillingFetchAccount">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${fetchAccountCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0war4jc">
      <bpmn:incoming>Flow_0va021r</bpmn:incoming>
      <bpmn:incoming>Flow_0dacc7b</bpmn:incoming>
      <bpmn:outgoing>Flow_0eoiwh1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0va021r" sourceRef="Gateway_10l2yu5" targetRef="Gateway_0war4jc" />
    <bpmn:sequenceFlow id="Flow_0eoiwh1" sourceRef="Gateway_0war4jc" targetRef="PaymentWorkflow" />
    <bpmn:sequenceFlow id="Flow_0dacc7b" sourceRef="Gateway_1xgl0xd" targetRef="Gateway_0war4jc" />
  </bpmn:process>
  <bpmn:message id="Message_02m47bb" name="SOMLifeCycleTerminationCallback" />
  <bpmn:message id="Message_0vntsms" name="SDPCancelSubCallBack" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="TerminateService">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="199" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0j4rk40_di" bpmnElement="Gateway_0j4rk40" isMarkerVisible="true">
        <dc:Bounds x="2975" y="192" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_164xuyz_di" bpmnElement="Event_164xuyz">
        <dc:Bounds x="2982" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0dtk7rr_di" bpmnElement="Gateway_0dtk7rr" isMarkerVisible="true">
        <dc:Bounds x="2315" y="192" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0trewhl_di" bpmnElement="Event_0trewhl">
        <dc:Bounds x="2322" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1dlxd4z_di" bpmnElement="SOMTerminateService">
        <dc:Bounds x="2460" y="177" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1c9g6wi_di" bpmnElement="Gateway_1c9g6wi" isMarkerVisible="true">
        <dc:Bounds x="2635" y="192" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0suk5rs_di" bpmnElement="Event_0suk5rs">
        <dc:Bounds x="2642" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1h5e770_di" bpmnElement="SOMLifeCycleTerminationCallback">
        <dc:Bounds x="2790" y="177" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0gq2axm_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="2130" y="177" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1r9lu6v_di" bpmnElement="OCSTerminateService">
        <dc:Bounds x="3190" y="177" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0qjmzz7" bpmnElement="ChangeGroupOwnership">
        <dc:Bounds x="1720" y="177" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1scb5gs" bpmnElement="Gateway_1qdermh" isMarkerVisible="true">
        <dc:Bounds x="1925" y="192" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16di8of" bpmnElement="Event_16dqmm3">
        <dc:Bounds x="1932" y="322" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13qf77o" bpmnElement="BSTerminateService">
        <dc:Bounds x="3550" y="177" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_05um4vx" bpmnElement="Gateway_0tu0jc5" isMarkerVisible="true">
        <dc:Bounds x="3385" y="192" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0thpxxv" bpmnElement="Event_0q80t2t">
        <dc:Bounds x="3392" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0a9rda2_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="1390" y="184" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_11mztas_di" bpmnElement="Gateway_11mztas" isMarkerVisible="true">
        <dc:Bounds x="1595" y="192" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0q5v83z_di" bpmnElement="Event_0q5v83z">
        <dc:Bounds x="1602" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1agwemf_di" bpmnElement="Gateway_1agwemf" isMarkerVisible="true">
        <dc:Bounds x="3765" y="192" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_039x06h_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="4152" y="199" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1idi5ya_di" bpmnElement="Event_1idi5ya">
        <dc:Bounds x="3772" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_117lo35_di" bpmnElement="NmsTerminateService">
        <dc:Bounds x="3930" y="177" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0dj2bmt_di" bpmnElement="Gateway_0dj2bmt" isMarkerVisible="true">
        <dc:Bounds x="285" y="192" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0yqusjg_di" bpmnElement="Gateway_0yqusjg" isMarkerVisible="true">
        <dc:Bounds x="585" y="192" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0hw9v4b_di" bpmnElement="Gateway_0hw9v4b" isMarkerVisible="true">
        <dc:Bounds x="705" y="192" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1iame7z_di" bpmnElement="BookServiceFee">
        <dc:Bounds x="410" y="177" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0dcg74r_di" bpmnElement="Event_0dcg74r">
        <dc:Bounds x="592" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_10l2yu5_di" bpmnElement="Gateway_10l2yu5" isMarkerVisible="true">
        <dc:Bounds x="1115" y="192" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0n7aeec_di" bpmnElement="Event_0n7aeec">
        <dc:Bounds x="1122" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_148sivz" bpmnElement="BillingFetchAccount">
        <dc:Bounds x="970" y="177" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1xgl0xd_di" bpmnElement="Gateway_1xgl0xd" isMarkerVisible="true">
        <dc:Bounds x="845" y="192" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0war4jc_di" bpmnElement="Gateway_0war4jc" isMarkerVisible="true">
        <dc:Bounds x="1235" y="192" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0wmm778_di" bpmnElement="Flow_0wmm778">
        <di:waypoint x="3000" y="242" />
        <di:waypoint x="3000" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3003" y="260" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rpkbfh_di" bpmnElement="Flow_0rpkbfh">
        <di:waypoint x="2365" y="217" />
        <di:waypoint x="2460" y="217" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2384" y="199" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wnikr9_di" bpmnElement="Flow_0wnikr9">
        <di:waypoint x="2340" y="242" />
        <di:waypoint x="2340" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2343" y="263" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ewwdhg_di" bpmnElement="Flow_0ewwdhg">
        <di:waypoint x="2560" y="217" />
        <di:waypoint x="2635" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00q7o4z_di" bpmnElement="Flow_00q7o4z">
        <di:waypoint x="2685" y="217" />
        <di:waypoint x="2790" y="217" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2716" y="199" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b8tggi_di" bpmnElement="Flow_1b8tggi">
        <di:waypoint x="2660" y="242" />
        <di:waypoint x="2660" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2663" y="260" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ffttxw_di" bpmnElement="Flow_1ffttxw">
        <di:waypoint x="2890" y="217" />
        <di:waypoint x="2975" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nuphds_di" bpmnElement="Flow_0nuphds">
        <di:waypoint x="2230" y="217" />
        <di:waypoint x="2315" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mi1bjg_di" bpmnElement="Flow_0mi1bjg">
        <di:waypoint x="1820" y="217" />
        <di:waypoint x="1925" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s6souu_di" bpmnElement="Flow_1s6souu">
        <di:waypoint x="1975" y="217" />
        <di:waypoint x="2130" y="217" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2032" y="199" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ma2f9v_di" bpmnElement="Flow_0ma2f9v">
        <di:waypoint x="3290" y="217" />
        <di:waypoint x="3385" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mjhrnw_di" bpmnElement="Flow_0mjhrnw">
        <di:waypoint x="3435" y="217" />
        <di:waypoint x="3550" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v9upod_di" bpmnElement="Flow_0v9upod">
        <di:waypoint x="3410" y="242" />
        <di:waypoint x="3410" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3422" y="260" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qsyxmk_di" bpmnElement="Flow_0qsyxmk">
        <di:waypoint x="3025" y="217" />
        <di:waypoint x="3190" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wdt83j_di" bpmnElement="Flow_1wdt83j">
        <di:waypoint x="1490" y="224" />
        <di:waypoint x="1543" y="224" />
        <di:waypoint x="1543" y="217" />
        <di:waypoint x="1595" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1y3v84t_di" bpmnElement="Flow_1y3v84t">
        <di:waypoint x="1620" y="242" />
        <di:waypoint x="1620" y="312" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1udd6y9_di" bpmnElement="Flow_1udd6y9">
        <di:waypoint x="1644" y="218" />
        <di:waypoint x="1720" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1se0tuw_di" bpmnElement="Flow_1se0tuw">
        <di:waypoint x="3650" y="217" />
        <di:waypoint x="3765" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pzg93e_di" bpmnElement="Flow_1pzg93e">
        <di:waypoint x="3815" y="217" />
        <di:waypoint x="3930" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zsqyjc_di" bpmnElement="Flow_0zsqyjc">
        <di:waypoint x="4030" y="217" />
        <di:waypoint x="4152" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qqqw71_di" bpmnElement="Flow_1qqqw71">
        <di:waypoint x="3790" y="242" />
        <di:waypoint x="3790" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3788" y="274" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xxzemy_di" bpmnElement="Flow_1xxzemy">
        <di:waypoint x="1950" y="242" />
        <di:waypoint x="1950" y="322" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1948" y="279" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zgx359_di" bpmnElement="Flow_0zgx359">
        <di:waypoint x="188" y="217" />
        <di:waypoint x="285" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oxyag6_di" bpmnElement="Flow_1oxyag6">
        <di:waypoint x="335" y="217" />
        <di:waypoint x="410" y="217" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="364" y="199" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nrpe64_di" bpmnElement="Flow_0nrpe64">
        <di:waypoint x="510" y="217" />
        <di:waypoint x="585" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15ymgaj_di" bpmnElement="Flow_15ymgaj">
        <di:waypoint x="635" y="217" />
        <di:waypoint x="705" y="217" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="650" y="199" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0on7zlu_di" bpmnElement="Flow_0on7zlu">
        <di:waypoint x="310" y="192" />
        <di:waypoint x="310" y="100" />
        <di:waypoint x="730" y="100" />
        <di:waypoint x="730" y="192" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="514" y="82" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11wics5_di" bpmnElement="Flow_11wics5">
        <di:waypoint x="610" y="242" />
        <di:waypoint x="610" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="608" y="268" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ndd6xc_di" bpmnElement="Flow_1ndd6xc">
        <di:waypoint x="1140" y="242" />
        <di:waypoint x="1140" y="292" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wm52qp_di" bpmnElement="Flow_1wm52qp">
        <di:waypoint x="1070" y="217" />
        <di:waypoint x="1115" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10jcte8_di" bpmnElement="Flow_10jcte8">
        <di:waypoint x="755" y="217" />
        <di:waypoint x="845" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14m9uch_di" bpmnElement="Flow_14m9uch">
        <di:waypoint x="895" y="217" />
        <di:waypoint x="970" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0va021r_di" bpmnElement="Flow_0va021r">
        <di:waypoint x="1165" y="217" />
        <di:waypoint x="1235" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0eoiwh1_di" bpmnElement="Flow_0eoiwh1">
        <di:waypoint x="1285" y="217" />
        <di:waypoint x="1390" y="217" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dacc7b_di" bpmnElement="Flow_0dacc7b">
        <di:waypoint x="870" y="192" />
        <di:waypoint x="870" y="70" />
        <di:waypoint x="1260" y="70" />
        <di:waypoint x="1260" y="192" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
