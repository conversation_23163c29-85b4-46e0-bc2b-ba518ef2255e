<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.21.0">
  <bpmn:process id="UpdateServiceAddress" name="UpdateServiceAddress" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:subProcess id="ServiceAddress-MultiInstance" name="Update Service Address-Multi Instance" camunda:asyncBefore="true">
      <bpmn:incoming>Flow_1gfe9a9</bpmn:incoming>
      <bpmn:outgoing>Flow_09tec1i</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${workflowData.jsonPath(&#34;$.order.address&#34;).elementList()}" camunda:elementVariable="executionData">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
      <bpmn:serviceTask id="BSUpdateServiceAddress" name="BS UpdateServiceAddress" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
        <bpmn:incoming>Flow_1cq1m4v</bpmn:incoming>
        <bpmn:outgoing>Flow_1c4dv1s</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="subProcessEndEvent">
        <bpmn:incoming>Flow_1c4dv1s</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:startEvent id="subProcessStartEvent">
        <bpmn:outgoing>Flow_1cq1m4v</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="Flow_1c4dv1s" sourceRef="BSUpdateServiceAddress" targetRef="subProcessEndEvent" />
      <bpmn:sequenceFlow id="Flow_1cq1m4v" sourceRef="subProcessStartEvent" targetRef="BSUpdateServiceAddress" />
    </bpmn:subProcess>
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1gfe9a9</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1gfe9a9" sourceRef="orderExecStart" targetRef="ServiceAddress-MultiInstance" />
    <bpmn:endEvent id="orderExecEnd" camunda:asyncBefore="true">
      <bpmn:incoming>Flow_09tec1i</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_09tec1i" sourceRef="ServiceAddress-MultiInstance" targetRef="orderExecEnd" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="UpdateServiceAddress">
      <bpmndi:BPMNShape id="Activity_14cl5j8_di" bpmnElement="ServiceAddress-MultiInstance" isExpanded="true">
        <dc:Bounds x="280" y="80" width="390" height="160" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ngn6ow_di" bpmnElement="BSUpdateServiceAddress">
        <dc:Bounds x="430" y="120" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1p88c2w_di" bpmnElement="subProcessEndEvent">
        <dc:Bounds x="592" y="142" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_00k11e2_di" bpmnElement="subProcessStartEvent">
        <dc:Bounds x="332" y="142" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1c4dv1s_di" bpmnElement="Flow_1c4dv1s">
        <di:waypoint x="530" y="160" />
        <di:waypoint x="592" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cq1m4v_di" bpmnElement="Flow_1cq1m4v">
        <di:waypoint x="368" y="160" />
        <di:waypoint x="430" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_0ypni87_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="152" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0pxja4r_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="762" y="142" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1gfe9a9_di" bpmnElement="Flow_1gfe9a9">
        <di:waypoint x="188" y="170" />
        <di:waypoint x="280" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09tec1i_di" bpmnElement="Flow_09tec1i">
        <di:waypoint x="670" y="160" />
        <di:waypoint x="762" y="160" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
