<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0">
  <bpmn:process id="PortInFromExtTelcoOrSingtel" name="PortInFromExtTelcoOrSingtel" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="Event_1hjhhcv">
      <bpmn:outgoing>Flow_125fo0m</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_07owlzf" default="Flow_163v0ip">
      <bpmn:incoming>Flow_1ogj2lu</bpmn:incoming>
      <bpmn:outgoing>Flow_0z4o4f6</bpmn:outgoing>
      <bpmn:outgoing>Flow_163v0ip</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ogj2lu" sourceRef="BSCreateService" targetRef="Gateway_07owlzf" />
    <bpmn:endEvent id="Event_0eso59k">
      <bpmn:incoming>Flow_0z4o4f6</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0z4o4f6" name="Failure" sourceRef="Gateway_07owlzf" targetRef="Event_0eso59k">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_163v0ip" name="Success" sourceRef="Gateway_07owlzf" targetRef="BSAddSubscription" />
    <bpmn:exclusiveGateway id="Gateway_0vvd1dw" default="Flow_140e8mx">
      <bpmn:incoming>Flow_116r5g8</bpmn:incoming>
      <bpmn:outgoing>Flow_0ety70b</bpmn:outgoing>
      <bpmn:outgoing>Flow_140e8mx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_116r5g8" sourceRef="BSAddSubscription" targetRef="Gateway_0vvd1dw" />
    <bpmn:endEvent id="Event_1c6l7ml">
      <bpmn:incoming>Flow_0ety70b</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0ety70b" name="Failure" sourceRef="Gateway_0vvd1dw" targetRef="Event_1c6l7ml">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_140e8mx" name="Success" sourceRef="Gateway_0vvd1dw" targetRef="Gateway_1tnfnuj" />
    <bpmn:serviceTask id="SDPSubmitPortIn" name="Submit port in " camunda:asyncBefore="true" camunda:delegateExpression="${submitPortIn}">
      <bpmn:incoming>Flow_0dsd9jf</bpmn:incoming>
      <bpmn:outgoing>Flow_0x790bn</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSAddSubscription" name="BS Create Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_163v0ip</bpmn:incoming>
      <bpmn:outgoing>Flow_116r5g8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSCreateService" name="Billing Service creation" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_125fo0m</bpmn:incoming>
      <bpmn:outgoing>Flow_1ogj2lu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0eb5jrb" default="Flow_1uusmv0">
      <bpmn:incoming>Flow_0x790bn</bpmn:incoming>
      <bpmn:outgoing>Flow_1x6vcfz</bpmn:outgoing>
      <bpmn:outgoing>Flow_1uusmv0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0x790bn" sourceRef="SDPSubmitPortIn" targetRef="Gateway_0eb5jrb" />
    <bpmn:endEvent id="Event_052uwjo">
      <bpmn:incoming>Flow_1x6vcfz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1x6vcfz" name="Failure" sourceRef="Gateway_0eb5jrb" targetRef="Event_052uwjo">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="ConnectServiceIntCallback" name="Wait for connect service callback" camunda:asyncBefore="true" messageRef="Message_0qgbj08">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SDPSubmitPortIn" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1uusmv0</bpmn:incoming>
      <bpmn:outgoing>Flow_0p1bagf</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_1uusmv0" name="Success" sourceRef="Gateway_0eb5jrb" targetRef="ConnectServiceIntCallback" />
    <bpmn:sequenceFlow id="Flow_0p1bagf" sourceRef="ConnectServiceIntCallback" targetRef="Gateway_1i8m9uf" />
    <bpmn:serviceTask id="SOM_Provisioning" name="Provisioning in HLR (via SOM)" camunda:asyncBefore="true" camunda:delegateExpression="${somServiceOrderCreation}">
      <bpmn:incoming>Flow_0iwhogl</bpmn:incoming>
      <bpmn:outgoing>Flow_0ax48eu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0paffsv" default="Flow_1h5qypc">
      <bpmn:incoming>Flow_0ax48eu</bpmn:incoming>
      <bpmn:outgoing>Flow_0dm3ryx</bpmn:outgoing>
      <bpmn:outgoing>Flow_1h5qypc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ax48eu" sourceRef="SOM_Provisioning" targetRef="Gateway_0paffsv" />
    <bpmn:endEvent id="Event_05qjnsn">
      <bpmn:incoming>Flow_0dm3ryx</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0dm3ryx" name="Failure" sourceRef="Gateway_0paffsv" targetRef="Event_05qjnsn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1h5qypc" name="Success" sourceRef="Gateway_0paffsv" targetRef="SOM_Callback" />
    <bpmn:receiveTask id="SOM_Callback" name="Wait for SOM callback" camunda:asyncBefore="true" messageRef="Message_14c63bs">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOM_Provisioning</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOM_Provisioning" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1h5qypc</bpmn:incoming>
      <bpmn:outgoing>Flow_18rb9ws</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_0oq1n56" default="Flow_0xrblsx">
      <bpmn:incoming>Flow_18rb9ws</bpmn:incoming>
      <bpmn:outgoing>Flow_0xrblsx</bpmn:outgoing>
      <bpmn:outgoing>Flow_1dwn11l</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_18rb9ws" sourceRef="SOM_Callback" targetRef="Gateway_0oq1n56" />
    <bpmn:sequenceFlow id="Flow_0xrblsx" name="Success" sourceRef="Gateway_0oq1n56" targetRef="Gateway_0qhfx28" />
    <bpmn:endEvent id="Event_0bb5bnp">
      <bpmn:incoming>Flow_1dwn11l</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1dwn11l" name="Failure" sourceRef="Gateway_0oq1n56" targetRef="Event_0bb5bnp">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0iwqp7u" default="Flow_1c4od85">
      <bpmn:incoming>Flow_1wc6i9r</bpmn:incoming>
      <bpmn:outgoing>Flow_1c4od85</bpmn:outgoing>
      <bpmn:outgoing>Flow_1t5yaxs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1wc6i9r" sourceRef="ESB_CreateAccount" targetRef="Gateway_0iwqp7u" />
    <bpmn:sequenceFlow id="Flow_1c4od85" name="Success" sourceRef="Gateway_0iwqp7u" targetRef="Gateway_1k23cvg" />
    <bpmn:exclusiveGateway id="Gateway_0k3lwgh" default="Flow_0g9r5rm">
      <bpmn:incoming>Flow_1fb7pav</bpmn:incoming>
      <bpmn:outgoing>Flow_0u34h4a</bpmn:outgoing>
      <bpmn:outgoing>Flow_0g9r5rm</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1fb7pav" sourceRef="ESBCreateSubscriber" targetRef="Gateway_0k3lwgh" />
    <bpmn:exclusiveGateway id="Gateway_1kjv2vi" default="Flow_0toy5xo">
      <bpmn:incoming>Flow_0mwgfrx</bpmn:incoming>
      <bpmn:outgoing>Flow_0toy5xo</bpmn:outgoing>
      <bpmn:outgoing>Flow_04jyj1k</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0toy5xo" name="Success" sourceRef="Gateway_1kjv2vi" targetRef="BSActivateService" />
    <bpmn:endEvent id="Event_1ydo4qs">
      <bpmn:incoming>Flow_04jyj1k</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_04jyj1k" name="Failure" sourceRef="Gateway_1kjv2vi" targetRef="Event_1ydo4qs">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1cd2m4m">
      <bpmn:incoming>Flow_0u34h4a</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0u34h4a" name="Failure" sourceRef="Gateway_0k3lwgh" targetRef="Event_1cd2m4m">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_08x8wf7">
      <bpmn:incoming>Flow_1t5yaxs</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1t5yaxs" name="Failure" sourceRef="Gateway_0iwqp7u" targetRef="Event_08x8wf7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSActivateService" name="Service Activation in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0toy5xo</bpmn:incoming>
      <bpmn:outgoing>Flow_0ym5p93</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="ESBCreateSubscriber" name="Create Service in OCS via ESB" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1jwg310</bpmn:incoming>
      <bpmn:outgoing>Flow_1fb7pav</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="ESB_CreateAccount" name="Create Account in OCS via ESB" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0rtt3ow</bpmn:incoming>
      <bpmn:outgoing>Flow_1wc6i9r</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:callActivity id="OCSOfferActivationProcess" name="OCS Offer Activation Process" camunda:asyncBefore="true" calledElement="OCSOfferActivationProcess" camunda:calledElementBinding="deployment">
      <bpmn:documentation>Iterate over addon subs in BS Add subs response list. planType  1 is base, 0 is addon</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:out variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0g9r5rm</bpmn:incoming>
      <bpmn:outgoing>Flow_0mwgfrx</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.BSAddSubscriptionResponseAttributes.subscriptions[?(@.planType==&#39;0&#39;)]&#34;).elementList()}" camunda:elementVariable="execution">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0g9r5rm" name="Success" sourceRef="Gateway_0k3lwgh" targetRef="OCSOfferActivationProcess" />
    <bpmn:sequenceFlow id="Flow_0mwgfrx" sourceRef="OCSOfferActivationProcess" targetRef="Gateway_1kjv2vi" />
    <bpmn:exclusiveGateway id="Gateway_0qhfx28" default="Flow_0rtt3ow">
      <bpmn:incoming>Flow_0xrblsx</bpmn:incoming>
      <bpmn:outgoing>Flow_0rtt3ow</bpmn:outgoing>
      <bpmn:outgoing>Flow_0aljmhn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0rtt3ow" name="No" sourceRef="Gateway_0qhfx28" targetRef="ESB_CreateAccount" />
    <bpmn:exclusiveGateway id="Gateway_1k23cvg">
      <bpmn:incoming>Flow_1c4od85</bpmn:incoming>
      <bpmn:incoming>Flow_0aljmhn</bpmn:incoming>
      <bpmn:outgoing>Flow_1jwg310</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1jwg310" sourceRef="Gateway_1k23cvg" targetRef="ESBCreateSubscriber" />
    <bpmn:sequenceFlow id="Flow_0aljmhn" name="Yes" sourceRef="Gateway_0qhfx28" targetRef="Gateway_1k23cvg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isExistingAccount}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="subProcessEndEvent_2">
      <bpmn:incoming>Flow_00o7vzz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_01737r5" default="Flow_1u2akys">
      <bpmn:incoming>Flow_0ym5p93</bpmn:incoming>
      <bpmn:outgoing>Flow_1u2akys</bpmn:outgoing>
      <bpmn:outgoing>Flow_1oo4ugl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1fnqo58">
      <bpmn:incoming>Flow_1oo4ugl</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="OCS_ServicePortDetailsUpdate" name="OCS ServicePortDetailsUpdate" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1u2akys</bpmn:incoming>
      <bpmn:outgoing>Flow_0pun1ua</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0pun1ua" sourceRef="OCS_ServicePortDetailsUpdate" targetRef="Gateway_0rs0761" />
    <bpmn:sequenceFlow id="Flow_1u2akys" name="Success" sourceRef="Gateway_01737r5" targetRef="OCS_ServicePortDetailsUpdate" />
    <bpmn:sequenceFlow id="Flow_1oo4ugl" name="Failure" sourceRef="Gateway_01737r5" targetRef="Event_1fnqo58">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ym5p93" sourceRef="BSActivateService" targetRef="Gateway_01737r5" />
    <bpmn:serviceTask id="NMS_PortIn" name="NMS Port In" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1eibpb7</bpmn:incoming>
      <bpmn:outgoing>Flow_1nwvivm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1nwvivm" sourceRef="NMS_PortIn" targetRef="Gateway_1jsb3pc" />
    <bpmn:exclusiveGateway id="Gateway_1jsb3pc" default="Flow_0iwhogl">
      <bpmn:incoming>Flow_1nwvivm</bpmn:incoming>
      <bpmn:outgoing>Flow_0iwhogl</bpmn:outgoing>
      <bpmn:outgoing>Flow_1e2huhb</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0iwhogl" name="Success" sourceRef="Gateway_1jsb3pc" targetRef="SOM_Provisioning" />
    <bpmn:endEvent id="Event_19j8l3e">
      <bpmn:incoming>Flow_1e2huhb</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1e2huhb" name="Failure" sourceRef="Gateway_1jsb3pc" targetRef="Event_19j8l3e">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_125fo0m" sourceRef="Event_1hjhhcv" targetRef="BSCreateService" />
    <bpmn:exclusiveGateway id="Gateway_1tnfnuj" name="portinType">
      <bpmn:incoming>Flow_140e8mx</bpmn:incoming>
      <bpmn:outgoing>Flow_0dsd9jf</bpmn:outgoing>
      <bpmn:outgoing>Flow_02uakhg</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0dsd9jf" name="external" sourceRef="Gateway_1tnfnuj" targetRef="SDPSubmitPortIn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portInType=='PortInFromExternalTelco'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1i8m9uf">
      <bpmn:incoming>Flow_0p1bagf</bpmn:incoming>
      <bpmn:incoming>Flow_0m4ak4u</bpmn:incoming>
      <bpmn:outgoing>Flow_1eibpb7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1eibpb7" sourceRef="Gateway_1i8m9uf" targetRef="NMS_PortIn" />
    <bpmn:sequenceFlow id="Flow_02uakhg" name="singtel" sourceRef="Gateway_1tnfnuj" targetRef="SDPSubmitPortInInternal">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portInType=='PortInFromSingtel'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="SDPSubmitPortInInternal" name="Submit port in internal " camunda:asyncBefore="true" camunda:delegateExpression="${submitPortInInternal}">
      <bpmn:incoming>Flow_02uakhg</bpmn:incoming>
      <bpmn:outgoing>Flow_00nqfml</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0by1zan" default="Flow_036fjqb">
      <bpmn:incoming>Flow_00nqfml</bpmn:incoming>
      <bpmn:outgoing>Flow_0tr8b3n</bpmn:outgoing>
      <bpmn:outgoing>Flow_036fjqb</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_106pd0j">
      <bpmn:incoming>Flow_0tr8b3n</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_00nqfml" sourceRef="SDPSubmitPortInInternal" targetRef="Gateway_0by1zan" />
    <bpmn:sequenceFlow id="Flow_0tr8b3n" name="Failure" sourceRef="Gateway_0by1zan" targetRef="Event_106pd0j">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_036fjqb" name="Success" sourceRef="Gateway_0by1zan" targetRef="MNPChangeProductDateCalculator" />
    <bpmn:intermediateCatchEvent id="MNPChangeProductTimer" name="MNPChangeProductTimer" camunda:asyncBefore="true">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1gvpnv7</bpmn:incoming>
      <bpmn:outgoing>Flow_1o98hl7</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_0oqhtc5">
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${mnpChangeProductDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:serviceTask id="MNPChangeProductDateCalculator" name="Calculate change product date" camunda:asyncBefore="true" camunda:delegateExpression="${mnpChangeProductDateCalculator}">
      <bpmn:incoming>Flow_036fjqb</bpmn:incoming>
      <bpmn:outgoing>Flow_1gvpnv7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1gvpnv7" sourceRef="MNPChangeProductDateCalculator" targetRef="MNPChangeProductTimer" />
    <bpmn:serviceTask id="SDPChangeProductStatus" name="Change Product Status" camunda:asyncBefore="true" camunda:delegateExpression="${changeProductStatus}">
      <bpmn:incoming>Flow_1o98hl7</bpmn:incoming>
      <bpmn:outgoing>Flow_1c7rz14</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1o98hl7" sourceRef="MNPChangeProductTimer" targetRef="SDPChangeProductStatus" />
    <bpmn:exclusiveGateway id="Gateway_1q8hui8" default="Flow_0ea3zl4">
      <bpmn:incoming>Flow_1c7rz14</bpmn:incoming>
      <bpmn:outgoing>Flow_0ea3zl4</bpmn:outgoing>
      <bpmn:outgoing>Flow_1hsp6sc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1c7rz14" sourceRef="SDPChangeProductStatus" targetRef="Gateway_1q8hui8" />
    <bpmn:sequenceFlow id="Flow_0ea3zl4" name="success" sourceRef="Gateway_1q8hui8" targetRef="MNPConnectServiceDateCalculator" />
    <bpmn:endEvent id="Event_0x1103f">
      <bpmn:incoming>Flow_1hsp6sc</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1hsp6sc" name="Failure" sourceRef="Gateway_1q8hui8" targetRef="Event_0x1103f">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:intermediateCatchEvent id="MNPConnectServiceIntTimer" name="MNPConnectServiceInt Timer" camunda:asyncBefore="true">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0vkftax</bpmn:incoming>
      <bpmn:outgoing>Flow_0m4ak4u</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1h0htq8">
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${mnpConnectServiceIntDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_0m4ak4u" sourceRef="MNPConnectServiceIntTimer" targetRef="Gateway_1i8m9uf" />
    <bpmn:serviceTask id="MNPConnectServiceDateCalculator" name="Calculate Connect Service date" camunda:asyncBefore="true" camunda:delegateExpression="${mnpConnectServiceDateCalculator}">
      <bpmn:incoming>Flow_0ea3zl4</bpmn:incoming>
      <bpmn:outgoing>Flow_0vkftax</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0vkftax" sourceRef="MNPConnectServiceDateCalculator" targetRef="MNPConnectServiceIntTimer" />
    <bpmn:serviceTask id="Esb_fetch_EsimDetails" name="Esb_fetchEsimDetails" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0oe0ibu</bpmn:incoming>
      <bpmn:outgoing>Flow_07mf2yy</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_07mf2yy" sourceRef="Esb_fetch_EsimDetails" targetRef="Gateway_1abnh4k" />
    <bpmn:exclusiveGateway id="Gateway_0rs0761" default="Flow_0f7fpri">
      <bpmn:incoming>Flow_0pun1ua</bpmn:incoming>
      <bpmn:outgoing>Flow_0f7fpri</bpmn:outgoing>
      <bpmn:outgoing>Flow_0pnts9g</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0f7fpri" sourceRef="Gateway_0rs0761" targetRef="Gateway_06sfaad" />
    <bpmn:endEvent id="Event_0fbradw">
      <bpmn:incoming>Flow_0pnts9g</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_1coakcn">
      <bpmn:incoming>Flow_0c6zfo4</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1abnh4k" default="Flow_0pc8xqn">
      <bpmn:incoming>Flow_07mf2yy</bpmn:incoming>
      <bpmn:outgoing>Flow_0pc8xqn</bpmn:outgoing>
      <bpmn:outgoing>Flow_0c6zfo4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0pc8xqn" sourceRef="Gateway_1abnh4k" targetRef="Esb_Fetch_EsimCallback_to_DAG" />
    <bpmn:serviceTask id="Esb_Fetch_EsimCallback_to_DAG" name="Esb_FetchEsimCallback_to_DAG" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0pc8xqn</bpmn:incoming>
      <bpmn:outgoing>Flow_0lcc8h3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0lcc8h3" sourceRef="Esb_Fetch_EsimCallback_to_DAG" targetRef="Gateway_0hsfoe0" />
    <bpmn:sequenceFlow id="Flow_0pnts9g" name="Failure" sourceRef="Gateway_0rs0761" targetRef="Event_0fbradw">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0c6zfo4" name="Failure" sourceRef="Gateway_1abnh4k" targetRef="Event_1coakcn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0hsfoe0" default="Flow_1wft9yx">
      <bpmn:incoming>Flow_0lcc8h3</bpmn:incoming>
      <bpmn:outgoing>Flow_1wft9yx</bpmn:outgoing>
      <bpmn:outgoing>Flow_04vf01i</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1wft9yx" sourceRef="Gateway_0hsfoe0" targetRef="Gateway_10924el" />
    <bpmn:exclusiveGateway id="Gateway_10924el">
      <bpmn:incoming>Flow_1wft9yx</bpmn:incoming>
      <bpmn:incoming>Flow_1eu6xl7</bpmn:incoming>
      <bpmn:outgoing>Flow_00o7vzz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_00o7vzz" sourceRef="Gateway_10924el" targetRef="subProcessEndEvent_2" />
    <bpmn:exclusiveGateway id="Gateway_08fbn2h" default="Flow_1eu6xl7">
      <bpmn:incoming>Flow_1hkkal8</bpmn:incoming>
      <bpmn:outgoing>Flow_0oe0ibu</bpmn:outgoing>
      <bpmn:outgoing>Flow_1eu6xl7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0oe0ibu" name="isEsimCall=true" sourceRef="Gateway_08fbn2h" targetRef="Esb_fetch_EsimDetails">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isEsimCall}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_16s8ahy">
      <bpmn:incoming>Flow_04vf01i</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_04vf01i" name="Failure" sourceRef="Gateway_0hsfoe0" targetRef="Event_16s8ahy">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1eu6xl7" sourceRef="Gateway_08fbn2h" targetRef="Gateway_10924el" />
    <bpmn:exclusiveGateway id="Gateway_06sfaad" default="Flow_0pgzxbv">
      <bpmn:incoming>Flow_0f7fpri</bpmn:incoming>
      <bpmn:outgoing>Flow_0xoqa0q</bpmn:outgoing>
      <bpmn:outgoing>Flow_0pgzxbv</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0xoqa0q" name="IsEkycCallreqd =true" sourceRef="Gateway_06sfaad" targetRef="ESBEkycUpdate">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order").element().hasProp('profile')&amp;&amp; workflowData.jsonPath("$.order.profile.account.serviceGroups[0].services[0]").element().hasProp('isKycRequired')&amp;&amp; workflowData.jsonPath("$.order.profile.account.serviceGroups[0].services[0].isKycRequired").stringValue()== 'true' }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1p721c6" default="Flow_0p9hed7">
      <bpmn:incoming>Flow_0ujvh7f</bpmn:incoming>
      <bpmn:outgoing>Flow_0p9hed7</bpmn:outgoing>
      <bpmn:outgoing>Flow_0kagajh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0p9hed7" sourceRef="Gateway_1p721c6" targetRef="Gateway_11wq2ut" />
    <bpmn:exclusiveGateway id="Gateway_11wq2ut">
      <bpmn:incoming>Flow_0p9hed7</bpmn:incoming>
      <bpmn:incoming>Flow_0pgzxbv</bpmn:incoming>
      <bpmn:outgoing>Flow_1hkkal8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1hkkal8" sourceRef="Gateway_11wq2ut" targetRef="Gateway_08fbn2h" />
    <bpmn:serviceTask id="ESBEkycUpdate" name="ESB_EkycUpdate" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0xoqa0q</bpmn:incoming>
      <bpmn:outgoing>Flow_0ujvh7f</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ujvh7f" sourceRef="ESBEkycUpdate" targetRef="Gateway_1p721c6" />
    <bpmn:sequenceFlow id="Flow_0pgzxbv" sourceRef="Gateway_06sfaad" targetRef="Gateway_11wq2ut" />
    <bpmn:endEvent id="Event_0y8duc2">
      <bpmn:incoming>Flow_0kagajh</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0kagajh" name="Failure" sourceRef="Gateway_1p721c6" targetRef="Event_0y8duc2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_18lupa4" name="MNPCallBack" />
  <bpmn:message id="Message_0cxkag2" />
  <bpmn:message id="Message_0uyo7lb" />
  <bpmn:message id="Message_0z0mmot" name="MNPCallBack" />
  <bpmn:message id="Message_14c63bs" name="SOM_Callback" />
  <bpmn:message id="Message_0qgbj08" name="ConnectServiceIntCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="PortInFromExtTelcoOrSingtel">
      <bpmndi:BPMNShape id="Event_1hjhhcv_di" bpmnElement="Event_1hjhhcv">
        <dc:Bounds x="152" y="262" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_07owlzf_di" bpmnElement="Gateway_07owlzf" isMarkerVisible="true">
        <dc:Bounds x="405" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0eso59k_di" bpmnElement="Event_0eso59k">
        <dc:Bounds x="412" y="382" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vvd1dw_di" bpmnElement="Gateway_0vvd1dw" isMarkerVisible="true">
        <dc:Bounds x="735" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1c6l7ml_di" bpmnElement="Event_1c6l7ml">
        <dc:Bounds x="742" y="382" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ldqej4_di" bpmnElement="SDPSubmitPortIn">
        <dc:Bounds x="1130" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0cll812_di" bpmnElement="BSAddSubscription">
        <dc:Bounds x="560" y="240" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_06ca3dv_di" bpmnElement="BSCreateService">
        <dc:Bounds x="250" y="240" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0eb5jrb_di" bpmnElement="Gateway_0eb5jrb" isMarkerVisible="true">
        <dc:Bounds x="1355" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_052uwjo_di" bpmnElement="Event_052uwjo">
        <dc:Bounds x="1362" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04ffx6j_di" bpmnElement="ConnectServiceIntCallback">
        <dc:Bounds x="1730" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_17ma6e6_di" bpmnElement="SOM_Provisioning">
        <dc:Bounds x="2340" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0paffsv_di" bpmnElement="Gateway_0paffsv" isMarkerVisible="true">
        <dc:Bounds x="2525" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_05qjnsn_di" bpmnElement="Event_05qjnsn">
        <dc:Bounds x="2532" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0i53tmd_di" bpmnElement="SOM_Callback">
        <dc:Bounds x="2660" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0oq1n56_di" bpmnElement="Gateway_0oq1n56" isMarkerVisible="true">
        <dc:Bounds x="2845" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0bb5bnp_di" bpmnElement="Event_0bb5bnp">
        <dc:Bounds x="2852" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0iwqp7u_di" bpmnElement="Gateway_0iwqp7u" isMarkerVisible="true">
        <dc:Bounds x="3265" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0k3lwgh_di" bpmnElement="Gateway_0k3lwgh" isMarkerVisible="true">
        <dc:Bounds x="3715" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1kjv2vi_di" bpmnElement="Gateway_1kjv2vi" isMarkerVisible="true">
        <dc:Bounds x="4035" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ydo4qs_di" bpmnElement="Event_1ydo4qs">
        <dc:Bounds x="4042" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1cd2m4m_di" bpmnElement="Event_1cd2m4m">
        <dc:Bounds x="3722" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_08x8wf7_di" bpmnElement="Event_08x8wf7">
        <dc:Bounds x="3272" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1jj6utl_di" bpmnElement="BSActivateService">
        <dc:Bounds x="4170" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0xy8epi_di" bpmnElement="ESBCreateSubscriber">
        <dc:Bounds x="3530" y="240" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1qccgh3_di" bpmnElement="ESB_CreateAccount">
        <dc:Bounds x="3120" y="240" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rkhdmw" bpmnElement="OCSOfferActivationProcess">
        <dc:Bounds x="3860" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0qhfx28_di" bpmnElement="Gateway_0qhfx28" isMarkerVisible="true">
        <dc:Bounds x="2985" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1k23cvg_di" bpmnElement="Gateway_1k23cvg" isMarkerVisible="true">
        <dc:Bounds x="3415" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1bzcfnf" bpmnElement="subProcessEndEvent_2">
        <dc:Bounds x="6132" y="262" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_01737r5_di" bpmnElement="Gateway_01737r5" isMarkerVisible="true">
        <dc:Bounds x="4365" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1fnqo58_di" bpmnElement="Event_1fnqo58">
        <dc:Bounds x="4372" y="364" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_15kqr60" bpmnElement="OCS_ServicePortDetailsUpdate">
        <dc:Bounds x="4500" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_137domw" bpmnElement="NMS_PortIn">
        <dc:Bounds x="2060" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1jsb3pc_di" bpmnElement="Gateway_1jsb3pc" isMarkerVisible="true">
        <dc:Bounds x="2205" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_19j8l3e_di" bpmnElement="Event_19j8l3e">
        <dc:Bounds x="2212" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tnfnuj_di" bpmnElement="Gateway_1tnfnuj" isMarkerVisible="true">
        <dc:Bounds x="875" y="255" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="875" y="312" width="52" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1i8m9uf_di" bpmnElement="Gateway_1i8m9uf" isMarkerVisible="true">
        <dc:Bounds x="1975" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0rqsj6k" bpmnElement="SDPSubmitPortInInternal">
        <dc:Bounds x="930" y="40" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0exy4cb" bpmnElement="Gateway_0by1zan" isMarkerVisible="true">
        <dc:Bounds x="1065" y="55" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ab70y0" bpmnElement="Event_106pd0j">
        <dc:Bounds x="1072" y="142" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1e0z73s" bpmnElement="MNPChangeProductTimer">
        <dc:Bounds x="1332" y="62" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1310" y="105" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1lgu8v2" bpmnElement="MNPChangeProductDateCalculator">
        <dc:Bounds x="1180" y="40" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_01mqaj1" bpmnElement="SDPChangeProductStatus">
        <dc:Bounds x="1430" y="40" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1q8hui8_di" bpmnElement="Gateway_1q8hui8" isMarkerVisible="true">
        <dc:Bounds x="1555" y="55" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0x1103f_di" bpmnElement="Event_0x1103f">
        <dc:Bounds x="1562" y="132" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1mzwi0d_di" bpmnElement="MNPConnectServiceIntTimer">
        <dc:Bounds x="1942" y="62" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1917" y="105" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0weliw6" bpmnElement="MNPConnectServiceDateCalculator">
        <dc:Bounds x="1730" y="40" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0k0o7rj" bpmnElement="Esb_fetch_EsimDetails">
        <dc:Bounds x="5480" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06hawlw" bpmnElement="Gateway_0rs0761" isMarkerVisible="true">
        <dc:Bounds x="4675" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ydeo3u" bpmnElement="Event_0fbradw">
        <dc:Bounds x="4682" y="382" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ujklzi" bpmnElement="Event_1coakcn">
        <dc:Bounds x="5672" y="382" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0qtgn8d" bpmnElement="Gateway_1abnh4k" isMarkerVisible="true">
        <dc:Bounds x="5665" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1j6e8ct" bpmnElement="Esb_Fetch_EsimCallback_to_DAG">
        <dc:Bounds x="5770" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0r226yn" bpmnElement="Gateway_0hsfoe0" isMarkerVisible="true">
        <dc:Bounds x="5925" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0bls4n9" bpmnElement="Gateway_10924el" isMarkerVisible="true">
        <dc:Bounds x="6025" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0vuax15" bpmnElement="Gateway_08fbn2h" isMarkerVisible="true">
        <dc:Bounds x="5375" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ecq9gm" bpmnElement="Event_16s8ahy">
        <dc:Bounds x="5932" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rqoyk6" bpmnElement="Gateway_06sfaad" isMarkerVisible="true">
        <dc:Bounds x="4815" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_17jqtz8" bpmnElement="Gateway_1p721c6" isMarkerVisible="true">
        <dc:Bounds x="5165" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1c81b46" bpmnElement="Gateway_11wq2ut" isMarkerVisible="true">
        <dc:Bounds x="5275" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_158jle6" bpmnElement="ESBEkycUpdate">
        <dc:Bounds x="4950" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1hactoy" bpmnElement="Event_0y8duc2">
        <dc:Bounds x="5172" y="392" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1ogj2lu_di" bpmnElement="Flow_1ogj2lu">
        <di:waypoint x="350" y="280" />
        <di:waypoint x="405" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0z4o4f6_di" bpmnElement="Flow_0z4o4f6">
        <di:waypoint x="430" y="305" />
        <di:waypoint x="430" y="382" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="429" y="341" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_163v0ip_di" bpmnElement="Flow_163v0ip">
        <di:waypoint x="455" y="280" />
        <di:waypoint x="560" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="487" y="262" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_116r5g8_di" bpmnElement="Flow_116r5g8">
        <di:waypoint x="660" y="280" />
        <di:waypoint x="735" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ety70b_di" bpmnElement="Flow_0ety70b">
        <di:waypoint x="760" y="305" />
        <di:waypoint x="760" y="382" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="762" y="342" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_140e8mx_di" bpmnElement="Flow_140e8mx">
        <di:waypoint x="785" y="280" />
        <di:waypoint x="875" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="782" y="262" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x790bn_di" bpmnElement="Flow_0x790bn">
        <di:waypoint x="1230" y="280" />
        <di:waypoint x="1355" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1x6vcfz_di" bpmnElement="Flow_1x6vcfz">
        <di:waypoint x="1380" y="305" />
        <di:waypoint x="1380" y="372" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1383" y="312" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uusmv0_di" bpmnElement="Flow_1uusmv0">
        <di:waypoint x="1405" y="280" />
        <di:waypoint x="1730" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1440" y="262" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p1bagf_di" bpmnElement="Flow_0p1bagf">
        <di:waypoint x="1830" y="280" />
        <di:waypoint x="1975" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ax48eu_di" bpmnElement="Flow_0ax48eu">
        <di:waypoint x="2440" y="280" />
        <di:waypoint x="2525" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dm3ryx_di" bpmnElement="Flow_0dm3ryx">
        <di:waypoint x="2550" y="305" />
        <di:waypoint x="2550" y="372" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2563" y="337" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h5qypc_di" bpmnElement="Flow_1h5qypc">
        <di:waypoint x="2575" y="280" />
        <di:waypoint x="2660" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2596" y="262" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18rb9ws_di" bpmnElement="Flow_18rb9ws">
        <di:waypoint x="2760" y="280" />
        <di:waypoint x="2845" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xrblsx_di" bpmnElement="Flow_0xrblsx">
        <di:waypoint x="2895" y="280" />
        <di:waypoint x="2985" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2916" y="262" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dwn11l_di" bpmnElement="Flow_1dwn11l">
        <di:waypoint x="2870" y="305" />
        <di:waypoint x="2870" y="372" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2883" y="337" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wc6i9r_di" bpmnElement="Flow_1wc6i9r">
        <di:waypoint x="3220" y="280" />
        <di:waypoint x="3265" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c4od85_di" bpmnElement="Flow_1c4od85">
        <di:waypoint x="3315" y="280" />
        <di:waypoint x="3415" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3337" y="262" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fb7pav_di" bpmnElement="Flow_1fb7pav">
        <di:waypoint x="3630" y="280" />
        <di:waypoint x="3715" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0toy5xo_di" bpmnElement="Flow_0toy5xo">
        <di:waypoint x="4085" y="280" />
        <di:waypoint x="4170" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4107" y="262" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04jyj1k_di" bpmnElement="Flow_04jyj1k">
        <di:waypoint x="4060" y="305" />
        <di:waypoint x="4060" y="372" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4073" y="323" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u34h4a_di" bpmnElement="Flow_0u34h4a">
        <di:waypoint x="3740" y="305" />
        <di:waypoint x="3740" y="372" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3753" y="337" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t5yaxs_di" bpmnElement="Flow_1t5yaxs">
        <di:waypoint x="3290" y="305" />
        <di:waypoint x="3290" y="372" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3303" y="337" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0g9r5rm_di" bpmnElement="Flow_0g9r5rm">
        <di:waypoint x="3765" y="280" />
        <di:waypoint x="3860" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3792" y="262" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mwgfrx_di" bpmnElement="Flow_0mwgfrx">
        <di:waypoint x="3960" y="280" />
        <di:waypoint x="4035" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rtt3ow_di" bpmnElement="Flow_0rtt3ow">
        <di:waypoint x="3035" y="280" />
        <di:waypoint x="3120" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3070" y="262" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jwg310_di" bpmnElement="Flow_1jwg310">
        <di:waypoint x="3465" y="280" />
        <di:waypoint x="3530" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0aljmhn_di" bpmnElement="Flow_0aljmhn">
        <di:waypoint x="3010" y="255" />
        <di:waypoint x="3010" y="160" />
        <di:waypoint x="3440" y="160" />
        <di:waypoint x="3440" y="255" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3216" y="142" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pun1ua_di" bpmnElement="Flow_0pun1ua">
        <di:waypoint x="4600" y="280" />
        <di:waypoint x="4675" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1u2akys_di" bpmnElement="Flow_1u2akys">
        <di:waypoint x="4415" y="280" />
        <di:waypoint x="4500" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4436" y="262" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oo4ugl_di" bpmnElement="Flow_1oo4ugl">
        <di:waypoint x="4390" y="305" />
        <di:waypoint x="4390" y="364" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4403" y="327" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ym5p93_di" bpmnElement="Flow_0ym5p93">
        <di:waypoint x="4270" y="280" />
        <di:waypoint x="4365" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nwvivm_di" bpmnElement="Flow_1nwvivm">
        <di:waypoint x="2160" y="280" />
        <di:waypoint x="2205" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0iwhogl_di" bpmnElement="Flow_0iwhogl">
        <di:waypoint x="2255" y="280" />
        <di:waypoint x="2340" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2276" y="262" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1e2huhb_di" bpmnElement="Flow_1e2huhb">
        <di:waypoint x="2230" y="305" />
        <di:waypoint x="2230" y="372" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2253" y="337" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_125fo0m_di" bpmnElement="Flow_125fo0m">
        <di:waypoint x="188" y="280" />
        <di:waypoint x="250" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dsd9jf_di" bpmnElement="Flow_0dsd9jf">
        <di:waypoint x="925" y="280" />
        <di:waypoint x="1130" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="938" y="262" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eibpb7_di" bpmnElement="Flow_1eibpb7">
        <di:waypoint x="2025" y="280" />
        <di:waypoint x="2060" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02uakhg_di" bpmnElement="Flow_02uakhg">
        <di:waypoint x="900" y="255" />
        <di:waypoint x="900" y="80" />
        <di:waypoint x="930" y="80" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="864" y="173" width="32" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_02m025e" bpmnElement="Flow_00nqfml">
        <di:waypoint x="1030" y="80" />
        <di:waypoint x="1065" y="80" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0e2yw0d" bpmnElement="Flow_0tr8b3n">
        <di:waypoint x="1090" y="105" />
        <di:waypoint x="1090" y="142" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1091" y="114" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0bdyfx9" bpmnElement="Flow_036fjqb">
        <di:waypoint x="1115" y="80" />
        <di:waypoint x="1180" y="80" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1118" y="53" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0sqa0g9" bpmnElement="Flow_1gvpnv7">
        <di:waypoint x="1280" y="80" />
        <di:waypoint x="1332" y="80" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o98hl7_di" bpmnElement="Flow_1o98hl7">
        <di:waypoint x="1368" y="80" />
        <di:waypoint x="1430" y="80" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c7rz14_di" bpmnElement="Flow_1c7rz14">
        <di:waypoint x="1530" y="80" />
        <di:waypoint x="1555" y="80" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ea3zl4_di" bpmnElement="Flow_0ea3zl4">
        <di:waypoint x="1605" y="80" />
        <di:waypoint x="1730" y="80" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1643" y="53" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hsp6sc_di" bpmnElement="Flow_1hsp6sc">
        <di:waypoint x="1580" y="105" />
        <di:waypoint x="1580" y="132" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1593" y="112" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0m4ak4u_di" bpmnElement="Flow_0m4ak4u">
        <di:waypoint x="1978" y="80" />
        <di:waypoint x="2000" y="80" />
        <di:waypoint x="2000" y="255" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vkftax_di" bpmnElement="Flow_0vkftax">
        <di:waypoint x="1830" y="80" />
        <di:waypoint x="1942" y="80" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07mf2yy_di" bpmnElement="Flow_07mf2yy">
        <di:waypoint x="5580" y="280" />
        <di:waypoint x="5665" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f7fpri_di" bpmnElement="Flow_0f7fpri">
        <di:waypoint x="4725" y="280" />
        <di:waypoint x="4815" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pc8xqn_di" bpmnElement="Flow_0pc8xqn">
        <di:waypoint x="5715" y="280" />
        <di:waypoint x="5770" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lcc8h3_di" bpmnElement="Flow_0lcc8h3">
        <di:waypoint x="5870" y="280" />
        <di:waypoint x="5925" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pnts9g_di" bpmnElement="Flow_0pnts9g">
        <di:waypoint x="4700" y="305" />
        <di:waypoint x="4700" y="382" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4699" y="341" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c6zfo4_di" bpmnElement="Flow_0c6zfo4">
        <di:waypoint x="5690" y="305" />
        <di:waypoint x="5690" y="382" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5702" y="327" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wft9yx_di" bpmnElement="Flow_1wft9yx">
        <di:waypoint x="5975" y="280" />
        <di:waypoint x="6025" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00o7vzz_di" bpmnElement="Flow_00o7vzz">
        <di:waypoint x="6075" y="280" />
        <di:waypoint x="6132" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oe0ibu_di" bpmnElement="Flow_0oe0ibu">
        <di:waypoint x="5425" y="280" />
        <di:waypoint x="5480" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5361" y="312" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04vf01i_di" bpmnElement="Flow_04vf01i">
        <di:waypoint x="5950" y="305" />
        <di:waypoint x="5950" y="372" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5962" y="323" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eu6xl7_di" bpmnElement="Flow_1eu6xl7">
        <di:waypoint x="5400" y="255" />
        <di:waypoint x="5400" y="180" />
        <di:waypoint x="6050" y="180" />
        <di:waypoint x="6050" y="255" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xoqa0q_di" bpmnElement="Flow_0xoqa0q">
        <di:waypoint x="4865" y="280" />
        <di:waypoint x="4950" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4803" y="305" width="74" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p9hed7_di" bpmnElement="Flow_0p9hed7">
        <di:waypoint x="5215" y="280" />
        <di:waypoint x="5275" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hkkal8_di" bpmnElement="Flow_1hkkal8">
        <di:waypoint x="5325" y="280" />
        <di:waypoint x="5375" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ujvh7f_di" bpmnElement="Flow_0ujvh7f">
        <di:waypoint x="5050" y="280" />
        <di:waypoint x="5165" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pgzxbv_di" bpmnElement="Flow_0pgzxbv">
        <di:waypoint x="4840" y="255" />
        <di:waypoint x="4840" y="190" />
        <di:waypoint x="5300" y="190" />
        <di:waypoint x="5300" y="255" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kagajh_di" bpmnElement="Flow_0kagajh">
        <di:waypoint x="5190" y="305" />
        <di:waypoint x="5190" y="392" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5189" y="346" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
