<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.28.0">
  <bpmn:process id="PortInFromMvnoToMvno" name="PortInFromMvnoToMvno" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="Event_160bb6g">
      <bpmn:outgoing>Flow_0lyuhd6</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0lyuhd6" sourceRef="Event_160bb6g" targetRef="MNPDeProvDateCalculator" />
    <bpmn:serviceTask id="SOM_DeleteService" name="Delete service in SOM (from donor MVNO)" camunda:asyncBefore="true" camunda:delegateExpression="${somTerminateService}">
      <bpmn:documentation>Delete service in HLR via SOM (from donor MVNO)</bpmn:documentation>
      <bpmn:incoming>Flow_1whcpqm</bpmn:incoming>
      <bpmn:outgoing>Flow_0rpziyj</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_14fbyzh" default="Flow_0gp4y2v">
      <bpmn:incoming>Flow_0rpziyj</bpmn:incoming>
      <bpmn:outgoing>Flow_0gp4y2v</bpmn:outgoing>
      <bpmn:outgoing>Flow_0d02e9s</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0rpziyj" sourceRef="SOM_DeleteService" targetRef="Gateway_14fbyzh" />
    <bpmn:receiveTask id="SOM_DeleteServiceCallback" name="Wait for SOM Delete Callback" camunda:asyncBefore="true" messageRef="Message_2gkiae9">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOM_DeleteService" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOM_DeleteService</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0gp4y2v</bpmn:incoming>
      <bpmn:outgoing>Flow_1yjwcds</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_0gp4y2v" name="Success" sourceRef="Gateway_14fbyzh" targetRef="SOM_DeleteServiceCallback" />
    <bpmn:endEvent id="Event_0tct2zi">
      <bpmn:incoming>Flow_0d02e9s</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0d02e9s" name="Failure" sourceRef="Gateway_14fbyzh" targetRef="Event_0tct2zi">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0gopjhu" default="Flow_1q2va1c">
      <bpmn:incoming>Flow_1yjwcds</bpmn:incoming>
      <bpmn:outgoing>Flow_0qi65s5</bpmn:outgoing>
      <bpmn:outgoing>Flow_1q2va1c</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1yjwcds" sourceRef="SOM_DeleteServiceCallback" targetRef="Gateway_0gopjhu" />
    <bpmn:endEvent id="Event_15g1jjf">
      <bpmn:incoming>Flow_0qi65s5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0qi65s5" name="Failure" sourceRef="Gateway_0gopjhu" targetRef="Event_15g1jjf">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1q2va1c" name="Success" sourceRef="Gateway_0gopjhu" targetRef="ESB_TerminateService" />
    <bpmn:serviceTask id="ESB_TerminateService" name="Delete Subscriber in OCS (via ESB) - from donor MVNO" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1q2va1c</bpmn:incoming>
      <bpmn:outgoing>Flow_07rhotr</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_19y336j" default="Flow_073wysd">
      <bpmn:incoming>Flow_07rhotr</bpmn:incoming>
      <bpmn:outgoing>Flow_0xc6ko3</bpmn:outgoing>
      <bpmn:outgoing>Flow_073wysd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_07rhotr" sourceRef="ESB_TerminateService" targetRef="Gateway_19y336j" />
    <bpmn:endEvent id="Event_1sekyka">
      <bpmn:incoming>Flow_0xc6ko3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0xc6ko3" name="Failure" sourceRef="Gateway_19y336j" targetRef="Event_1sekyka">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_073wysd" name="Success" sourceRef="Gateway_19y336j" targetRef="BS_TerminateService" />
    <bpmn:exclusiveGateway id="Gateway_1x6bml3" default="Flow_00j83nk">
      <bpmn:incoming>Flow_027u8fg</bpmn:incoming>
      <bpmn:outgoing>Flow_1mx0h49</bpmn:outgoing>
      <bpmn:outgoing>Flow_00j83nk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_027u8fg" sourceRef="BS_TerminateService" targetRef="Gateway_1x6bml3" />
    <bpmn:serviceTask id="BS_TerminateService" name="Terminate the service from Billing System - from donor MVNO" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_073wysd</bpmn:incoming>
      <bpmn:outgoing>Flow_027u8fg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0hmpn8m">
      <bpmn:incoming>Flow_1mx0h49</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1mx0h49" name="Failure" sourceRef="Gateway_1x6bml3" targetRef="Event_0hmpn8m">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_012s1rh" default="Flow_1cviudj">
      <bpmn:incoming>Flow_02tbb80</bpmn:incoming>
      <bpmn:outgoing>Flow_1ky7vge</bpmn:outgoing>
      <bpmn:outgoing>Flow_1cviudj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1helrod">
      <bpmn:incoming>Flow_1ky7vge</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_07owlzf" default="Flow_163v0ip">
      <bpmn:incoming>Flow_1ogj2lu</bpmn:incoming>
      <bpmn:outgoing>Flow_0z4o4f6</bpmn:outgoing>
      <bpmn:outgoing>Flow_163v0ip</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0eso59k">
      <bpmn:incoming>Flow_0z4o4f6</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0vvd1dw" default="Flow_1ix4ojd">
      <bpmn:incoming>Flow_116r5g8</bpmn:incoming>
      <bpmn:outgoing>Flow_0ety70b</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ix4ojd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1c6l7ml">
      <bpmn:incoming>Flow_0ety70b</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSAddSubscriptionMVNO" name="BS Create Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_163v0ip</bpmn:incoming>
      <bpmn:outgoing>Flow_116r5g8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSCreateServiceMVNO" name="Billing Service creation" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1cviudj</bpmn:incoming>
      <bpmn:outgoing>Flow_1ogj2lu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1ky7vge" name="Failure" sourceRef="Gateway_012s1rh" targetRef="Event_1helrod">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1cviudj" name="Success" sourceRef="Gateway_012s1rh" targetRef="BSCreateServiceMVNO" />
    <bpmn:sequenceFlow id="Flow_1ogj2lu" sourceRef="BSCreateServiceMVNO" targetRef="Gateway_07owlzf" />
    <bpmn:sequenceFlow id="Flow_0z4o4f6" name="Failure" sourceRef="Gateway_07owlzf" targetRef="Event_0eso59k">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_163v0ip" name="Success" sourceRef="Gateway_07owlzf" targetRef="BSAddSubscriptionMVNO" />
    <bpmn:sequenceFlow id="Flow_116r5g8" sourceRef="BSAddSubscriptionMVNO" targetRef="Gateway_0vvd1dw" />
    <bpmn:sequenceFlow id="Flow_0ety70b" name="Failure" sourceRef="Gateway_0vvd1dw" targetRef="Event_1c6l7ml">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:intermediateCatchEvent id="MnpProvisioningTimer" name="MnpProvisioningTimer" camunda:asyncBefore="true">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1f9sg5i</bpmn:incoming>
      <bpmn:outgoing>Flow_0y0kwju</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1ueltgs">
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${mnpProvisioningDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:intermediateCatchEvent id="MnpDeProvisioningTimer" name="MnpDeProvisioningTimer" camunda:asyncBefore="true">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1coiasm</bpmn:incoming>
      <bpmn:outgoing>Flow_1l9n203</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_10m5tvc">
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${mnpDeProvisioningDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_1l9n203" sourceRef="MnpDeProvisioningTimer" targetRef="SOMFetchServiceRegistry" />
    <bpmn:sequenceFlow id="Flow_1coiasm" sourceRef="MNPDeProvDateCalculator" targetRef="MnpDeProvisioningTimer" />
    <bpmn:serviceTask id="MNPDeProvDateCalculator" name="Calculate de provisioning date" camunda:asyncBefore="true" camunda:delegateExpression="${mnpProvisioningDateCalculator}">
      <bpmn:incoming>Flow_0lyuhd6</bpmn:incoming>
      <bpmn:outgoing>Flow_1coiasm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="MNPProvDateCalculator" name="Calculate provisioning date" camunda:asyncBefore="true" camunda:delegateExpression="${mnpProvisioningDateCalculator}">
      <bpmn:incoming>Flow_1kkqns0</bpmn:incoming>
      <bpmn:outgoing>Flow_1f9sg5i</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1f9sg5i" sourceRef="MNPProvDateCalculator" targetRef="MnpProvisioningTimer" />
    <bpmn:serviceTask id="SOM_ProvisioningMVNO" name="Provisioning in HLR (via SOM)" camunda:asyncBefore="true" camunda:delegateExpression="${somServiceOrderCreation}">
      <bpmn:incoming>Flow_1ix4ojd</bpmn:incoming>
      <bpmn:outgoing>Flow_0ax48eu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0paffsv" default="Flow_1h5qypc">
      <bpmn:incoming>Flow_0ax48eu</bpmn:incoming>
      <bpmn:outgoing>Flow_0dm3ryx</bpmn:outgoing>
      <bpmn:outgoing>Flow_1h5qypc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_05qjnsn">
      <bpmn:incoming>Flow_0dm3ryx</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:receiveTask id="SOM_CallbackMVNO" name="Wait for SOM callback" camunda:asyncBefore="true" messageRef="Message_14c63bs">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOM_ProvisioningMVNO</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOM_ProvisioningMVNO" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1h5qypc</bpmn:incoming>
      <bpmn:outgoing>Flow_18rb9ws</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_0oq1n56" default="Flow_0xrblsx">
      <bpmn:incoming>Flow_18rb9ws</bpmn:incoming>
      <bpmn:outgoing>Flow_0xrblsx</bpmn:outgoing>
      <bpmn:outgoing>Flow_1dwn11l</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0bb5bnp">
      <bpmn:incoming>Flow_1dwn11l</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0iwqp7u" default="Flow_1c4od85">
      <bpmn:incoming>Flow_1wc6i9r</bpmn:incoming>
      <bpmn:outgoing>Flow_1c4od85</bpmn:outgoing>
      <bpmn:outgoing>Flow_1t5yaxs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0k3lwgh" default="Flow_0g9r5rm">
      <bpmn:incoming>Flow_1fb7pav</bpmn:incoming>
      <bpmn:outgoing>Flow_0u34h4a</bpmn:outgoing>
      <bpmn:outgoing>Flow_0g9r5rm</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1kjv2vi" default="Flow_0toy5xo">
      <bpmn:incoming>Flow_0mwgfrx</bpmn:incoming>
      <bpmn:outgoing>Flow_0toy5xo</bpmn:outgoing>
      <bpmn:outgoing>Flow_04jyj1k</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1ydo4qs">
      <bpmn:incoming>Flow_04jyj1k</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_1cd2m4m">
      <bpmn:incoming>Flow_0u34h4a</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_08x8wf7">
      <bpmn:incoming>Flow_1t5yaxs</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="ESB_CreateAccountMVNO" name="Create Account in OCS via ESB" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0m7jomk</bpmn:incoming>
      <bpmn:outgoing>Flow_1wc6i9r</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="ESBCreateSubscriberMVNO" name="Create Service in OCS via ESB" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0k4eev0</bpmn:incoming>
      <bpmn:outgoing>Flow_1fb7pav</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:callActivity id="OCSOfferActivationProcess" name="OCS Offer Activation Process" camunda:asyncBefore="true" calledElement="OCSOfferActivationProcess" camunda:calledElementBinding="deployment">
      <bpmn:documentation>Iterate over addon subs in BS Add subs response list. planType  1 is base, 0 is addon</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:out variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0g9r5rm</bpmn:incoming>
      <bpmn:outgoing>Flow_0mwgfrx</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.BSAddSubscriptionMVNOResponseAttributes.subscriptions[?(@.planType==&#39;0&#39;)]&#34;).elementList()}" camunda:elementVariable="execution">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:serviceTask id="BSActivateServiceMVNO" name="Service Activation in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0toy5xo</bpmn:incoming>
      <bpmn:outgoing>Flow_1s9ykb6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="subProcessEndEvent_2">
      <bpmn:incoming>Flow_0kk98c9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0ax48eu" sourceRef="SOM_ProvisioningMVNO" targetRef="Gateway_0paffsv" />
    <bpmn:sequenceFlow id="Flow_0dm3ryx" name="Failure" sourceRef="Gateway_0paffsv" targetRef="Event_05qjnsn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1h5qypc" name="Success" sourceRef="Gateway_0paffsv" targetRef="SOM_CallbackMVNO" />
    <bpmn:sequenceFlow id="Flow_18rb9ws" sourceRef="SOM_CallbackMVNO" targetRef="Gateway_0oq1n56" />
    <bpmn:sequenceFlow id="Flow_0xrblsx" name="Success" sourceRef="Gateway_0oq1n56" targetRef="Gateway_0o6n36u" />
    <bpmn:sequenceFlow id="Flow_1dwn11l" name="Failure" sourceRef="Gateway_0oq1n56" targetRef="Event_0bb5bnp">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wc6i9r" sourceRef="ESB_CreateAccountMVNO" targetRef="Gateway_0iwqp7u" />
    <bpmn:sequenceFlow id="Flow_1c4od85" name="Success" sourceRef="Gateway_0iwqp7u" targetRef="Gateway_1ke31hk" />
    <bpmn:sequenceFlow id="Flow_1t5yaxs" name="Failure" sourceRef="Gateway_0iwqp7u" targetRef="Event_08x8wf7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1fb7pav" sourceRef="ESBCreateSubscriberMVNO" targetRef="Gateway_0k3lwgh" />
    <bpmn:sequenceFlow id="Flow_0u34h4a" name="Failure" sourceRef="Gateway_0k3lwgh" targetRef="Event_1cd2m4m">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0g9r5rm" name="Success" sourceRef="Gateway_0k3lwgh" targetRef="OCSOfferActivationProcess" />
    <bpmn:sequenceFlow id="Flow_0mwgfrx" sourceRef="OCSOfferActivationProcess" targetRef="Gateway_1kjv2vi" />
    <bpmn:sequenceFlow id="Flow_0toy5xo" name="Success" sourceRef="Gateway_1kjv2vi" targetRef="BSActivateServiceMVNO" />
    <bpmn:sequenceFlow id="Flow_04jyj1k" name="Failure" sourceRef="Gateway_1kjv2vi" targetRef="Event_1ydo4qs">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1s9ykb6" sourceRef="BSActivateServiceMVNO" targetRef="Gateway_01737r5" />
    <bpmn:sequenceFlow id="Flow_1ix4ojd" name="success" sourceRef="Gateway_0vvd1dw" targetRef="SOM_ProvisioningMVNO" />
    <bpmn:exclusiveGateway id="Gateway_0o6n36u" default="Flow_0m7jomk">
      <bpmn:incoming>Flow_0xrblsx</bpmn:incoming>
      <bpmn:outgoing>Flow_0m7jomk</bpmn:outgoing>
      <bpmn:outgoing>Flow_1t8ha9s</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0m7jomk" name="No" sourceRef="Gateway_0o6n36u" targetRef="ESB_CreateAccountMVNO" />
    <bpmn:exclusiveGateway id="Gateway_1ke31hk">
      <bpmn:incoming>Flow_1c4od85</bpmn:incoming>
      <bpmn:incoming>Flow_1t8ha9s</bpmn:incoming>
      <bpmn:outgoing>Flow_0k4eev0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0k4eev0" sourceRef="Gateway_1ke31hk" targetRef="ESBCreateSubscriberMVNO" />
    <bpmn:sequenceFlow id="Flow_1t8ha9s" name="Yes" sourceRef="Gateway_0o6n36u" targetRef="Gateway_1ke31hk">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isExistingAccount}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_01737r5" default="Flow_1u2akys">
      <bpmn:incoming>Flow_1s9ykb6</bpmn:incoming>
      <bpmn:outgoing>Flow_1u2akys</bpmn:outgoing>
      <bpmn:outgoing>Flow_1oo4ugl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1u2akys" name="Success" sourceRef="Gateway_01737r5" targetRef="OCS_ServicePortDetailsUpdateMVNO" />
    <bpmn:endEvent id="Event_1fnqo58">
      <bpmn:incoming>Flow_1oo4ugl</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1oo4ugl" name="Failure" sourceRef="Gateway_01737r5" targetRef="Event_1fnqo58">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="OCS_ServicePortDetailsUpdateMVNO" name="OCS ServicePortDetailsUpdate" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1u2akys</bpmn:incoming>
      <bpmn:outgoing>Flow_0pun1ua</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0pun1ua" sourceRef="OCS_ServicePortDetailsUpdateMVNO" targetRef="Gateway_1bgat7k" />
    <bpmn:serviceTask id="NMS_MVNOPortIn" name="NMS Port In" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_01btt5p</bpmn:incoming>
      <bpmn:outgoing>Flow_02tbb80</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0y0kwju" sourceRef="MnpProvisioningTimer" targetRef="NMS_UnPairing" />
    <bpmn:sequenceFlow id="Flow_02tbb80" sourceRef="NMS_MVNOPortIn" targetRef="Gateway_012s1rh" />
    <bpmn:sequenceFlow id="Flow_00j83nk" name="Success" sourceRef="Gateway_1x6bml3" targetRef="MNPPortOutOrderGeneration" />
    <bpmn:exclusiveGateway id="Gateway_1qvsl1k" default="Flow_1whcpqm">
      <bpmn:incoming>SequenceFlow_0ljkt7u</bpmn:incoming>
      <bpmn:outgoing>Flow_02e3uev</bpmn:outgoing>
      <bpmn:outgoing>Flow_1whcpqm</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_01pb4gg">
      <bpmn:incoming>Flow_02e3uev</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch Service Registry" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1l9n203</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0ljkt7u</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0ljkt7u" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_1qvsl1k" />
    <bpmn:sequenceFlow id="Flow_02e3uev" name="Failure" sourceRef="Gateway_1qvsl1k" targetRef="Event_01pb4gg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1whcpqm" name="Success" sourceRef="Gateway_1qvsl1k" targetRef="SOM_DeleteService" />
    <bpmn:exclusiveGateway id="Gateway_16v0h0n" default="Flow_01btt5p">
      <bpmn:incoming>Flow_0tybm4u</bpmn:incoming>
      <bpmn:outgoing>Flow_0tfcins</bpmn:outgoing>
      <bpmn:outgoing>Flow_01btt5p</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0hp3nnp">
      <bpmn:incoming>Flow_0tfcins</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="NMS_UnPairing" name="NMS UnPairing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0y0kwju</bpmn:incoming>
      <bpmn:outgoing>Flow_0tybm4u</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0tybm4u" sourceRef="NMS_UnPairing" targetRef="Gateway_16v0h0n" />
    <bpmn:sequenceFlow id="Flow_0tfcins" name="Failure" sourceRef="Gateway_16v0h0n" targetRef="Event_0hp3nnp">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_01btt5p" name="Success" sourceRef="Gateway_16v0h0n" targetRef="NMS_MVNOPortIn" />
    <bpmn:sequenceFlow id="Flow_1kkqns0" sourceRef="MNPPortOutOrderGeneration" targetRef="MNPProvDateCalculator" />
    <bpmn:serviceTask id="MNPPortOutOrderGeneration" name="MNP Port Out Order Generation" camunda:asyncBefore="true" camunda:delegateExpression="${mnpPortoutOrderGenaration}">
      <bpmn:incoming>Flow_00j83nk</bpmn:incoming>
      <bpmn:outgoing>Flow_1kkqns0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1bgat7k" default="Flow_02hn1di">
      <bpmn:incoming>Flow_0pun1ua</bpmn:incoming>
      <bpmn:outgoing>Flow_02hn1di</bpmn:outgoing>
      <bpmn:outgoing>Flow_0s32ksw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_02hn1di" sourceRef="Gateway_1bgat7k" targetRef="Gateway_1mt0g2d" />
    <bpmn:exclusiveGateway id="Gateway_0nmcf1t" default="Flow_1ialxy2">
      <bpmn:incoming>Flow_0zoow0v</bpmn:incoming>
      <bpmn:outgoing>Flow_1ialxy2</bpmn:outgoing>
      <bpmn:outgoing>Flow_17mvox6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ialxy2" name="Success" sourceRef="Gateway_0nmcf1t" targetRef="Esb_Fetch_EsimCallback_MVN0" />
    <bpmn:serviceTask id="Esb_fetchEsimDetails_MVNO" name="Esb_fetchEsimDetails" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_09rk7i6</bpmn:incoming>
      <bpmn:outgoing>Flow_0zoow0v</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0zoow0v" sourceRef="Esb_fetchEsimDetails_MVNO" targetRef="Gateway_0nmcf1t" />
    <bpmn:serviceTask id="Esb_Fetch_EsimCallback_MVN0" name="Esb_FetchEsimCallback_DAG" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1ialxy2</bpmn:incoming>
      <bpmn:outgoing>Flow_1d481qo</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1d481qo" sourceRef="Esb_Fetch_EsimCallback_MVN0" targetRef="Gateway_1ljqcuq" />
    <bpmn:endEvent id="Event_0ofcqxs">
      <bpmn:incoming>Flow_0s32ksw</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_0bb4ijm">
      <bpmn:incoming>Flow_17mvox6</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0s32ksw" name="Failure" sourceRef="Gateway_1bgat7k" targetRef="Event_0ofcqxs">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_17mvox6" name="Failure" sourceRef="Gateway_0nmcf1t" targetRef="Event_0bb4ijm">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1ljqcuq" default="Flow_19uks7w">
      <bpmn:incoming>Flow_1d481qo</bpmn:incoming>
      <bpmn:outgoing>Flow_19uks7w</bpmn:outgoing>
      <bpmn:outgoing>Flow_138mgf4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_19uks7w" name="Success" sourceRef="Gateway_1ljqcuq" targetRef="Gateway_0o200pf" />
    <bpmn:exclusiveGateway id="Gateway_0o200pf">
      <bpmn:incoming>Flow_19uks7w</bpmn:incoming>
      <bpmn:incoming>Flow_0kh137b</bpmn:incoming>
      <bpmn:outgoing>Flow_0kk98c9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0kk98c9" sourceRef="Gateway_0o200pf" targetRef="subProcessEndEvent_2" />
    <bpmn:exclusiveGateway id="Gateway_0y6zsvk" default="Flow_0kh137b">
      <bpmn:incoming>Flow_15yi6th</bpmn:incoming>
      <bpmn:outgoing>Flow_09rk7i6</bpmn:outgoing>
      <bpmn:outgoing>Flow_0kh137b</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_09rk7i6" name="isEsimCall=true" sourceRef="Gateway_0y6zsvk" targetRef="Esb_fetchEsimDetails_MVNO">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isEsimCall}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0kh137b" sourceRef="Gateway_0y6zsvk" targetRef="Gateway_0o200pf" />
    <bpmn:endEvent id="Event_14vb74j">
      <bpmn:incoming>Flow_138mgf4</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_138mgf4" name="Failure" sourceRef="Gateway_1ljqcuq" targetRef="Event_14vb74j">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_03zi2v4">
      <bpmn:incoming>Flow_1fe5rcw</bpmn:incoming>
      <bpmn:incoming>Flow_1izb795</bpmn:incoming>
      <bpmn:outgoing>Flow_15yi6th</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_15yi6th" sourceRef="Gateway_03zi2v4" targetRef="Gateway_0y6zsvk" />
    <bpmn:exclusiveGateway id="Gateway_085p8wx" default="Flow_1fe5rcw">
      <bpmn:incoming>Flow_0ghdhpl</bpmn:incoming>
      <bpmn:outgoing>Flow_1fe5rcw</bpmn:outgoing>
      <bpmn:outgoing>Flow_1wx9s5q</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1fe5rcw" sourceRef="Gateway_085p8wx" targetRef="Gateway_03zi2v4" />
    <bpmn:exclusiveGateway id="Gateway_1mt0g2d" default="Flow_1izb795">
      <bpmn:incoming>Flow_02hn1di</bpmn:incoming>
      <bpmn:outgoing>Flow_10c11y5</bpmn:outgoing>
      <bpmn:outgoing>Flow_1izb795</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_10c11y5" name="IsEkycCallreqd =true" sourceRef="Gateway_1mt0g2d" targetRef="ESBEkycUpdateMVNO">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order").element().hasProp('profile')&amp;&amp; workflowData.jsonPath("$.order.profile.account.serviceGroups[0].services[0]").element().hasProp('isKycRequired') &amp;&amp; workflowData.jsonPath("$.order.profile.account.serviceGroups[0].services[0].isKycRequired").stringValue() == 'true' }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1izb795" sourceRef="Gateway_1mt0g2d" targetRef="Gateway_03zi2v4" />
    <bpmn:serviceTask id="ESBEkycUpdateMVNO" name="ESB_EkycUpdate" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_10c11y5</bpmn:incoming>
      <bpmn:outgoing>Flow_0ghdhpl</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ghdhpl" sourceRef="ESBEkycUpdateMVNO" targetRef="Gateway_085p8wx" />
    <bpmn:endEvent id="Event_0zicvlt">
      <bpmn:incoming>Flow_1wx9s5q</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1wx9s5q" name="Failure" sourceRef="Gateway_085p8wx" targetRef="Event_0zicvlt">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_18lupa4" name="MNPCallBack" />
  <bpmn:message id="Message_0cxkag2" />
  <bpmn:message id="Message_0uyo7lb" />
  <bpmn:message id="Message_0z0mmot" name="MNPCallBack" />
  <bpmn:message id="Message_2gkiae9" name="SOM_DeleteServiceCallback" />
  <bpmn:message id="Message_14c63bs" name="SOM_CallbackMVNO" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="PortInFromMvnoToMvno">
      <bpmndi:BPMNShape id="Event_160bb6g_di" bpmnElement="Event_160bb6g">
        <dc:Bounds x="152" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_19sibhi_di" bpmnElement="SOM_DeleteService">
        <dc:Bounds x="940" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_14fbyzh_di" bpmnElement="Gateway_14fbyzh" isMarkerVisible="true">
        <dc:Bounds x="1185" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_037jqhm_di" bpmnElement="SOM_DeleteServiceCallback">
        <dc:Bounds x="1340" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0tct2zi_di" bpmnElement="Event_0tct2zi">
        <dc:Bounds x="1192" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0gopjhu_di" bpmnElement="Gateway_0gopjhu" isMarkerVisible="true">
        <dc:Bounds x="1525" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_15g1jjf_di" bpmnElement="Event_15g1jjf">
        <dc:Bounds x="1532" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0rlvr5v_di" bpmnElement="ESB_TerminateService">
        <dc:Bounds x="1660" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_19y336j_di" bpmnElement="Gateway_19y336j" isMarkerVisible="true">
        <dc:Bounds x="1845" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1sekyka_di" bpmnElement="Event_1sekyka">
        <dc:Bounds x="1852" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1x6bml3_di" bpmnElement="Gateway_1x6bml3" isMarkerVisible="true">
        <dc:Bounds x="2195" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0mbgjp7_di" bpmnElement="BS_TerminateService">
        <dc:Bounds x="2010" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0hmpn8m_di" bpmnElement="Event_0hmpn8m">
        <dc:Bounds x="2202" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_012s1rh_di" bpmnElement="Gateway_012s1rh" isMarkerVisible="true">
        <dc:Bounds x="3755" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1helrod_di" bpmnElement="Event_1helrod">
        <dc:Bounds x="3762" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_07owlzf_di" bpmnElement="Gateway_07owlzf" isMarkerVisible="true">
        <dc:Bounds x="4095" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0eso59k_di" bpmnElement="Event_0eso59k">
        <dc:Bounds x="4102" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vvd1dw_di" bpmnElement="Gateway_0vvd1dw" isMarkerVisible="true">
        <dc:Bounds x="4455" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1c6l7ml_di" bpmnElement="Event_1c6l7ml">
        <dc:Bounds x="4462" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0cll812_di" bpmnElement="BSAddSubscriptionMVNO">
        <dc:Bounds x="4250" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_06ca3dv_di" bpmnElement="BSCreateServiceMVNO">
        <dc:Bounds x="3900" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_19aqlau_di" bpmnElement="MnpProvisioningTimer">
        <dc:Bounds x="3062" y="182" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3040" y="225" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1v6a7v2" bpmnElement="MnpDeProvisioningTimer">
        <dc:Bounds x="492" y="182" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="470" y="225" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ttxr0h_di" bpmnElement="MNPDeProvDateCalculator">
        <dc:Bounds x="300" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ehrcsx" bpmnElement="MNPProvDateCalculator">
        <dc:Bounds x="2860" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1y1f08p" bpmnElement="SOM_ProvisioningMVNO">
        <dc:Bounds x="4580" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1y0xyvi" bpmnElement="Gateway_0paffsv" isMarkerVisible="true">
        <dc:Bounds x="4765" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0haf1fd" bpmnElement="Event_05qjnsn">
        <dc:Bounds x="4772" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0dzmyxs" bpmnElement="SOM_CallbackMVNO">
        <dc:Bounds x="4900" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0i6fg72" bpmnElement="Gateway_0oq1n56" isMarkerVisible="true">
        <dc:Bounds x="5055" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1sxqe9i" bpmnElement="Event_0bb5bnp">
        <dc:Bounds x="5062" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1a5qlb7" bpmnElement="Gateway_0iwqp7u" isMarkerVisible="true">
        <dc:Bounds x="5435" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_024hzxo" bpmnElement="Gateway_0k3lwgh" isMarkerVisible="true">
        <dc:Bounds x="5815" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0e26yzm" bpmnElement="Gateway_1kjv2vi" isMarkerVisible="true">
        <dc:Bounds x="6135" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_106hnfj" bpmnElement="Event_1ydo4qs">
        <dc:Bounds x="6142" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0fqdxvo" bpmnElement="Event_1cd2m4m">
        <dc:Bounds x="5822" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0pgxq07" bpmnElement="Event_08x8wf7">
        <dc:Bounds x="5442" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ko6pox" bpmnElement="ESB_CreateAccountMVNO">
        <dc:Bounds x="5300" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ajqpbf" bpmnElement="ESBCreateSubscriberMVNO">
        <dc:Bounds x="5680" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rkhdmw" bpmnElement="OCSOfferActivationProcess">
        <dc:Bounds x="5960" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_10xbcnf" bpmnElement="BSActivateServiceMVNO">
        <dc:Bounds x="6270" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1bzcfnf" bpmnElement="subProcessEndEvent_2">
        <dc:Bounds x="8422" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0o6n36u_di" bpmnElement="Gateway_0o6n36u" isMarkerVisible="true">
        <dc:Bounds x="5195" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ke31hk_di" bpmnElement="Gateway_1ke31hk" isMarkerVisible="true">
        <dc:Bounds x="5565" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_01737r5_di" bpmnElement="Gateway_01737r5" isMarkerVisible="true">
        <dc:Bounds x="6425" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1fnqo58_di" bpmnElement="Event_1fnqo58">
        <dc:Bounds x="6432" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_15kqr60" bpmnElement="OCS_ServicePortDetailsUpdateMVNO">
        <dc:Bounds x="6600" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_137domw" bpmnElement="NMS_MVNOPortIn">
        <dc:Bounds x="3580" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1qvsl1k_di" bpmnElement="Gateway_1qvsl1k" isMarkerVisible="true">
        <dc:Bounds x="815" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_01pb4gg_di" bpmnElement="Event_01pb4gg">
        <dc:Bounds x="822" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0hs1oog_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="620" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_01ca6qk" bpmnElement="Gateway_16v0h0n" isMarkerVisible="true">
        <dc:Bounds x="3365" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0m11nlz" bpmnElement="Event_0hp3nnp">
        <dc:Bounds x="3372" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0y88ngl" bpmnElement="NMS_UnPairing">
        <dc:Bounds x="3190" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_05ljudt_di" bpmnElement="MNPPortOutOrderGeneration">
        <dc:Bounds x="2480" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0tzwjnh" bpmnElement="Gateway_1bgat7k" isMarkerVisible="true">
        <dc:Bounds x="6785" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1sfc0rh" bpmnElement="Gateway_0nmcf1t" isMarkerVisible="true">
        <dc:Bounds x="7855" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ztqmia" bpmnElement="Esb_fetchEsimDetails_MVNO">
        <dc:Bounds x="7650" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1nlaylu" bpmnElement="Esb_Fetch_EsimCallback_MVN0">
        <dc:Bounds x="8000" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0sxypgr" bpmnElement="Event_0ofcqxs">
        <dc:Bounds x="6792" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_037ryrg" bpmnElement="Event_0bb4ijm">
        <dc:Bounds x="7862" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0mvnw1n" bpmnElement="Gateway_1ljqcuq" isMarkerVisible="true">
        <dc:Bounds x="8175" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1lgkr1p" bpmnElement="Gateway_0o200pf" isMarkerVisible="true">
        <dc:Bounds x="8295" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1lusdbk" bpmnElement="Gateway_0y6zsvk" isMarkerVisible="true">
        <dc:Bounds x="7505" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1mrhemw" bpmnElement="Event_14vb74j">
        <dc:Bounds x="8182" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1lny68m" bpmnElement="Gateway_03zi2v4" isMarkerVisible="true">
        <dc:Bounds x="7395" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1g7sor5" bpmnElement="Gateway_085p8wx" isMarkerVisible="true">
        <dc:Bounds x="7265" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1tgv6ky" bpmnElement="Gateway_1mt0g2d" isMarkerVisible="true">
        <dc:Bounds x="6935" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ex4od6" bpmnElement="ESBEkycUpdateMVNO">
        <dc:Bounds x="7070" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0bxkw6z" bpmnElement="Event_0zicvlt">
        <dc:Bounds x="7272" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0lyuhd6_di" bpmnElement="Flow_0lyuhd6">
        <di:waypoint x="188" y="200" />
        <di:waypoint x="300" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rpziyj_di" bpmnElement="Flow_0rpziyj">
        <di:waypoint x="1040" y="200" />
        <di:waypoint x="1185" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gp4y2v_di" bpmnElement="Flow_0gp4y2v">
        <di:waypoint x="1235" y="200" />
        <di:waypoint x="1340" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1266" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d02e9s_di" bpmnElement="Flow_0d02e9s">
        <di:waypoint x="1210" y="225" />
        <di:waypoint x="1210" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1223" y="256" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yjwcds_di" bpmnElement="Flow_1yjwcds">
        <di:waypoint x="1440" y="200" />
        <di:waypoint x="1525" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qi65s5_di" bpmnElement="Flow_0qi65s5">
        <di:waypoint x="1550" y="225" />
        <di:waypoint x="1550" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1563" y="256" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1q2va1c_di" bpmnElement="Flow_1q2va1c">
        <di:waypoint x="1575" y="200" />
        <di:waypoint x="1660" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1596" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07rhotr_di" bpmnElement="Flow_07rhotr">
        <di:waypoint x="1760" y="200" />
        <di:waypoint x="1845" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xc6ko3_di" bpmnElement="Flow_0xc6ko3">
        <di:waypoint x="1870" y="225" />
        <di:waypoint x="1870" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1883" y="256" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_073wysd_di" bpmnElement="Flow_073wysd">
        <di:waypoint x="1895" y="200" />
        <di:waypoint x="2010" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1931" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_027u8fg_di" bpmnElement="Flow_027u8fg">
        <di:waypoint x="2110" y="200" />
        <di:waypoint x="2195" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mx0h49_di" bpmnElement="Flow_1mx0h49">
        <di:waypoint x="2220" y="225" />
        <di:waypoint x="2220" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2233" y="266" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ky7vge_di" bpmnElement="Flow_1ky7vge">
        <di:waypoint x="3780" y="225" />
        <di:waypoint x="3780" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3793" y="253" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cviudj_di" bpmnElement="Flow_1cviudj">
        <di:waypoint x="3805" y="200" />
        <di:waypoint x="3900" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3831" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ogj2lu_di" bpmnElement="Flow_1ogj2lu">
        <di:waypoint x="4000" y="200" />
        <di:waypoint x="4095" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0z4o4f6_di" bpmnElement="Flow_0z4o4f6">
        <di:waypoint x="4120" y="225" />
        <di:waypoint x="4120" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4119" y="261" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_163v0ip_di" bpmnElement="Flow_163v0ip">
        <di:waypoint x="4145" y="200" />
        <di:waypoint x="4250" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4177" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_116r5g8_di" bpmnElement="Flow_116r5g8">
        <di:waypoint x="4350" y="200" />
        <di:waypoint x="4455" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ety70b_di" bpmnElement="Flow_0ety70b">
        <di:waypoint x="4480" y="225" />
        <di:waypoint x="4480" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4503" y="257" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1l9n203_di" bpmnElement="Flow_1l9n203">
        <di:waypoint x="528" y="200" />
        <di:waypoint x="620" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1coiasm_di" bpmnElement="Flow_1coiasm">
        <di:waypoint x="400" y="200" />
        <di:waypoint x="492" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1f9sg5i_di" bpmnElement="Flow_1f9sg5i">
        <di:waypoint x="2960" y="200" />
        <di:waypoint x="3062" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0wasdhk" bpmnElement="Flow_0ax48eu">
        <di:waypoint x="4680" y="200" />
        <di:waypoint x="4765" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_13ag58o" bpmnElement="Flow_0dm3ryx">
        <di:waypoint x="4790" y="225" />
        <di:waypoint x="4790" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4803" y="257" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_15uxrbf" bpmnElement="Flow_1h5qypc">
        <di:waypoint x="4815" y="200" />
        <di:waypoint x="4900" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4836" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0naisrc" bpmnElement="Flow_18rb9ws">
        <di:waypoint x="5000" y="200" />
        <di:waypoint x="5055" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1dtvu3m" bpmnElement="Flow_0xrblsx">
        <di:waypoint x="5105" y="200" />
        <di:waypoint x="5195" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5110" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0uk1aam" bpmnElement="Flow_1dwn11l">
        <di:waypoint x="5080" y="225" />
        <di:waypoint x="5080" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5077" y="250" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_02gz397" bpmnElement="Flow_1wc6i9r">
        <di:waypoint x="5400" y="200" />
        <di:waypoint x="5435" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1mcybmq" bpmnElement="Flow_1c4od85">
        <di:waypoint x="5485" y="200" />
        <di:waypoint x="5565" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5499" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0ncevi5" bpmnElement="Flow_1t5yaxs">
        <di:waypoint x="5460" y="225" />
        <di:waypoint x="5460" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5473" y="257" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_111qaa3" bpmnElement="Flow_1fb7pav">
        <di:waypoint x="5780" y="200" />
        <di:waypoint x="5815" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0cqf7bg" bpmnElement="Flow_0u34h4a">
        <di:waypoint x="5840" y="225" />
        <di:waypoint x="5840" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5853" y="257" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0g9r5rm_di" bpmnElement="Flow_0g9r5rm">
        <di:waypoint x="5865" y="200" />
        <di:waypoint x="5960" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5892" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mwgfrx_di" bpmnElement="Flow_0mwgfrx">
        <di:waypoint x="6060" y="200" />
        <di:waypoint x="6135" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_06nsjru" bpmnElement="Flow_0toy5xo">
        <di:waypoint x="6185" y="200" />
        <di:waypoint x="6270" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6206" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_083clzw" bpmnElement="Flow_04jyj1k">
        <di:waypoint x="6160" y="225" />
        <di:waypoint x="6160" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6173" y="243" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0cgnoa3" bpmnElement="Flow_1s9ykb6">
        <di:waypoint x="6370" y="200" />
        <di:waypoint x="6425" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ix4ojd_di" bpmnElement="Flow_1ix4ojd">
        <di:waypoint x="4505" y="200" />
        <di:waypoint x="4580" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4522" y="182" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0m7jomk_di" bpmnElement="Flow_0m7jomk">
        <di:waypoint x="5245" y="200" />
        <di:waypoint x="5300" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5265" y="182" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0k4eev0_di" bpmnElement="Flow_0k4eev0">
        <di:waypoint x="5615" y="200" />
        <di:waypoint x="5680" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t8ha9s_di" bpmnElement="Flow_1t8ha9s">
        <di:waypoint x="5220" y="175" />
        <di:waypoint x="5220" y="100" />
        <di:waypoint x="5590" y="100" />
        <di:waypoint x="5590" y="175" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5396" y="82" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1u2akys_di" bpmnElement="Flow_1u2akys">
        <di:waypoint x="6475" y="200" />
        <di:waypoint x="6600" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6517" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oo4ugl_di" bpmnElement="Flow_1oo4ugl">
        <di:waypoint x="6450" y="225" />
        <di:waypoint x="6450" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6463" y="256" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pun1ua_di" bpmnElement="Flow_0pun1ua">
        <di:waypoint x="6700" y="200" />
        <di:waypoint x="6785" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y0kwju_di" bpmnElement="Flow_0y0kwju">
        <di:waypoint x="3098" y="200" />
        <di:waypoint x="3190" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02tbb80_di" bpmnElement="Flow_02tbb80">
        <di:waypoint x="3680" y="200" />
        <di:waypoint x="3755" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00j83nk_di" bpmnElement="Flow_00j83nk">
        <di:waypoint x="2245" y="200" />
        <di:waypoint x="2480" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2267" y="173" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0ljkt7u_di" bpmnElement="SequenceFlow_0ljkt7u">
        <di:waypoint x="720" y="200" />
        <di:waypoint x="815" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02e3uev_di" bpmnElement="Flow_02e3uev">
        <di:waypoint x="840" y="225" />
        <di:waypoint x="840" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="843" y="254" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1whcpqm_di" bpmnElement="Flow_1whcpqm">
        <di:waypoint x="865" y="200" />
        <di:waypoint x="940" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="752" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0om79hl" bpmnElement="Flow_0tybm4u">
        <di:waypoint x="3290" y="200" />
        <di:waypoint x="3365" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1brnn3k" bpmnElement="Flow_0tfcins">
        <di:waypoint x="3390" y="225" />
        <di:waypoint x="3390" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3403" y="253" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01btt5p_di" bpmnElement="Flow_01btt5p">
        <di:waypoint x="3415" y="200" />
        <di:waypoint x="3580" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3502" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kkqns0_di" bpmnElement="Flow_1kkqns0">
        <di:waypoint x="2580" y="200" />
        <di:waypoint x="2860" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02hn1di_di" bpmnElement="Flow_02hn1di">
        <di:waypoint x="6835" y="200" />
        <di:waypoint x="6935" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ialxy2_di" bpmnElement="Flow_1ialxy2">
        <di:waypoint x="7905" y="200" />
        <di:waypoint x="8000" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7931" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zoow0v_di" bpmnElement="Flow_0zoow0v">
        <di:waypoint x="7750" y="200" />
        <di:waypoint x="7855" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1d481qo_di" bpmnElement="Flow_1d481qo">
        <di:waypoint x="8100" y="200" />
        <di:waypoint x="8175" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0s32ksw_di" bpmnElement="Flow_0s32ksw">
        <di:waypoint x="6810" y="225" />
        <di:waypoint x="6810" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6808" y="261" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17mvox6_di" bpmnElement="Flow_17mvox6">
        <di:waypoint x="7880" y="225" />
        <di:waypoint x="7880" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7879" y="255" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19uks7w_di" bpmnElement="Flow_19uks7w">
        <di:waypoint x="8225" y="200" />
        <di:waypoint x="8295" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8239" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kk98c9_di" bpmnElement="Flow_0kk98c9">
        <di:waypoint x="8345" y="200" />
        <di:waypoint x="8422" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09rk7i6_di" bpmnElement="Flow_09rk7i6">
        <di:waypoint x="7555" y="200" />
        <di:waypoint x="7650" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7565" y="182" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kh137b_di" bpmnElement="Flow_0kh137b">
        <di:waypoint x="7530" y="175" />
        <di:waypoint x="7530" y="80" />
        <di:waypoint x="8320" y="80" />
        <di:waypoint x="8320" y="175" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_138mgf4_di" bpmnElement="Flow_138mgf4">
        <di:waypoint x="8200" y="225" />
        <di:waypoint x="8200" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8198" y="256" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15yi6th_di" bpmnElement="Flow_15yi6th">
        <di:waypoint x="7445" y="200" />
        <di:waypoint x="7505" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fe5rcw_di" bpmnElement="Flow_1fe5rcw">
        <di:waypoint x="7315" y="200" />
        <di:waypoint x="7395" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10c11y5_di" bpmnElement="Flow_10c11y5">
        <di:waypoint x="6985" y="200" />
        <di:waypoint x="7070" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6923" y="236" width="74" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1izb795_di" bpmnElement="Flow_1izb795">
        <di:waypoint x="6960" y="175" />
        <di:waypoint x="6960" y="80" />
        <di:waypoint x="7420" y="80" />
        <di:waypoint x="7420" y="175" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ghdhpl_di" bpmnElement="Flow_0ghdhpl">
        <di:waypoint x="7170" y="200" />
        <di:waypoint x="7265" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wx9s5q_di" bpmnElement="Flow_1wx9s5q">
        <di:waypoint x="7290" y="225" />
        <di:waypoint x="7290" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7289" y="266" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
