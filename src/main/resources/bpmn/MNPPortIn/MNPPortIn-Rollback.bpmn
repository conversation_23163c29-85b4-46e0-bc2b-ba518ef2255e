<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0xt6om3" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0">
  <bpmn:process id="MNPPortIn-Rollback" name="MNPPortIn-Rollback" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_136n4zk</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1222wig">
      <bpmn:incoming>SequenceFlow_0vgw1x3</bpmn:incoming>
      <bpmn:incoming>Flow_1mr9yjb</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_19b6n6t</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0vgw1x3" sourceRef="BSDeleteAccount" targetRef="ExclusiveGateway_1222wig" />
    <bpmn:sequenceFlow id="SequenceFlow_19b6n6t" sourceRef="ExclusiveGateway_1222wig" targetRef="ExclusiveGateway_1xr9z89" />
    <bpmn:endEvent id="EndEvent_17dzzi8">
      <bpmn:incoming>Flow_0f0psjs</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0t1ijim" sourceRef="BSDeleteProfile" targetRef="ExclusiveGateway_1bpowob" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_1xr9z89" name="is profile deletion required" default="SequenceFlow_02u3rfj">
      <bpmn:incoming>SequenceFlow_19b6n6t</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0w74tvg</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_02u3rfj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0w74tvg" name="Yes" sourceRef="ExclusiveGateway_1xr9z89" targetRef="BSDeleteProfile">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${bs_create_profile}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1bpowob">
      <bpmn:incoming>SequenceFlow_0t1ijim</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_02u3rfj</bpmn:incoming>
      <bpmn:outgoing>Flow_1xmpaza</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_02u3rfj" name="No" sourceRef="ExclusiveGateway_1xr9z89" targetRef="ExclusiveGateway_1bpowob" />
    <bpmn:serviceTask id="BSDeleteAccount" name="BS Delete Account" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0e9683r</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0vgw1x3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSDeleteProfile" name="BS Delete Profile" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_0w74tvg</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0t1ijim</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_02jg7v7" sourceRef="BSDeleteService" targetRef="Gateway_0igtw6k" />
    <bpmn:exclusiveGateway id="Gateway_0igtw6k">
      <bpmn:incoming>Flow_02jg7v7</bpmn:incoming>
      <bpmn:incoming>Flow_0zfjwij</bpmn:incoming>
      <bpmn:outgoing>Flow_0jrh5yg</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="BSDeleteService" name="BS Delete Service" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0mzorba</bpmn:incoming>
      <bpmn:outgoing>Flow_02jg7v7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_06pig0r" name="is service deletion required" default="Flow_0zfjwij">
      <bpmn:incoming>Flow_0kbqh8d</bpmn:incoming>
      <bpmn:outgoing>Flow_0mzorba</bpmn:outgoing>
      <bpmn:outgoing>Flow_0zfjwij</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0mzorba" sourceRef="Gateway_06pig0r" targetRef="BSDeleteService">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${bs_service_creation}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0zfjwij" sourceRef="Gateway_06pig0r" targetRef="Gateway_0igtw6k" />
    <bpmn:exclusiveGateway id="Gateway_08rlbem" name="is account deletion required" default="Flow_1mr9yjb">
      <bpmn:incoming>Flow_1f796s6</bpmn:incoming>
      <bpmn:outgoing>Flow_0e9683r</bpmn:outgoing>
      <bpmn:outgoing>Flow_1mr9yjb</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0e9683r" sourceRef="Gateway_08rlbem" targetRef="BSDeleteAccount">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${bs_account_creation}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1mr9yjb" sourceRef="Gateway_08rlbem" targetRef="ExclusiveGateway_1222wig" />
    <bpmn:exclusiveGateway id="Gateway_0vibwuu" default="Flow_1un2i4g">
      <bpmn:incoming>Flow_136n4zk</bpmn:incoming>
      <bpmn:outgoing>Flow_07exj5c</bpmn:outgoing>
      <bpmn:outgoing>Flow_1un2i4g</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_136n4zk" sourceRef="StartEvent_1" targetRef="Gateway_0vibwuu" />
    <bpmn:serviceTask id="CancelSDPSubmitPortIn" name="Cancel Submit PortIn" camunda:asyncBefore="true" camunda:delegateExpression="${cancelPortIn}">
      <bpmn:incoming>Flow_07exj5c</bpmn:incoming>
      <bpmn:outgoing>Flow_0qbvdr2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_07exj5c" sourceRef="Gateway_0vibwuu" targetRef="CancelSDPSubmitPortIn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${cancelSubmitPortInCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0uyxtoa">
      <bpmn:incoming>Flow_0qbvdr2</bpmn:incoming>
      <bpmn:incoming>Flow_1un2i4g</bpmn:incoming>
      <bpmn:outgoing>Flow_0kbqh8d</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0qbvdr2" sourceRef="CancelSDPSubmitPortIn" targetRef="Gateway_0uyxtoa" />
    <bpmn:sequenceFlow id="Flow_0kbqh8d" sourceRef="Gateway_0uyxtoa" targetRef="Gateway_06pig0r" />
    <bpmn:sequenceFlow id="Flow_1un2i4g" sourceRef="Gateway_0vibwuu" targetRef="Gateway_0uyxtoa" />
    <bpmn:exclusiveGateway id="Gateway_0wt5kvp" default="Flow_0yn8vkq">
      <bpmn:incoming>Flow_1pckzeu</bpmn:incoming>
      <bpmn:outgoing>Flow_18m7dwu</bpmn:outgoing>
      <bpmn:outgoing>Flow_0yn8vkq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="NMSUnblockSim" name="NMS Unblock Sim" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_18m7dwu</bpmn:incoming>
      <bpmn:outgoing>Flow_0xqjs4k</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_18m7dwu" sourceRef="Gateway_0wt5kvp" targetRef="NMSUnblockSim">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${sim_delivery_callback}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0m5n5zy">
      <bpmn:incoming>Flow_0xqjs4k</bpmn:incoming>
      <bpmn:incoming>Flow_0yn8vkq</bpmn:incoming>
      <bpmn:outgoing>Flow_0f0psjs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0xqjs4k" sourceRef="NMSUnblockSim" targetRef="Gateway_0m5n5zy" />
    <bpmn:sequenceFlow id="Flow_0f0psjs" sourceRef="Gateway_0m5n5zy" targetRef="EndEvent_17dzzi8" />
    <bpmn:sequenceFlow id="Flow_0yn8vkq" sourceRef="Gateway_0wt5kvp" targetRef="Gateway_0m5n5zy" />
    <bpmn:sequenceFlow id="Flow_0jrh5yg" sourceRef="Gateway_0igtw6k" targetRef="BSFetchServiceMNPPortIn" />
    <bpmn:exclusiveGateway id="Gateway_1nz7ncw" default="Flow_0v1vbun">
      <bpmn:incoming>Flow_0aqaova</bpmn:incoming>
      <bpmn:outgoing>Flow_1f796s6</bpmn:outgoing>
      <bpmn:outgoing>Flow_0v1vbun</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1f796s6" sourceRef="Gateway_1nz7ncw" targetRef="Gateway_08rlbem">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${accountandProfileDeletionReg}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1cxr9zt">
      <bpmn:incoming>Flow_1xmpaza</bpmn:incoming>
      <bpmn:incoming>Flow_0v1vbun</bpmn:incoming>
      <bpmn:outgoing>Flow_1pckzeu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1xmpaza" sourceRef="ExclusiveGateway_1bpowob" targetRef="Gateway_1cxr9zt" />
    <bpmn:sequenceFlow id="Flow_1pckzeu" sourceRef="Gateway_1cxr9zt" targetRef="Gateway_0wt5kvp" />
    <bpmn:sequenceFlow id="Flow_0v1vbun" sourceRef="Gateway_1nz7ncw" targetRef="Gateway_1cxr9zt" />
    <bpmn:exclusiveGateway id="Gateway_07bygho" default="Flow_0aqaova">
      <bpmn:incoming>Flow_0d4rydz</bpmn:incoming>
      <bpmn:outgoing>Flow_0aqaova</bpmn:outgoing>
      <bpmn:outgoing>Flow_0au68kz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0d4rydz" sourceRef="BSFetchServiceMNPPortIn" targetRef="Gateway_07bygho" />
    <bpmn:sequenceFlow id="Flow_0aqaova" sourceRef="Gateway_07bygho" targetRef="Gateway_1nz7ncw" />
    <bpmn:endEvent id="Event_05orxd1">
      <bpmn:incoming>Flow_0au68kz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0au68kz" name="Failure" sourceRef="Gateway_07bygho" targetRef="Event_05orxd1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSFetchServiceMNPPortIn" name="BSFetchServiceMNPPortIn" camunda:asyncBefore="true" camunda:delegateExpression="${bsFetchService}">
      <bpmn:incoming>Flow_0jrh5yg</bpmn:incoming>
      <bpmn:outgoing>Flow_0d4rydz</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="MNPPortIn-Rollback">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="192" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_17dzzi8_di" bpmnElement="EndEvent_17dzzi8">
        <dc:Bounds x="2822" y="192" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_06pig0r_di" bpmnElement="Gateway_06pig0r" isMarkerVisible="true">
        <dc:Bounds x="605" y="185" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="586" y="242" width="88" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vibwuu_di" bpmnElement="Gateway_0vibwuu" isMarkerVisible="true">
        <dc:Bounds x="255" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1gw5qqe_di" bpmnElement="CancelSDPSubmitPortIn">
        <dc:Bounds x="350" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0uyxtoa_di" bpmnElement="Gateway_0uyxtoa" isMarkerVisible="true">
        <dc:Bounds x="495" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0wt5kvp_di" bpmnElement="Gateway_0wt5kvp" isMarkerVisible="true">
        <dc:Bounds x="2315" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1kyopeg" bpmnElement="NMSUnblockSim">
        <dc:Bounds x="2470" y="170" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0m5n5zy_di" bpmnElement="Gateway_0m5n5zy" isMarkerVisible="true">
        <dc:Bounds x="2655" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1uinbhl_di" bpmnElement="BSDeleteService">
        <dc:Bounds x="720" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0igtw6k_di" bpmnElement="Gateway_0igtw6k" isMarkerVisible="true">
        <dc:Bounds x="885" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1222wig_di" bpmnElement="ExclusiveGateway_1222wig" isMarkerVisible="true">
        <dc:Bounds x="1635" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1wbapye_di" bpmnElement="BSDeleteAccount">
        <dc:Bounds x="1470" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1xr9z89_di" bpmnElement="ExclusiveGateway_1xr9z89" isMarkerVisible="true">
        <dc:Bounds x="1745" y="185" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1729" y="242" width="83" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0y6y5za_di" bpmnElement="BSDeleteProfile">
        <dc:Bounds x="1860" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1bpowob_di" bpmnElement="ExclusiveGateway_1bpowob" isMarkerVisible="true">
        <dc:Bounds x="2025" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1cxr9zt_di" bpmnElement="Gateway_1cxr9zt" isMarkerVisible="true">
        <dc:Bounds x="2165" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1nz7ncw_di" bpmnElement="Gateway_1nz7ncw" isMarkerVisible="true">
        <dc:Bounds x="1255" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09fa1hj_di" bpmnElement="BSFetchServiceMNPPortIn">
        <dc:Bounds x="1000" y="170" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_08rlbem_di" bpmnElement="Gateway_08rlbem" isMarkerVisible="true">
        <dc:Bounds x="1365" y="185" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1349" y="242" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_07bygho_di" bpmnElement="Gateway_07bygho" isMarkerVisible="true">
        <dc:Bounds x="1145" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_05orxd1_di" bpmnElement="Event_05orxd1">
        <dc:Bounds x="1152" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0vgw1x3_di" bpmnElement="SequenceFlow_0vgw1x3">
        <di:waypoint x="1570" y="210" />
        <di:waypoint x="1635" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_19b6n6t_di" bpmnElement="SequenceFlow_19b6n6t">
        <di:waypoint x="1685" y="210" />
        <di:waypoint x="1745" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0t1ijim_di" bpmnElement="SequenceFlow_0t1ijim">
        <di:waypoint x="1960" y="210" />
        <di:waypoint x="2025" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0w74tvg_di" bpmnElement="SequenceFlow_0w74tvg">
        <di:waypoint x="1795" y="210" />
        <di:waypoint x="1860" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1819" y="192" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_02u3rfj_di" bpmnElement="SequenceFlow_02u3rfj">
        <di:waypoint x="1770" y="185" />
        <di:waypoint x="1770" y="130" />
        <di:waypoint x="2050" y="130" />
        <di:waypoint x="2050" y="185" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1903" y="112" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02jg7v7_di" bpmnElement="Flow_02jg7v7">
        <di:waypoint x="820" y="210" />
        <di:waypoint x="885" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mzorba_di" bpmnElement="Flow_0mzorba">
        <di:waypoint x="655" y="210" />
        <di:waypoint x="720" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zfjwij_di" bpmnElement="Flow_0zfjwij">
        <di:waypoint x="630" y="185" />
        <di:waypoint x="630" y="120" />
        <di:waypoint x="910" y="120" />
        <di:waypoint x="910" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e9683r_di" bpmnElement="Flow_0e9683r">
        <di:waypoint x="1415" y="210" />
        <di:waypoint x="1470" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mr9yjb_di" bpmnElement="Flow_1mr9yjb">
        <di:waypoint x="1390" y="185" />
        <di:waypoint x="1390" y="120" />
        <di:waypoint x="1660" y="120" />
        <di:waypoint x="1660" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_136n4zk_di" bpmnElement="Flow_136n4zk">
        <di:waypoint x="188" y="210" />
        <di:waypoint x="255" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07exj5c_di" bpmnElement="Flow_07exj5c">
        <di:waypoint x="305" y="210" />
        <di:waypoint x="350" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qbvdr2_di" bpmnElement="Flow_0qbvdr2">
        <di:waypoint x="450" y="210" />
        <di:waypoint x="495" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kbqh8d_di" bpmnElement="Flow_0kbqh8d">
        <di:waypoint x="545" y="210" />
        <di:waypoint x="605" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1un2i4g_di" bpmnElement="Flow_1un2i4g">
        <di:waypoint x="280" y="185" />
        <di:waypoint x="280" y="100" />
        <di:waypoint x="520" y="100" />
        <di:waypoint x="520" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18m7dwu_di" bpmnElement="Flow_18m7dwu">
        <di:waypoint x="2365" y="210" />
        <di:waypoint x="2470" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xqjs4k_di" bpmnElement="Flow_0xqjs4k">
        <di:waypoint x="2570" y="210" />
        <di:waypoint x="2655" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f0psjs_di" bpmnElement="Flow_0f0psjs">
        <di:waypoint x="2705" y="210" />
        <di:waypoint x="2822" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yn8vkq_di" bpmnElement="Flow_0yn8vkq">
        <di:waypoint x="2340" y="185" />
        <di:waypoint x="2340" y="120" />
        <di:waypoint x="2680" y="120" />
        <di:waypoint x="2680" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jrh5yg_di" bpmnElement="Flow_0jrh5yg">
        <di:waypoint x="935" y="210" />
        <di:waypoint x="1000" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1f796s6_di" bpmnElement="Flow_1f796s6">
        <di:waypoint x="1305" y="210" />
        <di:waypoint x="1365" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xmpaza_di" bpmnElement="Flow_1xmpaza">
        <di:waypoint x="2075" y="210" />
        <di:waypoint x="2165" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pckzeu_di" bpmnElement="Flow_1pckzeu">
        <di:waypoint x="2215" y="210" />
        <di:waypoint x="2315" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v1vbun_di" bpmnElement="Flow_0v1vbun">
        <di:waypoint x="1280" y="185" />
        <di:waypoint x="1280" y="40" />
        <di:waypoint x="2190" y="40" />
        <di:waypoint x="2190" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d4rydz_di" bpmnElement="Flow_0d4rydz">
        <di:waypoint x="1100" y="210" />
        <di:waypoint x="1145" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0aqaova_di" bpmnElement="Flow_0aqaova">
        <di:waypoint x="1195" y="210" />
        <di:waypoint x="1255" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0au68kz_di" bpmnElement="Flow_0au68kz">
        <di:waypoint x="1170" y="235" />
        <di:waypoint x="1170" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1167" y="282" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
