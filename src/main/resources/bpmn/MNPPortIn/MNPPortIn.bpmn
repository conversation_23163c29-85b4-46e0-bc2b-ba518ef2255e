<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0">
  <bpmn:process id="MNPPortIn" name="MNPPortIn" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_0kcxe03</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0kcxe03" sourceRef="orderExecStart" targetRef="OrderEnrichment" />
    <bpmn:exclusiveGateway id="Gateway_0dxkbwm" default="Flow_1awrbjt">
      <bpmn:incoming>Flow_0y9sn5m</bpmn:incoming>
      <bpmn:outgoing>Flow_1awrbjt</bpmn:outgoing>
      <bpmn:outgoing>Flow_1qjz8qd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0y9sn5m" sourceRef="BSCreateProfile" targetRef="Gateway_0dxkbwm" />
    <bpmn:sequenceFlow id="Flow_1awrbjt" name="Success" sourceRef="Gateway_0dxkbwm" targetRef="Gateway_0txto23" />
    <bpmn:endEvent id="Event_0g7f75e">
      <bpmn:incoming>Flow_1qjz8qd</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1qjz8qd" name="Failure" sourceRef="Gateway_0dxkbwm" targetRef="Event_0g7f75e">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1769hon" default="Flow_0p62f5e">
      <bpmn:incoming>Flow_191nzu7</bpmn:incoming>
      <bpmn:outgoing>Flow_017f0t1</bpmn:outgoing>
      <bpmn:outgoing>Flow_0p62f5e</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_191nzu7" sourceRef="BSCreateAccount" targetRef="Gateway_1769hon" />
    <bpmn:endEvent id="Event_0q3kcn0">
      <bpmn:incoming>Flow_017f0t1</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_017f0t1" name="Failure" sourceRef="Gateway_1769hon" targetRef="Event_0q3kcn0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="PortInTypeFinder" name="PortIn Type Finder" camunda:asyncBefore="true" camunda:delegateExpression="${portInTypeFinder}">
      <bpmn:documentation>Find Port in Type (call billing to fetch the service id details)</bpmn:documentation>
      <bpmn:incoming>Flow_0ovu2ln</bpmn:incoming>
      <bpmn:outgoing>Flow_0nqxv3x</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1k3dkyo" default="Flow_124ny4t">
      <bpmn:incoming>Flow_1prrytd</bpmn:incoming>
      <bpmn:outgoing>Flow_124ny4t</bpmn:outgoing>
      <bpmn:outgoing>Flow_1hmnjzb</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_124ny4t" name="Success" sourceRef="Gateway_1k3dkyo" targetRef="Gateway_1seqkgf" />
    <bpmn:endEvent id="Event_0cxn4t8">
      <bpmn:incoming>Flow_1hmnjzb</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1hmnjzb" name="Failure" sourceRef="Gateway_1k3dkyo" targetRef="Event_0cxn4t8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1p3ub4k" name="If upfront payment applicable" default="Flow_03u6f66">
      <bpmn:incoming>Flow_0xd8vn1</bpmn:incoming>
      <bpmn:outgoing>Flow_00yqrsi</bpmn:outgoing>
      <bpmn:outgoing>Flow_03u6f66</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0p62f5e" name="Success" sourceRef="Gateway_1769hon" targetRef="Gateway_1tzpm9n" />
    <bpmn:sequenceFlow id="Flow_00yqrsi" name="Yes" sourceRef="Gateway_1p3ub4k" targetRef="PaymentWorkflow">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${paymentCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="PaymentWorkflow" name="Payment workflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_00yqrsi</bpmn:incoming>
      <bpmn:outgoing>Flow_0cunzyv</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_1dsta8e">
      <bpmn:incoming>Flow_03u6f66</bpmn:incoming>
      <bpmn:incoming>Flow_0cunzyv</bpmn:incoming>
      <bpmn:outgoing>Flow_1fd0qw1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_03u6f66" name="no" sourceRef="Gateway_1p3ub4k" targetRef="Gateway_1dsta8e" />
    <bpmn:sequenceFlow id="Flow_0cunzyv" sourceRef="PaymentWorkflow" targetRef="Gateway_1dsta8e" />
    <bpmn:exclusiveGateway id="Gateway_1p5ayxd" name="check if sim delivery applicable" default="Flow_1eqmwrx">
      <bpmn:incoming>Flow_10po6r8</bpmn:incoming>
      <bpmn:outgoing>Flow_1eqmwrx</bpmn:outgoing>
      <bpmn:outgoing>Flow_0to0bgk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1fd0qw1" sourceRef="Gateway_1dsta8e" targetRef="Gateway_0cfgzjg" />
    <bpmn:exclusiveGateway id="Gateway_1kq96lc">
      <bpmn:incoming>Flow_1eqmwrx</bpmn:incoming>
      <bpmn:incoming>Flow_00jx8dd</bpmn:incoming>
      <bpmn:outgoing>Flow_1r1vep6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1eqmwrx" name="No" sourceRef="Gateway_1p5ayxd" targetRef="Gateway_1kq96lc" />
    <bpmn:exclusiveGateway id="Gateway_1ii6t8z" name="Check the port-in type">
      <bpmn:incoming>Flow_0qnhsxs</bpmn:incoming>
      <bpmn:outgoing>Flow_0awpx7k</bpmn:outgoing>
      <bpmn:outgoing>Flow_1l2hlpg</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1r1vep6" sourceRef="Gateway_1kq96lc" targetRef="Gateway_1row4bw" />
    <bpmn:sequenceFlow id="Flow_0awpx7k" name="Port in from external telco / port in from Singtel" sourceRef="Gateway_1ii6t8z" targetRef="PortInFromExtTelcoOrSingtel">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portInType=='PortInFromExternalTelco'||portInType=='PortInFromSingtel'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1l2hlpg" name="Port in from MVNO to MVNO" sourceRef="Gateway_1ii6t8z" targetRef="PortInFromMvnoToMvno">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portInType=='PortInFromMvnoToMvno'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="PortInFromExtTelcoOrSingtel" name="Port in from Ext Telco/Singtel" camunda:asyncBefore="true" calledElement="PortInFromExtTelcoOrSingtel" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0awpx7k</bpmn:incoming>
      <bpmn:outgoing>Flow_07vuljq</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:callActivity id="PortInFromMvnoToMvno" name="Port in from MVNO to MVNO" camunda:asyncBefore="true" calledElement="PortInFromMvnoToMvno" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1l2hlpg</bpmn:incoming>
      <bpmn:outgoing>Flow_1c0f03x</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:endEvent id="orderExecEnd" camunda:asyncBefore="true">
      <bpmn:incoming>Flow_08xftxu</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1c0f03x" sourceRef="PortInFromMvnoToMvno" targetRef="Gateway_1tuf0dy" />
    <bpmn:exclusiveGateway id="Gateway_0fuzl5a" default="Flow_038iwbr">
      <bpmn:incoming>Flow_0vvv1ml</bpmn:incoming>
      <bpmn:outgoing>Flow_1pgh3s3</bpmn:outgoing>
      <bpmn:outgoing>Flow_038iwbr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1fi99c8">
      <bpmn:incoming>Flow_1pgh3s3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="OrderEnrichment" name="Order Enrichment Delegate" camunda:asyncBefore="true" camunda:delegateExpression="${orderEnrichmentDelegate}">
      <bpmn:incoming>Flow_0kcxe03</bpmn:incoming>
      <bpmn:outgoing>Flow_0vvv1ml</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0vvv1ml" sourceRef="OrderEnrichment" targetRef="Gateway_0fuzl5a" />
    <bpmn:sequenceFlow id="Flow_1pgh3s3" name="Failure" sourceRef="Gateway_0fuzl5a" targetRef="Event_1fi99c8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1tuf0dy">
      <bpmn:incoming>Flow_1c0f03x</bpmn:incoming>
      <bpmn:incoming>Flow_07vuljq</bpmn:incoming>
      <bpmn:outgoing>Flow_08xftxu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_08xftxu" sourceRef="Gateway_1tuf0dy" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_07vuljq" sourceRef="PortInFromExtTelcoOrSingtel" targetRef="Gateway_1tuf0dy" />
    <bpmn:exclusiveGateway id="Gateway_1seqkgf" name="isExistingProfile" default="Flow_14m0f1e">
      <bpmn:incoming>Flow_124ny4t</bpmn:incoming>
      <bpmn:outgoing>Flow_14m0f1e</bpmn:outgoing>
      <bpmn:outgoing>Flow_0m8r0uy</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_14m0f1e" name="No" sourceRef="Gateway_1seqkgf" targetRef="BSCreateProfile" />
    <bpmn:exclusiveGateway id="Gateway_0txto23">
      <bpmn:incoming>Flow_1awrbjt</bpmn:incoming>
      <bpmn:incoming>Flow_0m8r0uy</bpmn:incoming>
      <bpmn:outgoing>Flow_1rk6k8w</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1rk6k8w" sourceRef="Gateway_0txto23" targetRef="Gateway_0vkmuz1" />
    <bpmn:sequenceFlow id="Flow_0m8r0uy" name="Yes" sourceRef="Gateway_1seqkgf" targetRef="Gateway_0txto23">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isExistingProfile}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0vkmuz1" name="isExistingAccount" default="Flow_0vk7hpu">
      <bpmn:incoming>Flow_1rk6k8w</bpmn:incoming>
      <bpmn:outgoing>Flow_0vk7hpu</bpmn:outgoing>
      <bpmn:outgoing>Flow_11a2ays</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0vk7hpu" name="No" sourceRef="Gateway_0vkmuz1" targetRef="BSCreateAccount" />
    <bpmn:exclusiveGateway id="Gateway_1tzpm9n">
      <bpmn:incoming>Flow_0p62f5e</bpmn:incoming>
      <bpmn:incoming>Flow_11a2ays</bpmn:incoming>
      <bpmn:outgoing>Flow_0xd8vn1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0xd8vn1" sourceRef="Gateway_1tzpm9n" targetRef="Gateway_1p3ub4k" />
    <bpmn:sequenceFlow id="Flow_11a2ays" name="Yes" sourceRef="Gateway_0vkmuz1" targetRef="Gateway_1tzpm9n">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isExistingAccount}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSCreateProfile" name="Create Profile In Party Management" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_14m0f1e</bpmn:incoming>
      <bpmn:outgoing>Flow_0y9sn5m</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSCreateAccount" name="Create Billing Account" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0vk7hpu</bpmn:incoming>
      <bpmn:outgoing>Flow_191nzu7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:receiveTask id="SimDeliveryCallback" name="SimDelivery Callback" camunda:asyncBefore="true" messageRef="Message_1y1dj88">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0jq96qp</bpmn:incoming>
      <bpmn:outgoing>Flow_0nmr9xg</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:serviceTask id="QueryMNPDetails" name="QueryMNPDetails" camunda:asyncBefore="true" camunda:delegateExpression="${queryMnpDetails}">
      <bpmn:documentation>Query Singtel MNP System to verify operator details</bpmn:documentation>
      <bpmn:incoming>Flow_11etlja</bpmn:incoming>
      <bpmn:outgoing>Flow_1o8ufww</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1izqv5e" default="Flow_1trwe5t">
      <bpmn:incoming>Flow_0nqxv3x</bpmn:incoming>
      <bpmn:outgoing>Flow_1xnl3mx</bpmn:outgoing>
      <bpmn:outgoing>Flow_1trwe5t</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0jigfk3">
      <bpmn:incoming>Flow_1xnl3mx</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1xnl3mx" name="Failure" sourceRef="Gateway_1izqv5e" targetRef="Event_0jigfk3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1o8ufww" sourceRef="QueryMNPDetails" targetRef="Gateway_172336i" />
    <bpmn:sequenceFlow id="Flow_038iwbr" name="Success" sourceRef="Gateway_0fuzl5a" targetRef="Gateway_1h1pk5h" />
    <bpmn:sequenceFlow id="Flow_0nqxv3x" sourceRef="PortInTypeFinder" targetRef="Gateway_1izqv5e" />
    <bpmn:exclusiveGateway id="Gateway_1cjm82h" name="check if query mnp required (check if mvno mvno port in)">
      <bpmn:incoming>Flow_1trwe5t</bpmn:incoming>
      <bpmn:outgoing>Flow_11etlja</bpmn:outgoing>
      <bpmn:outgoing>Flow_12pubzs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1trwe5t" name="Success" sourceRef="Gateway_1izqv5e" targetRef="Gateway_1cjm82h" />
    <bpmn:sequenceFlow id="Flow_11etlja" name="Yes" sourceRef="Gateway_1cjm82h" targetRef="QueryMNPDetails">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${queryMnpRequired}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0w1elll">
      <bpmn:incoming>Flow_043h64e</bpmn:incoming>
      <bpmn:incoming>Flow_1ginpkc</bpmn:incoming>
      <bpmn:outgoing>Flow_1prrytd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1prrytd" sourceRef="Gateway_0w1elll" targetRef="Gateway_1k3dkyo" />
    <bpmn:exclusiveGateway id="Gateway_172336i" default="Flow_043h64e">
      <bpmn:incoming>Flow_1o8ufww</bpmn:incoming>
      <bpmn:outgoing>Flow_043h64e</bpmn:outgoing>
      <bpmn:outgoing>Flow_1fgoiti</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_043h64e" name="Success" sourceRef="Gateway_172336i" targetRef="Gateway_0w1elll" />
    <bpmn:endEvent id="Event_0d1frv1">
      <bpmn:incoming>Flow_1fgoiti</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1fgoiti" name="Failure" sourceRef="Gateway_172336i" targetRef="Event_0d1frv1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0cut48i" default="Flow_043shfl">
      <bpmn:incoming>Flow_12pubzs</bpmn:incoming>
      <bpmn:outgoing>Flow_13el5q7</bpmn:outgoing>
      <bpmn:outgoing>Flow_043shfl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_12pubzs" sourceRef="Gateway_1cjm82h" targetRef="Gateway_0cut48i">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${!queryMnpRequired}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="IdentificationIdValidator" name="Identification Id Validator" camunda:asyncBefore="true" camunda:delegateExpression="${identificationIdValidator}">
      <bpmn:documentation>Query Singtel MNP System to verify operator details</bpmn:documentation>
      <bpmn:incoming>Flow_13el5q7</bpmn:incoming>
      <bpmn:outgoing>Flow_05vjfjy</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_13el5q7" sourceRef="Gateway_0cut48i" targetRef="IdentificationIdValidator">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isIdValidationReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0csm1dd" default="Flow_1ol9fyl">
      <bpmn:incoming>Flow_05vjfjy</bpmn:incoming>
      <bpmn:outgoing>Flow_1dl8ubs</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ol9fyl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_05vjfjy" sourceRef="IdentificationIdValidator" targetRef="Gateway_0csm1dd" />
    <bpmn:endEvent id="Event_162rku6">
      <bpmn:incoming>Flow_1dl8ubs</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1dl8ubs" sourceRef="Gateway_0csm1dd" targetRef="Event_162rku6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_18mi2jk">
      <bpmn:incoming>Flow_1ol9fyl</bpmn:incoming>
      <bpmn:incoming>Flow_043shfl</bpmn:incoming>
      <bpmn:outgoing>Flow_1ginpkc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ol9fyl" sourceRef="Gateway_0csm1dd" targetRef="Gateway_18mi2jk" />
    <bpmn:sequenceFlow id="Flow_1ginpkc" sourceRef="Gateway_18mi2jk" targetRef="Gateway_0w1elll" />
    <bpmn:sequenceFlow id="Flow_043shfl" sourceRef="Gateway_0cut48i" targetRef="Gateway_18mi2jk" />
    <bpmn:exclusiveGateway id="Gateway_1ouv3ww" camunda:asyncBefore="true" default="Flow_0rmpw83">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.CallBackExecutionListener" event="start" />
        <camunda:properties>
          <camunda:property name="callBackType" value="SIM" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0to0bgk</bpmn:incoming>
      <bpmn:outgoing>Flow_0jq96qp</bpmn:outgoing>
      <bpmn:outgoing>Flow_0rmpw83</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0to0bgk" sourceRef="Gateway_1p5ayxd" targetRef="Gateway_1ouv3ww">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${simDeliveryCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1yf7ptk">
      <bpmn:incoming>Flow_0nmr9xg</bpmn:incoming>
      <bpmn:incoming>Flow_0rmpw83</bpmn:incoming>
      <bpmn:outgoing>Flow_00jx8dd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0nmr9xg" sourceRef="SimDeliveryCallback" targetRef="Gateway_1yf7ptk" />
    <bpmn:sequenceFlow id="Flow_00jx8dd" sourceRef="Gateway_1yf7ptk" targetRef="Gateway_1kq96lc" />
    <bpmn:sequenceFlow id="Flow_0jq96qp" sourceRef="Gateway_1ouv3ww" targetRef="SimDeliveryCallback">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callBackProcessReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0rmpw83" sourceRef="Gateway_1ouv3ww" targetRef="Gateway_1yf7ptk" />
    <bpmn:exclusiveGateway id="Gateway_1row4bw">
      <bpmn:incoming>Flow_1r1vep6</bpmn:incoming>
      <bpmn:incoming>Flow_00pu76a</bpmn:incoming>
      <bpmn:outgoing>Flow_0d7qlig</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0d7qlig" sourceRef="Gateway_1row4bw" targetRef="Gateway_122yee4" />
    <bpmn:exclusiveGateway id="Gateway_0cfgzjg" name="isEsimCall=true" default="Flow_10po6r8">
      <bpmn:incoming>Flow_1fd0qw1</bpmn:incoming>
      <bpmn:outgoing>Flow_10po6r8</bpmn:outgoing>
      <bpmn:outgoing>Flow_00pu76a</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_10po6r8" sourceRef="Gateway_0cfgzjg" targetRef="Gateway_1p5ayxd" />
    <bpmn:sequenceFlow id="Flow_00pu76a" name="IsEsim= true" sourceRef="Gateway_0cfgzjg" targetRef="Gateway_1row4bw">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isEsimCall}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0rrrvav" default="Flow_1hzq9nu">
      <bpmn:incoming>Flow_0ebeuaz</bpmn:incoming>
      <bpmn:outgoing>Flow_1hzq9nu</bpmn:outgoing>
      <bpmn:outgoing>Flow_0mlbmzk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1hzq9nu" sourceRef="Gateway_0rrrvav" targetRef="Gateway_0unddxx" />
    <bpmn:exclusiveGateway id="Gateway_11ndai3" default="Flow_1o7vxiu">
      <bpmn:incoming>Flow_13ax0hy</bpmn:incoming>
      <bpmn:outgoing>Flow_1o7vxiu</bpmn:outgoing>
      <bpmn:outgoing>Flow_1qyd15o</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1o7vxiu" sourceRef="Gateway_11ndai3" targetRef="ESBEsimBlockCallback" />
    <bpmn:serviceTask id="Esb_blockEsim" name="Esb_blockEsim" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_09i5hod</bpmn:incoming>
      <bpmn:outgoing>Flow_13ax0hy</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_13ax0hy" sourceRef="Esb_blockEsim" targetRef="Gateway_11ndai3" />
    <bpmn:receiveTask id="ESBEsimBlockCallback" name="Esb_blockEsimCallback" camunda:asyncBefore="true" messageRef="Message_2hdqiii">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">Esb_blockEsim</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="Esb_blockEsim" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1o7vxiu</bpmn:incoming>
      <bpmn:outgoing>Flow_0ebeuaz</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_0ebeuaz" sourceRef="ESBEsimBlockCallback" targetRef="Gateway_0rrrvav" />
    <bpmn:exclusiveGateway id="Gateway_0unddxx">
      <bpmn:incoming>Flow_1hzq9nu</bpmn:incoming>
      <bpmn:incoming>Flow_0rmqi2m</bpmn:incoming>
      <bpmn:outgoing>Flow_0qnhsxs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0qnhsxs" sourceRef="Gateway_0unddxx" targetRef="Gateway_1ii6t8z" />
    <bpmn:endEvent id="Event_01uzsky">
      <bpmn:incoming>Flow_1qyd15o</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_0uhjvip">
      <bpmn:incoming>Flow_0mlbmzk</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1qyd15o" name="Failure" sourceRef="Gateway_11ndai3" targetRef="Event_01uzsky">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0mlbmzk" name="Failure" sourceRef="Gateway_0rrrvav" targetRef="Event_0uhjvip">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_122yee4" default="Flow_0rmqi2m">
      <bpmn:incoming>Flow_0d7qlig</bpmn:incoming>
      <bpmn:outgoing>Flow_09i5hod</bpmn:outgoing>
      <bpmn:outgoing>Flow_0rmqi2m</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_09i5hod" name="isEsimCall=true" sourceRef="Gateway_122yee4" targetRef="Esb_blockEsim">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isEsimCall}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0rmqi2m" sourceRef="Gateway_122yee4" targetRef="Gateway_0unddxx" />
    <bpmn:serviceTask id="Arm_FetchAssetDetails" name="Arm Fetch Asset Details" camunda:asyncBefore="true" camunda:delegateExpression="${fetchDetailsFromArm}">
      <bpmn:incoming>Flow_1jgn8gm</bpmn:incoming>
      <bpmn:outgoing>Flow_0kd8dyw</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0kd8dyw" sourceRef="Arm_FetchAssetDetails" targetRef="Gateway_16ba59x" />
    <bpmn:exclusiveGateway id="Gateway_1h1pk5h" default="Flow_1hz2pxk">
      <bpmn:incoming>Flow_038iwbr</bpmn:incoming>
      <bpmn:outgoing>Flow_1jgn8gm</bpmn:outgoing>
      <bpmn:outgoing>Flow_1hz2pxk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1jgn8gm" name="isIccidValidationReq=true" sourceRef="Gateway_1h1pk5h" targetRef="Arm_FetchAssetDetails">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isIccidValidationReq}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0ccrygs">
      <bpmn:incoming>Flow_089o5u4</bpmn:incoming>
      <bpmn:incoming>Flow_1hz2pxk</bpmn:incoming>
      <bpmn:outgoing>Flow_0ovu2ln</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ovu2ln" sourceRef="Gateway_0ccrygs" targetRef="PortInTypeFinder" />
    <bpmn:exclusiveGateway id="Gateway_16ba59x" default="Flow_089o5u4">
      <bpmn:incoming>Flow_0kd8dyw</bpmn:incoming>
      <bpmn:outgoing>Flow_089o5u4</bpmn:outgoing>
      <bpmn:outgoing>Flow_0sxplwc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_089o5u4" sourceRef="Gateway_16ba59x" targetRef="Gateway_0ccrygs" />
    <bpmn:sequenceFlow id="Flow_1hz2pxk" sourceRef="Gateway_1h1pk5h" targetRef="Gateway_0ccrygs" />
    <bpmn:endEvent id="Event_0xns9cg">
      <bpmn:incoming>Flow_0sxplwc</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0sxplwc" name="Failure" sourceRef="Gateway_16ba59x" targetRef="Event_0xns9cg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_18lupa4" name="MNPCallBack" />
  <bpmn:message id="Message_0cxkag2" />
  <bpmn:message id="Message_0uyo7lb" />
  <bpmn:message id="Message_0z0mmot" name="MNPCallBack" />
  <bpmn:message id="Message_0o55ogt" name="SimDeliveryCallback" />
  <bpmn:message id="Message_1y1dj88" name="ESBEsimBlockCallback" />
  <bpmn:message id="Message_2hdqiii" name="ESBEsimBlockCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="MNPPortIn">
      <bpmndi:BPMNShape id="Event_1tkja61_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0dxkbwm_di" bpmnElement="Gateway_0dxkbwm" isMarkerVisible="true">
        <dc:Bounds x="2475" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0g7f75e_di" bpmnElement="Event_0g7f75e">
        <dc:Bounds x="2482" y="472" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1769hon_di" bpmnElement="Gateway_1769hon" isMarkerVisible="true">
        <dc:Bounds x="3035" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0q3kcn0_di" bpmnElement="Event_0q3kcn0">
        <dc:Bounds x="3042" y="472" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0sgogx3_di" bpmnElement="PortInTypeFinder">
        <dc:Bounds x="1090" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1k3dkyo_di" bpmnElement="Gateway_1k3dkyo" isMarkerVisible="true">
        <dc:Bounds x="2035" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0cxn4t8_di" bpmnElement="Event_0cxn4t8">
        <dc:Bounds x="2042" y="462" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1p3ub4k_di" bpmnElement="Gateway_1p3ub4k" isMarkerVisible="true">
        <dc:Bounds x="3305" y="345" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3286" y="402" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0o6zmcp_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="3520" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1dsta8e_di" bpmnElement="Gateway_1dsta8e" isMarkerVisible="true">
        <dc:Bounds x="3765" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1p5ayxd_di" bpmnElement="Gateway_1p5ayxd" isMarkerVisible="true">
        <dc:Bounds x="4075" y="345" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4071" y="402" width="58" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1kq96lc_di" bpmnElement="Gateway_1kq96lc" isMarkerVisible="true">
        <dc:Bounds x="4745" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ii6t8z_di" bpmnElement="Gateway_1ii6t8z" isMarkerVisible="true">
        <dc:Bounds x="5935" y="345" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5995" y="356" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1pzk86x_di" bpmnElement="PortInFromExtTelcoOrSingtel">
        <dc:Bounds x="6130" y="190" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1gp8prf_di" bpmnElement="PortInFromMvnoToMvno">
        <dc:Bounds x="6140" y="470" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0t2c59y_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="6552" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fuzl5a_di" bpmnElement="Gateway_0fuzl5a" isMarkerVisible="true">
        <dc:Bounds x="485" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1fi99c8_di" bpmnElement="Event_1fi99c8">
        <dc:Bounds x="492" y="472" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01wyi3h_di" bpmnElement="OrderEnrichment">
        <dc:Bounds x="300" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tuf0dy_di" bpmnElement="Gateway_1tuf0dy" isMarkerVisible="true">
        <dc:Bounds x="6405" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1seqkgf_di" bpmnElement="Gateway_1seqkgf" isMarkerVisible="true">
        <dc:Bounds x="2185" y="345" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2172" y="402" width="79" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0txto23_di" bpmnElement="Gateway_0txto23" isMarkerVisible="true">
        <dc:Bounds x="2625" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vkmuz1_di" bpmnElement="Gateway_0vkmuz1" isMarkerVisible="true">
        <dc:Bounds x="2735" y="345" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2718" y="402" width="87" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tzpm9n_di" bpmnElement="Gateway_1tzpm9n" isMarkerVisible="true">
        <dc:Bounds x="3145" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0borkz1_di" bpmnElement="BSCreateProfile">
        <dc:Bounds x="2310" y="330" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_045dqjt_di" bpmnElement="BSCreateAccount">
        <dc:Bounds x="2840" y="330" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_03l5qgl_di" bpmnElement="SimDeliveryCallback">
        <dc:Bounds x="4400" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_18b1s7l" bpmnElement="QueryMNPDetails">
        <dc:Bounds x="1600" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1izqv5e_di" bpmnElement="Gateway_1izqv5e" isMarkerVisible="true">
        <dc:Bounds x="1265" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0jigfk3_di" bpmnElement="Event_0jigfk3">
        <dc:Bounds x="1272" y="472" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1cjm82h_di" bpmnElement="Gateway_1cjm82h" isMarkerVisible="true">
        <dc:Bounds x="1425" y="345" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1414" y="283" width="72" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0w1elll_di" bpmnElement="Gateway_0w1elll" isMarkerVisible="true">
        <dc:Bounds x="1885" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_172336i_di" bpmnElement="Gateway_172336i" isMarkerVisible="true">
        <dc:Bounds x="1745" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0d1frv1_di" bpmnElement="Event_0d1frv1">
        <dc:Bounds x="1752" y="462" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0cut48i_di" bpmnElement="Gateway_0cut48i" isMarkerVisible="true">
        <dc:Bounds x="1425" y="525" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_00opl3v" bpmnElement="IdentificationIdValidator">
        <dc:Bounds x="1590" y="510" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0csm1dd_di" bpmnElement="Gateway_0csm1dd" isMarkerVisible="true">
        <dc:Bounds x="1745" y="525" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_162rku6_di" bpmnElement="Event_162rku6">
        <dc:Bounds x="1752" y="622" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_18mi2jk_di" bpmnElement="Gateway_18mi2jk" isMarkerVisible="true">
        <dc:Bounds x="1885" y="525" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ouv3ww_di" bpmnElement="Gateway_1ouv3ww" isMarkerVisible="true">
        <dc:Bounds x="4245" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1yf7ptk_di" bpmnElement="Gateway_1yf7ptk" isMarkerVisible="true">
        <dc:Bounds x="4625" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ddefff" bpmnElement="Gateway_1row4bw" isMarkerVisible="true">
        <dc:Bounds x="4895" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0zy7r3h" bpmnElement="Gateway_0cfgzjg" isMarkerVisible="true">
        <dc:Bounds x="3925" y="345" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3911" y="402" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0i6xnr3" bpmnElement="Gateway_0rrrvav" isMarkerVisible="true">
        <dc:Bounds x="5705" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0wta7hw" bpmnElement="Gateway_11ndai3" isMarkerVisible="true">
        <dc:Bounds x="5445" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0hxv2c6" bpmnElement="Esb_blockEsim">
        <dc:Bounds x="5250" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0wquxu7" bpmnElement="ESBEsimBlockCallback">
        <dc:Bounds x="5550" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ws12zo" bpmnElement="Gateway_0unddxx" isMarkerVisible="true">
        <dc:Bounds x="5825" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06v4s0a" bpmnElement="Event_01uzsky">
        <dc:Bounds x="5452" y="482" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0zfczip" bpmnElement="Event_0uhjvip">
        <dc:Bounds x="5712" y="492" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_05u3j4t" bpmnElement="Gateway_122yee4" isMarkerVisible="true">
        <dc:Bounds x="5115" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1y903vd" bpmnElement="Arm_FetchAssetDetails">
        <dc:Bounds x="760" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0nstosa" bpmnElement="Gateway_1h1pk5h" isMarkerVisible="true">
        <dc:Bounds x="635" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ij8ele" bpmnElement="Gateway_0ccrygs" isMarkerVisible="true">
        <dc:Bounds x="995" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_058lq5k" bpmnElement="Gateway_16ba59x" isMarkerVisible="true">
        <dc:Bounds x="895" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0tpwl8x" bpmnElement="Event_0xns9cg">
        <dc:Bounds x="902" y="472" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0kcxe03_di" bpmnElement="Flow_0kcxe03">
        <di:waypoint x="188" y="370" />
        <di:waypoint x="300" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y9sn5m_di" bpmnElement="Flow_0y9sn5m">
        <di:waypoint x="2410" y="370" />
        <di:waypoint x="2475" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1awrbjt_di" bpmnElement="Flow_1awrbjt">
        <di:waypoint x="2525" y="370" />
        <di:waypoint x="2625" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2531" y="352" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qjz8qd_di" bpmnElement="Flow_1qjz8qd">
        <di:waypoint x="2500" y="395" />
        <di:waypoint x="2500" y="472" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2513" y="431" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_191nzu7_di" bpmnElement="Flow_191nzu7">
        <di:waypoint x="2940" y="370" />
        <di:waypoint x="3035" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_017f0t1_di" bpmnElement="Flow_017f0t1">
        <di:waypoint x="3060" y="395" />
        <di:waypoint x="3060" y="472" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3059" y="414" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_124ny4t_di" bpmnElement="Flow_124ny4t">
        <di:waypoint x="2085" y="370" />
        <di:waypoint x="2185" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2093" y="343" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hmnjzb_di" bpmnElement="Flow_1hmnjzb">
        <di:waypoint x="2060" y="395" />
        <di:waypoint x="2060" y="462" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2063" y="426" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p62f5e_di" bpmnElement="Flow_0p62f5e">
        <di:waypoint x="3085" y="370" />
        <di:waypoint x="3145" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3083" y="343" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00yqrsi_di" bpmnElement="Flow_00yqrsi">
        <di:waypoint x="3355" y="370" />
        <di:waypoint x="3520" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3429" y="352" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03u6f66_di" bpmnElement="Flow_03u6f66">
        <di:waypoint x="3330" y="345" />
        <di:waypoint x="3330" y="240" />
        <di:waypoint x="3790" y="240" />
        <di:waypoint x="3790" y="345" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3554" y="222" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cunzyv_di" bpmnElement="Flow_0cunzyv">
        <di:waypoint x="3620" y="370" />
        <di:waypoint x="3765" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fd0qw1_di" bpmnElement="Flow_1fd0qw1">
        <di:waypoint x="3815" y="370" />
        <di:waypoint x="3925" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eqmwrx_di" bpmnElement="Flow_1eqmwrx">
        <di:waypoint x="4100" y="345" />
        <di:waypoint x="4100" y="200" />
        <di:waypoint x="4770" y="200" />
        <di:waypoint x="4770" y="345" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4430" y="182" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r1vep6_di" bpmnElement="Flow_1r1vep6">
        <di:waypoint x="4795" y="370" />
        <di:waypoint x="4895" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0awpx7k_di" bpmnElement="Flow_0awpx7k">
        <di:waypoint x="5960" y="345" />
        <di:waypoint x="5960" y="230" />
        <di:waypoint x="6130" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6007" y="161" width="72" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1l2hlpg_di" bpmnElement="Flow_1l2hlpg">
        <di:waypoint x="5960" y="395" />
        <di:waypoint x="5960" y="510" />
        <di:waypoint x="6140" y="510" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5976" y="466" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c0f03x_di" bpmnElement="Flow_1c0f03x">
        <di:waypoint x="6240" y="510" />
        <di:waypoint x="6430" y="510" />
        <di:waypoint x="6430" y="395" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vvv1ml_di" bpmnElement="Flow_0vvv1ml">
        <di:waypoint x="400" y="370" />
        <di:waypoint x="485" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pgh3s3_di" bpmnElement="Flow_1pgh3s3">
        <di:waypoint x="510" y="395" />
        <di:waypoint x="510" y="472" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="453" y="431" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08xftxu_di" bpmnElement="Flow_08xftxu">
        <di:waypoint x="6455" y="370" />
        <di:waypoint x="6552" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07vuljq_di" bpmnElement="Flow_07vuljq">
        <di:waypoint x="6230" y="230" />
        <di:waypoint x="6430" y="230" />
        <di:waypoint x="6430" y="345" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14m0f1e_di" bpmnElement="Flow_14m0f1e">
        <di:waypoint x="2235" y="370" />
        <di:waypoint x="2310" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2265" y="352" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rk6k8w_di" bpmnElement="Flow_1rk6k8w">
        <di:waypoint x="2675" y="370" />
        <di:waypoint x="2735" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0m8r0uy_di" bpmnElement="Flow_0m8r0uy">
        <di:waypoint x="2210" y="345" />
        <di:waypoint x="2210" y="230" />
        <di:waypoint x="2650" y="230" />
        <di:waypoint x="2650" y="345" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2421" y="212" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vk7hpu_di" bpmnElement="Flow_0vk7hpu">
        <di:waypoint x="2785" y="370" />
        <di:waypoint x="2840" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2805" y="352" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xd8vn1_di" bpmnElement="Flow_0xd8vn1">
        <di:waypoint x="3195" y="370" />
        <di:waypoint x="3305" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11a2ays_di" bpmnElement="Flow_11a2ays">
        <di:waypoint x="2760" y="345" />
        <di:waypoint x="2760" y="240" />
        <di:waypoint x="3170" y="240" />
        <di:waypoint x="3170" y="345" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2956" y="222" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xnl3mx_di" bpmnElement="Flow_1xnl3mx">
        <di:waypoint x="1290" y="395" />
        <di:waypoint x="1290" y="472" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1303" y="431" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o8ufww_di" bpmnElement="Flow_1o8ufww">
        <di:waypoint x="1700" y="370" />
        <di:waypoint x="1745" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_038iwbr_di" bpmnElement="Flow_038iwbr">
        <di:waypoint x="535" y="370" />
        <di:waypoint x="635" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="562" y="352" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nqxv3x_di" bpmnElement="Flow_0nqxv3x">
        <di:waypoint x="1190" y="370" />
        <di:waypoint x="1265" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1trwe5t_di" bpmnElement="Flow_1trwe5t">
        <di:waypoint x="1315" y="370" />
        <di:waypoint x="1425" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1349" y="352" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11etlja_di" bpmnElement="Flow_11etlja">
        <di:waypoint x="1475" y="370" />
        <di:waypoint x="1600" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1529" y="352" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1prrytd_di" bpmnElement="Flow_1prrytd">
        <di:waypoint x="1935" y="370" />
        <di:waypoint x="2035" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_043h64e_di" bpmnElement="Flow_043h64e">
        <di:waypoint x="1795" y="370" />
        <di:waypoint x="1885" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1819" y="352" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fgoiti_di" bpmnElement="Flow_1fgoiti">
        <di:waypoint x="1770" y="395" />
        <di:waypoint x="1770" y="462" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1773" y="423" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12pubzs_di" bpmnElement="Flow_12pubzs">
        <di:waypoint x="1450" y="395" />
        <di:waypoint x="1450" y="525" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13el5q7_di" bpmnElement="Flow_13el5q7">
        <di:waypoint x="1475" y="550" />
        <di:waypoint x="1590" y="550" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05vjfjy_di" bpmnElement="Flow_05vjfjy">
        <di:waypoint x="1690" y="550" />
        <di:waypoint x="1745" y="550" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dl8ubs_di" bpmnElement="Flow_1dl8ubs">
        <di:waypoint x="1770" y="575" />
        <di:waypoint x="1770" y="622" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ol9fyl_di" bpmnElement="Flow_1ol9fyl">
        <di:waypoint x="1795" y="550" />
        <di:waypoint x="1885" y="550" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ginpkc_di" bpmnElement="Flow_1ginpkc">
        <di:waypoint x="1910" y="525" />
        <di:waypoint x="1910" y="395" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_043shfl_di" bpmnElement="Flow_043shfl">
        <di:waypoint x="1450" y="575" />
        <di:waypoint x="1450" y="680" />
        <di:waypoint x="1910" y="680" />
        <di:waypoint x="1910" y="575" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0to0bgk_di" bpmnElement="Flow_0to0bgk">
        <di:waypoint x="4125" y="370" />
        <di:waypoint x="4245" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nmr9xg_di" bpmnElement="Flow_0nmr9xg">
        <di:waypoint x="4500" y="370" />
        <di:waypoint x="4625" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00jx8dd_di" bpmnElement="Flow_00jx8dd">
        <di:waypoint x="4675" y="370" />
        <di:waypoint x="4745" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jq96qp_di" bpmnElement="Flow_0jq96qp">
        <di:waypoint x="4295" y="370" />
        <di:waypoint x="4400" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rmpw83_di" bpmnElement="Flow_0rmpw83">
        <di:waypoint x="4270" y="345" />
        <di:waypoint x="4270" y="270" />
        <di:waypoint x="4650" y="270" />
        <di:waypoint x="4650" y="345" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d7qlig_di" bpmnElement="Flow_0d7qlig">
        <di:waypoint x="4945" y="370" />
        <di:waypoint x="5115" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10po6r8_di" bpmnElement="Flow_10po6r8">
        <di:waypoint x="3975" y="370" />
        <di:waypoint x="4075" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00pu76a_di" bpmnElement="Flow_00pu76a">
        <di:waypoint x="3950" y="345" />
        <di:waypoint x="3950" y="110" />
        <di:waypoint x="4920" y="110" />
        <di:waypoint x="4920" y="345" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4390" y="83" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hzq9nu_di" bpmnElement="Flow_1hzq9nu">
        <di:waypoint x="5755" y="370" />
        <di:waypoint x="5825" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o7vxiu_di" bpmnElement="Flow_1o7vxiu">
        <di:waypoint x="5495" y="370" />
        <di:waypoint x="5550" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13ax0hy_di" bpmnElement="Flow_13ax0hy">
        <di:waypoint x="5350" y="370" />
        <di:waypoint x="5445" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ebeuaz_di" bpmnElement="Flow_0ebeuaz">
        <di:waypoint x="5650" y="370" />
        <di:waypoint x="5705" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qnhsxs_di" bpmnElement="Flow_0qnhsxs">
        <di:waypoint x="5875" y="370" />
        <di:waypoint x="5935" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qyd15o_di" bpmnElement="Flow_1qyd15o">
        <di:waypoint x="5470" y="395" />
        <di:waypoint x="5470" y="482" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5469" y="435" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mlbmzk_di" bpmnElement="Flow_0mlbmzk">
        <di:waypoint x="5730" y="395" />
        <di:waypoint x="5730" y="492" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5728" y="441" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09i5hod_di" bpmnElement="Flow_09i5hod">
        <di:waypoint x="5165" y="370" />
        <di:waypoint x="5250" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5111" y="409" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rmqi2m_di" bpmnElement="Flow_0rmqi2m">
        <di:waypoint x="5140" y="345" />
        <di:waypoint x="5140" y="240" />
        <di:waypoint x="5850" y="240" />
        <di:waypoint x="5850" y="345" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kd8dyw_di" bpmnElement="Flow_0kd8dyw">
        <di:waypoint x="860" y="370" />
        <di:waypoint x="895" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jgn8gm_di" bpmnElement="Flow_1jgn8gm">
        <di:waypoint x="685" y="370" />
        <di:waypoint x="760" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="620" y="408" width="79" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ovu2ln_di" bpmnElement="Flow_0ovu2ln">
        <di:waypoint x="1045" y="370" />
        <di:waypoint x="1090" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_089o5u4_di" bpmnElement="Flow_089o5u4">
        <di:waypoint x="945" y="370" />
        <di:waypoint x="995" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hz2pxk_di" bpmnElement="Flow_1hz2pxk">
        <di:waypoint x="660" y="345" />
        <di:waypoint x="660" y="270" />
        <di:waypoint x="1020" y="270" />
        <di:waypoint x="1020" y="345" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0sxplwc_di" bpmnElement="Flow_0sxplwc">
        <di:waypoint x="920" y="395" />
        <di:waypoint x="920" y="472" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="918" y="431" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
