<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1xm6jg7" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.15.0">
  <bpmn:process id="NumberRecycle" name="NumberRecycle" isExecutable="true" camunda:jobPriority="${priority}">
    <bpmn:serviceTask id="BSUpdateServiceState" name="Billing Update Service State" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_140muro</bpmn:incoming>
      <bpmn:outgoing>Flow_0icdfhf</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_00p2uky</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_05ovuqm</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_15aez6n" default="Flow_1oy393a">
      <bpmn:incoming>Flow_1stwzzc</bpmn:incoming>
      <bpmn:outgoing>Flow_081rpga</bpmn:outgoing>
      <bpmn:outgoing>Flow_1oy393a</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1stwzzc" sourceRef="OCSNumberRecycle" targetRef="Gateway_15aez6n" />
    <bpmn:endEvent id="Event_124rpcw">
      <bpmn:incoming>Flow_081rpga</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_081rpga" name="Failure" sourceRef="Gateway_15aez6n" targetRef="Event_124rpcw">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="OCSNumberRecycle" name="OCS NumberRecycle" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1gng0ha</bpmn:incoming>
      <bpmn:outgoing>Flow_1stwzzc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0zt1mnj" default="Flow_18omvms">
      <bpmn:incoming>Flow_0icdfhf</bpmn:incoming>
      <bpmn:outgoing>Flow_18omvms</bpmn:outgoing>
      <bpmn:outgoing>Flow_09fdnni</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0icdfhf" sourceRef="BSUpdateServiceState" targetRef="Gateway_0zt1mnj" />
    <bpmn:sequenceFlow id="Flow_18omvms" name="Success" sourceRef="Gateway_0zt1mnj" targetRef="NMSNumberRecycle" />
    <bpmn:sequenceFlow id="Flow_00p2uky" sourceRef="NMSNumberRecycle" targetRef="orderExecEnd" />
    <bpmn:endEvent id="Event_0zwusrk">
      <bpmn:incoming>Flow_09fdnni</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_09fdnni" name="Failure" sourceRef="Gateway_0zt1mnj" targetRef="Event_0zwusrk">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="NMSNumberRecycle" name="NMS NumberRecycle" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_18omvms</bpmn:incoming>
      <bpmn:outgoing>Flow_00p2uky</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0rm262y" default="Flow_1yptzti">
      <bpmn:incoming>Flow_05ovuqm</bpmn:incoming>
      <bpmn:outgoing>Flow_0xwzwcv</bpmn:outgoing>
      <bpmn:outgoing>Flow_1yptzti</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_05ovuqm" sourceRef="orderExecStart" targetRef="Gateway_0rm262y" />
    <bpmn:sequenceFlow id="Flow_0xwzwcv" name="yes" sourceRef="Gateway_0rm262y" targetRef="BookServiceFee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${additionalCharges}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1n1lb25" default="Flow_0fnivpc">
      <bpmn:incoming>Flow_1bpiau3</bpmn:incoming>
      <bpmn:outgoing>Flow_0fnivpc</bpmn:outgoing>
      <bpmn:outgoing>Flow_1otzg36</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1bpiau3" sourceRef="BookServiceFee" targetRef="Gateway_1n1lb25" />
    <bpmn:exclusiveGateway id="Gateway_1g7ju1g" default="Flow_1gng0ha">
      <bpmn:incoming>Flow_0fnivpc</bpmn:incoming>
      <bpmn:incoming>Flow_1yptzti</bpmn:incoming>
      <bpmn:outgoing>Flow_1gng0ha</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0fnivpc" name="success" sourceRef="Gateway_1n1lb25" targetRef="Gateway_1g7ju1g" />
    <bpmn:sequenceFlow id="Flow_1gng0ha" sourceRef="Gateway_1g7ju1g" targetRef="OCSNumberRecycle" />
    <bpmn:sequenceFlow id="Flow_1yptzti" name="no" sourceRef="Gateway_0rm262y" targetRef="Gateway_1g7ju1g" />
    <bpmn:endEvent id="Event_1uuq8kt">
      <bpmn:incoming>Flow_1otzg36</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1otzg36" name="failure" sourceRef="Gateway_1n1lb25" targetRef="Event_1uuq8kt">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BookServiceFee" name="BookServiceFee" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0xwzwcv</bpmn:incoming>
      <bpmn:outgoing>Flow_1bpiau3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1oy393a" name="Success" sourceRef="Gateway_15aez6n" targetRef="Hard_delete_from_OCS_history_table" />
    <bpmn:exclusiveGateway id="Gateway_024lwxa" default="Flow_140muro">
      <bpmn:incoming>Flow_1x4k8gd</bpmn:incoming>
      <bpmn:outgoing>Flow_140muro</bpmn:outgoing>
      <bpmn:outgoing>Flow_04kk1vy</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1x4k8gd" sourceRef="Hard_delete_from_OCS_history_table" targetRef="Gateway_024lwxa" />
    <bpmn:sequenceFlow id="Flow_140muro" name="Success" sourceRef="Gateway_024lwxa" targetRef="BSUpdateServiceState" />
    <bpmn:endEvent id="Event_0cb3qzr">
      <bpmn:incoming>Flow_04kk1vy</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_04kk1vy" name="Failure" sourceRef="Gateway_024lwxa" targetRef="Event_0cb3qzr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Hard_delete_from_OCS_history_table" name="Hard delete from OCS history table" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1oy393a</bpmn:incoming>
      <bpmn:outgoing>Flow_1x4k8gd</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmn:message id="Message_0rcq5am" name="SOMDeleteCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="NumberRecycle">
      <bpmndi:BPMNShape id="Activity_093rfbl_di" bpmnElement="BSUpdateServiceState">
        <dc:Bounds x="1590" y="157" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1y90ydb_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2142" y="179" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1imwspl_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="179" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_15aez6n_di" bpmnElement="Gateway_15aez6n" isMarkerVisible="true">
        <dc:Bounds x="1025" y="172" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_124rpcw_di" bpmnElement="Event_124rpcw">
        <dc:Bounds x="1032" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1cdix96_di" bpmnElement="OCSNumberRecycle">
        <dc:Bounds x="810" y="157" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0zt1mnj_di" bpmnElement="Gateway_0zt1mnj" isMarkerVisible="true">
        <dc:Bounds x="1775" y="172" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0zwusrk_di" bpmnElement="Event_0zwusrk">
        <dc:Bounds x="1782" y="295" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1d0wkx7_di" bpmnElement="NMSNumberRecycle">
        <dc:Bounds x="1940" y="157" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0rm262y_di" bpmnElement="Gateway_0rm262y" isMarkerVisible="true">
        <dc:Bounds x="245" y="172" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1n1lb25_di" bpmnElement="Gateway_1n1lb25" isMarkerVisible="true">
        <dc:Bounds x="525" y="172" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1g7ju1g_di" bpmnElement="Gateway_1g7ju1g" isMarkerVisible="true">
        <dc:Bounds x="645" y="172" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1uuq8kt_di" bpmnElement="Event_1uuq8kt">
        <dc:Bounds x="532" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1nhvcui_di" bpmnElement="BookServiceFee">
        <dc:Bounds x="360" y="157" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_024lwxa_di" bpmnElement="Gateway_024lwxa" isMarkerVisible="true">
        <dc:Bounds x="1405" y="172" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0cb3qzr_di" bpmnElement="Event_0cb3qzr">
        <dc:Bounds x="1412" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1g8fq23_di" bpmnElement="Hard_delete_from_OCS_history_table">
        <dc:Bounds x="1190" y="157" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1stwzzc_di" bpmnElement="Flow_1stwzzc">
        <di:waypoint x="910" y="197" />
        <di:waypoint x="1025" y="197" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_081rpga_di" bpmnElement="Flow_081rpga">
        <di:waypoint x="1050" y="222" />
        <di:waypoint x="1050" y="282" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1054" y="249" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0icdfhf_di" bpmnElement="Flow_0icdfhf">
        <di:waypoint x="1690" y="197" />
        <di:waypoint x="1775" y="197" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18omvms_di" bpmnElement="Flow_18omvms">
        <di:waypoint x="1825" y="197" />
        <di:waypoint x="1940" y="197" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1862" y="179" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00p2uky_di" bpmnElement="Flow_00p2uky">
        <di:waypoint x="2040" y="197" />
        <di:waypoint x="2142" y="197" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09fdnni_di" bpmnElement="Flow_09fdnni">
        <di:waypoint x="1800" y="222" />
        <di:waypoint x="1800" y="295" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1798" y="256" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05ovuqm_di" bpmnElement="Flow_05ovuqm">
        <di:waypoint x="188" y="197" />
        <di:waypoint x="245" y="197" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xwzwcv_di" bpmnElement="Flow_0xwzwcv">
        <di:waypoint x="295" y="197" />
        <di:waypoint x="360" y="197" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="319" y="179" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bpiau3_di" bpmnElement="Flow_1bpiau3">
        <di:waypoint x="460" y="197" />
        <di:waypoint x="525" y="197" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fnivpc_di" bpmnElement="Flow_0fnivpc">
        <di:waypoint x="575" y="197" />
        <di:waypoint x="645" y="197" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="590" y="179" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gng0ha_di" bpmnElement="Flow_1gng0ha">
        <di:waypoint x="695" y="197" />
        <di:waypoint x="810" y="197" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yptzti_di" bpmnElement="Flow_1yptzti">
        <di:waypoint x="270" y="172" />
        <di:waypoint x="270" y="100" />
        <di:waypoint x="670" y="100" />
        <di:waypoint x="670" y="172" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="464" y="82" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1otzg36_di" bpmnElement="Flow_1otzg36">
        <di:waypoint x="550" y="222" />
        <di:waypoint x="550" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="550" y="254" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oy393a_di" bpmnElement="Flow_1oy393a">
        <di:waypoint x="1075" y="197" />
        <di:waypoint x="1190" y="197" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1113" y="179" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1x4k8gd_di" bpmnElement="Flow_1x4k8gd">
        <di:waypoint x="1290" y="197" />
        <di:waypoint x="1405" y="197" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_140muro_di" bpmnElement="Flow_140muro">
        <di:waypoint x="1455" y="197" />
        <di:waypoint x="1590" y="197" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1502" y="179" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04kk1vy_di" bpmnElement="Flow_04kk1vy">
        <di:waypoint x="1430" y="222" />
        <di:waypoint x="1430" y="282" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1428" y="249" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>