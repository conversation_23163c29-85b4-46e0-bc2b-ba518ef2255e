<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/********/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1xm6jg7" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.15.0">
  <bpmn:process id="DeleteGroupAccount" name="Delete Group Account" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1vyjd5g</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="BSDeleteGroup" name="Billing Delete Group" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1vyjd5g</bpmn:incoming>
      <bpmn:outgoing>Flow_0qmn3dk</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0qmn3dk</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1vyjd5g" sourceRef="orderExecStart" targetRef="BSDeleteGroup" />
    <bpmn:sequenceFlow id="Flow_0qmn3dk" sourceRef="BSDeleteGroup" targetRef="orderExecEnd" />
  </bpmn:process>
  <bpmn:message id="Message_0rcq5am" name="SOMDeleteCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="DeleteGroupAccount">
      <bpmndi:BPMNEdge id="Flow_0qmn3dk_di" bpmnElement="Flow_0qmn3dk">
        <di:waypoint x="350" y="117" />
        <di:waypoint x="392" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vyjd5g_di" bpmnElement="Flow_1vyjd5g">
        <di:waypoint x="188" y="117" />
        <di:waypoint x="250" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_093rfbl_di" bpmnElement="BSDeleteGroup">
        <dc:Bounds x="250" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1y90ydb_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="392" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
