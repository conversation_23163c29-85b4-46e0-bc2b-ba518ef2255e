<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="DeleteIdentification" name="DeleteIdentification" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:serviceTask id="BSDeleteIdentification" name="Billing DeleteIdentification" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_19b9cbd</bpmn:incoming>
      <bpmn:outgoing>Flow_1nzkyg3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1nzkyg3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1nzkyg3" sourceRef="BSDeleteIdentification" targetRef="orderExecEnd" />
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_19b9cbd</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_19b9cbd" sourceRef="orderExecStart" targetRef="BSDeleteIdentification" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="DeleteIdentification">
      <bpmndi:BPMNEdge id="Flow_19b9cbd_di" bpmnElement="Flow_19b9cbd">
        <di:waypoint x="188" y="140" />
        <di:waypoint x="260" y="140" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nzkyg3_di" bpmnElement="Flow_1nzkyg3">
        <di:waypoint x="360" y="140" />
        <di:waypoint x="432" y="140" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0oi7fzj_di" bpmnElement="BSDeleteIdentification">
        <dc:Bounds x="260" y="100" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1qkykgh_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="432" y="122" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1s9muhq_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="122" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
