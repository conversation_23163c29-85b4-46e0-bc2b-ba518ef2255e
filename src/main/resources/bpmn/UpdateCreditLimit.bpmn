<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="UpdateCreditLimit" name="UpdateCreditLimit" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_15altqe</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="NCCUpdateCreditLimit" name="NCC Update Credit Limit&#10;" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1uy5sn4</bpmn:incoming>
      <bpmn:outgoing>Flow_1t0t7o7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSUpdateCreditLimit" name="Billing UpdateCreditLimit" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1wvj22a</bpmn:incoming>
      <bpmn:outgoing>Flow_0ebdxdu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1tyxpcl" default="Flow_1wvj22a">
      <bpmn:incoming>Flow_1t0t7o7</bpmn:incoming>
      <bpmn:outgoing>Flow_1wvj22a</bpmn:outgoing>
      <bpmn:outgoing>Flow_1luuggx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1wvj22a" name="Success" sourceRef="Gateway_1tyxpcl" targetRef="BSUpdateCreditLimit" />
    <bpmn:endEvent id="Event_1b7ko4d">
      <bpmn:incoming>Flow_1luuggx</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1luuggx" name="Failure" sourceRef="Gateway_1tyxpcl" targetRef="Event_1b7ko4d">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0ebdxdu</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1t0t7o7" sourceRef="NCCUpdateCreditLimit" targetRef="Gateway_1tyxpcl" />
    <bpmn:sequenceFlow id="Flow_0ebdxdu" sourceRef="BSUpdateCreditLimit" targetRef="orderExecEnd" />
    <bpmn:serviceTask id="BSBookDeposit" name="Billing BookDeposit" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_08ab14p</bpmn:incoming>
      <bpmn:outgoing>Flow_0w4w6ik</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:callActivity id="PaymentWorkflow" name="Payment Workflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow">
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:out variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_05r3dm5</bpmn:incoming>
      <bpmn:outgoing>Flow_0lbds8g</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_1i18jht" default="Flow_1lheqa0">
      <bpmn:incoming>Flow_0lbds8g</bpmn:incoming>
      <bpmn:outgoing>Flow_1t5tgzs</bpmn:outgoing>
      <bpmn:outgoing>Flow_1lheqa0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_184o5hn">
      <bpmn:incoming>Flow_1lheqa0</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1rr371q" name="is upfrontPayment?" default="Flow_0g91kj9">
      <bpmn:incoming>Flow_15altqe</bpmn:incoming>
      <bpmn:outgoing>Flow_05r3dm5</bpmn:outgoing>
      <bpmn:outgoing>Flow_0g91kj9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0hfj9ge">
      <bpmn:incoming>Flow_1t5tgzs</bpmn:incoming>
      <bpmn:incoming>Flow_0g91kj9</bpmn:incoming>
      <bpmn:outgoing>Flow_0qg0oae</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1vjkocm" default="Flow_1am8n8b">
      <bpmn:incoming>Flow_0w4w6ik</bpmn:incoming>
      <bpmn:outgoing>Flow_1am8n8b</bpmn:outgoing>
      <bpmn:outgoing>Flow_010hc6j</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1n74fvy" name="check if the channel is configured for the ERPDeposit" default="Flow_0zeykas">
      <bpmn:incoming>Flow_1am8n8b</bpmn:incoming>
      <bpmn:outgoing>Flow_1klwp3v</bpmn:outgoing>
      <bpmn:outgoing>Flow_0zeykas</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1ltwhuy">
      <bpmn:incoming>Flow_010hc6j</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1c1teui">
      <bpmn:incoming>Flow_0zeykas</bpmn:incoming>
      <bpmn:incoming>Flow_0y5mbru</bpmn:incoming>
      <bpmn:outgoing>Flow_1b37x76</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0qg0oae" sourceRef="Gateway_0hfj9ge" targetRef="Gateway_1wra78f" />
    <bpmn:sequenceFlow id="Flow_0w4w6ik" sourceRef="BSBookDeposit" targetRef="Gateway_1vjkocm" />
    <bpmn:sequenceFlow id="Flow_05r3dm5" name="true" sourceRef="Gateway_1rr371q" targetRef="PaymentWorkflow">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${sndWorkflowTrigger}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0lbds8g" sourceRef="PaymentWorkflow" targetRef="Gateway_1i18jht" />
    <bpmn:sequenceFlow id="Flow_1t5tgzs" name="Success" sourceRef="Gateway_1i18jht" targetRef="Gateway_0hfj9ge">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1lheqa0" name="Failure" sourceRef="Gateway_1i18jht" targetRef="Event_184o5hn" />
    <bpmn:sequenceFlow id="Flow_0g91kj9" name="false" sourceRef="Gateway_1rr371q" targetRef="Gateway_0hfj9ge" />
    <bpmn:sequenceFlow id="Flow_1klwp3v" name="yes" sourceRef="Gateway_1n74fvy" targetRef="ERPBookDeposit">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${erpDeposit}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1am8n8b" name="Success" sourceRef="Gateway_1vjkocm" targetRef="Gateway_1n74fvy" />
    <bpmn:sequenceFlow id="Flow_010hc6j" name="Failure" sourceRef="Gateway_1vjkocm" targetRef="Event_1ltwhuy">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0zeykas" name="no" sourceRef="Gateway_1n74fvy" targetRef="Gateway_1c1teui" />
    <bpmn:sequenceFlow id="Flow_15altqe" sourceRef="orderExecStart" targetRef="Gateway_1rr371q" />
    <bpmn:sequenceFlow id="Flow_1b37x76" sourceRef="Gateway_1c1teui" targetRef="Gateway_1ezfik7" />
    <bpmn:exclusiveGateway id="Gateway_0c9s7dk" default="Flow_0y5mbru">
      <bpmn:incoming>Flow_1b8wr7c</bpmn:incoming>
      <bpmn:outgoing>Flow_0y5mbru</bpmn:outgoing>
      <bpmn:outgoing>Flow_0vsmmg8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0y5mbru" name="Success" sourceRef="Gateway_0c9s7dk" targetRef="Gateway_1c1teui" />
    <bpmn:endEvent id="Event_1uizowl">
      <bpmn:incoming>Flow_0vsmmg8</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0vsmmg8" name="Failure" sourceRef="Gateway_0c9s7dk" targetRef="Event_1uizowl">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1ezfik7">
      <bpmn:incoming>Flow_1b37x76</bpmn:incoming>
      <bpmn:incoming>Flow_0a80gcd</bpmn:incoming>
      <bpmn:outgoing>Flow_1uy5sn4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1uy5sn4" sourceRef="Gateway_1ezfik7" targetRef="NCCUpdateCreditLimit" />
    <bpmn:exclusiveGateway id="Gateway_1wra78f" name="if deposit present" default="Flow_0a80gcd">
      <bpmn:incoming>Flow_0qg0oae</bpmn:incoming>
      <bpmn:outgoing>Flow_08ab14p</bpmn:outgoing>
      <bpmn:outgoing>Flow_0a80gcd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_08ab14p" name="yes" sourceRef="Gateway_1wra78f" targetRef="BSBookDeposit">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order.serviceManagement").element().hasProp('depositReqd') &amp;&amp; workflowData.jsonPath("$.order.serviceManagement.depositReqd").stringValue() == 'true'&amp;&amp; workflowData.jsonPath("$.order.serviceManagement").element().hasProp('deposit')}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0a80gcd" name="no" sourceRef="Gateway_1wra78f" targetRef="Gateway_1ezfik7" />
    <bpmn:serviceTask id="ERPBookDeposit" name="Deposit in ERP" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1klwp3v</bpmn:incoming>
      <bpmn:outgoing>Flow_1b8wr7c</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1b8wr7c" sourceRef="ERPBookDeposit" targetRef="Gateway_0c9s7dk" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="UpdateCreditLimit">
      <bpmndi:BPMNEdge id="Flow_1b8wr7c_di" bpmnElement="Flow_1b8wr7c">
        <di:waypoint x="1520" y="270" />
        <di:waypoint x="1595" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a80gcd_di" bpmnElement="Flow_0a80gcd">
        <di:waypoint x="860" y="245" />
        <di:waypoint x="860" y="120" />
        <di:waypoint x="1870" y="120" />
        <di:waypoint x="1870" y="245" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1359" y="102" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08ab14p_di" bpmnElement="Flow_08ab14p">
        <di:waypoint x="885" y="270" />
        <di:waypoint x="1000" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="934" y="252" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uy5sn4_di" bpmnElement="Flow_1uy5sn4">
        <di:waypoint x="1895" y="270" />
        <di:waypoint x="1950" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vsmmg8_di" bpmnElement="Flow_0vsmmg8">
        <di:waypoint x="1620" y="295" />
        <di:waypoint x="1620" y="372" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1618" y="331" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y5mbru_di" bpmnElement="Flow_0y5mbru">
        <di:waypoint x="1645" y="270" />
        <di:waypoint x="1715" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1659" y="252" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b37x76_di" bpmnElement="Flow_1b37x76">
        <di:waypoint x="1765" y="270" />
        <di:waypoint x="1845" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15altqe_di" bpmnElement="Flow_15altqe">
        <di:waypoint x="188" y="270" />
        <di:waypoint x="275" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zeykas_di" bpmnElement="Flow_0zeykas">
        <di:waypoint x="1310" y="245" />
        <di:waypoint x="1310" y="160" />
        <di:waypoint x="1740" y="160" />
        <di:waypoint x="1740" y="245" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1519" y="142" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_010hc6j_di" bpmnElement="Flow_010hc6j">
        <di:waypoint x="1190" y="295" />
        <di:waypoint x="1190" y="372" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1190" y="331" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1am8n8b_di" bpmnElement="Flow_1am8n8b">
        <di:waypoint x="1215" y="270" />
        <di:waypoint x="1285" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1229" y="252" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1klwp3v_di" bpmnElement="Flow_1klwp3v">
        <di:waypoint x="1335" y="270" />
        <di:waypoint x="1420" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1370" y="252" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0g91kj9_di" bpmnElement="Flow_0g91kj9">
        <di:waypoint x="300" y="245" />
        <di:waypoint x="300" y="170" />
        <di:waypoint x="730" y="170" />
        <di:waypoint x="730" y="245" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="504" y="152" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lheqa0_di" bpmnElement="Flow_1lheqa0">
        <di:waypoint x="600" y="295" />
        <di:waypoint x="600" y="372" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="599" y="309" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t5tgzs_di" bpmnElement="Flow_1t5tgzs">
        <di:waypoint x="625" y="270" />
        <di:waypoint x="705" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="644" y="252" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lbds8g_di" bpmnElement="Flow_0lbds8g">
        <di:waypoint x="500" y="270" />
        <di:waypoint x="575" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05r3dm5_di" bpmnElement="Flow_05r3dm5">
        <di:waypoint x="325" y="270" />
        <di:waypoint x="400" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="355" y="252" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0w4w6ik_di" bpmnElement="Flow_0w4w6ik">
        <di:waypoint x="1100" y="270" />
        <di:waypoint x="1165" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qg0oae_di" bpmnElement="Flow_0qg0oae">
        <di:waypoint x="755" y="270" />
        <di:waypoint x="835" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ebdxdu_di" bpmnElement="Flow_0ebdxdu">
        <di:waypoint x="2410" y="270" />
        <di:waypoint x="2502" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t0t7o7_di" bpmnElement="Flow_1t0t7o7">
        <di:waypoint x="2050" y="270" />
        <di:waypoint x="2115" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1luuggx_di" bpmnElement="Flow_1luuggx">
        <di:waypoint x="2140" y="295" />
        <di:waypoint x="2140" y="382" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2138" y="359" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wvj22a_di" bpmnElement="Flow_1wvj22a">
        <di:waypoint x="2165" y="270" />
        <di:waypoint x="2310" y="270" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2217" y="252" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_1x7dsnm_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="252" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1tegtwq_di" bpmnElement="NCCUpdateCreditLimit">
        <dc:Bounds x="1950" y="230" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_19c2pez_di" bpmnElement="BSUpdateCreditLimit">
        <dc:Bounds x="2310" y="230" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tyxpcl_di" bpmnElement="Gateway_1tyxpcl" isMarkerVisible="true">
        <dc:Bounds x="2115" y="245" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1b7ko4d_di" bpmnElement="Event_1b7ko4d">
        <dc:Bounds x="2122" y="382" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0oin3ho_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2502" y="252" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BSBookDeposit_di" bpmnElement="BSBookDeposit">
        <dc:Bounds x="1000" y="230" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="PaymentWorkflow_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="400" y="230" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1i18jht_di" bpmnElement="Gateway_1i18jht" isMarkerVisible="true">
        <dc:Bounds x="575" y="245" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_184o5hn_di" bpmnElement="Event_184o5hn">
        <dc:Bounds x="582" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1rr371q_di" bpmnElement="Gateway_1rr371q" isMarkerVisible="true">
        <dc:Bounds x="275" y="245" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="259" y="302" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0hfj9ge_di" bpmnElement="Gateway_0hfj9ge" isMarkerVisible="true">
        <dc:Bounds x="705" y="245" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1vjkocm_di" bpmnElement="Gateway_1vjkocm" isMarkerVisible="true">
        <dc:Bounds x="1165" y="245" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1n74fvy_di" bpmnElement="Gateway_1n74fvy" isMarkerVisible="true">
        <dc:Bounds x="1285" y="245" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1270.5" y="304.5" width="79" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ltwhuy_di" bpmnElement="Event_1ltwhuy">
        <dc:Bounds x="1172" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1c1teui_di" bpmnElement="Gateway_1c1teui" isMarkerVisible="true">
        <dc:Bounds x="1715" y="245" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0c9s7dk_di" bpmnElement="Gateway_0c9s7dk" isMarkerVisible="true">
        <dc:Bounds x="1595" y="245" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1uizowl_di" bpmnElement="Event_1uizowl">
        <dc:Bounds x="1602" y="372" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ezfik7_di" bpmnElement="Gateway_1ezfik7" isMarkerVisible="true">
        <dc:Bounds x="1845" y="245" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1wra78f_di" bpmnElement="Gateway_1wra78f" isMarkerVisible="true">
        <dc:Bounds x="835" y="245" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="819" y="302" width="85" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ERPDeposit_di" bpmnElement="ERPBookDeposit">
        <dc:Bounds x="1420" y="230" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
