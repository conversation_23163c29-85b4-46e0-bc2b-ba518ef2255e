<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0mlnpxf" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.28.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.20.0">
  <bpmn:process id="ChangeGroupSubscriptionOwner" name="ChangeGroupSubscriptionOwner" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}" camunda:historyTimeToLive="180">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0vzmd6n</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0vzmd6n" sourceRef="StartEvent_1" targetRef="BSViewGroupSubscription" />
    <bpmn:exclusiveGateway id="Gateway_10e9730" default="Flow_1cy0yc0">
      <bpmn:incoming>Flow_15grzt1</bpmn:incoming>
      <bpmn:outgoing>Flow_1cy0yc0</bpmn:outgoing>
      <bpmn:outgoing>Flow_08etddi</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_15grzt1" sourceRef="BSViewGroupSubscription" targetRef="Gateway_10e9730" />
    <bpmn:sequenceFlow id="Flow_1cy0yc0" sourceRef="Gateway_10e9730" targetRef="BillingCancelGroupSubscription" />
    <bpmn:exclusiveGateway id="Gateway_06syb6c">
      <bpmn:incoming>Flow_1f1s4iq</bpmn:incoming>
      <bpmn:outgoing>Flow_0s5rmj3</bpmn:outgoing>
      <bpmn:outgoing>Flow_19x5300</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1f1s4iq" sourceRef="BillingCancelGroupSubscription" targetRef="Gateway_06syb6c" />
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_081bwa4</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSViewGroupSubscription" name="BS_View_GroupSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0vzmd6n</bpmn:incoming>
      <bpmn:outgoing>Flow_15grzt1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1rovmfe">
      <bpmn:incoming>Flow_08etddi</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_08etddi" name="Failure" sourceRef="Gateway_10e9730" targetRef="Event_1rovmfe">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1bpdh0q">
      <bpmn:incoming>Flow_0s5rmj3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0s5rmj3" name="Failure" sourceRef="Gateway_06syb6c" targetRef="Event_1bpdh0q">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="BillingCancelGroupSubscription" name="BillingCancelGroupSubscription" camunda:asyncBefore="true" calledElement="CancelGroupSubscriptions" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1cy0yc0</bpmn:incoming>
      <bpmn:outgoing>Flow_1f1s4iq</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:asyncBefore="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.BSViewGroupSubscriptionResponseAttributes&#34;).elementList()}" camunda:elementVariable="execution">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_19x5300" sourceRef="Gateway_06syb6c" targetRef="BillingAddGroupSubscription" />
    <bpmn:sequenceFlow id="Flow_081bwa4" sourceRef="BillingAddGroupSubscription" targetRef="orderExecEnd" />
    <bpmn:serviceTask id="BillingAddGroupSubscription" name="BillingAddGroupSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_19x5300</bpmn:incoming>
      <bpmn:outgoing>Flow_081bwa4</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ChangeGroupSubscriptionOwner">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_10e9730_di" bpmnElement="Gateway_10e9730" isMarkerVisible="true">
        <dc:Bounds x="425" y="92" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_06syb6c_di" bpmnElement="Gateway_06syb6c" isMarkerVisible="true">
        <dc:Bounds x="685" y="92" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_15fsfk6_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="952" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0n21qho_di" bpmnElement="BSViewGroupSubscription">
        <dc:Bounds x="270" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1rovmfe_di" bpmnElement="Event_1rovmfe">
        <dc:Bounds x="432" y="212" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1bpdh0q_di" bpmnElement="Event_1bpdh0q">
        <dc:Bounds x="692" y="212" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0xhu04e_di" bpmnElement="BillingCancelGroupSubscription">
        <dc:Bounds x="530" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1vh04w2_di" bpmnElement="BillingAddGroupSubscription">
        <dc:Bounds x="790" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0vzmd6n_di" bpmnElement="Flow_0vzmd6n">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="270" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15grzt1_di" bpmnElement="Flow_15grzt1">
        <di:waypoint x="370" y="117" />
        <di:waypoint x="425" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cy0yc0_di" bpmnElement="Flow_1cy0yc0">
        <di:waypoint x="475" y="117" />
        <di:waypoint x="530" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1f1s4iq_di" bpmnElement="Flow_1f1s4iq">
        <di:waypoint x="630" y="117" />
        <di:waypoint x="685" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08etddi_di" bpmnElement="Flow_08etddi">
        <di:waypoint x="450" y="142" />
        <di:waypoint x="450" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="448" y="174" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0s5rmj3_di" bpmnElement="Flow_0s5rmj3">
        <di:waypoint x="710" y="142" />
        <di:waypoint x="710" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="708" y="174" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19x5300_di" bpmnElement="Flow_19x5300">
        <di:waypoint x="735" y="117" />
        <di:waypoint x="790" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_081bwa4_di" bpmnElement="Flow_081bwa4">
        <di:waypoint x="890" y="117" />
        <di:waypoint x="952" y="117" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
