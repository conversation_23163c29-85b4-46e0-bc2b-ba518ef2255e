<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0">
  <bpmn:process id="CreateAccount" name="CreateAccount" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:serviceTask id="BSCreateAccount" name="Account creation in billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_141scf5</bpmn:incoming>
      <bpmn:outgoing>Flow_0qcha4e</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0qoac4w" default="Flow_1fj53du">
      <bpmn:incoming>Flow_0qcha4e</bpmn:incoming>
      <bpmn:outgoing>Flow_1fj53du</bpmn:outgoing>
      <bpmn:outgoing>Flow_0wdo5p0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0qcha4e" sourceRef="BSCreateAccount" targetRef="Gateway_0qoac4w" />
    <bpmn:endEvent id="Event_0ki8c02">
      <bpmn:incoming>Flow_1fj53du</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1fj53du" sourceRef="Gateway_0qoac4w" targetRef="Event_0ki8c02" />
    <bpmn:sequenceFlow id="Flow_0wdo5p0" name="Success" sourceRef="Gateway_0qoac4w" targetRef="OCSCreateAccount">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>SequenceFlow_1wls5h3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="OCSCreateAccount" name="Account Creation In OCS System" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0wdo5p0</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1wls5h3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1wls5h3" sourceRef="OCSCreateAccount" targetRef="orderExecEnd" />
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_141scf5</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_141scf5" sourceRef="orderExecStart" targetRef="BSCreateAccount" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CreateAccount">
      <bpmndi:BPMNShape id="Activity_0ngn6ow_di" bpmnElement="BSCreateAccount">
        <dc:Bounds x="340" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0qoac4w_di" bpmnElement="Gateway_0qoac4w" isMarkerVisible="true">
        <dc:Bounds x="485" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ki8c02_di" bpmnElement="Event_0ki8c02">
        <dc:Bounds x="492" y="232" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1p88c2w_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="812" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1uix45n_di" bpmnElement="OCSCreateAccount">
        <dc:Bounds x="629" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0cws8xx_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0qcha4e_di" bpmnElement="Flow_0qcha4e">
        <di:waypoint x="440" y="120" />
        <di:waypoint x="485" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fj53du_di" bpmnElement="Flow_1fj53du">
        <di:waypoint x="510" y="145" />
        <di:waypoint x="510" y="232" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wdo5p0_di" bpmnElement="Flow_0wdo5p0">
        <di:waypoint x="535" y="120" />
        <di:waypoint x="629" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="562" y="102" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1wls5h3_di" bpmnElement="SequenceFlow_1wls5h3">
        <di:waypoint x="729" y="120" />
        <di:waypoint x="812" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_141scf5_di" bpmnElement="Flow_141scf5">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="340" y="120" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
