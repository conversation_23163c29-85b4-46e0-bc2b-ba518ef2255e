<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_07kq1lv" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.6.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.19.0">
  <bpmn:process id="BillingCancelSubscriptionProcess" name="BillingCancelSubscription" isExecutable="true" camunda:historyTimeToLive="180">
    <bpmn:startEvent id="subOrderExecStart">
      <bpmn:outgoing>Flow_0iq4ewp</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_1uf4io5" default="Flow_02xpp64">
      <bpmn:incoming>Flow_0u5fqrk</bpmn:incoming>
      <bpmn:outgoing>Flow_15k0t00</bpmn:outgoing>
      <bpmn:outgoing>Flow_02xpp64</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0dc9e6m">
      <bpmn:incoming>Flow_15k0t00</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="subProcessEndEvent2">
      <bpmn:incoming>Flow_02xpp64</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="Billing_CancelSubscription" name="Billing Cancel Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0iq4ewp</bpmn:incoming>
      <bpmn:outgoing>Flow_0u5fqrk</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0iq4ewp" sourceRef="subOrderExecStart" targetRef="Billing_CancelSubscription" />
    <bpmn:sequenceFlow id="Flow_0u5fqrk" sourceRef="Billing_CancelSubscription" targetRef="Gateway_1uf4io5" />
    <bpmn:sequenceFlow id="Flow_15k0t00" name="Failure" sourceRef="Gateway_1uf4io5" targetRef="Event_0dc9e6m">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_02xpp64" name="Success" sourceRef="Gateway_1uf4io5" targetRef="subProcessEndEvent2" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="BillingCancelSubscriptionProcess">
      <bpmndi:BPMNEdge id="Flow_02xpp64_di" bpmnElement="Flow_02xpp64">
        <di:waypoint x="498" y="117" />
        <di:waypoint x="585" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="520" y="99" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15k0t00_di" bpmnElement="Flow_15k0t00">
        <di:waypoint x="473" y="142" />
        <di:waypoint x="473" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="486" y="174" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u5fqrk_di" bpmnElement="Flow_0u5fqrk">
        <di:waypoint x="393" y="117" />
        <di:waypoint x="448" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0iq4ewp_di" bpmnElement="Flow_0iq4ewp">
        <di:waypoint x="188" y="117" />
        <di:waypoint x="293" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_00vhiph" bpmnElement="subOrderExecStart">
        <dc:Bounds x="152" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1uf4io5_di" bpmnElement="Gateway_1uf4io5" isMarkerVisible="true">
        <dc:Bounds x="448" y="92" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0dc9e6m_di" bpmnElement="Event_0dc9e6m">
        <dc:Bounds x="455" y="212" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0zh3wjy_di" bpmnElement="subProcessEndEvent2">
        <dc:Bounds x="585" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0hgf4w6_di" bpmnElement="Billing_CancelSubscription">
        <dc:Bounds x="293" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
