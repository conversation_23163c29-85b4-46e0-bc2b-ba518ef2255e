<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1tst601" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.28.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.19.0">
  <bpmn:process id="ChangeSubscription" name="ChangeSubscription" isExecutable="true" camunda:historyTimeToLive="180">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_1jp4qu1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_10kxi27" sourceRef="PaymentWorkflow" targetRef="CheckEligibilityCriteria" />
    <bpmn:callActivity id="PaymentWorkflow" name="Payment and future order wokrflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1nzpmwg</bpmn:incoming>
      <bpmn:outgoing>Flow_10kxi27</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_0o7y2v7" default="Flow_1doamtx">
      <bpmn:incoming>Flow_0otz4km</bpmn:incoming>
      <bpmn:outgoing>Flow_1doamtx</bpmn:outgoing>
      <bpmn:outgoing>Flow_0w1j1xw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="OrderEnrichment" name="Erich plan details" camunda:asyncBefore="true" camunda:delegateExpression="${orderEnrichmentDelegate}">
      <bpmn:incoming>Flow_1athhpj</bpmn:incoming>
      <bpmn:outgoing>Flow_14bp7p3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="CheckEligibilityCriteria" name="Plan comparison" camunda:asyncBefore="true" camunda:delegateExpression="${planComparision}">
      <bpmn:incoming>Flow_10kxi27</bpmn:incoming>
      <bpmn:outgoing>Flow_0otz4km</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0otz4km" sourceRef="CheckEligibilityCriteria" targetRef="Gateway_0o7y2v7" />
    <bpmn:serviceTask id="UPCPlanFetch" name="UPC Plan Fetch for non-eligible addons" camunda:asyncBefore="true" camunda:delegateExpression="${upcFetchNonAllowedAddons}">
      <bpmn:incoming>Flow_0reljoi</bpmn:incoming>
      <bpmn:outgoing>Flow_13w7vn0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_18vh7yf" default="Flow_0n2s41b">
      <bpmn:incoming>Flow_13w7vn0</bpmn:incoming>
      <bpmn:outgoing>Flow_00d6k6u</bpmn:outgoing>
      <bpmn:outgoing>Flow_0n2s41b</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1wuufgs">
      <bpmn:incoming>Flow_00d6k6u</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1c05mlk">
      <bpmn:incoming>Flow_0n2s41b</bpmn:incoming>
      <bpmn:incoming>Flow_0td4i25</bpmn:incoming>
      <bpmn:outgoing>Flow_10mn3l6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1ufwied" name="is UPC non-allowed addons fetch req" default="Flow_0td4i25">
      <bpmn:incoming>Flow_1doamtx</bpmn:incoming>
      <bpmn:outgoing>Flow_0reljoi</bpmn:outgoing>
      <bpmn:outgoing>Flow_0td4i25</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0reljoi" name="yes" sourceRef="Gateway_1ufwied" targetRef="UPCPlanFetch">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${upcFecthReq}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_13w7vn0" sourceRef="UPCPlanFetch" targetRef="Gateway_18vh7yf" />
    <bpmn:sequenceFlow id="Flow_00d6k6u" name="Failure" sourceRef="Gateway_18vh7yf" targetRef="Event_1wuufgs">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0n2s41b" name="Success" sourceRef="Gateway_18vh7yf" targetRef="Gateway_1c05mlk" />
    <bpmn:sequenceFlow id="Flow_0td4i25" name="no" sourceRef="Gateway_1ufwied" targetRef="Gateway_1c05mlk" />
    <bpmn:sequenceFlow id="Flow_1doamtx" name="Success" sourceRef="Gateway_0o7y2v7" targetRef="Gateway_1ufwied" />
    <bpmn:endEvent id="Event_151ym8d">
      <bpmn:incoming>Flow_0w1j1xw</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0w1j1xw" name="Failure" sourceRef="Gateway_0o7y2v7" targetRef="Event_151ym8d">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="OCSChangePlan" name="Change base plan in OCS via ESB" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0fdmfbt</bpmn:incoming>
      <bpmn:outgoing>Flow_1miqqlf</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1rsmcee">
      <bpmn:incoming>Flow_1re3e5l</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_02szurk">
      <bpmn:incoming>Flow_12nmr53</bpmn:incoming>
      <bpmn:incoming>Flow_072rgkw</bpmn:incoming>
      <bpmn:outgoing>Flow_0js6icl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1avpoa4" name="check if contract exists for new base plan" default="Flow_12nmr53">
      <bpmn:incoming>Flow_1s5i2qo</bpmn:incoming>
      <bpmn:outgoing>Flow_12nmr53</bpmn:outgoing>
      <bpmn:outgoing>Flow_1un3qui</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1r38i6r" default="Flow_072rgkw">
      <bpmn:incoming>Flow_1q7c9zp</bpmn:incoming>
      <bpmn:outgoing>Flow_1re3e5l</bpmn:outgoing>
      <bpmn:outgoing>Flow_072rgkw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="BSCreateContract" name="Billing Create Contract" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1un3qui</bpmn:incoming>
      <bpmn:outgoing>Flow_1q7c9zp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSChangeSubscription" name=" Change subscription in Billing System" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1u05wqw</bpmn:incoming>
      <bpmn:outgoing>Flow_0vab98d</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0omclyp" default="Flow_1s5i2qo">
      <bpmn:incoming>Flow_0vab98d</bpmn:incoming>
      <bpmn:outgoing>Flow_1s5i2qo</bpmn:outgoing>
      <bpmn:outgoing>Flow_1rxp1ep</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1ix0ov7">
      <bpmn:incoming>Flow_1rxp1ep</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1re3e5l" name="Failure" sourceRef="Gateway_1r38i6r" targetRef="Event_1rsmcee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_12nmr53" name="no" sourceRef="Gateway_1avpoa4" targetRef="Gateway_02szurk" />
    <bpmn:sequenceFlow id="Flow_072rgkw" name="Success" sourceRef="Gateway_1r38i6r" targetRef="Gateway_02szurk" />
    <bpmn:sequenceFlow id="Flow_1s5i2qo" name="Success" sourceRef="Gateway_0omclyp" targetRef="Gateway_1avpoa4" />
    <bpmn:sequenceFlow id="Flow_1un3qui" name="yes" sourceRef="Gateway_1avpoa4" targetRef="BSCreateContract">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${upcFecthReq}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1q7c9zp" sourceRef="BSCreateContract" targetRef="Gateway_1r38i6r" />
    <bpmn:sequenceFlow id="Flow_0vab98d" sourceRef="BSChangeSubscription" targetRef="Gateway_0omclyp" />
    <bpmn:sequenceFlow id="Flow_1rxp1ep" name="Failure" sourceRef="Gateway_0omclyp" targetRef="Event_1ix0ov7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_10mn3l6" sourceRef="Gateway_1c05mlk" targetRef="Gateway_0voml6q" />
    <bpmn:endEvent id="Event_0b8e9iw">
      <bpmn:incoming>Flow_0sb2ehf</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1298igf" default="Flow_1o48tgp">
      <bpmn:incoming>Flow_1l6gth8</bpmn:incoming>
      <bpmn:outgoing>Flow_0sb2ehf</bpmn:outgoing>
      <bpmn:outgoing>Flow_1o48tgp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1eatg7u" name="check if contracts exists for old base plan" default="Flow_0ugtdqz">
      <bpmn:incoming>Flow_1o48tgp</bpmn:incoming>
      <bpmn:outgoing>Flow_0nl3kfc</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ugtdqz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="BSCancelContract" name="Billing Cancel Contract" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0nl3kfc</bpmn:incoming>
      <bpmn:outgoing>Flow_0mto1sa</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1pk6fr0" default="Flow_06c3zvy">
      <bpmn:incoming>Flow_0mto1sa</bpmn:incoming>
      <bpmn:outgoing>Flow_06c3zvy</bpmn:outgoing>
      <bpmn:outgoing>Flow_0pfgrs8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0mm7dxo">
      <bpmn:incoming>Flow_0ugtdqz</bpmn:incoming>
      <bpmn:incoming>Flow_06c3zvy</bpmn:incoming>
      <bpmn:outgoing>Flow_1erowpw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_19g0r5p">
      <bpmn:incoming>Flow_0pfgrs8</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSFetchContract" name="Billing Fetch Contract" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0e1u60m</bpmn:incoming>
      <bpmn:outgoing>Flow_1l6gth8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0sb2ehf" name="Failure" sourceRef="Gateway_1298igf" targetRef="Event_0b8e9iw">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1l6gth8" sourceRef="BSFetchContract" targetRef="Gateway_1298igf" />
    <bpmn:sequenceFlow id="Flow_1o48tgp" name="Success" sourceRef="Gateway_1298igf" targetRef="Gateway_1eatg7u" />
    <bpmn:sequenceFlow id="Flow_0nl3kfc" name="yes" sourceRef="Gateway_1eatg7u" targetRef="BSCancelContract">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.workflowData").element().hasProp('BSFetchContractResponseAttributes') &amp;&amp;  workflowData.jsonPath("$.workflowData.BSFetchContractResponseAttributes").element().hasProp('contract')}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ugtdqz" name="no active contracts" sourceRef="Gateway_1eatg7u" targetRef="Gateway_0mm7dxo" />
    <bpmn:sequenceFlow id="Flow_0mto1sa" sourceRef="BSCancelContract" targetRef="Gateway_1pk6fr0" />
    <bpmn:sequenceFlow id="Flow_06c3zvy" name="Success" sourceRef="Gateway_1pk6fr0" targetRef="Gateway_0mm7dxo" />
    <bpmn:sequenceFlow id="Flow_0pfgrs8" name="Failure" sourceRef="Gateway_1pk6fr0" targetRef="Event_19g0r5p">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1erowpw" sourceRef="Gateway_0mm7dxo" targetRef="Gateway_1x8vu6y" />
    <bpmn:serviceTask id="BSAddSubscription" name="BS AddSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1yb85f2</bpmn:incoming>
      <bpmn:outgoing>Flow_1e3ozvq</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0p68hnv">
      <bpmn:incoming>Flow_17ecrj4</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_13s29ze" default="Flow_1vcsnnz">
      <bpmn:incoming>Flow_1e3ozvq</bpmn:incoming>
      <bpmn:outgoing>Flow_17ecrj4</bpmn:outgoing>
      <bpmn:outgoing>Flow_1vcsnnz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_192ye9b" name="is billing AddSub req" default="Flow_1gr0h73">
      <bpmn:incoming>Flow_0js6icl</bpmn:incoming>
      <bpmn:outgoing>Flow_1yb85f2</bpmn:outgoing>
      <bpmn:outgoing>Flow_1gr0h73</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1r3xq2d">
      <bpmn:incoming>Flow_1vcsnnz</bpmn:incoming>
      <bpmn:incoming>Flow_1gr0h73</bpmn:incoming>
      <bpmn:outgoing>Flow_13nt8ri</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_13nt8ri" sourceRef="Gateway_1r3xq2d" targetRef="SOMFetchServiceRegistry" />
    <bpmn:sequenceFlow id="Flow_1yb85f2" name="yes" sourceRef="Gateway_192ye9b" targetRef="BSAddSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.workflowData").element().hasProp('BSAddSubscriptionReq') &amp;&amp; workflowData.jsonPath("$.workflowData.BSAddSubscriptionReq").stringValue() == 'true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1e3ozvq" sourceRef="BSAddSubscription" targetRef="Gateway_13s29ze" />
    <bpmn:sequenceFlow id="Flow_17ecrj4" name="Failure" sourceRef="Gateway_13s29ze" targetRef="Event_0p68hnv">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1vcsnnz" name="success" sourceRef="Gateway_13s29ze" targetRef="Gateway_1r3xq2d" />
    <bpmn:sequenceFlow id="Flow_1gr0h73" name="no" sourceRef="Gateway_192ye9b" targetRef="Gateway_1r3xq2d" />
    <bpmn:serviceTask id="SOMChangeBasePlan" name="SOM Change Base Plan" camunda:asyncBefore="true" camunda:delegateExpression="${somChangePlan}">
      <bpmn:incoming>Flow_1aqx2kh</bpmn:incoming>
      <bpmn:outgoing>Flow_15zzj8j</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:receiveTask id="SOMChangePlanCallBack" name="SOM Change Plan WaitSignal" camunda:asyncBefore="true" messageRef="Message_090pae1">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOMChangeBasePlan</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOMChangeBasePlan" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1tkvjcj</bpmn:incoming>
      <bpmn:outgoing>Flow_0u16yoq</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_1k7vvo8" default="Flow_1tkvjcj">
      <bpmn:incoming>Flow_15zzj8j</bpmn:incoming>
      <bpmn:outgoing>Flow_1tkvjcj</bpmn:outgoing>
      <bpmn:outgoing>Flow_0iiviln</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0gdnvs7">
      <bpmn:incoming>Flow_0iiviln</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_15zzj8j" sourceRef="SOMChangeBasePlan" targetRef="Gateway_1k7vvo8" />
    <bpmn:sequenceFlow id="Flow_1tkvjcj" name="Success" sourceRef="Gateway_1k7vvo8" targetRef="SOMChangePlanCallBack" />
    <bpmn:sequenceFlow id="Flow_0iiviln" name="Failure" sourceRef="Gateway_1k7vvo8" targetRef="Event_0gdnvs7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1apbmwi" default="Flow_0fdmfbt">
      <bpmn:incoming>Flow_0u16yoq</bpmn:incoming>
      <bpmn:outgoing>Flow_0fdmfbt</bpmn:outgoing>
      <bpmn:outgoing>Flow_0psbiml</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0u16yoq" sourceRef="SOMChangePlanCallBack" targetRef="Gateway_1apbmwi" />
    <bpmn:sequenceFlow id="Flow_0fdmfbt" name="Success" sourceRef="Gateway_1apbmwi" targetRef="OCSChangePlan" />
    <bpmn:endEvent id="Event_17zy133">
      <bpmn:incoming>Flow_0psbiml</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0psbiml" name="Failure" sourceRef="Gateway_1apbmwi" targetRef="Event_17zy133">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1vbu6ux" default="Flow_15qkglt">
      <bpmn:incoming>Flow_1miqqlf</bpmn:incoming>
      <bpmn:outgoing>Flow_05m0ejw</bpmn:outgoing>
      <bpmn:outgoing>Flow_15qkglt</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1miqqlf" sourceRef="OCSChangePlan" targetRef="Gateway_1vbu6ux" />
    <bpmn:endEvent id="Event_16tchcb">
      <bpmn:incoming>Flow_05m0ejw</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_05m0ejw" name="Failure" sourceRef="Gateway_1vbu6ux" targetRef="Event_16tchcb">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="OCSOfferActivationProcess" name="Subscription creation OCS via ESB" camunda:asyncBefore="true" calledElement="OCSAddSubscriptionProcess" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0cubali</bpmn:incoming>
      <bpmn:outgoing>Flow_1tdcei0</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.newSubscriptionAddonCart&#34;).elementList()}" camunda:elementVariable="executionData">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_1tdcei0" sourceRef="OCSOfferActivationProcess" targetRef="Gateway_0iksjhs" />
    <bpmn:endEvent id="Event_0g858tx">
      <bpmn:incoming>Flow_1cskt6e</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_05auzd9" default="Flow_169iz9a">
      <bpmn:incoming>Flow_0sgw2ys</bpmn:incoming>
      <bpmn:outgoing>Flow_1cskt6e</bpmn:outgoing>
      <bpmn:outgoing>Flow_169iz9a</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0kh3hd5" name="if no retainable recurring addons present" default="Flow_0kb6jyo">
      <bpmn:incoming>Flow_0419xpi</bpmn:incoming>
      <bpmn:outgoing>Flow_1xho7a3</bpmn:outgoing>
      <bpmn:outgoing>Flow_0kb6jyo</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_03hzzsb">
      <bpmn:incoming>Flow_169iz9a</bpmn:incoming>
      <bpmn:incoming>Flow_0kb6jyo</bpmn:incoming>
      <bpmn:outgoing>Flow_1wb60fr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1e5qmph">
      <bpmn:incoming>Flow_0uljf30</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1u97jwf" default="Flow_0vhj670">
      <bpmn:incoming>Flow_0fj3lu4</bpmn:incoming>
      <bpmn:outgoing>Flow_0uljf30</bpmn:outgoing>
      <bpmn:outgoing>Flow_0vhj670</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_10vhcb7" name="if non retainable recurring addons present" default="Flow_078fsaj">
      <bpmn:incoming>Flow_1wb60fr</bpmn:incoming>
      <bpmn:outgoing>Flow_08l73ou</bpmn:outgoing>
      <bpmn:outgoing>Flow_078fsaj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_19758i0">
      <bpmn:incoming>Flow_0vhj670</bpmn:incoming>
      <bpmn:incoming>Flow_078fsaj</bpmn:incoming>
      <bpmn:outgoing>Flow_13sz4p3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:callActivity id="Activity_1gvd500" name="Cancel addons in billing" camunda:asyncBefore="true" calledElement="BillingCancelSubscriptionProcess">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_08l73ou</bpmn:incoming>
      <bpmn:outgoing>Flow_0fj3lu4</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.CancelSubscriptionCart&#34;).elementList()}" camunda:elementVariable="executionData">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:callActivity id="Activity_14ynu0e" name="Cancel addons in OCS" camunda:asyncBefore="true" calledElement="OCSCancelSubscriptionProcess">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1xho7a3</bpmn:incoming>
      <bpmn:outgoing>Flow_0sgw2ys</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.CancelSubscriptionCart&#34;).elementList()}" camunda:elementVariable="executionData">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_1cskt6e" name="Failure" sourceRef="Gateway_05auzd9" targetRef="Event_0g858tx">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0sgw2ys" sourceRef="Activity_14ynu0e" targetRef="Gateway_05auzd9" />
    <bpmn:sequenceFlow id="Flow_169iz9a" name="success" sourceRef="Gateway_05auzd9" targetRef="Gateway_03hzzsb" />
    <bpmn:sequenceFlow id="Flow_1xho7a3" name="yes" sourceRef="Gateway_0kh3hd5" targetRef="Activity_14ynu0e">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.workflowData").element().hasProp('BSCancelSubscriptionReq') &amp;&amp; workflowData.jsonPath("$.workflowData.BSCancelSubscriptionReq").stringValue() == 'true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0kb6jyo" name="no" sourceRef="Gateway_0kh3hd5" targetRef="Gateway_03hzzsb" />
    <bpmn:sequenceFlow id="Flow_1wb60fr" sourceRef="Gateway_03hzzsb" targetRef="Gateway_10vhcb7" />
    <bpmn:sequenceFlow id="Flow_0uljf30" name="Failure" sourceRef="Gateway_1u97jwf" targetRef="Event_1e5qmph">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0fj3lu4" sourceRef="Activity_1gvd500" targetRef="Gateway_1u97jwf" />
    <bpmn:sequenceFlow id="Flow_0vhj670" name="success" sourceRef="Gateway_1u97jwf" targetRef="Gateway_19758i0" />
    <bpmn:sequenceFlow id="Flow_08l73ou" name="yes" sourceRef="Gateway_10vhcb7" targetRef="Activity_1gvd500">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.workflowData").element().hasProp('BSCancelSubscriptionReq') &amp;&amp; workflowData.jsonPath("$.workflowData.BSCancelSubscriptionReq").stringValue() == 'true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_078fsaj" name="no" sourceRef="Gateway_10vhcb7" targetRef="Gateway_19758i0" />
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_05z05r3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_13sz4p3" sourceRef="Gateway_19758i0" targetRef="Gateway_0zltncg" />
    <bpmn:exclusiveGateway id="Gateway_0voml6q" default="Flow_190k3iz">
      <bpmn:incoming>Flow_10mn3l6</bpmn:incoming>
      <bpmn:outgoing>Flow_0e1u60m</bpmn:outgoing>
      <bpmn:outgoing>Flow_190k3iz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0e1u60m" sourceRef="Gateway_0voml6q" targetRef="BSFetchContract">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${upcFecthReq}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1x8vu6y">
      <bpmn:incoming>Flow_190k3iz</bpmn:incoming>
      <bpmn:incoming>Flow_1erowpw</bpmn:incoming>
      <bpmn:outgoing>Flow_1u05wqw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_190k3iz" sourceRef="Gateway_0voml6q" targetRef="Gateway_1x8vu6y" />
    <bpmn:sequenceFlow id="Flow_0js6icl" sourceRef="Gateway_02szurk" targetRef="Gateway_192ye9b" />
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch Service " camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="respSpecKey">SOM_FetchServiceResponse</camunda:inputParameter>
          <camunda:inputParameter name="thirdPartyId">som-fetch-registry</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_13nt8ri</bpmn:incoming>
      <bpmn:outgoing>Flow_082v8d1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0dscxsa" default="Flow_1aqx2kh">
      <bpmn:incoming>Flow_082v8d1</bpmn:incoming>
      <bpmn:outgoing>Flow_1aqx2kh</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ipu6lj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_082v8d1" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_0dscxsa" />
    <bpmn:sequenceFlow id="Flow_1aqx2kh" sourceRef="Gateway_0dscxsa" targetRef="SOMChangeBasePlan" />
    <bpmn:endEvent id="Event_1eeui88">
      <bpmn:incoming>Flow_1ipu6lj</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1ipu6lj" sourceRef="Gateway_0dscxsa" targetRef="Event_1eeui88">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1u05wqw" sourceRef="Gateway_1x8vu6y" targetRef="BSChangeSubscription" />
    <bpmn:exclusiveGateway id="Gateway_0iksjhs">
      <bpmn:incoming>Flow_1tdcei0</bpmn:incoming>
      <bpmn:incoming>Flow_0yeoi0r</bpmn:incoming>
      <bpmn:outgoing>Flow_0419xpi</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0419xpi" sourceRef="Gateway_0iksjhs" targetRef="Gateway_0kh3hd5" />
    <bpmn:exclusiveGateway id="Gateway_1p0w8ut" name="if any new addons present in request" default="Flow_0yeoi0r">
      <bpmn:incoming>Flow_15qkglt</bpmn:incoming>
      <bpmn:outgoing>Flow_0yeoi0r</bpmn:outgoing>
      <bpmn:outgoing>Flow_0cubali</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0yeoi0r" name="No" sourceRef="Gateway_1p0w8ut" targetRef="Gateway_0iksjhs" />
    <bpmn:sequenceFlow id="Flow_0cubali" name="Yes" sourceRef="Gateway_1p0w8ut" targetRef="OCSOfferActivationProcess">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.workflowData").element().hasProp('BSAddSubscriptionReq') &amp;&amp; workflowData.jsonPath("$.workflowData.BSAddSubscriptionReq").stringValue() == 'true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_15qkglt" sourceRef="Gateway_1vbu6ux" targetRef="Gateway_1p0w8ut" />
    <bpmn:sequenceFlow id="Flow_14bp7p3" sourceRef="OrderEnrichment" targetRef="Gateway_1616nwf" />
    <bpmn:exclusiveGateway id="Gateway_1ewld6t" default="Flow_177fhdz">
      <bpmn:incoming>Flow_1jp4qu1</bpmn:incoming>
      <bpmn:outgoing>Flow_1vb92ir</bpmn:outgoing>
      <bpmn:outgoing>Flow_177fhdz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1jp4qu1" sourceRef="StartEvent_1" targetRef="Gateway_1ewld6t" />
    <bpmn:sequenceFlow id="Flow_1vb92ir" name="yes" sourceRef="Gateway_1ewld6t" targetRef="BookServiceFee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${additionalCharges}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1412apd" default="Flow_1nwebqd">
      <bpmn:incoming>Flow_1tesxqv</bpmn:incoming>
      <bpmn:outgoing>Flow_1nwebqd</bpmn:outgoing>
      <bpmn:outgoing>Flow_0v8jwxf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1tesxqv" sourceRef="BookServiceFee" targetRef="Gateway_1412apd" />
    <bpmn:exclusiveGateway id="Gateway_1uruyyi">
      <bpmn:incoming>Flow_1nwebqd</bpmn:incoming>
      <bpmn:incoming>Flow_177fhdz</bpmn:incoming>
      <bpmn:outgoing>Flow_1athhpj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1nwebqd" name="success" sourceRef="Gateway_1412apd" targetRef="Gateway_1uruyyi" />
    <bpmn:sequenceFlow id="Flow_1athhpj" sourceRef="Gateway_1uruyyi" targetRef="OrderEnrichment" />
    <bpmn:sequenceFlow id="Flow_177fhdz" name="no" sourceRef="Gateway_1ewld6t" targetRef="Gateway_1uruyyi" />
    <bpmn:endEvent id="Event_1g3wuoi">
      <bpmn:incoming>Flow_0v8jwxf</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0v8jwxf" name="Failure" sourceRef="Gateway_1412apd" targetRef="Event_1g3wuoi">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BookServiceFee" name="BookServiceFee" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1vb92ir</bpmn:incoming>
      <bpmn:outgoing>Flow_1tesxqv</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0zltncg" default="Flow_1358gjq">
      <bpmn:incoming>Flow_13sz4p3</bpmn:incoming>
      <bpmn:outgoing>Flow_111e1bv</bpmn:outgoing>
      <bpmn:outgoing>Flow_1358gjq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_111e1bv" sourceRef="Gateway_0zltncg" targetRef="ESBEkycUpdate">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order").element().hasProp('serviceManagement')&amp;&amp; workflowData.jsonPath("$.order.serviceManagement.newCart").element().hasProp('isKycRequired') &amp;&amp; workflowData.jsonPath("$.order.serviceManagement.newCart.isKycRequired").stringValue() == 'true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0f2c7bs">
      <bpmn:incoming>Flow_18otl35</bpmn:incoming>
      <bpmn:incoming>Flow_1358gjq</bpmn:incoming>
      <bpmn:outgoing>Flow_05z05r3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_05z05r3" sourceRef="Gateway_0f2c7bs" targetRef="orderExecEnd" />
    <bpmn:exclusiveGateway id="Gateway_0zqrtyh" default="Flow_18otl35">
      <bpmn:incoming>Flow_04cuq0j</bpmn:incoming>
      <bpmn:outgoing>Flow_18otl35</bpmn:outgoing>
      <bpmn:outgoing>Flow_1wknuc3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_18otl35" sourceRef="Gateway_0zqrtyh" targetRef="Gateway_0f2c7bs" />
    <bpmn:sequenceFlow id="Flow_1358gjq" sourceRef="Gateway_0zltncg" targetRef="Gateway_0f2c7bs" />
    <bpmn:endEvent id="Event_10cwhw5">
      <bpmn:incoming>Flow_1wknuc3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1wknuc3" name="Failure" sourceRef="Gateway_0zqrtyh" targetRef="Event_10cwhw5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="ESBEkycUpdate" name="ESB_EkycUpdate" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_111e1bv</bpmn:incoming>
      <bpmn:outgoing>Flow_04cuq0j</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_04cuq0j" sourceRef="ESBEkycUpdate" targetRef="Gateway_0zqrtyh" />
    <bpmn:exclusiveGateway id="Gateway_1616nwf" default="Flow_1nzpmwg">
      <bpmn:incoming>Flow_14bp7p3</bpmn:incoming>
      <bpmn:outgoing>Flow_1nzpmwg</bpmn:outgoing>
      <bpmn:outgoing>Flow_1tjwy0h</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1nzpmwg" name="success" sourceRef="Gateway_1616nwf" targetRef="PaymentWorkflow" />
    <bpmn:endEvent id="Event_0949jgz">
      <bpmn:incoming>Flow_1tjwy0h</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1tjwy0h" name="Failure" sourceRef="Gateway_1616nwf" targetRef="Event_0949jgz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_090pae1" name="SOMChangePlanCallBack" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ChangeSubscription">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="219" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1h9etri_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="1170" y="197" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0o7y2v7_di" bpmnElement="Gateway_0o7y2v7" isMarkerVisible="true">
        <dc:Bounds x="1685" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1aso3x7_di" bpmnElement="CheckEligibilityCriteria">
        <dc:Bounds x="1530" y="197" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UPCPlanFecth_di" bpmnElement="UPCPlanFetch">
        <dc:Bounds x="1990" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_18vh7yf_di" bpmnElement="Gateway_18vh7yf" isMarkerVisible="true">
        <dc:Bounds x="2155" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1wuufgs_di" bpmnElement="Event_1wuufgs">
        <dc:Bounds x="2162" y="350" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1c05mlk_di" bpmnElement="Gateway_1c05mlk" isMarkerVisible="true">
        <dc:Bounds x="2295" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ufwied_di" bpmnElement="Gateway_1ufwied" isMarkerVisible="true">
        <dc:Bounds x="1845" y="212" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1833" y="269" width="77" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_151ym8d_di" bpmnElement="Event_151ym8d">
        <dc:Bounds x="1692" y="350" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04epp91_di" bpmnElement="OCSChangePlan">
        <dc:Bounds x="6250" y="197" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1185vl8" bpmnElement="Event_1rsmcee">
        <dc:Bounds x="4212" y="344" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0g497hj" bpmnElement="Gateway_02szurk" isMarkerVisible="true">
        <dc:Bounds x="4355" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_18u4sjh" bpmnElement="Gateway_1avpoa4" isMarkerVisible="true">
        <dc:Bounds x="3875" y="212" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3862" y="269" width="80" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16axdkm" bpmnElement="Gateway_1r38i6r" isMarkerVisible="true">
        <dc:Bounds x="4205" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0aq8x3t" bpmnElement="BSCreateContract">
        <dc:Bounds x="4020" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_02d3q2a" bpmnElement="BSChangeSubscription">
        <dc:Bounds x="3540" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0omclyp_di" bpmnElement="Gateway_0omclyp" isMarkerVisible="true">
        <dc:Bounds x="3725" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ix0ov7_di" bpmnElement="Event_1ix0ov7">
        <dc:Bounds x="3732" y="344" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0kz9rjd" bpmnElement="Event_0b8e9iw">
        <dc:Bounds x="2652" y="339" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0b9tizq" bpmnElement="Gateway_1298igf" isMarkerVisible="true">
        <dc:Bounds x="2645" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0on0kh9" bpmnElement="Gateway_1eatg7u" isMarkerVisible="true">
        <dc:Bounds x="2775" y="212" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2758" y="269" width="90" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0duts58" bpmnElement="BSCancelContract">
        <dc:Bounds x="2920" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1nkso2v" bpmnElement="Gateway_1pk6fr0" isMarkerVisible="true">
        <dc:Bounds x="3105" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0i8qwpb" bpmnElement="Gateway_0mm7dxo" isMarkerVisible="true">
        <dc:Bounds x="3255" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0sji3ml" bpmnElement="Event_19g0r5p">
        <dc:Bounds x="3112" y="344" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_04r1c5c" bpmnElement="BSFetchContract">
        <dc:Bounds x="2470" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_18edxpj" bpmnElement="BSAddSubscription">
        <dc:Bounds x="4720" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0k8e9gu" bpmnElement="Event_0p68hnv">
        <dc:Bounds x="4892" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ha8kt4" bpmnElement="Gateway_13s29ze" isMarkerVisible="true">
        <dc:Bounds x="4885" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0thvlic" bpmnElement="Gateway_192ye9b" isMarkerVisible="true">
        <dc:Bounds x="4605" y="212" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4592" y="269" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0dw50ec" bpmnElement="Gateway_1r3xq2d" isMarkerVisible="true">
        <dc:Bounds x="4995" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1fvdjnm" bpmnElement="SOMChangeBasePlan">
        <dc:Bounds x="5620" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0db391r" bpmnElement="SOMChangePlanCallBack">
        <dc:Bounds x="5960" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_14svlz3" bpmnElement="Gateway_1k7vvo8" isMarkerVisible="true">
        <dc:Bounds x="5805" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1yddh2n" bpmnElement="Event_0gdnvs7">
        <dc:Bounds x="5812" y="344" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1apbmwi_di" bpmnElement="Gateway_1apbmwi" isMarkerVisible="true">
        <dc:Bounds x="6125" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_17zy133_di" bpmnElement="Event_17zy133">
        <dc:Bounds x="6132" y="339" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1vbu6ux_di" bpmnElement="Gateway_1vbu6ux" isMarkerVisible="true">
        <dc:Bounds x="6425" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_16tchcb_di" bpmnElement="Event_16tchcb">
        <dc:Bounds x="6432" y="350" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0zia7ng_di" bpmnElement="OCSOfferActivationProcess">
        <dc:Bounds x="6750" y="197" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0hh6kl9" bpmnElement="Event_0g858tx">
        <dc:Bounds x="7472" y="344" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rjh7gz" bpmnElement="Gateway_05auzd9" isMarkerVisible="true">
        <dc:Bounds x="7465" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0rzts44" bpmnElement="Gateway_0kh3hd5" isMarkerVisible="true">
        <dc:Bounds x="7155" y="212" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7148" y="276" width="84" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0hq9x6z" bpmnElement="Gateway_03hzzsb" isMarkerVisible="true">
        <dc:Bounds x="7625" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_068ah16" bpmnElement="Event_1e5qmph">
        <dc:Bounds x="8032" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0cks7ol" bpmnElement="Gateway_1u97jwf" isMarkerVisible="true">
        <dc:Bounds x="8025" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1mlj85e" bpmnElement="Gateway_10vhcb7" isMarkerVisible="true">
        <dc:Bounds x="7745" y="212" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7730" y="269" width="84" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0jc7e2x" bpmnElement="Gateway_19758i0" isMarkerVisible="true">
        <dc:Bounds x="8145" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_199or9v" bpmnElement="Activity_1gvd500">
        <dc:Bounds x="7870" y="197" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1l4f5zf" bpmnElement="Activity_14ynu0e">
        <dc:Bounds x="7290" y="197" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_08oki84_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="8782" y="219" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0voml6q_di" bpmnElement="Gateway_0voml6q" isMarkerVisible="true">
        <dc:Bounds x="2365" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1x8vu6y_di" bpmnElement="Gateway_1x8vu6y" isMarkerVisible="true">
        <dc:Bounds x="3395" y="215" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_11phy69_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="5190" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0dscxsa_di" bpmnElement="Gateway_0dscxsa" isMarkerVisible="true">
        <dc:Bounds x="5415" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1eeui88_di" bpmnElement="Event_1eeui88">
        <dc:Bounds x="5422" y="402" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0iksjhs_di" bpmnElement="Gateway_0iksjhs" isMarkerVisible="true">
        <dc:Bounds x="6995" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1p0w8ut_di" bpmnElement="Gateway_1p0w8ut" isMarkerVisible="true">
        <dc:Bounds x="6555" y="212" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6535" y="269" width="90" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ewld6t_di" bpmnElement="Gateway_1ewld6t" isMarkerVisible="true">
        <dc:Bounds x="245" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1412apd_di" bpmnElement="Gateway_1412apd" isMarkerVisible="true">
        <dc:Bounds x="525" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1uruyyi_di" bpmnElement="Gateway_1uruyyi" isMarkerVisible="true">
        <dc:Bounds x="645" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1g3wuoi_di" bpmnElement="Event_1g3wuoi">
        <dc:Bounds x="532" y="339" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0axp69f_di" bpmnElement="BookServiceFee">
        <dc:Bounds x="360" y="197" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0lwm0bp" bpmnElement="Gateway_0zltncg" isMarkerVisible="true">
        <dc:Bounds x="8255" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1l9kw7f" bpmnElement="Gateway_0f2c7bs" isMarkerVisible="true">
        <dc:Bounds x="8675" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_09njoln" bpmnElement="Gateway_0zqrtyh" isMarkerVisible="true">
        <dc:Bounds x="8565" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_10cwhw5_di" bpmnElement="Event_10cwhw5">
        <dc:Bounds x="8572" y="362" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13hmgme" bpmnElement="ESBEkycUpdate">
        <dc:Bounds x="8380" y="197" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_08s5se2" bpmnElement="Gateway_1616nwf" isMarkerVisible="true">
        <dc:Bounds x="1025" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_07tgc1f" bpmnElement="Event_0949jgz">
        <dc:Bounds x="1032" y="350" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01wyi3h_di" bpmnElement="OrderEnrichment">
        <dc:Bounds x="850" y="197" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_10kxi27_di" bpmnElement="Flow_10kxi27">
        <di:waypoint x="1270" y="237" />
        <di:waypoint x="1530" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0otz4km_di" bpmnElement="Flow_0otz4km">
        <di:waypoint x="1630" y="237" />
        <di:waypoint x="1685" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0reljoi_di" bpmnElement="Flow_0reljoi">
        <di:waypoint x="1895" y="237" />
        <di:waypoint x="1990" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1934" y="219" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13w7vn0_di" bpmnElement="Flow_13w7vn0">
        <di:waypoint x="2090" y="237" />
        <di:waypoint x="2155" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00d6k6u_di" bpmnElement="Flow_00d6k6u">
        <di:waypoint x="2180" y="262" />
        <di:waypoint x="2180" y="350" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2178" y="308" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n2s41b_di" bpmnElement="Flow_0n2s41b">
        <di:waypoint x="2205" y="237" />
        <di:waypoint x="2295" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2211" y="219" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0td4i25_di" bpmnElement="Flow_0td4i25">
        <di:waypoint x="1870" y="212" />
        <di:waypoint x="1870" y="146" />
        <di:waypoint x="2320" y="146" />
        <di:waypoint x="2320" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2089" y="128" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1doamtx_di" bpmnElement="Flow_1doamtx">
        <di:waypoint x="1735" y="237" />
        <di:waypoint x="1845" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1769" y="219" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0w1j1xw_di" bpmnElement="Flow_0w1j1xw">
        <di:waypoint x="1710" y="262" />
        <di:waypoint x="1710" y="350" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1723" y="303" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0d2ih14" bpmnElement="Flow_1re3e5l">
        <di:waypoint x="4230" y="262" />
        <di:waypoint x="4230" y="344" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4244" y="289" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1e74yvv" bpmnElement="Flow_12nmr53">
        <di:waypoint x="3900" y="212" />
        <di:waypoint x="3900" y="146" />
        <di:waypoint x="4380" y="146" />
        <di:waypoint x="4380" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4134" y="128" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_01ut2lk" bpmnElement="Flow_072rgkw">
        <di:waypoint x="4255" y="237" />
        <di:waypoint x="4355" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4279" y="219" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s5i2qo_di" bpmnElement="Flow_1s5i2qo">
        <di:waypoint x="3775" y="237" />
        <di:waypoint x="3875" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3804" y="219" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1s5qc1b" bpmnElement="Flow_1un3qui">
        <di:waypoint x="3925" y="237" />
        <di:waypoint x="4020" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3962" y="217" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1p0oxls" bpmnElement="Flow_1q7c9zp">
        <di:waypoint x="4120" y="237" />
        <di:waypoint x="4205" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vab98d_di" bpmnElement="Flow_0vab98d">
        <di:waypoint x="3640" y="237" />
        <di:waypoint x="3725" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rxp1ep_di" bpmnElement="Flow_1rxp1ep">
        <di:waypoint x="3750" y="262" />
        <di:waypoint x="3750" y="344" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3753" y="291" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10mn3l6_di" bpmnElement="Flow_10mn3l6">
        <di:waypoint x="2345" y="237" />
        <di:waypoint x="2365" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0wy3ozy" bpmnElement="Flow_0sb2ehf">
        <di:waypoint x="2670" y="262" />
        <di:waypoint x="2670" y="339" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2673" y="284" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_09497l6" bpmnElement="Flow_1l6gth8">
        <di:waypoint x="2570" y="237" />
        <di:waypoint x="2645" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_15rykzc" bpmnElement="Flow_1o48tgp">
        <di:waypoint x="2695" y="237" />
        <di:waypoint x="2775" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2706" y="215" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0lxd5ba" bpmnElement="Flow_0nl3kfc">
        <di:waypoint x="2825" y="237" />
        <di:waypoint x="2920" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2856" y="224" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1kzfbny" bpmnElement="Flow_0ugtdqz">
        <di:waypoint x="2800" y="212" />
        <di:waypoint x="2800" y="137" />
        <di:waypoint x="3280" y="137" />
        <di:waypoint x="3280" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3018" y="103" width="46" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0594tqq" bpmnElement="Flow_0mto1sa">
        <di:waypoint x="3020" y="237" />
        <di:waypoint x="3105" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1xmk022" bpmnElement="Flow_06c3zvy">
        <di:waypoint x="3155" y="237" />
        <di:waypoint x="3255" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3178" y="217" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1bd5oh6" bpmnElement="Flow_0pfgrs8">
        <di:waypoint x="3130" y="262" />
        <di:waypoint x="3130" y="344" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3143" y="291" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1erowpw_di" bpmnElement="Flow_1erowpw">
        <di:waypoint x="3305" y="237" />
        <di:waypoint x="3350" y="237" />
        <di:waypoint x="3350" y="240" />
        <di:waypoint x="3395" y="240" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1rwzlnl" bpmnElement="Flow_13nt8ri">
        <di:waypoint x="5045" y="237" />
        <di:waypoint x="5190" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0qptnri" bpmnElement="Flow_1yb85f2">
        <di:waypoint x="4655" y="237" />
        <di:waypoint x="4720" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4679" y="219" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_158ykoh" bpmnElement="Flow_1e3ozvq">
        <di:waypoint x="4820" y="237" />
        <di:waypoint x="4885" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0z3zk1w" bpmnElement="Flow_17ecrj4">
        <di:waypoint x="4910" y="262" />
        <di:waypoint x="4910" y="342" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4913" y="304" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0z8ckb5" bpmnElement="Flow_1vcsnnz">
        <di:waypoint x="4935" y="237" />
        <di:waypoint x="4995" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4929" y="219" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0tiaswj" bpmnElement="Flow_1gr0h73">
        <di:waypoint x="4630" y="212" />
        <di:waypoint x="4630" y="156" />
        <di:waypoint x="5020" y="156" />
        <di:waypoint x="5020" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4819" y="138" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0am4fii" bpmnElement="Flow_15zzj8j">
        <di:waypoint x="5720" y="237" />
        <di:waypoint x="5805" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0y43ikq" bpmnElement="Flow_1tkvjcj">
        <di:waypoint x="5855" y="237" />
        <di:waypoint x="5960" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5878" y="219" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_01x3dab" bpmnElement="Flow_0iiviln">
        <di:waypoint x="5830" y="262" />
        <di:waypoint x="5830" y="344" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5853" y="282" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u16yoq_di" bpmnElement="Flow_0u16yoq">
        <di:waypoint x="6060" y="237" />
        <di:waypoint x="6125" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fdmfbt_di" bpmnElement="Flow_0fdmfbt">
        <di:waypoint x="6175" y="237" />
        <di:waypoint x="6250" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6192" y="219" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0psbiml_di" bpmnElement="Flow_0psbiml">
        <di:waypoint x="6150" y="262" />
        <di:waypoint x="6150" y="339" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6163" y="303" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1miqqlf_di" bpmnElement="Flow_1miqqlf">
        <di:waypoint x="6350" y="237" />
        <di:waypoint x="6425" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05m0ejw_di" bpmnElement="Flow_05m0ejw">
        <di:waypoint x="6450" y="262" />
        <di:waypoint x="6450" y="350" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6463" y="308" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tdcei0_di" bpmnElement="Flow_1tdcei0">
        <di:waypoint x="6850" y="237" />
        <di:waypoint x="6995" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0guabdl" bpmnElement="Flow_1cskt6e">
        <di:waypoint x="7490" y="262" />
        <di:waypoint x="7490" y="344" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7493" y="289" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0ij4wea" bpmnElement="Flow_0sgw2ys">
        <di:waypoint x="7390" y="237" />
        <di:waypoint x="7465" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0ped8dw" bpmnElement="Flow_169iz9a">
        <di:waypoint x="7515" y="237" />
        <di:waypoint x="7625" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7549" y="223" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_02mhff7" bpmnElement="Flow_1xho7a3">
        <di:waypoint x="7205" y="237" />
        <di:waypoint x="7290" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7231" y="223" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1u2hcad" bpmnElement="Flow_0kb6jyo">
        <di:waypoint x="7180" y="212" />
        <di:waypoint x="7180" y="156" />
        <di:waypoint x="7650" y="156" />
        <di:waypoint x="7650" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7409" y="138" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1ylvqpp" bpmnElement="Flow_1wb60fr">
        <di:waypoint x="7675" y="237" />
        <di:waypoint x="7745" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1callpd" bpmnElement="Flow_0uljf30">
        <di:waypoint x="8050" y="262" />
        <di:waypoint x="8050" y="342" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8053" y="304" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0n3h7tn" bpmnElement="Flow_0fj3lu4">
        <di:waypoint x="7970" y="237" />
        <di:waypoint x="8025" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0f1wkjg" bpmnElement="Flow_0vhj670">
        <di:waypoint x="8075" y="237" />
        <di:waypoint x="8145" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8079" y="219" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_08kjxfu" bpmnElement="Flow_08l73ou">
        <di:waypoint x="7795" y="237" />
        <di:waypoint x="7870" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7806" y="219" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_08aslcb" bpmnElement="Flow_078fsaj">
        <di:waypoint x="7770" y="212" />
        <di:waypoint x="7770" y="156" />
        <di:waypoint x="8170" y="156" />
        <di:waypoint x="8170" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7964" y="138" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13sz4p3_di" bpmnElement="Flow_13sz4p3">
        <di:waypoint x="8195" y="237" />
        <di:waypoint x="8255" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e1u60m_di" bpmnElement="Flow_0e1u60m">
        <di:waypoint x="2415" y="237" />
        <di:waypoint x="2470" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_190k3iz_di" bpmnElement="Flow_190k3iz">
        <di:waypoint x="2390" y="212" />
        <di:waypoint x="2390" y="90" />
        <di:waypoint x="3420" y="80" />
        <di:waypoint x="3420" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0js6icl_di" bpmnElement="Flow_0js6icl">
        <di:waypoint x="4405" y="237" />
        <di:waypoint x="4605" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_082v8d1_di" bpmnElement="Flow_082v8d1">
        <di:waypoint x="5290" y="237" />
        <di:waypoint x="5415" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1aqx2kh_di" bpmnElement="Flow_1aqx2kh">
        <di:waypoint x="5465" y="237" />
        <di:waypoint x="5543" y="237" />
        <di:waypoint x="5543" y="235" />
        <di:waypoint x="5620" y="235" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ipu6lj_di" bpmnElement="Flow_1ipu6lj">
        <di:waypoint x="5440" y="262" />
        <di:waypoint x="5440" y="402" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1u05wqw_di" bpmnElement="Flow_1u05wqw">
        <di:waypoint x="3445" y="240" />
        <di:waypoint x="3493" y="240" />
        <di:waypoint x="3493" y="237" />
        <di:waypoint x="3540" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0419xpi_di" bpmnElement="Flow_0419xpi">
        <di:waypoint x="7045" y="237" />
        <di:waypoint x="7155" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yeoi0r_di" bpmnElement="Flow_0yeoi0r">
        <di:waypoint x="6580" y="212" />
        <di:waypoint x="6580" y="100" />
        <di:waypoint x="7020" y="100" />
        <di:waypoint x="7020" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6793" y="82" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cubali_di" bpmnElement="Flow_0cubali">
        <di:waypoint x="6604" y="238" />
        <di:waypoint x="6750" y="240" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6621" y="231" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15qkglt_di" bpmnElement="Flow_15qkglt">
        <di:waypoint x="6475" y="237" />
        <di:waypoint x="6555" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14bp7p3_di" bpmnElement="Flow_14bp7p3">
        <di:waypoint x="950" y="238" />
        <di:waypoint x="988" y="238" />
        <di:waypoint x="988" y="237" />
        <di:waypoint x="1025" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jp4qu1_di" bpmnElement="Flow_1jp4qu1">
        <di:waypoint x="188" y="237" />
        <di:waypoint x="245" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vb92ir_di" bpmnElement="Flow_1vb92ir">
        <di:waypoint x="295" y="237" />
        <di:waypoint x="360" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="319" y="219" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tesxqv_di" bpmnElement="Flow_1tesxqv">
        <di:waypoint x="460" y="237" />
        <di:waypoint x="525" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nwebqd_di" bpmnElement="Flow_1nwebqd">
        <di:waypoint x="575" y="237" />
        <di:waypoint x="645" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="590" y="219" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1athhpj_di" bpmnElement="Flow_1athhpj">
        <di:waypoint x="695" y="237" />
        <di:waypoint x="850" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_177fhdz_di" bpmnElement="Flow_177fhdz">
        <di:waypoint x="270" y="212" />
        <di:waypoint x="270" y="110" />
        <di:waypoint x="670" y="110" />
        <di:waypoint x="670" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="464" y="92" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v8jwxf_di" bpmnElement="Flow_0v8jwxf">
        <di:waypoint x="550" y="262" />
        <di:waypoint x="550" y="339" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="548" y="298" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_111e1bv_di" bpmnElement="Flow_111e1bv">
        <di:waypoint x="8305" y="237" />
        <di:waypoint x="8380" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05z05r3_di" bpmnElement="Flow_05z05r3">
        <di:waypoint x="8725" y="237" />
        <di:waypoint x="8782" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18otl35_di" bpmnElement="Flow_18otl35">
        <di:waypoint x="8615" y="237" />
        <di:waypoint x="8675" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1358gjq_di" bpmnElement="Flow_1358gjq">
        <di:waypoint x="8280" y="212" />
        <di:waypoint x="8280" y="120" />
        <di:waypoint x="8700" y="120" />
        <di:waypoint x="8700" y="212" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wknuc3_di" bpmnElement="Flow_1wknuc3">
        <di:waypoint x="8590" y="262" />
        <di:waypoint x="8590" y="362" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8588" y="309" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04cuq0j_di" bpmnElement="Flow_04cuq0j">
        <di:waypoint x="8480" y="237" />
        <di:waypoint x="8565" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nzpmwg_di" bpmnElement="Flow_1nzpmwg">
        <di:waypoint x="1075" y="237" />
        <di:waypoint x="1170" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1102" y="219" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tjwy0h_di" bpmnElement="Flow_1tjwy0h">
        <di:waypoint x="1050" y="262" />
        <di:waypoint x="1050" y="350" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1049" y="303" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
