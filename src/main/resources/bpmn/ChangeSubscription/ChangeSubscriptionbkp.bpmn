<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.6.0">
  <bpmn:process id="ChangeSubscriptionbkp1" name="ChangeSubscription" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_0wrbnu8</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0wrbnu8" sourceRef="orderExecStart" targetRef="OrderEnrichment" />
    <bpmn:exclusiveGateway id="Gateway_1tojy5a">
      <bpmn:incoming>Flow_1lxm7t2</bpmn:incoming>
      <bpmn:outgoing>Flow_1oj724n</bpmn:outgoing>
      <bpmn:outgoing>Flow_17zmf12</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0fuzl5a" default="Flow_0fo3ng0">
      <bpmn:incoming>Flow_0vvv1ml</bpmn:incoming>
      <bpmn:outgoing>Flow_1pgh3s3</bpmn:outgoing>
      <bpmn:outgoing>Flow_0fo3ng0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1fi99c8">
      <bpmn:incoming>Flow_1pgh3s3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="OrderEnrichment" name="Order Enrichment Delegate" camunda:asyncBefore="true" camunda:delegateExpression="${orderEnrichmentDelegate}">
      <bpmn:incoming>Flow_0wrbnu8</bpmn:incoming>
      <bpmn:outgoing>Flow_0vvv1ml</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0vvv1ml" sourceRef="OrderEnrichment" targetRef="Gateway_0fuzl5a" />
    <bpmn:sequenceFlow id="Flow_1pgh3s3" name="Failure" sourceRef="Gateway_0fuzl5a" targetRef="Event_1fi99c8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1kf1i1c</bpmn:incoming>
      <bpmn:incoming>Flow_1baloy6</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSBookServiceFee" name="BSBookServiceFee" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0mypey8</bpmn:incoming>
      <bpmn:outgoing>Flow_0oe2zzt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0z2j46h" default="Flow_1enajy0">
      <bpmn:incoming>Flow_0oe2zzt</bpmn:incoming>
      <bpmn:outgoing>Flow_0n11d10</bpmn:outgoing>
      <bpmn:outgoing>Flow_1enajy0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0oe2zzt" sourceRef="BSBookServiceFee" targetRef="Gateway_0z2j46h" />
    <bpmn:endEvent id="Event_1d798jb">
      <bpmn:incoming>Flow_0n11d10</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0n11d10" name="Failure" sourceRef="Gateway_0z2j46h" targetRef="Event_1d798jb">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_08e4yru">
      <bpmn:incoming>Flow_1enajy0</bpmn:incoming>
      <bpmn:incoming>Flow_0zwul2m</bpmn:incoming>
      <bpmn:outgoing>Flow_0oy5jtj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1enajy0" sourceRef="Gateway_0z2j46h" targetRef="Gateway_08e4yru" />
    <bpmn:exclusiveGateway id="Gateway_0hbenea" default="Flow_0zwul2m">
      <bpmn:incoming>Flow_0fo3ng0</bpmn:incoming>
      <bpmn:outgoing>Flow_0mypey8</bpmn:outgoing>
      <bpmn:outgoing>Flow_0zwul2m</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0fo3ng0" sourceRef="Gateway_0fuzl5a" targetRef="Gateway_0hbenea" />
    <bpmn:sequenceFlow id="Flow_0mypey8" name="paymentReceived = false" sourceRef="Gateway_0hbenea" targetRef="BSBookServiceFee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${paymentReceived}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0zwul2m" sourceRef="Gateway_0hbenea" targetRef="Gateway_08e4yru" />
    <bpmn:receiveTask id="PaymentCallback" name="Payment Callback " messageRef="Message_061isi2">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1bep805</bpmn:incoming>
      <bpmn:outgoing>Flow_04ihxlw</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_1vr41hn" default="Flow_1ojsgty">
      <bpmn:incoming>Flow_04ihxlw</bpmn:incoming>
      <bpmn:outgoing>Flow_009rhwp</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ojsgty</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0yq91sr">
      <bpmn:incoming>Flow_009rhwp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0kkoqwp">
      <bpmn:incoming>Flow_1ojsgty</bpmn:incoming>
      <bpmn:incoming>Flow_1eas86d</bpmn:incoming>
      <bpmn:outgoing>Flow_0s1f2ui</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_04ihxlw" sourceRef="PaymentCallback" targetRef="Gateway_1vr41hn" />
    <bpmn:sequenceFlow id="Flow_009rhwp" name="Failure" sourceRef="Gateway_1vr41hn" targetRef="Event_0yq91sr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ojsgty" sourceRef="Gateway_1vr41hn" targetRef="Gateway_0kkoqwp" />
    <bpmn:exclusiveGateway id="Gateway_07tyslj" default="Flow_1eas86d">
      <bpmn:incoming>Flow_0oy5jtj</bpmn:incoming>
      <bpmn:outgoing>Flow_1bep805</bpmn:outgoing>
      <bpmn:outgoing>Flow_1eas86d</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0oy5jtj" sourceRef="Gateway_08e4yru" targetRef="Gateway_07tyslj" />
    <bpmn:sequenceFlow id="Flow_1bep805" name="upfrontPayment" sourceRef="Gateway_07tyslj" targetRef="PaymentCallback">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${upfrontPayment}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1eas86d" sourceRef="Gateway_07tyslj" targetRef="Gateway_0kkoqwp" />
    <bpmn:exclusiveGateway id="Gateway_1n9rtle">
      <bpmn:incoming>Flow_0s1f2ui</bpmn:incoming>
      <bpmn:outgoing>Flow_0n7g8ye</bpmn:outgoing>
      <bpmn:outgoing>Flow_0gb684o</bpmn:outgoing>
      <bpmn:outgoing>Flow_0zgenkv</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0s1f2ui" sourceRef="Gateway_0kkoqwp" targetRef="Gateway_1n9rtle" />
    <bpmn:exclusiveGateway id="Gateway_14fz27g">
      <bpmn:incoming>Flow_0n7g8ye</bpmn:incoming>
      <bpmn:incoming>Flow_0ga3ma0</bpmn:incoming>
      <bpmn:outgoing>Flow_0oxl88t</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0n7g8ye" name="futureOrder" sourceRef="Gateway_1n9rtle" targetRef="Gateway_14fz27g">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${futureOrder}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0oxl88t" name="futureDatePresent" sourceRef="Gateway_14fz27g" targetRef="Gateway_15a79ca" />
    <bpmn:exclusiveGateway id="Gateway_15a79ca">
      <bpmn:incoming>Flow_0oxl88t</bpmn:incoming>
      <bpmn:outgoing>Flow_01oj5bc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_01oj5bc" sourceRef="Gateway_15a79ca" targetRef="futureOrderCallBack" />
    <bpmn:receiveTask id="futureOrderCallBack" name="futureOrderCallBack" messageRef="Message_043jqug">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_01oj5bc</bpmn:incoming>
      <bpmn:outgoing>Flow_1rbgqfo</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_0tzt84g">
      <bpmn:incoming>Flow_0gb684o</bpmn:incoming>
      <bpmn:incoming>Flow_0jmjatm</bpmn:incoming>
      <bpmn:outgoing>Flow_1lxm7t2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0gb684o" name="instant activation" sourceRef="Gateway_1n9rtle" targetRef="Gateway_0tzt84g">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${futureOrder}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1lxm7t2" sourceRef="Gateway_0tzt84g" targetRef="Gateway_1tojy5a" />
    <bpmn:sequenceFlow id="Flow_1oj724n" name="changing existing plan " sourceRef="Gateway_1tojy5a" targetRef="OCSChangePlan">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${newBasePlan}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="OCSChangePlan" name="OCSChangePlan" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1oj724n</bpmn:incoming>
      <bpmn:outgoing>Flow_1itd6nn</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_04q66c3" default="Flow_17hebqf">
      <bpmn:incoming>Flow_1itd6nn</bpmn:incoming>
      <bpmn:outgoing>Flow_17hebqf</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bclyrd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1itd6nn" sourceRef="OCSChangePlan" targetRef="Gateway_04q66c3" />
    <bpmn:sequenceFlow id="Flow_17hebqf" sourceRef="Gateway_04q66c3" targetRef="BS_Change_Subscription" />
    <bpmn:serviceTask id="BS_Change_Subscription" name="BSChange_Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_17hebqf</bpmn:incoming>
      <bpmn:outgoing>Flow_1w2o6mw</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1d62qbv" default="Flow_1kf1i1c">
      <bpmn:incoming>Flow_1w2o6mw</bpmn:incoming>
      <bpmn:outgoing>Flow_0fm3uzp</bpmn:outgoing>
      <bpmn:outgoing>Flow_1kf1i1c</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1w2o6mw" sourceRef="BS_Change_Subscription" targetRef="Gateway_1d62qbv" />
    <bpmn:endEvent id="Event_0jholxr">
      <bpmn:incoming>Flow_0bclyrd</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0bclyrd" name="Failure" sourceRef="Gateway_04q66c3" targetRef="Event_0jholxr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_0nr4q2x">
      <bpmn:incoming>Flow_0fm3uzp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0fm3uzp" name="Failure" sourceRef="Gateway_1d62qbv" targetRef="Event_0nr4q2x">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_17zmf12" name="adding new base plan" sourceRef="Gateway_1tojy5a" targetRef="BillingAddSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${newBasePlan}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BillingAddSubscription" name="BillingAddSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_17zmf12</bpmn:incoming>
      <bpmn:outgoing>Flow_1rwol0x</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1yse1n2" default="Flow_1yj23cc">
      <bpmn:incoming>Flow_1rwol0x</bpmn:incoming>
      <bpmn:outgoing>Flow_1yj23cc</bpmn:outgoing>
      <bpmn:outgoing>Flow_0qh6j3d</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1rwol0x" sourceRef="BillingAddSubscription" targetRef="Gateway_1yse1n2" />
    <bpmn:sequenceFlow id="Flow_1yj23cc" sourceRef="Gateway_1yse1n2" targetRef="OCS_Subscribe_Bundle" />
    <bpmn:serviceTask id="OCS_Subscribe_Bundle" name="OCS_Subscribe_Bundle" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1yj23cc</bpmn:incoming>
      <bpmn:outgoing>Flow_1igfmgx</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_12oiw0p" default="Flow_1baloy6">
      <bpmn:incoming>Flow_1igfmgx</bpmn:incoming>
      <bpmn:outgoing>Flow_0hrodiz</bpmn:outgoing>
      <bpmn:outgoing>Flow_1baloy6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1igfmgx" sourceRef="OCS_Subscribe_Bundle" targetRef="Gateway_12oiw0p" />
    <bpmn:endEvent id="Event_0f1hfb3">
      <bpmn:incoming>Flow_0qh6j3d</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0qh6j3d" name="Failure" sourceRef="Gateway_1yse1n2" targetRef="Event_0f1hfb3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1u3nybg">
      <bpmn:incoming>Flow_0hrodiz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0hrodiz" name="Failure" sourceRef="Gateway_12oiw0p" targetRef="Event_1u3nybg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0532vwy" default="Flow_0jmjatm">
      <bpmn:incoming>Flow_1rbgqfo</bpmn:incoming>
      <bpmn:outgoing>Flow_0jmjatm</bpmn:outgoing>
      <bpmn:outgoing>Flow_03jnbsc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1rbgqfo" sourceRef="futureOrderCallBack" targetRef="Gateway_0532vwy" />
    <bpmn:sequenceFlow id="Flow_0jmjatm" sourceRef="Gateway_0532vwy" targetRef="Gateway_0tzt84g" />
    <bpmn:endEvent id="Event_1unyb0w">
      <bpmn:incoming>Flow_03jnbsc</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_03jnbsc" name="Failure" sourceRef="Gateway_0532vwy" targetRef="Event_1unyb0w">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1baloy6" sourceRef="Gateway_12oiw0p" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_1kf1i1c" sourceRef="Gateway_1d62qbv" targetRef="orderExecEnd" />
    <bpmn:intermediateCatchEvent id="Event_0ypke34">
      <bpmn:incoming>Flow_0zgenkv</bpmn:incoming>
      <bpmn:outgoing>Flow_0ga3ma0</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1whon7v">
        <bpmn:timeDuration xsi:type="bpmn:tFormalExpression">${service_enable_time_delay}</bpmn:timeDuration>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_0zgenkv" sourceRef="Gateway_1n9rtle" targetRef="Event_0ypke34" />
    <bpmn:sequenceFlow id="Flow_0ga3ma0" sourceRef="Event_0ypke34" targetRef="Gateway_14fz27g" />
  </bpmn:process>
  <bpmn:message id="Message_090pae1" name="SOMChangePlanCallBack" />
  <bpmn:message id="Message_0n8jblo" name="SOMCancelPlanCallBack" />
  <bpmn:message id="Message_061isi2" name="PaymentCallback" />
  <bpmn:message id="Message_043jqug" name="futureOrderCallBack" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ChangeSubscriptionbkp1">
      <bpmndi:BPMNEdge id="Flow_0ga3ma0_di" bpmnElement="Flow_0ga3ma0">
        <di:waypoint x="2018" y="470" />
        <di:waypoint x="2130" y="470" />
        <di:waypoint x="2130" y="386" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zgenkv_di" bpmnElement="Flow_0zgenkv">
        <di:waypoint x="2000" y="386" />
        <di:waypoint x="2000" y="452" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kf1i1c_di" bpmnElement="Flow_1kf1i1c">
        <di:waypoint x="3525" y="361" />
        <di:waypoint x="3680" y="361" />
        <di:waypoint x="3680" y="270" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1baloy6_di" bpmnElement="Flow_1baloy6">
        <di:waypoint x="3525" y="120" />
        <di:waypoint x="3680" y="120" />
        <di:waypoint x="3680" y="232" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03jnbsc_di" bpmnElement="Flow_03jnbsc">
        <di:waypoint x="2570" y="386" />
        <di:waypoint x="2570" y="475" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2568" y="428" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jmjatm_di" bpmnElement="Flow_0jmjatm">
        <di:waypoint x="2595" y="361" />
        <di:waypoint x="2675" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rbgqfo_di" bpmnElement="Flow_1rbgqfo">
        <di:waypoint x="2470" y="361" />
        <di:waypoint x="2545" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hrodiz_di" bpmnElement="Flow_0hrodiz">
        <di:waypoint x="3500" y="145" />
        <di:waypoint x="3500" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3498" y="176" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qh6j3d_di" bpmnElement="Flow_0qh6j3d">
        <di:waypoint x="3140" y="145" />
        <di:waypoint x="3140" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3138" y="176" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1igfmgx_di" bpmnElement="Flow_1igfmgx">
        <di:waypoint x="3370" y="120" />
        <di:waypoint x="3475" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yj23cc_di" bpmnElement="Flow_1yj23cc">
        <di:waypoint x="3165" y="120" />
        <di:waypoint x="3270" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rwol0x_di" bpmnElement="Flow_1rwol0x">
        <di:waypoint x="3010" y="120" />
        <di:waypoint x="3115" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17zmf12_di" bpmnElement="Flow_17zmf12">
        <di:waypoint x="2810" y="336" />
        <di:waypoint x="2810" y="120" />
        <di:waypoint x="2910" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2798" y="86" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fm3uzp_di" bpmnElement="Flow_0fm3uzp">
        <di:waypoint x="3500" y="386" />
        <di:waypoint x="3500" y="475" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3498" y="428" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bclyrd_di" bpmnElement="Flow_0bclyrd">
        <di:waypoint x="3140" y="386" />
        <di:waypoint x="3140" y="475" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3138" y="428" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w2o6mw_di" bpmnElement="Flow_1w2o6mw">
        <di:waypoint x="3370" y="361" />
        <di:waypoint x="3475" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17hebqf_di" bpmnElement="Flow_17hebqf">
        <di:waypoint x="3165" y="361" />
        <di:waypoint x="3270" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1itd6nn_di" bpmnElement="Flow_1itd6nn">
        <di:waypoint x="3050" y="361" />
        <di:waypoint x="3115" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oj724n_di" bpmnElement="Flow_1oj724n">
        <di:waypoint x="2835" y="361" />
        <di:waypoint x="2950" y="361" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2850" y="326" width="86" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lxm7t2_di" bpmnElement="Flow_1lxm7t2">
        <di:waypoint x="2725" y="361" />
        <di:waypoint x="2785" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gb684o_di" bpmnElement="Flow_0gb684o">
        <di:waypoint x="2000" y="336" />
        <di:waypoint x="2000" y="250" />
        <di:waypoint x="2700" y="250" />
        <di:waypoint x="2700" y="336" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2309" y="232" width="82" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01oj5bc_di" bpmnElement="Flow_01oj5bc">
        <di:waypoint x="2315" y="361" />
        <di:waypoint x="2370" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oxl88t_di" bpmnElement="Flow_0oxl88t">
        <di:waypoint x="2155" y="361" />
        <di:waypoint x="2265" y="361" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2166" y="343" width="89" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n7g8ye_di" bpmnElement="Flow_0n7g8ye">
        <di:waypoint x="2025" y="361" />
        <di:waypoint x="2105" y="361" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2037" y="343" width="57" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0s1f2ui_di" bpmnElement="Flow_0s1f2ui">
        <di:waypoint x="1855" y="361" />
        <di:waypoint x="1975" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eas86d_di" bpmnElement="Flow_1eas86d">
        <di:waypoint x="1260" y="336" />
        <di:waypoint x="1260" y="250" />
        <di:waypoint x="1830" y="250" />
        <di:waypoint x="1830" y="336" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bep805_di" bpmnElement="Flow_1bep805">
        <di:waypoint x="1285" y="361" />
        <di:waypoint x="1420" y="361" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1314" y="343" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oy5jtj_di" bpmnElement="Flow_0oy5jtj">
        <di:waypoint x="1135" y="361" />
        <di:waypoint x="1235" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ojsgty_di" bpmnElement="Flow_1ojsgty">
        <di:waypoint x="1685" y="361" />
        <di:waypoint x="1805" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_009rhwp_di" bpmnElement="Flow_009rhwp">
        <di:waypoint x="1660" y="386" />
        <di:waypoint x="1660" y="463" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1659" y="420" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04ihxlw_di" bpmnElement="Flow_04ihxlw">
        <di:waypoint x="1520" y="361" />
        <di:waypoint x="1635" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zwul2m_di" bpmnElement="Flow_0zwul2m">
        <di:waypoint x="640" y="336" />
        <di:waypoint x="640" y="250" />
        <di:waypoint x="1110" y="250" />
        <di:waypoint x="1110" y="336" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mypey8_di" bpmnElement="Flow_0mypey8">
        <di:waypoint x="665" y="361" />
        <di:waypoint x="780" y="361" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="678" y="326" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fo3ng0_di" bpmnElement="Flow_0fo3ng0">
        <di:waypoint x="515" y="361" />
        <di:waypoint x="615" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1enajy0_di" bpmnElement="Flow_1enajy0">
        <di:waypoint x="985" y="361" />
        <di:waypoint x="1085" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n11d10_di" bpmnElement="Flow_0n11d10">
        <di:waypoint x="960" y="386" />
        <di:waypoint x="960" y="475" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="958" y="428" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oe2zzt_di" bpmnElement="Flow_0oe2zzt">
        <di:waypoint x="880" y="361" />
        <di:waypoint x="935" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pgh3s3_di" bpmnElement="Flow_1pgh3s3">
        <di:waypoint x="490" y="386" />
        <di:waypoint x="490" y="475" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="494" y="420" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vvv1ml_di" bpmnElement="Flow_0vvv1ml">
        <di:waypoint x="370" y="361" />
        <di:waypoint x="465" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wrbnu8_di" bpmnElement="Flow_0wrbnu8">
        <di:waypoint x="188" y="361" />
        <di:waypoint x="270" y="361" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_0rygtwa_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="343" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tojy5a_di" bpmnElement="Gateway_1tojy5a" isMarkerVisible="true">
        <dc:Bounds x="2785" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fuzl5a_di" bpmnElement="Gateway_0fuzl5a" isMarkerVisible="true">
        <dc:Bounds x="465" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1fi99c8_di" bpmnElement="Event_1fi99c8">
        <dc:Bounds x="472" y="475" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01wyi3h_di" bpmnElement="OrderEnrichment">
        <dc:Bounds x="270" y="321" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1w7n1yi_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="3662" y="232" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="10986" y="409" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0rlcq3p_di" bpmnElement="BSBookServiceFee">
        <dc:Bounds x="780" y="321" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0z2j46h_di" bpmnElement="Gateway_0z2j46h" isMarkerVisible="true">
        <dc:Bounds x="935" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1d798jb_di" bpmnElement="Event_1d798jb">
        <dc:Bounds x="942" y="475" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_08e4yru_di" bpmnElement="Gateway_08e4yru" isMarkerVisible="true">
        <dc:Bounds x="1085" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0hbenea_di" bpmnElement="Gateway_0hbenea" isMarkerVisible="true">
        <dc:Bounds x="615" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_03l5qgl_di" bpmnElement="PaymentCallback">
        <dc:Bounds x="1420" y="321" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1vr41hn_di" bpmnElement="Gateway_1vr41hn" isMarkerVisible="true">
        <dc:Bounds x="1635" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0yq91sr_di" bpmnElement="Event_0yq91sr">
        <dc:Bounds x="1642" y="463" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0kkoqwp_di" bpmnElement="Gateway_0kkoqwp" isMarkerVisible="true">
        <dc:Bounds x="1805" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_07tyslj_di" bpmnElement="Gateway_07tyslj" isMarkerVisible="true">
        <dc:Bounds x="1235" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1n9rtle_di" bpmnElement="Gateway_1n9rtle" isMarkerVisible="true">
        <dc:Bounds x="1975" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_14fz27g_di" bpmnElement="Gateway_14fz27g" isMarkerVisible="true">
        <dc:Bounds x="2105" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_15a79ca_di" bpmnElement="Gateway_15a79ca" isMarkerVisible="true">
        <dc:Bounds x="2265" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_04krpfy_di" bpmnElement="futureOrderCallBack">
        <dc:Bounds x="2370" y="321" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0tzt84g_di" bpmnElement="Gateway_0tzt84g" isMarkerVisible="true">
        <dc:Bounds x="2675" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_19hso7q_di" bpmnElement="OCSChangePlan">
        <dc:Bounds x="2950" y="321" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_04q66c3_di" bpmnElement="Gateway_04q66c3" isMarkerVisible="true">
        <dc:Bounds x="3115" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1lfszkg_di" bpmnElement="BS_Change_Subscription">
        <dc:Bounds x="3270" y="321" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1d62qbv_di" bpmnElement="Gateway_1d62qbv" isMarkerVisible="true">
        <dc:Bounds x="3475" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0jholxr_di" bpmnElement="Event_0jholxr">
        <dc:Bounds x="3122" y="475" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0nr4q2x_di" bpmnElement="Event_0nr4q2x">
        <dc:Bounds x="3482" y="475" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1vodx70_di" bpmnElement="BillingAddSubscription">
        <dc:Bounds x="2910" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1yse1n2_di" bpmnElement="Gateway_1yse1n2" isMarkerVisible="true">
        <dc:Bounds x="3115" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1fm9f8p_di" bpmnElement="OCS_Subscribe_Bundle">
        <dc:Bounds x="3270" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_12oiw0p_di" bpmnElement="Gateway_12oiw0p" isMarkerVisible="true">
        <dc:Bounds x="3475" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0f1hfb3_di" bpmnElement="Event_0f1hfb3">
        <dc:Bounds x="3122" y="212" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1u3nybg_di" bpmnElement="Event_1u3nybg">
        <dc:Bounds x="3482" y="212" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0532vwy_di" bpmnElement="Gateway_0532vwy" isMarkerVisible="true">
        <dc:Bounds x="2545" y="336" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1unyb0w_di" bpmnElement="Event_1unyb0w">
        <dc:Bounds x="2552" y="475" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ypke34_di" bpmnElement="Event_0ypke34">
        <dc:Bounds x="1982" y="452" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
