<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.23.0">
  <bpmn:process id="CreateProfileAccount" name="CreateProfileAccount" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0ywwsm3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_06qssrb</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0ywwsm3" sourceRef="orderExecStart" targetRef="Gateway_0fju902" />
    <bpmn:serviceTask id="BSCreateAccount" name="Create Account in Billing System" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1xqx5ws</bpmn:incoming>
      <bpmn:outgoing>Flow_1w7l6qk</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1w7l6qk" sourceRef="BSCreateAccount" targetRef="Gateway_1de5rus" />
    <bpmn:endEvent id="Event_1ghzk6c" name="failure">
      <bpmn:incoming>Flow_1fjd0n1</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1fjd0n1" name="Failure" sourceRef="Gateway_0swy6z2" targetRef="Event_1ghzk6c">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSCreateProfile" name="Create Customer Profile In Party Management" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_16w0vsq</bpmn:incoming>
      <bpmn:outgoing>Flow_1uydk5s</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1uydk5s" sourceRef="BSCreateProfile" targetRef="Gateway_0swy6z2" />
    <bpmn:exclusiveGateway id="Gateway_0swy6z2" default="Flow_1xqx5ws">
      <bpmn:incoming>Flow_1uydk5s</bpmn:incoming>
      <bpmn:outgoing>Flow_1xqx5ws</bpmn:outgoing>
      <bpmn:outgoing>Flow_1fjd0n1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1xqx5ws" name="success" sourceRef="Gateway_0swy6z2" targetRef="BSCreateAccount" />
    <bpmn:serviceTask id="ESB_CreateAccount" name="Account Creation in OCS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="thirdPartyId">ocs-account-creation</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1085bvu</bpmn:incoming>
      <bpmn:outgoing>Flow_06qssrb</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_06qssrb" sourceRef="ESB_CreateAccount" targetRef="orderExecEnd" />
    <bpmn:exclusiveGateway id="Gateway_1de5rus" default="Flow_0smb4ll">
      <bpmn:incoming>Flow_1w7l6qk</bpmn:incoming>
      <bpmn:outgoing>Flow_0smb4ll</bpmn:outgoing>
      <bpmn:outgoing>Flow_0n6k871</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0smb4ll" name="Success" sourceRef="Gateway_1de5rus" targetRef="Gateway_0qknwjc" />
    <bpmn:endEvent id="Event_01hi0u1">
      <bpmn:incoming>Flow_0n6k871</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0n6k871" name="Failure" sourceRef="Gateway_1de5rus" targetRef="Event_01hi0u1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0fju902" name="is profile and account created in enrichment" default="Flow_16w0vsq">
      <bpmn:incoming>Flow_0ywwsm3</bpmn:incoming>
      <bpmn:outgoing>Flow_16w0vsq</bpmn:outgoing>
      <bpmn:outgoing>Flow_01tkh4b</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_16w0vsq" sourceRef="Gateway_0fju902" targetRef="BSCreateProfile" />
    <bpmn:exclusiveGateway id="Gateway_0qknwjc">
      <bpmn:incoming>Flow_0smb4ll</bpmn:incoming>
      <bpmn:incoming>Flow_01tkh4b</bpmn:incoming>
      <bpmn:outgoing>Flow_1085bvu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1085bvu" sourceRef="Gateway_0qknwjc" targetRef="ESB_CreateAccount" />
    <bpmn:sequenceFlow id="Flow_01tkh4b" sourceRef="Gateway_0fju902" targetRef="Gateway_0qknwjc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isLegacy}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CreateProfileAccount">
      <bpmndi:BPMNShape id="Event_1x7dsnm_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="172" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1p88c2w_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="1382" y="172" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0o3s4qz_di" bpmnElement="BSCreateAccount">
        <dc:Bounds x="680" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ghzk6c_di" bpmnElement="Event_1ghzk6c">
        <dc:Bounds x="582" y="312" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="586" y="355" width="29" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1madche_di" bpmnElement="BSCreateProfile">
        <dc:Bounds x="400" y="150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0swy6z2_di" bpmnElement="Gateway_0swy6z2" isMarkerVisible="true">
        <dc:Bounds x="575" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0thv5ay_di" bpmnElement="ESB_CreateAccount">
        <dc:Bounds x="1150" y="150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1de5rus_di" bpmnElement="Gateway_1de5rus" isMarkerVisible="true">
        <dc:Bounds x="845" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_01hi0u1_di" bpmnElement="Event_01hi0u1">
        <dc:Bounds x="852" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fju902_di" bpmnElement="Gateway_0fju902" isMarkerVisible="true">
        <dc:Bounds x="275" y="165" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="261" y="222" width="79" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0qknwjc_di" bpmnElement="Gateway_0qknwjc" isMarkerVisible="true">
        <dc:Bounds x="975" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0ywwsm3_di" bpmnElement="Flow_0ywwsm3">
        <di:waypoint x="188" y="190" />
        <di:waypoint x="275" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w7l6qk_di" bpmnElement="Flow_1w7l6qk">
        <di:waypoint x="780" y="190" />
        <di:waypoint x="845" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fjd0n1_di" bpmnElement="Flow_1fjd0n1">
        <di:waypoint x="600" y="215" />
        <di:waypoint x="600" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="613" y="261" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uydk5s_di" bpmnElement="Flow_1uydk5s">
        <di:waypoint x="500" y="190" />
        <di:waypoint x="575" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xqx5ws_di" bpmnElement="Flow_1xqx5ws">
        <di:waypoint x="625" y="190" />
        <di:waypoint x="680" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="619" y="172" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06qssrb_di" bpmnElement="Flow_06qssrb">
        <di:waypoint x="1250" y="190" />
        <di:waypoint x="1382" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0smb4ll_di" bpmnElement="Flow_0smb4ll">
        <di:waypoint x="895" y="190" />
        <di:waypoint x="975" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="906" y="153" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n6k871_di" bpmnElement="Flow_0n6k871">
        <di:waypoint x="870" y="215" />
        <di:waypoint x="870" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="883" y="256" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16w0vsq_di" bpmnElement="Flow_16w0vsq">
        <di:waypoint x="325" y="190" />
        <di:waypoint x="400" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1085bvu_di" bpmnElement="Flow_1085bvu">
        <di:waypoint x="1025" y="190" />
        <di:waypoint x="1150" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01tkh4b_di" bpmnElement="Flow_01tkh4b">
        <di:waypoint x="300" y="165" />
        <di:waypoint x="300" y="80" />
        <di:waypoint x="1000" y="80" />
        <di:waypoint x="1000" y="165" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
