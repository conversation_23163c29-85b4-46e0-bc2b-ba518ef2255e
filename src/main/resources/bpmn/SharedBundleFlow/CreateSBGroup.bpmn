<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.23.0">
  <bpmn:process id="CreateSBGroup" name="CreateSBGroup" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1i15aj7</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="OCS_AddMembersToGroup" name="OCS Add Members To Group" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_12x5ofv</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1y9mbbm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0si27dg">
      <bpmn:incoming>SequenceFlow_1u85r7e</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_12cu8ej</bpmn:outgoing>
      <bpmn:outgoing>Flow_14t1duw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="EndEvent_16lnasa">
      <bpmn:incoming>SequenceFlow_12cu8ej</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSCreateSBGroup" name="BS Create SB Group" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_08rjcex</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1u85r7e</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1u85r7e" sourceRef="BSCreateSBGroup" targetRef="ExclusiveGateway_0si27dg" />
    <bpmn:sequenceFlow id="SequenceFlow_12cu8ej" name="Failure" sourceRef="ExclusiveGateway_0si27dg" targetRef="EndEvent_16lnasa">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>SequenceFlow_0kvt55s</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_1y9mbbm" sourceRef="OCS_AddMembersToGroup" targetRef="ExclusiveGateway_1nt34ww" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_1uss5by" name="isBeneficiary present" default="SequenceFlow_14w7sbt">
      <bpmn:incoming>Flow_0bhw1j9</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_12x5ofv</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_14w7sbt</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_12x5ofv" name="yes" sourceRef="ExclusiveGateway_1uss5by" targetRef="OCS_AddMembersToGroup">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isBeneficiaryPresent}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1nt34ww">
      <bpmn:incoming>SequenceFlow_1y9mbbm</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_14w7sbt</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0kvt55s</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0kvt55s" sourceRef="ExclusiveGateway_1nt34ww" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="SequenceFlow_14w7sbt" name="no" sourceRef="ExclusiveGateway_1uss5by" targetRef="ExclusiveGateway_1nt34ww" />
    <bpmn:serviceTask id="OCS_CreateGroup" name="OCS Create Group" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0a7pdlp</bpmn:incoming>
      <bpmn:outgoing>Flow_018cmn8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_018cmn8" sourceRef="OCS_CreateGroup" targetRef="Gateway_1g33iyl" />
    <bpmn:serviceTask id="OCS_AddSubscription" name="OCS AddSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1et4y38</bpmn:incoming>
      <bpmn:outgoing>Flow_066aqrw</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_066aqrw" sourceRef="OCS_AddSubscription" targetRef="Gateway_0qb5wfp" />
    <bpmn:exclusiveGateway id="Gateway_1g33iyl">
      <bpmn:incoming>Flow_018cmn8</bpmn:incoming>
      <bpmn:outgoing>Flow_10icady</bpmn:outgoing>
      <bpmn:outgoing>Flow_1b9ekng</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_10icady" name="Success" sourceRef="Gateway_1g33iyl" targetRef="Gateway_0uvbt2u">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0123kri">
      <bpmn:incoming>Flow_014xtrn</bpmn:incoming>
      <bpmn:outgoing>Flow_0bhw1j9</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ad0trn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0bhw1j9" name="Success" sourceRef="Gateway_0123kri" targetRef="ExclusiveGateway_1uss5by">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_12mt2ql">
      <bpmn:incoming>Flow_1b9ekng</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_1xkemir">
      <bpmn:incoming>Flow_0ad0trn</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1b9ekng" name="Failure" sourceRef="Gateway_1g33iyl" targetRef="Event_12mt2ql">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ad0trn" name="Failure" sourceRef="Gateway_0123kri" targetRef="Event_1xkemir">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1i15aj7" sourceRef="orderExecStart" targetRef="Gateway_1lkujxs" />
    <bpmn:serviceTask id="BS_AddSubscription" name="BS_AddSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0m8ovvv</bpmn:incoming>
      <bpmn:outgoing>Flow_1qmp82p</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1qmp82p" sourceRef="BS_AddSubscription" targetRef="Gateway_0qtxg0r" />
    <bpmn:exclusiveGateway id="Gateway_1v2tpjg">
      <bpmn:incoming>Flow_1ye1ri9</bpmn:incoming>
      <bpmn:incoming>Flow_0f5gm11</bpmn:incoming>
      <bpmn:outgoing>Flow_0a7pdlp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0a7pdlp" sourceRef="Gateway_1v2tpjg" targetRef="OCS_CreateGroup" />
    <bpmn:exclusiveGateway id="Gateway_0xqyrx8" name="Subesription Present" default="Flow_1ye1ri9">
      <bpmn:incoming>Flow_0txqn5n</bpmn:incoming>
      <bpmn:outgoing>Flow_0m8ovvv</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ye1ri9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0m8ovvv" name="true" sourceRef="Gateway_0xqyrx8" targetRef="BS_AddSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order.serviceManagement").element().hasProp('subscriptions')}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ye1ri9" sourceRef="Gateway_0xqyrx8" targetRef="Gateway_1v2tpjg" />
    <bpmn:exclusiveGateway id="Gateway_0uvbt2u" name="Check if subscriptions is there OR not" default="Flow_13ww037">
      <bpmn:incoming>Flow_10icady</bpmn:incoming>
      <bpmn:outgoing>Flow_1et4y38</bpmn:outgoing>
      <bpmn:outgoing>Flow_13ww037</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1et4y38" name="true" sourceRef="Gateway_0uvbt2u" targetRef="OCS_AddSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order.serviceManagement").element().hasProp('subscriptions')}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0qtxg0r">
      <bpmn:incoming>Flow_1qmp82p</bpmn:incoming>
      <bpmn:outgoing>Flow_0f5gm11</bpmn:outgoing>
      <bpmn:outgoing>Flow_0o0p26g</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0f5gm11" name="Success" sourceRef="Gateway_0qtxg0r" targetRef="Gateway_1v2tpjg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0qb5wfp">
      <bpmn:incoming>Flow_066aqrw</bpmn:incoming>
      <bpmn:outgoing>Flow_1w84iea</bpmn:outgoing>
      <bpmn:outgoing>Flow_1v6252a</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1w84iea" name="Success" sourceRef="Gateway_0qb5wfp" targetRef="Gateway_1rlrb7q">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1rlrb7q">
      <bpmn:incoming>Flow_1w84iea</bpmn:incoming>
      <bpmn:incoming>Flow_13ww037</bpmn:incoming>
      <bpmn:outgoing>Flow_014xtrn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_014xtrn" sourceRef="Gateway_1rlrb7q" targetRef="Gateway_0123kri" />
    <bpmn:sequenceFlow id="Flow_13ww037" sourceRef="Gateway_0uvbt2u" targetRef="Gateway_1rlrb7q" />
    <bpmn:endEvent id="Event_08r1lf9">
      <bpmn:incoming>Flow_1v6252a</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1v6252a" name="Failure" sourceRef="Gateway_0qb5wfp" targetRef="Event_08r1lf9">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1rsqjs7">
      <bpmn:incoming>Flow_0o0p26g</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0o0p26g" name="Failure" sourceRef="Gateway_0qtxg0r" targetRef="Event_1rsqjs7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="OrderEnrichments" name="Enrich plan details" camunda:asyncBefore="true" camunda:delegateExpression="${orderEnrichmentDelegate}">
      <bpmn:incoming>Flow_1kzysac</bpmn:incoming>
      <bpmn:outgoing>Flow_0n7zqnp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0n7zqnp" sourceRef="OrderEnrichments" targetRef="Gateway_0yh0mt2" />
    <bpmn:exclusiveGateway id="Gateway_1lkujxs" name="Check if subscriptions is there OR not" default="Flow_1fl7k5z">
      <bpmn:incoming>Flow_1i15aj7</bpmn:incoming>
      <bpmn:outgoing>Flow_1kzysac</bpmn:outgoing>
      <bpmn:outgoing>Flow_1fl7k5z</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1kzysac" sourceRef="Gateway_1lkujxs" targetRef="OrderEnrichments">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsSubscriptionPresent}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0yh0mt2">
      <bpmn:incoming>Flow_0n7zqnp</bpmn:incoming>
      <bpmn:outgoing>Flow_1u6g5jo</bpmn:outgoing>
      <bpmn:outgoing>Flow_1m0515g</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1u6g5jo" sourceRef="Gateway_0yh0mt2" targetRef="Gateway_1759bab">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1759bab">
      <bpmn:incoming>Flow_1u6g5jo</bpmn:incoming>
      <bpmn:incoming>Flow_1fl7k5z</bpmn:incoming>
      <bpmn:outgoing>Flow_0y2fywx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1fl7k5z" sourceRef="Gateway_1lkujxs" targetRef="Gateway_1759bab" />
    <bpmn:endEvent id="Event_0l08lay">
      <bpmn:incoming>Flow_1m0515g</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1m0515g" name="Failure" sourceRef="Gateway_0yh0mt2" targetRef="Event_0l08lay">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1czt4jg" default="Flow_08rjcex">
      <bpmn:incoming>Flow_0y2fywx</bpmn:incoming>
      <bpmn:outgoing>Flow_08rjcex</bpmn:outgoing>
      <bpmn:outgoing>Flow_1l3mab8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0y2fywx" sourceRef="Gateway_1759bab" targetRef="Gateway_1czt4jg" />
    <bpmn:sequenceFlow id="Flow_08rjcex" sourceRef="Gateway_1czt4jg" targetRef="BSCreateSBGroup" />
    <bpmn:exclusiveGateway id="Gateway_0bz0bvo">
      <bpmn:incoming>Flow_14t1duw</bpmn:incoming>
      <bpmn:incoming>Flow_1l3mab8</bpmn:incoming>
      <bpmn:outgoing>Flow_0txqn5n</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_14t1duw" sourceRef="ExclusiveGateway_0si27dg" targetRef="Gateway_0bz0bvo">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0txqn5n" sourceRef="Gateway_0bz0bvo" targetRef="Gateway_0xqyrx8" />
    <bpmn:sequenceFlow id="Flow_1l3mab8" sourceRef="Gateway_1czt4jg" targetRef="Gateway_0bz0bvo">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isLegacy}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CreateSBGroup">
      <bpmndi:BPMNShape id="ServiceTask_007udrl_di" bpmnElement="OCS_AddMembersToGroup">
        <dc:Bounds x="2870" y="181" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1bo8nna_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="3182" y="203" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1uss5by_di" bpmnElement="ExclusiveGateway_1uss5by" isMarkerVisible="true">
        <dc:Bounds x="2755" y="196" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2751" y="253" width="63" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1nt34ww_di" bpmnElement="ExclusiveGateway_1nt34ww" isMarkerVisible="true">
        <dc:Bounds x="3045" y="196" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_03w0von" bpmnElement="OCS_CreateGroup">
        <dc:Bounds x="1780" y="181" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_006ssh3" bpmnElement="OCS_AddSubscription">
        <dc:Bounds x="2210" y="181" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1a7h86n" bpmnElement="Gateway_1g33iyl" isMarkerVisible="true">
        <dc:Bounds x="1945" y="196" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1i69rts" bpmnElement="Gateway_0123kri" isMarkerVisible="true">
        <dc:Bounds x="2615" y="196" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_00oa1q0" bpmnElement="Event_12mt2ql">
        <dc:Bounds x="1952" y="332" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_17b9wa9" bpmnElement="Event_1xkemir">
        <dc:Bounds x="2622" y="322" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_09zs9fl" bpmnElement="BS_AddSubscription">
        <dc:Bounds x="1370" y="181" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1cmczt2" bpmnElement="Gateway_1v2tpjg" isMarkerVisible="true">
        <dc:Bounds x="1645" y="196" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0xlqavw" bpmnElement="Gateway_0xqyrx8" isMarkerVisible="true">
        <dc:Bounds x="1235" y="196" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1231" y="253" width="62" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1u8am4o" bpmnElement="Gateway_0uvbt2u" isMarkerVisible="true">
        <dc:Bounds x="2085" y="196" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2072" y="253" width="76" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0b0dku5" bpmnElement="Gateway_0qtxg0r" isMarkerVisible="true">
        <dc:Bounds x="1535" y="196" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0sj421a" bpmnElement="Gateway_0qb5wfp" isMarkerVisible="true">
        <dc:Bounds x="2385" y="196" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0m3yq72" bpmnElement="Gateway_1rlrb7q" isMarkerVisible="true">
        <dc:Bounds x="2495" y="196" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0iz33ln" bpmnElement="Event_08r1lf9">
        <dc:Bounds x="2392" y="332" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1xmt4xz" bpmnElement="Event_1rsqjs7">
        <dc:Bounds x="1542" y="332" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1x7dsnm_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="203" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0rv3juu" bpmnElement="Gateway_1lkujxs" isMarkerVisible="true">
        <dc:Bounds x="235" y="196" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="222" y="253" width="76" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13p21z4" bpmnElement="OrderEnrichments">
        <dc:Bounds x="380" y="181" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0nwzeum" bpmnElement="Gateway_0yh0mt2" isMarkerVisible="true">
        <dc:Bounds x="525" y="196" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1927ya1" bpmnElement="Gateway_1759bab" isMarkerVisible="true">
        <dc:Bounds x="635" y="196" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0noc7sy" bpmnElement="Event_0l08lay">
        <dc:Bounds x="532" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1czt4jg_di" bpmnElement="Gateway_1czt4jg" isMarkerVisible="true">
        <dc:Bounds x="745" y="196" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0oic3i3_di" bpmnElement="BSCreateSBGroup">
        <dc:Bounds x="870" y="181" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0si27dg_di" bpmnElement="ExclusiveGateway_0si27dg" isMarkerVisible="true">
        <dc:Bounds x="1005" y="196" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_16lnasa_di" bpmnElement="EndEvent_16lnasa">
        <dc:Bounds x="1012" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0bz0bvo_di" bpmnElement="Gateway_0bz0bvo" isMarkerVisible="true">
        <dc:Bounds x="1105" y="196" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1u85r7e_di" bpmnElement="SequenceFlow_1u85r7e">
        <di:waypoint x="970" y="221" />
        <di:waypoint x="1005" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_12cu8ej_di" bpmnElement="SequenceFlow_12cu8ej">
        <di:waypoint x="1030" y="246" />
        <di:waypoint x="1030" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1039" y="273" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1y9mbbm_di" bpmnElement="SequenceFlow_1y9mbbm">
        <di:waypoint x="2970" y="221" />
        <di:waypoint x="3045" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_12x5ofv_di" bpmnElement="SequenceFlow_12x5ofv">
        <di:waypoint x="2805" y="221" />
        <di:waypoint x="2870" y="221" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2849" y="193" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0kvt55s_di" bpmnElement="SequenceFlow_0kvt55s">
        <di:waypoint x="3095" y="221" />
        <di:waypoint x="3182" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_14w7sbt_di" bpmnElement="SequenceFlow_14w7sbt">
        <di:waypoint x="2780" y="196" />
        <di:waypoint x="2780" y="120" />
        <di:waypoint x="3070" y="120" />
        <di:waypoint x="3070" y="196" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2919" y="102" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_018cmn8_di" bpmnElement="Flow_018cmn8">
        <di:waypoint x="1880" y="221" />
        <di:waypoint x="1945" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_066aqrw_di" bpmnElement="Flow_066aqrw">
        <di:waypoint x="2310" y="221" />
        <di:waypoint x="2385" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10icady_di" bpmnElement="Flow_10icady">
        <di:waypoint x="1995" y="221" />
        <di:waypoint x="2085" y="221" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1997" y="203" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bhw1j9_di" bpmnElement="Flow_0bhw1j9">
        <di:waypoint x="2665" y="221" />
        <di:waypoint x="2755" y="221" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2712" y="193" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b9ekng_di" bpmnElement="Flow_1b9ekng">
        <di:waypoint x="1970" y="246" />
        <di:waypoint x="1970" y="332" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1968" y="286" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ad0trn_di" bpmnElement="Flow_0ad0trn">
        <di:waypoint x="2640" y="246" />
        <di:waypoint x="2640" y="322" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2639" y="280" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1i15aj7_di" bpmnElement="Flow_1i15aj7">
        <di:waypoint x="188" y="221" />
        <di:waypoint x="235" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qmp82p_di" bpmnElement="Flow_1qmp82p">
        <di:waypoint x="1470" y="221" />
        <di:waypoint x="1535" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a7pdlp_di" bpmnElement="Flow_0a7pdlp">
        <di:waypoint x="1695" y="221" />
        <di:waypoint x="1780" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0m8ovvv_di" bpmnElement="Flow_0m8ovvv">
        <di:waypoint x="1285" y="221" />
        <di:waypoint x="1370" y="221" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1319" y="203" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ye1ri9_di" bpmnElement="Flow_1ye1ri9">
        <di:waypoint x="1260" y="196" />
        <di:waypoint x="1260" y="130" />
        <di:waypoint x="1670" y="130" />
        <di:waypoint x="1670" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1et4y38_di" bpmnElement="Flow_1et4y38">
        <di:waypoint x="2135" y="221" />
        <di:waypoint x="2210" y="221" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2163" y="203" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f5gm11_di" bpmnElement="Flow_0f5gm11">
        <di:waypoint x="1585" y="221" />
        <di:waypoint x="1645" y="221" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1594" y="203" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w84iea_di" bpmnElement="Flow_1w84iea">
        <di:waypoint x="2435" y="221" />
        <di:waypoint x="2495" y="221" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2444" y="203" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_014xtrn_di" bpmnElement="Flow_014xtrn">
        <di:waypoint x="2545" y="221" />
        <di:waypoint x="2615" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13ww037_di" bpmnElement="Flow_13ww037">
        <di:waypoint x="2110" y="196" />
        <di:waypoint x="2110" y="120" />
        <di:waypoint x="2520" y="120" />
        <di:waypoint x="2520" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1v6252a_di" bpmnElement="Flow_1v6252a">
        <di:waypoint x="2410" y="246" />
        <di:waypoint x="2410" y="332" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2408" y="286" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o0p26g_di" bpmnElement="Flow_0o0p26g">
        <di:waypoint x="1560" y="246" />
        <di:waypoint x="1560" y="332" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1558" y="286" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n7zqnp_di" bpmnElement="Flow_0n7zqnp">
        <di:waypoint x="480" y="221" />
        <di:waypoint x="525" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kzysac_di" bpmnElement="Flow_1kzysac">
        <di:waypoint x="285" y="221" />
        <di:waypoint x="380" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1u6g5jo_di" bpmnElement="Flow_1u6g5jo">
        <di:waypoint x="575" y="221" />
        <di:waypoint x="635" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fl7k5z_di" bpmnElement="Flow_1fl7k5z">
        <di:waypoint x="260" y="196" />
        <di:waypoint x="260" y="140" />
        <di:waypoint x="660" y="140" />
        <di:waypoint x="660" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m0515g_di" bpmnElement="Flow_1m0515g">
        <di:waypoint x="550" y="246" />
        <di:waypoint x="550" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="548" y="276" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y2fywx_di" bpmnElement="Flow_0y2fywx">
        <di:waypoint x="685" y="221" />
        <di:waypoint x="745" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08rjcex_di" bpmnElement="Flow_08rjcex">
        <di:waypoint x="795" y="221" />
        <di:waypoint x="870" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14t1duw_di" bpmnElement="Flow_14t1duw">
        <di:waypoint x="1055" y="221" />
        <di:waypoint x="1105" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0txqn5n_di" bpmnElement="Flow_0txqn5n">
        <di:waypoint x="1155" y="221" />
        <di:waypoint x="1235" y="221" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1l3mab8_di" bpmnElement="Flow_1l3mab8">
        <di:waypoint x="770" y="196" />
        <di:waypoint x="770" y="80" />
        <di:waypoint x="1130" y="80" />
        <di:waypoint x="1130" y="196" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
