<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.8.0">
  <bpmn:process id="DeleteSBGroup" name="DeleteSBGroup" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1i15aj7</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="EndEvent_16lnasa">
      <bpmn:incoming>SequenceFlow_12cu8ej</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="OCS_DeleteSBGroup" name="OCS Delete SB Group" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_08ajd0w</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1u85r7e</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1u85r7e" sourceRef="OCS_DeleteSBGroup" targetRef="ExclusiveGateway_0si27dg" />
    <bpmn:serviceTask id="BS_DeleteSBGroup" name="BS Delete SB Group" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1uh2ryz</bpmn:incoming>
      <bpmn:outgoing>Flow_018cmn8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_018cmn8" sourceRef="BS_DeleteSBGroup" targetRef="Gateway_1g33iyl" />
    <bpmn:exclusiveGateway id="Gateway_1g33iyl">
      <bpmn:incoming>Flow_018cmn8</bpmn:incoming>
      <bpmn:outgoing>Flow_1b9ekng</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ighwgx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_12mt2ql">
      <bpmn:incoming>Flow_1b9ekng</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1b9ekng" name="Failure" sourceRef="Gateway_1g33iyl" targetRef="Event_12mt2ql">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1i15aj7" sourceRef="orderExecStart" targetRef="BS_ViewSubscription" />
    <bpmn:serviceTask id="BS_ViewSubscription" name="Bs view susbcription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1i15aj7</bpmn:incoming>
      <bpmn:outgoing>Flow_0n7zqnp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0n7zqnp" sourceRef="BS_ViewSubscription" targetRef="Gateway_0yh0mt2" />
    <bpmn:exclusiveGateway id="Gateway_0yh0mt2">
      <bpmn:incoming>Flow_0n7zqnp</bpmn:incoming>
      <bpmn:outgoing>Flow_1u6g5jo</bpmn:outgoing>
      <bpmn:outgoing>Flow_1m0515g</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1u6g5jo" sourceRef="Gateway_0yh0mt2" targetRef="SOMFetchServiceRegistry">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_0l08lay">
      <bpmn:incoming>Flow_1m0515g</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1m0515g" name="Failure" sourceRef="Gateway_0yh0mt2" targetRef="Event_0l08lay">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0si27dg">
      <bpmn:incoming>SequenceFlow_1u85r7e</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_12cu8ej</bpmn:outgoing>
      <bpmn:outgoing>Flow_1uh2ryz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_12cu8ej" name="Failure" sourceRef="ExclusiveGateway_0si27dg" targetRef="EndEvent_16lnasa">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1uh2ryz" sourceRef="ExclusiveGateway_0si27dg" targetRef="BS_DeleteSBGroup">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="BSDeleteGroupAddons" name="BS Delete Group Addons" camunda:asyncBefore="true" calledElement="BSDeleteGroupAddons" camunda:calledElementBinding="deployment">
      <bpmn:documentation>Iterate over addon subs in BS Add subs response list. planType  1 is base, 0 is addon</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:out variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ighwgx</bpmn:incoming>
      <bpmn:outgoing>Flow_0cqmmq8</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:asyncBefore="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.BS_ViewSubscriptionResponseAttributes.[?(@.planType==&#39;0&#39;)]&#34;).elementList()}" camunda:elementVariable="execution">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_1ighwgx" sourceRef="Gateway_1g33iyl" targetRef="BSDeleteGroupAddons">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0cqmmq8</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0cqmmq8" sourceRef="BSDeleteGroupAddons" targetRef="orderExecEnd" />
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch Service Registry to get Subscriptions" camunda:asyncBefore="true" camunda:delegateExpression="${somFetchGroupSubscriptions}">
      <bpmn:incoming>Flow_1u6g5jo</bpmn:incoming>
      <bpmn:outgoing>Flow_0otrbm7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0otrbm7" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_0o1hur8" />
    <bpmn:serviceTask id="SOMCancelsubscriptionDetails" name="SOM Deactivate Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${somCancelSubscription}">
      <bpmn:incoming>Flow_0of088j</bpmn:incoming>
      <bpmn:outgoing>Flow_1rf0eqg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1rf0eqg" sourceRef="SOMCancelsubscriptionDetails" targetRef="Gateway_1etu7cb" />
    <bpmn:exclusiveGateway id="Gateway_0o1hur8">
      <bpmn:incoming>Flow_0otrbm7</bpmn:incoming>
      <bpmn:outgoing>Flow_1bt29y2</bpmn:outgoing>
      <bpmn:outgoing>Flow_0t4ceo7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1bt29y2" sourceRef="Gateway_0o1hur8" targetRef="Gateway_0vzowmu">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1etu7cb">
      <bpmn:incoming>Flow_1rf0eqg</bpmn:incoming>
      <bpmn:outgoing>Flow_1lx1nxn</bpmn:outgoing>
      <bpmn:outgoing>Flow_0oaw2yh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1lx1nxn" sourceRef="Gateway_1etu7cb" targetRef="SOMCancelSubCallback">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_0scyqbx">
      <bpmn:incoming>Flow_0t4ceo7</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0t4ceo7" name="Failure" sourceRef="Gateway_0o1hur8" targetRef="Event_0scyqbx">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="SOMCancelSubCallback" name="SOMCancelSubCallback" camunda:asyncBefore="true" messageRef="Message_1fyqyx3">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOMCancelsubscriptionDetails</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOMCancelsubscriptionDetails" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1lx1nxn</bpmn:incoming>
      <bpmn:outgoing>Flow_1hn69ec</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_1jzvrlz">
      <bpmn:incoming>Flow_1hn69ec</bpmn:incoming>
      <bpmn:outgoing>Flow_0n9msxi</bpmn:outgoing>
      <bpmn:outgoing>Flow_18wmq1j</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0n9msxi" sourceRef="Gateway_1jzvrlz" targetRef="Gateway_0hci76d">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_191ll1u">
      <bpmn:incoming>Flow_0oaw2yh</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_0d3bke4">
      <bpmn:incoming>Flow_18wmq1j</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0oaw2yh" name="Failure" sourceRef="Gateway_1etu7cb" targetRef="Event_191ll1u">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_18wmq1j" name="Failure" sourceRef="Gateway_1jzvrlz" targetRef="Event_0d3bke4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1hn69ec" sourceRef="SOMCancelSubCallback" targetRef="Gateway_1jzvrlz" />
    <bpmn:exclusiveGateway id="Gateway_0vzowmu" default="Flow_1bxqigj">
      <bpmn:incoming>Flow_1bt29y2</bpmn:incoming>
      <bpmn:outgoing>Flow_0of088j</bpmn:outgoing>
      <bpmn:outgoing>Flow_1bxqigj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0of088j" sourceRef="Gateway_0vzowmu" targetRef="SOMCancelsubscriptionDetails">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${somDeleteGroupSubscriptions}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0hci76d">
      <bpmn:incoming>Flow_0n9msxi</bpmn:incoming>
      <bpmn:incoming>Flow_1bxqigj</bpmn:incoming>
      <bpmn:outgoing>Flow_08ajd0w</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_08ajd0w" sourceRef="Gateway_0hci76d" targetRef="OCS_DeleteSBGroup" />
    <bpmn:sequenceFlow id="Flow_1bxqigj" sourceRef="Gateway_0vzowmu" targetRef="Gateway_0hci76d" />
  </bpmn:process>
  <bpmn:message id="Message_1fyqyx3" name="SOMCancelSubCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="DeleteSBGroup">
      <bpmndi:BPMNShape id="Event_1x7dsnm_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="163" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_16lnasa_di" bpmnElement="EndEvent_16lnasa">
        <dc:Bounds x="1932" y="262" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0oic3i3_di" bpmnElement="OCS_DeleteSBGroup">
        <dc:Bounds x="1730" y="141" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_03w0von" bpmnElement="BS_DeleteSBGroup">
        <dc:Bounds x="2080" y="141" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1a7h86n" bpmnElement="Gateway_1g33iyl" isMarkerVisible="true">
        <dc:Bounds x="2245" y="156" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_00oa1q0" bpmnElement="Event_12mt2ql">
        <dc:Bounds x="2252" y="262" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13p21z4" bpmnElement="BS_ViewSubscription">
        <dc:Bounds x="300" y="141" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0nwzeum" bpmnElement="Gateway_0yh0mt2" isMarkerVisible="true">
        <dc:Bounds x="515" y="156" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0noc7sy" bpmnElement="Event_0l08lay">
        <dc:Bounds x="522" y="272" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0si27dg_di" bpmnElement="ExclusiveGateway_0si27dg" isMarkerVisible="true">
        <dc:Bounds x="1925" y="156" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rkhdmw" bpmnElement="BSDeleteGroupAddons">
        <dc:Bounds x="2400" y="141" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1y51hb3_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2562" y="163" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1l7h8rx_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="640" y="141" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1pwol3v_di" bpmnElement="SOMCancelSubCallback">
        <dc:Bounds x="1380" y="141" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1v2tujq" bpmnElement="Gateway_0vzowmu" isMarkerVisible="true">
        <dc:Bounds x="935" y="156" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0h76glc" bpmnElement="Gateway_0o1hur8" isMarkerVisible="true">
        <dc:Bounds x="805" y="156" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1xj0110" bpmnElement="Event_0scyqbx">
        <dc:Bounds x="812" y="272" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0y60c2b" bpmnElement="Gateway_1etu7cb" isMarkerVisible="true">
        <dc:Bounds x="1225" y="156" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ftdl66" bpmnElement="Event_191ll1u">
        <dc:Bounds x="1232" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1jzvrlz_di" bpmnElement="Gateway_1jzvrlz" isMarkerVisible="true">
        <dc:Bounds x="1535" y="156" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_12gm9ai" bpmnElement="Event_0d3bke4">
        <dc:Bounds x="1542" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0wctg7v" bpmnElement="Gateway_0hci76d" isMarkerVisible="true">
        <dc:Bounds x="1635" y="156" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ao82aw_di" bpmnElement="SOMCancelsubscriptionDetails">
        <dc:Bounds x="1050" y="141" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1i15aj7_di" bpmnElement="Flow_1i15aj7">
        <di:waypoint x="188" y="181" />
        <di:waypoint x="300" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_12cu8ej_di" bpmnElement="SequenceFlow_12cu8ej">
        <di:waypoint x="1950" y="206" />
        <di:waypoint x="1950" y="262" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1959" y="228" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08ajd0w_di" bpmnElement="Flow_08ajd0w">
        <di:waypoint x="1685" y="181" />
        <di:waypoint x="1730" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1u85r7e_di" bpmnElement="SequenceFlow_1u85r7e">
        <di:waypoint x="1830" y="181" />
        <di:waypoint x="1925" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uh2ryz_di" bpmnElement="Flow_1uh2ryz">
        <di:waypoint x="1975" y="181" />
        <di:waypoint x="2080" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_018cmn8_di" bpmnElement="Flow_018cmn8">
        <di:waypoint x="2180" y="181" />
        <di:waypoint x="2245" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b9ekng_di" bpmnElement="Flow_1b9ekng">
        <di:waypoint x="2270" y="206" />
        <di:waypoint x="2270" y="262" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2268" y="230" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ighwgx_di" bpmnElement="Flow_1ighwgx">
        <di:waypoint x="2295" y="181" />
        <di:waypoint x="2348" y="181" />
        <di:waypoint x="2348" y="180" />
        <di:waypoint x="2400" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n7zqnp_di" bpmnElement="Flow_0n7zqnp">
        <di:waypoint x="400" y="181" />
        <di:waypoint x="515" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1u6g5jo_di" bpmnElement="Flow_1u6g5jo">
        <di:waypoint x="565" y="181" />
        <di:waypoint x="640" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m0515g_di" bpmnElement="Flow_1m0515g">
        <di:waypoint x="540" y="206" />
        <di:waypoint x="540" y="272" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="538" y="236" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cqmmq8_di" bpmnElement="Flow_0cqmmq8">
        <di:waypoint x="2500" y="181" />
        <di:waypoint x="2562" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0otrbm7_di" bpmnElement="Flow_0otrbm7">
        <di:waypoint x="740" y="181" />
        <di:waypoint x="805" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lx1nxn_di" bpmnElement="Flow_1lx1nxn">
        <di:waypoint x="1275" y="181" />
        <di:waypoint x="1380" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hn69ec_di" bpmnElement="Flow_1hn69ec">
        <di:waypoint x="1480" y="181" />
        <di:waypoint x="1535" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bt29y2_di" bpmnElement="Flow_1bt29y2">
        <di:waypoint x="855" y="181" />
        <di:waypoint x="935" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0of088j_di" bpmnElement="Flow_0of088j">
        <di:waypoint x="985" y="181" />
        <di:waypoint x="1050" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bxqigj_di" bpmnElement="Flow_1bxqigj">
        <di:waypoint x="960" y="156" />
        <di:waypoint x="960" y="80" />
        <di:waypoint x="1660" y="80" />
        <di:waypoint x="1660" y="156" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0t4ceo7_di" bpmnElement="Flow_0t4ceo7">
        <di:waypoint x="830" y="206" />
        <di:waypoint x="830" y="272" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="828" y="236" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rf0eqg_di" bpmnElement="Flow_1rf0eqg">
        <di:waypoint x="1150" y="181" />
        <di:waypoint x="1225" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oaw2yh_di" bpmnElement="Flow_0oaw2yh">
        <di:waypoint x="1250" y="206" />
        <di:waypoint x="1250" y="282" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1248" y="241" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n9msxi_di" bpmnElement="Flow_0n9msxi">
        <di:waypoint x="1585" y="181" />
        <di:waypoint x="1635" y="181" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18wmq1j_di" bpmnElement="Flow_18wmq1j">
        <di:waypoint x="1560" y="206" />
        <di:waypoint x="1560" y="282" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1559" y="240" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
