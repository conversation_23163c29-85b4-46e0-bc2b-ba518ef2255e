<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0">
  <bpmn:process id="AddSBBeneficiary" name="AddSBBeneficiary" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0yo2zs6</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="OCSAddBeneficiary" name="OCS Add SB Beneficiary" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0yo2zs6</bpmn:incoming>
      <bpmn:outgoing>Flow_1c4dv1s</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1j286sz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1c4dv1s" sourceRef="OCSAddBeneficiary" targetRef="ExclusiveGateway_0za7t9j" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_0za7t9j" default="SequenceFlow_0pv4iss">
      <bpmn:incoming>Flow_1c4dv1s</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0pv4iss</bpmn:outgoing>
      <bpmn:outgoing>Flow_1qom6h3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0pv4iss" name="Success" sourceRef="ExclusiveGateway_0za7t9j" targetRef="BSAddBeneficiary" />
    <bpmn:serviceTask id="BSAddBeneficiary" name="BS Add SB Beneficiary" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_0pv4iss</bpmn:incoming>
      <bpmn:outgoing>Flow_1j286sz</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1j286sz" sourceRef="BSAddBeneficiary" targetRef="orderExecEnd" />
    <bpmn:endEvent id="Event_123kjxu">
      <bpmn:incoming>Flow_1qom6h3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1qom6h3" name="Failure" sourceRef="ExclusiveGateway_0za7t9j" targetRef="Event_123kjxu">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0yo2zs6" sourceRef="orderExecStart" targetRef="OCSAddBeneficiary" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="AddSBBeneficiary">
      <bpmndi:BPMNShape id="Event_1x7dsnm_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="103" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ngn6ow_di" bpmnElement="OCSAddBeneficiary">
        <dc:Bounds x="300" y="81" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1p88c2w_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="832" y="103" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0za7t9j_di" bpmnElement="ExclusiveGateway_0za7t9j" isMarkerVisible="true">
        <dc:Bounds x="482" y="96" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0pvqzq8_di" bpmnElement="BSAddBeneficiary">
        <dc:Bounds x="640" y="81" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_123kjxu_di" bpmnElement="Event_123kjxu">
        <dc:Bounds x="489" y="222" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1c4dv1s_di" bpmnElement="Flow_1c4dv1s">
        <di:waypoint x="400" y="121" />
        <di:waypoint x="482" y="121" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0pv4iss_di" bpmnElement="SequenceFlow_0pv4iss">
        <di:waypoint x="532" y="121" />
        <di:waypoint x="640" y="121" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="565" y="103" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1j286sz_di" bpmnElement="Flow_1j286sz">
        <di:waypoint x="740" y="121" />
        <di:waypoint x="832" y="121" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qom6h3_di" bpmnElement="Flow_1qom6h3">
        <di:waypoint x="507" y="146" />
        <di:waypoint x="507" y="222" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="505" y="181" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yo2zs6_di" bpmnElement="Flow_0yo2zs6">
        <di:waypoint x="188" y="121" />
        <di:waypoint x="300" y="121" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
