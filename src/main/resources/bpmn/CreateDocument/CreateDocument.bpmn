<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1lsckuo" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.21.0">
  <bpmn:process id="CreateDocument" name="CreateDocument" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1ybz3m3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1ybz3m3" sourceRef="orderExecStart" targetRef="Gateway_0uhzbw4" />
    <bpmn:serviceTask id="PMCreateDocument" name="PM Create Document" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1sqnvpe</bpmn:incoming>
      <bpmn:outgoing>Flow_0x84wdg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0uhzbw4" name="if isIdDocument true OR false" default="Flow_1h742ci">
      <bpmn:incoming>Flow_1ybz3m3</bpmn:incoming>
      <bpmn:outgoing>Flow_1sqnvpe</bpmn:outgoing>
      <bpmn:outgoing>Flow_1h742ci</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1sqnvpe" name=" true" sourceRef="Gateway_0uhzbw4" targetRef="PMCreateDocument">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order.documentDetails.isIdDocument").stringValue() == 'true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSCreateDocument" name="BS Create Document" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1h742ci</bpmn:incoming>
      <bpmn:outgoing>Flow_12wg9ta</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1h742ci" sourceRef="Gateway_0uhzbw4" targetRef="BSCreateDocument" />
    <bpmn:exclusiveGateway id="Gateway_196tgpx">
      <bpmn:incoming>Flow_12wg9ta</bpmn:incoming>
      <bpmn:incoming>Flow_0x84wdg</bpmn:incoming>
      <bpmn:outgoing>Flow_1jbi5bn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_12wg9ta" sourceRef="BSCreateDocument" targetRef="Gateway_196tgpx" />
    <bpmn:sequenceFlow id="Flow_0x84wdg" sourceRef="PMCreateDocument" targetRef="Gateway_196tgpx" />
    <bpmn:endEvent id="orderExecEnd" camunda:asyncBefore="true">
      <bpmn:incoming>Flow_1jbi5bn</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1jbi5bn" sourceRef="Gateway_196tgpx" targetRef="orderExecEnd" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CreateDocument">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="269" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0oe96hx_di" bpmnElement="PMCreateDocument">
        <dc:Bounds x="410" y="247" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0uhzbw4_di" bpmnElement="Gateway_0uhzbw4" isMarkerVisible="true">
        <dc:Bounds x="285" y="262" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="272" y="319" width="77" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1222hl4_di" bpmnElement="BSCreateDocument">
        <dc:Bounds x="410" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_196tgpx_di" bpmnElement="Gateway_196tgpx" isMarkerVisible="true">
        <dc:Bounds x="635" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1fo6wqe_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="772" y="192" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1ybz3m3_di" bpmnElement="Flow_1ybz3m3">
        <di:waypoint x="188" y="287" />
        <di:waypoint x="285" y="287" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sqnvpe_di" bpmnElement="Flow_1sqnvpe">
        <di:waypoint x="335" y="287" />
        <di:waypoint x="410" y="287" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="363" y="269" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h742ci_di" bpmnElement="Flow_1h742ci">
        <di:waypoint x="310" y="262" />
        <di:waypoint x="310" y="120" />
        <di:waypoint x="410" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12wg9ta_di" bpmnElement="Flow_12wg9ta">
        <di:waypoint x="510" y="120" />
        <di:waypoint x="660" y="120" />
        <di:waypoint x="660" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x84wdg_di" bpmnElement="Flow_0x84wdg">
        <di:waypoint x="510" y="287" />
        <di:waypoint x="660" y="287" />
        <di:waypoint x="660" y="235" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jbi5bn_di" bpmnElement="Flow_1jbi5bn">
        <di:waypoint x="685" y="210" />
        <di:waypoint x="772" y="210" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
