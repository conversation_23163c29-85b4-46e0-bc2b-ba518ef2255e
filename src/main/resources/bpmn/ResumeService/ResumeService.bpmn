<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0">
  <bpmn:process id="ResumeService" name="ResumeService" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1dymq4s</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_06d8752</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSUpdateServiceState" name="Resume Service in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0ug4d3h</bpmn:incoming>
      <bpmn:outgoing>Flow_06d8752</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="OCSResumeService" name="Resume Service in OCS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0r03ere</bpmn:incoming>
      <bpmn:outgoing>Flow_1ploq8g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_06d8752" sourceRef="BSUpdateServiceState" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_1ploq8g" sourceRef="OCSResumeService" targetRef="ExclusiveGateway_09alh96" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_09alh96" default="Flow_0ug4d3h">
      <bpmn:incoming>Flow_1ploq8g</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0zh3k8m</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ug4d3h</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="EndEvent_1513n03">
      <bpmn:incoming>SequenceFlow_0zh3k8m</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0zh3k8m" name="Failure" sourceRef="ExclusiveGateway_09alh96" targetRef="EndEvent_1513n03">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_11xsf41" name="document creation req" default="Flow_1lgs3ao">
      <bpmn:incoming>Flow_0von4yr</bpmn:incoming>
      <bpmn:outgoing>Flow_1f4c1gn</bpmn:outgoing>
      <bpmn:outgoing>Flow_1lgs3ao</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0mggjc9" default="Flow_05mcocn">
      <bpmn:incoming>Flow_0r4dp40</bpmn:incoming>
      <bpmn:outgoing>Flow_1lxj5d0</bpmn:outgoing>
      <bpmn:outgoing>Flow_05mcocn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1j0iibd">
      <bpmn:incoming>Flow_1lgs3ao</bpmn:incoming>
      <bpmn:incoming>Flow_05mcocn</bpmn:incoming>
      <bpmn:outgoing>Flow_0r03ere</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_15k8isc">
      <bpmn:incoming>Flow_1lxj5d0</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSCreateDocument" name="Create Document in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1f4c1gn</bpmn:incoming>
      <bpmn:outgoing>Flow_0r4dp40</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1f4c1gn" name="yes" sourceRef="Gateway_11xsf41" targetRef="BSCreateDocument">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${hasAttachment}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0r4dp40" sourceRef="BSCreateDocument" targetRef="Gateway_0mggjc9" />
    <bpmn:sequenceFlow id="Flow_05mcocn" name="success" sourceRef="Gateway_0mggjc9" targetRef="Gateway_1j0iibd" />
    <bpmn:sequenceFlow id="Flow_1lxj5d0" name="Failure" sourceRef="Gateway_0mggjc9" targetRef="Event_15k8isc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1lgs3ao" name="no" sourceRef="Gateway_11xsf41" targetRef="Gateway_1j0iibd" />
    <bpmn:callActivity id="PaymentWorkflow" name="Payment and future order wokrflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1r8gxre</bpmn:incoming>
      <bpmn:outgoing>Flow_0i8cmhd</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_1g6h0v4" default="Flow_0von4yr">
      <bpmn:incoming>Flow_0i8cmhd</bpmn:incoming>
      <bpmn:outgoing>Flow_1gbivta</bpmn:outgoing>
      <bpmn:outgoing>Flow_0von4yr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_12q1bkt">
      <bpmn:incoming>Flow_1gbivta</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1gbivta" sourceRef="Gateway_1g6h0v4" targetRef="Event_12q1bkt">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0i8cmhd" sourceRef="PaymentWorkflow" targetRef="Gateway_1g6h0v4" />
    <bpmn:sequenceFlow id="Flow_0von4yr" sourceRef="Gateway_1g6h0v4" targetRef="Gateway_11xsf41" />
    <bpmn:sequenceFlow id="Flow_0r03ere" sourceRef="Gateway_1j0iibd" targetRef="OCSResumeService" />
    <bpmn:sequenceFlow id="Flow_0ug4d3h" sourceRef="ExclusiveGateway_09alh96" targetRef="BSUpdateServiceState" />
    <bpmn:exclusiveGateway id="Gateway_1jars73" default="Flow_1kfi33s">
      <bpmn:incoming>Flow_1dymq4s</bpmn:incoming>
      <bpmn:outgoing>Flow_04nwkl6</bpmn:outgoing>
      <bpmn:outgoing>Flow_1kfi33s</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1dymq4s" sourceRef="orderExecStart" targetRef="Gateway_1jars73" />
    <bpmn:sequenceFlow id="Flow_04nwkl6" name="yes" sourceRef="Gateway_1jars73" targetRef="BookServiceFee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${additionalCharges}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1yalo92" default="Flow_0n6rw5s">
      <bpmn:incoming>Flow_0a8ciry</bpmn:incoming>
      <bpmn:outgoing>Flow_0n6rw5s</bpmn:outgoing>
      <bpmn:outgoing>Flow_0s218n9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0a8ciry" sourceRef="BookServiceFee" targetRef="Gateway_1yalo92" />
    <bpmn:exclusiveGateway id="Gateway_11mw0pc">
      <bpmn:incoming>Flow_0n6rw5s</bpmn:incoming>
      <bpmn:incoming>Flow_1kfi33s</bpmn:incoming>
      <bpmn:outgoing>Flow_1r8gxre</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0n6rw5s" name="Success" sourceRef="Gateway_1yalo92" targetRef="Gateway_11mw0pc" />
    <bpmn:sequenceFlow id="Flow_1r8gxre" sourceRef="Gateway_11mw0pc" targetRef="PaymentWorkflow" />
    <bpmn:sequenceFlow id="Flow_1kfi33s" name="no" sourceRef="Gateway_1jars73" targetRef="Gateway_11mw0pc" />
    <bpmn:endEvent id="Event_0ojo0na">
      <bpmn:incoming>Flow_0s218n9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0s218n9" name="Failure" sourceRef="Gateway_1yalo92" targetRef="Event_0ojo0na">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BookServiceFee" name="BookServiceFee" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_04nwkl6</bpmn:incoming>
      <bpmn:outgoing>Flow_0a8ciry</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmn:message id="Message_17ruo2d" name="SOMResumeServiceCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ResumeService">
      <bpmndi:BPMNShape id="Event_08ncim8_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2172" y="181" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13hk4ch_di" bpmnElement="BSUpdateServiceState">
        <dc:Bounds x="1970" y="159" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0172vy3_di" bpmnElement="OCSResumeService">
        <dc:Bounds x="1660" y="159" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_09alh96_di" bpmnElement="ExclusiveGateway_09alh96" isMarkerVisible="true">
        <dc:Bounds x="1825" y="174" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1513n03_di" bpmnElement="EndEvent_1513n03">
        <dc:Bounds x="1832" y="284" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13cvbk9" bpmnElement="Gateway_11xsf41" isMarkerVisible="true">
        <dc:Bounds x="1115" y="174" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1111" y="231" width="59" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1pgz91l" bpmnElement="Gateway_0mggjc9" isMarkerVisible="true">
        <dc:Bounds x="1415" y="174" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_11edh7g" bpmnElement="Gateway_1j0iibd" isMarkerVisible="true">
        <dc:Bounds x="1545" y="174" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_02cuh0r" bpmnElement="Event_15k8isc">
        <dc:Bounds x="1422" y="303" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ev7bow" bpmnElement="BSCreateDocument">
        <dc:Bounds x="1240" y="159" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1m97z0v_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="740" y="159" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1g6h0v4_di" bpmnElement="Gateway_1g6h0v4" isMarkerVisible="true">
        <dc:Bounds x="955" y="174" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_12q1bkt_di" bpmnElement="Event_12q1bkt">
        <dc:Bounds x="962" y="303" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1x7dsnm_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="181" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1jars73_di" bpmnElement="Gateway_1jars73" isMarkerVisible="true">
        <dc:Bounds x="245" y="174" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1yalo92_di" bpmnElement="Gateway_1yalo92" isMarkerVisible="true">
        <dc:Bounds x="525" y="174" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_11mw0pc_di" bpmnElement="Gateway_11mw0pc" isMarkerVisible="true">
        <dc:Bounds x="645" y="174" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ojo0na_di" bpmnElement="Event_0ojo0na">
        <dc:Bounds x="532" y="284" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07azbsf_di" bpmnElement="BookServiceFee">
        <dc:Bounds x="360" y="159" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_06d8752_di" bpmnElement="Flow_06d8752">
        <di:waypoint x="2070" y="199" />
        <di:waypoint x="2172" y="199" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ug4d3h_di" bpmnElement="Flow_0ug4d3h">
        <di:waypoint x="1875" y="199" />
        <di:waypoint x="1970" y="199" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r03ere_di" bpmnElement="Flow_0r03ere">
        <di:waypoint x="1595" y="199" />
        <di:waypoint x="1660" y="199" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ploq8g_di" bpmnElement="Flow_1ploq8g">
        <di:waypoint x="1760" y="199" />
        <di:waypoint x="1825" y="199" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0zh3k8m_di" bpmnElement="SequenceFlow_0zh3k8m">
        <di:waypoint x="1850" y="224" />
        <di:waypoint x="1850" y="284" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1850" y="251" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0von4yr_di" bpmnElement="Flow_0von4yr">
        <di:waypoint x="1005" y="199" />
        <di:waypoint x="1115" y="199" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_119f3wu" bpmnElement="Flow_1f4c1gn">
        <di:waypoint x="1165" y="199" />
        <di:waypoint x="1240" y="199" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1187" y="175" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1urd775" bpmnElement="Flow_1lgs3ao">
        <di:waypoint x="1140" y="174" />
        <di:waypoint x="1140" y="100" />
        <di:waypoint x="1570" y="100" />
        <di:waypoint x="1570" y="174" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1349" y="82" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_00jykig" bpmnElement="Flow_0r4dp40">
        <di:waypoint x="1340" y="199" />
        <di:waypoint x="1415" y="199" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_09iknvn" bpmnElement="Flow_05mcocn">
        <di:waypoint x="1465" y="199" />
        <di:waypoint x="1545" y="199" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1486" y="181" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1qciv1z" bpmnElement="Flow_1lxj5d0">
        <di:waypoint x="1440" y="224" />
        <di:waypoint x="1440" y="303" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1439" y="261" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r8gxre_di" bpmnElement="Flow_1r8gxre">
        <di:waypoint x="695" y="199" />
        <di:waypoint x="718" y="199" />
        <di:waypoint x="718" y="200" />
        <di:waypoint x="740" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0i8cmhd_di" bpmnElement="Flow_0i8cmhd">
        <di:waypoint x="840" y="199" />
        <di:waypoint x="955" y="199" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gbivta_di" bpmnElement="Flow_1gbivta">
        <di:waypoint x="980" y="224" />
        <di:waypoint x="980" y="303" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dymq4s_di" bpmnElement="Flow_1dymq4s">
        <di:waypoint x="188" y="199" />
        <di:waypoint x="245" y="199" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04nwkl6_di" bpmnElement="Flow_04nwkl6">
        <di:waypoint x="295" y="199" />
        <di:waypoint x="360" y="199" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="319" y="181" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kfi33s_di" bpmnElement="Flow_1kfi33s">
        <di:waypoint x="270" y="174" />
        <di:waypoint x="270" y="90" />
        <di:waypoint x="670" y="90" />
        <di:waypoint x="670" y="174" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="464" y="72" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a8ciry_di" bpmnElement="Flow_0a8ciry">
        <di:waypoint x="460" y="199" />
        <di:waypoint x="525" y="199" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n6rw5s_di" bpmnElement="Flow_0n6rw5s">
        <di:waypoint x="575" y="199" />
        <di:waypoint x="645" y="199" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="591" y="181" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0s218n9_di" bpmnElement="Flow_0s218n9">
        <di:waypoint x="550" y="224" />
        <di:waypoint x="550" y="284" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="548" y="251" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
