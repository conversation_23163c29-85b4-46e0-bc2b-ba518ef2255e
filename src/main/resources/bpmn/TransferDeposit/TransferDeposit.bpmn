<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="TransferDeposit" name="TransferDeposit" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1y9wji3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="BSTransferDeposit" name="BS Transfer Deposit" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1y9wji3</bpmn:incoming>
      <bpmn:outgoing>Flow_1c4dv1s</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1c4dv1s</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1c4dv1s" sourceRef="BSTransferDeposit" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_1y9wji3" sourceRef="orderExecStart" targetRef="BSTransferDeposit" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="TransferDeposit">
      <bpmndi:BPMNEdge id="Flow_1y9wji3_di" bpmnElement="Flow_1y9wji3">
        <di:waypoint x="228" y="120" />
        <di:waypoint x="310" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c4dv1s_di" bpmnElement="Flow_1c4dv1s">
        <di:waypoint x="410" y="120" />
        <di:waypoint x="492" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_1x7dsnm_di" bpmnElement="orderExecStart">
        <dc:Bounds x="192" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ngn6ow_di" bpmnElement="BSTransferDeposit">
        <dc:Bounds x="310" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1p88c2w_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="492" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
