<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.28.0">
  <bpmn:process id="ChangeSim" name="ChangeSim" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0xj3wcy</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="SOM_change_SIM" name="SOM change SIM" camunda:asyncBefore="true" camunda:delegateExpression="${somChangeSim}">
      <bpmn:incoming>Flow_1bj4wjv</bpmn:incoming>
      <bpmn:outgoing>Flow_0vekyb1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_11242py" default="Flow_003cyt6">
      <bpmn:incoming>Flow_0vekyb1</bpmn:incoming>
      <bpmn:outgoing>Flow_0xx5wld</bpmn:outgoing>
      <bpmn:outgoing>Flow_003cyt6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0vekyb1" sourceRef="SOM_change_SIM" targetRef="Gateway_11242py" />
    <bpmn:endEvent id="Event_0lbs8hj">
      <bpmn:incoming>Flow_0xx5wld</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0xx5wld" name="Failure" sourceRef="Gateway_11242py" targetRef="Event_0lbs8hj">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="OCS_Change_SIM" name="OCS Change SIM" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_157awwf</bpmn:incoming>
      <bpmn:outgoing>Flow_142allt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1pwl5pt" default="Flow_1ic6nwu">
      <bpmn:incoming>Flow_142allt</bpmn:incoming>
      <bpmn:outgoing>Flow_1ic6nwu</bpmn:outgoing>
      <bpmn:outgoing>Flow_1kwbuv9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_142allt" sourceRef="OCS_Change_SIM" targetRef="Gateway_1pwl5pt" />
    <bpmn:sequenceFlow id="Flow_1ic6nwu" sourceRef="Gateway_1pwl5pt" targetRef="BS_change_SIM" />
    <bpmn:exclusiveGateway id="Gateway_12muwui" default="Flow_1tdbzk9">
      <bpmn:incoming>Flow_1wkpxbh</bpmn:incoming>
      <bpmn:outgoing>Flow_1tdbzk9</bpmn:outgoing>
      <bpmn:outgoing>Flow_1r091me</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1wkpxbh" sourceRef="BS_change_SIM" targetRef="Gateway_12muwui" />
    <bpmn:serviceTask id="BS_change_SIM" name="BS change SIM" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1ic6nwu</bpmn:incoming>
      <bpmn:outgoing>Flow_1wkpxbh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1tdbzk9" sourceRef="Gateway_12muwui" targetRef="NMS_SimSwap" />
    <bpmn:serviceTask id="NMS_SimSwap" name="NMS Sim Swap " camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1tdbzk9</bpmn:incoming>
      <bpmn:outgoing>Flow_0ytzor1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0q2svdr">
      <bpmn:incoming>Flow_1kwbuv9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1kwbuv9" name="Failure" sourceRef="Gateway_1pwl5pt" targetRef="Event_0q2svdr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_09tz2xv">
      <bpmn:incoming>Flow_1r091me</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1r091me" name="Failure" sourceRef="Gateway_12muwui" targetRef="Event_09tz2xv">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_09qm017" default="Flow_02mh48o">
      <bpmn:incoming>Flow_14e8mfh</bpmn:incoming>
      <bpmn:outgoing>Flow_0kpyymf</bpmn:outgoing>
      <bpmn:outgoing>Flow_02mh48o</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0d70g0t</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch services" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0ygh3wi</bpmn:incoming>
      <bpmn:outgoing>Flow_07h0uoj</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0w1lgpi" default="Flow_1bj4wjv">
      <bpmn:incoming>Flow_07h0uoj</bpmn:incoming>
      <bpmn:outgoing>Flow_1bj4wjv</bpmn:outgoing>
      <bpmn:outgoing>Flow_16x0odp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_07h0uoj" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_0w1lgpi" />
    <bpmn:sequenceFlow id="Flow_1bj4wjv" sourceRef="Gateway_0w1lgpi" targetRef="SOM_change_SIM" />
    <bpmn:endEvent id="Event_0f0eaq5">
      <bpmn:incoming>Flow_16x0odp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_16x0odp" name="Failure" sourceRef="Gateway_0w1lgpi" targetRef="Event_0f0eaq5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="SOMChangeSimCallback" name="SOM Callback" camunda:asyncBefore="true" messageRef="Message_02m47bb">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOM_change_SIM</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOM_change_SIM" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_003cyt6</bpmn:incoming>
      <bpmn:outgoing>Flow_1gdl6nb</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_003cyt6" sourceRef="Gateway_11242py" targetRef="SOMChangeSimCallback" />
    <bpmn:exclusiveGateway id="Gateway_1e8xdnp" default="Flow_157awwf">
      <bpmn:incoming>Flow_1gdl6nb</bpmn:incoming>
      <bpmn:outgoing>Flow_157awwf</bpmn:outgoing>
      <bpmn:outgoing>Flow_1biwy1s</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1gdl6nb" sourceRef="SOMChangeSimCallback" targetRef="Gateway_1e8xdnp" />
    <bpmn:sequenceFlow id="Flow_157awwf" sourceRef="Gateway_1e8xdnp" targetRef="OCS_Change_SIM" />
    <bpmn:endEvent id="Event_1gsvqug">
      <bpmn:incoming>Flow_1biwy1s</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1biwy1s" name="Failure" sourceRef="Gateway_1e8xdnp" targetRef="Event_1gsvqug">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1pt3213" default="Flow_17dy2n2">
      <bpmn:incoming>Flow_1w55keh</bpmn:incoming>
      <bpmn:outgoing>Flow_115kl92</bpmn:outgoing>
      <bpmn:outgoing>Flow_17dy2n2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1w55keh" sourceRef="BookServiceFee" targetRef="Gateway_1pt3213" />
    <bpmn:serviceTask id="BookServiceFee" name="Book Service Fee" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0tchw38</bpmn:incoming>
      <bpmn:outgoing>Flow_1w55keh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_14ktkma">
      <bpmn:incoming>Flow_115kl92</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_115kl92" sourceRef="Gateway_1pt3213" targetRef="Event_14ktkma">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1f0iii0" default="Flow_0nbqnmz">
      <bpmn:incoming>Flow_0rfgzkk</bpmn:incoming>
      <bpmn:outgoing>Flow_0tchw38</bpmn:outgoing>
      <bpmn:outgoing>Flow_0nbqnmz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0tchw38" name="yes" sourceRef="Gateway_1f0iii0" targetRef="BookServiceFee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${additionalCharges}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0zgy5zs">
      <bpmn:incoming>Flow_17dy2n2</bpmn:incoming>
      <bpmn:incoming>Flow_0nbqnmz</bpmn:incoming>
      <bpmn:outgoing>Flow_0xnp3wr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_17dy2n2" name="success" sourceRef="Gateway_1pt3213" targetRef="Gateway_0zgy5zs" />
    <bpmn:sequenceFlow id="Flow_0nbqnmz" name="no" sourceRef="Gateway_1f0iii0" targetRef="Gateway_0zgy5zs" />
    <bpmn:sequenceFlow id="Flow_0xj3wcy" sourceRef="orderExecStart" targetRef="Arm_FetchAssetDetails" />
    <bpmn:exclusiveGateway id="Gateway_0o7pr1c" default="Flow_1g2f7wa">
      <bpmn:incoming>Flow_0xnp3wr</bpmn:incoming>
      <bpmn:outgoing>Flow_0onpo2m</bpmn:outgoing>
      <bpmn:outgoing>Flow_1g2f7wa</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0xnp3wr" sourceRef="Gateway_0zgy5zs" targetRef="Gateway_0o7pr1c" />
    <bpmn:sequenceFlow id="Flow_0onpo2m" name="e-Sim" sourceRef="Gateway_0o7pr1c" targetRef="Esb_blockEsim">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${eSIMCallReq}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1hhg262" default="Flow_1agrdxe">
      <bpmn:incoming>Flow_1desy2w</bpmn:incoming>
      <bpmn:outgoing>Flow_036d8zo</bpmn:outgoing>
      <bpmn:outgoing>Flow_1agrdxe</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1desy2w" sourceRef="Esb_blockEsim" targetRef="Gateway_1hhg262" />
    <bpmn:exclusiveGateway id="Gateway_0g96lkw" default="Flow_0ygh3wi">
      <bpmn:incoming>Flow_1g2f7wa</bpmn:incoming>
      <bpmn:incoming>Flow_1ehb4tj</bpmn:incoming>
      <bpmn:outgoing>Flow_0ygh3wi</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1g2f7wa" name="PhysicalSim" sourceRef="Gateway_0o7pr1c" targetRef="Gateway_0g96lkw" />
    <bpmn:endEvent id="Event_087z256">
      <bpmn:incoming>Flow_036d8zo</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_036d8zo" sourceRef="Gateway_1hhg262" targetRef="Event_087z256">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Esb_blockEsim" name="ESB Block eSim" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0onpo2m</bpmn:incoming>
      <bpmn:outgoing>Flow_1desy2w</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1agrdxe" sourceRef="Gateway_1hhg262" targetRef="ESBEsimBlockCallback" />
    <bpmn:exclusiveGateway id="Gateway_0bi9ful" default="Flow_1ehb4tj">
      <bpmn:incoming>Flow_00yznh5</bpmn:incoming>
      <bpmn:outgoing>Flow_1ehb4tj</bpmn:outgoing>
      <bpmn:outgoing>Flow_0u8bm20</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_00yznh5" sourceRef="ESBEsimBlockCallback" targetRef="Gateway_0bi9ful" />
    <bpmn:sequenceFlow id="Flow_1ehb4tj" sourceRef="Gateway_0bi9ful" targetRef="Gateway_0g96lkw" />
    <bpmn:endEvent id="Event_18i69yr">
      <bpmn:incoming>Flow_0u8bm20</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0u8bm20" sourceRef="Gateway_0bi9ful" targetRef="Event_18i69yr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="ESBEsimBlockCallback" name="ESB BlockSim CallBack" camunda:asyncBefore="true" messageRef="Message_1n1rh20">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="Esb_blockEsim" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">Esb_blockEsim</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1agrdxe</bpmn:incoming>
      <bpmn:outgoing>Flow_00yznh5</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_0kpyymf" name="eSIMisTrue" sourceRef="Gateway_09qm017" targetRef="ESB_fetch">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${eSIMCallReq}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="ESB_fetch" name=" Fetch Esim status From ESB" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0kpyymf</bpmn:incoming>
      <bpmn:outgoing>Flow_0k4vrvn</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0vn14lo" default="Flow_085zkua">
      <bpmn:incoming>Flow_0k4vrvn</bpmn:incoming>
      <bpmn:outgoing>Flow_085zkua</bpmn:outgoing>
      <bpmn:outgoing>Flow_1yn3ctn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0k4vrvn" sourceRef="ESB_fetch" targetRef="Gateway_0vn14lo" />
    <bpmn:sequenceFlow id="Flow_085zkua" sourceRef="Gateway_0vn14lo" targetRef="DAG_Esim" />
    <bpmn:serviceTask id="DAG_Esim" name="SIM Status Sent to DAG" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_085zkua</bpmn:incoming>
      <bpmn:outgoing>Flow_1c89w7z</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1m8qy34">
      <bpmn:incoming>Flow_1yn3ctn</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1yn3ctn" name="Failure" sourceRef="Gateway_0vn14lo" targetRef="Event_1m8qy34">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0w73x0l">
      <bpmn:incoming>Flow_1c89w7z</bpmn:incoming>
      <bpmn:incoming>Flow_02mh48o</bpmn:incoming>
      <bpmn:outgoing>Flow_0rvnkae</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1c89w7z" sourceRef="DAG_Esim" targetRef="Gateway_0w73x0l" />
    <bpmn:sequenceFlow id="Flow_0rvnkae" sourceRef="Gateway_0w73x0l" targetRef="Gateway_13xuu89" />
    <bpmn:sequenceFlow id="Flow_02mh48o" sourceRef="Gateway_09qm017" targetRef="Gateway_0w73x0l" />
    <bpmn:exclusiveGateway id="Gateway_10s750p" default="Flow_14e8mfh">
      <bpmn:incoming>Flow_0ytzor1</bpmn:incoming>
      <bpmn:outgoing>Flow_14e8mfh</bpmn:outgoing>
      <bpmn:outgoing>Flow_1s0bfpq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ytzor1" sourceRef="NMS_SimSwap" targetRef="Gateway_10s750p" />
    <bpmn:sequenceFlow id="Flow_14e8mfh" sourceRef="Gateway_10s750p" targetRef="Gateway_09qm017" />
    <bpmn:endEvent id="Event_1ljp1sz">
      <bpmn:incoming>Flow_1s0bfpq</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1s0bfpq" name="Failure" sourceRef="Gateway_10s750p" targetRef="Event_1ljp1sz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Arm_FetchAssetDetails" name="Arm Fetch Asset Details" camunda:asyncBefore="true" camunda:delegateExpression="${fetchDetailsFromArm}">
      <bpmn:incoming>Flow_0xj3wcy</bpmn:incoming>
      <bpmn:outgoing>Flow_1mf6q6n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1mf6q6n" sourceRef="Arm_FetchAssetDetails" targetRef="Gateway_1qd7juv" />
    <bpmn:exclusiveGateway id="Gateway_1qd7juv" default="Flow_0q5x43r">
      <bpmn:incoming>Flow_1mf6q6n</bpmn:incoming>
      <bpmn:outgoing>Flow_181w14t</bpmn:outgoing>
      <bpmn:outgoing>Flow_0q5x43r</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_005bgn6">
      <bpmn:incoming>Flow_181w14t</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_181w14t" sourceRef="Gateway_1qd7juv" targetRef="Event_005bgn6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSFetchNsaPlans" name="BS ViewSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${fetchNsaPlans}">
      <bpmn:incoming>Flow_0fn05aw</bpmn:incoming>
      <bpmn:outgoing>Flow_1jv7krb</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0nd8dc8" default="Flow_17dcvoy">
      <bpmn:incoming>Flow_1jv7krb</bpmn:incoming>
      <bpmn:outgoing>Flow_1ffpb9a</bpmn:outgoing>
      <bpmn:outgoing>Flow_17dcvoy</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0xjjt3c" default="Flow_0flta3b">
      <bpmn:incoming>Flow_01halg7</bpmn:incoming>
      <bpmn:outgoing>Flow_0joj8op</bpmn:outgoing>
      <bpmn:outgoing>Flow_0flta3b</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1wncks3">
      <bpmn:incoming>Flow_0joj8op</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0joj8op" name="Failure" sourceRef="Gateway_0xjjt3c" targetRef="Event_1wncks3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_067nxj8">
      <bpmn:incoming>Flow_1ffpb9a</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1ffpb9a" name="Failure" sourceRef="Gateway_0nd8dc8" targetRef="Event_067nxj8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_19sddny" name="From arm reponse if we get simtype=5G" default="Flow_1dn1ub4">
      <bpmn:incoming>Flow_0q5x43r</bpmn:incoming>
      <bpmn:outgoing>Flow_0fn05aw</bpmn:outgoing>
      <bpmn:outgoing>Flow_1dn1ub4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0q5x43r" sourceRef="Gateway_1qd7juv" targetRef="Gateway_19sddny" />
    <bpmn:sequenceFlow id="Flow_0fn05aw" name="simtype =5G" sourceRef="Gateway_19sddny" targetRef="BSFetchNsaPlans">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${fivegSimFound}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_07dpwmm">
      <bpmn:incoming>Flow_1dn1ub4</bpmn:incoming>
      <bpmn:incoming>Flow_0xpsgb6</bpmn:incoming>
      <bpmn:outgoing>Flow_0rfgzkk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0rfgzkk" sourceRef="Gateway_07dpwmm" targetRef="Gateway_1f0iii0" />
    <bpmn:sequenceFlow id="Flow_1dn1ub4" name="no" sourceRef="Gateway_19sddny" targetRef="Gateway_07dpwmm" />
    <bpmn:sequenceFlow id="Flow_0ygh3wi" sourceRef="Gateway_0g96lkw" targetRef="SOMFetchServiceRegistry" />
    <bpmn:sequenceFlow id="Flow_1jv7krb" sourceRef="BSFetchNsaPlans" targetRef="Gateway_0nd8dc8" />
    <bpmn:callActivity id="ChangeSimSaActivation" name="ChangeSimSaActivation" camunda:asyncBefore="true" calledElement="ChangeSimSaActivation" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1fbnpcw</bpmn:incoming>
      <bpmn:outgoing>Flow_0yowok8</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0yowok8" sourceRef="ChangeSimSaActivation" targetRef="Gateway_0vk75xs" />
    <bpmn:exclusiveGateway id="Gateway_13xuu89" name="From arm reponse if we get simtype=5G" default="Flow_0b2on7r">
      <bpmn:incoming>Flow_0rvnkae</bpmn:incoming>
      <bpmn:outgoing>Flow_1fbnpcw</bpmn:outgoing>
      <bpmn:outgoing>Flow_0b2on7r</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1fbnpcw" name="SimType=5G" sourceRef="Gateway_13xuu89" targetRef="ChangeSimSaActivation">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${activateSaAddon}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0kk2px4">
      <bpmn:incoming>Flow_0cgiynp</bpmn:incoming>
      <bpmn:incoming>Flow_0b2on7r</bpmn:incoming>
      <bpmn:outgoing>Flow_0d70g0t</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0d70g0t" sourceRef="Gateway_0kk2px4" targetRef="orderExecEnd" />
    <bpmn:exclusiveGateway id="Gateway_0vk75xs" default="Flow_0cgiynp">
      <bpmn:incoming>Flow_0yowok8</bpmn:incoming>
      <bpmn:outgoing>Flow_0cgiynp</bpmn:outgoing>
      <bpmn:outgoing>Flow_0wxvzd5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0cgiynp" sourceRef="Gateway_0vk75xs" targetRef="Gateway_0kk2px4" />
    <bpmn:sequenceFlow id="Flow_0b2on7r" sourceRef="Gateway_13xuu89" targetRef="Gateway_0kk2px4" />
    <bpmn:endEvent id="Event_07q3j08">
      <bpmn:incoming>Flow_0wxvzd5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0wxvzd5" name="Failure" sourceRef="Gateway_0vk75xs" targetRef="Event_07q3j08">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_18pj253">
      <bpmn:incoming>Flow_0flta3b</bpmn:incoming>
      <bpmn:incoming>Flow_1h71xrl</bpmn:incoming>
      <bpmn:outgoing>Flow_0xpsgb6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0flta3b" sourceRef="Gateway_0xjjt3c" targetRef="Gateway_18pj253" />
    <bpmn:sequenceFlow id="Flow_0xpsgb6" sourceRef="Gateway_18pj253" targetRef="Gateway_07dpwmm" />
    <bpmn:exclusiveGateway id="Gateway_0ynhr0x" default="Flow_1h71xrl">
      <bpmn:incoming>Flow_17dcvoy</bpmn:incoming>
      <bpmn:outgoing>Flow_1h71xrl</bpmn:outgoing>
      <bpmn:outgoing>Flow_1c3hzro</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_17dcvoy" sourceRef="Gateway_0nd8dc8" targetRef="Gateway_0ynhr0x" />
    <bpmn:sequenceFlow id="Flow_1h71xrl" sourceRef="Gateway_0ynhr0x" targetRef="Gateway_18pj253" />
    <bpmn:callActivity id="ChangeSimNsaCancellation" name="Change Sim Nsa Cancellation" camunda:asyncBefore="true" calledElement="ChangeSimNsaCancellation" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1c3hzro</bpmn:incoming>
      <bpmn:outgoing>Flow_01halg7</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_1c3hzro" sourceRef="Gateway_0ynhr0x" targetRef="ChangeSimNsaCancellation">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${cancelNsaAddon}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_01halg7" sourceRef="ChangeSimNsaCancellation" targetRef="Gateway_0xjjt3c" />
  </bpmn:process>
  <bpmn:message id="Message_097x7pa" name="SOMChangeSimCallback" />
  <bpmn:message id="Message_02m47bb" name="SOMChangeSimCallback" />
  <bpmn:message id="Message_3fi0456" name="Message_3fi0456" />
  <bpmn:message id="Message_3tgsf1s" name="ESBCallBack" />
  <bpmn:message id="Message_1n1rh20" name="ESBEsimBlockCallback" />
  <bpmn:message id="Message_0lrfarc" name="SOMDeactivateSubCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ChangeSim">
      <bpmndi:BPMNShape id="Event_0ipra3g_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0s6c775_di" bpmnElement="SOM_change_SIM">
        <dc:Bounds x="3520" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_11242py_di" bpmnElement="Gateway_11242py" isMarkerVisible="true">
        <dc:Bounds x="3675" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0lbs8hj_di" bpmnElement="Event_0lbs8hj">
        <dc:Bounds x="3772" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0brtnb4_di" bpmnElement="OCS_Change_SIM">
        <dc:Bounds x="4140" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1pwl5pt_di" bpmnElement="Gateway_1pwl5pt" isMarkerVisible="true">
        <dc:Bounds x="4295" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_12muwui_di" bpmnElement="Gateway_12muwui" isMarkerVisible="true">
        <dc:Bounds x="4555" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_17123iw_di" bpmnElement="BS_change_SIM">
        <dc:Bounds x="4400" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0qm1ofy_di" bpmnElement="NMS_SimSwap">
        <dc:Bounds x="4660" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0q2svdr_di" bpmnElement="Event_0q2svdr">
        <dc:Bounds x="4402" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09tz2xv_di" bpmnElement="Event_09tz2xv">
        <dc:Bounds x="4662" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_09qm017_di" bpmnElement="Gateway_09qm017" isMarkerVisible="true">
        <dc:Bounds x="4965" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_092d4pp_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="6252" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1v4jjfp_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="3220" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0w1lgpi_di" bpmnElement="Gateway_0w1lgpi" isMarkerVisible="true">
        <dc:Bounds x="3385" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0f0eaq5_di" bpmnElement="Event_0f0eaq5">
        <dc:Bounds x="3392" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1h5e770_di" bpmnElement="SOMChangeSimCallback">
        <dc:Bounds x="3810" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1e8xdnp_di" bpmnElement="Gateway_1e8xdnp" isMarkerVisible="true">
        <dc:Bounds x="3975" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1gsvqug_di" bpmnElement="Event_1gsvqug">
        <dc:Bounds x="4082" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1pt3213_di" bpmnElement="Gateway_1pt3213" isMarkerVisible="true">
        <dc:Bounds x="1755" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_05kikb3_di" bpmnElement="BookServiceFee">
        <dc:Bounds x="1580" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_14ktkma_di" bpmnElement="Event_14ktkma">
        <dc:Bounds x="1762" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1f0iii0_di" bpmnElement="Gateway_1f0iii0" isMarkerVisible="true">
        <dc:Bounds x="1425" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0zgy5zs_di" bpmnElement="Gateway_0zgy5zs" isMarkerVisible="true">
        <dc:Bounds x="1905" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0o7pr1c_di" bpmnElement="Gateway_0o7pr1c" isMarkerVisible="true">
        <dc:Bounds x="2195" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1hhg262_di" bpmnElement="Gateway_1hhg262" isMarkerVisible="true">
        <dc:Bounds x="2535" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0g96lkw_di" bpmnElement="Gateway_0g96lkw" isMarkerVisible="true">
        <dc:Bounds x="3035" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_087z256_di" bpmnElement="Event_087z256">
        <dc:Bounds x="2542" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0tis5ok_di" bpmnElement="Esb_blockEsim">
        <dc:Bounds x="2330" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0bi9ful_di" bpmnElement="Gateway_0bi9ful" isMarkerVisible="true">
        <dc:Bounds x="2905" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_18i69yr_di" bpmnElement="Event_18i69yr">
        <dc:Bounds x="2912" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0a4d4q5_di" bpmnElement="ESBEsimBlockCallback">
        <dc:Bounds x="2690" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1lak4vz_di" bpmnElement="ESB_fetch">
        <dc:Bounds x="5080" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vn14lo_di" bpmnElement="Gateway_0vn14lo" isMarkerVisible="true">
        <dc:Bounds x="5245" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0953h34_di" bpmnElement="DAG_Esim">
        <dc:Bounds x="5360" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1m8qy34_di" bpmnElement="Event_1m8qy34">
        <dc:Bounds x="5252" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0w73x0l_di" bpmnElement="Gateway_0w73x0l" isMarkerVisible="true">
        <dc:Bounds x="5525" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_10s750p_di" bpmnElement="Gateway_10s750p" isMarkerVisible="true">
        <dc:Bounds x="4815" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ljp1sz_di" bpmnElement="Event_1ljp1sz">
        <dc:Bounds x="4912" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1q0ybfl" bpmnElement="Arm_FetchAssetDetails">
        <dc:Bounds x="250" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0qq9idy" bpmnElement="Gateway_1qd7juv" isMarkerVisible="true">
        <dc:Bounds x="425" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ekqayi" bpmnElement="Event_005bgn6">
        <dc:Bounds x="432" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1nljsk0_di" bpmnElement="BSFetchNsaPlans">
        <dc:Bounds x="680" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0nd8dc8_di" bpmnElement="Gateway_0nd8dc8" isMarkerVisible="true">
        <dc:Bounds x="815" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0xjjt3c_di" bpmnElement="Gateway_0xjjt3c" isMarkerVisible="true">
        <dc:Bounds x="1145" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1wncks3_di" bpmnElement="Event_1wncks3">
        <dc:Bounds x="1152" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_067nxj8_di" bpmnElement="Event_067nxj8">
        <dc:Bounds x="822" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_19sddny_di" bpmnElement="Gateway_19sddny" isMarkerVisible="true">
        <dc:Bounds x="515" y="175" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="502" y="232" width="85" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_07dpwmm_di" bpmnElement="Gateway_07dpwmm" isMarkerVisible="true">
        <dc:Bounds x="1325" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0qjmzz7" bpmnElement="ChangeSimSaActivation">
        <dc:Bounds x="5770" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1b9jod0" bpmnElement="Gateway_13xuu89" isMarkerVisible="true">
        <dc:Bounds x="5655" y="175" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5642" y="232" width="85" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1uv4y8i" bpmnElement="Gateway_0kk2px4" isMarkerVisible="true">
        <dc:Bounds x="6055" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_12b9i7b" bpmnElement="Gateway_0vk75xs" isMarkerVisible="true">
        <dc:Bounds x="5945" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0s9fawb" bpmnElement="Event_07q3j08">
        <dc:Bounds x="5952" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_18pj253_di" bpmnElement="Gateway_18pj253" isMarkerVisible="true">
        <dc:Bounds x="1225" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ynhr0x_di" bpmnElement="Gateway_0ynhr0x" isMarkerVisible="true">
        <dc:Bounds x="905" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_06g4cmu_di" bpmnElement="ChangeSimNsaCancellation">
        <dc:Bounds x="1000" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0vekyb1_di" bpmnElement="Flow_0vekyb1">
        <di:waypoint x="3620" y="200" />
        <di:waypoint x="3675" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xx5wld_di" bpmnElement="Flow_0xx5wld">
        <di:waypoint x="3700" y="225" />
        <di:waypoint x="3700" y="310" />
        <di:waypoint x="3772" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3698" y="265" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_142allt_di" bpmnElement="Flow_142allt">
        <di:waypoint x="4240" y="200" />
        <di:waypoint x="4295" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ic6nwu_di" bpmnElement="Flow_1ic6nwu">
        <di:waypoint x="4345" y="200" />
        <di:waypoint x="4400" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wkpxbh_di" bpmnElement="Flow_1wkpxbh">
        <di:waypoint x="4500" y="200" />
        <di:waypoint x="4555" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tdbzk9_di" bpmnElement="Flow_1tdbzk9">
        <di:waypoint x="4605" y="200" />
        <di:waypoint x="4660" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kwbuv9_di" bpmnElement="Flow_1kwbuv9">
        <di:waypoint x="4320" y="225" />
        <di:waypoint x="4320" y="310" />
        <di:waypoint x="4402" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4318" y="265" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r091me_di" bpmnElement="Flow_1r091me">
        <di:waypoint x="4580" y="225" />
        <di:waypoint x="4580" y="310" />
        <di:waypoint x="4662" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4578" y="265" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07h0uoj_di" bpmnElement="Flow_07h0uoj">
        <di:waypoint x="3320" y="200" />
        <di:waypoint x="3385" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bj4wjv_di" bpmnElement="Flow_1bj4wjv">
        <di:waypoint x="3435" y="200" />
        <di:waypoint x="3520" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16x0odp_di" bpmnElement="Flow_16x0odp">
        <di:waypoint x="3410" y="225" />
        <di:waypoint x="3410" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3408" y="255" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_003cyt6_di" bpmnElement="Flow_003cyt6">
        <di:waypoint x="3725" y="200" />
        <di:waypoint x="3810" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gdl6nb_di" bpmnElement="Flow_1gdl6nb">
        <di:waypoint x="3910" y="200" />
        <di:waypoint x="3975" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_157awwf_di" bpmnElement="Flow_157awwf">
        <di:waypoint x="4025" y="200" />
        <di:waypoint x="4140" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1biwy1s_di" bpmnElement="Flow_1biwy1s">
        <di:waypoint x="4000" y="225" />
        <di:waypoint x="4000" y="310" />
        <di:waypoint x="4082" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3999" y="265" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w55keh_di" bpmnElement="Flow_1w55keh">
        <di:waypoint x="1680" y="200" />
        <di:waypoint x="1755" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_115kl92_di" bpmnElement="Flow_115kl92">
        <di:waypoint x="1780" y="225" />
        <di:waypoint x="1780" y="292" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tchw38_di" bpmnElement="Flow_0tchw38">
        <di:waypoint x="1475" y="200" />
        <di:waypoint x="1580" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1521" y="182" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17dy2n2_di" bpmnElement="Flow_17dy2n2">
        <di:waypoint x="1805" y="200" />
        <di:waypoint x="1905" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1837" y="182" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nbqnmz_di" bpmnElement="Flow_0nbqnmz">
        <di:waypoint x="1450" y="175" />
        <di:waypoint x="1450" y="100" />
        <di:waypoint x="1930" y="100" />
        <di:waypoint x="1930" y="175" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1685" y="82" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xj3wcy_di" bpmnElement="Flow_0xj3wcy">
        <di:waypoint x="188" y="200" />
        <di:waypoint x="250" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xnp3wr_di" bpmnElement="Flow_0xnp3wr">
        <di:waypoint x="1955" y="200" />
        <di:waypoint x="2195" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0onpo2m_di" bpmnElement="Flow_0onpo2m">
        <di:waypoint x="2245" y="200" />
        <di:waypoint x="2330" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2275" y="182" width="29" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1desy2w_di" bpmnElement="Flow_1desy2w">
        <di:waypoint x="2430" y="200" />
        <di:waypoint x="2535" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g2f7wa_di" bpmnElement="Flow_1g2f7wa">
        <di:waypoint x="2220" y="175" />
        <di:waypoint x="2220" y="100" />
        <di:waypoint x="3060" y="100" />
        <di:waypoint x="3060" y="175" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2610" y="82" width="61" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_036d8zo_di" bpmnElement="Flow_036d8zo">
        <di:waypoint x="2560" y="225" />
        <di:waypoint x="2560" y="292" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1agrdxe_di" bpmnElement="Flow_1agrdxe">
        <di:waypoint x="2585" y="200" />
        <di:waypoint x="2690" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00yznh5_di" bpmnElement="Flow_00yznh5">
        <di:waypoint x="2790" y="200" />
        <di:waypoint x="2905" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ehb4tj_di" bpmnElement="Flow_1ehb4tj">
        <di:waypoint x="2955" y="200" />
        <di:waypoint x="3035" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u8bm20_di" bpmnElement="Flow_0u8bm20">
        <di:waypoint x="2930" y="225" />
        <di:waypoint x="2930" y="292" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kpyymf_di" bpmnElement="Flow_0kpyymf">
        <di:waypoint x="5015" y="200" />
        <di:waypoint x="5080" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5022" y="182" width="57" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0k4vrvn_di" bpmnElement="Flow_0k4vrvn">
        <di:waypoint x="5180" y="200" />
        <di:waypoint x="5245" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_085zkua_di" bpmnElement="Flow_085zkua">
        <di:waypoint x="5295" y="200" />
        <di:waypoint x="5360" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yn3ctn_di" bpmnElement="Flow_1yn3ctn">
        <di:waypoint x="5270" y="225" />
        <di:waypoint x="5270" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5268" y="256" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c89w7z_di" bpmnElement="Flow_1c89w7z">
        <di:waypoint x="5460" y="200" />
        <di:waypoint x="5525" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rvnkae_di" bpmnElement="Flow_0rvnkae">
        <di:waypoint x="5575" y="200" />
        <di:waypoint x="5655" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02mh48o_di" bpmnElement="Flow_02mh48o">
        <di:waypoint x="4990" y="175" />
        <di:waypoint x="4990" y="100" />
        <di:waypoint x="5550" y="100" />
        <di:waypoint x="5550" y="175" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ytzor1_di" bpmnElement="Flow_0ytzor1">
        <di:waypoint x="4760" y="200" />
        <di:waypoint x="4815" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14e8mfh_di" bpmnElement="Flow_14e8mfh">
        <di:waypoint x="4865" y="200" />
        <di:waypoint x="4965" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s0bfpq_di" bpmnElement="Flow_1s0bfpq">
        <di:waypoint x="4840" y="225" />
        <di:waypoint x="4840" y="310" />
        <di:waypoint x="4912" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4838" y="265" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mf6q6n_di" bpmnElement="Flow_1mf6q6n">
        <di:waypoint x="350" y="200" />
        <di:waypoint x="425" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_181w14t_di" bpmnElement="Flow_181w14t">
        <di:waypoint x="450" y="225" />
        <di:waypoint x="450" y="292" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0joj8op_di" bpmnElement="Flow_0joj8op">
        <di:waypoint x="1170" y="225" />
        <di:waypoint x="1170" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1172" y="236" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ffpb9a_di" bpmnElement="Flow_1ffpb9a">
        <di:waypoint x="840" y="225" />
        <di:waypoint x="840" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="842" y="245" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0q5x43r_di" bpmnElement="Flow_0q5x43r">
        <di:waypoint x="475" y="200" />
        <di:waypoint x="515" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fn05aw_di" bpmnElement="Flow_0fn05aw">
        <di:waypoint x="565" y="200" />
        <di:waypoint x="680" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="581" y="182" width="63" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rfgzkk_di" bpmnElement="Flow_0rfgzkk">
        <di:waypoint x="1375" y="200" />
        <di:waypoint x="1425" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dn1ub4_di" bpmnElement="Flow_1dn1ub4">
        <di:waypoint x="540" y="175" />
        <di:waypoint x="540" y="90" />
        <di:waypoint x="1350" y="90" />
        <di:waypoint x="1350" y="175" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="939" y="72" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ygh3wi_di" bpmnElement="Flow_0ygh3wi">
        <di:waypoint x="3085" y="200" />
        <di:waypoint x="3220" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jv7krb_di" bpmnElement="Flow_1jv7krb">
        <di:waypoint x="780" y="200" />
        <di:waypoint x="815" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yowok8_di" bpmnElement="Flow_0yowok8">
        <di:waypoint x="5870" y="200" />
        <di:waypoint x="5945" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fbnpcw_di" bpmnElement="Flow_1fbnpcw">
        <di:waypoint x="5705" y="200" />
        <di:waypoint x="5770" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5698" y="173" width="64" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d70g0t_di" bpmnElement="Flow_0d70g0t">
        <di:waypoint x="6105" y="200" />
        <di:waypoint x="6252" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cgiynp_di" bpmnElement="Flow_0cgiynp">
        <di:waypoint x="5995" y="200" />
        <di:waypoint x="6055" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b2on7r_di" bpmnElement="Flow_0b2on7r">
        <di:waypoint x="5680" y="175" />
        <di:waypoint x="5680" y="90" />
        <di:waypoint x="6080" y="90" />
        <di:waypoint x="6080" y="175" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wxvzd5_di" bpmnElement="Flow_0wxvzd5">
        <di:waypoint x="5970" y="225" />
        <di:waypoint x="5970" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5968" y="256" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0flta3b_di" bpmnElement="Flow_0flta3b">
        <di:waypoint x="1195" y="200" />
        <di:waypoint x="1225" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xpsgb6_di" bpmnElement="Flow_0xpsgb6">
        <di:waypoint x="1275" y="200" />
        <di:waypoint x="1325" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17dcvoy_di" bpmnElement="Flow_17dcvoy">
        <di:waypoint x="865" y="200" />
        <di:waypoint x="905" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h71xrl_di" bpmnElement="Flow_1h71xrl">
        <di:waypoint x="930" y="175" />
        <di:waypoint x="930" y="120" />
        <di:waypoint x="1250" y="120" />
        <di:waypoint x="1250" y="175" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c3hzro_di" bpmnElement="Flow_1c3hzro">
        <di:waypoint x="955" y="200" />
        <di:waypoint x="1000" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01halg7_di" bpmnElement="Flow_01halg7">
        <di:waypoint x="1100" y="200" />
        <di:waypoint x="1145" y="200" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
