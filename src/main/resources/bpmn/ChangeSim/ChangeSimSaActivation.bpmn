<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0">
  <bpmn:process id="ChangeSimSaActivation" name="ChangeSimSaActivation" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:serviceTask id="SOMSaActivation" name="SOM_5G SaActivation" camunda:asyncBefore="true" camunda:delegateExpression="${somSaActivation}">
      <bpmn:incoming>Flow_1v7ftqy</bpmn:incoming>
      <bpmn:outgoing>Flow_1ro00ob</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0h19of5" default="Flow_1yv0b4u">
      <bpmn:incoming>Flow_1ro00ob</bpmn:incoming>
      <bpmn:outgoing>Flow_1yv0b4u</bpmn:outgoing>
      <bpmn:outgoing>Flow_1a994d4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ro00ob" sourceRef="SOMSaActivation" targetRef="Gateway_0h19of5" />
    <bpmn:serviceTask id="OCSSaActivation" name="OCS_5GSaActivation" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1fv4zed</bpmn:incoming>
      <bpmn:outgoing>Flow_19do09t</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1yv0b4u" sourceRef="Gateway_0h19of5" targetRef="SOMCallback" />
    <bpmn:exclusiveGateway id="Gateway_01h4p1x" default="Flow_15fvdxg">
      <bpmn:incoming>Flow_19do09t</bpmn:incoming>
      <bpmn:outgoing>Flow_0vtc983</bpmn:outgoing>
      <bpmn:outgoing>Flow_15fvdxg</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_19do09t" sourceRef="OCSSaActivation" targetRef="Gateway_01h4p1x" />
    <bpmn:receiveTask id="SOMCallback" name="SOM-5G_ServiceActivationCallback" camunda:asyncBefore="true" messageRef="Message_39561of">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOMSaActivation</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOMSaActivation" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1yv0b4u</bpmn:incoming>
      <bpmn:outgoing>Flow_0nq77aw</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_0nq77aw" sourceRef="SOMCallback" targetRef="Gateway_0u2ik93" />
    <bpmn:exclusiveGateway id="Gateway_0u2ik93" default="Flow_1fv4zed">
      <bpmn:incoming>Flow_0nq77aw</bpmn:incoming>
      <bpmn:outgoing>Flow_1fv4zed</bpmn:outgoing>
      <bpmn:outgoing>Flow_02v5yfm</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1fv4zed" sourceRef="Gateway_0u2ik93" targetRef="OCSSaActivation" />
    <bpmn:endEvent id="Event_11zg8zz">
      <bpmn:incoming>Flow_1a994d4</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1a994d4" name="Failure" sourceRef="Gateway_0h19of5" targetRef="Event_11zg8zz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1jqi5pq">
      <bpmn:incoming>Flow_02v5yfm</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_02v5yfm" name="Failure" sourceRef="Gateway_0u2ik93" targetRef="Event_1jqi5pq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_12b7q50">
      <bpmn:incoming>Flow_0vtc983</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0vtc983" name="Failure" sourceRef="Gateway_01h4p1x" targetRef="Event_12b7q50">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0d7ediz" default="Flow_1v7ftqy">
      <bpmn:incoming>Flow_12qnvwo</bpmn:incoming>
      <bpmn:outgoing>Flow_1v7ftqy</bpmn:outgoing>
      <bpmn:outgoing>Flow_049pzs8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1v7ftqy" sourceRef="Gateway_0d7ediz" targetRef="SOMSaActivation" />
    <bpmn:endEvent id="Event_162mjuq">
      <bpmn:incoming>Flow_049pzs8</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_049pzs8" name="Failure" sourceRef="Gateway_0d7ediz" targetRef="Event_162mjuq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSSaActivation" name="BS_5G_SaActivation" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_12tieaa</bpmn:incoming>
      <bpmn:outgoing>Flow_12qnvwo</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_12qnvwo" sourceRef="BSSaActivation" targetRef="Gateway_0d7ediz" />
    <bpmn:startEvent id="Event_0g4m2j6">
      <bpmn:outgoing>Flow_1hwm9xz</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1hwm9xz" sourceRef="Event_0g4m2j6" targetRef="UPCFetchPlanDetails" />
    <bpmn:exclusiveGateway id="Gateway_0auryez" default="Flow_12tieaa">
      <bpmn:incoming>Flow_03a4h6h</bpmn:incoming>
      <bpmn:outgoing>Flow_12tieaa</bpmn:outgoing>
      <bpmn:outgoing>Flow_1g0syfu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_12tieaa" sourceRef="Gateway_0auryez" targetRef="BSSaActivation" />
    <bpmn:endEvent id="Event_1efxorr">
      <bpmn:incoming>Flow_1g0syfu</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1g0syfu" name="Failure" sourceRef="Gateway_0auryez" targetRef="Event_1efxorr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="UPCFetchPlanDetails" name="UPC_FetchPlanDetails" camunda:asyncBefore="true" camunda:delegateExpression="${fetchSaPlanDetails}">
      <bpmn:incoming>Flow_1hwm9xz</bpmn:incoming>
      <bpmn:outgoing>Flow_03a4h6h</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_03a4h6h" sourceRef="UPCFetchPlanDetails" targetRef="Gateway_0auryez" />
    <bpmn:endEvent id="Event_05q2nqo">
      <bpmn:incoming>Flow_15fvdxg</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_15fvdxg" sourceRef="Gateway_01h4p1x" targetRef="Event_05q2nqo" />
  </bpmn:process>
  <bpmn:message id="Message_097x7pa" name="SOMChangeSimCallback" />
  <bpmn:message id="Message_02m47bb" name="SOMChangeSimCallback" />
  <bpmn:message id="Message_3fi0456" name="Message_3fi0456" />
  <bpmn:message id="Message_3tgsf1s" name="ESBCallBack" />
  <bpmn:message id="Message_1n1rh20" name="ESBEsimBlockCallback" />
  <bpmn:message id="Message_1rpltev" name="Message_1rpltev" />
  <bpmn:message id="Message_39561of" name="SOMCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ChangeSimSaActivation">
      <bpmndi:BPMNShape id="BPMNShape_0qfny2l" bpmnElement="SOMSaActivation">
        <dc:Bounds x="930" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_14kzu7b" bpmnElement="Gateway_0h19of5" isMarkerVisible="true">
        <dc:Bounds x="1125" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0undaz6" bpmnElement="OCSSaActivation">
        <dc:Bounds x="1650" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0c9nir6" bpmnElement="Gateway_01h4p1x" isMarkerVisible="true">
        <dc:Bounds x="1825" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1lh61sk" bpmnElement="SOMCallback">
        <dc:Bounds x="1280" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0vwbx04" bpmnElement="Gateway_0u2ik93" isMarkerVisible="true">
        <dc:Bounds x="1485" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0uo3h1z" bpmnElement="Event_11zg8zz">
        <dc:Bounds x="1132" y="260" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1xv5j47" bpmnElement="Event_1jqi5pq">
        <dc:Bounds x="1492" y="260" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ga8qvq" bpmnElement="Event_12b7q50">
        <dc:Bounds x="1832" y="260" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_14mymri" bpmnElement="Gateway_0d7ediz" isMarkerVisible="true">
        <dc:Bounds x="805" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0fof1l9" bpmnElement="Event_162mjuq">
        <dc:Bounds x="812" y="260" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0x2mp10" bpmnElement="BSSaActivation">
        <dc:Bounds x="650" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0g4m2j6_di" bpmnElement="Event_0g4m2j6">
        <dc:Bounds x="152" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_14ecpha" bpmnElement="Gateway_0auryez" isMarkerVisible="true">
        <dc:Bounds x="515" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0sgzap7" bpmnElement="Event_1efxorr">
        <dc:Bounds x="522" y="260" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_025teu2" bpmnElement="UPCFetchPlanDetails">
        <dc:Bounds x="300" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_05q2nqo_di" bpmnElement="Event_05q2nqo">
        <dc:Bounds x="2042" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1ro00ob_di" bpmnElement="Flow_1ro00ob">
        <di:waypoint x="1030" y="120" />
        <di:waypoint x="1125" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yv0b4u_di" bpmnElement="Flow_1yv0b4u">
        <di:waypoint x="1175" y="120" />
        <di:waypoint x="1280" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19do09t_di" bpmnElement="Flow_19do09t">
        <di:waypoint x="1750" y="120" />
        <di:waypoint x="1825" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nq77aw_di" bpmnElement="Flow_0nq77aw">
        <di:waypoint x="1380" y="120" />
        <di:waypoint x="1485" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fv4zed_di" bpmnElement="Flow_1fv4zed">
        <di:waypoint x="1535" y="120" />
        <di:waypoint x="1650" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a994d4_di" bpmnElement="Flow_1a994d4">
        <di:waypoint x="1150" y="145" />
        <di:waypoint x="1150" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1162" y="163" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02v5yfm_di" bpmnElement="Flow_02v5yfm">
        <di:waypoint x="1510" y="145" />
        <di:waypoint x="1510" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1532" y="163" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vtc983_di" bpmnElement="Flow_0vtc983">
        <di:waypoint x="1850" y="145" />
        <di:waypoint x="1850" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1862" y="163" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1v7ftqy_di" bpmnElement="Flow_1v7ftqy">
        <di:waypoint x="855" y="120" />
        <di:waypoint x="930" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_049pzs8_di" bpmnElement="Flow_049pzs8">
        <di:waypoint x="830" y="145" />
        <di:waypoint x="830" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="828" y="199" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12qnvwo_di" bpmnElement="Flow_12qnvwo">
        <di:waypoint x="750" y="120" />
        <di:waypoint x="805" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hwm9xz_di" bpmnElement="Flow_1hwm9xz">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="300" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12tieaa_di" bpmnElement="Flow_12tieaa">
        <di:waypoint x="565" y="120" />
        <di:waypoint x="650" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g0syfu_di" bpmnElement="Flow_1g0syfu">
        <di:waypoint x="540" y="145" />
        <di:waypoint x="540" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="538" y="200" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03a4h6h_di" bpmnElement="Flow_03a4h6h">
        <di:waypoint x="400" y="120" />
        <di:waypoint x="515" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15fvdxg_di" bpmnElement="Flow_15fvdxg">
        <di:waypoint x="1875" y="120" />
        <di:waypoint x="2042" y="120" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
