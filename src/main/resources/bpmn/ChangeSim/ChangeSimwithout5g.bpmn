<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.28.0">
  <bpmn:process id="ChangeSimbkp" name="ChangeSim" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0xj3wcy</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="SOM_change_SIM" name="SOM change SIM" camunda:asyncBefore="true" camunda:delegateExpression="${somChangeSim}">
      <bpmn:incoming>Flow_1bj4wjv</bpmn:incoming>
      <bpmn:outgoing>Flow_0vekyb1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_11242py" default="Flow_003cyt6">
      <bpmn:incoming>Flow_0vekyb1</bpmn:incoming>
      <bpmn:outgoing>Flow_0xx5wld</bpmn:outgoing>
      <bpmn:outgoing>Flow_003cyt6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0vekyb1" sourceRef="SOM_change_SIM" targetRef="Gateway_11242py" />
    <bpmn:endEvent id="Event_0lbs8hj">
      <bpmn:incoming>Flow_0xx5wld</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0xx5wld" name="Failure" sourceRef="Gateway_11242py" targetRef="Event_0lbs8hj">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="OCS_Change_SIM" name="OCS Change SIM" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_157awwf</bpmn:incoming>
      <bpmn:outgoing>Flow_142allt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1pwl5pt" default="Flow_1ic6nwu">
      <bpmn:incoming>Flow_142allt</bpmn:incoming>
      <bpmn:outgoing>Flow_1ic6nwu</bpmn:outgoing>
      <bpmn:outgoing>Flow_1kwbuv9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_142allt" sourceRef="OCS_Change_SIM" targetRef="Gateway_1pwl5pt" />
    <bpmn:sequenceFlow id="Flow_1ic6nwu" sourceRef="Gateway_1pwl5pt" targetRef="BS_change_SIM" />
    <bpmn:exclusiveGateway id="Gateway_12muwui" default="Flow_1tdbzk9">
      <bpmn:incoming>Flow_1wkpxbh</bpmn:incoming>
      <bpmn:outgoing>Flow_1tdbzk9</bpmn:outgoing>
      <bpmn:outgoing>Flow_1r091me</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1wkpxbh" sourceRef="BS_change_SIM" targetRef="Gateway_12muwui" />
    <bpmn:serviceTask id="BS_change_SIM" name="BS change SIM" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1ic6nwu</bpmn:incoming>
      <bpmn:outgoing>Flow_1wkpxbh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1tdbzk9" sourceRef="Gateway_12muwui" targetRef="NMS_SimSwap" />
    <bpmn:serviceTask id="NMS_SimSwap" name="NMS Sim Swap " camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1tdbzk9</bpmn:incoming>
      <bpmn:outgoing>Flow_0ytzor1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0q2svdr">
      <bpmn:incoming>Flow_1kwbuv9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1kwbuv9" name="Failure" sourceRef="Gateway_1pwl5pt" targetRef="Event_0q2svdr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_09tz2xv">
      <bpmn:incoming>Flow_1r091me</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1r091me" name="Failure" sourceRef="Gateway_12muwui" targetRef="Event_09tz2xv">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_09qm017" default="Flow_02mh48o">
      <bpmn:incoming>Flow_14e8mfh</bpmn:incoming>
      <bpmn:outgoing>Flow_0kpyymf</bpmn:outgoing>
      <bpmn:outgoing>Flow_02mh48o</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0rvnkae</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch services" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1uc2fos</bpmn:incoming>
      <bpmn:outgoing>Flow_07h0uoj</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0w1lgpi" default="Flow_1bj4wjv">
      <bpmn:incoming>Flow_07h0uoj</bpmn:incoming>
      <bpmn:outgoing>Flow_1bj4wjv</bpmn:outgoing>
      <bpmn:outgoing>Flow_16x0odp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_07h0uoj" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_0w1lgpi" />
    <bpmn:sequenceFlow id="Flow_1bj4wjv" sourceRef="Gateway_0w1lgpi" targetRef="SOM_change_SIM" />
    <bpmn:endEvent id="Event_0f0eaq5">
      <bpmn:incoming>Flow_16x0odp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_16x0odp" name="Failure" sourceRef="Gateway_0w1lgpi" targetRef="Event_0f0eaq5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="SOMChangeSimCallback" name="SOM Callback" camunda:asyncBefore="true" messageRef="Message_02m47bb">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOM_change_SIM</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOM_change_SIM" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_003cyt6</bpmn:incoming>
      <bpmn:outgoing>Flow_1gdl6nb</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_003cyt6" sourceRef="Gateway_11242py" targetRef="SOMChangeSimCallback" />
    <bpmn:exclusiveGateway id="Gateway_1e8xdnp" default="Flow_157awwf">
      <bpmn:incoming>Flow_1gdl6nb</bpmn:incoming>
      <bpmn:outgoing>Flow_157awwf</bpmn:outgoing>
      <bpmn:outgoing>Flow_1biwy1s</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1gdl6nb" sourceRef="SOMChangeSimCallback" targetRef="Gateway_1e8xdnp" />
    <bpmn:sequenceFlow id="Flow_157awwf" sourceRef="Gateway_1e8xdnp" targetRef="OCS_Change_SIM" />
    <bpmn:endEvent id="Event_1gsvqug">
      <bpmn:incoming>Flow_1biwy1s</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1biwy1s" name="Failure" sourceRef="Gateway_1e8xdnp" targetRef="Event_1gsvqug">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1pt3213" default="Flow_17dy2n2">
      <bpmn:incoming>Flow_1w55keh</bpmn:incoming>
      <bpmn:outgoing>Flow_115kl92</bpmn:outgoing>
      <bpmn:outgoing>Flow_17dy2n2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1w55keh" sourceRef="BookServiceFee" targetRef="Gateway_1pt3213" />
    <bpmn:serviceTask id="BookServiceFee" name="Book Service Fee" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0tchw38</bpmn:incoming>
      <bpmn:outgoing>Flow_1w55keh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_14ktkma">
      <bpmn:incoming>Flow_115kl92</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_115kl92" sourceRef="Gateway_1pt3213" targetRef="Event_14ktkma">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1f0iii0" default="Flow_0nbqnmz">
      <bpmn:incoming>Flow_0tyspjy</bpmn:incoming>
      <bpmn:outgoing>Flow_0tchw38</bpmn:outgoing>
      <bpmn:outgoing>Flow_0nbqnmz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0tchw38" name="yes" sourceRef="Gateway_1f0iii0" targetRef="BookServiceFee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${additionalCharges}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0zgy5zs">
      <bpmn:incoming>Flow_17dy2n2</bpmn:incoming>
      <bpmn:incoming>Flow_0nbqnmz</bpmn:incoming>
      <bpmn:outgoing>Flow_0xnp3wr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_17dy2n2" name="success" sourceRef="Gateway_1pt3213" targetRef="Gateway_0zgy5zs" />
    <bpmn:sequenceFlow id="Flow_0nbqnmz" name="no" sourceRef="Gateway_1f0iii0" targetRef="Gateway_0zgy5zs" />
    <bpmn:sequenceFlow id="Flow_0xj3wcy" sourceRef="orderExecStart" targetRef="Arm_FetchAssetDetails" />
    <bpmn:exclusiveGateway id="Gateway_0o7pr1c" default="Flow_1g2f7wa">
      <bpmn:incoming>Flow_0xnp3wr</bpmn:incoming>
      <bpmn:outgoing>Flow_0onpo2m</bpmn:outgoing>
      <bpmn:outgoing>Flow_1g2f7wa</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0xnp3wr" sourceRef="Gateway_0zgy5zs" targetRef="Gateway_0o7pr1c" />
    <bpmn:sequenceFlow id="Flow_0onpo2m" name="e-Sim" sourceRef="Gateway_0o7pr1c" targetRef="Esb_blockEsim">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${eSIMCallReq}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1hhg262" default="Flow_1agrdxe">
      <bpmn:incoming>Flow_1desy2w</bpmn:incoming>
      <bpmn:outgoing>Flow_036d8zo</bpmn:outgoing>
      <bpmn:outgoing>Flow_1agrdxe</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1desy2w" sourceRef="Esb_blockEsim" targetRef="Gateway_1hhg262" />
    <bpmn:exclusiveGateway id="Gateway_0g96lkw">
      <bpmn:incoming>Flow_1g2f7wa</bpmn:incoming>
      <bpmn:incoming>Flow_1ehb4tj</bpmn:incoming>
      <bpmn:outgoing>Flow_1uc2fos</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1uc2fos" sourceRef="Gateway_0g96lkw" targetRef="SOMFetchServiceRegistry" />
    <bpmn:sequenceFlow id="Flow_1g2f7wa" name="PhysicalSim" sourceRef="Gateway_0o7pr1c" targetRef="Gateway_0g96lkw" />
    <bpmn:endEvent id="Event_087z256">
      <bpmn:incoming>Flow_036d8zo</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_036d8zo" sourceRef="Gateway_1hhg262" targetRef="Event_087z256">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Esb_blockEsim" name="ESB Block eSim" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0onpo2m</bpmn:incoming>
      <bpmn:outgoing>Flow_1desy2w</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1agrdxe" sourceRef="Gateway_1hhg262" targetRef="ESBEsimBlockCallback" />
    <bpmn:exclusiveGateway id="Gateway_0bi9ful" default="Flow_1ehb4tj">
      <bpmn:incoming>Flow_00yznh5</bpmn:incoming>
      <bpmn:outgoing>Flow_1ehb4tj</bpmn:outgoing>
      <bpmn:outgoing>Flow_0u8bm20</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_00yznh5" sourceRef="ESBEsimBlockCallback" targetRef="Gateway_0bi9ful" />
    <bpmn:sequenceFlow id="Flow_1ehb4tj" sourceRef="Gateway_0bi9ful" targetRef="Gateway_0g96lkw" />
    <bpmn:endEvent id="Event_18i69yr">
      <bpmn:incoming>Flow_0u8bm20</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0u8bm20" sourceRef="Gateway_0bi9ful" targetRef="Event_18i69yr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="ESBEsimBlockCallback" name="ESB BlockSim CallBack" camunda:asyncBefore="true" messageRef="Message_1n1rh20">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="Esb_blockEsim" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">Esb_blockEsim</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1agrdxe</bpmn:incoming>
      <bpmn:outgoing>Flow_00yznh5</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_0kpyymf" name="eSIMisTrue" sourceRef="Gateway_09qm017" targetRef="ESB_fetch">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${eSIMCallReq}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="ESB_fetch" name=" Fetch Esim status From ESB" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0kpyymf</bpmn:incoming>
      <bpmn:outgoing>Flow_0k4vrvn</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0vn14lo" default="Flow_085zkua">
      <bpmn:incoming>Flow_0k4vrvn</bpmn:incoming>
      <bpmn:outgoing>Flow_085zkua</bpmn:outgoing>
      <bpmn:outgoing>Flow_1yn3ctn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0k4vrvn" sourceRef="ESB_fetch" targetRef="Gateway_0vn14lo" />
    <bpmn:sequenceFlow id="Flow_085zkua" sourceRef="Gateway_0vn14lo" targetRef="DAG_Esim" />
    <bpmn:serviceTask id="DAG_Esim" name="SIM Status Sent to DAG" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_085zkua</bpmn:incoming>
      <bpmn:outgoing>Flow_1c89w7z</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1m8qy34">
      <bpmn:incoming>Flow_1yn3ctn</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1yn3ctn" name="Failure" sourceRef="Gateway_0vn14lo" targetRef="Event_1m8qy34">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0w73x0l">
      <bpmn:incoming>Flow_1c89w7z</bpmn:incoming>
      <bpmn:incoming>Flow_02mh48o</bpmn:incoming>
      <bpmn:outgoing>Flow_0rvnkae</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1c89w7z" sourceRef="DAG_Esim" targetRef="Gateway_0w73x0l" />
    <bpmn:sequenceFlow id="Flow_0rvnkae" sourceRef="Gateway_0w73x0l" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_02mh48o" sourceRef="Gateway_09qm017" targetRef="Gateway_0w73x0l" />
    <bpmn:exclusiveGateway id="Gateway_10s750p" default="Flow_14e8mfh">
      <bpmn:incoming>Flow_0ytzor1</bpmn:incoming>
      <bpmn:outgoing>Flow_14e8mfh</bpmn:outgoing>
      <bpmn:outgoing>Flow_1s0bfpq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ytzor1" sourceRef="NMS_SimSwap" targetRef="Gateway_10s750p" />
    <bpmn:sequenceFlow id="Flow_14e8mfh" sourceRef="Gateway_10s750p" targetRef="Gateway_09qm017" />
    <bpmn:endEvent id="Event_1ljp1sz">
      <bpmn:incoming>Flow_1s0bfpq</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1s0bfpq" name="Failure" sourceRef="Gateway_10s750p" targetRef="Event_1ljp1sz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Arm_FetchAssetDetails" name="Arm Fetch Asset Details" camunda:asyncBefore="true" camunda:delegateExpression="${fetchDetailsFromArm}">
      <bpmn:incoming>Flow_0xj3wcy</bpmn:incoming>
      <bpmn:outgoing>Flow_1mf6q6n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1mf6q6n" sourceRef="Arm_FetchAssetDetails" targetRef="Gateway_1qd7juv" />
    <bpmn:exclusiveGateway id="Gateway_1qd7juv" default="Flow_0tyspjy">
      <bpmn:incoming>Flow_1mf6q6n</bpmn:incoming>
      <bpmn:outgoing>Flow_0tyspjy</bpmn:outgoing>
      <bpmn:outgoing>Flow_181w14t</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0tyspjy" name="success" sourceRef="Gateway_1qd7juv" targetRef="Gateway_1f0iii0" />
    <bpmn:endEvent id="Event_005bgn6">
      <bpmn:incoming>Flow_181w14t</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_181w14t" sourceRef="Gateway_1qd7juv" targetRef="Event_005bgn6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_097x7pa" name="SOMChangeSimCallback" />
  <bpmn:message id="Message_02m47bb" name="SOMChangeSimCallback" />
  <bpmn:message id="Message_3fi0456" name="Message_3fi0456" />
  <bpmn:message id="Message_3tgsf1s" name="ESBCallBack" />
  <bpmn:message id="Message_1n1rh20" name="ESBEsimBlockCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ChangeSimbkp">
      <bpmndi:BPMNShape id="Event_0ipra3g_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0s6c775_di" bpmnElement="SOM_change_SIM">
        <dc:Bounds x="2190" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_11242py_di" bpmnElement="Gateway_11242py" isMarkerVisible="true">
        <dc:Bounds x="2345" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0lbs8hj_di" bpmnElement="Event_0lbs8hj">
        <dc:Bounds x="2442" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0brtnb4_di" bpmnElement="OCS_Change_SIM">
        <dc:Bounds x="2810" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1pwl5pt_di" bpmnElement="Gateway_1pwl5pt" isMarkerVisible="true">
        <dc:Bounds x="2965" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_12muwui_di" bpmnElement="Gateway_12muwui" isMarkerVisible="true">
        <dc:Bounds x="3225" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_17123iw_di" bpmnElement="BS_change_SIM">
        <dc:Bounds x="3070" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0qm1ofy_di" bpmnElement="NMS_SimSwap">
        <dc:Bounds x="3330" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0q2svdr_di" bpmnElement="Event_0q2svdr">
        <dc:Bounds x="3072" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09tz2xv_di" bpmnElement="Event_09tz2xv">
        <dc:Bounds x="3332" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_09qm017_di" bpmnElement="Gateway_09qm017" isMarkerVisible="true">
        <dc:Bounds x="3635" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_092d4pp_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="4322" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1v4jjfp_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="1880" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0w1lgpi_di" bpmnElement="Gateway_0w1lgpi" isMarkerVisible="true">
        <dc:Bounds x="2055" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0f0eaq5_di" bpmnElement="Event_0f0eaq5">
        <dc:Bounds x="2192" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1h5e770_di" bpmnElement="SOMChangeSimCallback">
        <dc:Bounds x="2480" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1e8xdnp_di" bpmnElement="Gateway_1e8xdnp" isMarkerVisible="true">
        <dc:Bounds x="2645" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1gsvqug_di" bpmnElement="Event_1gsvqug">
        <dc:Bounds x="2752" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1pt3213_di" bpmnElement="Gateway_1pt3213" isMarkerVisible="true">
        <dc:Bounds x="895" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_05kikb3_di" bpmnElement="BookServiceFee">
        <dc:Bounds x="730" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_14ktkma_di" bpmnElement="Event_14ktkma">
        <dc:Bounds x="952" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1f0iii0_di" bpmnElement="Gateway_1f0iii0" isMarkerVisible="true">
        <dc:Bounds x="625" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0zgy5zs_di" bpmnElement="Gateway_0zgy5zs" isMarkerVisible="true">
        <dc:Bounds x="995" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0o7pr1c_di" bpmnElement="Gateway_0o7pr1c" isMarkerVisible="true">
        <dc:Bounds x="1155" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1hhg262_di" bpmnElement="Gateway_1hhg262" isMarkerVisible="true">
        <dc:Bounds x="1435" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0g96lkw_di" bpmnElement="Gateway_0g96lkw" isMarkerVisible="true">
        <dc:Bounds x="1785" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_087z256_di" bpmnElement="Event_087z256">
        <dc:Bounds x="1442" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0tis5ok_di" bpmnElement="Esb_blockEsim">
        <dc:Bounds x="1260" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0bi9ful_di" bpmnElement="Gateway_0bi9ful" isMarkerVisible="true">
        <dc:Bounds x="1695" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_18i69yr_di" bpmnElement="Event_18i69yr">
        <dc:Bounds x="1702" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0a4d4q5_di" bpmnElement="ESBEsimBlockCallback">
        <dc:Bounds x="1530" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1lak4vz_di" bpmnElement="ESB_fetch">
        <dc:Bounds x="3750" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vn14lo_di" bpmnElement="Gateway_0vn14lo" isMarkerVisible="true">
        <dc:Bounds x="3915" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0953h34_di" bpmnElement="DAG_Esim">
        <dc:Bounds x="4030" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1m8qy34_di" bpmnElement="Event_1m8qy34">
        <dc:Bounds x="3922" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0w73x0l_di" bpmnElement="Gateway_0w73x0l" isMarkerVisible="true">
        <dc:Bounds x="4195" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_10s750p_di" bpmnElement="Gateway_10s750p" isMarkerVisible="true">
        <dc:Bounds x="3485" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ljp1sz_di" bpmnElement="Event_1ljp1sz">
        <dc:Bounds x="3582" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1q0ybfl" bpmnElement="Arm_FetchAssetDetails">
        <dc:Bounds x="290" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0qq9idy" bpmnElement="Gateway_1qd7juv" isMarkerVisible="true">
        <dc:Bounds x="485" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ekqayi" bpmnElement="Event_005bgn6">
        <dc:Bounds x="492" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0vekyb1_di" bpmnElement="Flow_0vekyb1">
        <di:waypoint x="2290" y="200" />
        <di:waypoint x="2345" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xx5wld_di" bpmnElement="Flow_0xx5wld">
        <di:waypoint x="2370" y="225" />
        <di:waypoint x="2370" y="310" />
        <di:waypoint x="2442" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2368" y="265" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_142allt_di" bpmnElement="Flow_142allt">
        <di:waypoint x="2910" y="200" />
        <di:waypoint x="2965" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ic6nwu_di" bpmnElement="Flow_1ic6nwu">
        <di:waypoint x="3015" y="200" />
        <di:waypoint x="3070" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wkpxbh_di" bpmnElement="Flow_1wkpxbh">
        <di:waypoint x="3170" y="200" />
        <di:waypoint x="3225" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tdbzk9_di" bpmnElement="Flow_1tdbzk9">
        <di:waypoint x="3275" y="200" />
        <di:waypoint x="3330" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kwbuv9_di" bpmnElement="Flow_1kwbuv9">
        <di:waypoint x="2990" y="225" />
        <di:waypoint x="2990" y="310" />
        <di:waypoint x="3072" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2988" y="265" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r091me_di" bpmnElement="Flow_1r091me">
        <di:waypoint x="3250" y="225" />
        <di:waypoint x="3250" y="310" />
        <di:waypoint x="3332" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3248" y="265" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07h0uoj_di" bpmnElement="Flow_07h0uoj">
        <di:waypoint x="1980" y="200" />
        <di:waypoint x="2055" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bj4wjv_di" bpmnElement="Flow_1bj4wjv">
        <di:waypoint x="2105" y="200" />
        <di:waypoint x="2190" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16x0odp_di" bpmnElement="Flow_16x0odp">
        <di:waypoint x="2080" y="225" />
        <di:waypoint x="2080" y="310" />
        <di:waypoint x="2192" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2078" y="265" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_003cyt6_di" bpmnElement="Flow_003cyt6">
        <di:waypoint x="2395" y="200" />
        <di:waypoint x="2480" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gdl6nb_di" bpmnElement="Flow_1gdl6nb">
        <di:waypoint x="2580" y="200" />
        <di:waypoint x="2645" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_157awwf_di" bpmnElement="Flow_157awwf">
        <di:waypoint x="2695" y="200" />
        <di:waypoint x="2810" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1biwy1s_di" bpmnElement="Flow_1biwy1s">
        <di:waypoint x="2670" y="225" />
        <di:waypoint x="2670" y="310" />
        <di:waypoint x="2752" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2669" y="265" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w55keh_di" bpmnElement="Flow_1w55keh">
        <di:waypoint x="830" y="200" />
        <di:waypoint x="895" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_115kl92_di" bpmnElement="Flow_115kl92">
        <di:waypoint x="920" y="225" />
        <di:waypoint x="920" y="310" />
        <di:waypoint x="952" y="310" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tchw38_di" bpmnElement="Flow_0tchw38">
        <di:waypoint x="675" y="200" />
        <di:waypoint x="730" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="694" y="182" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17dy2n2_di" bpmnElement="Flow_17dy2n2">
        <di:waypoint x="945" y="200" />
        <di:waypoint x="995" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="950" y="182" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nbqnmz_di" bpmnElement="Flow_0nbqnmz">
        <di:waypoint x="650" y="175" />
        <di:waypoint x="650" y="100" />
        <di:waypoint x="1020" y="100" />
        <di:waypoint x="1020" y="175" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="829" y="82" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xj3wcy_di" bpmnElement="Flow_0xj3wcy">
        <di:waypoint x="188" y="200" />
        <di:waypoint x="290" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xnp3wr_di" bpmnElement="Flow_0xnp3wr">
        <di:waypoint x="1045" y="200" />
        <di:waypoint x="1155" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0onpo2m_di" bpmnElement="Flow_0onpo2m">
        <di:waypoint x="1205" y="200" />
        <di:waypoint x="1260" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1219" y="182" width="29" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1desy2w_di" bpmnElement="Flow_1desy2w">
        <di:waypoint x="1360" y="200" />
        <di:waypoint x="1435" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uc2fos_di" bpmnElement="Flow_1uc2fos">
        <di:waypoint x="1835" y="200" />
        <di:waypoint x="1880" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g2f7wa_di" bpmnElement="Flow_1g2f7wa">
        <di:waypoint x="1180" y="175" />
        <di:waypoint x="1180" y="100" />
        <di:waypoint x="1810" y="100" />
        <di:waypoint x="1810" y="175" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1465" y="82" width="61" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_036d8zo_di" bpmnElement="Flow_036d8zo">
        <di:waypoint x="1460" y="225" />
        <di:waypoint x="1460" y="292" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1agrdxe_di" bpmnElement="Flow_1agrdxe">
        <di:waypoint x="1485" y="200" />
        <di:waypoint x="1530" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00yznh5_di" bpmnElement="Flow_00yznh5">
        <di:waypoint x="1630" y="200" />
        <di:waypoint x="1695" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ehb4tj_di" bpmnElement="Flow_1ehb4tj">
        <di:waypoint x="1745" y="200" />
        <di:waypoint x="1785" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u8bm20_di" bpmnElement="Flow_0u8bm20">
        <di:waypoint x="1720" y="225" />
        <di:waypoint x="1720" y="292" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kpyymf_di" bpmnElement="Flow_0kpyymf">
        <di:waypoint x="3685" y="200" />
        <di:waypoint x="3750" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3692" y="182" width="57" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0k4vrvn_di" bpmnElement="Flow_0k4vrvn">
        <di:waypoint x="3850" y="200" />
        <di:waypoint x="3915" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_085zkua_di" bpmnElement="Flow_085zkua">
        <di:waypoint x="3965" y="200" />
        <di:waypoint x="4030" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yn3ctn_di" bpmnElement="Flow_1yn3ctn">
        <di:waypoint x="3940" y="225" />
        <di:waypoint x="3940" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3938" y="256" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c89w7z_di" bpmnElement="Flow_1c89w7z">
        <di:waypoint x="4130" y="200" />
        <di:waypoint x="4195" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rvnkae_di" bpmnElement="Flow_0rvnkae">
        <di:waypoint x="4245" y="200" />
        <di:waypoint x="4322" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02mh48o_di" bpmnElement="Flow_02mh48o">
        <di:waypoint x="3660" y="175" />
        <di:waypoint x="3660" y="100" />
        <di:waypoint x="4220" y="100" />
        <di:waypoint x="4220" y="175" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ytzor1_di" bpmnElement="Flow_0ytzor1">
        <di:waypoint x="3430" y="200" />
        <di:waypoint x="3485" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14e8mfh_di" bpmnElement="Flow_14e8mfh">
        <di:waypoint x="3535" y="200" />
        <di:waypoint x="3635" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s0bfpq_di" bpmnElement="Flow_1s0bfpq">
        <di:waypoint x="3510" y="225" />
        <di:waypoint x="3510" y="310" />
        <di:waypoint x="3582" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3508" y="265" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mf6q6n_di" bpmnElement="Flow_1mf6q6n">
        <di:waypoint x="390" y="200" />
        <di:waypoint x="485" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tyspjy_di" bpmnElement="Flow_0tyspjy">
        <di:waypoint x="535" y="200" />
        <di:waypoint x="625" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="535" y="182" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_181w14t_di" bpmnElement="Flow_181w14t">
        <di:waypoint x="510" y="225" />
        <di:waypoint x="510" y="292" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
