<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0">
  <bpmn:process id="ChangeSimNsaCancellation" name="ChangeSimNsaCancellation" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:exclusiveGateway id="Gateway_0y61vbd" default="Flow_0vv0zhc">
      <bpmn:incoming>Flow_13i5f75</bpmn:incoming>
      <bpmn:outgoing>Flow_0vv0zhc</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_19fmcu1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1jzvrlz" default="Flow_0hauvlo">
      <bpmn:incoming>Flow_03lp84m</bpmn:incoming>
      <bpmn:outgoing>Flow_0e9caqf</bpmn:outgoing>
      <bpmn:outgoing>Flow_0hauvlo</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1br1me9">
      <bpmn:incoming>Flow_0e9caqf</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0vv0zhc" name="Success" sourceRef="Gateway_0y61vbd" targetRef="SOMCallback" />
    <bpmn:sequenceFlow id="Flow_0e9caqf" name="Failure" sourceRef="Gateway_1jzvrlz" targetRef="Event_1br1me9">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="EndEvent_1vra2xp">
      <bpmn:incoming>SequenceFlow_19fmcu1</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_19fmcu1" name="Failure" sourceRef="Gateway_0y61vbd" targetRef="EndEvent_1vra2xp">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch Service Registry to get Subscriptions" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_13oz9ny</bpmn:incoming>
      <bpmn:outgoing>Flow_14snl0d</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_14qj6cm" default="Flow_0gwis13">
      <bpmn:incoming>Flow_14snl0d</bpmn:incoming>
      <bpmn:outgoing>Flow_0gwis13</bpmn:outgoing>
      <bpmn:outgoing>Flow_1p3nd71</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_14snl0d" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_14qj6cm" />
    <bpmn:sequenceFlow id="Flow_0gwis13" name="Success" sourceRef="Gateway_14qj6cm" targetRef="SOMCancelNsaPlan" />
    <bpmn:endEvent id="Event_1tyx8el">
      <bpmn:incoming>Flow_1p3nd71</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1p3nd71" name="Failure" sourceRef="Gateway_14qj6cm" targetRef="Event_1tyx8el">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="SOMCancelNsaPlan" name="SOM Deactivate Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${somCancelNsaPlan}">
      <bpmn:incoming>Flow_0gwis13</bpmn:incoming>
      <bpmn:outgoing>Flow_13i5f75</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_13i5f75" sourceRef="SOMCancelNsaPlan" targetRef="Gateway_0y61vbd" />
    <bpmn:receiveTask id="SOMCallback" name="SOMCallback" camunda:asyncBefore="true" messageRef="Message_1fyqyx3">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOMCancelNsaPlan</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOMCancelNsaPlan" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0vv0zhc</bpmn:incoming>
      <bpmn:outgoing>Flow_03lp84m</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_03lp84m" sourceRef="SOMCallback" targetRef="Gateway_1jzvrlz" />
    <bpmn:serviceTask id="BillingCancelSubscription" name="Billing Cancel Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0d1gx9z</bpmn:incoming>
      <bpmn:outgoing>Flow_0ripga5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_09qervv" default="Flow_0d1gx9z">
      <bpmn:incoming>Flow_1lp1dme</bpmn:incoming>
      <bpmn:outgoing>Flow_1t4bpry</bpmn:outgoing>
      <bpmn:outgoing>Flow_0d1gx9z</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="OCSCancelSubscription" name="Subsciption Deactivation in OCS Via ESB" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0hauvlo</bpmn:incoming>
      <bpmn:outgoing>Flow_1lp1dme</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1tezzgh">
      <bpmn:incoming>Flow_1t4bpry</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1lp1dme" sourceRef="OCSCancelSubscription" targetRef="Gateway_09qervv" />
    <bpmn:sequenceFlow id="Flow_1t4bpry" name="Failure" sourceRef="Gateway_09qervv" targetRef="Event_1tezzgh">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0d1gx9z" sourceRef="Gateway_09qervv" targetRef="BillingCancelSubscription" />
    <bpmn:startEvent id="Event_1cbs8zz">
      <bpmn:outgoing>Flow_13oz9ny</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_13oz9ny" sourceRef="Event_1cbs8zz" targetRef="SOMFetchServiceRegistry" />
    <bpmn:sequenceFlow id="Flow_0hauvlo" sourceRef="Gateway_1jzvrlz" targetRef="OCSCancelSubscription" />
    <bpmn:endEvent id="Event_1ksjevt">
      <bpmn:incoming>Flow_0zuhcxd</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0ripga5" sourceRef="BillingCancelSubscription" targetRef="Gateway_160wf11" />
    <bpmn:exclusiveGateway id="Gateway_160wf11" default="Flow_0zuhcxd">
      <bpmn:incoming>Flow_0ripga5</bpmn:incoming>
      <bpmn:outgoing>Flow_0zuhcxd</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ygwrby</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0zuhcxd" sourceRef="Gateway_160wf11" targetRef="Event_1ksjevt" />
    <bpmn:endEvent id="Event_1ej2r5r">
      <bpmn:incoming>Flow_0ygwrby</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0ygwrby" name="Failure" sourceRef="Gateway_160wf11" targetRef="Event_1ej2r5r">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_18lupa4" name="SOMAddSubCallback" />
  <bpmn:message id="Message_1fyqyx3" name="SOMCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ChangeSimNsaCancellation">
      <bpmndi:BPMNShape id="Gateway_0y61vbd_di" bpmnElement="Gateway_0y61vbd" isMarkerVisible="true">
        <dc:Bounds x="985" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1jzvrlz_di" bpmnElement="Gateway_1jzvrlz" isMarkerVisible="true">
        <dc:Bounds x="1325" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1br1me9_di" bpmnElement="Event_1br1me9">
        <dc:Bounds x="1332" y="224" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1vra2xp_di" bpmnElement="EndEvent_1vra2xp">
        <dc:Bounds x="992" y="242" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1l7h8rx_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="280" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_14qj6cm_di" bpmnElement="Gateway_14qj6cm" isMarkerVisible="true">
        <dc:Bounds x="465" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1tyx8el_di" bpmnElement="Event_1tyx8el">
        <dc:Bounds x="472" y="242" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ao82aw_di" bpmnElement="SOMCancelNsaPlan">
        <dc:Bounds x="680" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1pwol3v_di" bpmnElement="SOMCallback">
        <dc:Bounds x="1140" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_14h98ga" bpmnElement="BillingCancelSubscription">
        <dc:Bounds x="1960" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0m2wcbn" bpmnElement="Gateway_09qervv" isMarkerVisible="true">
        <dc:Bounds x="1805" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_10k8uh4" bpmnElement="OCSCancelSubscription">
        <dc:Bounds x="1560" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06j38ii" bpmnElement="Event_1tezzgh">
        <dc:Bounds x="1812" y="224" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_05db97y_di" bpmnElement="Event_1cbs8zz">
        <dc:Bounds x="152" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ksjevt_di" bpmnElement="Event_1ksjevt">
        <dc:Bounds x="2302" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1o6zwmb" bpmnElement="Gateway_160wf11" isMarkerVisible="true">
        <dc:Bounds x="2145" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0rbrmjv" bpmnElement="Event_1ej2r5r">
        <dc:Bounds x="2152" y="224" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0vv0zhc_di" bpmnElement="Flow_0vv0zhc">
        <di:waypoint x="1035" y="120" />
        <di:waypoint x="1140" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1058" y="96" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e9caqf_di" bpmnElement="Flow_0e9caqf">
        <di:waypoint x="1350" y="145" />
        <di:waypoint x="1350" y="224" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1353" y="175" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_19fmcu1_di" bpmnElement="SequenceFlow_19fmcu1">
        <di:waypoint x="1010" y="145" />
        <di:waypoint x="1010" y="242" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1013" y="183" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14snl0d_di" bpmnElement="Flow_14snl0d">
        <di:waypoint x="380" y="120" />
        <di:waypoint x="465" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gwis13_di" bpmnElement="Flow_0gwis13">
        <di:waypoint x="515" y="120" />
        <di:waypoint x="680" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="531" y="96" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p3nd71_di" bpmnElement="Flow_1p3nd71">
        <di:waypoint x="490" y="145" />
        <di:waypoint x="490" y="242" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="493" y="183" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13i5f75_di" bpmnElement="Flow_13i5f75">
        <di:waypoint x="780" y="120" />
        <di:waypoint x="985" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03lp84m_di" bpmnElement="Flow_03lp84m">
        <di:waypoint x="1240" y="120" />
        <di:waypoint x="1325" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0u14x0a" bpmnElement="Flow_1lp1dme">
        <di:waypoint x="1660" y="120" />
        <di:waypoint x="1805" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1vhuy9m" bpmnElement="Flow_1t4bpry">
        <di:waypoint x="1830" y="145" />
        <di:waypoint x="1830" y="224" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1833" y="166" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d1gx9z_di" bpmnElement="Flow_0d1gx9z">
        <di:waypoint x="1855" y="120" />
        <di:waypoint x="1960" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2071" y="222" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13oz9ny_di" bpmnElement="Flow_13oz9ny">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="280" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hauvlo_di" bpmnElement="Flow_0hauvlo">
        <di:waypoint x="1375" y="120" />
        <di:waypoint x="1560" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ripga5_di" bpmnElement="Flow_0ripga5">
        <di:waypoint x="2060" y="120" />
        <di:waypoint x="2145" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zuhcxd_di" bpmnElement="Flow_0zuhcxd">
        <di:waypoint x="2195" y="120" />
        <di:waypoint x="2302" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ygwrby_di" bpmnElement="Flow_0ygwrby">
        <di:waypoint x="2170" y="145" />
        <di:waypoint x="2170" y="224" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2182" y="175" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
