<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_12d5d0j" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="UpdateSafeCustody" name="UpdateSafeCustody" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>SequenceFlow_0rospwn</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0rospwn" sourceRef="orderExecStart" targetRef="BSUpdateSafeCustody" />
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>SequenceFlow_1iave39</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSUpdateSafeCustody" name="BS Update Safe Custody" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_0rospwn</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1iave39</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1iave39" sourceRef="BSUpdateSafeCustody" targetRef="orderExecEnd" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="UpdateSafeCustody">
      <bpmndi:BPMNEdge id="SequenceFlow_1iave39_di" bpmnElement="SequenceFlow_1iave39">
        <di:waypoint x="352" y="121" />
        <di:waypoint x="405" y="121" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0rospwn_di" bpmnElement="SequenceFlow_0rospwn">
        <di:waypoint x="192" y="121" />
        <di:waypoint x="252" y="121" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="156" y="103" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0x2p8qu_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="405" y="103" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0x6uu6i_di" bpmnElement="BSUpdateSafeCustody">
        <dc:Bounds x="252" y="81" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
