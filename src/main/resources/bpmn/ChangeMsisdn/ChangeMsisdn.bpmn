<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1uhfu8o" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0">
  <bpmn:process id="ChangeMsisdn" name="Change Msisdn" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_01tnsjd</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_1dae7nw" default="Flow_1ffe4if">
      <bpmn:incoming>Flow_085jd4f</bpmn:incoming>
      <bpmn:outgoing>Flow_1ffe4if</bpmn:outgoing>
      <bpmn:outgoing>Flow_1a89xud</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_085jd4f" sourceRef="SOMFetchservices" targetRef="Gateway_1dae7nw" />
    <bpmn:sequenceFlow id="Flow_1ffe4if" sourceRef="Gateway_1dae7nw" targetRef="SOM_ChangeMSISDN" />
    <bpmn:exclusiveGateway id="Gateway_1iy6nhz" default="Flow_0cvyc2v">
      <bpmn:incoming>Flow_0beh99w</bpmn:incoming>
      <bpmn:outgoing>Flow_0y73brh</bpmn:outgoing>
      <bpmn:outgoing>Flow_0cvyc2v</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0beh99w" sourceRef="SOM_ChangeMSISDN" targetRef="Gateway_1iy6nhz" />
    <bpmn:exclusiveGateway id="Gateway_10mg2mu" default="Flow_001vee4">
      <bpmn:incoming>Flow_0rug1b1</bpmn:incoming>
      <bpmn:outgoing>Flow_001vee4</bpmn:outgoing>
      <bpmn:outgoing>Flow_1feanrp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0rug1b1" sourceRef="OCS_ChangeMSISDN" targetRef="Gateway_10mg2mu" />
    <bpmn:sequenceFlow id="Flow_001vee4" sourceRef="Gateway_10mg2mu" targetRef="BS_ChangeMSISDN" />
    <bpmn:exclusiveGateway id="Gateway_15jy9q5" default="Flow_1sxzobi">
      <bpmn:incoming>Flow_02jip00</bpmn:incoming>
      <bpmn:outgoing>Flow_1sxzobi</bpmn:outgoing>
      <bpmn:outgoing>Flow_07q9nvd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_02jip00" sourceRef="BS_ChangeMSISDN" targetRef="Gateway_15jy9q5" />
    <bpmn:sequenceFlow id="Flow_1sxzobi" sourceRef="Gateway_15jy9q5" targetRef="NMS_ChangeMSISDN" />
    <bpmn:exclusiveGateway id="Gateway_1h53tmn" default="Flow_0gi47sh">
      <bpmn:incoming>Flow_1omqjic</bpmn:incoming>
      <bpmn:outgoing>Flow_0gi47sh</bpmn:outgoing>
      <bpmn:outgoing>Flow_0n3uoda</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1omqjic" sourceRef="NMS_ChangeMSISDN" targetRef="Gateway_1h53tmn" />
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0gi47sh</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0gi47sh" sourceRef="Gateway_1h53tmn" targetRef="orderExecEnd" />
    <bpmn:endEvent id="Event_1ywexl1">
      <bpmn:incoming>Flow_1a89xud</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1a89xud" name="failure" sourceRef="Gateway_1dae7nw" targetRef="Event_1ywexl1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1ub06r8">
      <bpmn:incoming>Flow_0y73brh</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0y73brh" name="failure" sourceRef="Gateway_1iy6nhz" targetRef="Event_1ub06r8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1yg8hsf">
      <bpmn:incoming>Flow_1feanrp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1feanrp" name="failure" sourceRef="Gateway_10mg2mu" targetRef="Event_1yg8hsf">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_0sm8oce">
      <bpmn:incoming>Flow_07q9nvd</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_07q9nvd" name="failure" sourceRef="Gateway_15jy9q5" targetRef="Event_0sm8oce">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="SOMFetchservices" name="SOM Fetch services" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1npv3kx</bpmn:incoming>
      <bpmn:outgoing>Flow_085jd4f</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="SOM_ChangeMSISDN" name="SOM change MSISDN" camunda:asyncBefore="true" camunda:delegateExpression="${somChangeMsisdn}">
      <bpmn:incoming>Flow_1ffe4if</bpmn:incoming>
      <bpmn:outgoing>Flow_0beh99w</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="OCS_ChangeMSISDN" name="OCS Change MSISDN" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0tjem2i</bpmn:incoming>
      <bpmn:outgoing>Flow_0rug1b1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BS_ChangeMSISDN" name="BS change MSISDN" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_001vee4</bpmn:incoming>
      <bpmn:outgoing>Flow_02jip00</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="NMS_ChangeMSISDN" name="NMS Change Msisdn" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1sxzobi</bpmn:incoming>
      <bpmn:outgoing>Flow_1omqjic</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0sae7sd">
      <bpmn:incoming>Flow_0n3uoda</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0n3uoda" name="failure" sourceRef="Gateway_1h53tmn" targetRef="Event_0sae7sd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="SOMChangeMsisdnCallback" name="SOM Callback" messageRef="Message_02m47bb">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOM_ChangeMSISDN</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOMTerminateService" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0cvyc2v</bpmn:incoming>
      <bpmn:outgoing>Flow_0v7suc6</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_0cvyc2v" sourceRef="Gateway_1iy6nhz" targetRef="SOMChangeMsisdnCallback" />
    <bpmn:exclusiveGateway id="Gateway_0y8ek8j" default="Flow_0tjem2i">
      <bpmn:incoming>Flow_0v7suc6</bpmn:incoming>
      <bpmn:outgoing>Flow_0tjem2i</bpmn:outgoing>
      <bpmn:outgoing>Flow_0b7ski2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0v7suc6" sourceRef="SOMChangeMsisdnCallback" targetRef="Gateway_0y8ek8j" />
    <bpmn:sequenceFlow id="Flow_0tjem2i" sourceRef="Gateway_0y8ek8j" targetRef="OCS_ChangeMSISDN" />
    <bpmn:endEvent id="Event_0m8p1lc">
      <bpmn:incoming>Flow_0b7ski2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0b7ski2" name="Failure" sourceRef="Gateway_0y8ek8j" targetRef="Event_0m8p1lc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0bhqma8" default="Flow_0cjcl9p">
      <bpmn:incoming>Flow_01tnsjd</bpmn:incoming>
      <bpmn:outgoing>Flow_1a1zk6a</bpmn:outgoing>
      <bpmn:outgoing>Flow_0cjcl9p</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_01tnsjd" sourceRef="orderExecStart" targetRef="Gateway_0bhqma8" />
    <bpmn:sequenceFlow id="Flow_1a1zk6a" name="yes" sourceRef="Gateway_0bhqma8" targetRef="BookServiceFee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${additionalCharges}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1u3gbfz" default="Flow_0ch54u9">
      <bpmn:incoming>Flow_1eab7qe</bpmn:incoming>
      <bpmn:outgoing>Flow_0ch54u9</bpmn:outgoing>
      <bpmn:outgoing>Flow_1mm0e45</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1eab7qe" sourceRef="BookServiceFee" targetRef="Gateway_1u3gbfz" />
    <bpmn:exclusiveGateway id="Gateway_0w1hw21">
      <bpmn:incoming>Flow_0ch54u9</bpmn:incoming>
      <bpmn:incoming>Flow_0cjcl9p</bpmn:incoming>
      <bpmn:outgoing>Flow_1npv3kx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ch54u9" name="success" sourceRef="Gateway_1u3gbfz" targetRef="Gateway_0w1hw21" />
    <bpmn:sequenceFlow id="Flow_1npv3kx" sourceRef="Gateway_0w1hw21" targetRef="SOMFetchservices" />
    <bpmn:serviceTask id="BookServiceFee" name="Book Service Fee" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1a1zk6a</bpmn:incoming>
      <bpmn:outgoing>Flow_1eab7qe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0cjcl9p" name="no" sourceRef="Gateway_0bhqma8" targetRef="Gateway_0w1hw21" />
    <bpmn:endEvent id="Event_158l57o">
      <bpmn:incoming>Flow_1mm0e45</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1mm0e45" name="Failure" sourceRef="Gateway_1u3gbfz" targetRef="Event_158l57o">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_02m47bb" name="SOMChangeMsisdnCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ChangeMsisdn">
      <bpmndi:BPMNShape id="Gateway_1dae7nw_di" bpmnElement="Gateway_1dae7nw" isMarkerVisible="true">
        <dc:Bounds x="905" y="182" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1iy6nhz_di" bpmnElement="Gateway_1iy6nhz" isMarkerVisible="true">
        <dc:Bounds x="1165" y="182" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_10mg2mu_di" bpmnElement="Gateway_10mg2mu" isMarkerVisible="true">
        <dc:Bounds x="1775" y="182" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_15jy9q5_di" bpmnElement="Gateway_15jy9q5" isMarkerVisible="true">
        <dc:Bounds x="2035" y="182" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1h53tmn_di" bpmnElement="Gateway_1h53tmn" isMarkerVisible="true">
        <dc:Bounds x="2295" y="182" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0m9xuog_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2402" y="189" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ywexl1_di" bpmnElement="Event_1ywexl1">
        <dc:Bounds x="1012" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ub06r8_di" bpmnElement="Event_1ub06r8">
        <dc:Bounds x="1282" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1yg8hsf_di" bpmnElement="Event_1yg8hsf">
        <dc:Bounds x="1882" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0sm8oce_di" bpmnElement="Event_0sm8oce">
        <dc:Bounds x="2142" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0skyi59_di" bpmnElement="SOMFetchservices">
        <dc:Bounds x="750" y="167" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0u19a1d_di" bpmnElement="SOM_ChangeMSISDN">
        <dc:Bounds x="1010" y="167" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0uk3wux_di" bpmnElement="OCS_ChangeMSISDN">
        <dc:Bounds x="1620" y="167" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1118u51_di" bpmnElement="BS_ChangeMSISDN">
        <dc:Bounds x="1880" y="167" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0dtpg9q_di" bpmnElement="NMS_ChangeMSISDN">
        <dc:Bounds x="2140" y="167" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0sae7sd_di" bpmnElement="Event_0sae7sd">
        <dc:Bounds x="2402" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1h5e770_di" bpmnElement="SOMChangeMsisdnCallback">
        <dc:Bounds x="1320" y="167" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0y8ek8j_di" bpmnElement="Gateway_0y8ek8j" isMarkerVisible="true">
        <dc:Bounds x="1485" y="182" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0m8p1lc_di" bpmnElement="Event_0m8p1lc">
        <dc:Bounds x="1592" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="189" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0bhqma8_di" bpmnElement="Gateway_0bhqma8" isMarkerVisible="true">
        <dc:Bounds x="245" y="182" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1u3gbfz_di" bpmnElement="Gateway_1u3gbfz" isMarkerVisible="true">
        <dc:Bounds x="525" y="182" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0w1hw21_di" bpmnElement="Gateway_0w1hw21" isMarkerVisible="true">
        <dc:Bounds x="645" y="182" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1x6zkat_di" bpmnElement="BookServiceFee">
        <dc:Bounds x="360" y="167" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_158l57o_di" bpmnElement="Event_158l57o">
        <dc:Bounds x="652" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_085jd4f_di" bpmnElement="Flow_085jd4f">
        <di:waypoint x="850" y="207" />
        <di:waypoint x="905" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ffe4if_di" bpmnElement="Flow_1ffe4if">
        <di:waypoint x="955" y="207" />
        <di:waypoint x="1010" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0beh99w_di" bpmnElement="Flow_0beh99w">
        <di:waypoint x="1110" y="207" />
        <di:waypoint x="1165" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rug1b1_di" bpmnElement="Flow_0rug1b1">
        <di:waypoint x="1720" y="207" />
        <di:waypoint x="1775" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_001vee4_di" bpmnElement="Flow_001vee4">
        <di:waypoint x="1825" y="207" />
        <di:waypoint x="1880" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02jip00_di" bpmnElement="Flow_02jip00">
        <di:waypoint x="1980" y="207" />
        <di:waypoint x="2035" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sxzobi_di" bpmnElement="Flow_1sxzobi">
        <di:waypoint x="2085" y="207" />
        <di:waypoint x="2140" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1omqjic_di" bpmnElement="Flow_1omqjic">
        <di:waypoint x="2240" y="207" />
        <di:waypoint x="2295" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gi47sh_di" bpmnElement="Flow_0gi47sh">
        <di:waypoint x="2345" y="207" />
        <di:waypoint x="2402" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a89xud_di" bpmnElement="Flow_1a89xud">
        <di:waypoint x="930" y="232" />
        <di:waypoint x="930" y="320" />
        <di:waypoint x="1012" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="931" y="273" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y73brh_di" bpmnElement="Flow_0y73brh">
        <di:waypoint x="1190" y="232" />
        <di:waypoint x="1190" y="320" />
        <di:waypoint x="1282" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1190" y="273" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1feanrp_di" bpmnElement="Flow_1feanrp">
        <di:waypoint x="1800" y="232" />
        <di:waypoint x="1800" y="320" />
        <di:waypoint x="1882" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1800" y="273" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07q9nvd_di" bpmnElement="Flow_07q9nvd">
        <di:waypoint x="2060" y="232" />
        <di:waypoint x="2060" y="320" />
        <di:waypoint x="2142" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2060" y="273" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n3uoda_di" bpmnElement="Flow_0n3uoda">
        <di:waypoint x="2320" y="232" />
        <di:waypoint x="2320" y="320" />
        <di:waypoint x="2402" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2320" y="273" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cvyc2v_di" bpmnElement="Flow_0cvyc2v">
        <di:waypoint x="1215" y="207" />
        <di:waypoint x="1320" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v7suc6_di" bpmnElement="Flow_0v7suc6">
        <di:waypoint x="1420" y="207" />
        <di:waypoint x="1485" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tjem2i_di" bpmnElement="Flow_0tjem2i">
        <di:waypoint x="1535" y="207" />
        <di:waypoint x="1620" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b7ski2_di" bpmnElement="Flow_0b7ski2">
        <di:waypoint x="1510" y="232" />
        <di:waypoint x="1510" y="320" />
        <di:waypoint x="1592" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1508" y="273" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01tnsjd_di" bpmnElement="Flow_01tnsjd">
        <di:waypoint x="188" y="207" />
        <di:waypoint x="245" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a1zk6a_di" bpmnElement="Flow_1a1zk6a">
        <di:waypoint x="295" y="207" />
        <di:waypoint x="360" y="207" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="319" y="189" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eab7qe_di" bpmnElement="Flow_1eab7qe">
        <di:waypoint x="460" y="207" />
        <di:waypoint x="525" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ch54u9_di" bpmnElement="Flow_0ch54u9">
        <di:waypoint x="575" y="207" />
        <di:waypoint x="645" y="207" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="590" y="189" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1npv3kx_di" bpmnElement="Flow_1npv3kx">
        <di:waypoint x="695" y="207" />
        <di:waypoint x="750" y="207" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cjcl9p_di" bpmnElement="Flow_0cjcl9p">
        <di:waypoint x="270" y="182" />
        <di:waypoint x="270" y="100" />
        <di:waypoint x="670" y="100" />
        <di:waypoint x="670" y="182" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="464" y="82" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mm0e45_di" bpmnElement="Flow_1mm0e45">
        <di:waypoint x="550" y="232" />
        <di:waypoint x="550" y="320" />
        <di:waypoint x="652" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="552" y="273" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>