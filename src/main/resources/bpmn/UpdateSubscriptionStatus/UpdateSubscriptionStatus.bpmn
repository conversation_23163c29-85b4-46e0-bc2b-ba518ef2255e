<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.28.0">
  <bpmn:process id="UpdateSubscriptionStatus" name="UpdateSubscriptionStatus" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:serviceTask id="BS_PauseUpdateSubscriptionStatus" name="Bs_Pause-UpdateSubscriptionStatus" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1xru2zu</bpmn:incoming>
      <bpmn:outgoing>Flow_1nzkyg3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_15ddmqi</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1nzkyg3" sourceRef="BS_PauseUpdateSubscriptionStatus" targetRef="Gateway_0gcdp7v" />
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_10r104l</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_10r104l" sourceRef="orderExecStart" targetRef="Gateway_056686s" />
    <bpmn:exclusiveGateway id="Gateway_056686s" name="If Action=pause">
      <bpmn:incoming>Flow_10r104l</bpmn:incoming>
      <bpmn:outgoing>Flow_1xru2zu</bpmn:outgoing>
      <bpmn:outgoing>Flow_0c7tyzp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1xru2zu" sourceRef="Gateway_056686s" targetRef="BS_PauseUpdateSubscriptionStatus">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order").element().hasProp('orderItem') &amp;&amp; workflowData.jsonPath("$.order.orderItem[0]").element().hasProp('action') &amp;&amp; workflowData.jsonPath("$.order.orderItem[0].action").stringValue() != null &amp;&amp; workflowData.jsonPath("$.order.orderItem[0].action").stringValue() != '' &amp;&amp; workflowData.jsonPath("$.order.orderItem[0].action").stringValue() == 'pause'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0dmci7v">
      <bpmn:incoming>Flow_16ugdwh</bpmn:incoming>
      <bpmn:incoming>Flow_071p2qh</bpmn:incoming>
      <bpmn:outgoing>Flow_15ddmqi</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_15ddmqi" sourceRef="Gateway_0dmci7v" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_0c7tyzp" name="If Action=resume" sourceRef="Gateway_056686s" targetRef="BS_ResumeUpdateSubscriptionStatus">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order").element().hasProp('orderItem') &amp;&amp; workflowData.jsonPath("$.order.orderItem[0]").element().hasProp('action') &amp;&amp; workflowData.jsonPath("$.order.orderItem[0].action").stringValue() != null &amp;&amp; workflowData.jsonPath("$.order.orderItem[0].action").stringValue() != '' &amp;&amp; workflowData.jsonPath("$.order.orderItem[0].action").stringValue() == 'resume'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BS_ResumeUpdateSubscriptionStatus" name="Bs_Resume-UpdateSubscriptionStatus" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0c7tyzp</bpmn:incoming>
      <bpmn:outgoing>Flow_17hqbdt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_17hqbdt" sourceRef="BS_ResumeUpdateSubscriptionStatus" targetRef="Gateway_0i9jx78" />
    <bpmn:serviceTask id="ESB_PauseUpdateSubscriptionStatus" name="ESB_Pause-UpdateSubscriptionStatus" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_11z71kw</bpmn:incoming>
      <bpmn:outgoing>Flow_0ue4qoh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ue4qoh" sourceRef="ESB_PauseUpdateSubscriptionStatus" targetRef="Gateway_1rm6qgm" />
    <bpmn:serviceTask id="ESB_ResumeUpdateSubscriptionStatus" name="ESB_Resume-UpdateSubscriptionStatus" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_06mdgfg</bpmn:incoming>
      <bpmn:outgoing>Flow_0bjpb9h</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0bjpb9h" sourceRef="ESB_ResumeUpdateSubscriptionStatus" targetRef="Gateway_19pq04b" />
    <bpmn:exclusiveGateway id="Gateway_0gcdp7v" default="Flow_11z71kw">
      <bpmn:incoming>Flow_1nzkyg3</bpmn:incoming>
      <bpmn:outgoing>Flow_11z71kw</bpmn:outgoing>
      <bpmn:outgoing>Flow_0lqgd2u</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_11z71kw" sourceRef="Gateway_0gcdp7v" targetRef="ESB_PauseUpdateSubscriptionStatus" />
    <bpmn:exclusiveGateway id="Gateway_0i9jx78" default="Flow_06mdgfg">
      <bpmn:incoming>Flow_17hqbdt</bpmn:incoming>
      <bpmn:outgoing>Flow_06mdgfg</bpmn:outgoing>
      <bpmn:outgoing>Flow_1i49s2f</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_06mdgfg" sourceRef="Gateway_0i9jx78" targetRef="ESB_ResumeUpdateSubscriptionStatus" />
    <bpmn:exclusiveGateway id="Gateway_19pq04b" default="Flow_16ugdwh">
      <bpmn:incoming>Flow_0bjpb9h</bpmn:incoming>
      <bpmn:outgoing>Flow_16ugdwh</bpmn:outgoing>
      <bpmn:outgoing>Flow_1vntpno</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_16ugdwh" sourceRef="Gateway_19pq04b" targetRef="Gateway_0dmci7v" />
    <bpmn:exclusiveGateway id="Gateway_1rm6qgm" default="Flow_071p2qh">
      <bpmn:incoming>Flow_0ue4qoh</bpmn:incoming>
      <bpmn:outgoing>Flow_071p2qh</bpmn:outgoing>
      <bpmn:outgoing>Flow_11bswkv</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_071p2qh" sourceRef="Gateway_1rm6qgm" targetRef="Gateway_0dmci7v" />
    <bpmn:endEvent id="Event_01g9025">
      <bpmn:incoming>Flow_1i49s2f</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_1cwrba6">
      <bpmn:incoming>Flow_0lqgd2u</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_0g1os6y">
      <bpmn:incoming>Flow_1vntpno</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_1bqonsk">
      <bpmn:incoming>Flow_11bswkv</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1vntpno" name="Failure" sourceRef="Gateway_19pq04b" targetRef="Event_0g1os6y">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_11bswkv" name="Failure" sourceRef="Gateway_1rm6qgm" targetRef="Event_1bqonsk">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1i49s2f" name="Failure" sourceRef="Gateway_0i9jx78" targetRef="Event_01g9025">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0lqgd2u" name="Failure" sourceRef="Gateway_0gcdp7v" targetRef="Event_1cwrba6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="UpdateSubscriptionStatus">
      <bpmndi:BPMNShape id="Activity_0oi7fzj_di" bpmnElement="BS_PauseUpdateSubscriptionStatus">
        <dc:Bounds x="510" y="300" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1qkykgh_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="1492" y="322" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0xfvfbf_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="322" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_056686s_di" bpmnElement="Gateway_056686s" isMarkerVisible="true">
        <dc:Bounds x="285" y="315" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="351" y="353" width="77" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1o4yaet" bpmnElement="Gateway_0dmci7v" isMarkerVisible="true">
        <dc:Bounds x="1315" y="315" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ei0i2y" bpmnElement="BS_ResumeUpdateSubscriptionStatus">
        <dc:Bounds x="510" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_15robbo" bpmnElement="ESB_PauseUpdateSubscriptionStatus">
        <dc:Bounds x="930" y="300" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ggs2i4" bpmnElement="ESB_ResumeUpdateSubscriptionStatus">
        <dc:Bounds x="930" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ufjw36" bpmnElement="Gateway_0gcdp7v" isMarkerVisible="true">
        <dc:Bounds x="715" y="315" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1dg6t50" bpmnElement="Gateway_0i9jx78" isMarkerVisible="true">
        <dc:Bounds x="715" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_11hwpsk" bpmnElement="Gateway_19pq04b" isMarkerVisible="true">
        <dc:Bounds x="1165" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1aznghx" bpmnElement="Gateway_1rm6qgm" isMarkerVisible="true">
        <dc:Bounds x="1155" y="315" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0do1n1c" bpmnElement="Event_01g9025">
        <dc:Bounds x="722" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ekwxhq" bpmnElement="Event_1cwrba6">
        <dc:Bounds x="722" y="442" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1gw5sbm" bpmnElement="Event_0g1os6y">
        <dc:Bounds x="1172" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1sd71tn" bpmnElement="Event_1bqonsk">
        <dc:Bounds x="1162" y="442" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1nzkyg3_di" bpmnElement="Flow_1nzkyg3">
        <di:waypoint x="610" y="340" />
        <di:waypoint x="715" y="340" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10r104l_di" bpmnElement="Flow_10r104l">
        <di:waypoint x="188" y="340" />
        <di:waypoint x="285" y="340" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xru2zu_di" bpmnElement="Flow_1xru2zu">
        <di:waypoint x="335" y="340" />
        <di:waypoint x="510" y="340" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15ddmqi_di" bpmnElement="Flow_15ddmqi">
        <di:waypoint x="1365" y="340" />
        <di:waypoint x="1492" y="340" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c7tyzp_di" bpmnElement="Flow_0c7tyzp">
        <di:waypoint x="310" y="315" />
        <di:waypoint x="310" y="120" />
        <di:waypoint x="510" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="328" y="93" width="83" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17hqbdt_di" bpmnElement="Flow_17hqbdt">
        <di:waypoint x="610" y="120" />
        <di:waypoint x="715" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ue4qoh_di" bpmnElement="Flow_0ue4qoh">
        <di:waypoint x="1030" y="340" />
        <di:waypoint x="1155" y="340" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bjpb9h_di" bpmnElement="Flow_0bjpb9h">
        <di:waypoint x="1030" y="120" />
        <di:waypoint x="1165" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11z71kw_di" bpmnElement="Flow_11z71kw">
        <di:waypoint x="765" y="340" />
        <di:waypoint x="930" y="340" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06mdgfg_di" bpmnElement="Flow_06mdgfg">
        <di:waypoint x="765" y="120" />
        <di:waypoint x="930" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16ugdwh_di" bpmnElement="Flow_16ugdwh">
        <di:waypoint x="1215" y="120" />
        <di:waypoint x="1340" y="120" />
        <di:waypoint x="1340" y="315" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_071p2qh_di" bpmnElement="Flow_071p2qh">
        <di:waypoint x="1205" y="340" />
        <di:waypoint x="1315" y="340" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vntpno_di" bpmnElement="Flow_1vntpno">
        <di:waypoint x="1190" y="145" />
        <di:waypoint x="1190" y="202" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1202" y="163" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11bswkv_di" bpmnElement="Flow_11bswkv">
        <di:waypoint x="1180" y="365" />
        <di:waypoint x="1180" y="442" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1192" y="401" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1i49s2f_di" bpmnElement="Flow_1i49s2f">
        <di:waypoint x="740" y="145" />
        <di:waypoint x="740" y="202" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="752" y="171" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lqgd2u_di" bpmnElement="Flow_0lqgd2u">
        <di:waypoint x="740" y="365" />
        <di:waypoint x="740" y="442" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="752" y="401" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
