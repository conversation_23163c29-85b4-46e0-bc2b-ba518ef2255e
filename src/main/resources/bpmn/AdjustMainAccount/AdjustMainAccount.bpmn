<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/********/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1pxbtg1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="AdjustMainAccount" name="AdjustMainAccount" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_1lwtzik</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0vl1b9j</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0vl1b9j" sourceRef="NCCAdjustMABalance" targetRef="orderExecEnd" />
    <bpmn:serviceTask id="NCCAdjustMABalance" name="Adjust Main Account Balance in NCC" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1lwtzik</bpmn:incoming>
      <bpmn:outgoing>Flow_0vl1b9j</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1lwtzik" sourceRef="orderExecStart" targetRef="NCCAdjustMABalance" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="AdjustMainAccount">
      <bpmndi:BPMNEdge id="Flow_1lwtzik_di" bpmnElement="Flow_1lwtzik">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="300" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vl1b9j_di" bpmnElement="Flow_0vl1b9j">
        <di:waypoint x="400" y="117" />
        <di:waypoint x="472" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="179" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0autycf_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="472" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0f14l3b_di" bpmnElement="NCCAdjustMABalance">
        <dc:Bounds x="300" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
