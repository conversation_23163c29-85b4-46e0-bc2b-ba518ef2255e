<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.0.0">
  <bpmn:process id="ResumeSubscription" name="ResumeSubscription" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:callActivity id="ResumeSubscription-In-Networks" name="Resume Subscription in Networks" camunda:asyncBefore="true" calledElement="ResumeSubscription-In-Networks" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:out variables="all" />
        <camunda:in variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1cyr95c</bpmn:incoming>
      <bpmn:outgoing>Flow_1pkobkl</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="${workflowData.jsonPath(&#34;$.order.orderItem&#34;).elementList()}" camunda:elementVariable="executionData">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1pkobkl</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1pkobkl" sourceRef="ResumeSubscription-In-Networks" targetRef="orderExecEnd" />
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1cyr95c</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1cyr95c" sourceRef="orderExecStart" targetRef="ResumeSubscription-In-Networks" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ResumeSubscription">
      <bpmndi:BPMNEdge id="Flow_1cyr95c_di" bpmnElement="Flow_1cyr95c">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="270" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pkobkl_di" bpmnElement="Flow_1pkobkl">
        <di:waypoint x="370" y="120" />
        <di:waypoint x="452" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_1f8y406_di" bpmnElement="ResumeSubscription-In-Networks">
        <dc:Bounds x="270" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_03c4vy4_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="452" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_12d45fr_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
