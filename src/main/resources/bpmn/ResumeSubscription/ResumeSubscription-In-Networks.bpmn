<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1e35mi7" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="ResumeSubscription-In-Networks" name="ResumeSubscription-In-Networks" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:sequenceFlow id="Flow_0b3mvr0" sourceRef="SOMResumesubscription" targetRef="Gateway_0u20tmo" />
    <bpmn:sequenceFlow id="Flow_1lwpxyt" name="Success" sourceRef="Gateway_0u20tmo" targetRef="SOMResumeSubCallback">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="SOMResumesubscription" name="SOM Resume Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${somSuspendResumeHandler}">
      <bpmn:incoming>Flow_1hri4bc</bpmn:incoming>
      <bpmn:outgoing>Flow_0b3mvr0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="subProcessEndEvent">
      <bpmn:incoming>Flow_1qw0nz7</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_0naf63l">
      <bpmn:incoming>Flow_0bjdx25</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0u20tmo">
      <bpmn:incoming>Flow_0b3mvr0</bpmn:incoming>
      <bpmn:outgoing>Flow_0bjdx25</bpmn:outgoing>
      <bpmn:outgoing>Flow_1lwpxyt</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0bjdx25" name="Failure" sourceRef="Gateway_0u20tmo" targetRef="Event_0naf63l">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="SOMResumeSubCallback" name="SOMResumeSubCallback" messageRef="Message_1mywhlp">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOMResumesubscription</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOMResumesubscription" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1lwpxyt</bpmn:incoming>
      <bpmn:outgoing>Flow_1phjk8g</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_06tyh9c" default="Flow_0p49gq8">
      <bpmn:incoming>Flow_1phjk8g</bpmn:incoming>
      <bpmn:outgoing>Flow_0ug4m79</bpmn:outgoing>
      <bpmn:outgoing>Flow_0p49gq8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1phjk8g" sourceRef="SOMResumeSubCallback" targetRef="Gateway_06tyh9c" />
    <bpmn:endEvent id="Event_1wcw3sf">
      <bpmn:incoming>Flow_0p49gq8</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSUpdateSubscription" name="Update Subscription in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1q2vukn</bpmn:incoming>
      <bpmn:outgoing>Flow_0i876sc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ug4m79" name="Success" sourceRef="Gateway_06tyh9c" targetRef="Gateway_05e0qgc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="SMResumeOffer" name="Resume Offer in SM" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1vxnknr</bpmn:incoming>
      <bpmn:outgoing>Flow_1y8of06</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0p49gq8" name="Failure" sourceRef="Gateway_06tyh9c" targetRef="Event_1wcw3sf" />
    <bpmn:exclusiveGateway id="Gateway_0ahl38m" default="Flow_05khro2">
      <bpmn:incoming>Flow_1y8of06</bpmn:incoming>
      <bpmn:outgoing>Flow_13y0qd5</bpmn:outgoing>
      <bpmn:outgoing>Flow_05khro2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1y8of06" sourceRef="SMResumeOffer" targetRef="Gateway_0ahl38m" />
    <bpmn:sequenceFlow id="Flow_13y0qd5" name="Success" sourceRef="Gateway_0ahl38m" targetRef="Gateway_0zxwfka">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_0nct4qh">
      <bpmn:incoming>Flow_05khro2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_05khro2" name="Failure" sourceRef="Gateway_0ahl38m" targetRef="Event_0nct4qh" />
    <bpmn:startEvent id="Event_0u3f3vz">
      <bpmn:outgoing>Flow_0csrc0f</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_05akw1w" default="Flow_0disqv3">
      <bpmn:incoming>Flow_02p9qvd</bpmn:incoming>
      <bpmn:outgoing>Flow_0disqv3</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ky6mh8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1cosdev">
      <bpmn:incoming>Flow_0disqv3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0disqv3" name="Failure" sourceRef="Gateway_05akw1w" targetRef="Event_1cosdev" />
    <bpmn:sequenceFlow id="Flow_0ky6mh8" name="Success" sourceRef="Gateway_05akw1w" targetRef="Gateway_0yjjqo3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0i876sc" sourceRef="BSUpdateSubscription" targetRef="Gateway_002kfba" />
    <bpmn:sequenceFlow id="Flow_0csrc0f" sourceRef="Event_0u3f3vz" targetRef="SOMFetchServiceRegistry" />
    <bpmn:exclusiveGateway id="Gateway_0y4g7ku" default="Flow_1sa3rzq">
      <bpmn:incoming>Flow_0ob6lyv</bpmn:incoming>
      <bpmn:outgoing>Flow_1vfdgrw</bpmn:outgoing>
      <bpmn:outgoing>Flow_1sa3rzq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1vfdgrw" name="Success" sourceRef="Gateway_0y4g7ku" targetRef="Gateway_13aa2yg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_16chlmz">
      <bpmn:incoming>Flow_1sa3rzq</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1sa3rzq" name="Failure" sourceRef="Gateway_0y4g7ku" targetRef="Event_16chlmz" />
    <bpmn:exclusiveGateway id="Gateway_002kfba" default="Flow_0py7jt1">
      <bpmn:incoming>Flow_0i876sc</bpmn:incoming>
      <bpmn:outgoing>Flow_0ixl909</bpmn:outgoing>
      <bpmn:outgoing>Flow_0py7jt1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ixl909" name="Success" sourceRef="Gateway_002kfba" targetRef="Gateway_0w1x11f">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0w1x11f">
      <bpmn:incoming>Flow_0ixl909</bpmn:incoming>
      <bpmn:incoming>Flow_0y3gz8g</bpmn:incoming>
      <bpmn:outgoing>Flow_1qw0nz7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1qw0nz7" sourceRef="Gateway_0w1x11f" targetRef="subProcessEndEvent" />
    <bpmn:exclusiveGateway id="Gateway_0zxwfka" default="Flow_0y3gz8g">
      <bpmn:incoming>Flow_13y0qd5</bpmn:incoming>
      <bpmn:outgoing>Flow_1q2vukn</bpmn:outgoing>
      <bpmn:outgoing>Flow_0y3gz8g</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1q2vukn" name="yes" sourceRef="Gateway_0zxwfka" targetRef="BSUpdateSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.hasProp('enrichmentResults') &amp;&amp; workflowData.jsonPath("$.enrichmentResults.serviceInfo.chargingPattern").stringValue() == '1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0y3gz8g" name="no" sourceRef="Gateway_0zxwfka" targetRef="Gateway_0w1x11f" />
    <bpmn:endEvent id="Event_18sv733">
      <bpmn:incoming>Flow_0py7jt1</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0py7jt1" name="Failure" sourceRef="Gateway_002kfba" targetRef="Event_18sv733" />
    <bpmn:exclusiveGateway id="Gateway_1qsdx50" name="check som call required or not" default="Flow_0i10drn">
      <bpmn:incoming>Flow_1eap41j</bpmn:incoming>
      <bpmn:outgoing>Flow_1hri4bc</bpmn:outgoing>
      <bpmn:outgoing>Flow_0i10drn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1hri4bc" name="yes" sourceRef="Gateway_1qsdx50" targetRef="SOMResumesubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.hasProp('enrichmentResults')&amp;&amp; workflowData.jsonPath("$.enrichmentResults.somCallRqd").boolValue() == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_05e0qgc">
      <bpmn:incoming>Flow_0ug4m79</bpmn:incoming>
      <bpmn:incoming>Flow_0i10drn</bpmn:incoming>
      <bpmn:outgoing>Flow_1vxnknr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1vxnknr" sourceRef="Gateway_05e0qgc" targetRef="SMResumeOffer" />
    <bpmn:sequenceFlow id="Flow_0i10drn" name="no" sourceRef="Gateway_1qsdx50" targetRef="Gateway_05e0qgc" />
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM FetchServiceRegistry" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0csrc0f</bpmn:incoming>
      <bpmn:outgoing>Flow_02p9qvd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_02p9qvd" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_05akw1w" />
    <bpmn:serviceTask id="NCCResumesubscription" name="Resume Subsciption in NCC" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1fytvtm</bpmn:incoming>
      <bpmn:incoming>Flow_0590gav</bpmn:incoming>
      <bpmn:outgoing>Flow_0ob6lyv</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ob6lyv" sourceRef="NCCResumesubscription" targetRef="Gateway_0y4g7ku" />
    <bpmn:exclusiveGateway id="Gateway_0yjjqo3" name="isNCCAddon" default="Flow_0qsubdc">
      <bpmn:incoming>Flow_0ky6mh8</bpmn:incoming>
      <bpmn:outgoing>Flow_1fytvtm</bpmn:outgoing>
      <bpmn:outgoing>Flow_0590gav</bpmn:outgoing>
      <bpmn:outgoing>Flow_0qsubdc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1fytvtm" sourceRef="Gateway_0yjjqo3" targetRef="NCCResumesubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.hasProp('enrichmentResults')&amp;&amp; workflowData.jsonPath("$.enrichmentResults.esbCallRqd").boolValue() == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_13aa2yg">
      <bpmn:incoming>Flow_1vfdgrw</bpmn:incoming>
      <bpmn:incoming>Flow_0qsubdc</bpmn:incoming>
      <bpmn:outgoing>Flow_1eap41j</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1eap41j" sourceRef="Gateway_13aa2yg" targetRef="Gateway_1qsdx50" />
    <bpmn:sequenceFlow id="Flow_0590gav" name="yes" sourceRef="Gateway_0yjjqo3" targetRef="NCCResumesubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.hasProp('enrichmentResults')&amp;&amp; workflowData.jsonPath("$.enrichmentResults.esbCallRqd").boolValue() == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0qsubdc" name="no" sourceRef="Gateway_0yjjqo3" targetRef="Gateway_13aa2yg" />
  </bpmn:process>
  <bpmn:signal id="Signal_1v1ovxs" name="SOMCallback" />
  <bpmn:error id="Error_1p4sqnl" name="Error_1t8ui0v" />
  <bpmn:message id="Message_1mywhlp" name="SOMResumeSubCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ResumeSubscription-In-Networks">
      <bpmndi:BPMNEdge id="Flow_0qsubdc_di" bpmnElement="Flow_0qsubdc">
        <di:waypoint x="570" y="212" />
        <di:waypoint x="570" y="140" />
        <di:waypoint x="980" y="140" />
        <di:waypoint x="980" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="769" y="122" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0590gav_di" bpmnElement="Flow_0590gav">
        <di:waypoint x="595" y="237" />
        <di:waypoint x="660" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="611" y="219" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eap41j_di" bpmnElement="Flow_1eap41j">
        <di:waypoint x="1005" y="237" />
        <di:waypoint x="1085" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fytvtm_di" bpmnElement="Flow_1fytvtm">
        <di:waypoint x="595" y="237" />
        <di:waypoint x="660" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ob6lyv_di" bpmnElement="Flow_0ob6lyv">
        <di:waypoint x="760" y="237" />
        <di:waypoint x="805" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02p9qvd_di" bpmnElement="Flow_02p9qvd">
        <di:waypoint x="370" y="237" />
        <di:waypoint x="425" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0i10drn_di" bpmnElement="Flow_0i10drn">
        <di:waypoint x="1110" y="212" />
        <di:waypoint x="1110" y="140" />
        <di:waypoint x="1790" y="140" />
        <di:waypoint x="1790" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1444" y="122" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vxnknr_di" bpmnElement="Flow_1vxnknr">
        <di:waypoint x="1815" y="237" />
        <di:waypoint x="1850" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hri4bc_di" bpmnElement="Flow_1hri4bc">
        <di:waypoint x="1135" y="237" />
        <di:waypoint x="1200" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1159" y="219" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0py7jt1_di" bpmnElement="Flow_0py7jt1">
        <di:waypoint x="2410" y="262" />
        <di:waypoint x="2410" y="322" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2409" y="289" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y3gz8g_di" bpmnElement="Flow_0y3gz8g">
        <di:waypoint x="2130" y="212" />
        <di:waypoint x="2130" y="140" />
        <di:waypoint x="2530" y="140" />
        <di:waypoint x="2530" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2324" y="122" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1q2vukn_di" bpmnElement="Flow_1q2vukn">
        <di:waypoint x="2155" y="237" />
        <di:waypoint x="2240" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2189" y="219" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qw0nz7_di" bpmnElement="Flow_1qw0nz7">
        <di:waypoint x="2555" y="237" />
        <di:waypoint x="2602" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ixl909_di" bpmnElement="Flow_0ixl909">
        <di:waypoint x="2435" y="237" />
        <di:waypoint x="2505" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2449" y="219" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sa3rzq_di" bpmnElement="Flow_1sa3rzq">
        <di:waypoint x="830" y="262" />
        <di:waypoint x="830" y="342" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="829" y="288" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vfdgrw_di" bpmnElement="Flow_1vfdgrw">
        <di:waypoint x="855" y="237" />
        <di:waypoint x="955" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="895" y="219" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0csrc0f_di" bpmnElement="Flow_0csrc0f">
        <di:waypoint x="188" y="237" />
        <di:waypoint x="270" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0i876sc_di" bpmnElement="Flow_0i876sc">
        <di:waypoint x="2340" y="237" />
        <di:waypoint x="2385" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ky6mh8_di" bpmnElement="Flow_0ky6mh8">
        <di:waypoint x="475" y="237" />
        <di:waypoint x="545" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="486" y="213" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0disqv3_di" bpmnElement="Flow_0disqv3">
        <di:waypoint x="450" y="262" />
        <di:waypoint x="450" y="322" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="463" y="305" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05khro2_di" bpmnElement="Flow_05khro2">
        <di:waypoint x="2000" y="262" />
        <di:waypoint x="2000" y="322" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2013" y="283" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13y0qd5_di" bpmnElement="Flow_13y0qd5">
        <di:waypoint x="2025" y="237" />
        <di:waypoint x="2105" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2031" y="219" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1y8of06_di" bpmnElement="Flow_1y8of06">
        <di:waypoint x="1950" y="237" />
        <di:waypoint x="1975" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p49gq8_di" bpmnElement="Flow_0p49gq8">
        <di:waypoint x="1680" y="262" />
        <di:waypoint x="1680" y="322" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1683" y="283" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ug4m79_di" bpmnElement="Flow_0ug4m79">
        <di:waypoint x="1705" y="237" />
        <di:waypoint x="1765" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1708" y="219" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1phjk8g_di" bpmnElement="Flow_1phjk8g">
        <di:waypoint x="1580" y="237" />
        <di:waypoint x="1655" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bjdx25_di" bpmnElement="Flow_0bjdx25">
        <di:waypoint x="1390" y="262" />
        <di:waypoint x="1390" y="322" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1393" y="283" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lwpxyt_di" bpmnElement="Flow_1lwpxyt">
        <di:waypoint x="1415" y="237" />
        <di:waypoint x="1480" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1422" y="213" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b3mvr0_di" bpmnElement="Flow_0b3mvr0">
        <di:waypoint x="1300" y="237" />
        <di:waypoint x="1365" y="237" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0re5gsc_di" bpmnElement="SOMResumesubscription">
        <dc:Bounds x="1200" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1bi8xzt_di" bpmnElement="subProcessEndEvent">
        <dc:Bounds x="2602" y="219" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0naf63l_di" bpmnElement="Event_0naf63l">
        <dc:Bounds x="1372" y="322" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0u20tmo_di" bpmnElement="Gateway_0u20tmo" isMarkerVisible="true">
        <dc:Bounds x="1365" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_03e8k2p_di" bpmnElement="SOMResumeSubCallback">
        <dc:Bounds x="1480" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_06tyh9c_di" bpmnElement="Gateway_06tyh9c" isMarkerVisible="true">
        <dc:Bounds x="1655" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1wcw3sf_di" bpmnElement="Event_1wcw3sf">
        <dc:Bounds x="1662" y="322" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0e3ofdc_di" bpmnElement="BSUpdateSubscription">
        <dc:Bounds x="2240" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_003yok6_di" bpmnElement="SMResumeOffer">
        <dc:Bounds x="1850" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ahl38m_di" bpmnElement="Gateway_0ahl38m" isMarkerVisible="true">
        <dc:Bounds x="1975" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0nct4qh_di" bpmnElement="Event_0nct4qh">
        <dc:Bounds x="1982" y="322" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0u3f3vz_di" bpmnElement="Event_0u3f3vz">
        <dc:Bounds x="152" y="219" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_05akw1w_di" bpmnElement="Gateway_05akw1w" isMarkerVisible="true">
        <dc:Bounds x="425" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1cosdev_di" bpmnElement="Event_1cosdev">
        <dc:Bounds x="432" y="322" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0y4g7ku_di" bpmnElement="Gateway_0y4g7ku" isMarkerVisible="true">
        <dc:Bounds x="805" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_16chlmz_di" bpmnElement="Event_16chlmz">
        <dc:Bounds x="812" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_002kfba_di" bpmnElement="Gateway_002kfba" isMarkerVisible="true">
        <dc:Bounds x="2385" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0w1x11f_di" bpmnElement="Gateway_0w1x11f" isMarkerVisible="true">
        <dc:Bounds x="2505" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0zxwfka_di" bpmnElement="Gateway_0zxwfka" isMarkerVisible="true">
        <dc:Bounds x="2105" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_18sv733_di" bpmnElement="Event_18sv733">
        <dc:Bounds x="2392" y="322" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1qsdx50_di" bpmnElement="Gateway_1qsdx50" isMarkerVisible="true">
        <dc:Bounds x="1085" y="212" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1074" y="269" width="73" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_05e0qgc_di" bpmnElement="Gateway_05e0qgc" isMarkerVisible="true">
        <dc:Bounds x="1765" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0xew6dd_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="270" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0o6sb3a_di" bpmnElement="NCCResumesubscription">
        <dc:Bounds x="660" y="197" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0yjjqo3_di" bpmnElement="Gateway_0yjjqo3" isMarkerVisible="true">
        <dc:Bounds x="545" y="212" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="538" y="269" width="64" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_13aa2yg_di" bpmnElement="Gateway_13aa2yg" isMarkerVisible="true">
        <dc:Bounds x="955" y="212" width="50" height="50" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
