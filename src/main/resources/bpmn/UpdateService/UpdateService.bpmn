<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.21.0">
  <bpmn:process id="UpdateService" name="UpdateService" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:serviceTask id="BSUpdateService" name="Update Service in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0lncqw7</bpmn:incoming>
      <bpmn:outgoing>Flow_1je1bh9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0yehpb9" default="Flow_1be5bci">
      <bpmn:incoming>Flow_1je1bh9</bpmn:incoming>
      <bpmn:outgoing>Flow_1be5bci</bpmn:outgoing>
      <bpmn:outgoing>Flow_1lhbgi9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1je1bh9" sourceRef="BSUpdateService" targetRef="Gateway_0yehpb9" />
    <bpmn:sequenceFlow id="Flow_1g0nn66" sourceRef="NCCUpdateLanguageDetails" targetRef="Gateway_0amx7uk" />
    <bpmn:endEvent id="orderExecEnd" camunda:asyncBefore="true">
      <bpmn:incoming>Flow_03pnzu4</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="NCCUpdateLanguageDetails" name="Update Language Details in NCC" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0osg8l4</bpmn:incoming>
      <bpmn:outgoing>Flow_1g0nn66</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1qhvg6k">
      <bpmn:incoming>Flow_1be5bci</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1be5bci" name="Failure" sourceRef="Gateway_0yehpb9" targetRef="Event_1qhvg6k" />
    <bpmn:sequenceFlow id="Flow_1lhbgi9" name="Success" sourceRef="Gateway_0yehpb9" targetRef="Gateway_0iszo9d">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0iszo9d" name="check if language id updation is required or not" default="Flow_0k2luxr">
      <bpmn:incoming>Flow_1lhbgi9</bpmn:incoming>
      <bpmn:outgoing>Flow_0osg8l4</bpmn:outgoing>
      <bpmn:outgoing>Flow_0k2luxr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0osg8l4" name="yes" sourceRef="Gateway_0iszo9d" targetRef="NCCUpdateLanguageDetails">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${esbUpdateCallReq=='true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0amx7uk">
      <bpmn:incoming>Flow_1g0nn66</bpmn:incoming>
      <bpmn:incoming>Flow_0k2luxr</bpmn:incoming>
      <bpmn:outgoing>Flow_03pnzu4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_03pnzu4" sourceRef="Gateway_0amx7uk" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_0k2luxr" name="no" sourceRef="Gateway_0iszo9d" targetRef="Gateway_0amx7uk" />
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0lncqw7</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0lncqw7" sourceRef="orderExecStart" targetRef="BSUpdateService" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="UpdateService">
      <bpmndi:BPMNShape id="Activity_0oi7fzj_di" bpmnElement="BSUpdateService">
        <dc:Bounds x="290" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0yehpb9_di" bpmnElement="Gateway_0yehpb9" isMarkerVisible="true">
        <dc:Bounds x="475" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0n6iqy1_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="1192" y="192" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1tegtwq_di" bpmnElement="NCCUpdateLanguageDetails">
        <dc:Bounds x="800" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1qhvg6k_di" bpmnElement="Event_1qhvg6k">
        <dc:Bounds x="482" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0iszo9d_di" bpmnElement="Gateway_0iszo9d" isMarkerVisible="true">
        <dc:Bounds x="635" y="185" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="618" y="242" width="86" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0amx7uk_di" bpmnElement="Gateway_0amx7uk" isMarkerVisible="true">
        <dc:Bounds x="995" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0k8kz6l_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="192" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1je1bh9_di" bpmnElement="Flow_1je1bh9">
        <di:waypoint x="390" y="210" />
        <di:waypoint x="475" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g0nn66_di" bpmnElement="Flow_1g0nn66">
        <di:waypoint x="900" y="210" />
        <di:waypoint x="995" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1be5bci_di" bpmnElement="Flow_1be5bci">
        <di:waypoint x="500" y="235" />
        <di:waypoint x="500" y="342" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="499" y="286" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lhbgi9_di" bpmnElement="Flow_1lhbgi9">
        <di:waypoint x="525" y="210" />
        <di:waypoint x="635" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="532" y="192" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0osg8l4_di" bpmnElement="Flow_0osg8l4">
        <di:waypoint x="685" y="210" />
        <di:waypoint x="800" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="734" y="192" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03pnzu4_di" bpmnElement="Flow_03pnzu4">
        <di:waypoint x="1045" y="210" />
        <di:waypoint x="1192" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0k2luxr_di" bpmnElement="Flow_0k2luxr">
        <di:waypoint x="660" y="185" />
        <di:waypoint x="660" y="120" />
        <di:waypoint x="1020" y="120" />
        <di:waypoint x="1020" y="185" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="834" y="102" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lncqw7_di" bpmnElement="Flow_0lncqw7">
        <di:waypoint x="188" y="210" />
        <di:waypoint x="290" y="210" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
