<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_103nwfv" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.20.0">
  <bpmn:process id="BillOnDemand" name="BillOnDemand" isExecutable="true" camunda:historyTimeToLive="180">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_13hfo81</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_13hfo81" sourceRef="orderExecStart" targetRef="BS_BillOnDemand" />
    <bpmn:endEvent id="Event_09gvo99">
      <bpmn:incoming>orderExecEnd</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="orderExecEnd" sourceRef="BS_BillOnDemand" targetRef="Event_09gvo99" />
    <bpmn:serviceTask id="BS_BillOnDemand" name="BS Bill-On-Demand" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_13hfo81</bpmn:incoming>
      <bpmn:outgoing>orderExecEnd</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="BillOnDemand">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="179" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_09gvo99_di" bpmnElement="Event_09gvo99">
        <dc:Bounds x="432" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0vmbgui_di" bpmnElement="BS_BillOnDemand">
        <dc:Bounds x="270" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_13hfo81_di" bpmnElement="Flow_13hfo81">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="270" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jvcgoz_di" bpmnElement="orderExecEnd">
        <di:waypoint x="370" y="117" />
        <di:waypoint x="432" y="117" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
