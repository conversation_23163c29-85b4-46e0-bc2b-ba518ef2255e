<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0">
  <bpmn:process id="InterimPortInFromExtTelcoOrSingtel" name="InterimPortInFromExtTelcoOrSingtel" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="Event_1hjhhcv">
      <bpmn:outgoing>Flow_0i6hynl</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="SDPSubmitPortIn" name="Submit port in request to Singtel SDP" camunda:asyncBefore="true" camunda:delegateExpression="${submitPortIn}">
      <bpmn:incoming>Flow_1ooyui5</bpmn:incoming>
      <bpmn:outgoing>Flow_0x790bn</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0eb5jrb" default="Flow_1uusmv0">
      <bpmn:incoming>Flow_0x790bn</bpmn:incoming>
      <bpmn:outgoing>Flow_1x6vcfz</bpmn:outgoing>
      <bpmn:outgoing>Flow_1uusmv0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0x790bn" sourceRef="SDPSubmitPortIn" targetRef="Gateway_0eb5jrb" />
    <bpmn:endEvent id="Event_052uwjo">
      <bpmn:incoming>Flow_1x6vcfz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1x6vcfz" name="Failure" sourceRef="Gateway_0eb5jrb" targetRef="Event_052uwjo">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1uusmv0" name="Success" sourceRef="Gateway_0eb5jrb" targetRef="ConnectServiceCallback" />
    <bpmn:serviceTask id="SOM_ChangeMsisdn" name="Change MSISDN in HLR via SOM" camunda:asyncBefore="true" camunda:delegateExpression="${somChangeMsisdn}">
      <bpmn:incoming>Flow_1gedgrn</bpmn:incoming>
      <bpmn:outgoing>Flow_0ax48eu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0paffsv" default="Flow_1h5qypc">
      <bpmn:incoming>Flow_0ax48eu</bpmn:incoming>
      <bpmn:outgoing>Flow_0dm3ryx</bpmn:outgoing>
      <bpmn:outgoing>Flow_1h5qypc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ax48eu" sourceRef="SOM_ChangeMsisdn" targetRef="Gateway_0paffsv" />
    <bpmn:endEvent id="Event_05qjnsn">
      <bpmn:incoming>Flow_0dm3ryx</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0dm3ryx" name="Failure" sourceRef="Gateway_0paffsv" targetRef="Event_05qjnsn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1h5qypc" name="Success" sourceRef="Gateway_0paffsv" targetRef="SOMChangeMsisdnCallback" />
    <bpmn:receiveTask id="SOMChangeMsisdnCallback" name="Wait for SOM callback" camunda:asyncBefore="true" messageRef="Message_14c63bs">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOM_ChangeMsisdn</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOM_ChangeMsisdn" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1h5qypc</bpmn:incoming>
      <bpmn:outgoing>Flow_18rb9ws</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_0oq1n56" default="Flow_0xrblsx">
      <bpmn:incoming>Flow_18rb9ws</bpmn:incoming>
      <bpmn:outgoing>Flow_0xrblsx</bpmn:outgoing>
      <bpmn:outgoing>Flow_1dwn11l</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_18rb9ws" sourceRef="SOMChangeMsisdnCallback" targetRef="Gateway_0oq1n56" />
    <bpmn:sequenceFlow id="Flow_0xrblsx" name="Success" sourceRef="Gateway_0oq1n56" targetRef="OCSChangeMsisdn" />
    <bpmn:endEvent id="Event_0bb5bnp">
      <bpmn:incoming>Flow_1dwn11l</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1dwn11l" name="Failure" sourceRef="Gateway_0oq1n56" targetRef="Event_0bb5bnp">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0iwqp7u" default="Flow_0me3v0o">
      <bpmn:incoming>Flow_1wc6i9r</bpmn:incoming>
      <bpmn:outgoing>Flow_1t5yaxs</bpmn:outgoing>
      <bpmn:outgoing>Flow_0me3v0o</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1wc6i9r" sourceRef="OCSChangeMsisdn" targetRef="Gateway_0iwqp7u" />
    <bpmn:endEvent id="Event_08x8wf7">
      <bpmn:incoming>Flow_1t5yaxs</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1t5yaxs" name="Failure" sourceRef="Gateway_0iwqp7u" targetRef="Event_08x8wf7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSChangeMsisdn" name="Change MSISDN in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0me3v0o</bpmn:incoming>
      <bpmn:outgoing>Flow_1s9ykb6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="subProcessEndEvent_1" camunda:asyncBefore="true">
      <bpmn:incoming>Flow_0wb2isj</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1s9ykb6" sourceRef="BSChangeMsisdn" targetRef="Gateway_1o8bm79" />
    <bpmn:serviceTask id="OCSChangeMsisdn" name="Change MSISDN in OCS Via ESB" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0xrblsx</bpmn:incoming>
      <bpmn:outgoing>Flow_1wc6i9r</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0i6hynl" sourceRef="Event_1hjhhcv" targetRef="Gateway_1tpxnco" />
    <bpmn:sequenceFlow id="Flow_0me3v0o" name="Success" sourceRef="Gateway_0iwqp7u" targetRef="BSChangeMsisdn" />
    <bpmn:receiveTask id="ConnectServiceCallback" name="Wait for connect service callback" camunda:asyncBefore="true" messageRef="Message_0qgbj08">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SDPSubmitPortIn" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1uusmv0</bpmn:incoming>
      <bpmn:outgoing>Flow_1b2oepf</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_1b2oepf" sourceRef="ConnectServiceCallback" targetRef="Gateway_08dsmw0" />
    <bpmn:exclusiveGateway id="Gateway_1o8bm79" default="Flow_0a9yinq">
      <bpmn:incoming>Flow_1s9ykb6</bpmn:incoming>
      <bpmn:outgoing>Flow_14ajiqx</bpmn:outgoing>
      <bpmn:outgoing>Flow_0a9yinq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_19v937h">
      <bpmn:incoming>Flow_14ajiqx</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_14ajiqx" name="Failure" sourceRef="Gateway_1o8bm79" targetRef="Event_19v937h">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0a9yinq" name="Success" sourceRef="Gateway_1o8bm79" targetRef="OCS_ServicePortDetailsUpdate" />
    <bpmn:serviceTask id="OCS_ServicePortDetailsUpdate" name="OCS ServicePortDetailsUpdate" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0a9yinq</bpmn:incoming>
      <bpmn:outgoing>Flow_1h9dh1k</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1h9dh1k" sourceRef="OCS_ServicePortDetailsUpdate" targetRef="Gateway_1u1j8vl" />
    <bpmn:exclusiveGateway id="Gateway_1qvsl1k" default="Flow_1gedgrn">
      <bpmn:incoming>SequenceFlow_0ljkt7u</bpmn:incoming>
      <bpmn:outgoing>Flow_02e3uev</bpmn:outgoing>
      <bpmn:outgoing>Flow_1gedgrn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_01pb4gg">
      <bpmn:incoming>Flow_02e3uev</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="SOMFetchservices" name="SOM Fetch Service Registry" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0nzkac3</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0ljkt7u</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0ljkt7u" sourceRef="SOMFetchservices" targetRef="Gateway_1qvsl1k" />
    <bpmn:sequenceFlow id="Flow_02e3uev" name="Failure" sourceRef="Gateway_1qvsl1k" targetRef="Event_01pb4gg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1gedgrn" name="Success" sourceRef="Gateway_1qvsl1k" targetRef="SOM_ChangeMsisdn" />
    <bpmn:exclusiveGateway id="Gateway_1tpxnco" name="PortinType" default="Flow_1ooyui5">
      <bpmn:incoming>Flow_0i6hynl</bpmn:incoming>
      <bpmn:outgoing>Flow_1ooyui5</bpmn:outgoing>
      <bpmn:outgoing>Flow_15wum3k</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ooyui5" name="External" sourceRef="Gateway_1tpxnco" targetRef="SDPSubmitPortIn" />
    <bpmn:exclusiveGateway id="Gateway_08dsmw0">
      <bpmn:incoming>Flow_1b2oepf</bpmn:incoming>
      <bpmn:incoming>Flow_1my4vra</bpmn:incoming>
      <bpmn:outgoing>Flow_1s4nx36</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_15wum3k" name="Singtel" sourceRef="Gateway_1tpxnco" targetRef="SDPSubmitPortInInternal">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portInType=='PortInFromSingtel'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="SDPSubmitPortInInternal" name="Submit port in internal " camunda:asyncBefore="true" camunda:delegateExpression="${submitPortInInternal}">
      <bpmn:incoming>Flow_15wum3k</bpmn:incoming>
      <bpmn:outgoing>Flow_00nqfml</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0by1zan" default="Flow_036fjqb">
      <bpmn:incoming>Flow_00nqfml</bpmn:incoming>
      <bpmn:outgoing>Flow_0tr8b3n</bpmn:outgoing>
      <bpmn:outgoing>Flow_036fjqb</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_106pd0j">
      <bpmn:incoming>Flow_0tr8b3n</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:intermediateCatchEvent id="MNPChangeProductTimer" name="MNPChangeProductTimer" camunda:asyncBefore="true">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1gvpnv7</bpmn:incoming>
      <bpmn:outgoing>Flow_1o98hl7</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_0oqhtc5">
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${mnpChangeProductDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:serviceTask id="MNPChangeProductDateCalculator" name="Calculate change product date" camunda:asyncBefore="true" camunda:delegateExpression="${mnpChangeProductDateCalculator}">
      <bpmn:incoming>Flow_036fjqb</bpmn:incoming>
      <bpmn:outgoing>Flow_1gvpnv7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="SDPChangeProductStatus" name="Change Product Status" camunda:asyncBefore="true" camunda:delegateExpression="${changeProductStatus}">
      <bpmn:incoming>Flow_1o98hl7</bpmn:incoming>
      <bpmn:outgoing>Flow_1c7rz14</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1q8hui8" default="Flow_0ea3zl4">
      <bpmn:incoming>Flow_1c7rz14</bpmn:incoming>
      <bpmn:outgoing>Flow_0ea3zl4</bpmn:outgoing>
      <bpmn:outgoing>Flow_1hsp6sc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0x1103f">
      <bpmn:incoming>Flow_1hsp6sc</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:intermediateCatchEvent id="MNPConnectServiceIntTimer" name="MNPConnectServiceInt Timer" camunda:asyncBefore="true">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0vkftax</bpmn:incoming>
      <bpmn:outgoing>Flow_1my4vra</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1h0htq8">
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${mnpConnectServiceIntDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:serviceTask id="MNPConnectServiceDateCalculator" name="Calculate Connect Service date" camunda:asyncBefore="true" camunda:delegateExpression="${mnpConnectServiceDateCalculator}">
      <bpmn:incoming>Flow_0ea3zl4</bpmn:incoming>
      <bpmn:outgoing>Flow_0vkftax</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_00nqfml" sourceRef="SDPSubmitPortInInternal" targetRef="Gateway_0by1zan" />
    <bpmn:sequenceFlow id="Flow_0tr8b3n" name="Failure" sourceRef="Gateway_0by1zan" targetRef="Event_106pd0j">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_036fjqb" name="Success" sourceRef="Gateway_0by1zan" targetRef="MNPChangeProductDateCalculator" />
    <bpmn:sequenceFlow id="Flow_1gvpnv7" sourceRef="MNPChangeProductDateCalculator" targetRef="MNPChangeProductTimer" />
    <bpmn:sequenceFlow id="Flow_1o98hl7" sourceRef="MNPChangeProductTimer" targetRef="SDPChangeProductStatus" />
    <bpmn:sequenceFlow id="Flow_1c7rz14" sourceRef="SDPChangeProductStatus" targetRef="Gateway_1q8hui8" />
    <bpmn:sequenceFlow id="Flow_0ea3zl4" name="success" sourceRef="Gateway_1q8hui8" targetRef="MNPConnectServiceDateCalculator" />
    <bpmn:sequenceFlow id="Flow_1hsp6sc" name="Failure" sourceRef="Gateway_1q8hui8" targetRef="Event_0x1103f">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0vkftax" sourceRef="MNPConnectServiceDateCalculator" targetRef="MNPConnectServiceIntTimer" />
    <bpmn:sequenceFlow id="Flow_1my4vra" sourceRef="MNPConnectServiceIntTimer" targetRef="Gateway_08dsmw0" />
    <bpmn:exclusiveGateway id="Gateway_1u1j8vl" default="Flow_1jpn6y9">
      <bpmn:incoming>Flow_1h9dh1k</bpmn:incoming>
      <bpmn:outgoing>Flow_1jpn6y9</bpmn:outgoing>
      <bpmn:outgoing>Flow_1va6saf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1jpn6y9" name="Success" sourceRef="Gateway_1u1j8vl" targetRef="NMS_PortInSwap" />
    <bpmn:endEvent id="Event_0npmejb">
      <bpmn:incoming>Flow_1va6saf</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1va6saf" name="Failure" sourceRef="Gateway_1u1j8vl" targetRef="Event_0npmejb">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="NMS_PortInSwap" name="Port in Swap in NMS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1jpn6y9</bpmn:incoming>
      <bpmn:outgoing>Flow_0wb2isj</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0wb2isj" sourceRef="NMS_PortInSwap" targetRef="subProcessEndEvent_1" />
    <bpmn:sequenceFlow id="Flow_1s4nx36" sourceRef="Gateway_08dsmw0" targetRef="ImsiBSFetchService" />
    <bpmn:exclusiveGateway id="Gateway_1tapcpk" default="Flow_0nzkac3">
      <bpmn:incoming>Flow_0l1ovw5</bpmn:incoming>
      <bpmn:outgoing>Flow_0nzkac3</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bn5f0c</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0l1ovw5" sourceRef="ImsiBSFetchService" targetRef="Gateway_1tapcpk" />
    <bpmn:sequenceFlow id="Flow_0nzkac3" name="Success" sourceRef="Gateway_1tapcpk" targetRef="SOMFetchservices" />
    <bpmn:endEvent id="Event_052f6vy">
      <bpmn:incoming>Flow_0bn5f0c</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0bn5f0c" name="Failure" sourceRef="Gateway_1tapcpk" targetRef="Event_052f6vy">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="ImsiBSFetchService" name="IMSI_BS_FETCH_SERVICE" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1s4nx36</bpmn:incoming>
      <bpmn:outgoing>Flow_0l1ovw5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:textAnnotation id="TextAnnotation_0giitpa">
      <bpmn:text>fetch old Msisdn</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1mzcgz0" sourceRef="SOMFetchservices" targetRef="TextAnnotation_0giitpa" />
  </bpmn:process>
  <bpmn:message id="Message_18lupa4" name="MNPCallBack" />
  <bpmn:message id="Message_0cxkag2" />
  <bpmn:message id="Message_0uyo7lb" />
  <bpmn:message id="Message_0z0mmot" name="MNPCallBack" />
  <bpmn:message id="Message_14c63bs" name="SOMChangeMsisdnCallback" />
  <bpmn:message id="Message_0qgbj08" name="ConnectServiceCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="InterimPortInFromExtTelcoOrSingtel">
      <bpmndi:BPMNShape id="Event_1hjhhcv_di" bpmnElement="Event_1hjhhcv">
        <dc:Bounds x="152" y="242" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ldqej4_di" bpmnElement="SDPSubmitPortIn">
        <dc:Bounds x="480" y="220" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0eb5jrb_di" bpmnElement="Gateway_0eb5jrb" isMarkerVisible="true">
        <dc:Bounds x="705" y="235" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_052uwjo_di" bpmnElement="Event_052uwjo">
        <dc:Bounds x="712" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1byzys4" bpmnElement="ConnectServiceCallback">
        <dc:Bounds x="960" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tpxnco_di" bpmnElement="Gateway_1tpxnco" isMarkerVisible="true">
        <dc:Bounds x="255" y="235" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="254" y="292" width="54" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_08dsmw0_di" bpmnElement="Gateway_08dsmw0" isMarkerVisible="true">
        <dc:Bounds x="1445" y="235" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0rqsj6k" bpmnElement="SDPSubmitPortInInternal">
        <dc:Bounds x="360" y="50" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0exy4cb" bpmnElement="Gateway_0by1zan" isMarkerVisible="true">
        <dc:Bounds x="495" y="65" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ab70y0" bpmnElement="Event_106pd0j">
        <dc:Bounds x="502" y="152" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1e0z73s" bpmnElement="MNPChangeProductTimer">
        <dc:Bounds x="762" y="72" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="740" y="115" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1lgu8v2" bpmnElement="MNPChangeProductDateCalculator">
        <dc:Bounds x="610" y="50" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_01mqaj1" bpmnElement="SDPChangeProductStatus">
        <dc:Bounds x="860" y="50" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1q8hui8_di" bpmnElement="Gateway_1q8hui8" isMarkerVisible="true">
        <dc:Bounds x="1015" y="65" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0x1103f_di" bpmnElement="Event_0x1103f">
        <dc:Bounds x="1022" y="142" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1mzwi0d_di" bpmnElement="MNPConnectServiceIntTimer">
        <dc:Bounds x="1312" y="72" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1287" y="115" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0weliw6" bpmnElement="MNPConnectServiceDateCalculator">
        <dc:Bounds x="1160" y="50" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_00ilai3_di" bpmnElement="subProcessEndEvent_1">
        <dc:Bounds x="4572" y="242" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0c3fudt" bpmnElement="NMS_PortInSwap">
        <dc:Bounds x="4310" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0v4rt8j" bpmnElement="Gateway_1u1j8vl" isMarkerVisible="true">
        <dc:Bounds x="4145" y="235" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1x2yg35" bpmnElement="Event_0npmejb">
        <dc:Bounds x="4152" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_15kqr60" bpmnElement="OCS_ServicePortDetailsUpdate">
        <dc:Bounds x="3950" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_18lpbkm" bpmnElement="Gateway_1o8bm79" isMarkerVisible="true">
        <dc:Bounds x="3805" y="235" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0opl9zx" bpmnElement="Event_19v937h">
        <dc:Bounds x="3812" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1jj6utl_di" bpmnElement="BSChangeMsisdn">
        <dc:Bounds x="3620" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0iwqp7u_di" bpmnElement="Gateway_0iwqp7u" isMarkerVisible="true">
        <dc:Bounds x="3455" y="235" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_08x8wf7_di" bpmnElement="Event_08x8wf7">
        <dc:Bounds x="3462" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1qccgh3_di" bpmnElement="OCSChangeMsisdn">
        <dc:Bounds x="3270" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0oq1n56_di" bpmnElement="Gateway_0oq1n56" isMarkerVisible="true">
        <dc:Bounds x="3135" y="235" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0bb5bnp_di" bpmnElement="Event_0bb5bnp">
        <dc:Bounds x="3142" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tapcpk_di" bpmnElement="Gateway_1tapcpk" isMarkerVisible="true">
        <dc:Bounds x="1805" y="235" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0hs1oog_di" bpmnElement="SOMFetchservices">
        <dc:Bounds x="1970" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1qvsl1k_di" bpmnElement="Gateway_1qvsl1k" isMarkerVisible="true">
        <dc:Bounds x="2215" y="235" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_01pb4gg_di" bpmnElement="Event_01pb4gg">
        <dc:Bounds x="2222" y="362" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_17ma6e6_di" bpmnElement="SOM_ChangeMsisdn">
        <dc:Bounds x="2430" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0paffsv_di" bpmnElement="Gateway_0paffsv" isMarkerVisible="true">
        <dc:Bounds x="2685" y="235" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_05qjnsn_di" bpmnElement="Event_05qjnsn">
        <dc:Bounds x="2692" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0i53tmd_di" bpmnElement="SOMChangeMsisdnCallback">
        <dc:Bounds x="2890" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_052f6vy_di" bpmnElement="Event_052f6vy">
        <dc:Bounds x="1812" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0kyugjl_di" bpmnElement="ImsiBSFetchService">
        <dc:Bounds x="1600" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_1mzcgz0_di" bpmnElement="Association_1mzcgz0">
        <di:waypoint x="2070" y="234" />
        <di:waypoint x="2132" y="201" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x790bn_di" bpmnElement="Flow_0x790bn">
        <di:waypoint x="580" y="260" />
        <di:waypoint x="705" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1x6vcfz_di" bpmnElement="Flow_1x6vcfz">
        <di:waypoint x="730" y="285" />
        <di:waypoint x="730" y="352" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="743" y="312" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uusmv0_di" bpmnElement="Flow_1uusmv0">
        <di:waypoint x="755" y="260" />
        <di:waypoint x="960" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="784" y="242" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ax48eu_di" bpmnElement="Flow_0ax48eu">
        <di:waypoint x="2530" y="260" />
        <di:waypoint x="2685" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dm3ryx_di" bpmnElement="Flow_0dm3ryx">
        <di:waypoint x="2710" y="285" />
        <di:waypoint x="2710" y="342" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2723" y="311" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h5qypc_di" bpmnElement="Flow_1h5qypc">
        <di:waypoint x="2735" y="260" />
        <di:waypoint x="2890" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2792" y="242" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18rb9ws_di" bpmnElement="Flow_18rb9ws">
        <di:waypoint x="2990" y="260" />
        <di:waypoint x="3135" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xrblsx_di" bpmnElement="Flow_0xrblsx">
        <di:waypoint x="3185" y="260" />
        <di:waypoint x="3270" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3206" y="242" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dwn11l_di" bpmnElement="Flow_1dwn11l">
        <di:waypoint x="3160" y="285" />
        <di:waypoint x="3160" y="352" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3173" y="317" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wc6i9r_di" bpmnElement="Flow_1wc6i9r">
        <di:waypoint x="3370" y="260" />
        <di:waypoint x="3455" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t5yaxs_di" bpmnElement="Flow_1t5yaxs">
        <di:waypoint x="3480" y="285" />
        <di:waypoint x="3480" y="352" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3493" y="317" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s9ykb6_di" bpmnElement="Flow_1s9ykb6">
        <di:waypoint x="3720" y="260" />
        <di:waypoint x="3805" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0i6hynl_di" bpmnElement="Flow_0i6hynl">
        <di:waypoint x="188" y="260" />
        <di:waypoint x="255" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0me3v0o_di" bpmnElement="Flow_0me3v0o">
        <di:waypoint x="3505" y="260" />
        <di:waypoint x="3620" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3541" y="242" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b2oepf_di" bpmnElement="Flow_1b2oepf">
        <di:waypoint x="1060" y="260" />
        <di:waypoint x="1445" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0ffunsq" bpmnElement="Flow_14ajiqx">
        <di:waypoint x="3830" y="285" />
        <di:waypoint x="3830" y="352" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3843" y="317" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a9yinq_di" bpmnElement="Flow_0a9yinq">
        <di:waypoint x="3855" y="260" />
        <di:waypoint x="3950" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3876" y="242" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h9dh1k_di" bpmnElement="Flow_1h9dh1k">
        <di:waypoint x="4050" y="260" />
        <di:waypoint x="4145" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0ljkt7u_di" bpmnElement="SequenceFlow_0ljkt7u">
        <di:waypoint x="2070" y="260" />
        <di:waypoint x="2215" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02e3uev_di" bpmnElement="Flow_02e3uev">
        <di:waypoint x="2240" y="285" />
        <di:waypoint x="2240" y="362" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2243" y="314" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gedgrn_di" bpmnElement="Flow_1gedgrn">
        <di:waypoint x="2265" y="260" />
        <di:waypoint x="2430" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2326" y="242" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ooyui5_di" bpmnElement="Flow_1ooyui5">
        <di:waypoint x="305" y="260" />
        <di:waypoint x="480" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="375" y="273" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15wum3k_di" bpmnElement="Flow_15wum3k">
        <di:waypoint x="280" y="235" />
        <di:waypoint x="280" y="90" />
        <di:waypoint x="360" y="90" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="232" y="159" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_02m025e" bpmnElement="Flow_00nqfml">
        <di:waypoint x="460" y="90" />
        <di:waypoint x="495" y="90" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0e2yw0d" bpmnElement="Flow_0tr8b3n">
        <di:waypoint x="520" y="115" />
        <di:waypoint x="520" y="152" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="521" y="124" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0bdyfx9" bpmnElement="Flow_036fjqb">
        <di:waypoint x="545" y="90" />
        <di:waypoint x="610" y="90" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="548" y="63" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0sqa0g9" bpmnElement="Flow_1gvpnv7">
        <di:waypoint x="710" y="90" />
        <di:waypoint x="762" y="90" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o98hl7_di" bpmnElement="Flow_1o98hl7">
        <di:waypoint x="798" y="90" />
        <di:waypoint x="860" y="90" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c7rz14_di" bpmnElement="Flow_1c7rz14">
        <di:waypoint x="960" y="90" />
        <di:waypoint x="1015" y="90" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ea3zl4_di" bpmnElement="Flow_0ea3zl4">
        <di:waypoint x="1065" y="90" />
        <di:waypoint x="1160" y="90" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1089" y="63" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hsp6sc_di" bpmnElement="Flow_1hsp6sc">
        <di:waypoint x="1040" y="115" />
        <di:waypoint x="1040" y="142" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1053" y="113" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vkftax_di" bpmnElement="Flow_0vkftax">
        <di:waypoint x="1260" y="90" />
        <di:waypoint x="1312" y="90" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1my4vra_di" bpmnElement="Flow_1my4vra">
        <di:waypoint x="1348" y="90" />
        <di:waypoint x="1470" y="90" />
        <di:waypoint x="1470" y="235" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jpn6y9_di" bpmnElement="Flow_1jpn6y9">
        <di:waypoint x="4195" y="260" />
        <di:waypoint x="4310" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4232" y="242" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1va6saf_di" bpmnElement="Flow_1va6saf">
        <di:waypoint x="4170" y="285" />
        <di:waypoint x="4170" y="352" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4168" y="317" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wb2isj_di" bpmnElement="Flow_0wb2isj">
        <di:waypoint x="4410" y="260" />
        <di:waypoint x="4572" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s4nx36_di" bpmnElement="Flow_1s4nx36">
        <di:waypoint x="1495" y="260" />
        <di:waypoint x="1600" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0l1ovw5_di" bpmnElement="Flow_0l1ovw5">
        <di:waypoint x="1700" y="260" />
        <di:waypoint x="1805" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nzkac3_di" bpmnElement="Flow_0nzkac3">
        <di:waypoint x="1855" y="260" />
        <di:waypoint x="1970" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1891" y="242" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_0giitpa_di" bpmnElement="TextAnnotation_0giitpa">
        <dc:Bounds x="2130" y="160" width="100" height="41" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0bn5f0c_di" bpmnElement="Flow_0bn5f0c">
        <di:waypoint x="1830" y="285" />
        <di:waypoint x="1830" y="352" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1828" y="316" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
