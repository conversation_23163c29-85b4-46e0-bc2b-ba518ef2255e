<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0xt6om3" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.23.0">
  <bpmn:process id="InterimNumberPortIn-Rollback" name="InterimNumberPortIn-Rollback" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" camunda:asyncBefore="true">
      <bpmn:outgoing>SequenceFlow_140om64</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="SequenceFlow_140om64" sourceRef="StartEvent_1" targetRef="Gateway_1ksmz96" />
    <bpmn:endEvent id="EndEvent_17dzzi8">
      <bpmn:incoming>Flow_0r2c36m</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="CancelSDPSubmitPortIn" name="Cancel Submit PortIn" camunda:asyncBefore="true" camunda:delegateExpression="${cancelPortIn}">
      <bpmn:incoming>Flow_1sqhvfg</bpmn:incoming>
      <bpmn:outgoing>Flow_1jg28bk</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1jg28bk" sourceRef="CancelSDPSubmitPortIn" targetRef="Gateway_0215t88" />
    <bpmn:exclusiveGateway id="Gateway_1ksmz96" name="port in rollback required" default="Flow_06tyy80">
      <bpmn:incoming>SequenceFlow_140om64</bpmn:incoming>
      <bpmn:outgoing>Flow_1sqhvfg</bpmn:outgoing>
      <bpmn:outgoing>Flow_06tyy80</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1sqhvfg" sourceRef="Gateway_1ksmz96" targetRef="CancelSDPSubmitPortIn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${cancelSubmitPortInCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0215t88">
      <bpmn:incoming>Flow_1jg28bk</bpmn:incoming>
      <bpmn:incoming>Flow_06tyy80</bpmn:incoming>
      <bpmn:outgoing>Flow_021g85r</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_021g85r" sourceRef="Gateway_0215t88" targetRef="Gateway_1nsfba0" />
    <bpmn:sequenceFlow id="Flow_06tyy80" sourceRef="Gateway_1ksmz96" targetRef="Gateway_0215t88" />
    <bpmn:serviceTask id="CancelSDPSubmitPortInInternal" name="Cancel Submit PortIn Internal" camunda:asyncBefore="true" camunda:delegateExpression="${cancelPortIn}">
      <bpmn:incoming>Flow_0gscg82</bpmn:incoming>
      <bpmn:outgoing>Flow_0sdd1yp</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1nsfba0" name="port in rollback required" default="Flow_1h209z2">
      <bpmn:incoming>Flow_021g85r</bpmn:incoming>
      <bpmn:outgoing>Flow_0gscg82</bpmn:outgoing>
      <bpmn:outgoing>Flow_1h209z2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0hh7k1d">
      <bpmn:incoming>Flow_0sdd1yp</bpmn:incoming>
      <bpmn:incoming>Flow_1h209z2</bpmn:incoming>
      <bpmn:outgoing>Flow_0r2c36m</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0gscg82" sourceRef="Gateway_1nsfba0" targetRef="CancelSDPSubmitPortInInternal">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${cancelSubmitPortInCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0sdd1yp" sourceRef="CancelSDPSubmitPortInInternal" targetRef="Gateway_0hh7k1d" />
    <bpmn:sequenceFlow id="Flow_1h209z2" sourceRef="Gateway_1nsfba0" targetRef="Gateway_0hh7k1d" />
    <bpmn:sequenceFlow id="Flow_0r2c36m" sourceRef="Gateway_0hh7k1d" targetRef="EndEvent_17dzzi8" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="InterimNumberPortIn-Rollback">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="192" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_17dzzi8_di" bpmnElement="EndEvent_17dzzi8">
        <dc:Bounds x="1242" y="192" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1gw5qqe_di" bpmnElement="CancelSDPSubmitPortIn">
        <dc:Bounds x="440" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ksmz96_di" bpmnElement="Gateway_1ksmz96" isMarkerVisible="true">
        <dc:Bounds x="325" y="185" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="314" y="242" width="73" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0215t88_di" bpmnElement="Gateway_0215t88" isMarkerVisible="true">
        <dc:Bounds x="625" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_01j7xzm" bpmnElement="CancelSDPSubmitPortInInternal">
        <dc:Bounds x="870" y="170" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_04ucc9w" bpmnElement="Gateway_1nsfba0" isMarkerVisible="true">
        <dc:Bounds x="755" y="185" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="744" y="242" width="73" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1e2stxf" bpmnElement="Gateway_0hh7k1d" isMarkerVisible="true">
        <dc:Bounds x="1055" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_140om64_di" bpmnElement="SequenceFlow_140om64">
        <di:waypoint x="188" y="210" />
        <di:waypoint x="325" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jg28bk_di" bpmnElement="Flow_1jg28bk">
        <di:waypoint x="540" y="210" />
        <di:waypoint x="625" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sqhvfg_di" bpmnElement="Flow_1sqhvfg">
        <di:waypoint x="375" y="210" />
        <di:waypoint x="440" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_021g85r_di" bpmnElement="Flow_021g85r">
        <di:waypoint x="675" y="210" />
        <di:waypoint x="755" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06tyy80_di" bpmnElement="Flow_06tyy80">
        <di:waypoint x="350" y="185" />
        <di:waypoint x="350" y="90" />
        <di:waypoint x="650" y="90" />
        <di:waypoint x="650" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0p2szwz" bpmnElement="Flow_0gscg82">
        <di:waypoint x="805" y="210" />
        <di:waypoint x="870" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_13g941n" bpmnElement="Flow_0sdd1yp">
        <di:waypoint x="970" y="210" />
        <di:waypoint x="1055" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1kcdpvg" bpmnElement="Flow_1h209z2">
        <di:waypoint x="780" y="185" />
        <di:waypoint x="780" y="90" />
        <di:waypoint x="1080" y="90" />
        <di:waypoint x="1080" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r2c36m_di" bpmnElement="Flow_0r2c36m">
        <di:waypoint x="1105" y="210" />
        <di:waypoint x="1242" y="210" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
