<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.8.0">
  <bpmn:process id="InterimPortInFromMvnoToMvno" name="Interim PortInFromMvnoToMvno" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:serviceTask id="SOM_ChangeMsisdn" name="Change MSISDN in HLR (via SOM)" camunda:asyncBefore="true" camunda:delegateExpression="${somChangeMsisdn}">
      <bpmn:incoming>Flow_11bvqvo</bpmn:incoming>
      <bpmn:outgoing>Flow_0ax48eu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0paffsv" default="Flow_1h5qypc">
      <bpmn:incoming>Flow_0ax48eu</bpmn:incoming>
      <bpmn:outgoing>Flow_0dm3ryx</bpmn:outgoing>
      <bpmn:outgoing>Flow_1h5qypc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_05qjnsn">
      <bpmn:incoming>Flow_0dm3ryx</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:receiveTask id="SOMChangeMsisdnCallback" name="Wait for SOM callback" camunda:asyncBefore="true" messageRef="Message_14c63bs">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOM_ChangeMsisdn</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOM_ChangeMsisdn" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1h5qypc</bpmn:incoming>
      <bpmn:outgoing>Flow_18rb9ws</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_0oq1n56" default="Flow_0xrblsx">
      <bpmn:incoming>Flow_18rb9ws</bpmn:incoming>
      <bpmn:outgoing>Flow_0xrblsx</bpmn:outgoing>
      <bpmn:outgoing>Flow_1dwn11l</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0bb5bnp">
      <bpmn:incoming>Flow_1dwn11l</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0iwqp7u" default="Flow_1c4od85">
      <bpmn:incoming>Flow_1wc6i9r</bpmn:incoming>
      <bpmn:outgoing>Flow_1c4od85</bpmn:outgoing>
      <bpmn:outgoing>Flow_1t5yaxs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_08x8wf7">
      <bpmn:incoming>Flow_1t5yaxs</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSChangeMsisdn" name="Change MSISDN in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0xrblsx</bpmn:incoming>
      <bpmn:outgoing>Flow_1wc6i9r</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="OCSChangeMsisdn" name="Change MSISDN in OCS via ESB" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1c4od85</bpmn:incoming>
      <bpmn:outgoing>Flow_1374vsx</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ax48eu" sourceRef="SOM_ChangeMsisdn" targetRef="Gateway_0paffsv" />
    <bpmn:sequenceFlow id="Flow_0dm3ryx" name="Failure" sourceRef="Gateway_0paffsv" targetRef="Event_05qjnsn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1h5qypc" name="Success" sourceRef="Gateway_0paffsv" targetRef="SOMChangeMsisdnCallback" />
    <bpmn:sequenceFlow id="Flow_18rb9ws" sourceRef="SOMChangeMsisdnCallback" targetRef="Gateway_0oq1n56" />
    <bpmn:sequenceFlow id="Flow_0xrblsx" name="Success" sourceRef="Gateway_0oq1n56" targetRef="BSChangeMsisdn" />
    <bpmn:sequenceFlow id="Flow_1dwn11l" name="Failure" sourceRef="Gateway_0oq1n56" targetRef="Event_0bb5bnp">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wc6i9r" sourceRef="BSChangeMsisdn" targetRef="Gateway_0iwqp7u" />
    <bpmn:sequenceFlow id="Flow_1c4od85" name="Success" sourceRef="Gateway_0iwqp7u" targetRef="OCSChangeMsisdn" />
    <bpmn:sequenceFlow id="Flow_1t5yaxs" name="Failure" sourceRef="Gateway_0iwqp7u" targetRef="Event_08x8wf7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="subProcessEndEvent_1" camunda:asyncBefore="true">
      <bpmn:incoming>Flow_0mnqlkh</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1374vsx" sourceRef="OCSChangeMsisdn" targetRef="Gateway_0vacdqc" />
    <bpmn:exclusiveGateway id="Gateway_0vacdqc" default="Flow_1vrqo2r">
      <bpmn:incoming>Flow_1374vsx</bpmn:incoming>
      <bpmn:outgoing>Flow_1vrqo2r</bpmn:outgoing>
      <bpmn:outgoing>Flow_09cne1c</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1vrqo2r" name="Success" sourceRef="Gateway_0vacdqc" targetRef="OCS_ServicePortDetailsUpdate" />
    <bpmn:endEvent id="Event_1d8uumr">
      <bpmn:incoming>Flow_09cne1c</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_09cne1c" name="Failure" sourceRef="Gateway_0vacdqc" targetRef="Event_1d8uumr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0wn9xct" sourceRef="OCS_ServicePortDetailsUpdate" targetRef="Gateway_1ua8zyx" />
    <bpmn:serviceTask id="OCS_ServicePortDetailsUpdate" name="OCS Port Details update" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1vrqo2r</bpmn:incoming>
      <bpmn:outgoing>Flow_0wn9xct</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="Event_160bb6g">
      <bpmn:outgoing>Flow_0lyuhd6</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="SOM_DeleteService" name="Delete service in SOM (from donor MVNO)" camunda:asyncBefore="true" camunda:delegateExpression="${somTerminateService}">
      <bpmn:documentation>Delete service in HLR via SOM (from donor MVNO)</bpmn:documentation>
      <bpmn:incoming>Flow_1whcpqm</bpmn:incoming>
      <bpmn:outgoing>Flow_0rpziyj</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_14fbyzh" default="Flow_0gp4y2v">
      <bpmn:incoming>Flow_0rpziyj</bpmn:incoming>
      <bpmn:outgoing>Flow_0gp4y2v</bpmn:outgoing>
      <bpmn:outgoing>Flow_0d02e9s</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:receiveTask id="SOM_DeleteServiceCallback" name="Wait for SOM Delete Callback" camunda:asyncBefore="true" messageRef="Message_2gkiae9">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOM_DeleteService" />
        </camunda:properties>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOM_DeleteService</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0gp4y2v</bpmn:incoming>
      <bpmn:outgoing>Flow_1yjwcds</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:endEvent id="Event_0tct2zi">
      <bpmn:incoming>Flow_0d02e9s</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0gopjhu" default="Flow_1q2va1c">
      <bpmn:incoming>Flow_1yjwcds</bpmn:incoming>
      <bpmn:outgoing>Flow_0qi65s5</bpmn:outgoing>
      <bpmn:outgoing>Flow_1q2va1c</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_15g1jjf">
      <bpmn:incoming>Flow_0qi65s5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="ESB_TerminateService" name="Delete Subscriber in OCS (via ESB) - from donor MVNO" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1q2va1c</bpmn:incoming>
      <bpmn:outgoing>Flow_07rhotr</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_19y336j" default="Flow_073wysd">
      <bpmn:incoming>Flow_07rhotr</bpmn:incoming>
      <bpmn:outgoing>Flow_0xc6ko3</bpmn:outgoing>
      <bpmn:outgoing>Flow_073wysd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1sekyka">
      <bpmn:incoming>Flow_0xc6ko3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1x6bml3" default="Flow_00j83nk">
      <bpmn:incoming>Flow_027u8fg</bpmn:incoming>
      <bpmn:outgoing>Flow_1mx0h49</bpmn:outgoing>
      <bpmn:outgoing>Flow_00j83nk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="BS_TerminateService" name="Terminate the service from Billing System - from donor MVNO" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_073wysd</bpmn:incoming>
      <bpmn:outgoing>Flow_027u8fg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_0hmpn8m">
      <bpmn:incoming>Flow_1mx0h49</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:intermediateCatchEvent id="MnpProvisioningTimer" name="MnpProvisioningTimer" camunda:asyncBefore="true">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0kc37bh</bpmn:incoming>
      <bpmn:outgoing>Flow_0ay65bt</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_13r83du">
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${mnpProvisioningDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:intermediateCatchEvent id="MnpDeProvisioningTimer" name="MnpDeProvisioningTimer" camunda:asyncBefore="true">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.TimerExecutionListener" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1damifc</bpmn:incoming>
      <bpmn:outgoing>Flow_1l9n203</bpmn:outgoing>
      <bpmn:timerEventDefinition id="TimerEventDefinition_1t28s5b">
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${mnpDeProvisioningDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:serviceTask id="MNPDeProvDateCalculator" name="Calculate de provisioning date" camunda:asyncBefore="true" camunda:delegateExpression="${mnpProvisioningDateCalculator}">
      <bpmn:incoming>Flow_0lyuhd6</bpmn:incoming>
      <bpmn:outgoing>Flow_1damifc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="MNPProvDateCalculator" name="Calculate provisioning date" camunda:asyncBefore="true" camunda:delegateExpression="${mnpProvisioningDateCalculator}">
      <bpmn:incoming>Flow_1af2w4k</bpmn:incoming>
      <bpmn:outgoing>Flow_0kc37bh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1qvsl1k" default="Flow_1whcpqm">
      <bpmn:incoming>SequenceFlow_0ljkt7u</bpmn:incoming>
      <bpmn:outgoing>Flow_1whcpqm</bpmn:outgoing>
      <bpmn:outgoing>Flow_02e3uev</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_01pb4gg">
      <bpmn:incoming>Flow_02e3uev</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch Service Registry for Delete" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1l9n203</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0ljkt7u</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0lyuhd6" sourceRef="Event_160bb6g" targetRef="MNPDeProvDateCalculator" />
    <bpmn:sequenceFlow id="Flow_1whcpqm" name="Success" sourceRef="Gateway_1qvsl1k" targetRef="SOM_DeleteService" />
    <bpmn:sequenceFlow id="Flow_0rpziyj" sourceRef="SOM_DeleteService" targetRef="Gateway_14fbyzh" />
    <bpmn:sequenceFlow id="Flow_0gp4y2v" name="Success" sourceRef="Gateway_14fbyzh" targetRef="SOM_DeleteServiceCallback" />
    <bpmn:sequenceFlow id="Flow_0d02e9s" name="Failure" sourceRef="Gateway_14fbyzh" targetRef="Event_0tct2zi">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1yjwcds" sourceRef="SOM_DeleteServiceCallback" targetRef="Gateway_0gopjhu" />
    <bpmn:sequenceFlow id="Flow_0qi65s5" name="Failure" sourceRef="Gateway_0gopjhu" targetRef="Event_15g1jjf">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1q2va1c" name="Success" sourceRef="Gateway_0gopjhu" targetRef="ESB_TerminateService" />
    <bpmn:sequenceFlow id="Flow_07rhotr" sourceRef="ESB_TerminateService" targetRef="Gateway_19y336j" />
    <bpmn:sequenceFlow id="Flow_0xc6ko3" name="Failure" sourceRef="Gateway_19y336j" targetRef="Event_1sekyka">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_073wysd" name="Success" sourceRef="Gateway_19y336j" targetRef="BS_TerminateService" />
    <bpmn:sequenceFlow id="Flow_027u8fg" sourceRef="BS_TerminateService" targetRef="Gateway_1x6bml3" />
    <bpmn:sequenceFlow id="Flow_1mx0h49" name="Failure" sourceRef="Gateway_1x6bml3" targetRef="Event_0hmpn8m">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_00j83nk" name="Success" sourceRef="Gateway_1x6bml3" targetRef="MNPPortOutOrderGeneration" />
    <bpmn:sequenceFlow id="Flow_1l9n203" sourceRef="MnpDeProvisioningTimer" targetRef="SOMFetchServiceRegistry" />
    <bpmn:sequenceFlow id="SequenceFlow_0ljkt7u" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_1qvsl1k" />
    <bpmn:sequenceFlow id="Flow_02e3uev" name="Failure" sourceRef="Gateway_1qvsl1k" targetRef="Event_01pb4gg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ay65bt" sourceRef="MnpProvisioningTimer" targetRef="SOMFetchservices" />
    <bpmn:exclusiveGateway id="Gateway_11hsp8p" default="Flow_11bvqvo">
      <bpmn:incoming>Flow_1fs07te</bpmn:incoming>
      <bpmn:outgoing>Flow_0v1i9sw</bpmn:outgoing>
      <bpmn:outgoing>Flow_11bvqvo</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0s3uwn1">
      <bpmn:incoming>Flow_0v1i9sw</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="SOMFetchservices" name="SOM Fetch Service Registry" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0ay65bt</bpmn:incoming>
      <bpmn:outgoing>Flow_1fs07te</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1fs07te" sourceRef="SOMFetchservices" targetRef="Gateway_11hsp8p" />
    <bpmn:sequenceFlow id="Flow_0v1i9sw" name="Failure" sourceRef="Gateway_11hsp8p" targetRef="Event_0s3uwn1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_11bvqvo" name="Success" sourceRef="Gateway_11hsp8p" targetRef="SOM_ChangeMsisdn" />
    <bpmn:sequenceFlow id="Flow_1damifc" sourceRef="MNPDeProvDateCalculator" targetRef="MnpDeProvisioningTimer" />
    <bpmn:sequenceFlow id="Flow_0kc37bh" sourceRef="MNPProvDateCalculator" targetRef="MnpProvisioningTimer" />
    <bpmn:exclusiveGateway id="Gateway_0ilh2b5" default="Flow_1af2w4k">
      <bpmn:incoming>Flow_12h948g</bpmn:incoming>
      <bpmn:outgoing>Flow_0z6qpgv</bpmn:outgoing>
      <bpmn:outgoing>Flow_1af2w4k</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_14kvnb5">
      <bpmn:incoming>Flow_0z6qpgv</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0z6qpgv" name="Failure" sourceRef="Gateway_0ilh2b5" targetRef="Event_14kvnb5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1af2w4k" name="Success" sourceRef="Gateway_0ilh2b5" targetRef="MNPProvDateCalculator" />
    <bpmn:serviceTask id="MNPPortOutOrderGeneration" name="MNP Port Out Order Generation" camunda:asyncBefore="true" camunda:delegateExpression="${mnpPortoutOrderGenaration}">
      <bpmn:incoming>Flow_00j83nk</bpmn:incoming>
      <bpmn:outgoing>Flow_12h948g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1ua8zyx" default="Flow_0ir3xie">
      <bpmn:incoming>Flow_0wn9xct</bpmn:incoming>
      <bpmn:outgoing>Flow_0ir3xie</bpmn:outgoing>
      <bpmn:outgoing>Flow_0xkzal4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ir3xie" name="Success" sourceRef="Gateway_1ua8zyx" targetRef="NMS_NumberSwap" />
    <bpmn:endEvent id="Event_00ntbe5">
      <bpmn:incoming>Flow_0xkzal4</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0xkzal4" name="Failure" sourceRef="Gateway_1ua8zyx" targetRef="Event_00ntbe5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="NMS_NumberSwap" name="Number Swap in NMS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0ir3xie</bpmn:incoming>
      <bpmn:outgoing>Flow_1p8ajt8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1p8ajt8" sourceRef="NMS_NumberSwap" targetRef="Gateway_087fysy" />
    <bpmn:exclusiveGateway id="Gateway_087fysy" default="Flow_1w4082x">
      <bpmn:incoming>Flow_1p8ajt8</bpmn:incoming>
      <bpmn:outgoing>Flow_1w4082x</bpmn:outgoing>
      <bpmn:outgoing>Flow_14piz01</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1w4082x" name="Success" sourceRef="Gateway_087fysy" targetRef="NMS_PortIn" />
    <bpmn:endEvent id="Event_14z9hlj">
      <bpmn:incoming>Flow_14piz01</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_14piz01" name="Failure" sourceRef="Gateway_087fysy" targetRef="Event_14z9hlj">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="NMS_PortIn" name="PortIn In NMS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1w4082x</bpmn:incoming>
      <bpmn:outgoing>Flow_0mnqlkh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0mnqlkh" sourceRef="NMS_PortIn" targetRef="subProcessEndEvent_1" />
    <bpmn:sequenceFlow id="Flow_12h948g" sourceRef="MNPPortOutOrderGeneration" targetRef="Gateway_0ilh2b5" />
    <bpmn:textAnnotation id="TextAnnotation_0mbbgdp">
      <bpmn:text>fetch new MSISDN</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1cxnlbc" sourceRef="SOMFetchServiceRegistry" targetRef="TextAnnotation_0mbbgdp" />
    <bpmn:textAnnotation id="TextAnnotation_0t7i78z">
      <bpmn:text>fetch old MSISDN</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0k11bl9" sourceRef="SOMFetchservices" targetRef="TextAnnotation_0t7i78z" />
    <bpmn:textAnnotation id="TextAnnotation_1x5zh6e">
      <bpmn:text>for linking newmsisdn to old msisdn's ICCID , IMSI</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0gqi64f" sourceRef="TextAnnotation_1x5zh6e" targetRef="NMS_NumberSwap" />
    <bpmn:textAnnotation id="TextAnnotation_1das1zx">
      <bpmn:text>for porting newMsisdn to new mvnoId</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_01le8u4" sourceRef="TextAnnotation_1das1zx" targetRef="NMS_PortIn" />
  </bpmn:process>
  <bpmn:message id="Message_18lupa4" name="MNPCallBack" />
  <bpmn:message id="Message_0cxkag2" />
  <bpmn:message id="Message_0uyo7lb" />
  <bpmn:message id="Message_0z0mmot" name="MNPCallBack" />
  <bpmn:message id="Message_2gkiae9" name="SOM_DeleteServiceCallback" />
  <bpmn:message id="Message_14c63bs" name="SOMChangeMsisdnCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="InterimPortInFromMvnoToMvno">
      <bpmndi:BPMNShape id="BPMNShape_0rod8y5" bpmnElement="TextAnnotation_1x5zh6e">
        <dc:Bounds x="6400" y="37" width="100" height="84" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_12w6mkn" bpmnElement="TextAnnotation_1das1zx">
        <dc:Bounds x="6690" y="37" width="100" height="53" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1y1f08p" bpmnElement="SOM_ChangeMsisdn">
        <dc:Bounds x="4450" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1y0xyvi" bpmnElement="Gateway_0paffsv" isMarkerVisible="true">
        <dc:Bounds x="4645" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0haf1fd" bpmnElement="Event_05qjnsn">
        <dc:Bounds x="4652" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0dzmyxs" bpmnElement="SOMChangeMsisdnCallback">
        <dc:Bounds x="4780" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0i6fg72" bpmnElement="Gateway_0oq1n56" isMarkerVisible="true">
        <dc:Bounds x="4935" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1sxqe9i" bpmnElement="Event_0bb5bnp">
        <dc:Bounds x="4942" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1a5qlb7" bpmnElement="Gateway_0iwqp7u" isMarkerVisible="true">
        <dc:Bounds x="5315" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0pgxq07" bpmnElement="Event_08x8wf7">
        <dc:Bounds x="5322" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ko6pox" bpmnElement="BSChangeMsisdn">
        <dc:Bounds x="5110" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ajqpbf" bpmnElement="OCSChangeMsisdn">
        <dc:Bounds x="5560" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vacdqc_di" bpmnElement="Gateway_0vacdqc" isMarkerVisible="true">
        <dc:Bounds x="5775" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1d8uumr_di" bpmnElement="Event_1d8uumr">
        <dc:Bounds x="5782" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1976bv0_di" bpmnElement="OCS_ServicePortDetailsUpdate">
        <dc:Bounds x="5950" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0yqokw5" bpmnElement="Event_160bb6g">
        <dc:Bounds x="152" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0fdinmr" bpmnElement="MnpProvisioningTimer">
        <dc:Bounds x="3972" y="182" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3950" y="225" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0v17be8" bpmnElement="MnpDeProvisioningTimer">
        <dc:Bounds x="512" y="182" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="490" y="225" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_021s934" bpmnElement="MNPDeProvDateCalculator">
        <dc:Bounds x="320" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0rwuvxf" bpmnElement="MNPProvDateCalculator">
        <dc:Bounds x="3760" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1qvsl1k_di" bpmnElement="Gateway_1qvsl1k" isMarkerVisible="true">
        <dc:Bounds x="885" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_01pb4gg_di" bpmnElement="Event_01pb4gg">
        <dc:Bounds x="892" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0hs1oog_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="690" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0rfq3xj" bpmnElement="Gateway_11hsp8p" isMarkerVisible="true">
        <dc:Bounds x="4305" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_01ksolt" bpmnElement="Event_0s3uwn1">
        <dc:Bounds x="4312" y="302" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_04oljb0" bpmnElement="SOMFetchservices">
        <dc:Bounds x="4110" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ur0bxq" bpmnElement="Gateway_0ilh2b5" isMarkerVisible="true">
        <dc:Bounds x="3555" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1vh1jwq" bpmnElement="Event_14kvnb5">
        <dc:Bounds x="3562" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0mbbgdp_di" bpmnElement="TextAnnotation_0mbbgdp">
        <dc:Bounds x="790" y="80" width="100" height="41" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0t7i78z_di" bpmnElement="TextAnnotation_0t7i78z">
        <dc:Bounds x="4210" y="80" width="100" height="41" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_011nc8g" bpmnElement="Gateway_1ua8zyx" isMarkerVisible="true">
        <dc:Bounds x="6115" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_18rypu7" bpmnElement="Event_00ntbe5">
        <dc:Bounds x="6122" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1gydo4f" bpmnElement="NMS_NumberSwap">
        <dc:Bounds x="6240" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_09f8333" bpmnElement="Gateway_087fysy" isMarkerVisible="true">
        <dc:Bounds x="6435" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0b4shay" bpmnElement="Event_14z9hlj">
        <dc:Bounds x="6442" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1a7ipk6" bpmnElement="NMS_PortIn">
        <dc:Bounds x="6570" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_05ljudt_di" bpmnElement="MNPPortOutOrderGeneration">
        <dc:Bounds x="3320" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_12gop74" bpmnElement="Gateway_1x6bml3" isMarkerVisible="true">
        <dc:Bounds x="3065" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_10cbhkg" bpmnElement="Event_0hmpn8m">
        <dc:Bounds x="3072" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1lookl4" bpmnElement="BS_TerminateService">
        <dc:Bounds x="2790" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0c5l15u" bpmnElement="Gateway_19y336j" isMarkerVisible="true">
        <dc:Bounds x="2455" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1qazh30" bpmnElement="Event_1sekyka">
        <dc:Bounds x="2462" y="332" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0gjlhxy" bpmnElement="ESB_TerminateService">
        <dc:Bounds x="2120" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1i58blx" bpmnElement="Gateway_0gopjhu" isMarkerVisible="true">
        <dc:Bounds x="1875" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1w090nx" bpmnElement="Event_15g1jjf">
        <dc:Bounds x="1882" y="332" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1bew31k" bpmnElement="SOM_DeleteServiceCallback">
        <dc:Bounds x="1600" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0qy3fl0" bpmnElement="Gateway_14fbyzh" isMarkerVisible="true">
        <dc:Bounds x="1385" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ejxbr0" bpmnElement="Event_0tct2zi">
        <dc:Bounds x="1392" y="332" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0tno4pv" bpmnElement="SOM_DeleteService">
        <dc:Bounds x="1090" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_13lncq0_di" bpmnElement="subProcessEndEvent_1">
        <dc:Bounds x="6802" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_0gqi64f_di" bpmnElement="Association_0gqi64f">
        <di:waypoint x="6400" y="117" />
        <di:waypoint x="6338" y="164" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_01le8u4_di" bpmnElement="Association_01le8u4">
        <di:waypoint x="6690" y="90" />
        <di:waypoint x="6645" y="160" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0wasdhk" bpmnElement="Flow_0ax48eu">
        <di:waypoint x="4550" y="200" />
        <di:waypoint x="4645" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_13ag58o" bpmnElement="Flow_0dm3ryx">
        <di:waypoint x="4670" y="225" />
        <di:waypoint x="4670" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4683" y="257" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_15uxrbf" bpmnElement="Flow_1h5qypc">
        <di:waypoint x="4695" y="200" />
        <di:waypoint x="4780" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4716" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0naisrc" bpmnElement="Flow_18rb9ws">
        <di:waypoint x="4880" y="200" />
        <di:waypoint x="4935" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1dtvu3m" bpmnElement="Flow_0xrblsx">
        <di:waypoint x="4985" y="200" />
        <di:waypoint x="5110" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4987" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0uk1aam" bpmnElement="Flow_1dwn11l">
        <di:waypoint x="4960" y="225" />
        <di:waypoint x="4960" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4957" y="250" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_02gz397" bpmnElement="Flow_1wc6i9r">
        <di:waypoint x="5210" y="200" />
        <di:waypoint x="5315" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1mcybmq" bpmnElement="Flow_1c4od85">
        <di:waypoint x="5365" y="200" />
        <di:waypoint x="5560" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5387" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0ncevi5" bpmnElement="Flow_1t5yaxs">
        <di:waypoint x="5340" y="225" />
        <di:waypoint x="5340" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5353" y="257" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1374vsx_di" bpmnElement="Flow_1374vsx">
        <di:waypoint x="5660" y="200" />
        <di:waypoint x="5775" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vrqo2r_di" bpmnElement="Flow_1vrqo2r">
        <di:waypoint x="5825" y="200" />
        <di:waypoint x="5950" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5866" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09cne1c_di" bpmnElement="Flow_09cne1c">
        <di:waypoint x="5800" y="225" />
        <di:waypoint x="5800" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5813" y="256" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wn9xct_di" bpmnElement="Flow_0wn9xct">
        <di:waypoint x="6050" y="200" />
        <di:waypoint x="6115" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1rtff0g" bpmnElement="Flow_0lyuhd6">
        <di:waypoint x="188" y="200" />
        <di:waypoint x="320" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1whcpqm_di" bpmnElement="Flow_1whcpqm">
        <di:waypoint x="935" y="200" />
        <di:waypoint x="1090" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="992" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0m7kjkh" bpmnElement="Flow_0rpziyj">
        <di:waypoint x="1190" y="200" />
        <di:waypoint x="1385" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0tgxbzt" bpmnElement="Flow_0gp4y2v">
        <di:waypoint x="1435" y="200" />
        <di:waypoint x="1600" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1496" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0x1z0zh" bpmnElement="Flow_0d02e9s">
        <di:waypoint x="1410" y="225" />
        <di:waypoint x="1410" y="332" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1423" y="278" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1a9wj6q" bpmnElement="Flow_1yjwcds">
        <di:waypoint x="1700" y="200" />
        <di:waypoint x="1875" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_100ki04" bpmnElement="Flow_0qi65s5">
        <di:waypoint x="1900" y="225" />
        <di:waypoint x="1900" y="332" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1913" y="271" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0j2wjf1" bpmnElement="Flow_1q2va1c">
        <di:waypoint x="1925" y="200" />
        <di:waypoint x="2120" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2056" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0ft3xfs" bpmnElement="Flow_07rhotr">
        <di:waypoint x="2220" y="200" />
        <di:waypoint x="2455" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1kus0p0" bpmnElement="Flow_0xc6ko3">
        <di:waypoint x="2480" y="225" />
        <di:waypoint x="2480" y="332" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2493" y="271" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0rbafvc" bpmnElement="Flow_073wysd">
        <di:waypoint x="2505" y="200" />
        <di:waypoint x="2790" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2626" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0xbn4bp" bpmnElement="Flow_027u8fg">
        <di:waypoint x="2890" y="200" />
        <di:waypoint x="3065" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1vmn7wq" bpmnElement="Flow_1mx0h49">
        <di:waypoint x="3090" y="225" />
        <di:waypoint x="3090" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3103" y="266" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00j83nk_di" bpmnElement="Flow_00j83nk">
        <di:waypoint x="3115" y="200" />
        <di:waypoint x="3320" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2948" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0kel8sb" bpmnElement="Flow_1l9n203">
        <di:waypoint x="548" y="200" />
        <di:waypoint x="690" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0ljkt7u_di" bpmnElement="SequenceFlow_0ljkt7u">
        <di:waypoint x="790" y="200" />
        <di:waypoint x="885" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02e3uev_di" bpmnElement="Flow_02e3uev">
        <di:waypoint x="910" y="225" />
        <di:waypoint x="910" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="913" y="254" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ay65bt_di" bpmnElement="Flow_0ay65bt">
        <di:waypoint x="4008" y="200" />
        <di:waypoint x="4110" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1jqs36u" bpmnElement="Flow_1fs07te">
        <di:waypoint x="4210" y="200" />
        <di:waypoint x="4305" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0lz9b7r" bpmnElement="Flow_0v1i9sw">
        <di:waypoint x="4330" y="225" />
        <di:waypoint x="4330" y="302" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4333" y="254" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11bvqvo_di" bpmnElement="Flow_11bvqvo">
        <di:waypoint x="4355" y="200" />
        <di:waypoint x="4450" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4381" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1damifc_di" bpmnElement="Flow_1damifc">
        <di:waypoint x="420" y="200" />
        <di:waypoint x="512" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kc37bh_di" bpmnElement="Flow_0kc37bh">
        <di:waypoint x="3860" y="200" />
        <di:waypoint x="3972" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_18897l0" bpmnElement="Flow_0z6qpgv">
        <di:waypoint x="3580" y="225" />
        <di:waypoint x="3580" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3593" y="266" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1af2w4k_di" bpmnElement="Flow_1af2w4k">
        <di:waypoint x="3605" y="200" />
        <di:waypoint x="3760" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3662" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_1cxnlbc_di" bpmnElement="Association_1cxnlbc">
        <di:waypoint x="778" y="160" />
        <di:waypoint x="815" y="121" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0k11bl9_di" bpmnElement="Association_0k11bl9">
        <di:waypoint x="4198" y="160" />
        <di:waypoint x="4235" y="121" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ir3xie_di" bpmnElement="Flow_0ir3xie">
        <di:waypoint x="6165" y="200" />
        <di:waypoint x="6240" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6182" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xkzal4_di" bpmnElement="Flow_0xkzal4">
        <di:waypoint x="6140" y="225" />
        <di:waypoint x="6140" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6138" y="266" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p8ajt8_di" bpmnElement="Flow_1p8ajt8">
        <di:waypoint x="6340" y="200" />
        <di:waypoint x="6435" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w4082x_di" bpmnElement="Flow_1w4082x">
        <di:waypoint x="6485" y="200" />
        <di:waypoint x="6570" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6506" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14piz01_di" bpmnElement="Flow_14piz01">
        <di:waypoint x="6460" y="225" />
        <di:waypoint x="6460" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6458" y="266" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mnqlkh_di" bpmnElement="Flow_0mnqlkh">
        <di:waypoint x="6670" y="200" />
        <di:waypoint x="6802" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12h948g_di" bpmnElement="Flow_12h948g">
        <di:waypoint x="3420" y="200" />
        <di:waypoint x="3555" y="200" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
