<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.23.0">
  <bpmn:process id="InterimNumberPortIn" name="InterimNumberPortIn" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_1g2mqme</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="PortInTypeFinder" name="PortIn Type Finder" camunda:asyncBefore="true" camunda:delegateExpression="${portInTypeFinder}">
      <bpmn:documentation>Find Port in Type (call billing to fetch the service id details)</bpmn:documentation>
      <bpmn:incoming>Flow_1g2mqme</bpmn:incoming>
      <bpmn:outgoing>Flow_1t2iyd5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1k3dkyo" default="Flow_03h6oj4">
      <bpmn:incoming>Flow_15zrv9q</bpmn:incoming>
      <bpmn:outgoing>Flow_1hmnjzb</bpmn:outgoing>
      <bpmn:outgoing>Flow_03h6oj4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0cxn4t8">
      <bpmn:incoming>Flow_1hmnjzb</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1hmnjzb" name="Failure" sourceRef="Gateway_1k3dkyo" targetRef="Event_0cxn4t8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1p3ub4k" name="If upfront payment applicable" default="Flow_03u6f66">
      <bpmn:incoming>Flow_1irpe3f</bpmn:incoming>
      <bpmn:outgoing>Flow_00yqrsi</bpmn:outgoing>
      <bpmn:outgoing>Flow_03u6f66</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_00yqrsi" name="Yes" sourceRef="Gateway_1p3ub4k" targetRef="PaymentWorkflow">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${paymentCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="PaymentWorkflow" name="Payment workflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_00yqrsi</bpmn:incoming>
      <bpmn:outgoing>Flow_0cunzyv</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_1dsta8e">
      <bpmn:incoming>Flow_03u6f66</bpmn:incoming>
      <bpmn:incoming>Flow_0cunzyv</bpmn:incoming>
      <bpmn:outgoing>Flow_0w5vsus</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_03u6f66" name="no" sourceRef="Gateway_1p3ub4k" targetRef="Gateway_1dsta8e" />
    <bpmn:sequenceFlow id="Flow_0cunzyv" sourceRef="PaymentWorkflow" targetRef="Gateway_1dsta8e" />
    <bpmn:exclusiveGateway id="Gateway_1ii6t8z" name="Check the port-in type">
      <bpmn:incoming>Flow_0w5vsus</bpmn:incoming>
      <bpmn:outgoing>Flow_0awpx7k</bpmn:outgoing>
      <bpmn:outgoing>Flow_1l2hlpg</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0awpx7k" name="Port in from external telco / port in from Singtel" sourceRef="Gateway_1ii6t8z" targetRef="InterimPortInFromExtTelcoOrSingtel">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portInType=='PortInFromExternalTelco'||portInType=='PortInFromSingtel'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1l2hlpg" name="Port in from MVNO to MVNO" sourceRef="Gateway_1ii6t8z" targetRef="InterimPortInFromMvnoToMvno">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portInType=='PortInFromMvnoToMvno'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="InterimPortInFromExtTelcoOrSingtel" name="InterimPortIn From ExtTelco Or Singtel" camunda:asyncBefore="true" calledElement="InterimPortInFromExtTelcoOrSingtel" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0awpx7k</bpmn:incoming>
      <bpmn:outgoing>Flow_07vuljq</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:callActivity id="InterimPortInFromMvnoToMvno" name="InterimPortin from MVNO to MVNO" camunda:asyncBefore="true" calledElement="InterimPortInFromMvnoToMvno" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1l2hlpg</bpmn:incoming>
      <bpmn:outgoing>Flow_1c0f03x</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:endEvent id="orderExecEnd" camunda:asyncBefore="true">
      <bpmn:incoming>Flow_08xftxu</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1c0f03x" sourceRef="InterimPortInFromMvnoToMvno" targetRef="Gateway_1tuf0dy" />
    <bpmn:exclusiveGateway id="Gateway_1tuf0dy">
      <bpmn:incoming>Flow_1c0f03x</bpmn:incoming>
      <bpmn:incoming>Flow_07vuljq</bpmn:incoming>
      <bpmn:outgoing>Flow_08xftxu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_08xftxu" sourceRef="Gateway_1tuf0dy" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_07vuljq" sourceRef="InterimPortInFromExtTelcoOrSingtel" targetRef="Gateway_1tuf0dy" />
    <bpmn:sequenceFlow id="Flow_0w5vsus" sourceRef="Gateway_1dsta8e" targetRef="Gateway_1ii6t8z" />
    <bpmn:sequenceFlow id="Flow_03h6oj4" name="Success" sourceRef="Gateway_1k3dkyo" targetRef="Gateway_0p1thx5" />
    <bpmn:serviceTask id="QueryMNPDetails" name="QueryMNPDetails" camunda:asyncBefore="true" camunda:delegateExpression="${queryMnpDetails}">
      <bpmn:documentation>Query Singtel MNP System to verify operator details</bpmn:documentation>
      <bpmn:incoming>Flow_07a7tzw</bpmn:incoming>
      <bpmn:outgoing>Flow_15zrv9q</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_06e9mar" default="Flow_0j7ffzz">
      <bpmn:incoming>Flow_1t2iyd5</bpmn:incoming>
      <bpmn:outgoing>Flow_1i2zowt</bpmn:outgoing>
      <bpmn:outgoing>Flow_0j7ffzz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1m75fss">
      <bpmn:incoming>Flow_1i2zowt</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1i2zowt" name="Failure" sourceRef="Gateway_06e9mar" targetRef="Event_1m75fss">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_15zrv9q" sourceRef="QueryMNPDetails" targetRef="Gateway_1k3dkyo" />
    <bpmn:sequenceFlow id="Flow_1g2mqme" sourceRef="orderExecStart" targetRef="PortInTypeFinder" />
    <bpmn:sequenceFlow id="Flow_1t2iyd5" sourceRef="PortInTypeFinder" targetRef="Gateway_06e9mar" />
    <bpmn:exclusiveGateway id="Gateway_1cd71mk" name="check if query mnp required (check if mvno mvno port in)">
      <bpmn:incoming>Flow_0j7ffzz</bpmn:incoming>
      <bpmn:outgoing>Flow_07a7tzw</bpmn:outgoing>
      <bpmn:outgoing>Flow_1e5aevy</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0j7ffzz" name="Success" sourceRef="Gateway_06e9mar" targetRef="Gateway_1cd71mk" />
    <bpmn:sequenceFlow id="Flow_07a7tzw" name="Yes" sourceRef="Gateway_1cd71mk" targetRef="QueryMNPDetails">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${queryMnpRequired}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0p1thx5">
      <bpmn:incoming>Flow_03h6oj4</bpmn:incoming>
      <bpmn:incoming>Flow_0tg5g7g</bpmn:incoming>
      <bpmn:outgoing>Flow_1irpe3f</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1irpe3f" sourceRef="Gateway_0p1thx5" targetRef="Gateway_1p3ub4k" />
    <bpmn:exclusiveGateway id="Gateway_0jvo0fx" default="Flow_0xtmg8g">
      <bpmn:incoming>Flow_1e5aevy</bpmn:incoming>
      <bpmn:outgoing>Flow_0tovska</bpmn:outgoing>
      <bpmn:outgoing>Flow_0xtmg8g</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1e5aevy" sourceRef="Gateway_1cd71mk" targetRef="Gateway_0jvo0fx">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${!queryMnpRequired}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="IdentificationIdValidator" name="Identification Id Validator" camunda:asyncBefore="true" camunda:delegateExpression="${identificationIdValidator}">
      <bpmn:documentation>Query Singtel MNP System to verify operator details</bpmn:documentation>
      <bpmn:incoming>Flow_0tovska</bpmn:incoming>
      <bpmn:outgoing>Flow_053jopm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0tovska" sourceRef="Gateway_0jvo0fx" targetRef="IdentificationIdValidator">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isIdValidationReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_04u0k3i" default="Flow_0ocfyx7">
      <bpmn:incoming>Flow_053jopm</bpmn:incoming>
      <bpmn:outgoing>Flow_1q3whuc</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ocfyx7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_053jopm" sourceRef="IdentificationIdValidator" targetRef="Gateway_04u0k3i" />
    <bpmn:endEvent id="Event_1833suq">
      <bpmn:incoming>Flow_1q3whuc</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1q3whuc" sourceRef="Gateway_04u0k3i" targetRef="Event_1833suq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_14yzvvg">
      <bpmn:incoming>Flow_0ocfyx7</bpmn:incoming>
      <bpmn:incoming>Flow_0xtmg8g</bpmn:incoming>
      <bpmn:outgoing>Flow_0tg5g7g</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ocfyx7" sourceRef="Gateway_04u0k3i" targetRef="Gateway_14yzvvg" />
    <bpmn:sequenceFlow id="Flow_0tg5g7g" sourceRef="Gateway_14yzvvg" targetRef="Gateway_0p1thx5" />
    <bpmn:sequenceFlow id="Flow_0xtmg8g" sourceRef="Gateway_0jvo0fx" targetRef="Gateway_14yzvvg" />
  </bpmn:process>
  <bpmn:message id="Message_18lupa4" name="MNPCallBack" />
  <bpmn:message id="Message_0cxkag2" />
  <bpmn:message id="Message_0uyo7lb" />
  <bpmn:message id="Message_0z0mmot" name="MNPCallBack" />
  <bpmn:message id="Message_0o55ogt" name="SimDeliveryCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="InterimNumberPortIn">
      <bpmndi:BPMNShape id="Event_1tkja61_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="212" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0sgogx3_di" bpmnElement="PortInTypeFinder">
        <dc:Bounds x="250" y="190" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1k3dkyo_di" bpmnElement="Gateway_1k3dkyo" isMarkerVisible="true">
        <dc:Bounds x="855" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0cxn4t8_di" bpmnElement="Event_0cxn4t8">
        <dc:Bounds x="862" y="322" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1p3ub4k_di" bpmnElement="Gateway_1p3ub4k" isMarkerVisible="true">
        <dc:Bounds x="1175" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1155" y="262" width="90" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0o6zmcp_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="1320" y="190" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1dsta8e_di" bpmnElement="Gateway_1dsta8e" isMarkerVisible="true">
        <dc:Bounds x="1515" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ii6t8z_di" bpmnElement="Gateway_1ii6t8z" isMarkerVisible="true">
        <dc:Bounds x="1655" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1715" y="216" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1pzk86x_di" bpmnElement="InterimPortInFromExtTelcoOrSingtel">
        <dc:Bounds x="1830" y="91" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1gp8prf_di" bpmnElement="InterimPortInFromMvnoToMvno">
        <dc:Bounds x="1830" y="320" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0t2c59y_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2142" y="212" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tuf0dy_di" bpmnElement="Gateway_1tuf0dy" isMarkerVisible="true">
        <dc:Bounds x="2015" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1csjr54" bpmnElement="QueryMNPDetails">
        <dc:Bounds x="680" y="190" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_06e9mar_di" bpmnElement="Gateway_06e9mar" isMarkerVisible="true">
        <dc:Bounds x="415" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1m75fss_di" bpmnElement="Event_1m75fss">
        <dc:Bounds x="422" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1cd71mk_di" bpmnElement="Gateway_1cd71mk" isMarkerVisible="true">
        <dc:Bounds x="535" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="524" y="141.5" width="72" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0p1thx5_di" bpmnElement="Gateway_0p1thx5" isMarkerVisible="true">
        <dc:Bounds x="1005" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0jvo0fx_di" bpmnElement="Gateway_0jvo0fx" isMarkerVisible="true">
        <dc:Bounds x="535" y="375" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_04u0k3i_di" bpmnElement="Gateway_04u0k3i" isMarkerVisible="true">
        <dc:Bounds x="855" y="375" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1833suq_di" bpmnElement="Event_1833suq">
        <dc:Bounds x="862" y="462" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0b77wml" bpmnElement="IdentificationIdValidator">
        <dc:Bounds x="680" y="360" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_14yzvvg_di" bpmnElement="Gateway_14yzvvg" isMarkerVisible="true">
        <dc:Bounds x="1005" y="375" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1hmnjzb_di" bpmnElement="Flow_1hmnjzb">
        <di:waypoint x="880" y="255" />
        <di:waypoint x="880" y="322" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="883" y="286" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00yqrsi_di" bpmnElement="Flow_00yqrsi">
        <di:waypoint x="1225" y="230" />
        <di:waypoint x="1320" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1264" y="212" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03u6f66_di" bpmnElement="Flow_03u6f66">
        <di:waypoint x="1200" y="205" />
        <di:waypoint x="1200" y="100" />
        <di:waypoint x="1540" y="100" />
        <di:waypoint x="1540" y="205" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1364" y="82" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cunzyv_di" bpmnElement="Flow_0cunzyv">
        <di:waypoint x="1420" y="230" />
        <di:waypoint x="1515" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0awpx7k_di" bpmnElement="Flow_0awpx7k">
        <di:waypoint x="1680" y="205" />
        <di:waypoint x="1680" y="131" />
        <di:waypoint x="1830" y="131" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1683" y="62" width="73" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1l2hlpg_di" bpmnElement="Flow_1l2hlpg">
        <di:waypoint x="1680" y="255" />
        <di:waypoint x="1680" y="360" />
        <di:waypoint x="1830" y="360" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1686" y="316" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c0f03x_di" bpmnElement="Flow_1c0f03x">
        <di:waypoint x="1930" y="360" />
        <di:waypoint x="2040" y="360" />
        <di:waypoint x="2040" y="255" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08xftxu_di" bpmnElement="Flow_08xftxu">
        <di:waypoint x="2065" y="230" />
        <di:waypoint x="2142" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07vuljq_di" bpmnElement="Flow_07vuljq">
        <di:waypoint x="1930" y="131" />
        <di:waypoint x="2040" y="131" />
        <di:waypoint x="2040" y="205" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0w5vsus_di" bpmnElement="Flow_0w5vsus">
        <di:waypoint x="1565" y="230" />
        <di:waypoint x="1655" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03h6oj4_di" bpmnElement="Flow_03h6oj4">
        <di:waypoint x="905" y="230" />
        <di:waypoint x="1005" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="917" y="212" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1i2zowt_di" bpmnElement="Flow_1i2zowt">
        <di:waypoint x="440" y="255" />
        <di:waypoint x="440" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="463" y="286" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15zrv9q_di" bpmnElement="Flow_15zrv9q">
        <di:waypoint x="780" y="230" />
        <di:waypoint x="855" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g2mqme_di" bpmnElement="Flow_1g2mqme">
        <di:waypoint x="188" y="230" />
        <di:waypoint x="250" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t2iyd5_di" bpmnElement="Flow_1t2iyd5">
        <di:waypoint x="350" y="230" />
        <di:waypoint x="415" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0j7ffzz_di" bpmnElement="Flow_0j7ffzz">
        <di:waypoint x="465" y="230" />
        <di:waypoint x="535" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="479" y="212" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07a7tzw_di" bpmnElement="Flow_07a7tzw">
        <di:waypoint x="585" y="230" />
        <di:waypoint x="680" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="624" y="212" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1irpe3f_di" bpmnElement="Flow_1irpe3f">
        <di:waypoint x="1055" y="230" />
        <di:waypoint x="1175" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1e5aevy_di" bpmnElement="Flow_1e5aevy">
        <di:waypoint x="560" y="255" />
        <di:waypoint x="560" y="375" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tovska_di" bpmnElement="Flow_0tovska">
        <di:waypoint x="585" y="400" />
        <di:waypoint x="680" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_053jopm_di" bpmnElement="Flow_053jopm">
        <di:waypoint x="780" y="400" />
        <di:waypoint x="855" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1q3whuc_di" bpmnElement="Flow_1q3whuc">
        <di:waypoint x="880" y="425" />
        <di:waypoint x="880" y="462" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ocfyx7_di" bpmnElement="Flow_0ocfyx7">
        <di:waypoint x="905" y="400" />
        <di:waypoint x="1005" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tg5g7g_di" bpmnElement="Flow_0tg5g7g">
        <di:waypoint x="1030" y="375" />
        <di:waypoint x="1030" y="255" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xtmg8g_di" bpmnElement="Flow_0xtmg8g">
        <di:waypoint x="560" y="425" />
        <di:waypoint x="560" y="530" />
        <di:waypoint x="1030" y="530" />
        <di:waypoint x="1030" y="425" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
