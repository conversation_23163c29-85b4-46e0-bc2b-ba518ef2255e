<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1xap55q" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="LifeCycleSync" isExecutable="true" camunda:jobPriority="${priority}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0sp9hy5</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_0j4rk40" default="Flow_03i6ukk">
      <bpmn:incoming>Flow_0ytl6wb</bpmn:incoming>
      <bpmn:outgoing>Flow_0wmm778</bpmn:outgoing>
      <bpmn:outgoing>Flow_03i6ukk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_164xuyz">
      <bpmn:incoming>Flow_0wmm778</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0wmm778" name="Failure" sourceRef="Gateway_0j4rk40" targetRef="Event_164xuyz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0rpkbfh</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_03i6ukk" name="Success" sourceRef="Gateway_0j4rk40" targetRef="Gateway_1emx9ai" />
    <bpmn:exclusiveGateway id="Gateway_0dtk7rr" default="Flow_0rpkbfh">
      <bpmn:incoming>Flow_114yyio</bpmn:incoming>
      <bpmn:outgoing>Flow_0rpkbfh</bpmn:outgoing>
      <bpmn:outgoing>Flow_0wnikr9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0rpkbfh" name="Success" sourceRef="Gateway_0dtk7rr" targetRef="orderExecEnd" />
    <bpmn:endEvent id="Event_0trewhl">
      <bpmn:incoming>Flow_0wnikr9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0wnikr9" name="Failure" sourceRef="Gateway_0dtk7rr" targetRef="Event_0trewhl">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0sp9hy5" sourceRef="orderExecStart" targetRef="Gateway_091glm1" />
    <bpmn:serviceTask id="BSUpdateServiceState" name="Billing Update Service State" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_08xl2rl</bpmn:incoming>
      <bpmn:outgoing>Flow_114yyio</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="SMLifeCycleSync" name="SM LifeCycleSync Suspend" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0kyi8fq</bpmn:incoming>
      <bpmn:outgoing>Flow_0ytl6wb</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ytl6wb" sourceRef="SMLifeCycleSync" targetRef="Gateway_0j4rk40" />
    <bpmn:sequenceFlow id="Flow_114yyio" sourceRef="BSUpdateServiceState" targetRef="Gateway_0dtk7rr" />
    <bpmn:exclusiveGateway id="Gateway_091glm1" default="Flow_068psh8">
      <bpmn:incoming>Flow_0sp9hy5</bpmn:incoming>
      <bpmn:outgoing>Flow_068psh8</bpmn:outgoing>
      <bpmn:outgoing>Flow_1eytum2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="SMLifeCycleSyncResume" name="SM LifeCycleSync Resume" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1nefgjl</bpmn:incoming>
      <bpmn:outgoing>Flow_1b9iyh6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_068psh8" name="Resume" sourceRef="Gateway_091glm1" targetRef="Gateway_10siisn" />
    <bpmn:exclusiveGateway id="Gateway_17wuhiz" default="Flow_0596hw3">
      <bpmn:incoming>Flow_1b9iyh6</bpmn:incoming>
      <bpmn:outgoing>Flow_08v3qwm</bpmn:outgoing>
      <bpmn:outgoing>Flow_0596hw3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1b9iyh6" sourceRef="SMLifeCycleSyncResume" targetRef="Gateway_17wuhiz" />
    <bpmn:endEvent id="Event_12p61dc">
      <bpmn:incoming>Flow_08v3qwm</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_08v3qwm" name="Failure" sourceRef="Gateway_17wuhiz" targetRef="Event_12p61dc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1emx9ai">
      <bpmn:incoming>Flow_0596hw3</bpmn:incoming>
      <bpmn:incoming>Flow_03i6ukk</bpmn:incoming>
      <bpmn:outgoing>Flow_08xl2rl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0596hw3" name="Sucess" sourceRef="Gateway_17wuhiz" targetRef="Gateway_1emx9ai" />
    <bpmn:sequenceFlow id="Flow_08xl2rl" sourceRef="Gateway_1emx9ai" targetRef="BSUpdateServiceState" />
    <bpmn:callActivity id="AddSubscriptionWorkflow" name="AddSubscription Workflow" camunda:asyncBefore="true" calledElement="AddSubscription">
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:out variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_047n4uk</bpmn:incoming>
      <bpmn:outgoing>Flow_0l652u1</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_0rta3tk" default="Flow_0syabv2">
      <bpmn:incoming>Flow_0l652u1</bpmn:incoming>
      <bpmn:outgoing>Flow_0oobgu7</bpmn:outgoing>
      <bpmn:outgoing>Flow_0syabv2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0l652u1" sourceRef="AddSubscriptionWorkflow" targetRef="Gateway_0rta3tk" />
    <bpmn:exclusiveGateway id="Gateway_0i4tloq">
      <bpmn:incoming>Flow_0oobgu7</bpmn:incoming>
      <bpmn:incoming>Flow_1vaps04</bpmn:incoming>
      <bpmn:outgoing>Flow_0kyi8fq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0oobgu7" name="Success" sourceRef="Gateway_0rta3tk" targetRef="Gateway_0i4tloq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1dco63h">
      <bpmn:incoming>Flow_0syabv2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0syabv2" name="Failure" sourceRef="Gateway_0rta3tk" targetRef="Event_1dco63h" />
    <bpmn:sequenceFlow id="Flow_1eytum2" name="Suspend" sourceRef="Gateway_091glm1" targetRef="Gateway_1nftw6q">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order.serviceManagement.newState").stringValue() == "Suspend"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1nftw6q" name="check if AddSubscription call required or not" default="Flow_1vaps04">
      <bpmn:incoming>Flow_1eytum2</bpmn:incoming>
      <bpmn:outgoing>Flow_047n4uk</bpmn:outgoing>
      <bpmn:outgoing>Flow_1vaps04</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_047n4uk" name="yes" sourceRef="Gateway_1nftw6q" targetRef="AddSubscriptionWorkflow">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.hasProp('enrichmentResults')&amp;&amp; workflowData.jsonPath("$.enrichmentResults.addSubscriptionCallRqd").boolValue() == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1vaps04" name="no" sourceRef="Gateway_1nftw6q" targetRef="Gateway_0i4tloq" />
    <bpmn:sequenceFlow id="Flow_0kyi8fq" sourceRef="Gateway_0i4tloq" targetRef="SMLifeCycleSync" />
    <bpmn:callActivity id="CancelSubscriptionWorkflow" name="CancelSubscription Workflow" camunda:asyncBefore="true" calledElement="CancelSubscription">
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:out variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0pzrwmr</bpmn:incoming>
      <bpmn:outgoing>Flow_12bukfo</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_10siisn" name="check if CancelSubscription call required or not" default="Flow_1kpy6bo">
      <bpmn:incoming>Flow_068psh8</bpmn:incoming>
      <bpmn:outgoing>Flow_0pzrwmr</bpmn:outgoing>
      <bpmn:outgoing>Flow_1kpy6bo</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0pzrwmr" name="yes" sourceRef="Gateway_10siisn" targetRef="CancelSubscriptionWorkflow">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.hasProp('enrichmentResults')&amp;&amp; workflowData.jsonPath("$.enrichmentResults.cancelSubscriptionCallRqd").boolValue() == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0s6evgd" default="Flow_0ob6al8">
      <bpmn:incoming>Flow_12bukfo</bpmn:incoming>
      <bpmn:outgoing>Flow_0qcbol2</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ob6al8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_12bukfo" sourceRef="CancelSubscriptionWorkflow" targetRef="Gateway_0s6evgd" />
    <bpmn:exclusiveGateway id="Gateway_0lmnqtd">
      <bpmn:incoming>Flow_0qcbol2</bpmn:incoming>
      <bpmn:incoming>Flow_1kpy6bo</bpmn:incoming>
      <bpmn:outgoing>Flow_1nefgjl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0qcbol2" name="success" sourceRef="Gateway_0s6evgd" targetRef="Gateway_0lmnqtd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1bpamih">
      <bpmn:incoming>Flow_0ob6al8</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0ob6al8" sourceRef="Gateway_0s6evgd" targetRef="Event_1bpamih" />
    <bpmn:sequenceFlow id="Flow_1kpy6bo" name="no" sourceRef="Gateway_10siisn" targetRef="Gateway_0lmnqtd" />
    <bpmn:sequenceFlow id="Flow_1nefgjl" sourceRef="Gateway_0lmnqtd" targetRef="SMLifeCycleSyncResume" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="LifeCycleSync">
      <bpmndi:BPMNEdge id="Flow_1nefgjl_di" bpmnElement="Flow_1nefgjl">
        <di:waypoint x="965" y="150" />
        <di:waypoint x="1090" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kpy6bo_di" bpmnElement="Flow_1kpy6bo">
        <di:waypoint x="420" y="125" />
        <di:waypoint x="420" y="80" />
        <di:waypoint x="940" y="80" />
        <di:waypoint x="940" y="125" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="674" y="62" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ob6al8_di" bpmnElement="Flow_0ob6al8">
        <di:waypoint x="780" y="175" />
        <di:waypoint x="780" y="222" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qcbol2_di" bpmnElement="Flow_0qcbol2">
        <di:waypoint x="805" y="150" />
        <di:waypoint x="915" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="840" y="132" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12bukfo_di" bpmnElement="Flow_12bukfo">
        <di:waypoint x="660" y="150" />
        <di:waypoint x="755" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pzrwmr_di" bpmnElement="Flow_0pzrwmr">
        <di:waypoint x="445" y="150" />
        <di:waypoint x="560" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="494" y="132" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kyi8fq_di" bpmnElement="Flow_0kyi8fq">
        <di:waypoint x="1095" y="397" />
        <di:waypoint x="1270" y="397" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vaps04_di" bpmnElement="Flow_1vaps04">
        <di:waypoint x="520" y="372" />
        <di:waypoint x="520" y="300" />
        <di:waypoint x="1070" y="300" />
        <di:waypoint x="1070" y="372" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="789" y="282" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_047n4uk_di" bpmnElement="Flow_047n4uk">
        <di:waypoint x="545" y="397" />
        <di:waypoint x="660" y="397" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="594" y="379" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eytum2_di" bpmnElement="Flow_1eytum2">
        <di:waypoint x="335" y="397" />
        <di:waypoint x="495" y="397" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="378" y="369" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0syabv2_di" bpmnElement="Flow_0syabv2">
        <di:waypoint x="900" y="422" />
        <di:waypoint x="900" y="512" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="898" y="464" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oobgu7_di" bpmnElement="Flow_0oobgu7">
        <di:waypoint x="925" y="397" />
        <di:waypoint x="1045" y="397" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="964" y="379" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0l652u1_di" bpmnElement="Flow_0l652u1">
        <di:waypoint x="760" y="397" />
        <di:waypoint x="875" y="397" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08xl2rl_di" bpmnElement="Flow_08xl2rl">
        <di:waypoint x="1655" y="397" />
        <di:waypoint x="1710" y="397" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0596hw3_di" bpmnElement="Flow_0596hw3">
        <di:waypoint x="1335" y="150" />
        <di:waypoint x="1630" y="150" />
        <di:waypoint x="1630" y="372" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1465" y="132" width="37" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08v3qwm_di" bpmnElement="Flow_08v3qwm">
        <di:waypoint x="1310" y="175" />
        <di:waypoint x="1310" y="222" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1251" y="182" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b9iyh6_di" bpmnElement="Flow_1b9iyh6">
        <di:waypoint x="1190" y="150" />
        <di:waypoint x="1285" y="150" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_068psh8_di" bpmnElement="Flow_068psh8">
        <di:waypoint x="310" y="372" />
        <di:waypoint x="310" y="150" />
        <di:waypoint x="395" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="329" y="123" width="42" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_114yyio_di" bpmnElement="Flow_114yyio">
        <di:waypoint x="1810" y="397" />
        <di:waypoint x="1875" y="397" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ytl6wb_di" bpmnElement="Flow_0ytl6wb">
        <di:waypoint x="1370" y="397" />
        <di:waypoint x="1455" y="397" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0sp9hy5_di" bpmnElement="Flow_0sp9hy5">
        <di:waypoint x="188" y="397" />
        <di:waypoint x="285" y="397" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wnikr9_di" bpmnElement="Flow_0wnikr9">
        <di:waypoint x="1900" y="422" />
        <di:waypoint x="1900" y="482" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1899" y="449" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rpkbfh_di" bpmnElement="Flow_0rpkbfh">
        <di:waypoint x="1925" y="397" />
        <di:waypoint x="2022" y="397" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1952" y="379" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03i6ukk_di" bpmnElement="Flow_03i6ukk">
        <di:waypoint x="1505" y="397" />
        <di:waypoint x="1605" y="397" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1505" y="379" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wmm778_di" bpmnElement="Flow_0wmm778">
        <di:waypoint x="1480" y="422" />
        <di:waypoint x="1480" y="482" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1479" y="459" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="379" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0j4rk40_di" bpmnElement="Gateway_0j4rk40" isMarkerVisible="true">
        <dc:Bounds x="1455" y="372" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_164xuyz_di" bpmnElement="Event_164xuyz">
        <dc:Bounds x="1462" y="482" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_02dypos_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2022" y="379" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0dtk7rr_di" bpmnElement="Gateway_0dtk7rr" isMarkerVisible="true">
        <dc:Bounds x="1875" y="372" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0trewhl_di" bpmnElement="Event_0trewhl">
        <dc:Bounds x="1882" y="482" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1a9w43n_di" bpmnElement="BSUpdateServiceState">
        <dc:Bounds x="1710" y="357" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0y3kma9_di" bpmnElement="SMLifeCycleSync">
        <dc:Bounds x="1270" y="357" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_091glm1_di" bpmnElement="Gateway_091glm1" isMarkerVisible="true">
        <dc:Bounds x="285" y="372" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1mfegcv_di" bpmnElement="SMLifeCycleSyncResume">
        <dc:Bounds x="1090" y="110" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_17wuhiz_di" bpmnElement="Gateway_17wuhiz" isMarkerVisible="true">
        <dc:Bounds x="1285" y="125" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_12p61dc_di" bpmnElement="Event_12p61dc">
        <dc:Bounds x="1292" y="222" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1emx9ai_di" bpmnElement="Gateway_1emx9ai" isMarkerVisible="true">
        <dc:Bounds x="1605" y="372" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0j3xs48_di" bpmnElement="AddSubscriptionWorkflow">
        <dc:Bounds x="660" y="357" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0rta3tk_di" bpmnElement="Gateway_0rta3tk" isMarkerVisible="true">
        <dc:Bounds x="875" y="372" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0i4tloq_di" bpmnElement="Gateway_0i4tloq" isMarkerVisible="true">
        <dc:Bounds x="1045" y="372" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1dco63h_di" bpmnElement="Event_1dco63h">
        <dc:Bounds x="882" y="512" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1nftw6q_di" bpmnElement="Gateway_1nftw6q" isMarkerVisible="true">
        <dc:Bounds x="495" y="372" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="482" y="429" width="80" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ti9o1o_di" bpmnElement="CancelSubscriptionWorkflow">
        <dc:Bounds x="560" y="110" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_10siisn_di" bpmnElement="Gateway_10siisn" isMarkerVisible="true">
        <dc:Bounds x="395" y="125" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="376" y="182" width="90" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0s6evgd_di" bpmnElement="Gateway_0s6evgd" isMarkerVisible="true">
        <dc:Bounds x="755" y="125" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0lmnqtd_di" bpmnElement="Gateway_0lmnqtd" isMarkerVisible="true">
        <dc:Bounds x="915" y="125" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1bpamih_di" bpmnElement="Event_1bpamih">
        <dc:Bounds x="762" y="222" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
