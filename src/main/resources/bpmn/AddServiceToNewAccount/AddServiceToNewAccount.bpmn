<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.23.0">
  <bpmn:process id="AddServiceToNewAccount" name="AddServiceToNewAccount" isExecutable="true" camunda:versionTag="${version}">
    <bpmn:extensionElements />
    <bpmn:startEvent id="orderExecStart" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_0gc4555</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_0bpbzxx" default="Flow_0k1pg3e">
      <bpmn:incoming>Flow_0cah0j6</bpmn:incoming>
      <bpmn:outgoing>Flow_01b8t2y</bpmn:outgoing>
      <bpmn:outgoing>Flow_0k1pg3e</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_03c4vy4">
      <bpmn:incoming>Flow_01b8t2y</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_01b8t2y" name="Failure" sourceRef="Gateway_0bpbzxx" targetRef="Event_03c4vy4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSCreateAccount" name="Create Billing Account" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0d1rpnq</bpmn:incoming>
      <bpmn:outgoing>Flow_0cah0j6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0gc4555" sourceRef="orderExecStart" targetRef="OrderEnrichment" />
    <bpmn:sequenceFlow id="Flow_0ae2gv8" sourceRef="CreateInstancesTask" targetRef="Gateway_1nrrv7h" />
    <bpmn:serviceTask id="CreateInstancesTask" name="Create Instances" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${createInstancesDelegateOnboarding}">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="childProcess">Onboarding-ServiceProvisioning</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1sz58uc</bpmn:incoming>
      <bpmn:incoming>Flow_14jjesd</bpmn:incoming>
      <bpmn:outgoing>Flow_0ae2gv8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1nrrv7h" name="All instances created ?">
      <bpmn:incoming>Flow_0ae2gv8</bpmn:incoming>
      <bpmn:outgoing>Flow_1kqxjia</bpmn:outgoing>
      <bpmn:outgoing>Flow_1sz58uc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1kqxjia" name="Yes" sourceRef="Gateway_1nrrv7h" targetRef="orderExecEnd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${allCreated}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1sz58uc" name="No" sourceRef="Gateway_1nrrv7h" targetRef="CreateInstancesTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${not allCreated}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1kqxjia</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0fuzl5a" default="Flow_1m63qpo">
      <bpmn:incoming>Flow_0vvv1ml</bpmn:incoming>
      <bpmn:outgoing>Flow_1pgh3s3</bpmn:outgoing>
      <bpmn:outgoing>Flow_1m63qpo</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1fi99c8">
      <bpmn:incoming>Flow_1pgh3s3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="OrderEnrichment" name="Order Enrichment Delegate" camunda:asyncBefore="true" camunda:delegateExpression="${orderEnrichmentDelegate}">
      <bpmn:incoming>Flow_0gc4555</bpmn:incoming>
      <bpmn:outgoing>Flow_0vvv1ml</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0vvv1ml" sourceRef="OrderEnrichment" targetRef="Gateway_0fuzl5a" />
    <bpmn:sequenceFlow id="Flow_1pgh3s3" name="Failure" sourceRef="Gateway_0fuzl5a" targetRef="Event_1fi99c8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0k1pg3e" name="success" sourceRef="Gateway_0bpbzxx" targetRef="Gateway_1ndv834" />
    <bpmn:exclusiveGateway id="Gateway_1vr41hn" default="Flow_1ojsgty">
      <bpmn:incoming>Flow_0pjs9j1</bpmn:incoming>
      <bpmn:outgoing>Flow_009rhwp</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ojsgty</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0yq91sr">
      <bpmn:incoming>Flow_009rhwp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_009rhwp" name="Failure" sourceRef="Gateway_1vr41hn" targetRef="Event_0yq91sr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ojsgty" sourceRef="Gateway_1vr41hn" targetRef="GenerateDataDelegateTask" />
    <bpmn:serviceTask id="GenerateDataDelegateTask" name="Generate Data Delegate" camunda:delegateExpression="${generateDataDelegateOnboarding}">
      <bpmn:incoming>Flow_1ojsgty</bpmn:incoming>
      <bpmn:outgoing>Flow_14jjesd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_14jjesd" sourceRef="GenerateDataDelegateTask" targetRef="CreateInstancesTask" />
    <bpmn:exclusiveGateway id="Gateway_1hxxfys" default="Flow_1v7i0kt">
      <bpmn:incoming>Flow_1rqe3ng</bpmn:incoming>
      <bpmn:outgoing>Flow_0pcxz9r</bpmn:outgoing>
      <bpmn:outgoing>Flow_1v7i0kt</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0k5jkz3">
      <bpmn:incoming>Flow_0pcxz9r</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0pcxz9r" name="Failure" sourceRef="Gateway_1hxxfys" targetRef="Event_0k5jkz3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="ESB_CreateAccount" name="Account Creation in OCS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="thirdPartyId">ocs-account-creation</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1png9t2</bpmn:incoming>
      <bpmn:outgoing>Flow_1rqe3ng</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1rqe3ng" sourceRef="ESB_CreateAccount" targetRef="Gateway_1hxxfys" />
    <bpmn:sequenceFlow id="Flow_1m63qpo" sourceRef="Gateway_0fuzl5a" targetRef="Gateway_0kgn9t3" />
    <bpmn:callActivity id="PaymentWorkflow" name="Payment and future order wokrflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1v7i0kt</bpmn:incoming>
      <bpmn:outgoing>Flow_0pjs9j1</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0pjs9j1" sourceRef="PaymentWorkflow" targetRef="Gateway_1vr41hn" />
    <bpmn:sequenceFlow id="Flow_1v7i0kt" sourceRef="Gateway_1hxxfys" targetRef="PaymentWorkflow" />
    <bpmn:exclusiveGateway id="Gateway_0kgn9t3" name="is account created in enrichment" default="Flow_0d1rpnq">
      <bpmn:incoming>Flow_1m63qpo</bpmn:incoming>
      <bpmn:outgoing>Flow_0d1rpnq</bpmn:outgoing>
      <bpmn:outgoing>Flow_0n7j4c4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0d1rpnq" name="No" sourceRef="Gateway_0kgn9t3" targetRef="BSCreateAccount" />
    <bpmn:sequenceFlow id="Flow_0cah0j6" sourceRef="BSCreateAccount" targetRef="Gateway_0bpbzxx" />
    <bpmn:exclusiveGateway id="Gateway_1ndv834">
      <bpmn:incoming>Flow_0k1pg3e</bpmn:incoming>
      <bpmn:incoming>Flow_0n7j4c4</bpmn:incoming>
      <bpmn:outgoing>Flow_1png9t2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1png9t2" sourceRef="Gateway_1ndv834" targetRef="ESB_CreateAccount" />
    <bpmn:sequenceFlow id="Flow_0n7j4c4" name="Yes" sourceRef="Gateway_0kgn9t3" targetRef="Gateway_1ndv834">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isLegacy}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_061isi2" name="PaymentCallback" />
  <bpmn:message id="Message_3ven5fh" name="SuspendWait" />
  <bpmn:message id="Message_1y1dj88" name="FOCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="AddServiceToNewAccount">
      <bpmndi:BPMNShape id="Event_1x7dsnm_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="162" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0bpbzxx_di" bpmnElement="Gateway_0bpbzxx" isMarkerVisible="true">
        <dc:Bounds x="825" y="155" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_03c4vy4_di" bpmnElement="Event_03c4vy4">
        <dc:Bounds x="832" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0n2v8qp_di" bpmnElement="BSCreateAccount">
        <dc:Bounds x="660" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1r0bov3_di" bpmnElement="CreateInstancesTask">
        <dc:Bounds x="1980" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1nrrv7h_di" bpmnElement="Gateway_1nrrv7h" isMarkerVisible="true">
        <dc:Bounds x="2175" y="155" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2169" y="125" width="62" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0gn54oi_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2312" y="162" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fuzl5a_di" bpmnElement="Gateway_0fuzl5a" isMarkerVisible="true">
        <dc:Bounds x="415" y="155" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1fi99c8_di" bpmnElement="Event_1fi99c8">
        <dc:Bounds x="422" y="272" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01wyi3h_di" bpmnElement="OrderEnrichment">
        <dc:Bounds x="250" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1vr41hn_di" bpmnElement="Gateway_1vr41hn" isMarkerVisible="true">
        <dc:Bounds x="1615" y="155" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0yq91sr_di" bpmnElement="Event_0yq91sr">
        <dc:Bounds x="1622" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_174xh0s_di" bpmnElement="GenerateDataDelegateTask">
        <dc:Bounds x="1750" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1hxxfys_di" bpmnElement="Gateway_1hxxfys" isMarkerVisible="true">
        <dc:Bounds x="1285" y="155" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0k5jkz3_di" bpmnElement="Event_0k5jkz3">
        <dc:Bounds x="1292" y="272" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0thv5ay_di" bpmnElement="ESB_CreateAccount">
        <dc:Bounds x="1080" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_08ln582_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="1420" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0kgn9t3_di" bpmnElement="Gateway_0kgn9t3" isMarkerVisible="true">
        <dc:Bounds x="535" y="155" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="515" y="212" width="90" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ndv834_di" bpmnElement="Gateway_1ndv834" isMarkerVisible="true">
        <dc:Bounds x="955" y="155" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_01b8t2y_di" bpmnElement="Flow_01b8t2y">
        <di:waypoint x="850" y="205" />
        <di:waypoint x="850" y="282" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="853" y="224" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gc4555_di" bpmnElement="Flow_0gc4555">
        <di:waypoint x="188" y="180" />
        <di:waypoint x="250" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ae2gv8_di" bpmnElement="Flow_0ae2gv8">
        <di:waypoint x="2080" y="180" />
        <di:waypoint x="2175" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kqxjia_di" bpmnElement="Flow_1kqxjia">
        <di:waypoint x="2225" y="180" />
        <di:waypoint x="2312" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2260" y="162" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sz58uc_di" bpmnElement="Flow_1sz58uc">
        <di:waypoint x="2200" y="205" />
        <di:waypoint x="2200" y="270" />
        <di:waypoint x="2030" y="270" />
        <di:waypoint x="2030" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2108" y="273" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vvv1ml_di" bpmnElement="Flow_0vvv1ml">
        <di:waypoint x="350" y="180" />
        <di:waypoint x="415" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pgh3s3_di" bpmnElement="Flow_1pgh3s3">
        <di:waypoint x="440" y="205" />
        <di:waypoint x="440" y="272" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="445" y="221" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0k1pg3e_di" bpmnElement="Flow_0k1pg3e">
        <di:waypoint x="875" y="180" />
        <di:waypoint x="955" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="890" y="153" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_009rhwp_di" bpmnElement="Flow_009rhwp">
        <di:waypoint x="1640" y="205" />
        <di:waypoint x="1640" y="282" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1641" y="239" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ojsgty_di" bpmnElement="Flow_1ojsgty">
        <di:waypoint x="1665" y="180" />
        <di:waypoint x="1750" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14jjesd_di" bpmnElement="Flow_14jjesd">
        <di:waypoint x="1850" y="180" />
        <di:waypoint x="1980" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pcxz9r_di" bpmnElement="Flow_0pcxz9r">
        <di:waypoint x="1310" y="205" />
        <di:waypoint x="1310" y="272" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1309" y="236" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rqe3ng_di" bpmnElement="Flow_1rqe3ng">
        <di:waypoint x="1180" y="180" />
        <di:waypoint x="1285" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m63qpo_di" bpmnElement="Flow_1m63qpo">
        <di:waypoint x="465" y="180" />
        <di:waypoint x="535" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pjs9j1_di" bpmnElement="Flow_0pjs9j1">
        <di:waypoint x="1520" y="180" />
        <di:waypoint x="1615" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1v7i0kt_di" bpmnElement="Flow_1v7i0kt">
        <di:waypoint x="1335" y="180" />
        <di:waypoint x="1420" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d1rpnq_di" bpmnElement="Flow_0d1rpnq">
        <di:waypoint x="585" y="180" />
        <di:waypoint x="660" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="615" y="162" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cah0j6_di" bpmnElement="Flow_0cah0j6">
        <di:waypoint x="760" y="180" />
        <di:waypoint x="825" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1png9t2_di" bpmnElement="Flow_1png9t2">
        <di:waypoint x="1005" y="180" />
        <di:waypoint x="1080" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n7j4c4_di" bpmnElement="Flow_0n7j4c4">
        <di:waypoint x="560" y="155" />
        <di:waypoint x="560" y="70" />
        <di:waypoint x="980" y="70" />
        <di:waypoint x="980" y="155" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="761" y="52" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
