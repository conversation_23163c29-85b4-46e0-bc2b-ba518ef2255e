<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="UpdateLanguage" name="UpdateLanguage" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_10616ee</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="OCSUpdateLanguageId" name="Update Language Id in OCS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_10616ee</bpmn:incoming>
      <bpmn:outgoing>Flow_1t0t7o7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSUpdateLanguageId" name="Billing UpdateLanguageId" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1wvj22a</bpmn:incoming>
      <bpmn:outgoing>Flow_09gswka</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_10616ee" sourceRef="orderExecStart" targetRef="OCSUpdateLanguageId" />
    <bpmn:exclusiveGateway id="Gateway_1tyxpcl" default="Flow_1wvj22a">
      <bpmn:incoming>Flow_1t0t7o7</bpmn:incoming>
      <bpmn:outgoing>Flow_1wvj22a</bpmn:outgoing>
      <bpmn:outgoing>Flow_1luuggx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1wvj22a" name="Success" sourceRef="Gateway_1tyxpcl" targetRef="BSUpdateLanguageId" />
    <bpmn:endEvent id="Event_1b7ko4d">
      <bpmn:incoming>Flow_1luuggx</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1luuggx" name="Failure" sourceRef="Gateway_1tyxpcl" targetRef="Event_1b7ko4d">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>SequenceFlow_1fgo1d8</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1t0t7o7" sourceRef="OCSUpdateLanguageId" targetRef="Gateway_1tyxpcl" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_1und7wx" default="SequenceFlow_1fgo1d8">
      <bpmn:incoming>Flow_09gswka</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1fgo1d8</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1uca47d</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_1fgo1d8" name="Success" sourceRef="ExclusiveGateway_1und7wx" targetRef="orderExecEnd" />
    <bpmn:endEvent id="EndEvent_1aqc2xy">
      <bpmn:incoming>SequenceFlow_1uca47d</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_1uca47d" name="Failure" sourceRef="ExclusiveGateway_1und7wx" targetRef="EndEvent_1aqc2xy">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_09gswka" sourceRef="BSUpdateLanguageId" targetRef="ExclusiveGateway_1und7wx" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="UpdateLanguage">
      <bpmndi:BPMNEdge id="SequenceFlow_1uca47d_di" bpmnElement="SequenceFlow_1uca47d">
        <di:waypoint x="920" y="125" />
        <di:waypoint x="920" y="202" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="930" y="124" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1fgo1d8_di" bpmnElement="SequenceFlow_1fgo1d8">
        <di:waypoint x="945" y="100" />
        <di:waypoint x="1112" y="100" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="964" y="82" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t0t7o7_di" bpmnElement="Flow_1t0t7o7">
        <di:waypoint x="390" y="100" />
        <di:waypoint x="485" y="100" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1luuggx_di" bpmnElement="Flow_1luuggx">
        <di:waypoint x="510" y="125" />
        <di:waypoint x="510" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="508" y="177" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wvj22a_di" bpmnElement="Flow_1wvj22a">
        <di:waypoint x="535" y="100" />
        <di:waypoint x="670" y="100" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="583" y="82" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10616ee_di" bpmnElement="Flow_10616ee">
        <di:waypoint x="188" y="100" />
        <di:waypoint x="290" y="100" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09gswka_di" bpmnElement="Flow_09gswka">
        <di:waypoint x="770" y="100" />
        <di:waypoint x="895" y="100" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_1x7dsnm_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="82" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1tegtwq_di" bpmnElement="OCSUpdateLanguageId">
        <dc:Bounds x="290" y="60" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_19c2pez_di" bpmnElement="BSUpdateLanguageId">
        <dc:Bounds x="670" y="60" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tyxpcl_di" bpmnElement="Gateway_1tyxpcl" isMarkerVisible="true">
        <dc:Bounds x="485" y="75" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1b7ko4d_di" bpmnElement="Event_1b7ko4d">
        <dc:Bounds x="492" y="212" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0oin3ho_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="1112" y="82" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1und7wx_di" bpmnElement="ExclusiveGateway_1und7wx" isMarkerVisible="true">
        <dc:Bounds x="895" y="75" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1aqc2xy_di" bpmnElement="EndEvent_1aqc2xy">
        <dc:Bounds x="902" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
