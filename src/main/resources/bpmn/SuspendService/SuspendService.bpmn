<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0">
  <bpmn:process id="SuspendService" name="SuspendService" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_07d5dfk</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_06d8752</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSUpdateServiceState" name="Suspend Service in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_0pbxzkj</bpmn:incoming>
      <bpmn:outgoing>Flow_06d8752</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="OCSSuspendService" name="Suspend Service in OCS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0sig6b0</bpmn:incoming>
      <bpmn:outgoing>Flow_1ploq8g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_06d8752" sourceRef="BSUpdateServiceState" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_1ploq8g" sourceRef="OCSSuspendService" targetRef="ExclusiveGateway_1dp8zxi" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_1dp8zxi" default="SequenceFlow_0pbxzkj">
      <bpmn:incoming>Flow_1ploq8g</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_12tc7e8</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0pbxzkj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="EndEvent_0by08pc">
      <bpmn:incoming>SequenceFlow_12tc7e8</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_12tc7e8" name="Failure" sourceRef="ExclusiveGateway_1dp8zxi" targetRef="EndEvent_0by08pc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0qxa0m2" name="document creation req" default="Flow_0dywg0f">
      <bpmn:incoming>Flow_0z4gk5k</bpmn:incoming>
      <bpmn:outgoing>Flow_1tffmzf</bpmn:outgoing>
      <bpmn:outgoing>Flow_0dywg0f</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="BSCreateDocument" name="Create Document in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1tffmzf</bpmn:incoming>
      <bpmn:outgoing>Flow_0n6huey</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1nfos5j" default="Flow_0t6wzfd">
      <bpmn:incoming>Flow_0n6huey</bpmn:incoming>
      <bpmn:outgoing>Flow_0t6wzfd</bpmn:outgoing>
      <bpmn:outgoing>Flow_1pftnxe</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_14680b2">
      <bpmn:incoming>Flow_0dywg0f</bpmn:incoming>
      <bpmn:incoming>Flow_0t6wzfd</bpmn:incoming>
      <bpmn:outgoing>Flow_0sig6b0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1v8k94h">
      <bpmn:incoming>Flow_1pftnxe</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1tffmzf" name="yes" sourceRef="Gateway_0qxa0m2" targetRef="BSCreateDocument">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${hasAttachment}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0dywg0f" name="no" sourceRef="Gateway_0qxa0m2" targetRef="Gateway_14680b2" />
    <bpmn:sequenceFlow id="Flow_0n6huey" sourceRef="BSCreateDocument" targetRef="Gateway_1nfos5j" />
    <bpmn:sequenceFlow id="Flow_0t6wzfd" name="success" sourceRef="Gateway_1nfos5j" targetRef="Gateway_14680b2" />
    <bpmn:sequenceFlow id="Flow_1pftnxe" name="Failure" sourceRef="Gateway_1nfos5j" targetRef="Event_1v8k94h">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0sig6b0" sourceRef="Gateway_14680b2" targetRef="OCSSuspendService" />
    <bpmn:callActivity id="PaymentWorkflow" name="Payment and future order wokrflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0tjnte9</bpmn:incoming>
      <bpmn:outgoing>Flow_1uxy4um</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_0w1kamm" default="Flow_0z4gk5k">
      <bpmn:incoming>Flow_1uxy4um</bpmn:incoming>
      <bpmn:outgoing>Flow_1mo8kqx</bpmn:outgoing>
      <bpmn:outgoing>Flow_0z4gk5k</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1b8gzzc">
      <bpmn:incoming>Flow_1mo8kqx</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1uxy4um" sourceRef="PaymentWorkflow" targetRef="Gateway_0w1kamm" />
    <bpmn:sequenceFlow id="Flow_1mo8kqx" sourceRef="Gateway_0w1kamm" targetRef="Event_1b8gzzc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0z4gk5k" sourceRef="Gateway_0w1kamm" targetRef="Gateway_0qxa0m2" />
    <bpmn:sequenceFlow id="SequenceFlow_0pbxzkj" sourceRef="ExclusiveGateway_1dp8zxi" targetRef="BSUpdateServiceState" />
    <bpmn:exclusiveGateway id="Gateway_00k0am0" default="Flow_0rszro9">
      <bpmn:incoming>Flow_07d5dfk</bpmn:incoming>
      <bpmn:outgoing>Flow_0suvdof</bpmn:outgoing>
      <bpmn:outgoing>Flow_0rszro9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_07d5dfk" sourceRef="orderExecStart" targetRef="Gateway_00k0am0" />
    <bpmn:sequenceFlow id="Flow_0suvdof" name="yes" sourceRef="Gateway_00k0am0" targetRef="BookServiceFee">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${additionalCharges}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0ax6q8k" default="Flow_1rwdikj">
      <bpmn:incoming>Flow_04p9wkf</bpmn:incoming>
      <bpmn:outgoing>Flow_1rwdikj</bpmn:outgoing>
      <bpmn:outgoing>Flow_01fzsmy</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_04p9wkf" sourceRef="BookServiceFee" targetRef="Gateway_0ax6q8k" />
    <bpmn:exclusiveGateway id="Gateway_0q2yum1">
      <bpmn:incoming>Flow_1rwdikj</bpmn:incoming>
      <bpmn:incoming>Flow_0rszro9</bpmn:incoming>
      <bpmn:outgoing>Flow_0tjnte9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1rwdikj" name="Success" sourceRef="Gateway_0ax6q8k" targetRef="Gateway_0q2yum1" />
    <bpmn:sequenceFlow id="Flow_0tjnte9" sourceRef="Gateway_0q2yum1" targetRef="PaymentWorkflow" />
    <bpmn:endEvent id="Event_1lsd0a5">
      <bpmn:incoming>Flow_01fzsmy</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_01fzsmy" name="failure" sourceRef="Gateway_0ax6q8k" targetRef="Event_1lsd0a5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0rszro9" name="no" sourceRef="Gateway_00k0am0" targetRef="Gateway_0q2yum1" />
    <bpmn:serviceTask id="BookServiceFee" name="BookServiceFee" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0suvdof</bpmn:incoming>
      <bpmn:outgoing>Flow_04p9wkf</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmn:message id="Message_17ruo2d" name="SOMSuspendAirfiberServiceCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="SuspendService">
      <bpmndi:BPMNShape id="Event_08ncim8_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2471" y="168" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_13hk4ch_di" bpmnElement="BSUpdateServiceState">
        <dc:Bounds x="2289" y="146" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0172vy3_di" bpmnElement="OCSSuspendService">
        <dc:Bounds x="1803" y="146" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1dp8zxi_di" bpmnElement="ExclusiveGateway_1dp8zxi" isMarkerVisible="true">
        <dc:Bounds x="2076" y="161" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0by08pc_di" bpmnElement="EndEvent_0by08pc">
        <dc:Bounds x="2083" y="271" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06w4tl9" bpmnElement="Gateway_0qxa0m2" isMarkerVisible="true">
        <dc:Bounds x="1185" y="161" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1181" y="218" width="59" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1m2hrb1" bpmnElement="BSCreateDocument">
        <dc:Bounds x="1320" y="146" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0vwaf9d" bpmnElement="Gateway_1nfos5j" isMarkerVisible="true">
        <dc:Bounds x="1475" y="161" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_02508zx" bpmnElement="Gateway_14680b2" isMarkerVisible="true">
        <dc:Bounds x="1605" y="161" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0htevff" bpmnElement="Event_1v8k94h">
        <dc:Bounds x="1482" y="288" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1jzb165_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="850" y="146" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0w1kamm_di" bpmnElement="Gateway_0w1kamm" isMarkerVisible="true">
        <dc:Bounds x="1035" y="161" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1b8gzzc_di" bpmnElement="Event_1b8gzzc">
        <dc:Bounds x="1042" y="281" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1x7dsnm_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="168" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_00k0am0_di" bpmnElement="Gateway_00k0am0" isMarkerVisible="true">
        <dc:Bounds x="245" y="161" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ax6q8k_di" bpmnElement="Gateway_0ax6q8k" isMarkerVisible="true">
        <dc:Bounds x="525" y="161" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0q2yum1_di" bpmnElement="Gateway_0q2yum1" isMarkerVisible="true">
        <dc:Bounds x="645" y="161" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1lsd0a5_di" bpmnElement="Event_1lsd0a5">
        <dc:Bounds x="532" y="281" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1pvfcy7_di" bpmnElement="BookServiceFee">
        <dc:Bounds x="360" y="146" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_06d8752_di" bpmnElement="Flow_06d8752">
        <di:waypoint x="2389" y="186" />
        <di:waypoint x="2471" y="186" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ploq8g_di" bpmnElement="Flow_1ploq8g">
        <di:waypoint x="1903" y="186" />
        <di:waypoint x="2076" y="186" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_12tc7e8_di" bpmnElement="SequenceFlow_12tc7e8">
        <di:waypoint x="2101" y="211" />
        <di:waypoint x="2101" y="271" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2108" y="227" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1tz7zuw" bpmnElement="Flow_1tffmzf">
        <di:waypoint x="1235" y="186" />
        <di:waypoint x="1320" y="186" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1271" y="168" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0325kw4" bpmnElement="Flow_0dywg0f">
        <di:waypoint x="1210" y="161" />
        <di:waypoint x="1210" y="100" />
        <di:waypoint x="1630" y="100" />
        <di:waypoint x="1630" y="161" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1414" y="82" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0z6q4lc" bpmnElement="Flow_0n6huey">
        <di:waypoint x="1420" y="186" />
        <di:waypoint x="1475" y="186" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1tscudz" bpmnElement="Flow_0t6wzfd">
        <di:waypoint x="1525" y="186" />
        <di:waypoint x="1605" y="186" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1545" y="168" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_117o925" bpmnElement="Flow_1pftnxe">
        <di:waypoint x="1500" y="211" />
        <di:waypoint x="1500" y="288" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1503" y="238" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0sig6b0_di" bpmnElement="Flow_0sig6b0">
        <di:waypoint x="1655" y="186" />
        <di:waypoint x="1803" y="186" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uxy4um_di" bpmnElement="Flow_1uxy4um">
        <di:waypoint x="950" y="186" />
        <di:waypoint x="1035" y="186" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mo8kqx_di" bpmnElement="Flow_1mo8kqx">
        <di:waypoint x="1060" y="211" />
        <di:waypoint x="1060" y="281" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0z4gk5k_di" bpmnElement="Flow_0z4gk5k">
        <di:waypoint x="1085" y="186" />
        <di:waypoint x="1185" y="186" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0pbxzkj_di" bpmnElement="SequenceFlow_0pbxzkj">
        <di:waypoint x="2126" y="186" />
        <di:waypoint x="2289" y="186" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07d5dfk_di" bpmnElement="Flow_07d5dfk">
        <di:waypoint x="188" y="186" />
        <di:waypoint x="245" y="186" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0suvdof_di" bpmnElement="Flow_0suvdof">
        <di:waypoint x="295" y="186" />
        <di:waypoint x="360" y="186" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="319" y="168" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04p9wkf_di" bpmnElement="Flow_04p9wkf">
        <di:waypoint x="460" y="186" />
        <di:waypoint x="525" y="186" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rwdikj_di" bpmnElement="Flow_1rwdikj">
        <di:waypoint x="575" y="186" />
        <di:waypoint x="645" y="186" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="589" y="168" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tjnte9_di" bpmnElement="Flow_0tjnte9">
        <di:waypoint x="695" y="186" />
        <di:waypoint x="773" y="186" />
        <di:waypoint x="773" y="190" />
        <di:waypoint x="850" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01fzsmy_di" bpmnElement="Flow_01fzsmy">
        <di:waypoint x="550" y="211" />
        <di:waypoint x="550" y="281" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="551" y="243" width="30" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rszro9_di" bpmnElement="Flow_0rszro9">
        <di:waypoint x="270" y="161" />
        <di:waypoint x="270" y="100" />
        <di:waypoint x="670" y="100" />
        <di:waypoint x="670" y="161" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="464" y="82" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
