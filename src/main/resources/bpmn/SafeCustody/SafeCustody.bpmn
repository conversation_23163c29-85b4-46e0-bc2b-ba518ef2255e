<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_0jfkxu9" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="SafeCustody" name="SafeCustody" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="StartEvent_00b5rc6">
      <bpmn:outgoing>SequenceFlow_1f65mfl</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="EndEvent_05s0ocb">
      <bpmn:incoming>SequenceFlow_1nb9m8w</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSSafeCustody" name="BS Safe Custody " camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_1f65mfl</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0q7aulm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1f65mfl" sourceRef="StartEvent_00b5rc6" targetRef="BSSafeCustody" />
    <bpmn:sequenceFlow id="SequenceFlow_0q7aulm" sourceRef="BSSafeCustody" targetRef="ExclusiveGateway_0eqrusu" />
    <bpmn:serviceTask id="BSAddSubscription" name="Addon Activation in Billing System" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_17gj0s5</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0uh0nhz</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0uh0nhz" sourceRef="BSAddSubscription" targetRef="ExclusiveGateway_0elxvjs" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_0eqrusu">
      <bpmn:incoming>SequenceFlow_0q7aulm</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_17gj0s5</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_17cyfwq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_17gj0s5" name=" true" sourceRef="ExclusiveGateway_0eqrusu" targetRef="BSAddSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${subscription=='true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0elxvjs">
      <bpmn:incoming>SequenceFlow_0uh0nhz</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_17cyfwq</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1nb9m8w</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_1nb9m8w" sourceRef="ExclusiveGateway_0elxvjs" targetRef="EndEvent_05s0ocb" />
    <bpmn:sequenceFlow id="SequenceFlow_17cyfwq" name="subscriptions false" sourceRef="ExclusiveGateway_0eqrusu" targetRef="ExclusiveGateway_0elxvjs">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${subscription=='false'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="SafeCustody">
      <bpmndi:BPMNEdge id="SequenceFlow_17cyfwq_di" bpmnElement="SequenceFlow_17cyfwq">
        <di:waypoint x="512" y="158" />
        <di:waypoint x="512" y="99" />
        <di:waypoint x="738" y="99" />
        <di:waypoint x="738" y="158" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="593" y="81" width="65" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1nb9m8w_di" bpmnElement="SequenceFlow_1nb9m8w">
        <di:waypoint x="763" y="183" />
        <di:waypoint x="784" y="183" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_17gj0s5_di" bpmnElement="SequenceFlow_17gj0s5">
        <di:waypoint x="537" y="183" />
        <di:waypoint x="580" y="183" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="551" y="165" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0uh0nhz_di" bpmnElement="SequenceFlow_0uh0nhz">
        <di:waypoint x="680" y="183" />
        <di:waypoint x="713" y="183" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0q7aulm_di" bpmnElement="SequenceFlow_0q7aulm">
        <di:waypoint x="377" y="183" />
        <di:waypoint x="487" y="183" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1f65mfl_di" bpmnElement="SequenceFlow_1f65mfl">
        <di:waypoint x="192" y="183" />
        <di:waypoint x="277" y="183" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="StartEvent_00b5rc6_di" bpmnElement="StartEvent_00b5rc6">
        <dc:Bounds x="156" y="165" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_05s0ocb_di" bpmnElement="EndEvent_05s0ocb">
        <dc:Bounds x="784" y="165" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1jea4er_di" bpmnElement="BSSafeCustody">
        <dc:Bounds x="277" y="143" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1gffqo2_di" bpmnElement="BSAddSubscription">
        <dc:Bounds x="580" y="143" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0eqrusu_di" bpmnElement="ExclusiveGateway_0eqrusu" isMarkerVisible="true">
        <dc:Bounds x="487" y="158" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0elxvjs_di" bpmnElement="ExclusiveGateway_0elxvjs" isMarkerVisible="true">
        <dc:Bounds x="713" y="158" width="50" height="50" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
