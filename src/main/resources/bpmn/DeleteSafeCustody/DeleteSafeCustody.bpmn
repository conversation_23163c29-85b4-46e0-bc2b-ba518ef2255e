<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_0jfkxu9" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.8.0">
  <bpmn:process id="DeleteSafeCustody" name="DeleteSafeCustody" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>SequenceFlow_1f65mfl</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0fv2udk</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSDeleteSafeCustody" name="Billing Delete SafeCustody" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_1f65mfl</bpmn:incoming>
      <bpmn:outgoing>Flow_0aw9w7r</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1f65mfl" sourceRef="orderExecStart" targetRef="BSDeleteSafeCustody" />
    <bpmn:sequenceFlow id="Flow_0aw9w7r" sourceRef="BSDeleteSafeCustody" targetRef="Gateway_0qydmo0" />
    <bpmn:exclusiveGateway id="Gateway_0qydmo0" default="Flow_0nsq6ab">
      <bpmn:incoming>Flow_0aw9w7r</bpmn:incoming>
      <bpmn:outgoing>Flow_0nsq6ab</bpmn:outgoing>
      <bpmn:outgoing>Flow_0qyc808</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0nsq6ab" sourceRef="Gateway_0qydmo0" targetRef="BS_ViewSubscription" />
    <bpmn:exclusiveGateway id="Gateway_08mvf1l" default="Flow_1psoc9x">
      <bpmn:incoming>Flow_04usbav</bpmn:incoming>
      <bpmn:outgoing>Flow_1psoc9x</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ffgxs3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1psoc9x" sourceRef="Gateway_08mvf1l" targetRef="Gateway_0j845ol" />
    <bpmn:serviceTask id="BS_ViewSubscription" name="BS_ViewSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0nsq6ab</bpmn:incoming>
      <bpmn:outgoing>Flow_04usbav</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_04usbav" sourceRef="BS_ViewSubscription" targetRef="Gateway_08mvf1l" />
    <bpmn:serviceTask id="BillingCancelSubscription" name="Billing Cancel Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1fwpnwm</bpmn:incoming>
      <bpmn:outgoing>Flow_10mbndk</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_10mbndk" sourceRef="BillingCancelSubscription" targetRef="Gateway_1ay6ytf" />
    <bpmn:exclusiveGateway id="Gateway_1ay6ytf" default="Flow_0w3hrwe">
      <bpmn:incoming>Flow_10mbndk</bpmn:incoming>
      <bpmn:outgoing>Flow_0w3hrwe</bpmn:outgoing>
      <bpmn:outgoing>Flow_0nmqn87</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0w3hrwe" sourceRef="Gateway_1ay6ytf" targetRef="Gateway_13g7ytu" />
    <bpmn:exclusiveGateway id="Gateway_13g7ytu">
      <bpmn:incoming>Flow_0w3hrwe</bpmn:incoming>
      <bpmn:incoming>Flow_1673oxz</bpmn:incoming>
      <bpmn:outgoing>Flow_0fv2udk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0fv2udk" sourceRef="Gateway_13g7ytu" targetRef="orderExecEnd" />
    <bpmn:endEvent id="Event_1mjev5q">
      <bpmn:incoming>Flow_0qyc808</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_0zsy0sp">
      <bpmn:incoming>Flow_0ffgxs3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_01dz0xv">
      <bpmn:incoming>Flow_0nmqn87</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0qyc808" name="Failure" sourceRef="Gateway_0qydmo0" targetRef="Event_1mjev5q">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ffgxs3" name="Failure" sourceRef="Gateway_08mvf1l" targetRef="Event_0zsy0sp">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0nmqn87" name="Failure" sourceRef="Gateway_1ay6ytf" targetRef="Event_01dz0xv">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0j845ol" default="Flow_1673oxz">
      <bpmn:incoming>Flow_1psoc9x</bpmn:incoming>
      <bpmn:outgoing>Flow_1fwpnwm</bpmn:outgoing>
      <bpmn:outgoing>Flow_1673oxz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1fwpnwm" sourceRef="Gateway_0j845ol" targetRef="BillingCancelSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.workflowData.BS_ViewSubscriptionResponseAttributes").element().hasProp('subscriptions')}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1673oxz" sourceRef="Gateway_0j845ol" targetRef="Gateway_13g7ytu" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="DeleteSafeCustody">
      <bpmndi:BPMNShape id="BPMNShape_0h8b3un" bpmnElement="Gateway_0qydmo0" isMarkerVisible="true">
        <dc:Bounds x="485" y="98" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1p8dj5d" bpmnElement="Gateway_08mvf1l" isMarkerVisible="true">
        <dc:Bounds x="775" y="98" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1tmk5kk" bpmnElement="BS_ViewSubscription">
        <dc:Bounds x="590" y="83" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1mjev5q_di" bpmnElement="Event_1mjev5q">
        <dc:Bounds x="492" y="232" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0zsy0sp_di" bpmnElement="Event_0zsy0sp">
        <dc:Bounds x="782" y="232" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_00b5rc6_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="105" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1jea4er_di" bpmnElement="BSDeleteSafeCustody">
        <dc:Bounds x="300" y="83" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_05s0ocb_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="1432" y="105" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_00ncvvs" bpmnElement="Gateway_13g7ytu" isMarkerVisible="true">
        <dc:Bounds x="1295" y="98" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0961p17" bpmnElement="Gateway_1ay6ytf" isMarkerVisible="true">
        <dc:Bounds x="1165" y="98" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_01dz0xv_di" bpmnElement="Event_01dz0xv">
        <dc:Bounds x="1172" y="232" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_14h98ga" bpmnElement="BillingCancelSubscription">
        <dc:Bounds x="1010" y="83" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0e9gfri" bpmnElement="Gateway_0j845ol" isMarkerVisible="true">
        <dc:Bounds x="885" y="98" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0aw9w7r_di" bpmnElement="Flow_0aw9w7r">
        <di:waypoint x="400" y="123" />
        <di:waypoint x="485" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nsq6ab_di" bpmnElement="Flow_0nsq6ab">
        <di:waypoint x="535" y="123" />
        <di:waypoint x="590" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qyc808_di" bpmnElement="Flow_0qyc808">
        <di:waypoint x="510" y="148" />
        <di:waypoint x="510" y="232" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="508" y="187" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04usbav_di" bpmnElement="Flow_04usbav">
        <di:waypoint x="690" y="123" />
        <di:waypoint x="775" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1psoc9x_di" bpmnElement="Flow_1psoc9x">
        <di:waypoint x="825" y="123" />
        <di:waypoint x="885" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ffgxs3_di" bpmnElement="Flow_0ffgxs3">
        <di:waypoint x="800" y="148" />
        <di:waypoint x="800" y="232" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="798" y="187" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10mbndk_di" bpmnElement="Flow_10mbndk">
        <di:waypoint x="1110" y="123" />
        <di:waypoint x="1165" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fv2udk_di" bpmnElement="Flow_0fv2udk">
        <di:waypoint x="1345" y="123" />
        <di:waypoint x="1432" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0w3hrwe_di" bpmnElement="Flow_0w3hrwe">
        <di:waypoint x="1215" y="123" />
        <di:waypoint x="1295" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nmqn87_di" bpmnElement="Flow_0nmqn87">
        <di:waypoint x="1190" y="148" />
        <di:waypoint x="1190" y="232" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1188" y="187" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1f65mfl_di" bpmnElement="SequenceFlow_1f65mfl">
        <di:waypoint x="188" y="123" />
        <di:waypoint x="300" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fwpnwm_di" bpmnElement="Flow_1fwpnwm">
        <di:waypoint x="935" y="123" />
        <di:waypoint x="1010" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1673oxz_di" bpmnElement="Flow_1673oxz">
        <di:waypoint x="910" y="98" />
        <di:waypoint x="910" y="50" />
        <di:waypoint x="1320" y="50" />
        <di:waypoint x="1320" y="98" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
