<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.21.0">
  <bpmn:process id="UpdateAccountAddress" name="UpdateAccountAddress" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:subProcess id="AccountAddressMultiInstance" name="Account Address multiInstance" camunda:asyncBefore="true">
      <bpmn:incoming>Flow_1iryarj</bpmn:incoming>
      <bpmn:outgoing>Flow_0lyu0ph</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:collection="${workflowData.jsonPath(&#34;$.order.address&#34;).elementList()}" camunda:elementVariable="executionData">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
      <bpmn:serviceTask id="BSUpdateAccountAddress" name="BS Update Account Address" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
        <bpmn:incoming>Flow_08pxwif</bpmn:incoming>
        <bpmn:outgoing>Flow_1jswzrf</bpmn:outgoing>
      </bpmn:serviceTask>
      <bpmn:endEvent id="subProcessEndEvent">
        <bpmn:incoming>Flow_1jswzrf</bpmn:incoming>
      </bpmn:endEvent>
      <bpmn:startEvent id="subProcessStartEvent">
        <bpmn:outgoing>Flow_08pxwif</bpmn:outgoing>
      </bpmn:startEvent>
      <bpmn:sequenceFlow id="Flow_1jswzrf" sourceRef="BSUpdateAccountAddress" targetRef="subProcessEndEvent" />
      <bpmn:sequenceFlow id="Flow_08pxwif" sourceRef="subProcessStartEvent" targetRef="BSUpdateAccountAddress" />
    </bpmn:subProcess>
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1iryarj</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1iryarj" sourceRef="orderExecStart" targetRef="AccountAddressMultiInstance" />
    <bpmn:endEvent id="orderExecEnd" camunda:asyncBefore="true">
      <bpmn:incoming>Flow_0lyu0ph</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0lyu0ph" sourceRef="AccountAddressMultiInstance" targetRef="orderExecEnd" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="UpdateAccountAddress">
      <bpmndi:BPMNShape id="Activity_102huvn_di" bpmnElement="AccountAddressMultiInstance" isExpanded="true">
        <dc:Bounds x="280" y="80" width="380" height="190" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ufdko4_di" bpmnElement="BSUpdateAccountAddress">
        <dc:Bounds x="400" y="130" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_056x0tu_di" bpmnElement="subProcessEndEvent">
        <dc:Bounds x="582" y="152" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0q9vvi0_di" bpmnElement="subProcessStartEvent">
        <dc:Bounds x="302" y="152" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1jswzrf_di" bpmnElement="Flow_1jswzrf">
        <di:waypoint x="500" y="170" />
        <di:waypoint x="582" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08pxwif_di" bpmnElement="Flow_08pxwif">
        <di:waypoint x="338" y="170" />
        <di:waypoint x="400" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_0ux7eel_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="162" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0i2zab7_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="912" y="157" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1iryarj_di" bpmnElement="Flow_1iryarj">
        <di:waypoint x="188" y="180" />
        <di:waypoint x="280" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lyu0ph_di" bpmnElement="Flow_0lyu0ph">
        <di:waypoint x="660" y="175" />
        <di:waypoint x="912" y="175" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
