<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0xt6om3" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.18.0">
  <bpmn:process id="AddService-Rollback" name="AddService-Rollback" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_0aqf7pf</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="EndEvent_17dzzi8">
      <bpmn:incoming>Flow_0g29sdc</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSDeleteService" name="BS Delete Service" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_03p860m</bpmn:incoming>
      <bpmn:outgoing>Flow_1g2eu1k</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="NMSDeleteService" name="NMS Delete Service" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0s4cvz6</bpmn:incoming>
      <bpmn:outgoing>Flow_0x610hc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0x610hc" sourceRef="NMSDeleteService" targetRef="Gateway_1izsp2k" />
    <bpmn:sequenceFlow id="Flow_1g2eu1k" sourceRef="BSDeleteService" targetRef="Gateway_07hjmwi" />
    <bpmn:sequenceFlow id="Flow_0aqf7pf" sourceRef="StartEvent_1" targetRef="Gateway_120anpb" />
    <bpmn:exclusiveGateway id="Gateway_120anpb" default="Flow_0pre1jt">
      <bpmn:incoming>Flow_0aqf7pf</bpmn:incoming>
      <bpmn:outgoing>Flow_0s4cvz6</bpmn:outgoing>
      <bpmn:outgoing>Flow_0pre1jt</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0s4cvz6" sourceRef="Gateway_120anpb" targetRef="NMSDeleteService">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${nms_pair_sim}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1izsp2k">
      <bpmn:incoming>Flow_0x610hc</bpmn:incoming>
      <bpmn:incoming>Flow_0pre1jt</bpmn:incoming>
      <bpmn:outgoing>Flow_1meij9p</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1meij9p" sourceRef="Gateway_1izsp2k" targetRef="Gateway_16w19dr" />
    <bpmn:sequenceFlow id="Flow_0pre1jt" sourceRef="Gateway_120anpb" targetRef="Gateway_1izsp2k" />
    <bpmn:exclusiveGateway id="Gateway_16w19dr" default="Flow_1owahlq">
      <bpmn:incoming>Flow_1meij9p</bpmn:incoming>
      <bpmn:outgoing>Flow_03p860m</bpmn:outgoing>
      <bpmn:outgoing>Flow_1owahlq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_03p860m" sourceRef="Gateway_16w19dr" targetRef="BSDeleteService">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${bs_service_creation}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_07hjmwi">
      <bpmn:incoming>Flow_1g2eu1k</bpmn:incoming>
      <bpmn:incoming>Flow_1owahlq</bpmn:incoming>
      <bpmn:outgoing>Flow_1cga052</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1cga052" sourceRef="Gateway_07hjmwi" targetRef="Gateway_1ibbr0g" />
    <bpmn:sequenceFlow id="Flow_1owahlq" sourceRef="Gateway_16w19dr" targetRef="Gateway_07hjmwi" />
    <bpmn:sequenceFlow id="Flow_18byfua" sourceRef="NMSUnblockSim" targetRef="Gateway_1cl5dzh" />
    <bpmn:exclusiveGateway id="Gateway_1ibbr0g" default="Flow_0al6h5r">
      <bpmn:incoming>Flow_1cga052</bpmn:incoming>
      <bpmn:outgoing>Flow_0mv6iy7</bpmn:outgoing>
      <bpmn:outgoing>Flow_0al6h5r</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0mv6iy7" sourceRef="Gateway_1ibbr0g" targetRef="NMSUnblockSim">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${nms_block_asset}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1cl5dzh">
      <bpmn:incoming>Flow_18byfua</bpmn:incoming>
      <bpmn:incoming>Flow_0al6h5r</bpmn:incoming>
      <bpmn:outgoing>Flow_0g29sdc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0g29sdc" sourceRef="Gateway_1cl5dzh" targetRef="EndEvent_17dzzi8" />
    <bpmn:sequenceFlow id="Flow_0al6h5r" sourceRef="Gateway_1ibbr0g" targetRef="Gateway_1cl5dzh" />
    <bpmn:serviceTask id="NMSUnblockSim" name="NMS Unblock Sim" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0mv6iy7</bpmn:incoming>
      <bpmn:outgoing>Flow_18byfua</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="AddService-Rollback">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="152" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_19lgt4b_di" bpmnElement="BSDeleteService">
        <dc:Bounds x="820" y="130" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ats9yu_di" bpmnElement="NMSDeleteService">
        <dc:Bounds x="410" y="130" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_120anpb_di" bpmnElement="Gateway_120anpb" isMarkerVisible="true">
        <dc:Bounds x="245" y="145" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1izsp2k_di" bpmnElement="Gateway_1izsp2k" isMarkerVisible="true">
        <dc:Bounds x="575" y="145" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_16w19dr_di" bpmnElement="Gateway_16w19dr" isMarkerVisible="true">
        <dc:Bounds x="685" y="145" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_07hjmwi_di" bpmnElement="Gateway_07hjmwi" isMarkerVisible="true">
        <dc:Bounds x="975" y="145" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_17dzzi8_di" bpmnElement="EndEvent_17dzzi8">
        <dc:Bounds x="1492" y="152" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ibbr0g_di" bpmnElement="Gateway_1ibbr0g" isMarkerVisible="true">
        <dc:Bounds x="1085" y="145" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1cl5dzh_di" bpmnElement="Gateway_1cl5dzh" isMarkerVisible="true">
        <dc:Bounds x="1385" y="145" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o98iot_di" bpmnElement="NMSUnblockSim">
        <dc:Bounds x="1210" y="130" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0x610hc_di" bpmnElement="Flow_0x610hc">
        <di:waypoint x="510" y="170" />
        <di:waypoint x="575" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g2eu1k_di" bpmnElement="Flow_1g2eu1k">
        <di:waypoint x="920" y="170" />
        <di:waypoint x="975" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0aqf7pf_di" bpmnElement="Flow_0aqf7pf">
        <di:waypoint x="188" y="170" />
        <di:waypoint x="245" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0s4cvz6_di" bpmnElement="Flow_0s4cvz6">
        <di:waypoint x="295" y="170" />
        <di:waypoint x="410" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1meij9p_di" bpmnElement="Flow_1meij9p">
        <di:waypoint x="625" y="170" />
        <di:waypoint x="685" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pre1jt_di" bpmnElement="Flow_0pre1jt">
        <di:waypoint x="270" y="145" />
        <di:waypoint x="270" y="80" />
        <di:waypoint x="600" y="80" />
        <di:waypoint x="600" y="145" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03p860m_di" bpmnElement="Flow_03p860m">
        <di:waypoint x="735" y="170" />
        <di:waypoint x="820" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cga052_di" bpmnElement="Flow_1cga052">
        <di:waypoint x="1025" y="170" />
        <di:waypoint x="1085" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1owahlq_di" bpmnElement="Flow_1owahlq">
        <di:waypoint x="710" y="145" />
        <di:waypoint x="710" y="70" />
        <di:waypoint x="1000" y="70" />
        <di:waypoint x="1000" y="145" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18byfua_di" bpmnElement="Flow_18byfua">
        <di:waypoint x="1310" y="170" />
        <di:waypoint x="1385" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mv6iy7_di" bpmnElement="Flow_0mv6iy7">
        <di:waypoint x="1135" y="170" />
        <di:waypoint x="1210" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0g29sdc_di" bpmnElement="Flow_0g29sdc">
        <di:waypoint x="1435" y="170" />
        <di:waypoint x="1492" y="170" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0al6h5r_di" bpmnElement="Flow_0al6h5r">
        <di:waypoint x="1110" y="145" />
        <di:waypoint x="1110" y="70" />
        <di:waypoint x="1410" y="70" />
        <di:waypoint x="1410" y="145" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
