<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.15.1">
  <bpmn:process id="CreateProfile" name="CreateProfile" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0ywwsm3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1qak9sg</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0ywwsm3" sourceRef="orderExecStart" targetRef="BSCreateProfile" />
    <bpmn:endEvent id="Event_1ghzk6c" name="failure">
      <bpmn:incoming>Flow_1fjd0n1</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1fjd0n1" name="Failure" sourceRef="Gateway_0swy6z2" targetRef="Event_1ghzk6c">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSCreateProfile" name="Create Customer Profile In Party Management" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0ywwsm3</bpmn:incoming>
      <bpmn:outgoing>Flow_1uydk5s</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1uydk5s" sourceRef="BSCreateProfile" targetRef="Gateway_0swy6z2" />
    <bpmn:exclusiveGateway id="Gateway_0swy6z2" default="Flow_1qak9sg">
      <bpmn:incoming>Flow_1uydk5s</bpmn:incoming>
      <bpmn:outgoing>Flow_1fjd0n1</bpmn:outgoing>
      <bpmn:outgoing>Flow_1qak9sg</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1qak9sg" name="Success" sourceRef="Gateway_0swy6z2" targetRef="orderExecEnd" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CreateProfile">
      <bpmndi:BPMNShape id="Event_1x7dsnm_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ghzk6c_di" bpmnElement="Event_1ghzk6c">
        <dc:Bounds x="522" y="242" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="526" y="285" width="29" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1madche_di" bpmnElement="BSCreateProfile">
        <dc:Bounds x="300" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0swy6z2_di" bpmnElement="Gateway_0swy6z2" isMarkerVisible="true">
        <dc:Bounds x="515" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1p88c2w_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="672" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0ywwsm3_di" bpmnElement="Flow_0ywwsm3">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="300" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fjd0n1_di" bpmnElement="Flow_1fjd0n1">
        <di:waypoint x="540" y="145" />
        <di:waypoint x="540" y="242" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="553" y="191" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uydk5s_di" bpmnElement="Flow_1uydk5s">
        <di:waypoint x="400" y="120" />
        <di:waypoint x="515" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qak9sg_di" bpmnElement="Flow_1qak9sg">
        <di:waypoint x="565" y="120" />
        <di:waypoint x="672" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="598" y="102" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
