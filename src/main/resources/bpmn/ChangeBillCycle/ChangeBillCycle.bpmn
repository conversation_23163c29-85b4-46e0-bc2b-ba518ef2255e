<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="ChangeBillCycle" name="ChangeBillCycle" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0x1k7xo</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="BSChangeBillCycle" name="BS ChangeBillCycle" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0x1k7xo</bpmn:incoming>
      <bpmn:outgoing>Flow_1c4dv1s</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1sgrebq</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1c4dv1s" sourceRef="BSChangeBillCycle" targetRef="Gateway_1wn3zzr" />
    <bpmn:serviceTask id="NCCChangeBillCycle" name="ChangeBillCycle in NCC" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0szwro6</bpmn:incoming>
      <bpmn:outgoing>Flow_1sgrebq</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1sgrebq" sourceRef="NCCChangeBillCycle" targetRef="orderExecEnd" />
    <bpmn:exclusiveGateway id="Gateway_1wn3zzr">
      <bpmn:incoming>Flow_1c4dv1s</bpmn:incoming>
      <bpmn:outgoing>Flow_0szwro6</bpmn:outgoing>
      <bpmn:outgoing>Flow_12nf3pn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0szwro6" name="Success" sourceRef="Gateway_1wn3zzr" targetRef="NCCChangeBillCycle">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_0lz6yyv">
      <bpmn:incoming>Flow_12nf3pn</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_12nf3pn" name="Failure" sourceRef="Gateway_1wn3zzr" targetRef="Event_0lz6yyv">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0x1k7xo" sourceRef="orderExecStart" targetRef="BSChangeBillCycle" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ChangeBillCycle">
      <bpmndi:BPMNEdge id="Flow_0x1k7xo_di" bpmnElement="Flow_0x1k7xo">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="260" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12nf3pn_di" bpmnElement="Flow_12nf3pn">
        <di:waypoint x="500" y="145" />
        <di:waypoint x="500" y="222" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="498" y="181" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0szwro6_di" bpmnElement="Flow_0szwro6">
        <di:waypoint x="525" y="120" />
        <di:waypoint x="650" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="567" y="102" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sgrebq_di" bpmnElement="Flow_1sgrebq">
        <di:waypoint x="750" y="120" />
        <di:waypoint x="852" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c4dv1s_di" bpmnElement="Flow_1c4dv1s">
        <di:waypoint x="360" y="120" />
        <di:waypoint x="475" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Event_1x7dsnm_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ngn6ow_di" bpmnElement="BSChangeBillCycle">
        <dc:Bounds x="260" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1p88c2w_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="852" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_09qinuw_di" bpmnElement="NCCChangeBillCycle">
        <dc:Bounds x="650" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1wn3zzr_di" bpmnElement="Gateway_1wn3zzr" isMarkerVisible="true">
        <dc:Bounds x="475" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0lz6yyv_di" bpmnElement="Event_0lz6yyv">
        <dc:Bounds x="482" y="222" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
