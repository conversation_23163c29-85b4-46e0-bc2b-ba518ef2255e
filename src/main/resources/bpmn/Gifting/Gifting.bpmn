<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1bg6vh8" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.28.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.20.0">
  <bpmn:process id="Gifting" name="Gifting" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}" camunda:historyTimeToLive="180">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_07u4mb8</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_010mo1i</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="OCScreategift" name="OCS Create Gift" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0a4xiwp</bpmn:incoming>
      <bpmn:outgoing>Flow_1wdm9g7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_13b4iqq" sourceRef="BSAddSubscription" targetRef="Gateway_0272ek7" />
    <bpmn:sequenceFlow id="Flow_0p2751f" sourceRef="OCSAddSubscription" targetRef="Gateway_163sm5b" />
    <bpmn:exclusiveGateway id="Gateway_0272ek7" default="Flow_1lqbw08">
      <bpmn:incoming>Flow_13b4iqq</bpmn:incoming>
      <bpmn:outgoing>Flow_1iaczsh</bpmn:outgoing>
      <bpmn:outgoing>Flow_1lqbw08</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1oh0zjj">
      <bpmn:incoming>Flow_1iaczsh</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1iaczsh" sourceRef="Gateway_0272ek7" targetRef="Event_1oh0zjj">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_163sm5b" default="Flow_0a4xiwp">
      <bpmn:incoming>Flow_0p2751f</bpmn:incoming>
      <bpmn:outgoing>Flow_0a4xiwp</bpmn:outgoing>
      <bpmn:outgoing>Flow_032ub60</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0a4xiwp" sourceRef="Gateway_163sm5b" targetRef="OCScreategift" />
    <bpmn:endEvent id="Event_0q121c0">
      <bpmn:incoming>Flow_032ub60</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_032ub60" sourceRef="Gateway_163sm5b" targetRef="Event_0q121c0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="OCSAddSubscription" name="Add Subscription in OCS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1lqbw08</bpmn:incoming>
      <bpmn:outgoing>Flow_0p2751f</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSAddSubscription" name="Add Subscription in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0ki8ogo</bpmn:incoming>
      <bpmn:outgoing>Flow_13b4iqq</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1uo3q4k" default="Flow_0ki8ogo">
      <bpmn:incoming>Flow_07u4mb8</bpmn:incoming>
      <bpmn:outgoing>Flow_0ki8ogo</bpmn:outgoing>
      <bpmn:outgoing>Flow_1haf33i</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_07u4mb8" sourceRef="orderExecStart" targetRef="Gateway_1uo3q4k" />
    <bpmn:sequenceFlow id="Flow_0ki8ogo" sourceRef="Gateway_1uo3q4k" targetRef="BSAddSubscription" />
    <bpmn:exclusiveGateway id="Gateway_1nposlj" default="Flow_08rgyra">
      <bpmn:incoming>Flow_1wdm9g7</bpmn:incoming>
      <bpmn:outgoing>Flow_1qsibgo</bpmn:outgoing>
      <bpmn:outgoing>Flow_08rgyra</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1wdm9g7" sourceRef="OCScreategift" targetRef="Gateway_1nposlj" />
    <bpmn:endEvent id="Event_05kh3qz">
      <bpmn:incoming>Flow_1qsibgo</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1qsibgo" sourceRef="Gateway_1nposlj" targetRef="Event_05kh3qz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1v8mxna">
      <bpmn:incoming>Flow_1haf33i</bpmn:incoming>
      <bpmn:incoming>Flow_0650eh3</bpmn:incoming>
      <bpmn:outgoing>Flow_010mo1i</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_08rgyra" sourceRef="Gateway_1nposlj" targetRef="Gateway_057f3vc" />
    <bpmn:sequenceFlow id="Flow_010mo1i" sourceRef="Gateway_1v8mxna" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_1lqbw08" sourceRef="Gateway_0272ek7" targetRef="OCSAddSubscription" />
    <bpmn:sequenceFlow id="Flow_1haf33i" sourceRef="Gateway_1uo3q4k" targetRef="Gateway_1v8mxna">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isLegacy}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_057f3vc" name="IsEkycCallreqd =true" default="Flow_1w2oeyy">
      <bpmn:incoming>Flow_08rgyra</bpmn:incoming>
      <bpmn:outgoing>Flow_1m054ra</bpmn:outgoing>
      <bpmn:outgoing>Flow_1w2oeyy</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1m054ra" sourceRef="Gateway_057f3vc" targetRef="ESBEkycUpdate">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order").element().hasProp('orderItem')&amp;&amp; workflowData.jsonPath("$.order.orderItem[0].productOffering").element().hasProp('isKycRequired') &amp;&amp; workflowData.jsonPath("$.order.orderItem[0].productOffering.isKycRequired").stringValue() == 'true' }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0xhcwn5">
      <bpmn:incoming>Flow_15ydl03</bpmn:incoming>
      <bpmn:incoming>Flow_1w2oeyy</bpmn:incoming>
      <bpmn:outgoing>Flow_0650eh3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0650eh3" sourceRef="Gateway_0xhcwn5" targetRef="Gateway_1v8mxna" />
    <bpmn:exclusiveGateway id="Gateway_0m3dmcf" default="Flow_15ydl03">
      <bpmn:incoming>Flow_10u0i50</bpmn:incoming>
      <bpmn:outgoing>Flow_15ydl03</bpmn:outgoing>
      <bpmn:outgoing>Flow_1sx447d</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_15ydl03" sourceRef="Gateway_0m3dmcf" targetRef="Gateway_0xhcwn5" />
    <bpmn:serviceTask id="ESBEkycUpdate" name="ESB_EkycUpdate" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1m054ra</bpmn:incoming>
      <bpmn:outgoing>Flow_10u0i50</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_10u0i50" sourceRef="ESBEkycUpdate" targetRef="Gateway_0m3dmcf" />
    <bpmn:endEvent id="Event_1dytyts">
      <bpmn:incoming>Flow_1sx447d</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1sx447d" name="Failure" sourceRef="Gateway_0m3dmcf" targetRef="Event_1dytyts">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1w2oeyy" sourceRef="Gateway_057f3vc" targetRef="Gateway_0xhcwn5" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Gifting">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="279" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_02n3zq0_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2172" y="279" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1018g41_di" bpmnElement="OCScreategift">
        <dc:Bounds x="1200" y="257" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0272ek7_di" bpmnElement="Gateway_0272ek7" isMarkerVisible="true">
        <dc:Bounds x="575" y="272" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1oh0zjj_di" bpmnElement="Event_1oh0zjj">
        <dc:Bounds x="582" y="382" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_163sm5b_di" bpmnElement="Gateway_163sm5b" isMarkerVisible="true">
        <dc:Bounds x="1015" y="272" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0q121c0_di" bpmnElement="Event_0q121c0">
        <dc:Bounds x="1022" y="382" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_08yzuei_di" bpmnElement="OCSAddSubscription">
        <dc:Bounds x="770" y="257" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0leyqsj_di" bpmnElement="BSAddSubscription">
        <dc:Bounds x="360" y="257" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1uo3q4k_di" bpmnElement="Gateway_1uo3q4k" isMarkerVisible="true">
        <dc:Bounds x="225" y="272" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1nposlj_di" bpmnElement="Gateway_1nposlj" isMarkerVisible="true">
        <dc:Bounds x="1345" y="272" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_05kh3qz_di" bpmnElement="Event_05kh3qz">
        <dc:Bounds x="1352" y="392" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1v8mxna_di" bpmnElement="Gateway_1v8mxna" isMarkerVisible="true">
        <dc:Bounds x="2075" y="272" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0jfd7fj" bpmnElement="Gateway_057f3vc" isMarkerVisible="true">
        <dc:Bounds x="1485" y="272" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1473" y="329" width="74" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0c9spew" bpmnElement="Gateway_0xhcwn5" isMarkerVisible="true">
        <dc:Bounds x="1965" y="272" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0wvww7g" bpmnElement="Gateway_0m3dmcf" isMarkerVisible="true">
        <dc:Bounds x="1795" y="272" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0maso2n" bpmnElement="ESBEkycUpdate">
        <dc:Bounds x="1620" y="257" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_111hwyj" bpmnElement="Event_1dytyts">
        <dc:Bounds x="1802" y="402" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_13b4iqq_di" bpmnElement="Flow_13b4iqq">
        <di:waypoint x="460" y="297" />
        <di:waypoint x="575" y="297" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p2751f_di" bpmnElement="Flow_0p2751f">
        <di:waypoint x="870" y="297" />
        <di:waypoint x="1015" y="297" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1iaczsh_di" bpmnElement="Flow_1iaczsh">
        <di:waypoint x="600" y="322" />
        <di:waypoint x="600" y="382" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a4xiwp_di" bpmnElement="Flow_0a4xiwp">
        <di:waypoint x="1065" y="297" />
        <di:waypoint x="1200" y="297" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_032ub60_di" bpmnElement="Flow_032ub60">
        <di:waypoint x="1040" y="322" />
        <di:waypoint x="1040" y="382" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07u4mb8_di" bpmnElement="Flow_07u4mb8">
        <di:waypoint x="188" y="297" />
        <di:waypoint x="225" y="297" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ki8ogo_di" bpmnElement="Flow_0ki8ogo">
        <di:waypoint x="275" y="297" />
        <di:waypoint x="360" y="297" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wdm9g7_di" bpmnElement="Flow_1wdm9g7">
        <di:waypoint x="1300" y="297" />
        <di:waypoint x="1345" y="297" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qsibgo_di" bpmnElement="Flow_1qsibgo">
        <di:waypoint x="1370" y="322" />
        <di:waypoint x="1370" y="392" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08rgyra_di" bpmnElement="Flow_08rgyra">
        <di:waypoint x="1395" y="297" />
        <di:waypoint x="1485" y="297" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_010mo1i_di" bpmnElement="Flow_010mo1i">
        <di:waypoint x="2125" y="297" />
        <di:waypoint x="2172" y="297" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lqbw08_di" bpmnElement="Flow_1lqbw08">
        <di:waypoint x="625" y="297" />
        <di:waypoint x="770" y="297" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1haf33i_di" bpmnElement="Flow_1haf33i">
        <di:waypoint x="250" y="272" />
        <di:waypoint x="250" y="80" />
        <di:waypoint x="2100" y="80" />
        <di:waypoint x="2100" y="272" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m054ra_di" bpmnElement="Flow_1m054ra">
        <di:waypoint x="1535" y="297" />
        <di:waypoint x="1620" y="297" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0650eh3_di" bpmnElement="Flow_0650eh3">
        <di:waypoint x="2015" y="297" />
        <di:waypoint x="2075" y="297" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15ydl03_di" bpmnElement="Flow_15ydl03">
        <di:waypoint x="1845" y="297" />
        <di:waypoint x="1965" y="297" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10u0i50_di" bpmnElement="Flow_10u0i50">
        <di:waypoint x="1720" y="297" />
        <di:waypoint x="1795" y="297" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sx447d_di" bpmnElement="Flow_1sx447d">
        <di:waypoint x="1820" y="322" />
        <di:waypoint x="1820" y="402" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1818" y="359" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w2oeyy_di" bpmnElement="Flow_1w2oeyy">
        <di:waypoint x="1510" y="272" />
        <di:waypoint x="1510" y="180" />
        <di:waypoint x="1990" y="180" />
        <di:waypoint x="1990" y="272" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
