<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_18pr9mp" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.18.0">
  <bpmn:process id="Onboarding-ServiceProvisioningbkpold" name="Onboarding-ServiceProvisioning" isExecutable="true" camunda:versionTag="${version}">
    <bpmn:serviceTask id="BSCreateService" name="Service Creation In Billing System" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1gfcf4s</bpmn:incoming>
      <bpmn:outgoing>Flow_13uxqks</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_13uxqks" sourceRef="BSCreateService" targetRef="Gateway_1meylc0" />
    <bpmn:exclusiveGateway id="Gateway_1meylc0" default="Flow_1dm1yfp">
      <bpmn:incoming>Flow_13uxqks</bpmn:incoming>
      <bpmn:outgoing>Flow_0xpvbhd</bpmn:outgoing>
      <bpmn:outgoing>Flow_1dm1yfp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0rw1vei">
      <bpmn:incoming>Flow_0xpvbhd</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0xpvbhd" name="Failure" sourceRef="Gateway_1meylc0" targetRef="Event_0rw1vei">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="SimDeliveryCallback" name="SimDelivery Callback " camunda:asyncBefore="true" messageRef="Message_1y1dj88">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ccm4fi</bpmn:incoming>
      <bpmn:outgoing>Flow_04ihxlw</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_1vr41hn" default="Flow_1ojsgty">
      <bpmn:incoming>Flow_04ihxlw</bpmn:incoming>
      <bpmn:outgoing>Flow_009rhwp</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ojsgty</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0yq91sr">
      <bpmn:incoming>Flow_009rhwp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_04ihxlw" sourceRef="SimDeliveryCallback" targetRef="Gateway_1vr41hn" />
    <bpmn:sequenceFlow id="Flow_009rhwp" name="Failure" sourceRef="Gateway_1vr41hn" targetRef="Event_0yq91sr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ojsgty" sourceRef="Gateway_1vr41hn" targetRef="PairSimInNMS" />
    <bpmn:serviceTask id="PairSimInNMS" name="PairSimInNMS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1ojsgty</bpmn:incoming>
      <bpmn:outgoing>Flow_1sc55va</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0no9d0g" default="Flow_0f4sioz">
      <bpmn:incoming>Flow_1sc55va</bpmn:incoming>
      <bpmn:outgoing>Flow_0f4sioz</bpmn:outgoing>
      <bpmn:outgoing>Flow_12cj440</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1sc55va" sourceRef="PairSimInNMS" targetRef="Gateway_0no9d0g" />
    <bpmn:sequenceFlow id="Flow_0f4sioz" sourceRef="Gateway_0no9d0g" targetRef="BS_ChangeSim" />
    <bpmn:exclusiveGateway id="Gateway_0khxihp" default="Flow_1fx3v49">
      <bpmn:incoming>Flow_0i6dkky</bpmn:incoming>
      <bpmn:outgoing>Flow_05m43wr</bpmn:outgoing>
      <bpmn:outgoing>Flow_1fx3v49</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0i6dkky" sourceRef="BS_ChangeSim" targetRef="Gateway_0khxihp" />
    <bpmn:exclusiveGateway id="Gateway_0dojsdb" default="Flow_0twpfne">
      <bpmn:incoming>Flow_1s0pp0i</bpmn:incoming>
      <bpmn:outgoing>Flow_01h7mlf</bpmn:outgoing>
      <bpmn:outgoing>Flow_0twpfne</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0plysyj">
      <bpmn:incoming>Flow_12cj440</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_12cj440" name="Failure" sourceRef="Gateway_0no9d0g" targetRef="Event_0plysyj">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1bvqa9u">
      <bpmn:incoming>Flow_05m43wr</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_05m43wr" name="Failure" sourceRef="Gateway_0khxihp" targetRef="Event_1bvqa9u">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_0iby9cr">
      <bpmn:incoming>Flow_01h7mlf</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_01h7mlf" name="Failure" sourceRef="Gateway_0dojsdb" targetRef="Event_0iby9cr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BS_ChangeSim" name="Billing Change Sim" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0f4sioz</bpmn:incoming>
      <bpmn:outgoing>Flow_0i6dkky</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1s98h1x" default="Flow_1rpq9ua">
      <bpmn:incoming>Flow_1k39dhe</bpmn:incoming>
      <bpmn:outgoing>Flow_1rpq9ua</bpmn:outgoing>
      <bpmn:outgoing>Flow_09ojkbr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1rpq9ua" sourceRef="Gateway_1s98h1x" targetRef="OCSOfferActivationProcess" />
    <bpmn:exclusiveGateway id="Gateway_0li77bm" default="Flow_13m76f2">
      <bpmn:incoming>Flow_1qg9rbz</bpmn:incoming>
      <bpmn:outgoing>Flow_145yh0o</bpmn:outgoing>
      <bpmn:outgoing>Flow_13m76f2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1m499j7" default="Flow_1lj84w5">
      <bpmn:incoming>Flow_161bbrt</bpmn:incoming>
      <bpmn:outgoing>Flow_10nxq9p</bpmn:outgoing>
      <bpmn:outgoing>Flow_1lj84w5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_161bbrt" sourceRef="BSAddSubscription" targetRef="Gateway_1m499j7" />
    <bpmn:endEvent id="subProcessEndEvent">
      <bpmn:incoming>Flow_1rlvo2k</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSAddSubscription" name="BSAddSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="${subscriptionChargeModifier}" event="start" />
        <camunda:executionListener delegateExpression="${subscriptionChargeModifier}" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0m1g3lk</bpmn:incoming>
      <bpmn:outgoing>Flow_161bbrt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSActivateService" name="BSActivateService" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_13m76f2</bpmn:incoming>
      <bpmn:outgoing>Flow_0667v8w</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1mu71an">
      <bpmn:incoming>Flow_09ojkbr</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_09ojkbr" name="Failure" sourceRef="Gateway_1s98h1x" targetRef="Event_1mu71an">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_0xlwtxa">
      <bpmn:incoming>Flow_145yh0o</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_145yh0o" name="Failure" sourceRef="Gateway_0li77bm" targetRef="Event_0xlwtxa">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1vihaa4">
      <bpmn:incoming>Flow_10nxq9p</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_10nxq9p" name="Failure" sourceRef="Gateway_1m499j7" targetRef="Event_1vihaa4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1b4c33j" default="Flow_1rlvo2k">
      <bpmn:incoming>Flow_0667v8w</bpmn:incoming>
      <bpmn:outgoing>Flow_02z6pdv</bpmn:outgoing>
      <bpmn:outgoing>Flow_1rlvo2k</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0667v8w" sourceRef="BSActivateService" targetRef="Gateway_1b4c33j" />
    <bpmn:endEvent id="Event_0okg50j">
      <bpmn:incoming>Flow_02z6pdv</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_02z6pdv" name="Failure" sourceRef="Gateway_1b4c33j" targetRef="Event_0okg50j">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="SOMCallback" name="SOM Provisioning Callback " camunda:asyncBefore="true" messageRef="Message_1lbkhkd">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOM_Provisioning</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOM_Provisioning" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1pjlej8</bpmn:incoming>
      <bpmn:outgoing>Flow_1s0pp0i</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_1s0pp0i" sourceRef="SOMCallback" targetRef="Gateway_0dojsdb" />
    <bpmn:sequenceFlow id="Flow_0twpfne" sourceRef="Gateway_0dojsdb" targetRef="ESBCreateSubscriber" />
    <bpmn:exclusiveGateway id="Gateway_03y9xye" default="Flow_1pjlej8">
      <bpmn:incoming>Flow_02dzn3n</bpmn:incoming>
      <bpmn:outgoing>Flow_1pjlej8</bpmn:outgoing>
      <bpmn:outgoing>Flow_00t33jz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_02dzn3n" sourceRef="SOM_Provisioning" targetRef="Gateway_03y9xye" />
    <bpmn:sequenceFlow id="Flow_1pjlej8" sourceRef="Gateway_03y9xye" targetRef="SOMCallback" />
    <bpmn:serviceTask id="SOM_Provisioning" name="SOM_Provisioning" camunda:asyncBefore="true" camunda:delegateExpression="${somServiceOrderCreation}">
      <bpmn:incoming>Flow_1lj84w5</bpmn:incoming>
      <bpmn:outgoing>Flow_02dzn3n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1lilan5">
      <bpmn:incoming>Flow_00t33jz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_00t33jz" name="Failure" sourceRef="Gateway_03y9xye" targetRef="Event_1lilan5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:startEvent id="start">
      <bpmn:outgoing>Flow_1gfcf4s</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_06972gh" default="Flow_0bnkzcu">
      <bpmn:incoming>Flow_1dm1yfp</bpmn:incoming>
      <bpmn:outgoing>Flow_0ccm4fi</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bnkzcu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ccm4fi" name="simDelivery = true" sourceRef="Gateway_06972gh" targetRef="SimDeliveryCallback">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${simDeliveryCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1dm1yfp" sourceRef="Gateway_1meylc0" targetRef="Gateway_06972gh" />
    <bpmn:sequenceFlow id="Flow_0bnkzcu" sourceRef="Gateway_06972gh" targetRef="Gateway_0occvxv" />
    <bpmn:sequenceFlow id="Flow_1rlvo2k" sourceRef="Gateway_1b4c33j" targetRef="subProcessEndEvent" />
    <bpmn:sequenceFlow id="Flow_13m76f2" sourceRef="Gateway_0li77bm" targetRef="BSActivateService" />
    <bpmn:sequenceFlow id="Flow_1lj84w5" sourceRef="Gateway_1m499j7" targetRef="SOM_Provisioning" />
    <bpmn:sequenceFlow id="Flow_1fx3v49" sourceRef="Gateway_0khxihp" targetRef="Gateway_0occvxv" />
    <bpmn:serviceTask id="ESBCreateSubscriber" name="Service Creation in OCS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0twpfne</bpmn:incoming>
      <bpmn:outgoing>Flow_1k39dhe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1k39dhe" sourceRef="ESBCreateSubscriber" targetRef="Gateway_1s98h1x" />
    <bpmn:callActivity id="OCSOfferActivationProcess" name="OCS Offer Activation Process" camunda:asyncBefore="true" calledElement="OCSOfferActivationProcess" camunda:calledElementBinding="deployment">
      <bpmn:documentation>Iterate over addon subs in BS Add subs response list. planType  1 is base, 0 is addon</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:out variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1rpq9ua</bpmn:incoming>
      <bpmn:outgoing>Flow_1qg9rbz</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:asyncBefore="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.currentExecution.BSAddSubscriptionResponseAttributes.subscriptions[?(@.planType==&#39;0&#39;)]&#34;).elementList()}" camunda:elementVariable="execution">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_1qg9rbz" sourceRef="OCSOfferActivationProcess" targetRef="Gateway_0li77bm" />
    <bpmn:exclusiveGateway id="Gateway_0occvxv">
      <bpmn:incoming>Flow_1fx3v49</bpmn:incoming>
      <bpmn:incoming>Flow_0bnkzcu</bpmn:incoming>
      <bpmn:outgoing>Flow_0m1g3lk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0m1g3lk" sourceRef="Gateway_0occvxv" targetRef="BSAddSubscription" />
    <bpmn:sequenceFlow id="Flow_1gfcf4s" sourceRef="start" targetRef="BSCreateService" />
  </bpmn:process>
  <bpmn:message id="Message_1m2g4r3" name="FlowOneCallback" />
  <bpmn:message id="Message_1y1dj88" name="SimDeliveryCallback" />
  <bpmn:message id="Message_1lbkhkd" name="SOMCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Onboarding-ServiceProvisioningbkpold">
      <bpmndi:BPMNShape id="Activity_1ff1mxo_di" bpmnElement="BSCreateService">
        <dc:Bounds x="610" y="156" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1meylc0_di" bpmnElement="Gateway_1meylc0" isMarkerVisible="true">
        <dc:Bounds x="805" y="171" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0rw1vei_di" bpmnElement="Event_0rw1vei">
        <dc:Bounds x="812" y="328" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_03l5qgl_di" bpmnElement="SimDeliveryCallback">
        <dc:Bounds x="1150" y="156" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1vr41hn_di" bpmnElement="Gateway_1vr41hn" isMarkerVisible="true">
        <dc:Bounds x="1365" y="171" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0yq91sr_di" bpmnElement="Event_0yq91sr">
        <dc:Bounds x="1372" y="348" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0s62pnk" bpmnElement="PairSimInNMS">
        <dc:Bounds x="1600" y="156" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0no9d0g_di" bpmnElement="Gateway_0no9d0g" isMarkerVisible="true">
        <dc:Bounds x="1805" y="171" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0khxihp_di" bpmnElement="Gateway_0khxihp" isMarkerVisible="true">
        <dc:Bounds x="2205" y="171" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0dojsdb_di" bpmnElement="Gateway_0dojsdb" isMarkerVisible="true">
        <dc:Bounds x="3225" y="171" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0plysyj_di" bpmnElement="Event_0plysyj">
        <dc:Bounds x="1812" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1bvqa9u_di" bpmnElement="Event_1bvqa9u">
        <dc:Bounds x="2212" y="348" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0iby9cr_di" bpmnElement="Event_0iby9cr">
        <dc:Bounds x="3232" y="348" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1wdmwsk_di" bpmnElement="BS_ChangeSim">
        <dc:Bounds x="2030" y="156" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1s98h1x_di" bpmnElement="Gateway_1s98h1x" isMarkerVisible="true">
        <dc:Bounds x="3535" y="171" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0li77bm_di" bpmnElement="Gateway_0li77bm" isMarkerVisible="true">
        <dc:Bounds x="3835" y="171" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1m499j7_di" bpmnElement="Gateway_1m499j7" isMarkerVisible="true">
        <dc:Bounds x="2585" y="171" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1lvyzky_di" bpmnElement="subProcessEndEvent">
        <dc:Bounds x="4272" y="178" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1loua6q_di" bpmnElement="BSAddSubscription">
        <dc:Bounds x="2410" y="156" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0xipuav_di" bpmnElement="BSActivateService">
        <dc:Bounds x="3980" y="156" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1mu71an_di" bpmnElement="Event_1mu71an">
        <dc:Bounds x="3542" y="348" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0xlwtxa_di" bpmnElement="Event_0xlwtxa">
        <dc:Bounds x="3842" y="348" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1vihaa4_di" bpmnElement="Event_1vihaa4">
        <dc:Bounds x="2592" y="362" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1b4c33j_di" bpmnElement="Gateway_1b4c33j" isMarkerVisible="true">
        <dc:Bounds x="4135" y="171" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0okg50j_di" bpmnElement="Event_0okg50j">
        <dc:Bounds x="4142" y="362" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ttj65s" bpmnElement="SOMCallback">
        <dc:Bounds x="3070" y="156" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_03y9xye_di" bpmnElement="Gateway_03y9xye" isMarkerVisible="true">
        <dc:Bounds x="2935" y="171" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1b0icqf_di" bpmnElement="SOM_Provisioning">
        <dc:Bounds x="2720" y="156" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1lilan5_di" bpmnElement="Event_1lilan5">
        <dc:Bounds x="2942" y="362" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_06972gh_di" bpmnElement="Gateway_06972gh" isMarkerVisible="true">
        <dc:Bounds x="985" y="171" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16rvaj0" bpmnElement="ESBCreateSubscriber">
        <dc:Bounds x="3360" y="156" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rkhdmw" bpmnElement="OCSOfferActivationProcess">
        <dc:Bounds x="3660" y="156" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0occvxv_di" bpmnElement="Gateway_0occvxv" isMarkerVisible="true">
        <dc:Bounds x="2305" y="171" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0933jlf" bpmnElement="start">
        <dc:Bounds x="152" y="178" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1gfcf4s_di" bpmnElement="Flow_1gfcf4s">
        <di:waypoint x="188" y="196" />
        <di:waypoint x="610" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13uxqks_di" bpmnElement="Flow_13uxqks">
        <di:waypoint x="710" y="196" />
        <di:waypoint x="805" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xpvbhd_di" bpmnElement="Flow_0xpvbhd">
        <di:waypoint x="830" y="221" />
        <di:waypoint x="830" y="328" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="843" y="272" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dm1yfp_di" bpmnElement="Flow_1dm1yfp">
        <di:waypoint x="855" y="196" />
        <di:waypoint x="985" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ccm4fi_di" bpmnElement="Flow_0ccm4fi">
        <di:waypoint x="1035" y="196" />
        <di:waypoint x="1150" y="196" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1049" y="178" width="89" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04ihxlw_di" bpmnElement="Flow_04ihxlw">
        <di:waypoint x="1250" y="196" />
        <di:waypoint x="1365" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_009rhwp_di" bpmnElement="Flow_009rhwp">
        <di:waypoint x="1390" y="221" />
        <di:waypoint x="1390" y="348" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1403" y="282" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ojsgty_di" bpmnElement="Flow_1ojsgty">
        <di:waypoint x="1415" y="196" />
        <di:waypoint x="1600" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sc55va_di" bpmnElement="Flow_1sc55va">
        <di:waypoint x="1700" y="196" />
        <di:waypoint x="1805" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f4sioz_di" bpmnElement="Flow_0f4sioz">
        <di:waypoint x="1855" y="196" />
        <di:waypoint x="2030" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12cj440_di" bpmnElement="Flow_12cj440">
        <di:waypoint x="1830" y="221" />
        <di:waypoint x="1830" y="342" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1843" y="272" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0i6dkky_di" bpmnElement="Flow_0i6dkky">
        <di:waypoint x="2130" y="196" />
        <di:waypoint x="2205" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05m43wr_di" bpmnElement="Flow_05m43wr">
        <di:waypoint x="2230" y="221" />
        <di:waypoint x="2230" y="348" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2230" y="281" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fx3v49_di" bpmnElement="Flow_1fx3v49">
        <di:waypoint x="2255" y="196" />
        <di:waypoint x="2305" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s0pp0i_di" bpmnElement="Flow_1s0pp0i">
        <di:waypoint x="3170" y="196" />
        <di:waypoint x="3225" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01h7mlf_di" bpmnElement="Flow_01h7mlf">
        <di:waypoint x="3250" y="221" />
        <di:waypoint x="3250" y="348" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3249" y="282" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0twpfne_di" bpmnElement="Flow_0twpfne">
        <di:waypoint x="3275" y="196" />
        <di:waypoint x="3360" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k39dhe_di" bpmnElement="Flow_1k39dhe">
        <di:waypoint x="3460" y="196" />
        <di:waypoint x="3535" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rpq9ua_di" bpmnElement="Flow_1rpq9ua">
        <di:waypoint x="3585" y="196" />
        <di:waypoint x="3660" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09ojkbr_di" bpmnElement="Flow_09ojkbr">
        <di:waypoint x="3560" y="221" />
        <di:waypoint x="3560" y="348" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3559" y="282" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qg9rbz_di" bpmnElement="Flow_1qg9rbz">
        <di:waypoint x="3760" y="196" />
        <di:waypoint x="3835" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_145yh0o_di" bpmnElement="Flow_145yh0o">
        <di:waypoint x="3860" y="221" />
        <di:waypoint x="3860" y="348" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3859" y="282" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13m76f2_di" bpmnElement="Flow_13m76f2">
        <di:waypoint x="3885" y="196" />
        <di:waypoint x="3980" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_161bbrt_di" bpmnElement="Flow_161bbrt">
        <di:waypoint x="2510" y="196" />
        <di:waypoint x="2585" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10nxq9p_di" bpmnElement="Flow_10nxq9p">
        <di:waypoint x="2610" y="221" />
        <di:waypoint x="2610" y="362" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2610" y="289" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lj84w5_di" bpmnElement="Flow_1lj84w5">
        <di:waypoint x="2635" y="196" />
        <di:waypoint x="2720" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rlvo2k_di" bpmnElement="Flow_1rlvo2k">
        <di:waypoint x="4185" y="196" />
        <di:waypoint x="4272" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0m1g3lk_di" bpmnElement="Flow_0m1g3lk">
        <di:waypoint x="2355" y="196" />
        <di:waypoint x="2410" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0667v8w_di" bpmnElement="Flow_0667v8w">
        <di:waypoint x="4080" y="196" />
        <di:waypoint x="4135" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02z6pdv_di" bpmnElement="Flow_02z6pdv">
        <di:waypoint x="4160" y="221" />
        <di:waypoint x="4160" y="362" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4159" y="289" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pjlej8_di" bpmnElement="Flow_1pjlej8">
        <di:waypoint x="2985" y="196" />
        <di:waypoint x="3070" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02dzn3n_di" bpmnElement="Flow_02dzn3n">
        <di:waypoint x="2820" y="196" />
        <di:waypoint x="2935" y="196" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00t33jz_di" bpmnElement="Flow_00t33jz">
        <di:waypoint x="2960" y="221" />
        <di:waypoint x="2960" y="362" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2958" y="289" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bnkzcu_di" bpmnElement="Flow_0bnkzcu">
        <di:waypoint x="1010" y="171" />
        <di:waypoint x="1010" y="96" />
        <di:waypoint x="2330" y="96" />
        <di:waypoint x="2330" y="171" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
