<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.23.0">
  <bpmn:process id="Onboarding" name="Onboarding" isExecutable="true" camunda:versionTag="${version}">
    <bpmn:extensionElements />
    <bpmn:startEvent id="orderExecStart" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_0gc4555</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_0bpbzxx" default="Flow_0k1pg3e">
      <bpmn:incoming>Flow_1kjwlsi</bpmn:incoming>
      <bpmn:outgoing>Flow_01b8t2y</bpmn:outgoing>
      <bpmn:outgoing>Flow_0k1pg3e</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_03c4vy4">
      <bpmn:incoming>Flow_01b8t2y</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_01b8t2y" name="Failure" sourceRef="Gateway_0bpbzxx" targetRef="Event_03c4vy4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSCreateProfile" name="Create Customer Profile In BS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_18zonsj</bpmn:incoming>
      <bpmn:outgoing>Flow_0ghye85</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSCreateAccount" name="Create Billing Account" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_06scbqe</bpmn:incoming>
      <bpmn:outgoing>Flow_1kjwlsi</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1qr3v0z" default="Flow_06scbqe">
      <bpmn:incoming>Flow_0ghye85</bpmn:incoming>
      <bpmn:outgoing>Flow_04cyann</bpmn:outgoing>
      <bpmn:outgoing>Flow_06scbqe</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ghye85" sourceRef="BSCreateProfile" targetRef="Gateway_1qr3v0z" />
    <bpmn:endEvent id="Event_0tpcvkz">
      <bpmn:incoming>Flow_04cyann</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_04cyann" name="Failure" sourceRef="Gateway_1qr3v0z" targetRef="Event_0tpcvkz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0gc4555" sourceRef="orderExecStart" targetRef="OrderEnrichment" />
    <bpmn:sequenceFlow id="Flow_0ae2gv8" sourceRef="CreateInstancesTask" targetRef="Gateway_1nrrv7h" />
    <bpmn:serviceTask id="CreateInstancesTask" name="Create Instances" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${createInstancesDelegateOnboarding}">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="childProcess">Onboarding-ServiceProvisioning</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1sz58uc</bpmn:incoming>
      <bpmn:incoming>Flow_14jjesd</bpmn:incoming>
      <bpmn:outgoing>Flow_0ae2gv8</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1nrrv7h" name="All instances created ?">
      <bpmn:incoming>Flow_0ae2gv8</bpmn:incoming>
      <bpmn:outgoing>Flow_1kqxjia</bpmn:outgoing>
      <bpmn:outgoing>Flow_1sz58uc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1kqxjia" name="Yes" sourceRef="Gateway_1nrrv7h" targetRef="orderExecEnd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${allCreated}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1sz58uc" name="No" sourceRef="Gateway_1nrrv7h" targetRef="CreateInstancesTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${not allCreated}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1kqxjia</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0fuzl5a" default="Flow_0ei8lsu">
      <bpmn:incoming>Flow_0vvv1ml</bpmn:incoming>
      <bpmn:outgoing>Flow_1pgh3s3</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ei8lsu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1fi99c8">
      <bpmn:incoming>Flow_1pgh3s3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="OrderEnrichment" name="Order Enrichment Delegate" camunda:asyncBefore="true" camunda:delegateExpression="${orderEnrichmentDelegate}">
      <bpmn:incoming>Flow_0gc4555</bpmn:incoming>
      <bpmn:outgoing>Flow_0vvv1ml</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0vvv1ml" sourceRef="OrderEnrichment" targetRef="Gateway_0fuzl5a" />
    <bpmn:sequenceFlow id="Flow_1pgh3s3" name="Failure" sourceRef="Gateway_0fuzl5a" targetRef="Event_1fi99c8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0k1pg3e" name="success" sourceRef="Gateway_0bpbzxx" targetRef="Gateway_10w8c8a" />
    <bpmn:serviceTask id="GenerateDataDelegateTask" name="Generate Data Delegate" camunda:asyncBefore="true" camunda:delegateExpression="${generateDataDelegateOnboarding}">
      <bpmn:incoming>Flow_0jajkmv</bpmn:incoming>
      <bpmn:outgoing>Flow_14jjesd</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_14jjesd" sourceRef="GenerateDataDelegateTask" targetRef="CreateInstancesTask" />
    <bpmn:sequenceFlow id="Flow_0ei8lsu" name="success" sourceRef="Gateway_0fuzl5a" targetRef="Gateway_1wx8v84" />
    <bpmn:sequenceFlow id="Flow_06scbqe" name="success" sourceRef="Gateway_1qr3v0z" targetRef="BSCreateAccount" />
    <bpmn:exclusiveGateway id="Gateway_1hxxfys" default="Flow_0oz82rh">
      <bpmn:incoming>Flow_1rqe3ng</bpmn:incoming>
      <bpmn:outgoing>Flow_0pcxz9r</bpmn:outgoing>
      <bpmn:outgoing>Flow_0oz82rh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0k5jkz3">
      <bpmn:incoming>Flow_0pcxz9r</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0pcxz9r" name="Failure" sourceRef="Gateway_1hxxfys" targetRef="Event_0k5jkz3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="ESB_CreateAccount" name="Account Creation in OCS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="thirdPartyId">ocs-account-creation</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1nrs10z</bpmn:incoming>
      <bpmn:outgoing>Flow_1rqe3ng</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1rqe3ng" sourceRef="ESB_CreateAccount" targetRef="Gateway_1hxxfys" />
    <bpmn:callActivity id="PaymentWorkflow" name="Payment and future order wokrflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0oz82rh</bpmn:incoming>
      <bpmn:outgoing>Flow_0neyp4l</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:exclusiveGateway id="Gateway_1g9sz8j" default="Flow_0jajkmv">
      <bpmn:incoming>Flow_0neyp4l</bpmn:incoming>
      <bpmn:outgoing>Flow_1ujkjz2</bpmn:outgoing>
      <bpmn:outgoing>Flow_0jajkmv</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1exlg9d">
      <bpmn:incoming>Flow_1ujkjz2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0neyp4l" sourceRef="PaymentWorkflow" targetRef="Gateway_1g9sz8j" />
    <bpmn:sequenceFlow id="Flow_1ujkjz2" sourceRef="Gateway_1g9sz8j" targetRef="Event_1exlg9d">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0oz82rh" sourceRef="Gateway_1hxxfys" targetRef="PaymentWorkflow" />
    <bpmn:sequenceFlow id="Flow_0jajkmv" sourceRef="Gateway_1g9sz8j" targetRef="GenerateDataDelegateTask" />
    <bpmn:exclusiveGateway id="Gateway_1wx8v84" name="is profile and account created in enrichment" default="Flow_18zonsj">
      <bpmn:incoming>Flow_0ei8lsu</bpmn:incoming>
      <bpmn:outgoing>Flow_18zonsj</bpmn:outgoing>
      <bpmn:outgoing>Flow_05j9bby</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_18zonsj" name="No" sourceRef="Gateway_1wx8v84" targetRef="BSCreateProfile" />
    <bpmn:sequenceFlow id="Flow_1kjwlsi" sourceRef="BSCreateAccount" targetRef="Gateway_0bpbzxx" />
    <bpmn:exclusiveGateway id="Gateway_10w8c8a">
      <bpmn:incoming>Flow_0k1pg3e</bpmn:incoming>
      <bpmn:incoming>Flow_05j9bby</bpmn:incoming>
      <bpmn:outgoing>Flow_1nrs10z</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1nrs10z" sourceRef="Gateway_10w8c8a" targetRef="ESB_CreateAccount" />
    <bpmn:sequenceFlow id="Flow_05j9bby" name="Yes" sourceRef="Gateway_1wx8v84" targetRef="Gateway_10w8c8a">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isLegacy}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:textAnnotation id="TextAnnotation_1py71py">
      <bpmn:text>for legacy flow, profile and account in billing will be created in enrichment ms</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_17a6fgp" associationDirection="None" sourceRef="Flow_05j9bby" targetRef="TextAnnotation_1py71py" />
  </bpmn:process>
  <bpmn:message id="Message_061isi2" name="PaymentCallback" />
  <bpmn:message id="Message_3ven5fh" name="SuspendWait" />
  <bpmn:message id="Message_1y1dj88" name="FOCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Onboarding">
      <bpmndi:BPMNShape id="Event_1x7dsnm_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0bpbzxx_di" bpmnElement="Gateway_0bpbzxx" isMarkerVisible="true">
        <dc:Bounds x="1125" y="305" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_03c4vy4_di" bpmnElement="Event_03c4vy4">
        <dc:Bounds x="1132" y="422" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0sxees3_di" bpmnElement="BSCreateProfile">
        <dc:Bounds x="670" y="290" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0n2v8qp_di" bpmnElement="BSCreateAccount">
        <dc:Bounds x="960" y="290" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1qr3v0z_di" bpmnElement="Gateway_1qr3v0z" isMarkerVisible="true">
        <dc:Bounds x="845" y="305" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0tpcvkz_di" bpmnElement="Event_0tpcvkz">
        <dc:Bounds x="852" y="432" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1r0bov3_di" bpmnElement="CreateInstancesTask">
        <dc:Bounds x="2480" y="290" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1nrrv7h_di" bpmnElement="Gateway_1nrrv7h" isMarkerVisible="true">
        <dc:Bounds x="2675" y="305" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2669" y="275" width="62" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0gn54oi_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2812" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fuzl5a_di" bpmnElement="Gateway_0fuzl5a" isMarkerVisible="true">
        <dc:Bounds x="445" y="305" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1fi99c8_di" bpmnElement="Event_1fi99c8">
        <dc:Bounds x="452" y="432" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01wyi3h_di" bpmnElement="OrderEnrichment">
        <dc:Bounds x="280" y="290" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_174xh0s_di" bpmnElement="GenerateDataDelegateTask">
        <dc:Bounds x="2250" y="290" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1hxxfys_di" bpmnElement="Gateway_1hxxfys" isMarkerVisible="true">
        <dc:Bounds x="1595" y="305" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0k5jkz3_di" bpmnElement="Event_0k5jkz3">
        <dc:Bounds x="1602" y="422" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0thv5ay_di" bpmnElement="ESB_CreateAccount">
        <dc:Bounds x="1390" y="290" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07fbfj5_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="1860" y="290" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1g9sz8j_di" bpmnElement="Gateway_1g9sz8j" isMarkerVisible="true">
        <dc:Bounds x="2045" y="305" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1exlg9d_di" bpmnElement="Event_1exlg9d">
        <dc:Bounds x="2052" y="425" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1wx8v84_di" bpmnElement="Gateway_1wx8v84" isMarkerVisible="true">
        <dc:Bounds x="575" y="305" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="561" y="362" width="79" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_10w8c8a_di" bpmnElement="Gateway_10w8c8a" isMarkerVisible="true">
        <dc:Bounds x="1275" y="305" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_17a6fgp_di" bpmnElement="Association_17a6fgp">
        <di:waypoint x="950" y="200" />
        <di:waypoint x="1080" y="124" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01b8t2y_di" bpmnElement="Flow_01b8t2y">
        <di:waypoint x="1150" y="355" />
        <di:waypoint x="1150" y="422" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1153" y="371" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ghye85_di" bpmnElement="Flow_0ghye85">
        <di:waypoint x="770" y="330" />
        <di:waypoint x="845" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04cyann_di" bpmnElement="Flow_04cyann">
        <di:waypoint x="870" y="355" />
        <di:waypoint x="870" y="432" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="874" y="387" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gc4555_di" bpmnElement="Flow_0gc4555">
        <di:waypoint x="188" y="330" />
        <di:waypoint x="280" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ae2gv8_di" bpmnElement="Flow_0ae2gv8">
        <di:waypoint x="2580" y="330" />
        <di:waypoint x="2675" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kqxjia_di" bpmnElement="Flow_1kqxjia">
        <di:waypoint x="2725" y="330" />
        <di:waypoint x="2812" y="330" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2760" y="312" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sz58uc_di" bpmnElement="Flow_1sz58uc">
        <di:waypoint x="2700" y="355" />
        <di:waypoint x="2700" y="420" />
        <di:waypoint x="2530" y="420" />
        <di:waypoint x="2530" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2608" y="423" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vvv1ml_di" bpmnElement="Flow_0vvv1ml">
        <di:waypoint x="380" y="330" />
        <di:waypoint x="445" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pgh3s3_di" bpmnElement="Flow_1pgh3s3">
        <di:waypoint x="470" y="355" />
        <di:waypoint x="470" y="432" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="475" y="374" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0k1pg3e_di" bpmnElement="Flow_0k1pg3e">
        <di:waypoint x="1175" y="330" />
        <di:waypoint x="1275" y="330" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1193" y="303" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14jjesd_di" bpmnElement="Flow_14jjesd">
        <di:waypoint x="2350" y="330" />
        <di:waypoint x="2480" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ei8lsu_di" bpmnElement="Flow_0ei8lsu">
        <di:waypoint x="495" y="330" />
        <di:waypoint x="575" y="330" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="527" y="312" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06scbqe_di" bpmnElement="Flow_06scbqe">
        <di:waypoint x="895" y="330" />
        <di:waypoint x="960" y="330" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="907" y="312" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pcxz9r_di" bpmnElement="Flow_0pcxz9r">
        <di:waypoint x="1620" y="355" />
        <di:waypoint x="1620" y="422" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1619" y="386" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rqe3ng_di" bpmnElement="Flow_1rqe3ng">
        <di:waypoint x="1490" y="330" />
        <di:waypoint x="1595" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0neyp4l_di" bpmnElement="Flow_0neyp4l">
        <di:waypoint x="1960" y="330" />
        <di:waypoint x="2045" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ujkjz2_di" bpmnElement="Flow_1ujkjz2">
        <di:waypoint x="2070" y="355" />
        <di:waypoint x="2070" y="425" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oz82rh_di" bpmnElement="Flow_0oz82rh">
        <di:waypoint x="1645" y="330" />
        <di:waypoint x="1860" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jajkmv_di" bpmnElement="Flow_0jajkmv">
        <di:waypoint x="2095" y="330" />
        <di:waypoint x="2250" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18zonsj_di" bpmnElement="Flow_18zonsj">
        <di:waypoint x="625" y="330" />
        <di:waypoint x="670" y="330" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="640" y="312" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kjwlsi_di" bpmnElement="Flow_1kjwlsi">
        <di:waypoint x="1060" y="330" />
        <di:waypoint x="1125" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nrs10z_di" bpmnElement="Flow_1nrs10z">
        <di:waypoint x="1325" y="330" />
        <di:waypoint x="1390" y="330" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05j9bby_di" bpmnElement="Flow_05j9bby">
        <di:waypoint x="600" y="305" />
        <di:waypoint x="600" y="200" />
        <di:waypoint x="1300" y="200" />
        <di:waypoint x="1300" y="305" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="941" y="182" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_1py71py_di" bpmnElement="TextAnnotation_1py71py">
        <dc:Bounds x="1080" y="80" width="100" height="98" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
