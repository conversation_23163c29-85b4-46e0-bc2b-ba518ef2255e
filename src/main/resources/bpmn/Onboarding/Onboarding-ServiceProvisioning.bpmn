<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_18pr9mp" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0">
  <bpmn:process id="Onboarding-ServiceProvisioning" name="Onboarding-ServiceProvisioning" isExecutable="true" camunda:versionTag="${version}">
    <bpmn:serviceTask id="BSCreateService" name="Service Creation In Billing System" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_18ife0f</bpmn:incoming>
      <bpmn:outgoing>Flow_13uxqks</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_13uxqks" sourceRef="BSCreateService" targetRef="Gateway_1meylc0" />
    <bpmn:exclusiveGateway id="Gateway_1meylc0" default="Flow_1dm1yfp">
      <bpmn:incoming>Flow_13uxqks</bpmn:incoming>
      <bpmn:outgoing>Flow_0xpvbhd</bpmn:outgoing>
      <bpmn:outgoing>Flow_1dm1yfp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0rw1vei">
      <bpmn:incoming>Flow_0xpvbhd</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0xpvbhd" name="Failure" sourceRef="Gateway_1meylc0" targetRef="Event_0rw1vei">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="SimDeliveryCallback" name="SimDelivery Callback " camunda:asyncBefore="true" messageRef="Message_1y1dj88">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1989ct4</bpmn:incoming>
      <bpmn:outgoing>Flow_04ihxlw</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_1vr41hn" default="Flow_0yt2zj6">
      <bpmn:incoming>Flow_04ihxlw</bpmn:incoming>
      <bpmn:outgoing>Flow_009rhwp</bpmn:outgoing>
      <bpmn:outgoing>Flow_0yt2zj6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0yq91sr">
      <bpmn:incoming>Flow_009rhwp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_04ihxlw" sourceRef="SimDeliveryCallback" targetRef="Gateway_1vr41hn" />
    <bpmn:sequenceFlow id="Flow_009rhwp" name="Failure" sourceRef="Gateway_1vr41hn" targetRef="Event_0yq91sr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1s98h1x" default="Flow_1rpq9ua">
      <bpmn:incoming>Flow_1k39dhe</bpmn:incoming>
      <bpmn:outgoing>Flow_1rpq9ua</bpmn:outgoing>
      <bpmn:outgoing>Flow_09ojkbr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1rpq9ua" sourceRef="Gateway_1s98h1x" targetRef="OCSOfferActivationProcess" />
    <bpmn:exclusiveGateway id="Gateway_0li77bm" default="Flow_13m76f2">
      <bpmn:incoming>Flow_1qg9rbz</bpmn:incoming>
      <bpmn:outgoing>Flow_145yh0o</bpmn:outgoing>
      <bpmn:outgoing>Flow_13m76f2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1m499j7" default="Flow_1lj84w5">
      <bpmn:incoming>Flow_161bbrt</bpmn:incoming>
      <bpmn:outgoing>Flow_10nxq9p</bpmn:outgoing>
      <bpmn:outgoing>Flow_1lj84w5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_161bbrt" sourceRef="BSAddSubscription" targetRef="Gateway_1m499j7" />
    <bpmn:endEvent id="subProcessEndEvent">
      <bpmn:incoming>Flow_05slb4h</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSAddSubscription" name="BSAddSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:extensionElements>
        <camunda:executionListener delegateExpression="${subscriptionChargeModifier}" event="start" />
        <camunda:executionListener delegateExpression="${subscriptionChargeModifier}" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1dshgiy</bpmn:incoming>
      <bpmn:outgoing>Flow_161bbrt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSActivateService" name="BSActivateService" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_13m76f2</bpmn:incoming>
      <bpmn:outgoing>Flow_0667v8w</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1mu71an">
      <bpmn:incoming>Flow_09ojkbr</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_09ojkbr" name="Failure" sourceRef="Gateway_1s98h1x" targetRef="Event_1mu71an">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_0xlwtxa">
      <bpmn:incoming>Flow_145yh0o</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_145yh0o" name="Failure" sourceRef="Gateway_0li77bm" targetRef="Event_0xlwtxa">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1vihaa4">
      <bpmn:incoming>Flow_10nxq9p</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_10nxq9p" name="Failure" sourceRef="Gateway_1m499j7" targetRef="Event_1vihaa4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1b4c33j" default="Flow_1rlvo2k">
      <bpmn:incoming>Flow_0667v8w</bpmn:incoming>
      <bpmn:outgoing>Flow_02z6pdv</bpmn:outgoing>
      <bpmn:outgoing>Flow_1rlvo2k</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0667v8w" sourceRef="BSActivateService" targetRef="Gateway_1b4c33j" />
    <bpmn:endEvent id="Event_0okg50j">
      <bpmn:incoming>Flow_02z6pdv</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_02z6pdv" name="Failure" sourceRef="Gateway_1b4c33j" targetRef="Event_0okg50j">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="SOMCallback" name="SOM Provisioning Callback " camunda:asyncBefore="true" messageRef="Message_1lbkhkd">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOM_Provisioning</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOM_Provisioning" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1pstdyk</bpmn:incoming>
      <bpmn:outgoing>Flow_1s0pp0i</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_1s0pp0i" sourceRef="SOMCallback" targetRef="Gateway_16nnuen" />
    <bpmn:exclusiveGateway id="Gateway_03y9xye" default="Flow_1pjlej8">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_02dzn3n</bpmn:incoming>
      <bpmn:outgoing>Flow_1pjlej8</bpmn:outgoing>
      <bpmn:outgoing>Flow_00t33jz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_02dzn3n" sourceRef="SOM_Provisioning" targetRef="Gateway_03y9xye" />
    <bpmn:sequenceFlow id="Flow_1pjlej8" sourceRef="Gateway_03y9xye" targetRef="Gateway_1p1ex8z" />
    <bpmn:serviceTask id="SOM_Provisioning" name="SOM_Provisioning" camunda:asyncBefore="true" camunda:delegateExpression="${somServiceOrderCreation}">
      <bpmn:incoming>Flow_1lj84w5</bpmn:incoming>
      <bpmn:outgoing>Flow_02dzn3n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1lilan5">
      <bpmn:incoming>Flow_00t33jz</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_00t33jz" name="Failure" sourceRef="Gateway_03y9xye" targetRef="Event_1lilan5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:startEvent id="start">
      <bpmn:outgoing>Flow_00abri6</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_06972gh" default="Flow_0bnkzcu">
      <bpmn:incoming>Flow_1self6d</bpmn:incoming>
      <bpmn:outgoing>Flow_14msemw</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bnkzcu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1dm1yfp" sourceRef="Gateway_1meylc0" targetRef="Gateway_1cr1o1g" />
    <bpmn:sequenceFlow id="Flow_1rlvo2k" sourceRef="Gateway_1b4c33j" targetRef="Gateway_0mnu01m" />
    <bpmn:sequenceFlow id="Flow_13m76f2" sourceRef="Gateway_0li77bm" targetRef="BSActivateService" />
    <bpmn:sequenceFlow id="Flow_1lj84w5" sourceRef="Gateway_1m499j7" targetRef="SOM_Provisioning" />
    <bpmn:serviceTask id="ESBCreateSubscriber" name="Service Creation in OCS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1ervo3l</bpmn:incoming>
      <bpmn:outgoing>Flow_1k39dhe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1k39dhe" sourceRef="ESBCreateSubscriber" targetRef="Gateway_1s98h1x" />
    <bpmn:callActivity id="OCSOfferActivationProcess" name="OCS Offer Activation Process" camunda:asyncBefore="true" calledElement="OCSOfferActivationProcess" camunda:calledElementBinding="deployment">
      <bpmn:documentation>Iterate over addon subs in BS Add subs response list. planType  1 is base, 0 is addon</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:out variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1rpq9ua</bpmn:incoming>
      <bpmn:outgoing>Flow_1qg9rbz</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:asyncBefore="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.currentExecution.BSAddSubscriptionResponseAttributes.subscriptions[?(@.planType==&#39;0&#39;)]&#34;).elementList()}" camunda:elementVariable="execution">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_1qg9rbz" sourceRef="OCSOfferActivationProcess" targetRef="Gateway_0li77bm" />
    <bpmn:sequenceFlow id="Flow_1tzqjw4" sourceRef="NMSFetchAsset" targetRef="Gateway_0h08wow" />
    <bpmn:sequenceFlow id="Flow_1rjnmgn" sourceRef="NMSBlockSim" targetRef="Gateway_01xdppj" />
    <bpmn:exclusiveGateway id="Gateway_01zcgdg" default="Flow_1qd6uha">
      <bpmn:incoming>Flow_0dvdnvx</bpmn:incoming>
      <bpmn:outgoing>Flow_1rpzomy</bpmn:outgoing>
      <bpmn:outgoing>Flow_1qd6uha</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1rpzomy" sourceRef="Gateway_01zcgdg" targetRef="NMSBlockSim">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${nmsUpdateCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_10vx64w">
      <bpmn:incoming>Flow_0ybtsdi</bpmn:incoming>
      <bpmn:incoming>Flow_1qd6uha</bpmn:incoming>
      <bpmn:outgoing>Flow_00xq99i</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_00xq99i" sourceRef="Gateway_10vx64w" targetRef="Gateway_0vabjfy" />
    <bpmn:exclusiveGateway id="Gateway_0h08wow" default="Flow_0dvdnvx">
      <bpmn:incoming>Flow_1tzqjw4</bpmn:incoming>
      <bpmn:outgoing>Flow_0dvdnvx</bpmn:outgoing>
      <bpmn:outgoing>Flow_0hngoqn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0dvdnvx" sourceRef="Gateway_0h08wow" targetRef="Gateway_01zcgdg" />
    <bpmn:endEvent id="Event_132ue39">
      <bpmn:incoming>Flow_0hngoqn</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0hngoqn" sourceRef="Gateway_0h08wow" targetRef="Event_132ue39">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_01xdppj" default="Flow_0ybtsdi">
      <bpmn:incoming>Flow_1rjnmgn</bpmn:incoming>
      <bpmn:outgoing>Flow_0ybtsdi</bpmn:outgoing>
      <bpmn:outgoing>Flow_1oe0lgp</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ybtsdi" sourceRef="Gateway_01xdppj" targetRef="Gateway_10vx64w" />
    <bpmn:endEvent id="Event_16xg273">
      <bpmn:incoming>Flow_1oe0lgp</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1oe0lgp" sourceRef="Gateway_01xdppj" targetRef="Event_16xg273">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1qd6uha" sourceRef="Gateway_01zcgdg" targetRef="Gateway_10vx64w" />
    <bpmn:serviceTask id="NMSFetchAsset" name="Fetch Asset From NMS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1u54nz3</bpmn:incoming>
      <bpmn:outgoing>Flow_1tzqjw4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="NMSBlockSim" name="Block Sim in NMS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1rpzomy</bpmn:incoming>
      <bpmn:outgoing>Flow_1rjnmgn</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1w48wao" default="Flow_0b1il50">
      <bpmn:incoming>Flow_1fby7ln</bpmn:incoming>
      <bpmn:outgoing>Flow_1u54nz3</bpmn:outgoing>
      <bpmn:outgoing>Flow_0b1il50</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1u54nz3" sourceRef="Gateway_1w48wao" targetRef="NMSFetchAsset">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${nmsCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0vabjfy">
      <bpmn:incoming>Flow_00xq99i</bpmn:incoming>
      <bpmn:incoming>Flow_0b1il50</bpmn:incoming>
      <bpmn:outgoing>Flow_18ife0f</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_18ife0f" sourceRef="Gateway_0vabjfy" targetRef="BSCreateService" />
    <bpmn:sequenceFlow id="Flow_0b1il50" sourceRef="Gateway_1w48wao" targetRef="Gateway_0vabjfy" />
    <bpmn:serviceTask id="PairSimInNMS" name="PairSimInNMS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1bgi1ih</bpmn:incoming>
      <bpmn:outgoing>Flow_0o1j51c</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0g8d0bs">
      <bpmn:incoming>Flow_0o1j51c</bpmn:incoming>
      <bpmn:outgoing>Flow_1716s36</bpmn:outgoing>
      <bpmn:outgoing>Flow_0shgi8q</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0o1j51c" sourceRef="PairSimInNMS" targetRef="Gateway_0g8d0bs" />
    <bpmn:endEvent id="Event_0xvgq47">
      <bpmn:incoming>Flow_1716s36</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1716s36" sourceRef="Gateway_0g8d0bs" targetRef="Event_0xvgq47">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_00abri6" sourceRef="start" targetRef="Gateway_03bi9vv" />
    <bpmn:exclusiveGateway id="Gateway_1j2pnf0">
      <bpmn:incoming>Flow_0yt2zj6</bpmn:incoming>
      <bpmn:incoming>Flow_19b593d</bpmn:incoming>
      <bpmn:outgoing>Flow_1qe1s2x</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0yt2zj6" sourceRef="Gateway_1vr41hn" targetRef="Gateway_1j2pnf0" />
    <bpmn:exclusiveGateway id="Gateway_1u6w8e4" camunda:asyncBefore="true" default="Flow_19b593d">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.CallBackExecutionListener" event="start" />
        <camunda:properties>
          <camunda:property name="callBackType" value="SIM" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_14msemw</bpmn:incoming>
      <bpmn:outgoing>Flow_1989ct4</bpmn:outgoing>
      <bpmn:outgoing>Flow_19b593d</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_14msemw" sourceRef="Gateway_06972gh" targetRef="Gateway_1u6w8e4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${simDeliveryCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1989ct4" sourceRef="Gateway_1u6w8e4" targetRef="SimDeliveryCallback">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callBackProcessReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_19b593d" sourceRef="Gateway_1u6w8e4" targetRef="Gateway_1j2pnf0" />
    <bpmn:exclusiveGateway id="Gateway_0occvxv">
      <bpmn:incoming>Flow_0bnkzcu</bpmn:incoming>
      <bpmn:incoming>Flow_1qe1s2x</bpmn:incoming>
      <bpmn:outgoing>Flow_0ppixsr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ppixsr" sourceRef="Gateway_0occvxv" targetRef="Gateway_0iwpaxl" />
    <bpmn:sequenceFlow id="Flow_0bnkzcu" sourceRef="Gateway_06972gh" targetRef="Gateway_0occvxv" />
    <bpmn:exclusiveGateway id="Gateway_03bi9vv" default="Flow_10ov43z">
      <bpmn:incoming>Flow_00abri6</bpmn:incoming>
      <bpmn:outgoing>Flow_1bpomgc</bpmn:outgoing>
      <bpmn:outgoing>Flow_10ov43z</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1bpomgc" name="isEsimCall-true" sourceRef="Gateway_03bi9vv" targetRef="Arm_FetchAssetDetails">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isIccidValidationReq}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1r0z53e" default="Flow_1dr62wy">
      <bpmn:incoming>Flow_1v25ynt</bpmn:incoming>
      <bpmn:outgoing>Flow_1dr62wy</bpmn:outgoing>
      <bpmn:outgoing>Flow_0lj87y7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1dr62wy" sourceRef="Gateway_1r0z53e" targetRef="Gateway_13lxypt" />
    <bpmn:exclusiveGateway id="Gateway_13lxypt">
      <bpmn:incoming>Flow_1dr62wy</bpmn:incoming>
      <bpmn:incoming>Flow_10ov43z</bpmn:incoming>
      <bpmn:outgoing>Flow_1fby7ln</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1fby7ln" sourceRef="Gateway_13lxypt" targetRef="Gateway_1w48wao" />
    <bpmn:serviceTask id="Arm_FetchAssetDetails" name="Arm Fetch Asset Details" camunda:asyncBefore="true" camunda:delegateExpression="${fetchDetailsFromArm}">
      <bpmn:incoming>Flow_1bpomgc</bpmn:incoming>
      <bpmn:outgoing>Flow_1v25ynt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1v25ynt" sourceRef="Arm_FetchAssetDetails" targetRef="Gateway_1r0z53e" />
    <bpmn:sequenceFlow id="Flow_10ov43z" sourceRef="Gateway_03bi9vv" targetRef="Gateway_13lxypt" />
    <bpmn:endEvent id="Event_05hmwxi">
      <bpmn:incoming>Flow_0lj87y7</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0lj87y7" sourceRef="Gateway_1r0z53e" targetRef="Event_05hmwxi">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1cr1o1g" default="Flow_1self6d">
      <bpmn:incoming>Flow_1dm1yfp</bpmn:incoming>
      <bpmn:outgoing>Flow_1self6d</bpmn:outgoing>
      <bpmn:outgoing>Flow_0cnvklw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1self6d" sourceRef="Gateway_1cr1o1g" targetRef="Gateway_06972gh" />
    <bpmn:exclusiveGateway id="Gateway_0iwpaxl">
      <bpmn:incoming>Flow_0ppixsr</bpmn:incoming>
      <bpmn:incoming>Flow_0cnvklw</bpmn:incoming>
      <bpmn:outgoing>Flow_128v3m5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_128v3m5" sourceRef="Gateway_0iwpaxl" targetRef="Gateway_0vd9u9k" />
    <bpmn:sequenceFlow id="Flow_0cnvklw" name="isEsimCall-true" sourceRef="Gateway_1cr1o1g" targetRef="Gateway_0iwpaxl">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isEsimCall}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0vd9u9k" default="Flow_0yd5kw0">
      <bpmn:incoming>Flow_128v3m5</bpmn:incoming>
      <bpmn:outgoing>Flow_0e4om3q</bpmn:outgoing>
      <bpmn:outgoing>Flow_0yd5kw0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0e4om3q" name="isEsimCall-true" sourceRef="Gateway_0vd9u9k" targetRef="Esb_blockEsim">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isEsimCall}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1w29n8p">
      <bpmn:incoming>Flow_0fk6vpr</bpmn:incoming>
      <bpmn:incoming>Flow_0yd5kw0</bpmn:incoming>
      <bpmn:outgoing>Flow_1bgi1ih</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1bgi1ih" sourceRef="Gateway_1w29n8p" targetRef="PairSimInNMS" />
    <bpmn:serviceTask id="Esb_blockEsim" name="Esb_blockEsim" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0e4om3q</bpmn:incoming>
      <bpmn:outgoing>Flow_08xt043</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_08xt043" sourceRef="Esb_blockEsim" targetRef="Gateway_14c69zm" />
    <bpmn:exclusiveGateway id="Gateway_14c69zm" default="Flow_1c9tdm6">
      <bpmn:incoming>Flow_08xt043</bpmn:incoming>
      <bpmn:outgoing>Flow_1c9tdm6</bpmn:outgoing>
      <bpmn:outgoing>Flow_1kt99hu</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1c9tdm6" sourceRef="Gateway_14c69zm" targetRef="ESBEsimBlockCallback" />
    <bpmn:exclusiveGateway id="Gateway_1m12f1z" default="Flow_0fk6vpr">
      <bpmn:incoming>Flow_0py3vtd</bpmn:incoming>
      <bpmn:outgoing>Flow_0fk6vpr</bpmn:outgoing>
      <bpmn:outgoing>Flow_18k02d2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0fk6vpr" sourceRef="Gateway_1m12f1z" targetRef="Gateway_1w29n8p" />
    <bpmn:receiveTask id="ESBEsimBlockCallback" name="Esb_blockEsimCallback" camunda:asyncBefore="true" messageRef="Message_2ipngvh">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">Esb_blockEsim</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="Esb_blockEsim" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1c9tdm6</bpmn:incoming>
      <bpmn:outgoing>Flow_0py3vtd</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_0py3vtd" sourceRef="ESBEsimBlockCallback" targetRef="Gateway_1m12f1z" />
    <bpmn:sequenceFlow id="Flow_0yd5kw0" sourceRef="Gateway_0vd9u9k" targetRef="Gateway_1w29n8p" />
    <bpmn:endEvent id="Event_1ystiom">
      <bpmn:incoming>Flow_18k02d2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_0g8kkey">
      <bpmn:incoming>Flow_1kt99hu</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_18k02d2" sourceRef="Gateway_1m12f1z" targetRef="Event_1ystiom">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1kt99hu" sourceRef="Gateway_14c69zm" targetRef="Event_0g8kkey">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Esb_fetchEsimDetails" name="Esb_fetchEsimDetails" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1c0jh7u</bpmn:incoming>
      <bpmn:outgoing>Flow_1d9u5f5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1d9u5f5" sourceRef="Esb_fetchEsimDetails" targetRef="Gateway_00o8s29" />
    <bpmn:exclusiveGateway id="Gateway_00o8s29" default="Flow_0ztf3r1">
      <bpmn:incoming>Flow_1d9u5f5</bpmn:incoming>
      <bpmn:outgoing>Flow_0ztf3r1</bpmn:outgoing>
      <bpmn:outgoing>Flow_1fiogz9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ztf3r1" sourceRef="Gateway_00o8s29" targetRef="Esb_Fetch_EsimCallback" />
    <bpmn:serviceTask id="Esb_Fetch_EsimCallback" name="Esb_FetchEsimCallback_to_DAG" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0ztf3r1</bpmn:incoming>
      <bpmn:outgoing>Flow_0yy9ke2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_02k1i13">
      <bpmn:incoming>Flow_1fiogz9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1fiogz9" name="Failure" sourceRef="Gateway_00o8s29" targetRef="Event_02k1i13">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0yy9ke2" sourceRef="Esb_Fetch_EsimCallback" targetRef="Gateway_1xk8mp9" />
    <bpmn:exclusiveGateway id="Gateway_1o6gpc5" default="Flow_1k6th72">
      <bpmn:incoming>Flow_0x56049</bpmn:incoming>
      <bpmn:outgoing>Flow_1c0jh7u</bpmn:outgoing>
      <bpmn:outgoing>Flow_1k6th72</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1c0jh7u" name="isEsimCall=true" sourceRef="Gateway_1o6gpc5" targetRef="Esb_fetchEsimDetails">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isEsimCall}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_12nbj64">
      <bpmn:incoming>Flow_1svf9by</bpmn:incoming>
      <bpmn:incoming>Flow_1k6th72</bpmn:incoming>
      <bpmn:outgoing>Flow_05slb4h</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_05slb4h" sourceRef="Gateway_12nbj64" targetRef="subProcessEndEvent" />
    <bpmn:exclusiveGateway id="Gateway_1xk8mp9" default="Flow_1svf9by">
      <bpmn:incoming>Flow_0yy9ke2</bpmn:incoming>
      <bpmn:outgoing>Flow_1svf9by</bpmn:outgoing>
      <bpmn:outgoing>Flow_10etk9s</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1svf9by" sourceRef="Gateway_1xk8mp9" targetRef="Gateway_12nbj64" />
    <bpmn:endEvent id="Event_1j5xx9l">
      <bpmn:incoming>Flow_10etk9s</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_10etk9s" name="Failure" sourceRef="Gateway_1xk8mp9" targetRef="Event_1j5xx9l">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1k6th72" sourceRef="Gateway_1o6gpc5" targetRef="Gateway_12nbj64" />
    <bpmn:exclusiveGateway id="Gateway_0639ev2" default="Flow_1jsyw9d">
      <bpmn:incoming>Flow_0shgi8q</bpmn:incoming>
      <bpmn:outgoing>Flow_1jsyw9d</bpmn:outgoing>
      <bpmn:outgoing>Flow_11hbrez</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0shgi8q" sourceRef="Gateway_0g8d0bs" targetRef="Gateway_0639ev2" />
    <bpmn:exclusiveGateway id="Gateway_0hk4ak5" default="Flow_1fxauve">
      <bpmn:incoming>Flow_1jsyw9d</bpmn:incoming>
      <bpmn:outgoing>Flow_06ns6fu</bpmn:outgoing>
      <bpmn:outgoing>Flow_1fxauve</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1jsyw9d" sourceRef="Gateway_0639ev2" targetRef="Gateway_0hk4ak5" />
    <bpmn:sequenceFlow id="Flow_06ns6fu" sourceRef="Gateway_0hk4ak5" targetRef="BS_ChangeSim">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${simDeliveryCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0kjqo20">
      <bpmn:incoming>Flow_0b0cjxk</bpmn:incoming>
      <bpmn:outgoing>Flow_07n5dys</bpmn:outgoing>
      <bpmn:outgoing>Flow_0pccg9a</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0b0cjxk" sourceRef="BS_ChangeSim" targetRef="Gateway_0kjqo20" />
    <bpmn:exclusiveGateway id="Gateway_0c7a5e6">
      <bpmn:incoming>Flow_07n5dys</bpmn:incoming>
      <bpmn:incoming>Flow_1fxauve</bpmn:incoming>
      <bpmn:outgoing>Flow_0jq170y</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_07n5dys" sourceRef="Gateway_0kjqo20" targetRef="Gateway_0c7a5e6" />
    <bpmn:exclusiveGateway id="Gateway_0c7lxl0">
      <bpmn:incoming>Flow_0jq170y</bpmn:incoming>
      <bpmn:incoming>Flow_11hbrez</bpmn:incoming>
      <bpmn:outgoing>Flow_1dshgiy</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0jq170y" sourceRef="Gateway_0c7a5e6" targetRef="Gateway_0c7lxl0" />
    <bpmn:sequenceFlow id="Flow_11hbrez" name="isEsimCall-true" sourceRef="Gateway_0639ev2" targetRef="Gateway_0c7lxl0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isEsimCall}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1fxauve" sourceRef="Gateway_0hk4ak5" targetRef="Gateway_0c7a5e6" />
    <bpmn:endEvent id="Event_0xbmvri">
      <bpmn:incoming>Flow_0pccg9a</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0pccg9a" name="Failure" sourceRef="Gateway_0kjqo20" targetRef="Event_0xbmvri">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BS_ChangeSim" name="Billing Change Sim" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_06ns6fu</bpmn:incoming>
      <bpmn:outgoing>Flow_0b0cjxk</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1qe1s2x" sourceRef="Gateway_1j2pnf0" targetRef="Gateway_0occvxv" />
    <bpmn:sequenceFlow id="Flow_1dshgiy" sourceRef="Gateway_0c7lxl0" targetRef="BSAddSubscription" />
    <bpmn:exclusiveGateway id="Gateway_0mnu01m" name="isKycCallReq=true" default="Flow_0kui9u9">
      <bpmn:incoming>Flow_1rlvo2k</bpmn:incoming>
      <bpmn:outgoing>Flow_1np7d9q</bpmn:outgoing>
      <bpmn:outgoing>Flow_0kui9u9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1np7d9q" sourceRef="Gateway_0mnu01m" targetRef="ESBEkycUpdate">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.workflowData").element().hasProp('currentExecution')&amp;&amp; workflowData.jsonPath("$.workflowData.currentExecution.executionData").element().hasProp('isKycRequired') &amp;&amp; workflowData.jsonPath("$.workflowData.currentExecution.executionData.isKycRequired").stringValue() == 'true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_16prd1l">
      <bpmn:incoming>Flow_1myx5r1</bpmn:incoming>
      <bpmn:incoming>Flow_0kui9u9</bpmn:incoming>
      <bpmn:outgoing>Flow_0x56049</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0x56049" sourceRef="Gateway_16prd1l" targetRef="Gateway_1o6gpc5" />
    <bpmn:exclusiveGateway id="Gateway_1v39tii" default="Flow_1myx5r1">
      <bpmn:incoming>Flow_11e61cz</bpmn:incoming>
      <bpmn:outgoing>Flow_1myx5r1</bpmn:outgoing>
      <bpmn:outgoing>Flow_11jaoq5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1myx5r1" sourceRef="Gateway_1v39tii" targetRef="Gateway_16prd1l" />
    <bpmn:sequenceFlow id="Flow_0kui9u9" sourceRef="Gateway_0mnu01m" targetRef="Gateway_16prd1l" />
    <bpmn:endEvent id="Event_1gxkm8b">
      <bpmn:incoming>Flow_11jaoq5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_11jaoq5" sourceRef="Gateway_1v39tii" targetRef="Event_1gxkm8b">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="ESBEkycUpdate" name="ESB_EkycUpdate" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1np7d9q</bpmn:incoming>
      <bpmn:outgoing>Flow_11e61cz</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_11e61cz" sourceRef="ESBEkycUpdate" targetRef="Gateway_1v39tii" />
    <bpmn:exclusiveGateway id="Gateway_1p1ex8z" camunda:asyncBefore="true" default="Flow_0oabxmj">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.CallBackExecutionListener" event="start" />
        <camunda:properties>
          <camunda:property name="callBackType" value="SOMCallback" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1pjlej8</bpmn:incoming>
      <bpmn:outgoing>Flow_1pstdyk</bpmn:outgoing>
      <bpmn:outgoing>Flow_0oabxmj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1pstdyk" name="callBackProcessReqd=true" sourceRef="Gateway_1p1ex8z" targetRef="SOMCallback">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callBackProcessReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_11gbukd">
      <bpmn:incoming>Flow_1xntscb</bpmn:incoming>
      <bpmn:incoming>Flow_0oabxmj</bpmn:incoming>
      <bpmn:outgoing>Flow_1ervo3l</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_16nnuen" default="Flow_1xntscb">
      <bpmn:incoming>Flow_1s0pp0i</bpmn:incoming>
      <bpmn:outgoing>Flow_1xntscb</bpmn:outgoing>
      <bpmn:outgoing>Flow_0shxurl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1xntscb" sourceRef="Gateway_16nnuen" targetRef="Gateway_11gbukd" />
    <bpmn:sequenceFlow id="Flow_0oabxmj" sourceRef="Gateway_1p1ex8z" targetRef="Gateway_11gbukd" />
    <bpmn:endEvent id="Event_1gnb34g">
      <bpmn:incoming>Flow_0shxurl</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0shxurl" name="Failure" sourceRef="Gateway_16nnuen" targetRef="Event_1gnb34g">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ervo3l" sourceRef="Gateway_11gbukd" targetRef="ESBCreateSubscriber" />
    <bpmn:textAnnotation id="TextAnnotation_1tmjhro">
      <bpmn:text>to skip the call back if the call back request is already present in the system</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1ck06fq" associationDirection="None" sourceRef="Flow_19b593d" targetRef="TextAnnotation_1tmjhro" />
    <bpmn:textAnnotation id="TextAnnotation_1yajmgn">
      <bpmn:text>SimDelivery = false for the batch case</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0onmi5s" associationDirection="None" sourceRef="Flow_0bnkzcu" targetRef="TextAnnotation_1yajmgn" />
    <bpmn:textAnnotation id="TextAnnotation_04cdv6g">
      <bpmn:text>SimDelivery = false for the batch case</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0phh4g6" associationDirection="None" sourceRef="Flow_1fxauve" targetRef="TextAnnotation_04cdv6g" />
  </bpmn:process>
  <bpmn:message id="Message_1m2g4r3" name="FlowOneCallback" />
  <bpmn:message id="Message_1y1dj88" name="SimDeliveryCallback" />
  <bpmn:message id="Message_1lbkhkd" name="SOMCallback" />
  <bpmn:message id="Message_2ipngvh" name="ESBEsimBlockCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Onboarding-ServiceProvisioning">
      <bpmndi:BPMNShape id="Activity_1ff1mxo_di" bpmnElement="BSCreateService">
        <dc:Bounds x="1850" y="496" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1meylc0_di" bpmnElement="Gateway_1meylc0" isMarkerVisible="true">
        <dc:Bounds x="2025" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0rw1vei_di" bpmnElement="Event_0rw1vei">
        <dc:Bounds x="2032" y="642" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_03l5qgl_di" bpmnElement="SimDeliveryCallback">
        <dc:Bounds x="2520" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1vr41hn_di" bpmnElement="Gateway_1vr41hn" isMarkerVisible="true">
        <dc:Bounds x="2675" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0yq91sr_di" bpmnElement="Event_0yq91sr">
        <dc:Bounds x="2682" y="672" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1s98h1x_di" bpmnElement="Gateway_1s98h1x" isMarkerVisible="true">
        <dc:Bounds x="6523" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0li77bm_di" bpmnElement="Gateway_0li77bm" isMarkerVisible="true">
        <dc:Bounds x="6823" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1m499j7_di" bpmnElement="Gateway_1m499j7" isMarkerVisible="true">
        <dc:Bounds x="5345" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1lvyzky_di" bpmnElement="subProcessEndEvent">
        <dc:Bounds x="8512" y="518" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1loua6q_di" bpmnElement="BSAddSubscription">
        <dc:Bounds x="5190" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0xipuav_di" bpmnElement="BSActivateService">
        <dc:Bounds x="6968" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1mu71an_di" bpmnElement="Event_1mu71an">
        <dc:Bounds x="6530" y="688" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0xlwtxa_di" bpmnElement="Event_0xlwtxa">
        <dc:Bounds x="6830" y="688" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1vihaa4_di" bpmnElement="Event_1vihaa4">
        <dc:Bounds x="5352" y="688" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1b4c33j_di" bpmnElement="Gateway_1b4c33j" isMarkerVisible="true">
        <dc:Bounds x="7123" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0okg50j_di" bpmnElement="Event_0okg50j">
        <dc:Bounds x="7130" y="702" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ttj65s" bpmnElement="SOMCallback">
        <dc:Bounds x="5860" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_03y9xye_di" bpmnElement="Gateway_03y9xye" isMarkerVisible="true">
        <dc:Bounds x="5625" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1b0icqf_di" bpmnElement="SOM_Provisioning">
        <dc:Bounds x="5450" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1lilan5_di" bpmnElement="Event_1lilan5">
        <dc:Bounds x="5632" y="688" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0933jlf" bpmnElement="start">
        <dc:Bounds x="152" y="518" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_06972gh_di" bpmnElement="Gateway_06972gh" isMarkerVisible="true">
        <dc:Bounds x="2255" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16rvaj0" bpmnElement="ESBCreateSubscriber">
        <dc:Bounds x="6348" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rkhdmw" bpmnElement="OCSOfferActivationProcess">
        <dc:Bounds x="6648" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_01zcgdg_di" bpmnElement="Gateway_01zcgdg" isMarkerVisible="true">
        <dc:Bounds x="1235" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_10vx64w_di" bpmnElement="Gateway_10vx64w" isMarkerVisible="true">
        <dc:Bounds x="1645" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0h08wow_di" bpmnElement="Gateway_0h08wow" isMarkerVisible="true">
        <dc:Bounds x="1115" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_132ue39_di" bpmnElement="Event_132ue39">
        <dc:Bounds x="1122" y="632" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_01xdppj_di" bpmnElement="Gateway_01xdppj" isMarkerVisible="true">
        <dc:Bounds x="1525" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_16xg273_di" bpmnElement="Event_16xg273">
        <dc:Bounds x="1532" y="622" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_097nh1b_di" bpmnElement="NMSFetchAsset">
        <dc:Bounds x="950" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_056rye4_di" bpmnElement="NMSBlockSim">
        <dc:Bounds x="1350" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1w48wao_di" bpmnElement="Gateway_1w48wao" isMarkerVisible="true">
        <dc:Bounds x="825" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vabjfy_di" bpmnElement="Gateway_0vabjfy" isMarkerVisible="true">
        <dc:Bounds x="1745" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0bec7tk" bpmnElement="PairSimInNMS">
        <dc:Bounds x="4220" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06r9p3d" bpmnElement="Gateway_0g8d0bs" isMarkerVisible="true">
        <dc:Bounds x="4355" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0xvgq47_di" bpmnElement="Event_0xvgq47">
        <dc:Bounds x="4362" y="688" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1j2pnf0_di" bpmnElement="Gateway_1j2pnf0" isMarkerVisible="true">
        <dc:Bounds x="2795" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1u6w8e4_di" bpmnElement="Gateway_1u6w8e4" isMarkerVisible="true">
        <dc:Bounds x="2365" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0occvxv_di" bpmnElement="Gateway_0occvxv" isMarkerVisible="true">
        <dc:Bounds x="2995" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_00nyacl" bpmnElement="Gateway_03bi9vv" isMarkerVisible="true">
        <dc:Bounds x="285" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_15cvepp" bpmnElement="Gateway_1r0z53e" isMarkerVisible="true">
        <dc:Bounds x="615" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1d8u7l1" bpmnElement="Gateway_13lxypt" isMarkerVisible="true">
        <dc:Bounds x="725" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1q0ybfl" bpmnElement="Arm_FetchAssetDetails">
        <dc:Bounds x="440" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1tvloa9" bpmnElement="Event_05hmwxi">
        <dc:Bounds x="622" y="652" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ii40rt" bpmnElement="Gateway_1cr1o1g" isMarkerVisible="true">
        <dc:Bounds x="2135" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0yfh58o" bpmnElement="Gateway_0iwpaxl" isMarkerVisible="true">
        <dc:Bounds x="3175" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1iuqg0b" bpmnElement="Gateway_0vd9u9k" isMarkerVisible="true">
        <dc:Bounds x="3335" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ty628l" bpmnElement="Gateway_1w29n8p" isMarkerVisible="true">
        <dc:Bounds x="4095" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0js7eg2" bpmnElement="Esb_blockEsim">
        <dc:Bounds x="3510" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0mqzllk" bpmnElement="Gateway_14c69zm" isMarkerVisible="true">
        <dc:Bounds x="3665" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0zrhuu1" bpmnElement="Gateway_1m12f1z" isMarkerVisible="true">
        <dc:Bounds x="3975" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0rfzvc4" bpmnElement="ESBEsimBlockCallback">
        <dc:Bounds x="3800" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_198ne03" bpmnElement="Event_1ystiom">
        <dc:Bounds x="3982" y="688" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0exxakt" bpmnElement="Event_0g8kkey">
        <dc:Bounds x="3672" y="688" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1f9csao" bpmnElement="Esb_fetchEsimDetails">
        <dc:Bounds x="7860" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1t4oh5f" bpmnElement="Gateway_00o8s29" isMarkerVisible="true">
        <dc:Bounds x="8005" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0csvsdp" bpmnElement="Esb_Fetch_EsimCallback">
        <dc:Bounds x="8110" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0nq4ewc" bpmnElement="Event_02k1i13">
        <dc:Bounds x="8012" y="688" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ld7a6p" bpmnElement="Gateway_1o6gpc5" isMarkerVisible="true">
        <dc:Bounds x="7735" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_14gqx4r" bpmnElement="Gateway_12nbj64" isMarkerVisible="true">
        <dc:Bounds x="8425" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1965spd" bpmnElement="Gateway_1xk8mp9" isMarkerVisible="true">
        <dc:Bounds x="8295" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0skcctn" bpmnElement="Event_1j5xx9l">
        <dc:Bounds x="8302" y="672" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0639ev2_di" bpmnElement="Gateway_0639ev2" isMarkerVisible="true">
        <dc:Bounds x="4505" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0hk4ak5_di" bpmnElement="Gateway_0hk4ak5" isMarkerVisible="true">
        <dc:Bounds x="4605" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0kjqo20_di" bpmnElement="Gateway_0kjqo20" isMarkerVisible="true">
        <dc:Bounds x="4875" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0c7a5e6_di" bpmnElement="Gateway_0c7a5e6" isMarkerVisible="true">
        <dc:Bounds x="4965" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0c7lxl0_di" bpmnElement="Gateway_0c7lxl0" isMarkerVisible="true">
        <dc:Bounds x="5065" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0xbmvri_di" bpmnElement="Event_0xbmvri">
        <dc:Bounds x="4882" y="688" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1kjooxa_di" bpmnElement="BS_ChangeSim">
        <dc:Bounds x="4720" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1fld2zu" bpmnElement="Gateway_0mnu01m" isMarkerVisible="true">
        <dc:Bounds x="7255" y="511" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7241" y="568" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0dyuu41" bpmnElement="Gateway_16prd1l" isMarkerVisible="true">
        <dc:Bounds x="7645" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ivj4ed" bpmnElement="Gateway_1v39tii" isMarkerVisible="true">
        <dc:Bounds x="7555" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1gxkm8b_di" bpmnElement="Event_1gxkm8b">
        <dc:Bounds x="7562" y="688" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13hmgme" bpmnElement="ESBEkycUpdate">
        <dc:Bounds x="7370" y="496" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_01wmun7" bpmnElement="Gateway_1p1ex8z" isMarkerVisible="true">
        <dc:Bounds x="5735" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_053rg9u" bpmnElement="Gateway_11gbukd" isMarkerVisible="true">
        <dc:Bounds x="6185" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1lhq54h" bpmnElement="Gateway_16nnuen" isMarkerVisible="true">
        <dc:Bounds x="6045" y="511" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rzeyjh" bpmnElement="Event_1gnb34g">
        <dc:Bounds x="6052" y="688" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_1tmjhro_di" bpmnElement="TextAnnotation_1tmjhro">
        <dc:Bounds x="2610" y="280" width="100" height="85" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_1yajmgn_di" bpmnElement="TextAnnotation_1yajmgn">
        <dc:Bounds x="2860" y="190" width="99.99241507552793" height="53" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_04cdv6g_di" bpmnElement="TextAnnotation_04cdv6g">
        <dc:Bounds x="4840" y="310" width="100" height="53" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_13uxqks_di" bpmnElement="Flow_13uxqks">
        <di:waypoint x="1950" y="536" />
        <di:waypoint x="2025" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xpvbhd_di" bpmnElement="Flow_0xpvbhd">
        <di:waypoint x="2050" y="561" />
        <di:waypoint x="2050" y="642" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2023" y="597" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04ihxlw_di" bpmnElement="Flow_04ihxlw">
        <di:waypoint x="2620" y="536" />
        <di:waypoint x="2675" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_009rhwp_di" bpmnElement="Flow_009rhwp">
        <di:waypoint x="2700" y="561" />
        <di:waypoint x="2700" y="672" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2662" y="597" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rpq9ua_di" bpmnElement="Flow_1rpq9ua">
        <di:waypoint x="6573" y="536" />
        <di:waypoint x="6648" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_161bbrt_di" bpmnElement="Flow_161bbrt">
        <di:waypoint x="5290" y="536" />
        <di:waypoint x="5345" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09ojkbr_di" bpmnElement="Flow_09ojkbr">
        <di:waypoint x="6548" y="561" />
        <di:waypoint x="6548" y="688" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6547" y="622" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_145yh0o_di" bpmnElement="Flow_145yh0o">
        <di:waypoint x="6848" y="561" />
        <di:waypoint x="6848" y="688" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6847" y="622" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10nxq9p_di" bpmnElement="Flow_10nxq9p">
        <di:waypoint x="5370" y="561" />
        <di:waypoint x="5370" y="688" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5370" y="622" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0667v8w_di" bpmnElement="Flow_0667v8w">
        <di:waypoint x="7068" y="536" />
        <di:waypoint x="7123" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02z6pdv_di" bpmnElement="Flow_02z6pdv">
        <di:waypoint x="7148" y="561" />
        <di:waypoint x="7148" y="702" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7147" y="629" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s0pp0i_di" bpmnElement="Flow_1s0pp0i">
        <di:waypoint x="5960" y="536" />
        <di:waypoint x="6045" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02dzn3n_di" bpmnElement="Flow_02dzn3n">
        <di:waypoint x="5550" y="536" />
        <di:waypoint x="5625" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pjlej8_di" bpmnElement="Flow_1pjlej8">
        <di:waypoint x="5675" y="536" />
        <di:waypoint x="5735" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00t33jz_di" bpmnElement="Flow_00t33jz">
        <di:waypoint x="5650" y="561" />
        <di:waypoint x="5650" y="688" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5648" y="622" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dm1yfp_di" bpmnElement="Flow_1dm1yfp">
        <di:waypoint x="2075" y="536" />
        <di:waypoint x="2135" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rlvo2k_di" bpmnElement="Flow_1rlvo2k">
        <di:waypoint x="7173" y="536" />
        <di:waypoint x="7255" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13m76f2_di" bpmnElement="Flow_13m76f2">
        <di:waypoint x="6873" y="536" />
        <di:waypoint x="6968" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lj84w5_di" bpmnElement="Flow_1lj84w5">
        <di:waypoint x="5395" y="536" />
        <di:waypoint x="5450" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k39dhe_di" bpmnElement="Flow_1k39dhe">
        <di:waypoint x="6448" y="536" />
        <di:waypoint x="6523" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qg9rbz_di" bpmnElement="Flow_1qg9rbz">
        <di:waypoint x="6748" y="536" />
        <di:waypoint x="6823" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tzqjw4_di" bpmnElement="Flow_1tzqjw4">
        <di:waypoint x="1050" y="536" />
        <di:waypoint x="1115" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rjnmgn_di" bpmnElement="Flow_1rjnmgn">
        <di:waypoint x="1450" y="536" />
        <di:waypoint x="1525" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rpzomy_di" bpmnElement="Flow_1rpzomy">
        <di:waypoint x="1285" y="536" />
        <di:waypoint x="1350" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00xq99i_di" bpmnElement="Flow_00xq99i">
        <di:waypoint x="1695" y="536" />
        <di:waypoint x="1745" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dvdnvx_di" bpmnElement="Flow_0dvdnvx">
        <di:waypoint x="1165" y="536" />
        <di:waypoint x="1235" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hngoqn_di" bpmnElement="Flow_0hngoqn">
        <di:waypoint x="1140" y="561" />
        <di:waypoint x="1140" y="632" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ybtsdi_di" bpmnElement="Flow_0ybtsdi">
        <di:waypoint x="1575" y="536" />
        <di:waypoint x="1645" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oe0lgp_di" bpmnElement="Flow_1oe0lgp">
        <di:waypoint x="1550" y="561" />
        <di:waypoint x="1550" y="622" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qd6uha_di" bpmnElement="Flow_1qd6uha">
        <di:waypoint x="1260" y="511" />
        <di:waypoint x="1260" y="440" />
        <di:waypoint x="1670" y="440" />
        <di:waypoint x="1670" y="511" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1u54nz3_di" bpmnElement="Flow_1u54nz3">
        <di:waypoint x="875" y="536" />
        <di:waypoint x="950" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18ife0f_di" bpmnElement="Flow_18ife0f">
        <di:waypoint x="1795" y="536" />
        <di:waypoint x="1850" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b1il50_di" bpmnElement="Flow_0b1il50">
        <di:waypoint x="850" y="511" />
        <di:waypoint x="850" y="380" />
        <di:waypoint x="1770" y="380" />
        <di:waypoint x="1770" y="511" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o1j51c_di" bpmnElement="Flow_0o1j51c">
        <di:waypoint x="4320" y="536" />
        <di:waypoint x="4355" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1716s36_di" bpmnElement="Flow_1716s36">
        <di:waypoint x="4380" y="561" />
        <di:waypoint x="4380" y="688" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00abri6_di" bpmnElement="Flow_00abri6">
        <di:waypoint x="188" y="536" />
        <di:waypoint x="285" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yt2zj6_di" bpmnElement="Flow_0yt2zj6">
        <di:waypoint x="2725" y="536" />
        <di:waypoint x="2795" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14msemw_di" bpmnElement="Flow_14msemw">
        <di:waypoint x="2305" y="536" />
        <di:waypoint x="2365" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1989ct4_di" bpmnElement="Flow_1989ct4">
        <di:waypoint x="2415" y="536" />
        <di:waypoint x="2520" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19b593d_di" bpmnElement="Flow_19b593d">
        <di:waypoint x="2390" y="511" />
        <di:waypoint x="2390" y="410" />
        <di:waypoint x="2820" y="410" />
        <di:waypoint x="2820" y="511" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ppixsr_di" bpmnElement="Flow_0ppixsr">
        <di:waypoint x="3045" y="536" />
        <di:waypoint x="3175" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bnkzcu_di" bpmnElement="Flow_0bnkzcu">
        <di:waypoint x="2280" y="511" />
        <di:waypoint x="2280" y="260" />
        <di:waypoint x="3020" y="260" />
        <di:waypoint x="3020" y="511" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bpomgc_di" bpmnElement="Flow_1bpomgc">
        <di:waypoint x="335" y="536" />
        <di:waypoint x="440" y="536" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="272" y="563" width="75" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dr62wy_di" bpmnElement="Flow_1dr62wy">
        <di:waypoint x="665" y="536" />
        <di:waypoint x="725" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fby7ln_di" bpmnElement="Flow_1fby7ln">
        <di:waypoint x="775" y="536" />
        <di:waypoint x="825" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1v25ynt_di" bpmnElement="Flow_1v25ynt">
        <di:waypoint x="540" y="536" />
        <di:waypoint x="615" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10ov43z_di" bpmnElement="Flow_10ov43z">
        <di:waypoint x="310" y="511" />
        <di:waypoint x="310" y="390" />
        <di:waypoint x="750" y="390" />
        <di:waypoint x="750" y="511" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lj87y7_di" bpmnElement="Flow_0lj87y7">
        <di:waypoint x="640" y="561" />
        <di:waypoint x="640" y="652" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1self6d_di" bpmnElement="Flow_1self6d">
        <di:waypoint x="2185" y="536" />
        <di:waypoint x="2255" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_128v3m5_di" bpmnElement="Flow_128v3m5">
        <di:waypoint x="3225" y="536" />
        <di:waypoint x="3335" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cnvklw_di" bpmnElement="Flow_0cnvklw">
        <di:waypoint x="2160" y="511" />
        <di:waypoint x="2160" y="100" />
        <di:waypoint x="3200" y="100" />
        <di:waypoint x="3200" y="511" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2644" y="82" width="75" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e4om3q_di" bpmnElement="Flow_0e4om3q">
        <di:waypoint x="3385" y="536" />
        <di:waypoint x="3510" y="536" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3357" y="563" width="75" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bgi1ih_di" bpmnElement="Flow_1bgi1ih">
        <di:waypoint x="4145" y="536" />
        <di:waypoint x="4220" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08xt043_di" bpmnElement="Flow_08xt043">
        <di:waypoint x="3610" y="536" />
        <di:waypoint x="3665" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c9tdm6_di" bpmnElement="Flow_1c9tdm6">
        <di:waypoint x="3715" y="536" />
        <di:waypoint x="3800" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fk6vpr_di" bpmnElement="Flow_0fk6vpr">
        <di:waypoint x="4025" y="536" />
        <di:waypoint x="4095" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0py3vtd_di" bpmnElement="Flow_0py3vtd">
        <di:waypoint x="3900" y="536" />
        <di:waypoint x="3975" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yd5kw0_di" bpmnElement="Flow_0yd5kw0">
        <di:waypoint x="3360" y="511" />
        <di:waypoint x="3360" y="410" />
        <di:waypoint x="4120" y="410" />
        <di:waypoint x="4120" y="511" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18k02d2_di" bpmnElement="Flow_18k02d2">
        <di:waypoint x="4000" y="561" />
        <di:waypoint x="4000" y="688" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kt99hu_di" bpmnElement="Flow_1kt99hu">
        <di:waypoint x="3690" y="561" />
        <di:waypoint x="3690" y="688" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1d9u5f5_di" bpmnElement="Flow_1d9u5f5">
        <di:waypoint x="7960" y="536" />
        <di:waypoint x="8005" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ztf3r1_di" bpmnElement="Flow_0ztf3r1">
        <di:waypoint x="8055" y="536" />
        <di:waypoint x="8110" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fiogz9_di" bpmnElement="Flow_1fiogz9">
        <di:waypoint x="8030" y="561" />
        <di:waypoint x="8030" y="688" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8017" y="649" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yy9ke2_di" bpmnElement="Flow_0yy9ke2">
        <di:waypoint x="8210" y="536" />
        <di:waypoint x="8295" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c0jh7u_di" bpmnElement="Flow_1c0jh7u">
        <di:waypoint x="7785" y="536" />
        <di:waypoint x="7860" y="536" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="7777" y="553" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05slb4h_di" bpmnElement="Flow_05slb4h">
        <di:waypoint x="8475" y="536" />
        <di:waypoint x="8512" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1svf9by_di" bpmnElement="Flow_1svf9by">
        <di:waypoint x="8345" y="536" />
        <di:waypoint x="8425" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10etk9s_di" bpmnElement="Flow_10etk9s">
        <di:waypoint x="8320" y="561" />
        <di:waypoint x="8320" y="672" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="8319" y="612" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k6th72_di" bpmnElement="Flow_1k6th72">
        <di:waypoint x="7760" y="511" />
        <di:waypoint x="7760" y="390" />
        <di:waypoint x="8450" y="390" />
        <di:waypoint x="8450" y="511" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0shgi8q_di" bpmnElement="Flow_0shgi8q">
        <di:waypoint x="4405" y="536" />
        <di:waypoint x="4505" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jsyw9d_di" bpmnElement="Flow_1jsyw9d">
        <di:waypoint x="4555" y="536" />
        <di:waypoint x="4605" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06ns6fu_di" bpmnElement="Flow_06ns6fu">
        <di:waypoint x="4655" y="536" />
        <di:waypoint x="4720" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b0cjxk_di" bpmnElement="Flow_0b0cjxk">
        <di:waypoint x="4820" y="536" />
        <di:waypoint x="4875" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07n5dys_di" bpmnElement="Flow_07n5dys">
        <di:waypoint x="4925" y="536" />
        <di:waypoint x="4965" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jq170y_di" bpmnElement="Flow_0jq170y">
        <di:waypoint x="5015" y="536" />
        <di:waypoint x="5065" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11hbrez_di" bpmnElement="Flow_11hbrez">
        <di:waypoint x="4530" y="511" />
        <di:waypoint x="4530" y="290" />
        <di:waypoint x="5090" y="290" />
        <di:waypoint x="5090" y="511" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4773" y="272" width="75" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fxauve_di" bpmnElement="Flow_1fxauve">
        <di:waypoint x="4630" y="511" />
        <di:waypoint x="4630" y="380" />
        <di:waypoint x="4990" y="380" />
        <di:waypoint x="4990" y="511" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4837" y="330" width="87" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pccg9a_di" bpmnElement="Flow_0pccg9a">
        <di:waypoint x="4900" y="561" />
        <di:waypoint x="4900" y="688" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4898" y="624" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qe1s2x_di" bpmnElement="Flow_1qe1s2x">
        <di:waypoint x="2845" y="536" />
        <di:waypoint x="2995" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dshgiy_di" bpmnElement="Flow_1dshgiy">
        <di:waypoint x="5115" y="536" />
        <di:waypoint x="5190" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1np7d9q_di" bpmnElement="Flow_1np7d9q">
        <di:waypoint x="7305" y="536" />
        <di:waypoint x="7370" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x56049_di" bpmnElement="Flow_0x56049">
        <di:waypoint x="7695" y="536" />
        <di:waypoint x="7735" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1myx5r1_di" bpmnElement="Flow_1myx5r1">
        <di:waypoint x="7605" y="536" />
        <di:waypoint x="7645" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kui9u9_di" bpmnElement="Flow_0kui9u9">
        <di:waypoint x="7280" y="511" />
        <di:waypoint x="7280" y="410" />
        <di:waypoint x="7670" y="410" />
        <di:waypoint x="7670" y="511" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11jaoq5_di" bpmnElement="Flow_11jaoq5">
        <di:waypoint x="7580" y="561" />
        <di:waypoint x="7580" y="688" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11e61cz_di" bpmnElement="Flow_11e61cz">
        <di:waypoint x="7470" y="536" />
        <di:waypoint x="7555" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pstdyk_di" bpmnElement="Flow_1pstdyk">
        <di:waypoint x="5785" y="536" />
        <di:waypoint x="5860" y="536" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="5719" y="568" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xntscb_di" bpmnElement="Flow_1xntscb">
        <di:waypoint x="6095" y="536" />
        <di:waypoint x="6185" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oabxmj_di" bpmnElement="Flow_0oabxmj">
        <di:waypoint x="5760" y="511" />
        <di:waypoint x="5760" y="440" />
        <di:waypoint x="6210" y="440" />
        <di:waypoint x="6210" y="511" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0shxurl_di" bpmnElement="Flow_0shxurl">
        <di:waypoint x="6070" y="561" />
        <di:waypoint x="6070" y="688" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="6068" y="623" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ervo3l_di" bpmnElement="Flow_1ervo3l">
        <di:waypoint x="6235" y="536" />
        <di:waypoint x="6348" y="536" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_1ck06fq_di" bpmnElement="Association_1ck06fq">
        <di:waypoint x="2605" y="410" />
        <di:waypoint x="2648" y="365" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0onmi5s_di" bpmnElement="Association_0onmi5s">
        <di:waypoint x="2650" y="260" />
        <di:waypoint x="2860" y="233" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0phh4g6_di" bpmnElement="Association_0phh4g6">
        <di:waypoint x="4810" y="380" />
        <di:waypoint x="4840" y="360" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
