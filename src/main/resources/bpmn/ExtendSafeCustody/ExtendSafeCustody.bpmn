<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1h0hzym" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="ExtendSafeCustody" isExecutable="true" camunda:jobPriority="${priority}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0fvclkl</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0fvclkl" sourceRef="orderExecStart" targetRef="BSExtendSafeCustody" />
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_01ifxpb</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSExtendSafeCustody" name="Billing ExtendSafeCustody" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0fvclkl</bpmn:incoming>
      <bpmn:outgoing>Flow_01ifxpb</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_01ifxpb" sourceRef="BSExtendSafeCustody" targetRef="orderExecEnd" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ExtendSafeCustody">
      <bpmndi:BPMNEdge id="Flow_01ifxpb_di" bpmnElement="Flow_01ifxpb">
        <di:waypoint x="380" y="117" />
        <di:waypoint x="472" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fvclkl_di" bpmnElement="Flow_0fvclkl">
        <di:waypoint x="188" y="117" />
        <di:waypoint x="280" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0uleo63_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="472" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ftjzix_di" bpmnElement="BSExtendSafeCustody">
        <dc:Bounds x="280" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
