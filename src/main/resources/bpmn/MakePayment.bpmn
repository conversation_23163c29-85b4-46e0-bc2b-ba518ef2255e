<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.15.1">
  <bpmn:process id="MakePayment" name="MakePayment" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_0v332gu</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="BillingMakePayment" name="Billing MakePayment" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1vjjg29</bpmn:incoming>
      <bpmn:outgoing>Flow_0tdphqs</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0tdphqs" sourceRef="BillingMakePayment" targetRef="Gateway_1o57io6" />
    <bpmn:exclusiveGateway id="Gateway_1o57io6" default="SequenceFlow_1q5ryub">
      <bpmn:incoming>Flow_0tdphqs</bpmn:incoming>
      <bpmn:outgoing>Flow_19d136j</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1q5ryub</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1dyqprn">
      <bpmn:incoming>Flow_19d136j</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_19d136j" name="Failure" sourceRef="Gateway_1o57io6" targetRef="Event_1dyqprn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>SequenceFlow_1q5ryub</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0v332gu" sourceRef="orderExecStart" targetRef="Gateway_0kaxbx2" />
    <bpmn:sequenceFlow id="SequenceFlow_1q5ryub" sourceRef="Gateway_1o57io6" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_1a5ev0z" sourceRef="ApiGwStripePayment" targetRef="Gateway_0qwb330" />
    <bpmn:exclusiveGateway id="Gateway_0kaxbx2" name="check if stripe call required (if paymentSubsId is present in order request)">
      <bpmn:incoming>Flow_0v332gu</bpmn:incoming>
      <bpmn:outgoing>Flow_0n1mg2u</bpmn:outgoing>
      <bpmn:outgoing>Flow_114zqnx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0n1mg2u" name="Yes" sourceRef="Gateway_0kaxbx2" targetRef="ApiGwStripePayment">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${pgwIntegration}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0ace7l4">
      <bpmn:incoming>Flow_114zqnx</bpmn:incoming>
      <bpmn:incoming>Flow_0isiz3w</bpmn:incoming>
      <bpmn:outgoing>Flow_1vjjg29</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1vjjg29" sourceRef="Gateway_0ace7l4" targetRef="BillingMakePayment" />
    <bpmn:sequenceFlow id="Flow_114zqnx" name="No" sourceRef="Gateway_0kaxbx2" targetRef="Gateway_0ace7l4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${!pgwIntegration}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="ApiGwStripePayment" name="Collect payment through Stripe PGW (via APIGW)" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0n1mg2u</bpmn:incoming>
      <bpmn:outgoing>Flow_1a5ev0z</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0qwb330" default="Flow_0isiz3w">
      <bpmn:incoming>Flow_1a5ev0z</bpmn:incoming>
      <bpmn:outgoing>Flow_0isiz3w</bpmn:outgoing>
      <bpmn:outgoing>Flow_0n0i2ac</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0isiz3w" name="Success" sourceRef="Gateway_0qwb330" targetRef="Gateway_0ace7l4" />
    <bpmn:endEvent id="Event_1f622u4">
      <bpmn:incoming>Flow_0n0i2ac</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0n0i2ac" name="Failure" sourceRef="Gateway_0qwb330" targetRef="Event_1f622u4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="MakePayment">
      <bpmndi:BPMNShape id="Event_0g25m9u_di" bpmnElement="orderExecStart">
        <dc:Bounds x="156" y="213" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_088djgj_di" bpmnElement="BillingMakePayment">
        <dc:Bounds x="840" y="191" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1o57io6_di" bpmnElement="Gateway_1o57io6" isMarkerVisible="true">
        <dc:Bounds x="1015" y="206" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1dyqprn_di" bpmnElement="Event_1dyqprn">
        <dc:Bounds x="1022" y="313" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1d72xko_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="1122" y="213" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0kaxbx2_di" bpmnElement="Gateway_0kaxbx2" isMarkerVisible="true">
        <dc:Bounds x="245" y="206" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="226" y="263" width="88" height="66" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ace7l4_di" bpmnElement="Gateway_0ace7l4" isMarkerVisible="true">
        <dc:Bounds x="705" y="206" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_17hf06g_di" bpmnElement="ApiGwStripePayment">
        <dc:Bounds x="360" y="191" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0qwb330_di" bpmnElement="Gateway_0qwb330" isMarkerVisible="true">
        <dc:Bounds x="535" y="206" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1f622u4_di" bpmnElement="Event_1f622u4">
        <dc:Bounds x="542" y="322" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0tdphqs_di" bpmnElement="Flow_0tdphqs">
        <di:waypoint x="940" y="231" />
        <di:waypoint x="1015" y="231" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19d136j_di" bpmnElement="Flow_19d136j">
        <di:waypoint x="1040" y="256" />
        <di:waypoint x="1040" y="313" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1053" y="290" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v332gu_di" bpmnElement="Flow_0v332gu">
        <di:waypoint x="192" y="231" />
        <di:waypoint x="245" y="231" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1q5ryub_di" bpmnElement="SequenceFlow_1q5ryub">
        <di:waypoint x="1065" y="231" />
        <di:waypoint x="1122" y="231" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a5ev0z_di" bpmnElement="Flow_1a5ev0z">
        <di:waypoint x="460" y="231" />
        <di:waypoint x="535" y="231" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n1mg2u_di" bpmnElement="Flow_0n1mg2u">
        <di:waypoint x="295" y="231" />
        <di:waypoint x="360" y="231" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="319" y="213" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vjjg29_di" bpmnElement="Flow_1vjjg29">
        <di:waypoint x="755" y="231" />
        <di:waypoint x="840" y="231" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_114zqnx_di" bpmnElement="Flow_114zqnx">
        <di:waypoint x="270" y="206" />
        <di:waypoint x="270" y="100" />
        <di:waypoint x="730" y="100" />
        <di:waypoint x="730" y="206" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="494" y="82" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0isiz3w_di" bpmnElement="Flow_0isiz3w">
        <di:waypoint x="585" y="231" />
        <di:waypoint x="705" y="231" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="624" y="213" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n0i2ac_di" bpmnElement="Flow_0n0i2ac">
        <di:waypoint x="560" y="256" />
        <di:waypoint x="560" y="322" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="573" y="289" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
