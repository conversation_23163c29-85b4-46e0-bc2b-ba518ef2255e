<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.28.0">
  <bpmn:process id="ExtendExpiryDate" name="ExtendExpiryDate" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:serviceTask id="GenerateDataDelegateTask" name="Generate Data Delegate" camunda:delegateExpression="${generateDataDelegateExtendExpiryDate}">
      <bpmn:incoming>Flow_006dlpa</bpmn:incoming>
      <bpmn:outgoing>Flow_16hv1kt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="CreateInstancesTask" name="Create Instances" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${createInstancesDelegateExtendExpiryDate}">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="childProcess">ExtendExpiryDate-SubFlow</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0f89hcs</bpmn:incoming>
      <bpmn:incoming>Flow_16hv1kt</bpmn:incoming>
      <bpmn:outgoing>Flow_0npos96</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0r0djc8" name="All instances created ?">
      <bpmn:incoming>Flow_0npos96</bpmn:incoming>
      <bpmn:outgoing>Flow_0xrnaaw</bpmn:outgoing>
      <bpmn:outgoing>Flow_0f89hcs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0xrnaaw</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_16hv1kt" sourceRef="GenerateDataDelegateTask" targetRef="CreateInstancesTask" />
    <bpmn:sequenceFlow id="Flow_0xrnaaw" name="Yes" sourceRef="Gateway_0r0djc8" targetRef="orderExecEnd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${allCreated}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0npos96" sourceRef="CreateInstancesTask" targetRef="Gateway_0r0djc8" />
    <bpmn:sequenceFlow id="Flow_0f89hcs" name="No" sourceRef="Gateway_0r0djc8" targetRef="CreateInstancesTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${not allCreated}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:callActivity id="PaymentWorkflow" name="Payment and future order wokrflow" camunda:asyncBefore="true" calledElement="PaymentWorkflow" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0i5r0hs</bpmn:incoming>
      <bpmn:outgoing>Flow_1h5mbos</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_1h5mbos" sourceRef="PaymentWorkflow" targetRef="Gateway_1kuelgu" />
    <bpmn:exclusiveGateway id="Gateway_1kuelgu" default="Flow_006dlpa">
      <bpmn:incoming>Flow_1h5mbos</bpmn:incoming>
      <bpmn:outgoing>Flow_006dlpa</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ollj5c</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_006dlpa" sourceRef="Gateway_1kuelgu" targetRef="GenerateDataDelegateTask" />
    <bpmn:endEvent id="Event_02szt1s">
      <bpmn:incoming>Flow_1ollj5c</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1ollj5c" sourceRef="Gateway_1kuelgu" targetRef="Event_02szt1s">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:startEvent id="orderExecStart" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_0i5r0hs</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0i5r0hs" sourceRef="orderExecStart" targetRef="PaymentWorkflow" />
  </bpmn:process>
  <bpmn:message id="Message_18lupa4" name="FlowOneAddSubCallback" />
  <bpmn:message id="Message_0kspzvs" name="${orderId}" />
  <bpmn:message id="Message_1ub3bvp" name="${orderId}" />
  <bpmn:message id="Message_0jnn3x7" name="${orderId}" />
  <bpmn:message id="Message_0uvxpri" name="multiCart" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ExtendExpiryDate">
      <bpmndi:BPMNShape id="Activity_1xs5z3a_di" bpmnElement="GenerateDataDelegateTask">
        <dc:Bounds x="680" y="99" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0z51h5s_di" bpmnElement="CreateInstancesTask">
        <dc:Bounds x="890" y="99" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0r0djc8_di" bpmnElement="Gateway_0r0djc8" isMarkerVisible="true">
        <dc:Bounds x="1085" y="114" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1079" y="84" width="62" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_044dwef_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="1222" y="121" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0u4fviu_di" bpmnElement="PaymentWorkflow">
        <dc:Bounds x="320" y="97" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1kuelgu_di" bpmnElement="Gateway_1kuelgu" isMarkerVisible="true">
        <dc:Bounds x="505" y="112" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_02szt1s_di" bpmnElement="Event_02szt1s">
        <dc:Bounds x="512" y="232" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1dykz4f_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="121" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_16hv1kt_di" bpmnElement="Flow_16hv1kt">
        <di:waypoint x="780" y="139" />
        <di:waypoint x="890" y="139" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xrnaaw_di" bpmnElement="Flow_0xrnaaw">
        <di:waypoint x="1135" y="139" />
        <di:waypoint x="1222" y="139" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1170" y="121" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0npos96_di" bpmnElement="Flow_0npos96">
        <di:waypoint x="990" y="139" />
        <di:waypoint x="1085" y="139" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f89hcs_di" bpmnElement="Flow_0f89hcs">
        <di:waypoint x="1110" y="164" />
        <di:waypoint x="1110" y="230" />
        <di:waypoint x="940" y="230" />
        <di:waypoint x="940" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1018" y="212" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h5mbos_di" bpmnElement="Flow_1h5mbos">
        <di:waypoint x="420" y="137" />
        <di:waypoint x="505" y="137" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_006dlpa_di" bpmnElement="Flow_006dlpa">
        <di:waypoint x="554" y="138" />
        <di:waypoint x="680" y="139" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ollj5c_di" bpmnElement="Flow_1ollj5c">
        <di:waypoint x="530" y="162" />
        <di:waypoint x="530" y="232" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0i5r0hs_di" bpmnElement="Flow_0i5r0hs">
        <di:waypoint x="188" y="139" />
        <di:waypoint x="320" y="140" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
