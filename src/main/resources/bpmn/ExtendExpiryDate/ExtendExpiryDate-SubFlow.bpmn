<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_077tjnv" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.28.0">
  <bpmn:process id="ExtendExpiryDate-SubFlow" name="ExtendExpiryDate-SubFlow" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="start" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_12egj9s</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_1waufjf" default="Flow_03gi279">
      <bpmn:incoming>Flow_135peys</bpmn:incoming>
      <bpmn:outgoing>Flow_0yut1v0</bpmn:outgoing>
      <bpmn:outgoing>Flow_03gi279</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="SOMExtendExpiryDate" name="SOM Extend ExpiryDate" camunda:asyncBefore="true" camunda:delegateExpression="${somExtendExpiryDate}">
      <bpmn:incoming>Flow_0lt2g3b</bpmn:incoming>
      <bpmn:outgoing>Flow_1va6qqc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0obp3bm" default="Flow_0p9axrf">
      <bpmn:incoming>Flow_1va6qqc</bpmn:incoming>
      <bpmn:outgoing>Flow_0ffbngx</bpmn:outgoing>
      <bpmn:outgoing>Flow_0p9axrf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1uv713u">
      <bpmn:incoming>Flow_0ffbngx</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1va6qqc" sourceRef="SOMExtendExpiryDate" targetRef="Gateway_0obp3bm" />
    <bpmn:sequenceFlow id="Flow_0ffbngx" name="Failure" sourceRef="Gateway_0obp3bm" targetRef="Event_1uv713u">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="OCSExtendExpiryDate" name="ExtendExpiryDate in OCS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_12egj9s</bpmn:incoming>
      <bpmn:outgoing>Flow_135peys</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_135peys" sourceRef="OCSExtendExpiryDate" targetRef="Gateway_1waufjf" />
    <bpmn:endEvent id="Event_0n98exd">
      <bpmn:incoming>Flow_0yut1v0</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0yut1v0" name="Failure" sourceRef="Gateway_1waufjf" targetRef="Event_0n98exd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="subProcessEndEvent">
      <bpmn:incoming>Flow_1bvci6d</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_12egj9s" sourceRef="start" targetRef="OCSExtendExpiryDate" />
    <bpmn:exclusiveGateway id="Gateway_15flrgg">
      <bpmn:incoming>Flow_06gpgjs</bpmn:incoming>
      <bpmn:incoming>Flow_0p9axrf</bpmn:incoming>
      <bpmn:outgoing>Flow_0xhl6f5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0xhl6f5" sourceRef="Gateway_15flrgg" targetRef="BS_ExtendExpiryDate" />
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch services" camunda:asyncBefore="true" camunda:delegateExpression="${somFetchSubscriptionIdDetails}">
      <bpmn:incoming>Flow_03gi279</bpmn:incoming>
      <bpmn:outgoing>Flow_144t09w</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_03gi279" sourceRef="Gateway_1waufjf" targetRef="SOMFetchServiceRegistry" />
    <bpmn:sequenceFlow id="Flow_144t09w" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_1r7rd4b" />
    <bpmn:exclusiveGateway id="Gateway_1r7rd4b" default="Flow_11qdh1p">
      <bpmn:incoming>Flow_144t09w</bpmn:incoming>
      <bpmn:outgoing>Flow_11qdh1p</bpmn:outgoing>
      <bpmn:outgoing>Flow_008aqkl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_11qdh1p" sourceRef="Gateway_1r7rd4b" targetRef="Gateway_088o080" />
    <bpmn:exclusiveGateway id="Gateway_088o080" name="shouldCallSom" default="Flow_06gpgjs">
      <bpmn:incoming>Flow_11qdh1p</bpmn:incoming>
      <bpmn:outgoing>Flow_0lt2g3b</bpmn:outgoing>
      <bpmn:outgoing>Flow_06gpgjs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0lt2g3b" sourceRef="Gateway_088o080" targetRef="SOMExtendExpiryDate">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${shouldCallSom}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1fuekmd">
      <bpmn:incoming>Flow_008aqkl</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_008aqkl" name="Failure" sourceRef="Gateway_1r7rd4b" targetRef="Event_1fuekmd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_06gpgjs" sourceRef="Gateway_088o080" targetRef="Gateway_15flrgg" />
    <bpmn:sequenceFlow id="Flow_0p9axrf" sourceRef="Gateway_0obp3bm" targetRef="Gateway_15flrgg" />
    <bpmn:serviceTask id="BS_ExtendExpiryDate" name="BS Extend ExpiryDate " camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0xhl6f5</bpmn:incoming>
      <bpmn:outgoing>Flow_0uwwfja</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0uwwfja" sourceRef="BS_ExtendExpiryDate" targetRef="Gateway_0efpej6" />
    <bpmn:exclusiveGateway id="Gateway_0efpej6" default="Flow_1bvci6d">
      <bpmn:incoming>Flow_0uwwfja</bpmn:incoming>
      <bpmn:outgoing>Flow_1bvci6d</bpmn:outgoing>
      <bpmn:outgoing>Flow_0f29oqi</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1bvci6d" sourceRef="Gateway_0efpej6" targetRef="subProcessEndEvent" />
    <bpmn:endEvent id="Event_1td82az">
      <bpmn:incoming>Flow_0f29oqi</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0f29oqi" name="Failure" sourceRef="Gateway_0efpej6" targetRef="Event_1td82az">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_08uiznb" name="SOMAddSubCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ExtendExpiryDate-SubFlow">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="start">
        <dc:Bounds x="162" y="159" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1waufjf_di" bpmnElement="Gateway_1waufjf" isMarkerVisible="true">
        <dc:Bounds x="545" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1blgwyz_di" bpmnElement="SOMExtendExpiryDate">
        <dc:Bounds x="1120" y="137" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0obp3bm_di" bpmnElement="Gateway_0obp3bm" isMarkerVisible="true">
        <dc:Bounds x="1305" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1uv713u_di" bpmnElement="Event_1uv713u">
        <dc:Bounds x="1312" y="272" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0mkfgdb_di" bpmnElement="OCSExtendExpiryDate">
        <dc:Bounds x="360" y="137" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0n98exd_di" bpmnElement="Event_0n98exd">
        <dc:Bounds x="552" y="284" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ccvmjb_di" bpmnElement="subProcessEndEvent">
        <dc:Bounds x="2112" y="159" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_15flrgg_di" bpmnElement="Gateway_15flrgg" isMarkerVisible="true">
        <dc:Bounds x="1535" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1v4jjfp_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="680" y="137" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0vn6kxy" bpmnElement="Gateway_1r7rd4b" isMarkerVisible="true">
        <dc:Bounds x="865" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1conbdt" bpmnElement="Gateway_088o080" isMarkerVisible="true">
        <dc:Bounds x="985" y="152" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="973" y="209" width="75" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_17cymvw" bpmnElement="Event_1fuekmd">
        <dc:Bounds x="872" y="284" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1y9umsa" bpmnElement="BS_ExtendExpiryDate">
        <dc:Bounds x="1740" y="137" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0y5pg8z" bpmnElement="Gateway_0efpej6" isMarkerVisible="true">
        <dc:Bounds x="1955" y="152" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0wpsdkw" bpmnElement="Event_1td82az">
        <dc:Bounds x="1962" y="272" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1va6qqc_di" bpmnElement="Flow_1va6qqc">
        <di:waypoint x="1220" y="177" />
        <di:waypoint x="1305" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ffbngx_di" bpmnElement="Flow_0ffbngx">
        <di:waypoint x="1330" y="202" />
        <di:waypoint x="1330" y="272" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1349" y="222" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_135peys_di" bpmnElement="Flow_135peys">
        <di:waypoint x="460" y="177" />
        <di:waypoint x="545" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yut1v0_di" bpmnElement="Flow_0yut1v0">
        <di:waypoint x="570" y="202" />
        <di:waypoint x="570" y="284" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="572" y="241" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12egj9s_di" bpmnElement="Flow_12egj9s">
        <di:waypoint x="198" y="177" />
        <di:waypoint x="360" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xhl6f5_di" bpmnElement="Flow_0xhl6f5">
        <di:waypoint x="1585" y="177" />
        <di:waypoint x="1740" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03gi279_di" bpmnElement="Flow_03gi279">
        <di:waypoint x="595" y="177" />
        <di:waypoint x="680" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_144t09w_di" bpmnElement="Flow_144t09w">
        <di:waypoint x="780" y="177" />
        <di:waypoint x="865" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11qdh1p_di" bpmnElement="Flow_11qdh1p">
        <di:waypoint x="915" y="177" />
        <di:waypoint x="985" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lt2g3b_di" bpmnElement="Flow_0lt2g3b">
        <di:waypoint x="1035" y="177" />
        <di:waypoint x="1120" y="177" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1040" y="159" width="75" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_008aqkl_di" bpmnElement="Flow_008aqkl">
        <di:waypoint x="890" y="202" />
        <di:waypoint x="890" y="284" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="888" y="240" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06gpgjs_di" bpmnElement="Flow_06gpgjs">
        <di:waypoint x="1010" y="152" />
        <di:waypoint x="1010" y="80" />
        <di:waypoint x="1560" y="80" />
        <di:waypoint x="1560" y="152" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p9axrf_di" bpmnElement="Flow_0p9axrf">
        <di:waypoint x="1355" y="177" />
        <di:waypoint x="1535" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0uwwfja_di" bpmnElement="Flow_0uwwfja">
        <di:waypoint x="1840" y="177" />
        <di:waypoint x="1955" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bvci6d_di" bpmnElement="Flow_1bvci6d">
        <di:waypoint x="2005" y="177" />
        <di:waypoint x="2112" y="177" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f29oqi_di" bpmnElement="Flow_0f29oqi">
        <di:waypoint x="1980" y="202" />
        <di:waypoint x="1980" y="272" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1978" y="234" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>