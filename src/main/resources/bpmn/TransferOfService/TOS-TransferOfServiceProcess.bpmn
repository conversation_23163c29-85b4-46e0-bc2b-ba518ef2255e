<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_168q52t" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.1.0">
  <bpmn:process id="TOS-TransferOfServiceProcess" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_1vqvjzl</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="BSTransferOfService" name="Billing Transfer of Service" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0jwwxdf</bpmn:incoming>
      <bpmn:outgoing>Flow_124cttg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1cygyis" default="Flow_1oze9a8">
      <bpmn:incoming>Flow_124cttg</bpmn:incoming>
      <bpmn:outgoing>Flow_0jh6nmn</bpmn:outgoing>
      <bpmn:outgoing>Flow_1oze9a8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1twex7y">
      <bpmn:incoming>Flow_0jh6nmn</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="OCSTransferOfService" name="OCS Transfer of Service" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1oze9a8</bpmn:incoming>
      <bpmn:outgoing>Flow_1vf1sc0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="LMSProfileUpdate" name="LMS Profile Update" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_09jx9dn</bpmn:incoming>
      <bpmn:outgoing>Flow_098yrgm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="subProcessEndEvent">
      <bpmn:incoming>Flow_0mlh34q</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1lnesd9" default="Flow_1vp1wku">
      <bpmn:incoming>Flow_098yrgm</bpmn:incoming>
      <bpmn:outgoing>Flow_1j9ch86</bpmn:outgoing>
      <bpmn:outgoing>Flow_1vp1wku</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0j7hxet">
      <bpmn:incoming>Flow_1j9ch86</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="OCSUpdateCreditLimit" name="OCS Update Credit Limit&#10;" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0dlhr3w</bpmn:incoming>
      <bpmn:outgoing>Flow_1chjbgf</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BillingUpdateCreditLimit" name="Billing UpdateCreditLimit" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0pboq73</bpmn:incoming>
      <bpmn:outgoing>Flow_18cmmfa</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0l8nofr" default="Flow_0pboq73">
      <bpmn:incoming>Flow_1chjbgf</bpmn:incoming>
      <bpmn:outgoing>Flow_0pboq73</bpmn:outgoing>
      <bpmn:outgoing>Flow_1jj4ga5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_00tlwhg">
      <bpmn:incoming>Flow_1jj4ga5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0tbvfws" default="Flow_1b36o3e">
      <bpmn:incoming>Flow_18cmmfa</bpmn:incoming>
      <bpmn:outgoing>Flow_1b36o3e</bpmn:outgoing>
      <bpmn:outgoing>Flow_14mw5fh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_01y52uv">
      <bpmn:incoming>Flow_14mw5fh</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0ziemfh" name="credit limit update req" default="Flow_07dby8n">
      <bpmn:incoming>Flow_07s5nfy</bpmn:incoming>
      <bpmn:outgoing>Flow_0dlhr3w</bpmn:outgoing>
      <bpmn:outgoing>Flow_07dby8n</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1pfugfd">
      <bpmn:incoming>Flow_1b36o3e</bpmn:incoming>
      <bpmn:incoming>Flow_07dby8n</bpmn:incoming>
      <bpmn:outgoing>Flow_05ja1mt</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0j7txkc" default="Flow_09jx9dn">
      <bpmn:incoming>Flow_1vf1sc0</bpmn:incoming>
      <bpmn:outgoing>Flow_09jx9dn</bpmn:outgoing>
      <bpmn:outgoing>Flow_1mpjk8j</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_12h4uuv">
      <bpmn:incoming>Flow_1mpjk8j</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_124cttg" sourceRef="BSTransferOfService" targetRef="Gateway_1cygyis" />
    <bpmn:sequenceFlow id="Flow_0jh6nmn" name="Failure" sourceRef="Gateway_1cygyis" targetRef="Event_1twex7y">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1oze9a8" name="Success" sourceRef="Gateway_1cygyis" targetRef="OCSTransferOfService" />
    <bpmn:sequenceFlow id="Flow_1vf1sc0" sourceRef="OCSTransferOfService" targetRef="Gateway_0j7txkc" />
    <bpmn:sequenceFlow id="Flow_09jx9dn" name="Success" sourceRef="Gateway_0j7txkc" targetRef="LMSProfileUpdate" />
    <bpmn:sequenceFlow id="Flow_098yrgm" sourceRef="LMSProfileUpdate" targetRef="Gateway_1lnesd9" />
    <bpmn:sequenceFlow id="Flow_1j9ch86" name="Failure" sourceRef="Gateway_1lnesd9" targetRef="Event_0j7hxet">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1vp1wku" sourceRef="Gateway_1lnesd9" targetRef="WSCTransferofService" />
    <bpmn:sequenceFlow id="Flow_0dlhr3w" name="yes" sourceRef="Gateway_0ziemfh" targetRef="OCSUpdateCreditLimit">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order.serviceManagement").element().hasProp('creditLimitUpdateReqd') &amp;&amp; workflowData.jsonPath("$.order.serviceManagement.creditLimitUpdateReqd").stringValue() == 'true'&amp;&amp; workflowData.jsonPath("$.order.serviceManagement").element().hasProp('creditLimit')}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1chjbgf" sourceRef="OCSUpdateCreditLimit" targetRef="Gateway_0l8nofr" />
    <bpmn:sequenceFlow id="Flow_0pboq73" name="Success" sourceRef="Gateway_0l8nofr" targetRef="BillingUpdateCreditLimit" />
    <bpmn:sequenceFlow id="Flow_18cmmfa" sourceRef="BillingUpdateCreditLimit" targetRef="Gateway_0tbvfws" />
    <bpmn:sequenceFlow id="Flow_1jj4ga5" name="Failure" sourceRef="Gateway_0l8nofr" targetRef="Event_00tlwhg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1b36o3e" name="success" sourceRef="Gateway_0tbvfws" targetRef="Gateway_1pfugfd" />
    <bpmn:sequenceFlow id="Flow_14mw5fh" name="Failure" sourceRef="Gateway_0tbvfws" targetRef="Event_01y52uv">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_07dby8n" name="no" sourceRef="Gateway_0ziemfh" targetRef="Gateway_1pfugfd" />
    <bpmn:sequenceFlow id="Flow_1mpjk8j" name="Failure" sourceRef="Gateway_0j7txkc" targetRef="Event_12h4uuv">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1vqvjzl" sourceRef="StartEvent_1" targetRef="BSFetchService" />
    <bpmn:serviceTask id="BSFetchService" name="Billing Fetch Service" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1vqvjzl</bpmn:incoming>
      <bpmn:outgoing>Flow_0f1soxc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0f1soxc" sourceRef="BSFetchService" targetRef="Gateway_05odi10" />
    <bpmn:exclusiveGateway id="Gateway_05odi10" default="Flow_0jwwxdf">
      <bpmn:incoming>Flow_0f1soxc</bpmn:incoming>
      <bpmn:outgoing>Flow_0jwwxdf</bpmn:outgoing>
      <bpmn:outgoing>Flow_1card6v</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0jwwxdf" name="Success" sourceRef="Gateway_05odi10" targetRef="BSTransferOfService" />
    <bpmn:endEvent id="Event_05v9ky7">
      <bpmn:incoming>Flow_1card6v</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1card6v" name="Failure" sourceRef="Gateway_05odi10" targetRef="Event_05v9ky7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="WSCTransferofService" name="WSC Transfer of Service" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1vp1wku</bpmn:incoming>
      <bpmn:outgoing>Flow_1m1n4g7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1m1n4g7" sourceRef="WSCTransferofService" targetRef="Gateway_1pfwi0z" />
    <bpmn:exclusiveGateway id="Gateway_1pfwi0z" default="Flow_07s5nfy">
      <bpmn:incoming>Flow_1m1n4g7</bpmn:incoming>
      <bpmn:outgoing>Flow_07s5nfy</bpmn:outgoing>
      <bpmn:outgoing>Flow_1604h96</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_07s5nfy" name="Success" sourceRef="Gateway_1pfwi0z" targetRef="Gateway_0ziemfh" />
    <bpmn:endEvent id="Event_1lpgel0">
      <bpmn:incoming>Flow_1604h96</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1604h96" name="Failure" sourceRef="Gateway_1pfwi0z" targetRef="Event_1lpgel0">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="NGWTransferOfService" name="NGW Transfer Of Service" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0m92bb2</bpmn:incoming>
      <bpmn:outgoing>Flow_03yeog1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_03fmo1s" default="Flow_0vxtz5u">
      <bpmn:incoming>SequenceFlow_1nsb2fi</bpmn:incoming>
      <bpmn:outgoing>Flow_0m92bb2</bpmn:outgoing>
      <bpmn:outgoing>Flow_0vxtz5u</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1tgku4g">
      <bpmn:incoming>Flow_0vxtz5u</bpmn:incoming>
      <bpmn:incoming>Flow_0rjedka</bpmn:incoming>
      <bpmn:outgoing>Flow_0mlh34q</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_04qlx17" default="Flow_0rjedka">
      <bpmn:incoming>Flow_03yeog1</bpmn:incoming>
      <bpmn:outgoing>Flow_0rjedka</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ddc48t</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_12yxsfp">
      <bpmn:incoming>Flow_1ddc48t</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0m92bb2" sourceRef="Gateway_03fmo1s" targetRef="NGWTransferOfService">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${deviceId}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_03yeog1" sourceRef="NGWTransferOfService" targetRef="Gateway_04qlx17" />
    <bpmn:sequenceFlow id="Flow_0vxtz5u" sourceRef="Gateway_03fmo1s" targetRef="Gateway_1tgku4g" />
    <bpmn:sequenceFlow id="Flow_0rjedka" name="Success" sourceRef="Gateway_04qlx17" targetRef="Gateway_1tgku4g" />
    <bpmn:sequenceFlow id="Flow_1ddc48t" name="Failure" sourceRef="Gateway_04qlx17" targetRef="Event_12yxsfp">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_05ja1mt" sourceRef="Gateway_1pfugfd" targetRef="NGWFetchDevice" />
    <bpmn:sequenceFlow id="Flow_0mlh34q" sourceRef="Gateway_1tgku4g" targetRef="subProcessEndEvent" />
    <bpmn:sequenceFlow id="SequenceFlow_1nsb2fi" sourceRef="NGWFetchDevice" targetRef="Gateway_03fmo1s" />
    <bpmn:serviceTask id="NGWFetchDevice" name="NGW Fetch Device" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_05ja1mt</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1nsb2fi</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="TOS-TransferOfServiceProcess">
      <bpmndi:BPMNEdge id="Flow_0mlh34q_di" bpmnElement="Flow_0mlh34q">
        <di:waypoint x="4169" y="120" />
        <di:waypoint x="4387" y="120" />
        <di:waypoint x="4387" y="222" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05ja1mt_di" bpmnElement="Flow_05ja1mt">
        <di:waypoint x="3165" y="247" />
        <di:waypoint x="3381" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ddc48t_di" bpmnElement="Flow_1ddc48t">
        <di:waypoint x="4147" y="265" />
        <di:waypoint x="4147" y="379" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4145" y="288" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rjedka_di" bpmnElement="Flow_0rjedka">
        <di:waypoint x="4147" y="215" />
        <di:waypoint x="4147" y="148" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4141" y="193" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vxtz5u_di" bpmnElement="Flow_0vxtz5u">
        <di:waypoint x="3837" y="222" />
        <di:waypoint x="3837" y="123" />
        <di:waypoint x="4122" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03yeog1_di" bpmnElement="Flow_03yeog1">
        <di:waypoint x="4047" y="247" />
        <di:waypoint x="4085" y="247" />
        <di:waypoint x="4085" y="240" />
        <di:waypoint x="4122" y="240" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0m92bb2_di" bpmnElement="Flow_0m92bb2">
        <di:waypoint x="3862" y="247" />
        <di:waypoint x="3947" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1604h96_di" bpmnElement="Flow_1604h96">
        <di:waypoint x="1850" y="272" />
        <di:waypoint x="1850" y="342" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1848" y="304" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07s5nfy_di" bpmnElement="Flow_07s5nfy">
        <di:waypoint x="1875" y="247" />
        <di:waypoint x="2115" y="247" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1974" y="229" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m1n4g7_di" bpmnElement="Flow_1m1n4g7">
        <di:waypoint x="1760" y="247" />
        <di:waypoint x="1825" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1card6v_di" bpmnElement="Flow_1card6v">
        <di:waypoint x="500" y="272" />
        <di:waypoint x="500" y="352" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="498" y="309" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jwwxdf_di" bpmnElement="Flow_0jwwxdf">
        <di:waypoint x="525" y="247" />
        <di:waypoint x="620" y="247" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="551" y="229" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f1soxc_di" bpmnElement="Flow_0f1soxc">
        <di:waypoint x="410" y="247" />
        <di:waypoint x="475" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vqvjzl_di" bpmnElement="Flow_1vqvjzl">
        <di:waypoint x="188" y="247" />
        <di:waypoint x="310" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mpjk8j_di" bpmnElement="Flow_1mpjk8j">
        <di:waypoint x="1180" y="272" />
        <di:waypoint x="1180" y="352" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1179" y="323" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07dby8n_di" bpmnElement="Flow_07dby8n">
        <di:waypoint x="2140" y="222" />
        <di:waypoint x="2140" y="97" />
        <di:waypoint x="3140" y="97" />
        <di:waypoint x="3140" y="222" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2634" y="78" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14mw5fh_di" bpmnElement="Flow_14mw5fh">
        <di:waypoint x="2960" y="272" />
        <di:waypoint x="2960" y="379" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2960" y="320" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b36o3e_di" bpmnElement="Flow_1b36o3e">
        <di:waypoint x="2985" y="247" />
        <di:waypoint x="3115" y="247" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3031" y="220" width="41" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jj4ga5_di" bpmnElement="Flow_1jj4ga5">
        <di:waypoint x="2540" y="272" />
        <di:waypoint x="2540" y="359" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2538" y="300" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18cmmfa_di" bpmnElement="Flow_18cmmfa">
        <di:waypoint x="2820" y="247" />
        <di:waypoint x="2935" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pboq73_di" bpmnElement="Flow_0pboq73">
        <di:waypoint x="2565" y="247" />
        <di:waypoint x="2720" y="247" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2622" y="229" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1chjbgf_di" bpmnElement="Flow_1chjbgf">
        <di:waypoint x="2380" y="247" />
        <di:waypoint x="2515" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dlhr3w_di" bpmnElement="Flow_0dlhr3w">
        <di:waypoint x="2165" y="247" />
        <di:waypoint x="2280" y="247" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2214" y="229" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vp1wku_di" bpmnElement="Flow_1vp1wku">
        <di:waypoint x="1535" y="247" />
        <di:waypoint x="1660" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1j9ch86_di" bpmnElement="Flow_1j9ch86">
        <di:waypoint x="1510" y="272" />
        <di:waypoint x="1510" y="359" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1513" y="301" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_098yrgm_di" bpmnElement="Flow_098yrgm">
        <di:waypoint x="1410" y="247" />
        <di:waypoint x="1485" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09jx9dn_di" bpmnElement="Flow_09jx9dn">
        <di:waypoint x="1205" y="247" />
        <di:waypoint x="1310" y="247" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1237" y="229" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vf1sc0_di" bpmnElement="Flow_1vf1sc0">
        <di:waypoint x="1040" y="247" />
        <di:waypoint x="1155" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oze9a8_di" bpmnElement="Flow_1oze9a8">
        <di:waypoint x="855" y="247" />
        <di:waypoint x="940" y="247" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="876" y="229" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jh6nmn_di" bpmnElement="Flow_0jh6nmn">
        <di:waypoint x="830" y="272" />
        <di:waypoint x="830" y="359" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="833" y="303" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_124cttg_di" bpmnElement="Flow_124cttg">
        <di:waypoint x="720" y="247" />
        <di:waypoint x="805" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="229" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1mqjxw9_di" bpmnElement="BSTransferOfService">
        <dc:Bounds x="620" y="207" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1cygyis_di" bpmnElement="Gateway_1cygyis" isMarkerVisible="true">
        <dc:Bounds x="805" y="222" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1twex7y_di" bpmnElement="Event_1twex7y">
        <dc:Bounds x="812" y="359" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_18xx8je_di" bpmnElement="OCSTransferOfService">
        <dc:Bounds x="940" y="207" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_06zqc8u_di" bpmnElement="LMSProfileUpdate">
        <dc:Bounds x="1310" y="207" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0uxlr6u_di" bpmnElement="subProcessEndEvent">
        <dc:Bounds x="4369" y="222" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1lnesd9_di" bpmnElement="Gateway_1lnesd9" isMarkerVisible="true">
        <dc:Bounds x="1485" y="222" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0j7hxet_di" bpmnElement="Event_0j7hxet">
        <dc:Bounds x="1492" y="359" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1l72p16_di" bpmnElement="OCSUpdateCreditLimit">
        <dc:Bounds x="2280" y="207" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_06vszky_di" bpmnElement="BillingUpdateCreditLimit">
        <dc:Bounds x="2720" y="207" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0l8nofr_di" bpmnElement="Gateway_0l8nofr" isMarkerVisible="true">
        <dc:Bounds x="2515" y="222" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_00tlwhg_di" bpmnElement="Event_00tlwhg">
        <dc:Bounds x="2522" y="359" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0tbvfws_di" bpmnElement="Gateway_0tbvfws" isMarkerVisible="true">
        <dc:Bounds x="2935" y="222" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_01y52uv_di" bpmnElement="Event_01y52uv">
        <dc:Bounds x="2942" y="379" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ziemfh_di" bpmnElement="Gateway_0ziemfh" isMarkerVisible="true">
        <dc:Bounds x="2115" y="222" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2098" y="279" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1pfugfd_di" bpmnElement="Gateway_1pfugfd" isMarkerVisible="true">
        <dc:Bounds x="3115" y="222" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0j7txkc_di" bpmnElement="Gateway_0j7txkc" isMarkerVisible="true">
        <dc:Bounds x="1155" y="222" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_12h4uuv_di" bpmnElement="Event_12h4uuv">
        <dc:Bounds x="1162" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1wy7bq7_di" bpmnElement="BSFetchService">
        <dc:Bounds x="310" y="207" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_05odi10_di" bpmnElement="Gateway_05odi10" isMarkerVisible="true">
        <dc:Bounds x="475" y="222" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_05v9ky7_di" bpmnElement="Event_05v9ky7">
        <dc:Bounds x="482" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0jkbvdl_di" bpmnElement="WSCTransferofService">
        <dc:Bounds x="1660" y="207" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1pfwi0z_di" bpmnElement="Gateway_1pfwi0z" isMarkerVisible="true">
        <dc:Bounds x="1825" y="222" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1lpgel0_di" bpmnElement="Event_1lpgel0">
        <dc:Bounds x="1832" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1khtl8c_di" bpmnElement="NGWTransferOfService">
        <dc:Bounds x="3947" y="207" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_03fmo1s_di" bpmnElement="Gateway_03fmo1s" isMarkerVisible="true">
        <dc:Bounds x="3812" y="222" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tgku4g_di" bpmnElement="Gateway_1tgku4g" isMarkerVisible="true">
        <dc:Bounds x="4122" y="98" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_04qlx17_di" bpmnElement="Gateway_04qlx17" isMarkerVisible="true">
        <dc:Bounds x="4122" y="215" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_12yxsfp_di" bpmnElement="Event_12yxsfp">
        <dc:Bounds x="4129" y="379" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1nsb2fi_di" bpmnElement="SequenceFlow_1nsb2fi">
        <di:waypoint x="3481" y="247" />
        <di:waypoint x="3812" y="247" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_1uvb987_di" bpmnElement="NGWFetchDevice">
        <dc:Bounds x="3381" y="207" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
