<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_0efb5vu" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.23.0">
  <bpmn:process id="TransferOfService" name="Transfer Of Service" isExecutable="true">
    <bpmn:startEvent id="orderExecStart" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_0nwsppc</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="BSTransferService" name="Transfer of Service in Billing " camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1vcc5ee</bpmn:incoming>
      <bpmn:outgoing>Flow_1ydhk0j</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0lf55lw" default="Flow_0f5mklo">
      <bpmn:incoming>Flow_1ydhk0j</bpmn:incoming>
      <bpmn:outgoing>Flow_1iuxa6f</bpmn:outgoing>
      <bpmn:outgoing>Flow_0f5mklo</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1wohbft">
      <bpmn:incoming>Flow_1iuxa6f</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="OCSDeactivateService" name="Deactivate Service in OCS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0ndcrh7</bpmn:incoming>
      <bpmn:outgoing>Flow_1qud553</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="OCSCreateService" name="Create Service in OCS with new account" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_07scj1m</bpmn:incoming>
      <bpmn:outgoing>Flow_0czz4vy</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1uec7ze" default="Flow_0b6em83">
      <bpmn:incoming>Flow_0czz4vy</bpmn:incoming>
      <bpmn:outgoing>Flow_1q1yris</bpmn:outgoing>
      <bpmn:outgoing>Flow_0b6em83</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0fr5dzf">
      <bpmn:incoming>Flow_1q1yris</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0bs5eoe" default="Flow_07scj1m">
      <bpmn:incoming>Flow_1qud553</bpmn:incoming>
      <bpmn:outgoing>Flow_07scj1m</bpmn:outgoing>
      <bpmn:outgoing>Flow_1xhh0z1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1lqk9mr">
      <bpmn:incoming>Flow_1xhh0z1</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1ydhk0j" sourceRef="BSTransferService" targetRef="Gateway_0lf55lw" />
    <bpmn:sequenceFlow id="Flow_1iuxa6f" name="Failure" sourceRef="Gateway_0lf55lw" targetRef="Event_1wohbft">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0f5mklo" name="Success" sourceRef="Gateway_0lf55lw" targetRef="Gateway_0ygm9cn" />
    <bpmn:sequenceFlow id="Flow_1qud553" sourceRef="OCSDeactivateService" targetRef="Gateway_0bs5eoe" />
    <bpmn:sequenceFlow id="Flow_07scj1m" name="Success" sourceRef="Gateway_0bs5eoe" targetRef="OCSCreateService" />
    <bpmn:sequenceFlow id="Flow_0czz4vy" sourceRef="OCSCreateService" targetRef="Gateway_1uec7ze" />
    <bpmn:sequenceFlow id="Flow_1q1yris" name="Failure" sourceRef="Gateway_1uec7ze" targetRef="Event_0fr5dzf">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1xhh0z1" name="Failure" sourceRef="Gateway_0bs5eoe" targetRef="Event_1lqk9mr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_06y57p0" sourceRef="CMPProfileCreation" targetRef="Gateway_03a38e0" />
    <bpmn:exclusiveGateway id="Gateway_03a38e0" default="Flow_1hznjiw">
      <bpmn:incoming>Flow_06y57p0</bpmn:incoming>
      <bpmn:outgoing>Flow_1hznjiw</bpmn:outgoing>
      <bpmn:outgoing>Flow_11tfbrh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1hznjiw" sourceRef="Gateway_03a38e0" targetRef="Gateway_1lsvsao" />
    <bpmn:endEvent id="Event_1bxzwd9">
      <bpmn:incoming>Flow_11tfbrh</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_11tfbrh" sourceRef="Gateway_03a38e0" targetRef="Event_1bxzwd9">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0cuo5e1" default="Flow_09yuthq">
      <bpmn:incoming>Flow_198o764</bpmn:incoming>
      <bpmn:outgoing>Flow_0xs8ev8</bpmn:outgoing>
      <bpmn:outgoing>Flow_09yuthq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0xs8ev8" sourceRef="Gateway_0cuo5e1" targetRef="CMPProfileCreation">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${newProfile}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1lsvsao">
      <bpmn:incoming>Flow_1hznjiw</bpmn:incoming>
      <bpmn:incoming>Flow_09yuthq</bpmn:incoming>
      <bpmn:outgoing>Flow_0ryem8y</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ryem8y" sourceRef="Gateway_1lsvsao" targetRef="Gateway_1w47yak" />
    <bpmn:sequenceFlow id="Flow_09yuthq" sourceRef="Gateway_0cuo5e1" targetRef="Gateway_1lsvsao" />
    <bpmn:sequenceFlow id="Flow_0iiukms" sourceRef="BSAccountCreation" targetRef="Gateway_17fb4is" />
    <bpmn:sequenceFlow id="Flow_1gaim4v" sourceRef="OCSAccountCreation" targetRef="Gateway_1s10z5f" />
    <bpmn:exclusiveGateway id="Gateway_1w47yak" default="Flow_0q0p244">
      <bpmn:incoming>Flow_0ryem8y</bpmn:incoming>
      <bpmn:outgoing>Flow_1a85708</bpmn:outgoing>
      <bpmn:outgoing>Flow_0q0p244</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1a85708" sourceRef="Gateway_1w47yak" targetRef="BSAccountCreation">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${newAccount}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1s10z5f" default="Flow_1oyybhu">
      <bpmn:incoming>Flow_1gaim4v</bpmn:incoming>
      <bpmn:outgoing>Flow_1oyybhu</bpmn:outgoing>
      <bpmn:outgoing>Flow_1gg6y4a</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1oyybhu" sourceRef="Gateway_1s10z5f" targetRef="Gateway_071aarb" />
    <bpmn:exclusiveGateway id="Gateway_17fb4is" default="Flow_1n67vj5">
      <bpmn:incoming>Flow_0iiukms</bpmn:incoming>
      <bpmn:outgoing>Flow_1616ve7</bpmn:outgoing>
      <bpmn:outgoing>Flow_1n67vj5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_0n7ca1w">
      <bpmn:incoming>Flow_1616ve7</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1616ve7" sourceRef="Gateway_17fb4is" targetRef="Event_0n7ca1w">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_02crr2a">
      <bpmn:incoming>Flow_1gg6y4a</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1gg6y4a" sourceRef="Gateway_1s10z5f" targetRef="Event_02crr2a">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_071aarb">
      <bpmn:incoming>Flow_1oyybhu</bpmn:incoming>
      <bpmn:incoming>Flow_0q0p244</bpmn:incoming>
      <bpmn:outgoing>Flow_1vcc5ee</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1vcc5ee" sourceRef="Gateway_071aarb" targetRef="BSTransferService" />
    <bpmn:sequenceFlow id="Flow_0q0p244" sourceRef="Gateway_1w47yak" targetRef="Gateway_071aarb" />
    <bpmn:serviceTask id="CMPProfileCreation" name="Profile Creation in CRM" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0xs8ev8</bpmn:incoming>
      <bpmn:outgoing>Flow_06y57p0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSAccountCreation" name="Account Creation in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1a85708</bpmn:incoming>
      <bpmn:outgoing>Flow_0iiukms</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="OCSAccountCreation" name="Account Creation in OCS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1n67vj5</bpmn:incoming>
      <bpmn:outgoing>Flow_1gaim4v</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_10reuv7</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0b6em83" sourceRef="Gateway_1uec7ze" targetRef="OCS_AddSubscriptionProcess" />
    <bpmn:sequenceFlow id="Flow_1n67vj5" sourceRef="Gateway_17fb4is" targetRef="OCSAccountCreation" />
    <bpmn:callActivity id="Activity_0dr2ich" name="ChangeGroupOwnership" camunda:asyncBefore="true" calledElement="ChangeGroupOwnership" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_05yvb9i</bpmn:incoming>
      <bpmn:outgoing>Flow_0srg9cn</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0srg9cn" sourceRef="Activity_0dr2ich" targetRef="Gateway_0uxnd4v" />
    <bpmn:exclusiveGateway id="Gateway_0uxnd4v" default="Flow_0ndcrh7">
      <bpmn:incoming>Flow_0srg9cn</bpmn:incoming>
      <bpmn:outgoing>Flow_0ndcrh7</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ffc3r9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ndcrh7" name="Success" sourceRef="Gateway_0uxnd4v" targetRef="OCSDeactivateService" />
    <bpmn:endEvent id="Event_1n973yz">
      <bpmn:incoming>Flow_0ffc3r9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0ffc3r9" sourceRef="Gateway_0uxnd4v" targetRef="Event_1n973yz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_15x6ljb" name="is legacy order request" default="Flow_1hox4x6">
      <bpmn:incoming>Flow_1x3zem1</bpmn:incoming>
      <bpmn:outgoing>Flow_0wi4zjq</bpmn:outgoing>
      <bpmn:outgoing>Flow_1hox4x6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0wi4zjq" name="Yes" sourceRef="Gateway_15x6ljb" targetRef="Gateway_0ygm9cn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isLegacy}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0ygm9cn">
      <bpmn:incoming>Flow_0f5mklo</bpmn:incoming>
      <bpmn:incoming>Flow_0wi4zjq</bpmn:incoming>
      <bpmn:outgoing>Flow_0yjwm88</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1hox4x6" name="no" sourceRef="Gateway_15x6ljb" targetRef="BSFetchSubscription" />
    <bpmn:serviceTask id="BSFetchSubscription" name=" Fetch Subscription Details in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${fetchOldSubscriptionDetails}">
      <bpmn:incoming>Flow_1hox4x6</bpmn:incoming>
      <bpmn:outgoing>Flow_151wrws</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:callActivity id="OCS_AddSubscriptionProcess" name="OCS_AddSubscriptionProcess" camunda:asyncBefore="true" calledElement="OCS_AddSubscriptionProcess" camunda:calledElementBinding="deployment">
      <bpmn:documentation>Iterate over subs in BS View Subscription response list. planType  1 is base, 0 is addon</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:out variables="all" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0b6em83</bpmn:incoming>
      <bpmn:outgoing>Flow_0rmfn5h</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics isSequential="true" camunda:asyncBefore="true" camunda:collection="${workflowData.jsonPath(&#34;$.workflowData.FetchSubscriptionIdsResponseAttributes.[?(@.planType==&#39;0&#39;)]&#34;).elementList()}" camunda:elementVariable="execution">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0rmfn5h" sourceRef="OCS_AddSubscriptionProcess" targetRef="Gateway_1di6n65" />
    <bpmn:exclusiveGateway id="Gateway_1di6n65" default="Flow_1p32b52">
      <bpmn:incoming>Flow_0rmfn5h</bpmn:incoming>
      <bpmn:outgoing>Flow_08lr9da</bpmn:outgoing>
      <bpmn:outgoing>Flow_1p32b52</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1lrfbu1">
      <bpmn:incoming>Flow_08lr9da</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_08lr9da" name="Failure" sourceRef="Gateway_1di6n65" targetRef="Event_1lrfbu1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="FetchSubscriptionIds" name="Fetch Subscription ids from billing" camunda:asyncBefore="true" camunda:delegateExpression="${fetchNewSubscriptionDetails}">
      <bpmn:incoming>Flow_0yjwm88</bpmn:incoming>
      <bpmn:outgoing>Flow_14gckfy</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0trx4mb" default="Flow_05yvb9i">
      <bpmn:incoming>Flow_14gckfy</bpmn:incoming>
      <bpmn:outgoing>Flow_07my4c7</bpmn:outgoing>
      <bpmn:outgoing>Flow_05yvb9i</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_14gckfy" sourceRef="FetchSubscriptionIds" targetRef="Gateway_0trx4mb" />
    <bpmn:endEvent id="Event_1kh7rqr">
      <bpmn:incoming>Flow_07my4c7</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_07my4c7" sourceRef="Gateway_0trx4mb" targetRef="Event_1kh7rqr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_05yvb9i" sourceRef="Gateway_0trx4mb" targetRef="Activity_0dr2ich" />
    <bpmn:sequenceFlow id="Flow_0yjwm88" sourceRef="Gateway_0ygm9cn" targetRef="FetchSubscriptionIds" />
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch Service Registry" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1p32b52</bpmn:incoming>
      <bpmn:outgoing>Flow_0l1o6ho</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1p32b52" sourceRef="Gateway_1di6n65" targetRef="SOMFetchServiceRegistry" />
    <bpmn:exclusiveGateway id="Gateway_1at4qe8" default="Flow_0gw0f86">
      <bpmn:incoming>Flow_0l1o6ho</bpmn:incoming>
      <bpmn:outgoing>Flow_0b3jb96</bpmn:outgoing>
      <bpmn:outgoing>Flow_0gw0f86</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0l1o6ho" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_1at4qe8" />
    <bpmn:endEvent id="Event_1fc3pj7">
      <bpmn:incoming>Flow_0b3jb96</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0b3jb96" sourceRef="Gateway_1at4qe8" targetRef="Event_1fc3pj7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="SOM_TransferOfService" name="SOM Transfer Of Service" camunda:asyncBefore="true" camunda:delegateExpression="${somTransferOfService}">
      <bpmn:incoming>Flow_0gw0f86</bpmn:incoming>
      <bpmn:outgoing>Flow_1qhnkdu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0gw0f86" sourceRef="Gateway_1at4qe8" targetRef="SOM_TransferOfService" />
    <bpmn:exclusiveGateway id="Gateway_1krxwt8" default="Flow_0j8q2ra">
      <bpmn:incoming>Flow_1qhnkdu</bpmn:incoming>
      <bpmn:outgoing>Flow_03njpm0</bpmn:outgoing>
      <bpmn:outgoing>Flow_0j8q2ra</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1qhnkdu" sourceRef="SOM_TransferOfService" targetRef="Gateway_1krxwt8" />
    <bpmn:endEvent id="Event_1g5gd9m">
      <bpmn:incoming>Flow_03njpm0</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_03njpm0" sourceRef="Gateway_1krxwt8" targetRef="Event_1g5gd9m">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="SOMCallback" name="SOM Provisioning Callback " camunda:asyncBefore="true" messageRef="Message_1lbkhkd">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOM_TransferOfService</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOM_TransferOfService" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0j8q2ra</bpmn:incoming>
      <bpmn:outgoing>Flow_1c8u9mq</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_0j8q2ra" sourceRef="Gateway_1krxwt8" targetRef="SOMCallback" />
    <bpmn:exclusiveGateway id="Gateway_1w0xvyw" default="Flow_10reuv7">
      <bpmn:incoming>Flow_1c8u9mq</bpmn:incoming>
      <bpmn:outgoing>Flow_0v60urs</bpmn:outgoing>
      <bpmn:outgoing>Flow_10reuv7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1c8u9mq" sourceRef="SOMCallback" targetRef="Gateway_1w0xvyw" />
    <bpmn:endEvent id="Event_1ect2q2">
      <bpmn:incoming>Flow_0v60urs</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0v60urs" sourceRef="Gateway_1w0xvyw" targetRef="Event_1ect2q2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_10reuv7" sourceRef="Gateway_1w0xvyw" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_151wrws" sourceRef="BSFetchSubscription" targetRef="Gateway_1w9r3jm" />
    <bpmn:exclusiveGateway id="Gateway_1w9r3jm" default="Flow_198o764">
      <bpmn:incoming>Flow_151wrws</bpmn:incoming>
      <bpmn:outgoing>Flow_0t7jbew</bpmn:outgoing>
      <bpmn:outgoing>Flow_198o764</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1dur3hk">
      <bpmn:incoming>Flow_0t7jbew</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0t7jbew" name="Failure" sourceRef="Gateway_1w9r3jm" targetRef="Event_1dur3hk">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_14inixi" default="Flow_1pta3sw">
      <bpmn:incoming>Flow_0nwsppc</bpmn:incoming>
      <bpmn:outgoing>Flow_1pta3sw</bpmn:outgoing>
      <bpmn:outgoing>Flow_19890kd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0nwsppc" sourceRef="orderExecStart" targetRef="Gateway_14inixi" />
    <bpmn:serviceTask id="BSFetchAccount" name=" Fetch Account Details in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1pta3sw</bpmn:incoming>
      <bpmn:outgoing>Flow_075s764</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1pta3sw" sourceRef="Gateway_14inixi" targetRef="BSFetchAccount" />
    <bpmn:exclusiveGateway id="Gateway_09me0vp" default="Flow_0ghlxw4">
      <bpmn:incoming>Flow_075s764</bpmn:incoming>
      <bpmn:outgoing>Flow_1fyfj94</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ghlxw4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_075s764" sourceRef="BSFetchAccount" targetRef="Gateway_09me0vp" />
    <bpmn:endEvent id="Event_1mu99fe">
      <bpmn:incoming>Flow_1fyfj94</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1fyfj94" sourceRef="Gateway_09me0vp" targetRef="Event_1mu99fe">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1slicq8">
      <bpmn:incoming>Flow_0ghlxw4</bpmn:incoming>
      <bpmn:incoming>Flow_19890kd</bpmn:incoming>
      <bpmn:outgoing>Flow_1x3zem1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ghlxw4" sourceRef="Gateway_09me0vp" targetRef="Gateway_1slicq8" />
    <bpmn:sequenceFlow id="Flow_1x3zem1" sourceRef="Gateway_1slicq8" targetRef="Gateway_15x6ljb" />
    <bpmn:sequenceFlow id="Flow_19890kd" sourceRef="Gateway_14inixi" targetRef="Gateway_1slicq8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${newAccount}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_198o764" sourceRef="Gateway_1w9r3jm" targetRef="Gateway_0cuo5e1" />
    <bpmn:textAnnotation id="TextAnnotation_1bqoh0o">
      <bpmn:text>for legacy order request, destination profile and account will be always existing, and Billing TOS request will be executed in enrichment</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0m0wagd" sourceRef="Gateway_15x6ljb" targetRef="TextAnnotation_1bqoh0o" />
  </bpmn:process>
  <bpmn:message id="Message_0y7i6m3" name="MakePaymentCallback" />
  <bpmn:message id="Message_1lbkhkd" name="SOMCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="TransferOfService">
      <bpmndi:BPMNShape id="Activity_0jav7ks_di" bpmnElement="BSTransferService">
        <dc:Bounds x="3150" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0lf55lw_di" bpmnElement="Gateway_0lf55lw" isMarkerVisible="true">
        <dc:Bounds x="3305" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1wohbft_di" bpmnElement="Event_1wohbft">
        <dc:Bounds x="3312" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0m83ilj_di" bpmnElement="OCSDeactivateService">
        <dc:Bounds x="4120" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_06vcfll_di" bpmnElement="OCSCreateService">
        <dc:Bounds x="4450" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1uec7ze_di" bpmnElement="Gateway_1uec7ze" isMarkerVisible="true">
        <dc:Bounds x="4625" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0fr5dzf_di" bpmnElement="Event_0fr5dzf">
        <dc:Bounds x="4632" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0bs5eoe_di" bpmnElement="Gateway_0bs5eoe" isMarkerVisible="true">
        <dc:Bounds x="4275" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1lqk9mr_di" bpmnElement="Event_1lqk9mr">
        <dc:Bounds x="4282" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_03a38e0_di" bpmnElement="Gateway_03a38e0" isMarkerVisible="true">
        <dc:Bounds x="2065" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1bxzwd9_di" bpmnElement="Event_1bxzwd9">
        <dc:Bounds x="2072" y="335" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0cuo5e1_di" bpmnElement="Gateway_0cuo5e1" isMarkerVisible="true">
        <dc:Bounds x="1805" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1lsvsao_di" bpmnElement="Gateway_1lsvsao" isMarkerVisible="true">
        <dc:Bounds x="2195" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1w47yak_di" bpmnElement="Gateway_1w47yak" isMarkerVisible="true">
        <dc:Bounds x="2335" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1s10z5f_di" bpmnElement="Gateway_1s10z5f" isMarkerVisible="true">
        <dc:Bounds x="2875" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_17fb4is_di" bpmnElement="Gateway_17fb4is" isMarkerVisible="true">
        <dc:Bounds x="2635" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0n7ca1w_di" bpmnElement="Event_0n7ca1w">
        <dc:Bounds x="2642" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_02crr2a_di" bpmnElement="Event_02crr2a">
        <dc:Bounds x="2882" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_071aarb_di" bpmnElement="Gateway_071aarb" isMarkerVisible="true">
        <dc:Bounds x="3025" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1j42xof_di" bpmnElement="CMPProfileCreation">
        <dc:Bounds x="1900" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1qjxhbd_di" bpmnElement="BSAccountCreation">
        <dc:Bounds x="2460" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ulbek8_di" bpmnElement="OCSAccountCreation">
        <dc:Bounds x="2740" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0qd5j9y_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="5992" y="212" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0myeygd" bpmnElement="Activity_0dr2ich">
        <dc:Bounds x="3790" y="190" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0uxnd4v_di" bpmnElement="Gateway_0uxnd4v" isMarkerVisible="true">
        <dc:Bounds x="3985" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1n973yz_di" bpmnElement="Event_1n973yz">
        <dc:Bounds x="3992" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ygm9cn_di" bpmnElement="Gateway_0ygm9cn" isMarkerVisible="true">
        <dc:Bounds x="3405" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1rkhdmw" bpmnElement="OCS_AddSubscriptionProcess">
        <dc:Bounds x="4770" y="190" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1lhhid2" bpmnElement="Gateway_1di6n65" isMarkerVisible="true">
        <dc:Bounds x="4965" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1iubyxc" bpmnElement="Event_1lrfbu1">
        <dc:Bounds x="4972" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0hvmz55" bpmnElement="FetchSubscriptionIds">
        <dc:Bounds x="3520" y="190" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0trx4mb_di" bpmnElement="Gateway_0trx4mb" isMarkerVisible="true">
        <dc:Bounds x="3675" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1kh7rqr_di" bpmnElement="Event_1kh7rqr">
        <dc:Bounds x="3682" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0hs1oog_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="5130" y="190" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1at4qe8_di" bpmnElement="Gateway_1at4qe8" isMarkerVisible="true">
        <dc:Bounds x="5295" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1fc3pj7_di" bpmnElement="Event_1fc3pj7">
        <dc:Bounds x="5302" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1y1f08p" bpmnElement="SOM_TransferOfService">
        <dc:Bounds x="5430" y="190" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1krxwt8_di" bpmnElement="Gateway_1krxwt8" isMarkerVisible="true">
        <dc:Bounds x="5595" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1g5gd9m_di" bpmnElement="Event_1g5gd9m">
        <dc:Bounds x="5602" y="335" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ttj65s" bpmnElement="SOMCallback">
        <dc:Bounds x="5700" y="190" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1w0xvyw_di" bpmnElement="Gateway_1w0xvyw" isMarkerVisible="true">
        <dc:Bounds x="5845" y="205" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ect2q2_di" bpmnElement="Event_1ect2q2">
        <dc:Bounds x="5852" y="342" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0yw686v_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1put5yh" bpmnElement="Gateway_14inixi" isMarkerVisible="true">
        <dc:Bounds x="285" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_04tx69v" bpmnElement="BSFetchAccount">
        <dc:Bounds x="390" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_09me0vp_di" bpmnElement="Gateway_09me0vp" isMarkerVisible="true">
        <dc:Bounds x="535" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1mu99fe_di" bpmnElement="Event_1mu99fe">
        <dc:Bounds x="542" y="320" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1w9r3jm_di" bpmnElement="Gateway_1w9r3jm" isMarkerVisible="true">
        <dc:Bounds x="1565" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1dur3hk_di" bpmnElement="Event_1dur3hk">
        <dc:Bounds x="1572" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0wdltu8_di" bpmnElement="BSFetchSubscription">
        <dc:Bounds x="1150" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_15x6ljb_di" bpmnElement="Gateway_15x6ljb" isMarkerVisible="true">
        <dc:Bounds x="895" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="884" y="165" width="73" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1slicq8_di" bpmnElement="Gateway_1slicq8" isMarkerVisible="true">
        <dc:Bounds x="705" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Association_0m0wagd_di" bpmnElement="Association_0m0wagd">
        <di:waypoint x="903" y="228" />
        <di:waypoint x="839" y="260" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vcc5ee_di" bpmnElement="Flow_1vcc5ee">
        <di:waypoint x="3075" y="230" />
        <di:waypoint x="3150" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ydhk0j_di" bpmnElement="Flow_1ydhk0j">
        <di:waypoint x="3250" y="230" />
        <di:waypoint x="3305" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1iuxa6f_di" bpmnElement="Flow_1iuxa6f">
        <di:waypoint x="3330" y="255" />
        <di:waypoint x="3330" y="342" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3335" y="286" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f5mklo_di" bpmnElement="Flow_0f5mklo">
        <di:waypoint x="3355" y="230" />
        <di:waypoint x="3405" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3355" y="213" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ndcrh7_di" bpmnElement="Flow_0ndcrh7">
        <di:waypoint x="4035" y="230" />
        <di:waypoint x="4120" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4056" y="212" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qud553_di" bpmnElement="Flow_1qud553">
        <di:waypoint x="4220" y="230" />
        <di:waypoint x="4275" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07scj1m_di" bpmnElement="Flow_07scj1m">
        <di:waypoint x="4325" y="230" />
        <di:waypoint x="4450" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4367" y="212" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0czz4vy_di" bpmnElement="Flow_0czz4vy">
        <di:waypoint x="4550" y="230" />
        <di:waypoint x="4625" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1q1yris_di" bpmnElement="Flow_1q1yris">
        <di:waypoint x="4650" y="255" />
        <di:waypoint x="4650" y="342" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4655" y="284" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b6em83_di" bpmnElement="Flow_0b6em83">
        <di:waypoint x="4675" y="230" />
        <di:waypoint x="4770" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xhh0z1_di" bpmnElement="Flow_1xhh0z1">
        <di:waypoint x="4300" y="255" />
        <di:waypoint x="4300" y="342" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4314" y="311" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06y57p0_di" bpmnElement="Flow_06y57p0">
        <di:waypoint x="2000" y="220" />
        <di:waypoint x="2065" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hznjiw_di" bpmnElement="Flow_1hznjiw">
        <di:waypoint x="2115" y="220" />
        <di:waypoint x="2195" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11tfbrh_di" bpmnElement="Flow_11tfbrh">
        <di:waypoint x="2090" y="245" />
        <di:waypoint x="2090" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xs8ev8_di" bpmnElement="Flow_0xs8ev8">
        <di:waypoint x="1855" y="220" />
        <di:waypoint x="1900" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09yuthq_di" bpmnElement="Flow_09yuthq">
        <di:waypoint x="1830" y="195" />
        <di:waypoint x="1830" y="130" />
        <di:waypoint x="2220" y="130" />
        <di:waypoint x="2220" y="195" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ryem8y_di" bpmnElement="Flow_0ryem8y">
        <di:waypoint x="2245" y="220" />
        <di:waypoint x="2335" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a85708_di" bpmnElement="Flow_1a85708">
        <di:waypoint x="2385" y="220" />
        <di:waypoint x="2460" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0q0p244_di" bpmnElement="Flow_0q0p244">
        <di:waypoint x="2360" y="195" />
        <di:waypoint x="2360" y="140" />
        <di:waypoint x="3050" y="140" />
        <di:waypoint x="3050" y="205" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gaim4v_di" bpmnElement="Flow_1gaim4v">
        <di:waypoint x="2840" y="230" />
        <di:waypoint x="2875" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oyybhu_di" bpmnElement="Flow_1oyybhu">
        <di:waypoint x="2925" y="230" />
        <di:waypoint x="3025" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gg6y4a_di" bpmnElement="Flow_1gg6y4a">
        <di:waypoint x="2900" y="255" />
        <di:waypoint x="2900" y="342" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0iiukms_di" bpmnElement="Flow_0iiukms">
        <di:waypoint x="2560" y="230" />
        <di:waypoint x="2635" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1616ve7_di" bpmnElement="Flow_1616ve7">
        <di:waypoint x="2660" y="255" />
        <di:waypoint x="2660" y="352" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1n67vj5_di" bpmnElement="Flow_1n67vj5">
        <di:waypoint x="2685" y="230" />
        <di:waypoint x="2740" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10reuv7_di" bpmnElement="Flow_10reuv7">
        <di:waypoint x="5895" y="230" />
        <di:waypoint x="5992" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05yvb9i_di" bpmnElement="Flow_05yvb9i">
        <di:waypoint x="3725" y="230" />
        <di:waypoint x="3790" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0srg9cn_di" bpmnElement="Flow_0srg9cn">
        <di:waypoint x="3890" y="230" />
        <di:waypoint x="3985" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ffc3r9_di" bpmnElement="Flow_0ffc3r9">
        <di:waypoint x="4010" y="255" />
        <di:waypoint x="4010" y="352" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1x3zem1_di" bpmnElement="Flow_1x3zem1">
        <di:waypoint x="755" y="220" />
        <di:waypoint x="895" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wi4zjq_di" bpmnElement="Flow_0wi4zjq">
        <di:waypoint x="920" y="245" />
        <di:waypoint x="920" y="440" />
        <di:waypoint x="3430" y="440" />
        <di:waypoint x="3430" y="255" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="931" y="413" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hox4x6_di" bpmnElement="Flow_1hox4x6">
        <di:waypoint x="945" y="220" />
        <di:waypoint x="1150" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1032" y="193" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yjwm88_di" bpmnElement="Flow_0yjwm88">
        <di:waypoint x="3455" y="230" />
        <di:waypoint x="3520" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_151wrws_di" bpmnElement="Flow_151wrws">
        <di:waypoint x="1250" y="220" />
        <di:waypoint x="1565" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rmfn5h_di" bpmnElement="Flow_0rmfn5h">
        <di:waypoint x="4870" y="230" />
        <di:waypoint x="4965" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08lr9da_di" bpmnElement="Flow_08lr9da">
        <di:waypoint x="4990" y="255" />
        <di:waypoint x="4990" y="342" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4989" y="296" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p32b52_di" bpmnElement="Flow_1p32b52">
        <di:waypoint x="5015" y="230" />
        <di:waypoint x="5130" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14gckfy_di" bpmnElement="Flow_14gckfy">
        <di:waypoint x="3620" y="230" />
        <di:waypoint x="3675" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07my4c7_di" bpmnElement="Flow_07my4c7">
        <di:waypoint x="3700" y="255" />
        <di:waypoint x="3700" y="352" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0l1o6ho_di" bpmnElement="Flow_0l1o6ho">
        <di:waypoint x="5230" y="230" />
        <di:waypoint x="5295" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b3jb96_di" bpmnElement="Flow_0b3jb96">
        <di:waypoint x="5320" y="255" />
        <di:waypoint x="5320" y="342" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gw0f86_di" bpmnElement="Flow_0gw0f86">
        <di:waypoint x="5345" y="230" />
        <di:waypoint x="5430" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qhnkdu_di" bpmnElement="Flow_1qhnkdu">
        <di:waypoint x="5530" y="230" />
        <di:waypoint x="5595" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03njpm0_di" bpmnElement="Flow_03njpm0">
        <di:waypoint x="5620" y="255" />
        <di:waypoint x="5620" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0j8q2ra_di" bpmnElement="Flow_0j8q2ra">
        <di:waypoint x="5645" y="230" />
        <di:waypoint x="5700" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c8u9mq_di" bpmnElement="Flow_1c8u9mq">
        <di:waypoint x="5800" y="230" />
        <di:waypoint x="5845" y="230" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v60urs_di" bpmnElement="Flow_0v60urs">
        <di:waypoint x="5870" y="255" />
        <di:waypoint x="5870" y="342" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0t7jbew_di" bpmnElement="Flow_0t7jbew">
        <di:waypoint x="1590" y="245" />
        <di:waypoint x="1590" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1603" y="279" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nwsppc_di" bpmnElement="Flow_0nwsppc">
        <di:waypoint x="188" y="220" />
        <di:waypoint x="285" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pta3sw_di" bpmnElement="Flow_1pta3sw">
        <di:waypoint x="335" y="220" />
        <di:waypoint x="390" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19890kd_di" bpmnElement="Flow_19890kd">
        <di:waypoint x="310" y="195" />
        <di:waypoint x="310" y="70" />
        <di:waypoint x="730" y="70" />
        <di:waypoint x="730" y="195" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_075s764_di" bpmnElement="Flow_075s764">
        <di:waypoint x="490" y="220" />
        <di:waypoint x="535" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fyfj94_di" bpmnElement="Flow_1fyfj94">
        <di:waypoint x="560" y="245" />
        <di:waypoint x="560" y="320" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ghlxw4_di" bpmnElement="Flow_0ghlxw4">
        <di:waypoint x="585" y="220" />
        <di:waypoint x="705" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_198o764_di" bpmnElement="Flow_198o764">
        <di:waypoint x="1615" y="220" />
        <di:waypoint x="1805" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="TextAnnotation_1bqoh0o_di" bpmnElement="TextAnnotation_1bqoh0o">
        <dc:Bounds x="760" y="260" width="100" height="156" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
