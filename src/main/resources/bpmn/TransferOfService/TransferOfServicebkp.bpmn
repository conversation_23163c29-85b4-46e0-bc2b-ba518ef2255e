<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_0efb5vu" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.6.0">
  <bpmn:process id="TransferOfServicebkp" name="Transfer Of Service" isExecutable="true" camunda:jobPriority="${priority}">
    <bpmn:serviceTask id="BSCreateProfile" name="Create Customer Profile In Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0623ua8</bpmn:incoming>
      <bpmn:outgoing>Flow_0ezuzv7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0kfqt1e</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0kfqt1e" sourceRef="orderExecStart" targetRef="Gateway_035mm0z" />
    <bpmn:exclusiveGateway id="Gateway_035mm0z" name="New profile creation" default="Flow_0c27nl2">
      <bpmn:incoming>Flow_0kfqt1e</bpmn:incoming>
      <bpmn:outgoing>Flow_0623ua8</bpmn:outgoing>
      <bpmn:outgoing>Flow_0c27nl2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0623ua8" name="Yes" sourceRef="Gateway_035mm0z" targetRef="BSCreateProfile">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${newProfile}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSCreateAccount" name="Create Billing Account" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0u8gabl</bpmn:incoming>
      <bpmn:incoming>Flow_1uur0ou</bpmn:incoming>
      <bpmn:outgoing>Flow_0u5neo5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1yfjaza" default="Flow_14re3wn">
      <bpmn:incoming>Flow_0ezuzv7</bpmn:incoming>
      <bpmn:outgoing>Flow_14re3wn</bpmn:outgoing>
      <bpmn:outgoing>Flow_0haug12</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0ezuzv7" sourceRef="BSCreateProfile" targetRef="Gateway_1yfjaza" />
    <bpmn:sequenceFlow id="Flow_14re3wn" name="Success" sourceRef="Gateway_1yfjaza" targetRef="Gateway_0v8j40p" />
    <bpmn:endEvent id="Event_0ralzrg">
      <bpmn:incoming>Flow_0haug12</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0haug12" name="Failure" sourceRef="Gateway_1yfjaza" targetRef="Event_0ralzrg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1dl1f2k" default="Flow_01cq8b5">
      <bpmn:incoming>Flow_0u5neo5</bpmn:incoming>
      <bpmn:outgoing>Flow_1xcqgot</bpmn:outgoing>
      <bpmn:outgoing>Flow_01cq8b5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0u5neo5" sourceRef="BSCreateAccount" targetRef="Gateway_1dl1f2k" />
    <bpmn:endEvent id="Event_1l1ktyd">
      <bpmn:incoming>Flow_1xcqgot</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1xcqgot" name="Failure" sourceRef="Gateway_1dl1f2k" targetRef="Event_1l1ktyd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1q3q25s">
      <bpmn:incoming>Flow_01cq8b5</bpmn:incoming>
      <bpmn:incoming>Flow_1yudop1</bpmn:incoming>
      <bpmn:outgoing>Flow_15iod7e</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_01cq8b5" name="Success" sourceRef="Gateway_1dl1f2k" targetRef="Gateway_1q3q25s" />
    <bpmn:sequenceFlow id="Flow_0c27nl2" name="No" sourceRef="Gateway_035mm0z" targetRef="Gateway_1q9r6w3" />
    <bpmn:exclusiveGateway id="Gateway_1q9r6w3" name="New Account Creation" default="Flow_0z7pjqr">
      <bpmn:incoming>Flow_0c27nl2</bpmn:incoming>
      <bpmn:outgoing>Flow_06smlm7</bpmn:outgoing>
      <bpmn:outgoing>Flow_0z7pjqr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0v8j40p">
      <bpmn:incoming>Flow_14re3wn</bpmn:incoming>
      <bpmn:incoming>Flow_06smlm7</bpmn:incoming>
      <bpmn:outgoing>Flow_1d2d5cm</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1d2d5cm" sourceRef="Gateway_0v8j40p" targetRef="Gateway_0y803df" />
    <bpmn:sequenceFlow id="Flow_06smlm7" name="Yes" sourceRef="Gateway_1q9r6w3" targetRef="Gateway_0v8j40p">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${newAccount}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0z7pjqr" name="No" sourceRef="Gateway_1q9r6w3" targetRef="Gateway_10c6b3k" />
    <bpmn:serviceTask id="BSFetchAccounts" name="Billing Fetch Account Details" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1cvioc5</bpmn:incoming>
      <bpmn:outgoing>Flow_04luln9</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_10c6b3k" name="isPostpaid" default="Flow_1cvioc5">
      <bpmn:incoming>Flow_0z7pjqr</bpmn:incoming>
      <bpmn:outgoing>Flow_1cvioc5</bpmn:outgoing>
      <bpmn:outgoing>Flow_1i9jtke</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1cvioc5" name="No" sourceRef="Gateway_10c6b3k" targetRef="BSFetchAccounts" />
    <bpmn:sequenceFlow id="Flow_04luln9" sourceRef="BSFetchAccounts" targetRef="Gateway_06bx8yq" />
    <bpmn:exclusiveGateway id="Gateway_06bx8yq" default="Flow_0uitu7e">
      <bpmn:incoming>Flow_04luln9</bpmn:incoming>
      <bpmn:outgoing>Flow_0uitu7e</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ceof7a</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0uitu7e" name="Success" sourceRef="Gateway_06bx8yq" targetRef="Gateway_1uvxs69" />
    <bpmn:endEvent id="Event_1lsacct">
      <bpmn:incoming>Flow_0ceof7a</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0ceof7a" name="Failure" sourceRef="Gateway_06bx8yq" targetRef="Event_1lsacct">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1uvxs69">
      <bpmn:incoming>Flow_0uitu7e</bpmn:incoming>
      <bpmn:incoming>Flow_1i9jtke</bpmn:incoming>
      <bpmn:outgoing>Flow_07gp8hs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1i9jtke" name="Yes" sourceRef="Gateway_10c6b3k" targetRef="Gateway_1uvxs69">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isPostpaid}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_07gp8hs" sourceRef="Gateway_1uvxs69" targetRef="Gateway_0a87k2a" />
    <bpmn:exclusiveGateway id="Gateway_0a87k2a" name="check if prepaid account is there or not" default="Flow_0u8gabl">
      <bpmn:incoming>Flow_07gp8hs</bpmn:incoming>
      <bpmn:outgoing>Flow_1yudop1</bpmn:outgoing>
      <bpmn:outgoing>Flow_0u8gabl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1yudop1" name="yes" sourceRef="Gateway_0a87k2a" targetRef="Gateway_1q3q25s">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.workflowData").element().hasProp('BSFetchAccountsResponseAttributes') || isPostpaid}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0u8gabl" name="no" sourceRef="Gateway_0a87k2a" targetRef="BSCreateAccount" />
    <bpmn:serviceTask id="GenerateDataDelegateTask" name="Generate Data Delegate" camunda:delegateExpression="${generateDataDelegateTOS}">
      <bpmn:incoming>Flow_15iod7e</bpmn:incoming>
      <bpmn:outgoing>Flow_0aqr7rg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="CreateInstancesTask" name="Create Instances" camunda:asyncBefore="true" camunda:asyncAfter="true" camunda:delegateExpression="${createInstancesDelegateTOS}">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="childProcess">TOS-TransferOfServiceProcess</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0aqr7rg</bpmn:incoming>
      <bpmn:incoming>Flow_0sd9xfv</bpmn:incoming>
      <bpmn:outgoing>Flow_1awn5yf</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1u2qhg8" name="All instances created ?">
      <bpmn:incoming>Flow_1awn5yf</bpmn:incoming>
      <bpmn:outgoing>Flow_0sd9xfv</bpmn:outgoing>
      <bpmn:outgoing>Flow_1aiey9t</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1aiey9t</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0aqr7rg" sourceRef="GenerateDataDelegateTask" targetRef="CreateInstancesTask" />
    <bpmn:sequenceFlow id="Flow_0sd9xfv" name="No" sourceRef="Gateway_1u2qhg8" targetRef="CreateInstancesTask">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${not allCreated}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1awn5yf" sourceRef="CreateInstancesTask" targetRef="Gateway_1u2qhg8" />
    <bpmn:sequenceFlow id="Flow_1aiey9t" name="Yes" sourceRef="Gateway_1u2qhg8" targetRef="orderExecEnd">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${allCreated}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_15iod7e" sourceRef="Gateway_1q3q25s" targetRef="GenerateDataDelegateTask" />
    <bpmn:serviceTask id="BSCreateAOContact" name="BIL Create AO Contact Details" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_1nyp95i</bpmn:incoming>
      <bpmn:outgoing>Flow_0pnx3qa</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0y803df" name="isPostpaid" default="Flow_0x293sf">
      <bpmn:incoming>Flow_1d2d5cm</bpmn:incoming>
      <bpmn:outgoing>Flow_1qke42l</bpmn:outgoing>
      <bpmn:outgoing>Flow_0x293sf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0dice9m" default="Flow_1c6yvw3">
      <bpmn:incoming>SequenceFlow_0sk5em8</bpmn:incoming>
      <bpmn:outgoing>Flow_1c6yvw3</bpmn:outgoing>
      <bpmn:outgoing>Flow_1yrq3hv</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1hgamjt">
      <bpmn:incoming>Flow_1yrq3hv</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1mmssu2">
      <bpmn:incoming>Flow_1c6yvw3</bpmn:incoming>
      <bpmn:incoming>Flow_0x293sf</bpmn:incoming>
      <bpmn:outgoing>Flow_1uur0ou</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0pnx3qa" sourceRef="BSCreateAOContact" targetRef="ExclusiveGateway_1qph207" />
    <bpmn:sequenceFlow id="Flow_1c6yvw3" name="Success" sourceRef="Gateway_0dice9m" targetRef="Gateway_1mmssu2" />
    <bpmn:sequenceFlow id="Flow_1yrq3hv" name="Failure" sourceRef="Gateway_0dice9m" targetRef="Event_1hgamjt">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1uur0ou" sourceRef="Gateway_1mmssu2" targetRef="BSCreateAccount" />
    <bpmn:sequenceFlow id="Flow_1qke42l" name="Corporate" sourceRef="Gateway_0y803df" targetRef="ExclusiveGateway_1yti3y8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${isCorporateAccount}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0x293sf" name="Residential" sourceRef="Gateway_0y803df" targetRef="Gateway_1mmssu2" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_1yti3y8" default="SequenceFlow_1nyp95i">
      <bpmn:incoming>Flow_1qke42l</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1nyp95i</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0eha6vd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_1nyp95i" sourceRef="ExclusiveGateway_1yti3y8" targetRef="BSCreateAOContact" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_1qph207">
      <bpmn:incoming>SequenceFlow_0eha6vd</bpmn:incoming>
      <bpmn:incoming>Flow_0pnx3qa</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0sk5em8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0eha6vd" name="skip AO" sourceRef="ExclusiveGateway_1yti3y8" targetRef="ExclusiveGateway_1qph207">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${skipCreateAO}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0sk5em8" sourceRef="ExclusiveGateway_1qph207" targetRef="Gateway_0dice9m" />
  </bpmn:process>
  <bpmn:message id="Message_0y7i6m3" name="MakePaymentCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="TransferOfServicebkp">
      <bpmndi:BPMNEdge id="SequenceFlow_0sk5em8_di" bpmnElement="SequenceFlow_0sk5em8">
        <di:waypoint x="1367" y="400" />
        <di:waypoint x="1385" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0eha6vd_di" bpmnElement="SequenceFlow_0eha6vd">
        <di:waypoint x="1147" y="375" />
        <di:waypoint x="1147" y="332" />
        <di:waypoint x="1342" y="332" />
        <di:waypoint x="1342" y="375" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1226" y="314" width="39" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1nyp95i_di" bpmnElement="SequenceFlow_1nyp95i">
        <di:waypoint x="1172" y="400" />
        <di:waypoint x="1200" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x293sf_di" bpmnElement="Flow_0x293sf">
        <di:waypoint x="1080" y="375" />
        <di:waypoint x="1080" y="306" />
        <di:waypoint x="1580" y="306" />
        <di:waypoint x="1580" y="375" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1303" y="288" width="55" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qke42l_di" bpmnElement="Flow_1qke42l">
        <di:waypoint x="1105" y="400" />
        <di:waypoint x="1122" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1085" y="412" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uur0ou_di" bpmnElement="Flow_1uur0ou">
        <di:waypoint x="1605" y="400" />
        <di:waypoint x="1700" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yrq3hv_di" bpmnElement="Flow_1yrq3hv">
        <di:waypoint x="1410" y="425" />
        <di:waypoint x="1410" y="482" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1417" y="444" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c6yvw3_di" bpmnElement="Flow_1c6yvw3">
        <di:waypoint x="1435" y="400" />
        <di:waypoint x="1555" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1465" y="382" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pnx3qa_di" bpmnElement="Flow_0pnx3qa">
        <di:waypoint x="1300" y="400" />
        <di:waypoint x="1317" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15iod7e_di" bpmnElement="Flow_15iod7e">
        <di:waypoint x="2095" y="400" />
        <di:waypoint x="2210" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1aiey9t_di" bpmnElement="Flow_1aiey9t">
        <di:waypoint x="2665" y="400" />
        <di:waypoint x="2752" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2700" y="382" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1awn5yf_di" bpmnElement="Flow_1awn5yf">
        <di:waypoint x="2520" y="400" />
        <di:waypoint x="2615" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0sd9xfv_di" bpmnElement="Flow_0sd9xfv">
        <di:waypoint x="2640" y="425" />
        <di:waypoint x="2640" y="490" />
        <di:waypoint x="2470" y="490" />
        <di:waypoint x="2470" y="440" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2548" y="493" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0aqr7rg_di" bpmnElement="Flow_0aqr7rg">
        <di:waypoint x="2310" y="400" />
        <di:waypoint x="2420" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u8gabl_di" bpmnElement="Flow_0u8gabl">
        <di:waypoint x="1750" y="245" />
        <di:waypoint x="1750" y="360" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1759" y="300" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yudop1_di" bpmnElement="Flow_1yudop1">
        <di:waypoint x="1775" y="220" />
        <di:waypoint x="2070" y="220" />
        <di:waypoint x="2070" y="375" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1914" y="202" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07gp8hs_di" bpmnElement="Flow_07gp8hs">
        <di:waypoint x="1645" y="220" />
        <di:waypoint x="1725" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1i9jtke_di" bpmnElement="Flow_1i9jtke">
        <di:waypoint x="1120" y="195" />
        <di:waypoint x="1120" y="120" />
        <di:waypoint x="1620" y="120" />
        <di:waypoint x="1620" y="195" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1362" y="102" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ceof7a_di" bpmnElement="Flow_0ceof7a">
        <di:waypoint x="1450" y="245" />
        <di:waypoint x="1450" y="282" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1457" y="255" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0uitu7e_di" bpmnElement="Flow_0uitu7e">
        <di:waypoint x="1475" y="220" />
        <di:waypoint x="1595" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1505" y="202" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04luln9_di" bpmnElement="Flow_04luln9">
        <di:waypoint x="1340" y="220" />
        <di:waypoint x="1425" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1cvioc5_di" bpmnElement="Flow_1cvioc5">
        <di:waypoint x="1145" y="220" />
        <di:waypoint x="1240" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1186" y="202" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0z7pjqr_di" bpmnElement="Flow_0z7pjqr">
        <di:waypoint x="1005" y="220" />
        <di:waypoint x="1095" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1036" y="202" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06smlm7_di" bpmnElement="Flow_06smlm7">
        <di:waypoint x="980" y="245" />
        <di:waypoint x="980" y="375" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="991" y="304" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1d2d5cm_di" bpmnElement="Flow_1d2d5cm">
        <di:waypoint x="1005" y="400" />
        <di:waypoint x="1055" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c27nl2_di" bpmnElement="Flow_0c27nl2">
        <di:waypoint x="310" y="375" />
        <di:waypoint x="310" y="220" />
        <di:waypoint x="955" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="322" y="303" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01cq8b5_di" bpmnElement="Flow_01cq8b5">
        <di:waypoint x="1945" y="400" />
        <di:waypoint x="2045" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1973" y="382" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xcqgot_di" bpmnElement="Flow_1xcqgot">
        <di:waypoint x="1920" y="425" />
        <di:waypoint x="1920" y="512" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1923" y="448" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u5neo5_di" bpmnElement="Flow_0u5neo5">
        <di:waypoint x="1800" y="400" />
        <di:waypoint x="1895" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0haug12_di" bpmnElement="Flow_0haug12">
        <di:waypoint x="790" y="425" />
        <di:waypoint x="790" y="512" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="793" y="448" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14re3wn_di" bpmnElement="Flow_14re3wn">
        <di:waypoint x="815" y="400" />
        <di:waypoint x="955" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="864" y="382" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ezuzv7_di" bpmnElement="Flow_0ezuzv7">
        <di:waypoint x="600" y="400" />
        <di:waypoint x="765" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0623ua8_di" bpmnElement="Flow_0623ua8">
        <di:waypoint x="335" y="400" />
        <di:waypoint x="500" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="410" y="382" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kfqt1e_di" bpmnElement="Flow_0kfqt1e">
        <di:waypoint x="188" y="400" />
        <di:waypoint x="285" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_17np00j" bpmnElement="BSCreateProfile">
        <dc:Bounds x="500" y="360" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1yk816n_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="382" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_035mm0z_di" bpmnElement="Gateway_035mm0z" isMarkerVisible="true">
        <dc:Bounds x="285" y="375" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="282" y="432" width="56" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1u2hnwy" bpmnElement="BSCreateAccount">
        <dc:Bounds x="1700" y="360" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1yfjaza_di" bpmnElement="Gateway_1yfjaza" isMarkerVisible="true">
        <dc:Bounds x="765" y="375" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ralzrg_di" bpmnElement="Event_0ralzrg">
        <dc:Bounds x="772" y="512" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1dl1f2k_di" bpmnElement="Gateway_1dl1f2k" isMarkerVisible="true">
        <dc:Bounds x="1895" y="375" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1l1ktyd_di" bpmnElement="Event_1l1ktyd">
        <dc:Bounds x="1902" y="512" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1q3q25s_di" bpmnElement="Gateway_1q3q25s" isMarkerVisible="true">
        <dc:Bounds x="2045" y="375" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1q9r6w3_di" bpmnElement="Gateway_1q9r6w3" isMarkerVisible="true">
        <dc:Bounds x="955" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="947" y="157.5" width="66" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0v8j40p_di" bpmnElement="Gateway_0v8j40p" isMarkerVisible="true">
        <dc:Bounds x="955" y="375" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1fk7xvl_di" bpmnElement="BSFetchAccounts">
        <dc:Bounds x="1240" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_10c6b3k_di" bpmnElement="Gateway_10c6b3k" isMarkerVisible="true">
        <dc:Bounds x="1095" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1094" y="253" width="51" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_06bx8yq_di" bpmnElement="Gateway_06bx8yq" isMarkerVisible="true">
        <dc:Bounds x="1425" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1lsacct_di" bpmnElement="Event_1lsacct">
        <dc:Bounds x="1432" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1uvxs69_di" bpmnElement="Gateway_1uvxs69" isMarkerVisible="true">
        <dc:Bounds x="1595" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0a87k2a_di" bpmnElement="Gateway_0a87k2a" isMarkerVisible="true">
        <dc:Bounds x="1725" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1711" y="140" width="78" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1h00p5g_di" bpmnElement="GenerateDataDelegateTask">
        <dc:Bounds x="2210" y="360" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1fquyyy_di" bpmnElement="CreateInstancesTask">
        <dc:Bounds x="2420" y="360" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1u2qhg8_di" bpmnElement="Gateway_1u2qhg8" isMarkerVisible="true">
        <dc:Bounds x="2615" y="375" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2609" y="345" width="62" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0tupzer_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2752" y="382" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_12oj15s_di" bpmnElement="BSCreateAOContact">
        <dc:Bounds x="1200" y="360" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0y803df_di" bpmnElement="Gateway_0y803df" isMarkerVisible="true">
        <dc:Bounds x="1055" y="375" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1054" y="433" width="51" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0dice9m_di" bpmnElement="Gateway_0dice9m" isMarkerVisible="true">
        <dc:Bounds x="1385" y="375" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1hgamjt_di" bpmnElement="Event_1hgamjt">
        <dc:Bounds x="1392" y="482" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1mmssu2_di" bpmnElement="Gateway_1mmssu2" isMarkerVisible="true">
        <dc:Bounds x="1555" y="375" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1yti3y8_di" bpmnElement="ExclusiveGateway_1yti3y8" isMarkerVisible="true">
        <dc:Bounds x="1122" y="375" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1qph207_di" bpmnElement="ExclusiveGateway_1qph207" isMarkerVisible="true">
        <dc:Bounds x="1317" y="375" width="50" height="50" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
