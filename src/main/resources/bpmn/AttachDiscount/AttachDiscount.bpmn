<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1hcyt6q" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.16.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.20.0">
  <bpmn:process id="AttachDiscount" name="AttachDiscount" isExecutable="true" camunda:historyTimeToLive="180">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0ldaiax</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0ldaiax" sourceRef="orderExecStart" targetRef="BillingCreateDiscount" />
    <bpmn:sequenceFlow id="Flow_0cm5dbl" sourceRef="BillingCreateDiscount" targetRef="orderExecEnd" />
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0cm5dbl</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BillingCreateDiscount" name="Billing Create Discount" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0ldaiax</bpmn:incoming>
      <bpmn:outgoing>Flow_0cm5dbl</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="AttachDiscount">
      <bpmndi:BPMNShape id="Event_0n7hhlj_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="472" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0uge817_di" bpmnElement="BillingCreateDiscount">
        <dc:Bounds x="270" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0cm5dbl_di" bpmnElement="Flow_0cm5dbl">
        <di:waypoint x="370" y="117" />
        <di:waypoint x="472" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ldaiax_di" bpmnElement="Flow_0ldaiax">
        <di:waypoint x="188" y="117" />
        <di:waypoint x="270" y="117" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
