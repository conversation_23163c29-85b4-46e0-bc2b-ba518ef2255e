<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.8.0">
  <bpmn:process id="CancelSubscription" name="CancelSubscription" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:exclusiveGateway id="Gateway_0y61vbd" default="Flow_0vv0zhc">
      <bpmn:incoming>Flow_13i5f75</bpmn:incoming>
      <bpmn:outgoing>Flow_0vv0zhc</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_19fmcu1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1jzvrlz" default="Flow_0rx6p7a">
      <bpmn:incoming>Flow_03lp84m</bpmn:incoming>
      <bpmn:outgoing>Flow_0rx6p7a</bpmn:outgoing>
      <bpmn:outgoing>Flow_0e9caqf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1br1me9">
      <bpmn:incoming>Flow_0e9caqf</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:startEvent id="orderExecStart" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_11y9jdn</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0vv0zhc" name="Success" sourceRef="Gateway_0y61vbd" targetRef="Gateway_0d4fuqx" />
    <bpmn:sequenceFlow id="Flow_0rx6p7a" name="Success" sourceRef="Gateway_1jzvrlz" targetRef="Gateway_055u0ju" />
    <bpmn:sequenceFlow id="Flow_0e9caqf" name="Failure" sourceRef="Gateway_1jzvrlz" targetRef="Event_1br1me9">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_11y9jdn" sourceRef="orderExecStart" targetRef="Gateway_1ded9c0" />
    <bpmn:exclusiveGateway id="Gateway_1ded9c0" name="check som call required or not&#10;&#10;" default="Flow_0wugeby">
      <bpmn:incoming>Flow_11y9jdn</bpmn:incoming>
      <bpmn:outgoing>Flow_1m5jo83</bpmn:outgoing>
      <bpmn:outgoing>Flow_0wugeby</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1m5jo83" name="yes&#10;" sourceRef="Gateway_1ded9c0" targetRef="SOMFetchServiceRegistry">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.hasProp('enrichmentResults')&amp;&amp; workflowData.jsonPath("$.enrichmentResults.somCallRqd").boolValue() == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0yakxns">
      <bpmn:incoming>Flow_0wugeby</bpmn:incoming>
      <bpmn:incoming>Flow_161x99i</bpmn:incoming>
      <bpmn:outgoing>Flow_16frnk1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0wugeby" name="no" sourceRef="Gateway_1ded9c0" targetRef="Gateway_0yakxns" />
    <bpmn:endEvent id="EndEvent_1vra2xp">
      <bpmn:incoming>SequenceFlow_19fmcu1</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_19fmcu1" name="Failure" sourceRef="Gateway_0y61vbd" targetRef="EndEvent_1vra2xp">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch Service Registry to get Subscriptions" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1m5jo83</bpmn:incoming>
      <bpmn:outgoing>Flow_14snl0d</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_14qj6cm" default="Flow_0gwis13">
      <bpmn:incoming>Flow_14snl0d</bpmn:incoming>
      <bpmn:outgoing>Flow_0gwis13</bpmn:outgoing>
      <bpmn:outgoing>Flow_1p3nd71</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_14snl0d" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_14qj6cm" />
    <bpmn:sequenceFlow id="Flow_0gwis13" name="Success" sourceRef="Gateway_14qj6cm" targetRef="Gateway_1je9s7i" />
    <bpmn:endEvent id="Event_1tyx8el">
      <bpmn:incoming>Flow_1p3nd71</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1p3nd71" name="Failure" sourceRef="Gateway_14qj6cm" targetRef="Event_1tyx8el">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_16frnk1" sourceRef="Gateway_0yakxns" targetRef="Gateway_1xwau9g" />
    <bpmn:serviceTask id="SOMCancelsubscription" name="SOM Deactivate Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${somCancelSubscription}">
      <bpmn:incoming>Flow_0rtuc14</bpmn:incoming>
      <bpmn:outgoing>Flow_13i5f75</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_13i5f75" sourceRef="SOMCancelsubscription" targetRef="Gateway_0y61vbd" />
    <bpmn:receiveTask id="SOMCancelSubCallback" name="SOMCancelSubCallback" camunda:asyncBefore="true" messageRef="Message_1fyqyx3">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOMCancelsubscription</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOMCancelsubscription" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1s4zdm3</bpmn:incoming>
      <bpmn:outgoing>Flow_03lp84m</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_03lp84m" sourceRef="SOMCancelSubCallback" targetRef="Gateway_1jzvrlz" />
    <bpmn:serviceTask id="BillingCancelSubscription" name="Billing Cancel Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1ugmg2o</bpmn:incoming>
      <bpmn:outgoing>Flow_0v3yb3j</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1je9s7i" default="Flow_1s1qq1g">
      <bpmn:incoming>Flow_0gwis13</bpmn:incoming>
      <bpmn:outgoing>Flow_0rtuc14</bpmn:outgoing>
      <bpmn:outgoing>Flow_1s1qq1g</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0rtuc14" name="som call req" sourceRef="Gateway_1je9s7i" targetRef="SOMCancelsubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${somCancelSubReq}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1q0pdcv">
      <bpmn:incoming>Flow_1s1qq1g</bpmn:incoming>
      <bpmn:incoming>Flow_15bvnv7</bpmn:incoming>
      <bpmn:outgoing>Flow_161x99i</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_161x99i" sourceRef="Gateway_1q0pdcv" targetRef="Gateway_0yakxns" />
    <bpmn:sequenceFlow id="Flow_1s1qq1g" name="som call not req" sourceRef="Gateway_1je9s7i" targetRef="Gateway_1q0pdcv" />
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0v3yb3j</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_09qervv" default="Flow_0d1gx9z">
      <bpmn:incoming>Flow_1lp1dme</bpmn:incoming>
      <bpmn:outgoing>Flow_1t4bpry</bpmn:outgoing>
      <bpmn:outgoing>Flow_0d1gx9z</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="OCSCancelSubscription" name="Subsciption Deactivation in OCS Via ESB" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0n4zwzp</bpmn:incoming>
      <bpmn:outgoing>Flow_1lp1dme</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1tezzgh">
      <bpmn:incoming>Flow_1t4bpry</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1lp1dme" sourceRef="OCSCancelSubscription" targetRef="Gateway_09qervv" />
    <bpmn:sequenceFlow id="Flow_1t4bpry" name="Failure" sourceRef="Gateway_09qervv" targetRef="Event_1tezzgh">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0d1gx9z" name="Success" sourceRef="Gateway_09qervv" targetRef="Gateway_10giny3" />
    <bpmn:sequenceFlow id="Flow_0v3yb3j" sourceRef="BillingCancelSubscription" targetRef="orderExecEnd" />
    <bpmn:exclusiveGateway id="Gateway_1xwau9g" default="Flow_1rcpjb4">
      <bpmn:incoming>Flow_16frnk1</bpmn:incoming>
      <bpmn:outgoing>Flow_0n4zwzp</bpmn:outgoing>
      <bpmn:outgoing>Flow_1rcpjb4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0n4zwzp" sourceRef="Gateway_1xwau9g" targetRef="OCSCancelSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${ocsCallReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_10giny3">
      <bpmn:incoming>Flow_0d1gx9z</bpmn:incoming>
      <bpmn:incoming>Flow_1rcpjb4</bpmn:incoming>
      <bpmn:outgoing>Flow_1ugmg2o</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ugmg2o" sourceRef="Gateway_10giny3" targetRef="BillingCancelSubscription" />
    <bpmn:sequenceFlow id="Flow_1rcpjb4" sourceRef="Gateway_1xwau9g" targetRef="Gateway_10giny3" />
    <bpmn:exclusiveGateway id="Gateway_0d4fuqx" camunda:asyncBefore="true" default="Flow_10nxorc">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.CallBackExecutionListener" event="start" />
        <camunda:properties>
          <camunda:property name="callBackType" value="SOMCallback" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0vv0zhc</bpmn:incoming>
      <bpmn:outgoing>Flow_1s4zdm3</bpmn:outgoing>
      <bpmn:outgoing>Flow_10nxorc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1s4zdm3" sourceRef="Gateway_0d4fuqx" targetRef="SOMCancelSubCallback">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callBackProcessReqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_055u0ju">
      <bpmn:incoming>Flow_0rx6p7a</bpmn:incoming>
      <bpmn:incoming>Flow_10nxorc</bpmn:incoming>
      <bpmn:outgoing>Flow_15bvnv7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_15bvnv7" sourceRef="Gateway_055u0ju" targetRef="Gateway_1q0pdcv" />
    <bpmn:sequenceFlow id="Flow_10nxorc" sourceRef="Gateway_0d4fuqx" targetRef="Gateway_055u0ju" />
  </bpmn:process>
  <bpmn:message id="Message_18lupa4" name="SOMAddSubCallback" />
  <bpmn:message id="Message_1fyqyx3" name="SOMCancelSubCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CancelSubscription">
      <bpmndi:BPMNShape id="Gateway_0yakxns_di" bpmnElement="Gateway_0yakxns" isMarkerVisible="true">
        <dc:Bounds x="1926" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_14h98ga" bpmnElement="BillingCancelSubscription">
        <dc:Bounds x="2610" y="330" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_16wx4h3_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2852" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0m2wcbn" bpmnElement="Gateway_09qervv" isMarkerVisible="true">
        <dc:Bounds x="2345" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_10k8uh4" bpmnElement="OCSCancelSubscription">
        <dc:Bounds x="2160" y="330" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06j38ii" bpmnElement="Event_1tezzgh">
        <dc:Bounds x="2352" y="474" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1xwau9g_di" bpmnElement="Gateway_1xwau9g" isMarkerVisible="true">
        <dc:Bounds x="2025" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_10giny3_di" bpmnElement="Gateway_10giny3" isMarkerVisible="true">
        <dc:Bounds x="2475" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1q0pdcv_di" bpmnElement="Gateway_1q0pdcv" isMarkerVisible="true">
        <dc:Bounds x="1815" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0px3azh_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="352" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ded9c0_di" bpmnElement="Gateway_1ded9c0" isMarkerVisible="true">
        <dc:Bounds x="275" y="345" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="264" y="402" width="73" height="53" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1l7h8rx_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="380" y="330" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_14qj6cm_di" bpmnElement="Gateway_14qj6cm" isMarkerVisible="true">
        <dc:Bounds x="545" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1tyx8el_di" bpmnElement="Event_1tyx8el">
        <dc:Bounds x="552" y="512" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1je9s7i_di" bpmnElement="Gateway_1je9s7i" isMarkerVisible="true">
        <dc:Bounds x="695" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ao82aw_di" bpmnElement="SOMCancelsubscription">
        <dc:Bounds x="840" y="330" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0y61vbd_di" bpmnElement="Gateway_0y61vbd" isMarkerVisible="true">
        <dc:Bounds x="1015" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1vra2xp_di" bpmnElement="EndEvent_1vra2xp">
        <dc:Bounds x="1022" y="492" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0yl1sh8" bpmnElement="Gateway_0d4fuqx" isMarkerVisible="true">
        <dc:Bounds x="1175" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1pwol3v_di" bpmnElement="SOMCancelSubCallback">
        <dc:Bounds x="1350" y="330" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1jzvrlz_di" bpmnElement="Gateway_1jzvrlz" isMarkerVisible="true">
        <dc:Bounds x="1535" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1br1me9_di" bpmnElement="Event_1br1me9">
        <dc:Bounds x="1542" y="492" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0addu73" bpmnElement="Gateway_055u0ju" isMarkerVisible="true">
        <dc:Bounds x="1685" y="345" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0wugeby_di" bpmnElement="Flow_0wugeby">
        <di:waypoint x="300" y="345" />
        <di:waypoint x="300" y="120" />
        <di:waypoint x="1951" y="120" />
        <di:waypoint x="1951" y="345" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1312" y="83" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_161x99i_di" bpmnElement="Flow_161x99i">
        <di:waypoint x="1865" y="370" />
        <di:waypoint x="1926" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16frnk1_di" bpmnElement="Flow_16frnk1">
        <di:waypoint x="1976" y="370" />
        <di:waypoint x="2025" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ugmg2o_di" bpmnElement="Flow_1ugmg2o">
        <di:waypoint x="2525" y="370" />
        <di:waypoint x="2610" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0v3yb3j_di" bpmnElement="Flow_0v3yb3j">
        <di:waypoint x="2710" y="370" />
        <di:waypoint x="2852" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0u14x0a" bpmnElement="Flow_1lp1dme">
        <di:waypoint x="2260" y="370" />
        <di:waypoint x="2345" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1vhuy9m" bpmnElement="Flow_1t4bpry">
        <di:waypoint x="2370" y="395" />
        <di:waypoint x="2370" y="474" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2373" y="416" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d1gx9z_di" bpmnElement="Flow_0d1gx9z">
        <di:waypoint x="2395" y="370" />
        <di:waypoint x="2475" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2741" y="352" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n4zwzp_di" bpmnElement="Flow_0n4zwzp">
        <di:waypoint x="2075" y="370" />
        <di:waypoint x="2160" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rcpjb4_di" bpmnElement="Flow_1rcpjb4">
        <di:waypoint x="2050" y="345" />
        <di:waypoint x="2050" y="250" />
        <di:waypoint x="2500" y="250" />
        <di:waypoint x="2500" y="345" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s1qq1g_di" bpmnElement="Flow_1s1qq1g">
        <di:waypoint x="720" y="345" />
        <di:waypoint x="720" y="170" />
        <di:waypoint x="1840" y="170" />
        <di:waypoint x="1840" y="345" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1234" y="133" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15bvnv7_di" bpmnElement="Flow_15bvnv7">
        <di:waypoint x="1735" y="370" />
        <di:waypoint x="1815" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11y9jdn_di" bpmnElement="Flow_11y9jdn">
        <di:waypoint x="188" y="370" />
        <di:waypoint x="275" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m5jo83_di" bpmnElement="Flow_1m5jo83">
        <di:waypoint x="325" y="370" />
        <di:waypoint x="380" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="337" y="345" width="18" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14snl0d_di" bpmnElement="Flow_14snl0d">
        <di:waypoint x="480" y="370" />
        <di:waypoint x="545" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gwis13_di" bpmnElement="Flow_0gwis13">
        <di:waypoint x="595" y="370" />
        <di:waypoint x="695" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="589" y="346" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p3nd71_di" bpmnElement="Flow_1p3nd71">
        <di:waypoint x="570" y="395" />
        <di:waypoint x="570" y="512" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="573" y="442" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rtuc14_di" bpmnElement="Flow_0rtuc14">
        <di:waypoint x="745" y="370" />
        <di:waypoint x="840" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="750" y="352" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13i5f75_di" bpmnElement="Flow_13i5f75">
        <di:waypoint x="940" y="370" />
        <di:waypoint x="1015" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vv0zhc_di" bpmnElement="Flow_0vv0zhc">
        <di:waypoint x="1065" y="370" />
        <di:waypoint x="1175" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1096" y="352" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_19fmcu1_di" bpmnElement="SequenceFlow_19fmcu1">
        <di:waypoint x="1040" y="395" />
        <di:waypoint x="1040" y="492" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1043" y="433" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s4zdm3_di" bpmnElement="Flow_1s4zdm3">
        <di:waypoint x="1225" y="370" />
        <di:waypoint x="1350" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10nxorc_di" bpmnElement="Flow_10nxorc">
        <di:waypoint x="1200" y="345" />
        <di:waypoint x="1200" y="260" />
        <di:waypoint x="1710" y="260" />
        <di:waypoint x="1710" y="345" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03lp84m_di" bpmnElement="Flow_03lp84m">
        <di:waypoint x="1450" y="370" />
        <di:waypoint x="1535" y="370" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rx6p7a_di" bpmnElement="Flow_0rx6p7a">
        <di:waypoint x="1585" y="370" />
        <di:waypoint x="1685" y="370" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1613" y="346" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e9caqf_di" bpmnElement="Flow_0e9caqf">
        <di:waypoint x="1560" y="395" />
        <di:waypoint x="1560" y="492" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1563" y="433" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
