<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="StopAutoRenewal" name="StopAutoRenewal" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:serviceTask id="BSUpdateSubscription" name="Billing UpdateSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_04x79el</bpmn:incoming>
      <bpmn:outgoing>Flow_1nzkyg3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1n0dnj1</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1nzkyg3" sourceRef="BSUpdateSubscription" targetRef="Gateway_1319yfl" />
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1xqman8</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0vxml2n" sourceRef="NCCUpdateSubscription" targetRef="Gateway_080yi47" />
    <bpmn:exclusiveGateway id="Gateway_080yi47" default="Flow_0la6fac">
      <bpmn:incoming>Flow_0vxml2n</bpmn:incoming>
      <bpmn:outgoing>Flow_0d6rsvk</bpmn:outgoing>
      <bpmn:outgoing>Flow_0la6fac</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0d6rsvk" name="Success" sourceRef="Gateway_080yi47" targetRef="Gateway_1tgugoo">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_0sn6t3l">
      <bpmn:incoming>Flow_0la6fac</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0la6fac" name="Failure" sourceRef="Gateway_080yi47" targetRef="Event_0sn6t3l" />
    <bpmn:sequenceFlow id="Flow_15d7psn" sourceRef="SMUpdateSubscription" targetRef="Gateway_0vvs9yw" />
    <bpmn:exclusiveGateway id="Gateway_0vvs9yw" default="Flow_1gt97an">
      <bpmn:incoming>Flow_15d7psn</bpmn:incoming>
      <bpmn:outgoing>Flow_0joa3qm</bpmn:outgoing>
      <bpmn:outgoing>Flow_1gt97an</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0joa3qm" name="Success" sourceRef="Gateway_0vvs9yw" targetRef="Gateway_1gbrlik">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_18230vp">
      <bpmn:incoming>Flow_1gt97an</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1gt97an" name="Failure" sourceRef="Gateway_0vvs9yw" targetRef="Event_18230vp" />
    <bpmn:serviceTask id="NCCUpdateSubscription" name="UpdateSubscription in NCC" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1hucezu</bpmn:incoming>
      <bpmn:outgoing>Flow_0vxml2n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="SMUpdateSubscription" name="SM UpdateSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0aa5gfy</bpmn:incoming>
      <bpmn:outgoing>Flow_15d7psn</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1gbrlik" name="Connectiion Type" default="Flow_1a8bq0n">
      <bpmn:incoming>Flow_0joa3qm</bpmn:incoming>
      <bpmn:outgoing>Flow_04x79el</bpmn:outgoing>
      <bpmn:outgoing>Flow_1a8bq0n</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_04x79el" name="postpaid" sourceRef="Gateway_1gbrlik" targetRef="BSUpdateSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.hasProp('enrichmentResults') &amp;&amp; workflowData.jsonPath("$.enrichmentResults.serviceInfo.chargingPattern").stringValue() == '1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1319yfl" default="Flow_03fjmkr">
      <bpmn:incoming>Flow_1nzkyg3</bpmn:incoming>
      <bpmn:outgoing>Flow_1ly63de</bpmn:outgoing>
      <bpmn:outgoing>Flow_03fjmkr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ly63de" name="Success" sourceRef="Gateway_1319yfl" targetRef="Gateway_11xny4h">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_06pdf71">
      <bpmn:incoming>Flow_03fjmkr</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_03fjmkr" name="Failure" sourceRef="Gateway_1319yfl" targetRef="Event_06pdf71" />
    <bpmn:exclusiveGateway id="Gateway_11xny4h">
      <bpmn:incoming>Flow_1ly63de</bpmn:incoming>
      <bpmn:incoming>Flow_1a8bq0n</bpmn:incoming>
      <bpmn:outgoing>Flow_1n0dnj1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1n0dnj1" sourceRef="Gateway_11xny4h" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_1a8bq0n" name="prepaid" sourceRef="Gateway_1gbrlik" targetRef="Gateway_11xny4h" />
    <bpmn:sequenceFlow id="Flow_1xqman8" sourceRef="orderExecStart" targetRef="OrderEnrichment" />
    <bpmn:exclusiveGateway id="Gateway_17q99ys" name="Esb Call Reqd" default="Flow_15ia90w">
      <bpmn:incoming>Flow_198yx4f</bpmn:incoming>
      <bpmn:outgoing>Flow_1hucezu</bpmn:outgoing>
      <bpmn:outgoing>Flow_15ia90w</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1tgugoo">
      <bpmn:incoming>Flow_0d6rsvk</bpmn:incoming>
      <bpmn:incoming>Flow_15ia90w</bpmn:incoming>
      <bpmn:outgoing>Flow_0aa5gfy</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0aa5gfy" sourceRef="Gateway_1tgugoo" targetRef="SMUpdateSubscription" />
    <bpmn:sequenceFlow id="Flow_1hucezu" name="esb call Reqd true" sourceRef="Gateway_17q99ys" targetRef="NCCUpdateSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order.serviceManagement.subscriptions[0].esbCallRqd").boolValue() == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_15ia90w" name="False" sourceRef="Gateway_17q99ys" targetRef="Gateway_1tgugoo" />
    <bpmn:exclusiveGateway id="Gateway_0fuzl5a" default="Flow_198yx4f">
      <bpmn:incoming>Flow_0vvv1ml</bpmn:incoming>
      <bpmn:outgoing>Flow_1pgh3s3</bpmn:outgoing>
      <bpmn:outgoing>Flow_198yx4f</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1fi99c8">
      <bpmn:incoming>Flow_1pgh3s3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="OrderEnrichment" name="Order Enrichment Delegate" camunda:asyncBefore="true" camunda:delegateExpression="${orderEnrichmentDelegate}">
      <bpmn:incoming>Flow_1xqman8</bpmn:incoming>
      <bpmn:outgoing>Flow_0vvv1ml</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0vvv1ml" sourceRef="OrderEnrichment" targetRef="Gateway_0fuzl5a" />
    <bpmn:sequenceFlow id="Flow_1pgh3s3" name="Failure" sourceRef="Gateway_0fuzl5a" targetRef="Event_1fi99c8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_198yx4f" name="Success" sourceRef="Gateway_0fuzl5a" targetRef="Gateway_17q99ys" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="StopAutoRenewal">
      <bpmndi:BPMNEdge id="Flow_198yx4f_di" bpmnElement="Flow_198yx4f">
        <di:waypoint x="495" y="220" />
        <di:waypoint x="605" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="529" y="202" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pgh3s3_di" bpmnElement="Flow_1pgh3s3">
        <di:waypoint x="470" y="245" />
        <di:waypoint x="470" y="350" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="473" y="274" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vvv1ml_di" bpmnElement="Flow_0vvv1ml">
        <di:waypoint x="380" y="220" />
        <di:waypoint x="445" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15ia90w_di" bpmnElement="Flow_15ia90w">
        <di:waypoint x="630" y="195" />
        <di:waypoint x="630" y="120" />
        <di:waypoint x="1170" y="120" />
        <di:waypoint x="1170" y="195" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="887" y="102" width="27" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hucezu_di" bpmnElement="Flow_1hucezu">
        <di:waypoint x="655" y="220" />
        <di:waypoint x="800" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="675" y="191" width="89" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0aa5gfy_di" bpmnElement="Flow_0aa5gfy">
        <di:waypoint x="1195" y="220" />
        <di:waypoint x="1270" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xqman8_di" bpmnElement="Flow_1xqman8">
        <di:waypoint x="188" y="220" />
        <di:waypoint x="280" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a8bq0n_di" bpmnElement="Flow_1a8bq0n">
        <di:waypoint x="1590" y="195" />
        <di:waypoint x="1590" y="130" />
        <di:waypoint x="2060" y="130" />
        <di:waypoint x="2060" y="195" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1807" y="112" width="37" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1n0dnj1_di" bpmnElement="Flow_1n0dnj1">
        <di:waypoint x="2085" y="220" />
        <di:waypoint x="2182" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03fjmkr_di" bpmnElement="Flow_03fjmkr">
        <di:waypoint x="1920" y="245" />
        <di:waypoint x="1920" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1923" y="271" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ly63de_di" bpmnElement="Flow_1ly63de">
        <di:waypoint x="1945" y="220" />
        <di:waypoint x="2035" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1969" y="202" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04x79el_di" bpmnElement="Flow_04x79el">
        <di:waypoint x="1615" y="220" />
        <di:waypoint x="1730" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1654" y="202" width="42" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gt97an_di" bpmnElement="Flow_1gt97an">
        <di:waypoint x="1440" y="245" />
        <di:waypoint x="1440" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1443" y="276" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0joa3qm_di" bpmnElement="Flow_0joa3qm">
        <di:waypoint x="1465" y="220" />
        <di:waypoint x="1565" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1485" y="202" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15d7psn_di" bpmnElement="Flow_15d7psn">
        <di:waypoint x="1370" y="220" />
        <di:waypoint x="1415" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0la6fac_di" bpmnElement="Flow_0la6fac">
        <di:waypoint x="1000" y="245" />
        <di:waypoint x="1000" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1003" y="280" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d6rsvk_di" bpmnElement="Flow_0d6rsvk">
        <di:waypoint x="1025" y="220" />
        <di:waypoint x="1145" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1055" y="202" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vxml2n_di" bpmnElement="Flow_0vxml2n">
        <di:waypoint x="900" y="220" />
        <di:waypoint x="975" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nzkyg3_di" bpmnElement="Flow_1nzkyg3">
        <di:waypoint x="1830" y="220" />
        <di:waypoint x="1895" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0oi7fzj_di" bpmnElement="BSUpdateSubscription">
        <dc:Bounds x="1730" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1qkykgh_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2182" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1lur0ix_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_080yi47_di" bpmnElement="Gateway_080yi47" isMarkerVisible="true">
        <dc:Bounds x="975" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0sn6t3l_di" bpmnElement="Event_0sn6t3l">
        <dc:Bounds x="982" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0vvs9yw_di" bpmnElement="Gateway_0vvs9yw" isMarkerVisible="true">
        <dc:Bounds x="1415" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_18230vp_di" bpmnElement="Event_18230vp">
        <dc:Bounds x="1422" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1qgs7pn_di" bpmnElement="NCCUpdateSubscription">
        <dc:Bounds x="800" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1gg8048_di" bpmnElement="SMUpdateSubscription">
        <dc:Bounds x="1270" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1gbrlik_di" bpmnElement="Gateway_1gbrlik" isMarkerVisible="true">
        <dc:Bounds x="1565" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1548" y="252" width="85" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1319yfl_di" bpmnElement="Gateway_1319yfl" isMarkerVisible="true">
        <dc:Bounds x="1895" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_06pdf71_di" bpmnElement="Event_06pdf71">
        <dc:Bounds x="1902" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_11xny4h_di" bpmnElement="Gateway_11xny4h" isMarkerVisible="true">
        <dc:Bounds x="2035" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_17q99ys_di" bpmnElement="Gateway_17q99ys" isMarkerVisible="true">
        <dc:Bounds x="605" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="596" y="252" width="71" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tgugoo_di" bpmnElement="Gateway_1tgugoo" isMarkerVisible="true">
        <dc:Bounds x="1145" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fuzl5a_di" bpmnElement="Gateway_0fuzl5a" isMarkerVisible="true">
        <dc:Bounds x="445" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1fi99c8_di" bpmnElement="Event_1fi99c8">
        <dc:Bounds x="452" y="350" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01wyi3h_di" bpmnElement="OrderEnrichment">
        <dc:Bounds x="280" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
