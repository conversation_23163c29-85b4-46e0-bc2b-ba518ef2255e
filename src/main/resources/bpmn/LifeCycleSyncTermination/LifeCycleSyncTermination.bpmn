<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1xap55q" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="LifeCycleSyncTermination" isExecutable="true" camunda:jobPriority="${priority}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1desc4o</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1desc4o" sourceRef="orderExecStart" targetRef="SOMFetchServiceRegistry" />
    <bpmn:serviceTask id="BSLifeCycleTermination" name="Billing LifeCycleTermination" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_04tjd9a</bpmn:incoming>
      <bpmn:outgoing>Flow_1ujuviq</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0j4rk40" default="Flow_03i6ukk">
      <bpmn:incoming>Flow_1ffttxw</bpmn:incoming>
      <bpmn:outgoing>Flow_0wmm778</bpmn:outgoing>
      <bpmn:outgoing>Flow_03i6ukk</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_164xuyz">
      <bpmn:incoming>Flow_0wmm778</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0wmm778" name="Failure" sourceRef="Gateway_0j4rk40" targetRef="Event_164xuyz">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_03i6ukk" name="Success" sourceRef="Gateway_0j4rk40" targetRef="SMLifeCycleTermination" />
    <bpmn:exclusiveGateway id="Gateway_0dtk7rr" default="Flow_0rpkbfh">
      <bpmn:incoming>Flow_0nuphds</bpmn:incoming>
      <bpmn:outgoing>Flow_0rpkbfh</bpmn:outgoing>
      <bpmn:outgoing>Flow_0wnikr9</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0rpkbfh" name="Success" sourceRef="Gateway_0dtk7rr" targetRef="SOMLifeCycleTermination" />
    <bpmn:endEvent id="Event_0trewhl">
      <bpmn:incoming>Flow_0wnikr9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0wnikr9" name="Failure" sourceRef="Gateway_0dtk7rr" targetRef="Event_0trewhl">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="SMLifeCycleTermination" name="SM LifeCycleTermination" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_03i6ukk</bpmn:incoming>
      <bpmn:outgoing>Flow_0lkqhqf</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ewwdhg" sourceRef="SOMLifeCycleTermination" targetRef="Gateway_1c9g6wi" />
    <bpmn:serviceTask id="SOMLifeCycleTermination" name="SOM LifeCycleTermination" camunda:asyncBefore="true" camunda:delegateExpression="${somTerminateService}">
      <bpmn:incoming>Flow_0rpkbfh</bpmn:incoming>
      <bpmn:outgoing>Flow_0ewwdhg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1c9g6wi" default="Flow_00q7o4z">
      <bpmn:incoming>Flow_0ewwdhg</bpmn:incoming>
      <bpmn:outgoing>Flow_00q7o4z</bpmn:outgoing>
      <bpmn:outgoing>Flow_1b8tggi</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_00q7o4z" name="Success" sourceRef="Gateway_1c9g6wi" targetRef="SOMLifeCycleTerminationCallback" />
    <bpmn:endEvent id="Event_0suk5rs">
      <bpmn:incoming>Flow_1b8tggi</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1b8tggi" name="Failure" sourceRef="Gateway_1c9g6wi" targetRef="Event_0suk5rs">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="SOMLifeCycleTerminationCallback" name="SOM LifeCycleTerminationCallback" messageRef="Message_02m47bb">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOMLifeCycleTermination</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_00q7o4z</bpmn:incoming>
      <bpmn:outgoing>Flow_1ffttxw</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_10x05ok" default="Flow_04tjd9a">
      <bpmn:incoming>Flow_0lkqhqf</bpmn:incoming>
      <bpmn:outgoing>Flow_0fxjpmi</bpmn:outgoing>
      <bpmn:outgoing>Flow_04tjd9a</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_1yt2dgo">
      <bpmn:incoming>Flow_0fxjpmi</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch Service Registry to get Subscriptions" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1desc4o</bpmn:incoming>
      <bpmn:outgoing>Flow_0nuphds</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0fxjpmi" name="Failure" sourceRef="Gateway_10x05ok" targetRef="Event_1yt2dgo">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_04tjd9a" name="Success" sourceRef="Gateway_10x05ok" targetRef="BSLifeCycleTermination" />
    <bpmn:sequenceFlow id="Flow_1ffttxw" sourceRef="SOMLifeCycleTerminationCallback" targetRef="Gateway_0j4rk40" />
    <bpmn:sequenceFlow id="Flow_0lkqhqf" sourceRef="SMLifeCycleTermination" targetRef="Gateway_10x05ok" />
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1ujuviq</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1ujuviq" sourceRef="BSLifeCycleTermination" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_0nuphds" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_0dtk7rr" />
  </bpmn:process>
  <bpmn:message id="Message_02m47bb" name="SOMLifeCycleTerminationCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="LifeCycleSyncTermination">
      <bpmndi:BPMNEdge id="Flow_0nuphds_di" bpmnElement="Flow_0nuphds">
        <di:waypoint x="400" y="117" />
        <di:waypoint x="505" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ujuviq_di" bpmnElement="Flow_1ujuviq">
        <di:waypoint x="1910" y="117" />
        <di:waypoint x="2002" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lkqhqf_di" bpmnElement="Flow_0lkqhqf">
        <di:waypoint x="1580" y="117" />
        <di:waypoint x="1635" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ffttxw_di" bpmnElement="Flow_1ffttxw">
        <di:waypoint x="1120" y="117" />
        <di:waypoint x="1205" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04tjd9a_di" bpmnElement="Flow_04tjd9a">
        <di:waypoint x="1685" y="117" />
        <di:waypoint x="1810" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1688" y="99" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fxjpmi_di" bpmnElement="Flow_0fxjpmi">
        <di:waypoint x="1660" y="142" />
        <di:waypoint x="1660" y="202" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1673" y="163" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1b8tggi_di" bpmnElement="Flow_1b8tggi">
        <di:waypoint x="850" y="142" />
        <di:waypoint x="850" y="222" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="848" y="194" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00q7o4z_di" bpmnElement="Flow_00q7o4z">
        <di:waypoint x="875" y="117" />
        <di:waypoint x="1020" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="926" y="99" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ewwdhg_di" bpmnElement="Flow_0ewwdhg">
        <di:waypoint x="760" y="117" />
        <di:waypoint x="825" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wnikr9_di" bpmnElement="Flow_0wnikr9">
        <di:waypoint x="530" y="142" />
        <di:waypoint x="530" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="529" y="179" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rpkbfh_di" bpmnElement="Flow_0rpkbfh">
        <di:waypoint x="555" y="117" />
        <di:waypoint x="660" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="578" y="99" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03i6ukk_di" bpmnElement="Flow_03i6ukk">
        <di:waypoint x="1255" y="117" />
        <di:waypoint x="1480" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1286" y="99" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wmm778_di" bpmnElement="Flow_0wmm778">
        <di:waypoint x="1230" y="142" />
        <di:waypoint x="1230" y="222" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1229" y="185" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1desc4o_di" bpmnElement="Flow_1desc4o">
        <di:waypoint x="188" y="117" />
        <di:waypoint x="300" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_06hy9lw_di" bpmnElement="BSLifeCycleTermination">
        <dc:Bounds x="1810" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0j4rk40_di" bpmnElement="Gateway_0j4rk40" isMarkerVisible="true">
        <dc:Bounds x="1205" y="92" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_164xuyz_di" bpmnElement="Event_164xuyz">
        <dc:Bounds x="1212" y="222" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0dtk7rr_di" bpmnElement="Gateway_0dtk7rr" isMarkerVisible="true">
        <dc:Bounds x="505" y="92" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0trewhl_di" bpmnElement="Event_0trewhl">
        <dc:Bounds x="512" y="212" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ky2khh_di" bpmnElement="SMLifeCycleTermination">
        <dc:Bounds x="1480" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1dlxd4z_di" bpmnElement="SOMLifeCycleTermination">
        <dc:Bounds x="660" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1c9g6wi_di" bpmnElement="Gateway_1c9g6wi" isMarkerVisible="true">
        <dc:Bounds x="825" y="92" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0suk5rs_di" bpmnElement="Event_0suk5rs">
        <dc:Bounds x="832" y="222" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1h5e770_di" bpmnElement="SOMLifeCycleTerminationCallback">
        <dc:Bounds x="1020" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_10x05ok_di" bpmnElement="Gateway_10x05ok" isMarkerVisible="true">
        <dc:Bounds x="1635" y="92" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1yt2dgo_di" bpmnElement="Event_1yt2dgo">
        <dc:Bounds x="1642" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0gq2axm_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="300" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ucircw_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2002" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
