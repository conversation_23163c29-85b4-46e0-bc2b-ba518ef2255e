<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1lsckuo" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="UpdateDocument" name="UpdateDocument" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1ybz3m3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1ybz3m3" sourceRef="orderExecStart" targetRef="Gateway_0uhzbw4" />
    <bpmn:serviceTask id="PMUpdateDocument" name="PM Update Document" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1sqnvpe</bpmn:incoming>
      <bpmn:outgoing>Flow_0s5d3qe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0uhzbw4" name="if isIdDocument true OR false" default="Flow_1h742ci">
      <bpmn:incoming>Flow_1ybz3m3</bpmn:incoming>
      <bpmn:outgoing>Flow_1sqnvpe</bpmn:outgoing>
      <bpmn:outgoing>Flow_1h742ci</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1sqnvpe" name=" true" sourceRef="Gateway_0uhzbw4" targetRef="PMUpdateDocument">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order.documentDetails.isIdDocument").stringValue() == 'true'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSUpdateDocument" name="BS Update Document" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1h742ci</bpmn:incoming>
      <bpmn:outgoing>Flow_0qcv8z6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1h742ci" sourceRef="Gateway_0uhzbw4" targetRef="BSUpdateDocument" />
    <bpmn:exclusiveGateway id="Gateway_1b95gan">
      <bpmn:incoming>Flow_0qcv8z6</bpmn:incoming>
      <bpmn:incoming>Flow_0s5d3qe</bpmn:incoming>
      <bpmn:outgoing>Flow_1drwt3x</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0qcv8z6" sourceRef="BSUpdateDocument" targetRef="Gateway_1b95gan" />
    <bpmn:sequenceFlow id="Flow_0s5d3qe" sourceRef="PMUpdateDocument" targetRef="Gateway_1b95gan" />
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1drwt3x</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1drwt3x" sourceRef="Gateway_1b95gan" targetRef="orderExecEnd" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="UpdateDocument">
      <bpmndi:BPMNEdge id="Flow_1drwt3x_di" bpmnElement="Flow_1drwt3x">
        <di:waypoint x="725" y="210" />
        <di:waypoint x="802" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0s5d3qe_di" bpmnElement="Flow_0s5d3qe">
        <di:waypoint x="560" y="287" />
        <di:waypoint x="700" y="287" />
        <di:waypoint x="700" y="235" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qcv8z6_di" bpmnElement="Flow_0qcv8z6">
        <di:waypoint x="570" y="120" />
        <di:waypoint x="700" y="120" />
        <di:waypoint x="700" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h742ci_di" bpmnElement="Flow_1h742ci">
        <di:waypoint x="310" y="262" />
        <di:waypoint x="310" y="120" />
        <di:waypoint x="470" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="218" y="176" width="84" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sqnvpe_di" bpmnElement="Flow_1sqnvpe">
        <di:waypoint x="335" y="287" />
        <di:waypoint x="460" y="287" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="391" y="256" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ybz3m3_di" bpmnElement="Flow_1ybz3m3">
        <di:waypoint x="188" y="287" />
        <di:waypoint x="285" y="287" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="269" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0oe96hx_di" bpmnElement="PMUpdateDocument">
        <dc:Bounds x="460" y="247" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0uhzbw4_di" bpmnElement="Gateway_0uhzbw4" isMarkerVisible="true">
        <dc:Bounds x="285" y="262" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="274" y="319" width="77" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1222hl4_di" bpmnElement="BSUpdateDocument">
        <dc:Bounds x="470" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1b95gan_di" bpmnElement="Gateway_1b95gan" isMarkerVisible="true">
        <dc:Bounds x="675" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1o8mb60_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="802" y="192" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
