<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0jfkxu9" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="CancelSafeCustody" name="CancelSafeCustody" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>SequenceFlow_1f65mfl</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>SequenceFlow_0q7aulm</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSCancelSafeCustody" name="BS Cancel Safe Custody" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_1f65mfl</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0q7aulm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1f65mfl" sourceRef="orderExecStart" targetRef="BSCancelSafeCustody" />
    <bpmn:sequenceFlow id="SequenceFlow_0q7aulm" sourceRef="BSCancelSafeCustody" targetRef="orderExecEnd" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CancelSafeCustody">
      <bpmndi:BPMNEdge id="SequenceFlow_0q7aulm_di" bpmnElement="SequenceFlow_0q7aulm">
        <di:waypoint x="402" y="121" />
        <di:waypoint x="468" y="121" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1f65mfl_di" bpmnElement="SequenceFlow_1f65mfl">
        <di:waypoint x="228" y="121" />
        <di:waypoint x="302" y="121" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="StartEvent_00b5rc6_di" bpmnElement="orderExecStart">
        <dc:Bounds x="192" y="103" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_05s0ocb_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="468" y="103" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1jea4er_di" bpmnElement="BSCancelSafeCustody">
        <dc:Bounds x="302" y="81" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
