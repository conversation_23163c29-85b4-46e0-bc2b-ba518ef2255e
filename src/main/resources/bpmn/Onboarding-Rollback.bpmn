<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0xt6om3" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0">
  <bpmn:process id="Onboarding-Rollback" name="Onboarding-Rollback" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" camunda:asyncBefore="true">
      <bpmn:outgoing>SequenceFlow_140om64</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="SequenceFlow_140om64" sourceRef="StartEvent_1" targetRef="Gateway_1tkyr42" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_1222wig">
      <bpmn:incoming>SequenceFlow_0vgw1x3</bpmn:incoming>
      <bpmn:incoming>Flow_0rm6ies</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_19b6n6t</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0vgw1x3" sourceRef="BSDeleteAccount" targetRef="ExclusiveGateway_1222wig" />
    <bpmn:sequenceFlow id="SequenceFlow_19b6n6t" sourceRef="ExclusiveGateway_1222wig" targetRef="ExclusiveGateway_1xr9z89" />
    <bpmn:endEvent id="EndEvent_17dzzi8">
      <bpmn:incoming>Flow_1ou6mi8</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0t1ijim" sourceRef="BSDeleteProfile" targetRef="ExclusiveGateway_1bpowob" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_14kign4" name="is ocs account deletion required?" default="SequenceFlow_0fbo9h1">
      <bpmn:incoming>Flow_1i050lf</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0njj8dc</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0fbo9h1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0njj8dc" name="Yes" sourceRef="ExclusiveGateway_14kign4" targetRef="OCSSDeleteAccount">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${esb_create_account}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0fbo9h1" name="No" sourceRef="ExclusiveGateway_14kign4" targetRef="Gateway_1m17hor" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_1xr9z89" name="Is ProfileId created" default="SequenceFlow_02u3rfj">
      <bpmn:incoming>SequenceFlow_19b6n6t</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0w74tvg</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_02u3rfj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0w74tvg" name="Yes" sourceRef="ExclusiveGateway_1xr9z89" targetRef="BSDeleteProfile">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${bs_create_profile}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1bpowob">
      <bpmn:incoming>SequenceFlow_0t1ijim</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_02u3rfj</bpmn:incoming>
      <bpmn:outgoing>Flow_0o68jkb</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_02u3rfj" name="No" sourceRef="ExclusiveGateway_1xr9z89" targetRef="ExclusiveGateway_1bpowob" />
    <bpmn:serviceTask id="BSDeleteAccount" name="BS Delete Account" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1y14ar4</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0vgw1x3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSDeleteProfile" name="BS Delete Profile" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_0w74tvg</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0t1ijim</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0cghwid" sourceRef="NMSDeleteService" targetRef="Gateway_1tdmr0t" />
    <bpmn:sequenceFlow id="Flow_02jg7v7" sourceRef="BSDeleteService" targetRef="Gateway_0igtw6k" />
    <bpmn:sequenceFlow id="Flow_0qjxjpj" sourceRef="OCSSDeleteAccount" targetRef="Gateway_1m17hor" />
    <bpmn:serviceTask id="OCSSDeleteAccount" name="OCS Delete Account" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_0njj8dc</bpmn:incoming>
      <bpmn:outgoing>Flow_0qjxjpj</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1tkyr42" name="is nms sim unpairing required" default="Flow_15lngnb">
      <bpmn:incoming>SequenceFlow_140om64</bpmn:incoming>
      <bpmn:outgoing>Flow_1x8sknd</bpmn:outgoing>
      <bpmn:outgoing>Flow_15lngnb</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1x8sknd" sourceRef="Gateway_1tkyr42" targetRef="NMSDeleteService">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${nms_pair_sim}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0igtw6k">
      <bpmn:incoming>Flow_02jg7v7</bpmn:incoming>
      <bpmn:incoming>Flow_0qi1ssn</bpmn:incoming>
      <bpmn:outgoing>Flow_1i050lf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1i050lf" sourceRef="Gateway_0igtw6k" targetRef="ExclusiveGateway_14kign4" />
    <bpmn:serviceTask id="NMSDeleteService" name="NMS Delete Service" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1x8sknd</bpmn:incoming>
      <bpmn:outgoing>Flow_0cghwid</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSDeleteService" name="BS Delete Service" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_038v9ql</bpmn:incoming>
      <bpmn:outgoing>Flow_02jg7v7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_15lngnb" sourceRef="Gateway_1tkyr42" targetRef="Gateway_1tdmr0t" />
    <bpmn:exclusiveGateway id="Gateway_1tdmr0t">
      <bpmn:incoming>Flow_0cghwid</bpmn:incoming>
      <bpmn:incoming>Flow_15lngnb</bpmn:incoming>
      <bpmn:outgoing>Flow_0y4o02q</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0y4o02q" sourceRef="Gateway_1tdmr0t" targetRef="Gateway_1xpxnxw" />
    <bpmn:exclusiveGateway id="Gateway_1xpxnxw" name="is service deletion required" default="Flow_0qi1ssn">
      <bpmn:incoming>Flow_0y4o02q</bpmn:incoming>
      <bpmn:outgoing>Flow_038v9ql</bpmn:outgoing>
      <bpmn:outgoing>Flow_0qi1ssn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_038v9ql" sourceRef="Gateway_1xpxnxw" targetRef="BSDeleteService">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${bs_service_creation}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0qi1ssn" sourceRef="Gateway_1xpxnxw" targetRef="Gateway_0igtw6k" />
    <bpmn:exclusiveGateway id="Gateway_1m17hor">
      <bpmn:incoming>Flow_0qjxjpj</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0fbo9h1</bpmn:incoming>
      <bpmn:outgoing>Flow_0vctzzg</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1voo6el" name="is bs account deletion required" default="Flow_0rm6ies">
      <bpmn:incoming>Flow_1dy3o8g</bpmn:incoming>
      <bpmn:outgoing>Flow_1y14ar4</bpmn:outgoing>
      <bpmn:outgoing>Flow_0rm6ies</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1y14ar4" sourceRef="Gateway_1voo6el" targetRef="BSDeleteAccount">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${bs_create_account}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0rm6ies" sourceRef="Gateway_1voo6el" targetRef="ExclusiveGateway_1222wig" />
    <bpmn:exclusiveGateway id="Gateway_13lbcdp" default="Flow_05pmqyj">
      <bpmn:incoming>Flow_0b1auab</bpmn:incoming>
      <bpmn:outgoing>Flow_0j20rdh</bpmn:outgoing>
      <bpmn:outgoing>Flow_05pmqyj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0mtfv43">
      <bpmn:incoming>Flow_05pmqyj</bpmn:incoming>
      <bpmn:incoming>Flow_1lp4xl2</bpmn:incoming>
      <bpmn:outgoing>Flow_1ou6mi8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="NMSUnblockSim" name="NMS Unblock Sim" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0j20rdh</bpmn:incoming>
      <bpmn:outgoing>Flow_1lp4xl2</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0j20rdh" name="Yes" sourceRef="Gateway_13lbcdp" targetRef="NMSUnblockSim">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${nms_block_asset}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_05pmqyj" name="No" sourceRef="Gateway_13lbcdp" targetRef="Gateway_0mtfv43" />
    <bpmn:sequenceFlow id="Flow_1lp4xl2" sourceRef="NMSUnblockSim" targetRef="Gateway_0mtfv43" />
    <bpmn:sequenceFlow id="Flow_1ou6mi8" sourceRef="Gateway_0mtfv43" targetRef="EndEvent_17dzzi8" />
    <bpmn:sequenceFlow id="Flow_0vctzzg" sourceRef="Gateway_1m17hor" targetRef="BSFetchService" />
    <bpmn:serviceTask id="BSFetchService" name="BS FetchService" camunda:asyncBefore="true" camunda:delegateExpression="${bsFetchService}">
      <bpmn:incoming>Flow_0vctzzg</bpmn:incoming>
      <bpmn:outgoing>Flow_1vz88by</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_19r4ptl" default="Flow_0fg37uf">
      <bpmn:incoming>Flow_11bi06h</bpmn:incoming>
      <bpmn:outgoing>Flow_1dy3o8g</bpmn:outgoing>
      <bpmn:outgoing>Flow_0fg37uf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1dy3o8g" sourceRef="Gateway_19r4ptl" targetRef="Gateway_1voo6el">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${accountandProfileDeletionReg}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0h1olwm">
      <bpmn:incoming>Flow_0o68jkb</bpmn:incoming>
      <bpmn:incoming>Flow_0fg37uf</bpmn:incoming>
      <bpmn:outgoing>Flow_0b1auab</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0o68jkb" sourceRef="ExclusiveGateway_1bpowob" targetRef="Gateway_0h1olwm" />
    <bpmn:sequenceFlow id="Flow_0b1auab" sourceRef="Gateway_0h1olwm" targetRef="Gateway_13lbcdp" />
    <bpmn:sequenceFlow id="Flow_0fg37uf" sourceRef="Gateway_19r4ptl" targetRef="Gateway_0h1olwm" />
    <bpmn:endEvent id="Event_1bvn4qr">
      <bpmn:incoming>Flow_0qiyivj</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_04ab22m" default="Flow_11bi06h">
      <bpmn:incoming>Flow_1vz88by</bpmn:incoming>
      <bpmn:outgoing>Flow_11bi06h</bpmn:outgoing>
      <bpmn:outgoing>Flow_0qiyivj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_11bi06h" sourceRef="Gateway_04ab22m" targetRef="Gateway_19r4ptl" />
    <bpmn:sequenceFlow id="Flow_0qiyivj" name="Failure" sourceRef="Gateway_04ab22m" targetRef="Event_1bvn4qr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1vz88by" sourceRef="BSFetchService" targetRef="Gateway_04ab22m" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Onboarding-Rollback">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="192" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_17dzzi8_di" bpmnElement="EndEvent_17dzzi8">
        <dc:Bounds x="3352" y="192" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_14kign4_di" bpmnElement="ExclusiveGateway_14kign4" isMarkerVisible="true">
        <dc:Bounds x="1195" y="185" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1177" y="242" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07ofp2u_di" bpmnElement="OCSSDeleteAccount">
        <dc:Bounds x="1330" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tkyr42_di" bpmnElement="Gateway_1tkyr42" isMarkerVisible="true">
        <dc:Bounds x="295" y="185" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="277" y="242" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0igtw6k_di" bpmnElement="Gateway_0igtw6k" isMarkerVisible="true">
        <dc:Bounds x="1045" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0cy7opf_di" bpmnElement="NMSDeleteService">
        <dc:Bounds x="400" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1uinbhl_di" bpmnElement="BSDeleteService">
        <dc:Bounds x="840" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1tdmr0t_di" bpmnElement="Gateway_1tdmr0t" isMarkerVisible="true">
        <dc:Bounds x="565" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1xpxnxw_di" bpmnElement="Gateway_1xpxnxw" isMarkerVisible="true">
        <dc:Bounds x="685" y="185" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="666" y="242" width="88" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1m17hor_di" bpmnElement="Gateway_1m17hor" isMarkerVisible="true">
        <dc:Bounds x="1505" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1voo6el_di" bpmnElement="Gateway_1voo6el" isMarkerVisible="true">
        <dc:Bounds x="1985" y="185" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1969" y="242" width="82" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1e31ps7" bpmnElement="Gateway_13lbcdp" isMarkerVisible="true">
        <dc:Bounds x="2935" y="185" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2534" y="242" width="52" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_14itqc6" bpmnElement="Gateway_0mtfv43" isMarkerVisible="true">
        <dc:Bounds x="3215" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0d9sd6r" bpmnElement="NMSUnblockSim">
        <dc:Bounds x="3050" y="170" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_022wsum_di" bpmnElement="BSFetchService">
        <dc:Bounds x="1630" y="170" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_04ab22m_di" bpmnElement="Gateway_04ab22m" isMarkerVisible="true">
        <dc:Bounds x="1765" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1bvn4qr_di" bpmnElement="Event_1bvn4qr">
        <dc:Bounds x="1772" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_19r4ptl_di" bpmnElement="Gateway_19r4ptl" isMarkerVisible="true">
        <dc:Bounds x="1855" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1222wig_di" bpmnElement="ExclusiveGateway_1222wig" isMarkerVisible="true">
        <dc:Bounds x="2305" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1xr9z89_di" bpmnElement="ExclusiveGateway_1xr9z89" isMarkerVisible="true">
        <dc:Bounds x="2435" y="185" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2434" y="242" width="52" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0y6y5za_di" bpmnElement="BSDeleteProfile">
        <dc:Bounds x="2560" y="170" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1bpowob_di" bpmnElement="ExclusiveGateway_1bpowob" isMarkerVisible="true">
        <dc:Bounds x="2725" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0h1olwm_di" bpmnElement="Gateway_0h1olwm" isMarkerVisible="true">
        <dc:Bounds x="2825" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1wbapye_di" bpmnElement="BSDeleteAccount">
        <dc:Bounds x="2120" y="170" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_140om64_di" bpmnElement="SequenceFlow_140om64">
        <di:waypoint x="188" y="210" />
        <di:waypoint x="295" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0vgw1x3_di" bpmnElement="SequenceFlow_0vgw1x3">
        <di:waypoint x="2220" y="210" />
        <di:waypoint x="2305" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_19b6n6t_di" bpmnElement="SequenceFlow_19b6n6t">
        <di:waypoint x="2355" y="210" />
        <di:waypoint x="2435" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0t1ijim_di" bpmnElement="SequenceFlow_0t1ijim">
        <di:waypoint x="2660" y="210" />
        <di:waypoint x="2725" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0njj8dc_di" bpmnElement="SequenceFlow_0njj8dc">
        <di:waypoint x="1245" y="210" />
        <di:waypoint x="1330" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1260" y="233" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0fbo9h1_di" bpmnElement="SequenceFlow_0fbo9h1">
        <di:waypoint x="1220" y="185" />
        <di:waypoint x="1220" y="120" />
        <di:waypoint x="1530" y="120" />
        <di:waypoint x="1530" y="185" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1369" y="102" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0w74tvg_di" bpmnElement="SequenceFlow_0w74tvg">
        <di:waypoint x="2485" y="210" />
        <di:waypoint x="2560" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2514" y="192" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_02u3rfj_di" bpmnElement="SequenceFlow_02u3rfj">
        <di:waypoint x="2460" y="185" />
        <di:waypoint x="2460" y="130" />
        <di:waypoint x="2750" y="130" />
        <di:waypoint x="2750" y="185" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2598" y="112" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cghwid_di" bpmnElement="Flow_0cghwid">
        <di:waypoint x="500" y="210" />
        <di:waypoint x="565" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02jg7v7_di" bpmnElement="Flow_02jg7v7">
        <di:waypoint x="940" y="210" />
        <di:waypoint x="1045" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qjxjpj_di" bpmnElement="Flow_0qjxjpj">
        <di:waypoint x="1430" y="210" />
        <di:waypoint x="1505" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1x8sknd_di" bpmnElement="Flow_1x8sknd">
        <di:waypoint x="345" y="210" />
        <di:waypoint x="400" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1i050lf_di" bpmnElement="Flow_1i050lf">
        <di:waypoint x="1095" y="210" />
        <di:waypoint x="1195" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15lngnb_di" bpmnElement="Flow_15lngnb">
        <di:waypoint x="320" y="185" />
        <di:waypoint x="320" y="120" />
        <di:waypoint x="590" y="120" />
        <di:waypoint x="590" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y4o02q_di" bpmnElement="Flow_0y4o02q">
        <di:waypoint x="615" y="210" />
        <di:waypoint x="685" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_038v9ql_di" bpmnElement="Flow_038v9ql">
        <di:waypoint x="735" y="210" />
        <di:waypoint x="840" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qi1ssn_di" bpmnElement="Flow_0qi1ssn">
        <di:waypoint x="710" y="185" />
        <di:waypoint x="710" y="110" />
        <di:waypoint x="1070" y="110" />
        <di:waypoint x="1070" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1y14ar4_di" bpmnElement="Flow_1y14ar4">
        <di:waypoint x="2035" y="210" />
        <di:waypoint x="2120" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rm6ies_di" bpmnElement="Flow_0rm6ies">
        <di:waypoint x="2010" y="185" />
        <di:waypoint x="2010" y="110" />
        <di:waypoint x="2330" y="110" />
        <di:waypoint x="2330" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1ajudrk" bpmnElement="Flow_0j20rdh">
        <di:waypoint x="2985" y="210" />
        <di:waypoint x="3050" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3009" y="192" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1br3qch" bpmnElement="Flow_05pmqyj">
        <di:waypoint x="2960" y="185" />
        <di:waypoint x="2960" y="130" />
        <di:waypoint x="3240" y="130" />
        <di:waypoint x="3240" y="185" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3093" y="112" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1qlk8uz" bpmnElement="Flow_1lp4xl2">
        <di:waypoint x="3150" y="210" />
        <di:waypoint x="3215" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ou6mi8_di" bpmnElement="Flow_1ou6mi8">
        <di:waypoint x="3265" y="210" />
        <di:waypoint x="3352" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vctzzg_di" bpmnElement="Flow_0vctzzg">
        <di:waypoint x="1555" y="210" />
        <di:waypoint x="1630" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vz88by_di" bpmnElement="Flow_1vz88by">
        <di:waypoint x="1730" y="210" />
        <di:waypoint x="1765" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qiyivj_di" bpmnElement="Flow_0qiyivj">
        <di:waypoint x="1790" y="235" />
        <di:waypoint x="1790" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1788" y="261" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11bi06h_di" bpmnElement="Flow_11bi06h">
        <di:waypoint x="1815" y="210" />
        <di:waypoint x="1855" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dy3o8g_di" bpmnElement="Flow_1dy3o8g">
        <di:waypoint x="1905" y="210" />
        <di:waypoint x="1985" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o68jkb_di" bpmnElement="Flow_0o68jkb">
        <di:waypoint x="2775" y="210" />
        <di:waypoint x="2825" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b1auab_di" bpmnElement="Flow_0b1auab">
        <di:waypoint x="2875" y="210" />
        <di:waypoint x="2935" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fg37uf_di" bpmnElement="Flow_0fg37uf">
        <di:waypoint x="1880" y="185" />
        <di:waypoint x="1880" y="60" />
        <di:waypoint x="2850" y="60" />
        <di:waypoint x="2850" y="185" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
