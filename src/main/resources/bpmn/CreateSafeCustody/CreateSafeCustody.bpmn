<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_0jfkxu9" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.8.0">
  <bpmn:process id="CreateSafeCustody" name="CreateSafeCustody" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1shfz9j</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1on0j8c</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSCreateSafeCustody" name="Billing Create SafeCustody" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1q1on30</bpmn:incoming>
      <bpmn:outgoing>Flow_075lt0n</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1wgsnka" default="Flow_1i2a82d">
      <bpmn:incoming>Flow_075lt0n</bpmn:incoming>
      <bpmn:outgoing>Flow_1i2a82d</bpmn:outgoing>
      <bpmn:outgoing>Flow_0o11x0i</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_075lt0n" sourceRef="BSCreateSafeCustody" targetRef="Gateway_1wgsnka" />
    <bpmn:sequenceFlow id="Flow_1i2a82d" sourceRef="Gateway_1wgsnka" targetRef="Gateway_1xmzvn3" />
    <bpmn:serviceTask id="BSAddSubscription" name="BS AddSubscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_07od4b1</bpmn:incoming>
      <bpmn:outgoing>Flow_0no1iew</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="Event_1w1hc2c">
      <bpmn:incoming>Flow_0o11x0i</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0o11x0i" name="Failure" sourceRef="Gateway_1wgsnka" targetRef="Event_1w1hc2c">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1xmzvn3" name="Check if subscriptions is there OR not" default="Flow_1jxxc4p">
      <bpmn:incoming>Flow_1i2a82d</bpmn:incoming>
      <bpmn:outgoing>Flow_07od4b1</bpmn:outgoing>
      <bpmn:outgoing>Flow_1jxxc4p</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_07od4b1" name="yes" sourceRef="Gateway_1xmzvn3" targetRef="BSAddSubscription">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${workflowData.jsonPath("$.order.serviceManagement").element().hasProp('subscriptions')}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0h9bkzo">
      <bpmn:incoming>Flow_1jxxc4p</bpmn:incoming>
      <bpmn:incoming>Flow_19nlohk</bpmn:incoming>
      <bpmn:outgoing>Flow_1on0j8c</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1on0j8c" sourceRef="Gateway_0h9bkzo" targetRef="orderExecEnd" />
    <bpmn:sequenceFlow id="Flow_1jxxc4p" name="no" sourceRef="Gateway_1xmzvn3" targetRef="Gateway_0h9bkzo" />
    <bpmn:exclusiveGateway id="Gateway_12ibj8c" default="Flow_19nlohk">
      <bpmn:incoming>Flow_0no1iew</bpmn:incoming>
      <bpmn:outgoing>Flow_19nlohk</bpmn:outgoing>
      <bpmn:outgoing>Flow_0069367</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0no1iew" sourceRef="BSAddSubscription" targetRef="Gateway_12ibj8c" />
    <bpmn:sequenceFlow id="Flow_19nlohk" sourceRef="Gateway_12ibj8c" targetRef="Gateway_0h9bkzo" />
    <bpmn:endEvent id="Event_0ajwgpj">
      <bpmn:incoming>Flow_0069367</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0069367" name="Failure" sourceRef="Gateway_12ibj8c" targetRef="Event_0ajwgpj">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="OrderEnrichments" name="Enrich plan details" camunda:asyncBefore="true" camunda:delegateExpression="${orderEnrichmentDelegate}">
      <bpmn:incoming>Flow_119y8b8</bpmn:incoming>
      <bpmn:outgoing>Flow_08kc76b</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1shfz9j" sourceRef="orderExecStart" targetRef="Gateway_1ndygzo" />
    <bpmn:sequenceFlow id="Flow_08kc76b" sourceRef="OrderEnrichments" targetRef="Gateway_0ebw4le" />
    <bpmn:exclusiveGateway id="Gateway_1ndygzo" name="Check if subscriptions is there OR not" default="Flow_1kflwzc">
      <bpmn:incoming>Flow_1shfz9j</bpmn:incoming>
      <bpmn:outgoing>Flow_119y8b8</bpmn:outgoing>
      <bpmn:outgoing>Flow_1kflwzc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_119y8b8" sourceRef="Gateway_1ndygzo" targetRef="OrderEnrichments">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsSubscriptionPresent}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1omwy9g">
      <bpmn:incoming>Flow_0m3ng28</bpmn:incoming>
      <bpmn:incoming>Flow_1kflwzc</bpmn:incoming>
      <bpmn:outgoing>Flow_1q1on30</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0ebw4le">
      <bpmn:incoming>Flow_08kc76b</bpmn:incoming>
      <bpmn:outgoing>Flow_0m3ng28</bpmn:outgoing>
      <bpmn:outgoing>Flow_00oyaa2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0m3ng28" sourceRef="Gateway_0ebw4le" targetRef="Gateway_1omwy9g">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1kflwzc" sourceRef="Gateway_1ndygzo" targetRef="Gateway_1omwy9g" />
    <bpmn:endEvent id="Event_0y2oh3t">
      <bpmn:incoming>Flow_00oyaa2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_00oyaa2" sourceRef="Gateway_0ebw4le" targetRef="Event_0y2oh3t">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1q1on30" sourceRef="Gateway_1omwy9g" targetRef="BSCreateSafeCustody" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CreateSafeCustody">
      <bpmndi:BPMNShape id="EndEvent_05s0ocb_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="2112" y="205" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1jea4er_di" bpmnElement="BSCreateSafeCustody">
        <dc:Bounds x="997" y="183" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1wgsnka_di" bpmnElement="Gateway_1wgsnka" isMarkerVisible="true">
        <dc:Bounds x="1195" y="198" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1jdpjze_di" bpmnElement="BSAddSubscription">
        <dc:Bounds x="1540" y="183" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1w1hc2c_di" bpmnElement="Event_1w1hc2c">
        <dc:Bounds x="1202" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1xmzvn3_di" bpmnElement="Gateway_1xmzvn3" isMarkerVisible="true">
        <dc:Bounds x="1345" y="198" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1333" y="255" width="76" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0h9bkzo_di" bpmnElement="Gateway_0h9bkzo" isMarkerVisible="true">
        <dc:Bounds x="1925" y="198" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_12ibj8c_di" bpmnElement="Gateway_12ibj8c" isMarkerVisible="true">
        <dc:Bounds x="1755" y="198" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0ajwgpj_di" bpmnElement="Event_0ajwgpj">
        <dc:Bounds x="1762" y="312" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_00b5rc6_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="205" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ldtc7n" bpmnElement="Gateway_1ndygzo" isMarkerVisible="true">
        <dc:Bounds x="325" y="198" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="312" y="255" width="76" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13p21z4" bpmnElement="OrderEnrichments">
        <dc:Bounds x="460" y="183" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0923c6h" bpmnElement="Gateway_1omwy9g" isMarkerVisible="true">
        <dc:Bounds x="725" y="198" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0gi75lq" bpmnElement="Gateway_0ebw4le" isMarkerVisible="true">
        <dc:Bounds x="615" y="198" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ruha5t" bpmnElement="Event_0y2oh3t">
        <dc:Bounds x="622" y="332" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1on0j8c_di" bpmnElement="Flow_1on0j8c">
        <di:waypoint x="1975" y="223" />
        <di:waypoint x="2112" y="223" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_075lt0n_di" bpmnElement="Flow_075lt0n">
        <di:waypoint x="1097" y="223" />
        <di:waypoint x="1195" y="223" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1i2a82d_di" bpmnElement="Flow_1i2a82d">
        <di:waypoint x="1245" y="223" />
        <di:waypoint x="1345" y="223" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o11x0i_di" bpmnElement="Flow_0o11x0i">
        <di:waypoint x="1220" y="248" />
        <di:waypoint x="1220" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1218" y="277" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07od4b1_di" bpmnElement="Flow_07od4b1">
        <di:waypoint x="1395" y="223" />
        <di:waypoint x="1540" y="223" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1461" y="205" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0no1iew_di" bpmnElement="Flow_0no1iew">
        <di:waypoint x="1640" y="223" />
        <di:waypoint x="1755" y="223" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jxxc4p_di" bpmnElement="Flow_1jxxc4p">
        <di:waypoint x="1370" y="198" />
        <di:waypoint x="1370" y="130" />
        <di:waypoint x="1950" y="130" />
        <di:waypoint x="1950" y="198" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1654" y="112" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19nlohk_di" bpmnElement="Flow_19nlohk">
        <di:waypoint x="1805" y="223" />
        <di:waypoint x="1925" y="223" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0069367_di" bpmnElement="Flow_0069367">
        <di:waypoint x="1780" y="248" />
        <di:waypoint x="1780" y="312" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1778" y="277" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1q1on30_di" bpmnElement="Flow_1q1on30">
        <di:waypoint x="775" y="223" />
        <di:waypoint x="997" y="223" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1shfz9j_di" bpmnElement="Flow_1shfz9j">
        <di:waypoint x="188" y="223" />
        <di:waypoint x="325" y="223" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_119y8b8_di" bpmnElement="Flow_119y8b8">
        <di:waypoint x="375" y="223" />
        <di:waypoint x="460" y="223" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kflwzc_di" bpmnElement="Flow_1kflwzc">
        <di:waypoint x="350" y="198" />
        <di:waypoint x="350" y="130" />
        <di:waypoint x="750" y="130" />
        <di:waypoint x="750" y="198" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08kc76b_di" bpmnElement="Flow_08kc76b">
        <di:waypoint x="560" y="223" />
        <di:waypoint x="615" y="223" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0m3ng28_di" bpmnElement="Flow_0m3ng28">
        <di:waypoint x="665" y="223" />
        <di:waypoint x="725" y="223" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00oyaa2_di" bpmnElement="Flow_00oyaa2">
        <di:waypoint x="640" y="248" />
        <di:waypoint x="640" y="332" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
