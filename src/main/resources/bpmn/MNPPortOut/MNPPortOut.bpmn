<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0yalseh" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.23.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.19.0">
  <bpmn:process id="MNPPortOut" name="MNP Port Out" isExecutable="true" camunda:historyTimeToLive="180">
    <bpmn:exclusiveGateway id="Gateway_1lyrh8l" name="check port out type">
      <bpmn:incoming>Flow_1b71le0</bpmn:incoming>
      <bpmn:outgoing>Flow_0pei4bw</bpmn:outgoing>
      <bpmn:outgoing>Flow_12g4jls</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0pei4bw" name="Portout to external telco" sourceRef="Gateway_1lyrh8l" targetRef="NotifyPortOutValidation">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portOutType=='PortOutToExternalTelco'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1u8avkv" default="Flow_0z13c45">
      <bpmn:incoming>Flow_01q5lny</bpmn:incoming>
      <bpmn:outgoing>Flow_0z13c45</bpmn:outgoing>
      <bpmn:outgoing>Flow_01mwveg</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_01q5lny" sourceRef="NotifyPortOutValidation" targetRef="Gateway_1u8avkv" />
    <bpmn:sequenceFlow id="Flow_12g4jls" name="Port out to singtel" sourceRef="Gateway_1lyrh8l" targetRef="DisconnectServiceIntCallback">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portOutType=='PortOutToSingtel'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="DisconnectServiceCallback" name="Disconnect Service Callback" camunda:asyncBefore="true" messageRef="Message_049qpi7">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="NotifyPortOutValidation" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0z13c45</bpmn:incoming>
      <bpmn:outgoing>Flow_04nrv61</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:sequenceFlow id="Flow_0z13c45" name="Success" sourceRef="Gateway_1u8avkv" targetRef="DisconnectServiceCallback" />
    <bpmn:endEvent id="Event_1wl7cul">
      <bpmn:incoming>Flow_01mwveg</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_01mwveg" name="Failure" sourceRef="Gateway_1u8avkv" targetRef="Event_1wl7cul">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:receiveTask id="DisconnectServiceIntCallback" name="Disconnect Service Internal Callback" camunda:asyncBefore="true" messageRef="Message_049qpi7">
      <bpmn:extensionElements>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_12g4jls</bpmn:incoming>
      <bpmn:outgoing>Flow_1llxgnj</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_0g0j615">
      <bpmn:incoming>Flow_04nrv61</bpmn:incoming>
      <bpmn:incoming>Flow_1llxgnj</bpmn:incoming>
      <bpmn:outgoing>Flow_14pub2k</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_04nrv61" sourceRef="DisconnectServiceCallback" targetRef="Gateway_0g0j615" />
    <bpmn:sequenceFlow id="Flow_1llxgnj" sourceRef="DisconnectServiceIntCallback" targetRef="Gateway_0g0j615" />
    <bpmn:serviceTask id="SOM_DeleteService" name="Terminate Service from SOM" camunda:asyncBefore="true" camunda:delegateExpression="${somTerminateService}">
      <bpmn:incoming>Flow_06zk957</bpmn:incoming>
      <bpmn:outgoing>Flow_186zwzb</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1hkb9eb" default="Flow_0fbc1db">
      <bpmn:incoming>Flow_186zwzb</bpmn:incoming>
      <bpmn:outgoing>Flow_0fbc1db</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ymtqlv</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_186zwzb" sourceRef="SOM_DeleteService" targetRef="Gateway_1hkb9eb" />
    <bpmn:sequenceFlow id="Flow_0fbc1db" name="Success" sourceRef="Gateway_1hkb9eb" targetRef="SOMPortOutCallback" />
    <bpmn:receiveTask id="SOMPortOutCallback" name="Waiting for SOM callback" camunda:asyncBefore="true" messageRef="Message_2vl9j7t">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="linkedServiceTask">SOM_DeleteService</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="linkedServiceTask" value="SOM_DeleteService" />
        </camunda:properties>
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="start" />
        <camunda:executionListener class="in.co.sixdee.bss.com.orderorchestrator.config.camunda.listener.SignalExecutionListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0fbc1db</bpmn:incoming>
      <bpmn:outgoing>Flow_1mhrpex</bpmn:outgoing>
    </bpmn:receiveTask>
    <bpmn:exclusiveGateway id="Gateway_1kkl6y6" default="Flow_1g2bnq8">
      <bpmn:incoming>Flow_1mhrpex</bpmn:incoming>
      <bpmn:outgoing>Flow_1g2bnq8</bpmn:outgoing>
      <bpmn:outgoing>Flow_088mus4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1mhrpex" sourceRef="SOMPortOutCallback" targetRef="Gateway_1kkl6y6" />
    <bpmn:exclusiveGateway id="Gateway_1uelx2m" name="Check port out type">
      <bpmn:incoming>Flow_1g2bnq8</bpmn:incoming>
      <bpmn:outgoing>Flow_1yga8yd</bpmn:outgoing>
      <bpmn:outgoing>Flow_13sni7c</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1g2bnq8" name="Success" sourceRef="Gateway_1kkl6y6" targetRef="Gateway_1uelx2m" />
    <bpmn:sequenceFlow id="Flow_1yga8yd" name="Port out to external telco" sourceRef="Gateway_1uelx2m" targetRef="NotifyServiceDisconnectSCS">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portOutType=='PortOutToExternalTelco'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1akchnw" default="Flow_187upge">
      <bpmn:incoming>Flow_0o3050t</bpmn:incoming>
      <bpmn:outgoing>Flow_187upge</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ufdt8a</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0o3050t" sourceRef="NotifyServiceDisconnectSCS" targetRef="Gateway_1akchnw" />
    <bpmn:exclusiveGateway id="Gateway_058twu3">
      <bpmn:incoming>Flow_187upge</bpmn:incoming>
      <bpmn:incoming>Flow_13sni7c</bpmn:incoming>
      <bpmn:outgoing>Flow_1xwipii</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_187upge" name="Success" sourceRef="Gateway_1akchnw" targetRef="Gateway_058twu3" />
    <bpmn:sequenceFlow id="Flow_13sni7c" name="Port out to singtel" sourceRef="Gateway_1uelx2m" targetRef="Gateway_058twu3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portOutType=='PortOutToSingtel'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="NotifyServiceDisconnectSCS" name="Notify service dsconnect success" camunda:asyncBefore="true" camunda:delegateExpression="${notifyPortOutValidation}">
      <bpmn:incoming>Flow_1yga8yd</bpmn:incoming>
      <bpmn:outgoing>Flow_0o3050t</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="NotifyServiceDisconnectFail" name="Notify service dsconnect failure" camunda:asyncBefore="true" camunda:delegateExpression="${notifyServiceDisconnect}">
      <bpmn:incoming>Flow_0qf0yww</bpmn:incoming>
      <bpmn:outgoing>Flow_12l6uy4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0pr6pz5">
      <bpmn:incoming>Flow_1ymtqlv</bpmn:incoming>
      <bpmn:incoming>Flow_088mus4</bpmn:incoming>
      <bpmn:outgoing>Flow_0t96mzz</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ymtqlv" name="Failure" sourceRef="Gateway_1hkb9eb" targetRef="Gateway_0pr6pz5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_088mus4" name="Failure" sourceRef="Gateway_1kkl6y6" targetRef="Gateway_0pr6pz5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_12cuhul" name="Check port out type">
      <bpmn:incoming>Flow_0t96mzz</bpmn:incoming>
      <bpmn:outgoing>Flow_0qf0yww</bpmn:outgoing>
      <bpmn:outgoing>Flow_1eyb0o6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0t96mzz" sourceRef="Gateway_0pr6pz5" targetRef="Gateway_12cuhul" />
    <bpmn:sequenceFlow id="Flow_0qf0yww" name="Port out to external telco" sourceRef="Gateway_12cuhul" targetRef="NotifyServiceDisconnectFail">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portOutType=='PortOutToExternalTelco'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1ut34z6">
      <bpmn:incoming>Flow_1eyb0o6</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1eyb0o6" name="Port out to singtel" sourceRef="Gateway_12cuhul" targetRef="Event_1ut34z6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portOutType=='PortOutToSingtel'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1xvy0be">
      <bpmn:incoming>Flow_12l6uy4</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_12l6uy4" sourceRef="NotifyServiceDisconnectFail" targetRef="Event_1xvy0be" />
    <bpmn:endEvent id="Event_1jpk6kr">
      <bpmn:incoming>Flow_1ufdt8a</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1ufdt8a" name="Failure" sourceRef="Gateway_1akchnw" targetRef="Event_1jpk6kr">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1xwipii" sourceRef="Gateway_058twu3" targetRef="ESB_TerminateService" />
    <bpmn:exclusiveGateway id="Gateway_127h5s0" default="Flow_09wzivt">
      <bpmn:incoming>Flow_0lk22if</bpmn:incoming>
      <bpmn:outgoing>Flow_09wzivt</bpmn:outgoing>
      <bpmn:outgoing>Flow_1vke9al</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0lk22if" sourceRef="ESB_TerminateService" targetRef="Gateway_127h5s0" />
    <bpmn:sequenceFlow id="Flow_09wzivt" name="Success" sourceRef="Gateway_127h5s0" targetRef="BS_TerminateService" />
    <bpmn:endEvent id="Event_17vz79h">
      <bpmn:incoming>Flow_1vke9al</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1vke9al" name="Failure" sourceRef="Gateway_127h5s0" targetRef="Event_17vz79h">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="ESB_TerminateService" name="Terminate service from OCS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1xwipii</bpmn:incoming>
      <bpmn:outgoing>Flow_0lk22if</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BS_TerminateService" name="Terminate Service from Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_09wzivt</bpmn:incoming>
      <bpmn:outgoing>Flow_1hvt1mg</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1cynsfo" default="Flow_1kqj1no">
      <bpmn:incoming>Flow_1hvt1mg</bpmn:incoming>
      <bpmn:outgoing>Flow_1kqj1no</bpmn:outgoing>
      <bpmn:outgoing>Flow_1a1zd80</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1hvt1mg" sourceRef="BS_TerminateService" targetRef="Gateway_1cynsfo" />
    <bpmn:sequenceFlow id="Flow_1kqj1no" name="Success" sourceRef="Gateway_1cynsfo" targetRef="NMS_PortOut" />
    <bpmn:endEvent id="orderExecEnd" camunda:asyncBefore="true">
      <bpmn:incoming>Flow_0733e6l</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0733e6l" sourceRef="NMS_PortOut" targetRef="orderExecEnd" />
    <bpmn:endEvent id="Event_00k21zi">
      <bpmn:incoming>Flow_1a1zd80</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1a1zd80" name="Failure" sourceRef="Gateway_1cynsfo" targetRef="Event_00k21zi">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status==1}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="NotifyPortOutValidation" name="Notify Portout Validaton Success" camunda:asyncBefore="true" camunda:delegateExpression="${notifyPortOutValidation}">
      <bpmn:incoming>Flow_0pei4bw</bpmn:incoming>
      <bpmn:outgoing>Flow_01q5lny</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="NMS_PortOut" name="Port out in NMS" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1kqj1no</bpmn:incoming>
      <bpmn:outgoing>Flow_0733e6l</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1qvsl1k" default="Flow_06zk957">
      <bpmn:incoming>SequenceFlow_0ljkt7u</bpmn:incoming>
      <bpmn:outgoing>Flow_02e3uev</bpmn:outgoing>
      <bpmn:outgoing>Flow_06zk957</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_01pb4gg">
      <bpmn:incoming>Flow_02e3uev</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch Service Registry" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1as599e</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0ljkt7u</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0ljkt7u" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_1qvsl1k" />
    <bpmn:sequenceFlow id="Flow_02e3uev" name="Failure" sourceRef="Gateway_1qvsl1k" targetRef="Event_01pb4gg">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_06zk957" name="Success" sourceRef="Gateway_1qvsl1k" targetRef="SOM_DeleteService" />
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1sa546y</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_14pub2k" sourceRef="Gateway_0g0j615" targetRef="ChangeGroupOwnership" />
    <bpmn:callActivity id="ChangeGroupOwnership" name="ChangeGroupOwnership" camunda:asyncBefore="true" calledElement="ChangeGroupOwnership" camunda:calledElementBinding="deployment">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in variables="all" />
        <camunda:out variables="all" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_14pub2k</bpmn:incoming>
      <bpmn:outgoing>Flow_0p3ratm</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0p3ratm" sourceRef="ChangeGroupOwnership" targetRef="Gateway_1jtoymo" />
    <bpmn:exclusiveGateway id="Gateway_1jtoymo">
      <bpmn:incoming>Flow_0p3ratm</bpmn:incoming>
      <bpmn:outgoing>Flow_1as599e</bpmn:outgoing>
      <bpmn:outgoing>Flow_06rqeom</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1as599e" sourceRef="Gateway_1jtoymo" targetRef="SOMFetchServiceRegistry">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='0'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1gij4w8">
      <bpmn:incoming>Flow_06rqeom</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_06rqeom" name="Failure" sourceRef="Gateway_1jtoymo" targetRef="Event_1gij4w8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="PortOutDataValidator" name="Port Out Data Validator " camunda:asyncBefore="true" camunda:delegateExpression="${portOutDataValidator}">
      <bpmn:incoming>Flow_1sa546y</bpmn:incoming>
      <bpmn:outgoing>Flow_0b3hlb7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1bc48rc" default="Flow_1b71le0">
      <bpmn:incoming>Flow_0b3hlb7</bpmn:incoming>
      <bpmn:outgoing>Flow_1b71le0</bpmn:outgoing>
      <bpmn:outgoing>Flow_0e5hpvi</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0b3hlb7" sourceRef="PortOutDataValidator" targetRef="Gateway_1bc48rc" />
    <bpmn:serviceTask id="NotifyPortOutValidationFailure" name="Notify Portout Validaton Failure" camunda:asyncBefore="true" camunda:delegateExpression="${notifyPortOutValidation}">
      <bpmn:incoming>Flow_03qz550</bpmn:incoming>
      <bpmn:outgoing>Flow_1qlew6a</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_02l0353">
      <bpmn:incoming>Flow_0e5hpvi</bpmn:incoming>
      <bpmn:outgoing>Flow_0i8te0g</bpmn:outgoing>
      <bpmn:outgoing>Flow_03qz550</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:endEvent id="Event_08t3dxi">
      <bpmn:incoming>Flow_0i8te0g</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0i8te0g" name="Port out to singtel" sourceRef="Gateway_02l0353" targetRef="Event_08t3dxi">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portOutType=='PortOutToSingtel'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_03qz550" name="Portout to external telco" sourceRef="Gateway_02l0353" targetRef="NotifyPortOutValidationFailure">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${portOutType=='PortOutToExternalTelco'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1sa546y" sourceRef="orderExecStart" targetRef="PortOutDataValidator" />
    <bpmn:sequenceFlow id="Flow_1b71le0" sourceRef="Gateway_1bc48rc" targetRef="Gateway_1lyrh8l" />
    <bpmn:sequenceFlow id="Flow_0e5hpvi" sourceRef="Gateway_1bc48rc" targetRef="Gateway_02l0353">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_18cgnhv">
      <bpmn:incoming>Flow_1qlew6a</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1qlew6a" sourceRef="NotifyPortOutValidationFailure" targetRef="Event_18cgnhv" />
  </bpmn:process>
  <bpmn:message id="Message_049qpi7" name="DisconnectServiceCallback" />
  <bpmn:message id="Message_2vl9j7t" name="SOMPortOutCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="MNPPortOut">
      <bpmndi:BPMNShape id="Gateway_1lyrh8l_di" bpmnElement="Gateway_1lyrh8l" isMarkerVisible="true">
        <dc:Bounds x="785" y="162" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="776" y="132" width="70" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1u8avkv_di" bpmnElement="Gateway_1u8avkv" isMarkerVisible="true">
        <dc:Bounds x="1075" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ssyau4_di" bpmnElement="DisconnectServiceCallback">
        <dc:Bounds x="1210" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1wl7cul_di" bpmnElement="Event_1wl7cul">
        <dc:Bounds x="1082" y="272" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0zgsr0g_di" bpmnElement="DisconnectServiceIntCallback">
        <dc:Bounds x="1180" y="320" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0g0j615_di" bpmnElement="Gateway_0g0j615" isMarkerVisible="true">
        <dc:Bounds x="1365" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0kr5obf_di" bpmnElement="SOM_DeleteService">
        <dc:Bounds x="2290" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1hkb9eb_di" bpmnElement="Gateway_1hkb9eb" isMarkerVisible="true">
        <dc:Bounds x="2455" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07xfbfu_di" bpmnElement="SOMPortOutCallback">
        <dc:Bounds x="2600" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1kkl6y6_di" bpmnElement="Gateway_1kkl6y6" isMarkerVisible="true">
        <dc:Bounds x="2795" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1uelx2m_di" bpmnElement="Gateway_1uelx2m" isMarkerVisible="true">
        <dc:Bounds x="2945" y="162" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2935" y="219" width="72" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1akchnw_di" bpmnElement="Gateway_1akchnw" isMarkerVisible="true">
        <dc:Bounds x="3305" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_058twu3_di" bpmnElement="Gateway_058twu3" isMarkerVisible="true">
        <dc:Bounds x="3465" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01ue2hh_di" bpmnElement="NotifyServiceDisconnectSCS">
        <dc:Bounds x="3100" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0j1ffna" bpmnElement="NotifyServiceDisconnectFail">
        <dc:Bounds x="2590" y="530" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0pr6pz5_di" bpmnElement="Gateway_0pr6pz5" isMarkerVisible="true">
        <dc:Bounds x="2615" y="285" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_12cuhul_di" bpmnElement="Gateway_12cuhul" isMarkerVisible="true">
        <dc:Bounds x="2615" y="395" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2525" y="410" width="72" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ut34z6_di" bpmnElement="Event_1ut34z6">
        <dc:Bounds x="2782" y="402" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1xvy0be_di" bpmnElement="Event_1xvy0be">
        <dc:Bounds x="2782" y="552" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1jpk6kr_di" bpmnElement="Event_1jpk6kr">
        <dc:Bounds x="3312" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_127h5s0_di" bpmnElement="Gateway_127h5s0" isMarkerVisible="true">
        <dc:Bounds x="3865" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_17vz79h_di" bpmnElement="Event_17vz79h">
        <dc:Bounds x="3872" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0qbvi21_di" bpmnElement="ESB_TerminateService">
        <dc:Bounds x="3680" y="147" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1l1ek20_di" bpmnElement="BS_TerminateService">
        <dc:Bounds x="4020" y="147" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1cynsfo_di" bpmnElement="Gateway_1cynsfo" isMarkerVisible="true">
        <dc:Bounds x="4225" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_08o91x4_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="4572" y="169" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_00k21zi_di" bpmnElement="Event_00k21zi">
        <dc:Bounds x="4232" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1cf4prz_di" bpmnElement="NotifyPortOutValidation">
        <dc:Bounds x="930" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1bqz0ks_di" bpmnElement="NMS_PortOut">
        <dc:Bounds x="4370" y="147" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1qvsl1k_di" bpmnElement="Gateway_1qvsl1k" isMarkerVisible="true">
        <dc:Bounds x="2155" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_01pb4gg_di" bpmnElement="Event_01pb4gg">
        <dc:Bounds x="2162" y="289" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0hs1oog_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="2010" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0pm68tr_di" bpmnElement="ChangeGroupOwnership">
        <dc:Bounds x="1550" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1lm4d3r" bpmnElement="Gateway_1jtoymo" isMarkerVisible="true">
        <dc:Bounds x="1845" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0nivayz" bpmnElement="Event_1gij4w8">
        <dc:Bounds x="1852" y="292" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1bc48rc_di" bpmnElement="Gateway_1bc48rc" isMarkerVisible="true">
        <dc:Bounds x="585" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1d7r8uk_di" bpmnElement="orderExecStart">
        <dc:Bounds x="192" y="169" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0wxpxji" bpmnElement="PortOutDataValidator">
        <dc:Bounds x="350" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_02l0353_di" bpmnElement="Gateway_02l0353" isMarkerVisible="true">
        <dc:Bounds x="585" y="375" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="635" y="417.5" width="70" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_08t3dxi_di" bpmnElement="Event_08t3dxi">
        <dc:Bounds x="732" y="382" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0gc8g7l" bpmnElement="NotifyPortOutValidationFailure">
        <dc:Bounds x="560" y="500" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_18cgnhv_di" bpmnElement="Event_18cgnhv">
        <dc:Bounds x="732" y="522" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1b71le0_di" bpmnElement="Flow_1b71le0">
        <di:waypoint x="635" y="187" />
        <di:waypoint x="785" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pei4bw_di" bpmnElement="Flow_0pei4bw">
        <di:waypoint x="835" y="187" />
        <di:waypoint x="930" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="839" y="146" width="90" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12g4jls_di" bpmnElement="Flow_12g4jls">
        <di:waypoint x="810" y="212" />
        <di:waypoint x="810" y="360" />
        <di:waypoint x="1180" y="360" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="823" y="333" width="86" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01q5lny_di" bpmnElement="Flow_01q5lny">
        <di:waypoint x="1030" y="187" />
        <di:waypoint x="1075" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0z13c45_di" bpmnElement="Flow_0z13c45">
        <di:waypoint x="1125" y="187" />
        <di:waypoint x="1210" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1125" y="169" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01mwveg_di" bpmnElement="Flow_01mwveg">
        <di:waypoint x="1100" y="212" />
        <di:waypoint x="1100" y="272" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1122" y="215" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04nrv61_di" bpmnElement="Flow_04nrv61">
        <di:waypoint x="1310" y="187" />
        <di:waypoint x="1365" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1llxgnj_di" bpmnElement="Flow_1llxgnj">
        <di:waypoint x="1280" y="360" />
        <di:waypoint x="1390" y="360" />
        <di:waypoint x="1390" y="212" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14pub2k_di" bpmnElement="Flow_14pub2k">
        <di:waypoint x="1415" y="187" />
        <di:waypoint x="1550" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06zk957_di" bpmnElement="Flow_06zk957">
        <di:waypoint x="2205" y="187" />
        <di:waypoint x="2290" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2228" y="169" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_186zwzb_di" bpmnElement="Flow_186zwzb">
        <di:waypoint x="2390" y="187" />
        <di:waypoint x="2455" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fbc1db_di" bpmnElement="Flow_0fbc1db">
        <di:waypoint x="2505" y="187" />
        <di:waypoint x="2600" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2532" y="169" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ymtqlv_di" bpmnElement="Flow_1ymtqlv">
        <di:waypoint x="2480" y="212" />
        <di:waypoint x="2480" y="310" />
        <di:waypoint x="2615" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2493" y="258" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mhrpex_di" bpmnElement="Flow_1mhrpex">
        <di:waypoint x="2700" y="187" />
        <di:waypoint x="2795" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g2bnq8_di" bpmnElement="Flow_1g2bnq8">
        <di:waypoint x="2845" y="187" />
        <di:waypoint x="2945" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2874" y="169" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_088mus4_di" bpmnElement="Flow_088mus4">
        <di:waypoint x="2820" y="212" />
        <di:waypoint x="2820" y="310" />
        <di:waypoint x="2665" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2773" y="243" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yga8yd_di" bpmnElement="Flow_1yga8yd">
        <di:waypoint x="2995" y="187" />
        <di:waypoint x="3100" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3027" y="196" width="66" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13sni7c_di" bpmnElement="Flow_13sni7c">
        <di:waypoint x="2970" y="162" />
        <di:waypoint x="2970" y="100" />
        <di:waypoint x="3490" y="100" />
        <di:waypoint x="3490" y="162" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2977" y="82" width="86" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o3050t_di" bpmnElement="Flow_0o3050t">
        <di:waypoint x="3200" y="187" />
        <di:waypoint x="3305" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_187upge_di" bpmnElement="Flow_187upge">
        <di:waypoint x="3355" y="187" />
        <di:waypoint x="3465" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3389" y="169" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ufdt8a_di" bpmnElement="Flow_1ufdt8a">
        <di:waypoint x="3330" y="212" />
        <di:waypoint x="3330" y="282" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3343" y="243" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xwipii_di" bpmnElement="Flow_1xwipii">
        <di:waypoint x="3515" y="187" />
        <di:waypoint x="3680" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qf0yww_di" bpmnElement="Flow_0qf0yww">
        <di:waypoint x="2640" y="445" />
        <di:waypoint x="2640" y="530" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2657" y="485" width="66" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12l6uy4_di" bpmnElement="Flow_12l6uy4">
        <di:waypoint x="2690" y="570" />
        <di:waypoint x="2782" y="570" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0t96mzz_di" bpmnElement="Flow_0t96mzz">
        <di:waypoint x="2640" y="335" />
        <di:waypoint x="2640" y="395" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eyb0o6_di" bpmnElement="Flow_1eyb0o6">
        <di:waypoint x="2665" y="420" />
        <di:waypoint x="2782" y="420" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2681" y="402" width="86" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lk22if_di" bpmnElement="Flow_0lk22if">
        <di:waypoint x="3780" y="187" />
        <di:waypoint x="3865" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09wzivt_di" bpmnElement="Flow_09wzivt">
        <di:waypoint x="3915" y="187" />
        <di:waypoint x="4020" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3946" y="169" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vke9al_di" bpmnElement="Flow_1vke9al">
        <di:waypoint x="3890" y="212" />
        <di:waypoint x="3890" y="282" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3913" y="233" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hvt1mg_di" bpmnElement="Flow_1hvt1mg">
        <di:waypoint x="4120" y="187" />
        <di:waypoint x="4225" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kqj1no_di" bpmnElement="Flow_1kqj1no">
        <di:waypoint x="4275" y="187" />
        <di:waypoint x="4370" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4301" y="169" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a1zd80_di" bpmnElement="Flow_1a1zd80">
        <di:waypoint x="4250" y="212" />
        <di:waypoint x="4250" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="4253" y="243" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0733e6l_di" bpmnElement="Flow_0733e6l">
        <di:waypoint x="4470" y="187" />
        <di:waypoint x="4572" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0ljkt7u_di" bpmnElement="SequenceFlow_0ljkt7u">
        <di:waypoint x="2110" y="187" />
        <di:waypoint x="2155" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02e3uev_di" bpmnElement="Flow_02e3uev">
        <di:waypoint x="2180" y="212" />
        <di:waypoint x="2180" y="289" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2183" y="241" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1as599e_di" bpmnElement="Flow_1as599e">
        <di:waypoint x="1895" y="187" />
        <di:waypoint x="2010" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p3ratm_di" bpmnElement="Flow_0p3ratm">
        <di:waypoint x="1650" y="187" />
        <di:waypoint x="1845" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06rqeom_di" bpmnElement="Flow_06rqeom">
        <di:waypoint x="1870" y="212" />
        <di:waypoint x="1870" y="292" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1869" y="249" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03qz550_di" bpmnElement="Flow_03qz550">
        <di:waypoint x="610" y="425" />
        <di:waypoint x="610" y="500" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="615" y="461" width="90" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b3hlb7_di" bpmnElement="Flow_0b3hlb7">
        <di:waypoint x="450" y="187" />
        <di:waypoint x="585" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e5hpvi_di" bpmnElement="Flow_0e5hpvi">
        <di:waypoint x="610" y="212" />
        <di:waypoint x="610" y="375" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sa546y_di" bpmnElement="Flow_1sa546y">
        <di:waypoint x="228" y="187" />
        <di:waypoint x="350" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0i8te0g_di" bpmnElement="Flow_0i8te0g">
        <di:waypoint x="635" y="400" />
        <di:waypoint x="732" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="635" y="382" width="86" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1qlew6a_di" bpmnElement="Flow_1qlew6a">
        <di:waypoint x="660" y="540" />
        <di:waypoint x="732" y="540" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
