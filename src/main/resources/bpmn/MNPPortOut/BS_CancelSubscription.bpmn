<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0s2ioce" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.8.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="BS_CancelSubscription" name="BS_CancelSubscription" isExecutable="true" camunda:jobPriority="${priority}">
    <bpmn:startEvent id="Event_03frksg" camunda:asyncBefore="true">
      <bpmn:outgoing>Flow_0r97sag</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="subProcessEndEvent1">
      <bpmn:incoming>Flow_0ntd764</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0r97sag" sourceRef="Event_03frksg" targetRef="BillingCancelSubscription" />
    <bpmn:serviceTask id="BillingCancelSubscription" name="Billing Cancel Subscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0r97sag</bpmn:incoming>
      <bpmn:outgoing>Flow_0ntd764</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ntd764" sourceRef="BillingCancelSubscription" targetRef="subProcessEndEvent1" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="BS_CancelSubscription">
      <bpmndi:BPMNShape id="Event_03frksg_di" bpmnElement="Event_03frksg">
        <dc:Bounds x="152" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1lvy8ur_di" bpmnElement="subProcessEndEvent1">
        <dc:Bounds x="652" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_14h98ga" bpmnElement="BillingCancelSubscription">
        <dc:Bounds x="340" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0r97sag_di" bpmnElement="Flow_0r97sag">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="340" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ntd764_di" bpmnElement="Flow_0ntd764">
        <di:waypoint x="440" y="120" />
        <di:waypoint x="652" y="120" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
