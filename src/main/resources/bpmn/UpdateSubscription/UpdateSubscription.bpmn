<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1gxyh89" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.20.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.20.0">
  <bpmn:process id="UpdateSubscription" name="Update Subscription" isExecutable="true" camunda:jobPriority="${priority}" camunda:historyTimeToLive="180">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0rslc09</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0rslc09" sourceRef="orderExecStart" targetRef="SOMFetchServiceRegistry" />
    <bpmn:exclusiveGateway id="Gateway_0q89054" default="Flow_1xswdi6">
      <bpmn:incoming>Flow_0dx82yn</bpmn:incoming>
      <bpmn:outgoing>Flow_1xswdi6</bpmn:outgoing>
      <bpmn:outgoing>Flow_0yl7y5g</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0dx82yn" sourceRef="SOMFetchServiceRegistry" targetRef="Gateway_0q89054" />
    <bpmn:sequenceFlow id="Flow_1xswdi6" sourceRef="Gateway_0q89054" targetRef="SomForUpdatingSubscriptionId" />
    <bpmn:exclusiveGateway id="Gateway_1lyvt19" default="Flow_02o2nv4">
      <bpmn:incoming>Flow_1ea6972</bpmn:incoming>
      <bpmn:outgoing>Flow_02o2nv4</bpmn:outgoing>
      <bpmn:outgoing>Flow_184d5mh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ea6972" sourceRef="SomForUpdatingSubscriptionId" targetRef="Gateway_1lyvt19" />
    <bpmn:sequenceFlow id="Flow_02o2nv4" sourceRef="Gateway_1lyvt19" targetRef="BSUpdateExpiryDate" />
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1c0cu0d</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1c0cu0d" sourceRef="BSUpdateExpiryDate" targetRef="orderExecEnd" />
    <bpmn:endEvent id="Event_04xytqq">
      <bpmn:incoming>Flow_0yl7y5g</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0yl7y5g" name="Failure" sourceRef="Gateway_0q89054" targetRef="Event_04xytqq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_1bzfkgk">
      <bpmn:incoming>Flow_184d5mh</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_184d5mh" name="Failure" sourceRef="Gateway_1lyvt19" targetRef="Event_1bzfkgk">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="SOMFetchServiceRegistry" name="SOM Fetch Service Registry to get Subscriptions" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0rslc09</bpmn:incoming>
      <bpmn:outgoing>Flow_0dx82yn</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="SomForUpdatingSubscriptionId" name="SOM for updating the subscription id" camunda:asyncBefore="true" camunda:delegateExpression="${somUpdateSubscription}">
      <bpmn:incoming>Flow_1xswdi6</bpmn:incoming>
      <bpmn:outgoing>Flow_1ea6972</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSUpdateExpiryDate" name="BS Updating ExpiryDate" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_02o2nv4</bpmn:incoming>
      <bpmn:outgoing>Flow_1c0cu0d</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="UpdateSubscription">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="179" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0q89054_di" bpmnElement="Gateway_0q89054" isMarkerVisible="true">
        <dc:Bounds x="425" y="92" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1lyvt19_di" bpmnElement="Gateway_1lyvt19" isMarkerVisible="true">
        <dc:Bounds x="685" y="92" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1gz2tjk_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="952" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_04xytqq_di" bpmnElement="Event_04xytqq">
        <dc:Bounds x="432" y="212" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1bzfkgk_di" bpmnElement="Event_1bzfkgk">
        <dc:Bounds x="692" y="212" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_18qkgu2_di" bpmnElement="SOMFetchServiceRegistry">
        <dc:Bounds x="270" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0qrprq3_di" bpmnElement="SomForUpdatingSubscriptionId">
        <dc:Bounds x="530" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0zjq8jw_di" bpmnElement="BSUpdateExpiryDate">
        <dc:Bounds x="790" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0rslc09_di" bpmnElement="Flow_0rslc09">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="270" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dx82yn_di" bpmnElement="Flow_0dx82yn">
        <di:waypoint x="370" y="117" />
        <di:waypoint x="425" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xswdi6_di" bpmnElement="Flow_1xswdi6">
        <di:waypoint x="475" y="117" />
        <di:waypoint x="530" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ea6972_di" bpmnElement="Flow_1ea6972">
        <di:waypoint x="630" y="117" />
        <di:waypoint x="685" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02o2nv4_di" bpmnElement="Flow_02o2nv4">
        <di:waypoint x="735" y="117" />
        <di:waypoint x="790" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c0cu0d_di" bpmnElement="Flow_1c0cu0d">
        <di:waypoint x="890" y="117" />
        <di:waypoint x="952" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yl7y5g_di" bpmnElement="Flow_0yl7y5g">
        <di:waypoint x="450" y="142" />
        <di:waypoint x="450" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="448" y="174" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_184d5mh_di" bpmnElement="Flow_184d5mh">
        <di:waypoint x="710" y="142" />
        <di:waypoint x="710" y="212" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="708" y="174" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
