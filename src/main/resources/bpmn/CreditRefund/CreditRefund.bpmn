<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="CreditRefund" name="CreditRefund" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:serviceTask id="BSCreditRefund" name="BS CreditRefund" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0mv45h8</bpmn:incoming>
      <bpmn:outgoing>Flow_1c4dv1s</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_1c4dv1s</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1c4dv1s" sourceRef="BSCreditRefund" targetRef="orderExecEnd" />
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1rsb8rk</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="NCCAdjustMABalance" name="Adjust Main Account Balance in NCC" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0cz8ezj</bpmn:incoming>
      <bpmn:outgoing>Flow_0cbg4iu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0ac2vk9" default="Flow_18v83yt">
      <bpmn:incoming>Flow_0cbg4iu</bpmn:incoming>
      <bpmn:outgoing>Flow_18v83yt</bpmn:outgoing>
      <bpmn:outgoing>Flow_0jjf8jr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0cbg4iu" sourceRef="NCCAdjustMABalance" targetRef="Gateway_0ac2vk9" />
    <bpmn:exclusiveGateway id="Gateway_1ylmfcs">
      <bpmn:incoming>Flow_18v83yt</bpmn:incoming>
      <bpmn:incoming>Flow_1vimeoo</bpmn:incoming>
      <bpmn:outgoing>Flow_0mv45h8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_18v83yt" name="Success" sourceRef="Gateway_0ac2vk9" targetRef="Gateway_1ylmfcs" />
    <bpmn:sequenceFlow id="Flow_0mv45h8" sourceRef="Gateway_1ylmfcs" targetRef="BSCreditRefund" />
    <bpmn:exclusiveGateway id="Gateway_0nkikbs" name="if refundType is 2" default="Flow_1vimeoo">
      <bpmn:incoming>Flow_1rsb8rk</bpmn:incoming>
      <bpmn:outgoing>Flow_0l9l1zv</bpmn:outgoing>
      <bpmn:outgoing>Flow_1vimeoo</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1rsb8rk" sourceRef="orderExecStart" targetRef="Gateway_0nkikbs" />
    <bpmn:sequenceFlow id="Flow_0l9l1zv" name="yes" sourceRef="Gateway_0nkikbs" targetRef="BSFetchServiceDetails">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${nccAdjustMABalanceCallRqd}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1vimeoo" name="no" sourceRef="Gateway_0nkikbs" targetRef="Gateway_1ylmfcs" />
    <bpmn:endEvent id="Event_0jcxtv5">
      <bpmn:incoming>Flow_0jjf8jr</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0jjf8jr" name="Failure" sourceRef="Gateway_0ac2vk9" targetRef="Event_0jcxtv5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSFetchServiceDetails" name="Billing Fetch Service Details" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0l9l1zv</bpmn:incoming>
      <bpmn:outgoing>Flow_1lpn5ow</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1lpn5ow" sourceRef="BSFetchServiceDetails" targetRef="Gateway_071ll2i" />
    <bpmn:exclusiveGateway id="Gateway_071ll2i" default="Flow_0cz8ezj">
      <bpmn:incoming>Flow_1lpn5ow</bpmn:incoming>
      <bpmn:outgoing>Flow_0cz8ezj</bpmn:outgoing>
      <bpmn:outgoing>Flow_1kf0q4c</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0cz8ezj" name="Success" sourceRef="Gateway_071ll2i" targetRef="NCCAdjustMABalance" />
    <bpmn:endEvent id="Event_13tq9wy">
      <bpmn:incoming>Flow_1kf0q4c</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1kf0q4c" name="Failure" sourceRef="Gateway_071ll2i" targetRef="Event_13tq9wy">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CreditRefund">
      <bpmndi:BPMNEdge id="Flow_1kf0q4c_di" bpmnElement="Flow_1kf0q4c">
        <di:waypoint x="580" y="225" />
        <di:waypoint x="580" y="282" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="579" y="251" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cz8ezj_di" bpmnElement="Flow_0cz8ezj">
        <di:waypoint x="605" y="200" />
        <di:waypoint x="710" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="637" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lpn5ow_di" bpmnElement="Flow_1lpn5ow">
        <di:waypoint x="490" y="200" />
        <di:waypoint x="555" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jjf8jr_di" bpmnElement="Flow_0jjf8jr">
        <di:waypoint x="930" y="225" />
        <di:waypoint x="930" y="282" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="930" y="251" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vimeoo_di" bpmnElement="Flow_1vimeoo">
        <di:waypoint x="250" y="175" />
        <di:waypoint x="250" y="100" />
        <di:waypoint x="1070" y="100" />
        <di:waypoint x="1070" y="175" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="654" y="82" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0l9l1zv_di" bpmnElement="Flow_0l9l1zv">
        <di:waypoint x="275" y="200" />
        <di:waypoint x="390" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="291" y="182" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rsb8rk_di" bpmnElement="Flow_1rsb8rk">
        <di:waypoint x="188" y="200" />
        <di:waypoint x="225" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mv45h8_di" bpmnElement="Flow_0mv45h8">
        <di:waypoint x="1095" y="200" />
        <di:waypoint x="1170" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18v83yt_di" bpmnElement="Flow_18v83yt">
        <di:waypoint x="955" y="200" />
        <di:waypoint x="1045" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="979" y="182" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cbg4iu_di" bpmnElement="Flow_0cbg4iu">
        <di:waypoint x="810" y="200" />
        <di:waypoint x="905" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c4dv1s_di" bpmnElement="Flow_1c4dv1s">
        <di:waypoint x="1270" y="210" />
        <di:waypoint x="1362" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="Activity_0ngn6ow_di" bpmnElement="BSCreditRefund">
        <dc:Bounds x="1170" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1p88c2w_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="1362" y="192" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0g25m9u_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ycitfy_di" bpmnElement="NCCAdjustMABalance">
        <dc:Bounds x="710" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ac2vk9_di" bpmnElement="Gateway_0ac2vk9" isMarkerVisible="true">
        <dc:Bounds x="905" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ylmfcs_di" bpmnElement="Gateway_1ylmfcs" isMarkerVisible="true">
        <dc:Bounds x="1045" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0nkikbs_di" bpmnElement="Gateway_0nkikbs" isMarkerVisible="true">
        <dc:Bounds x="225" y="175" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="209" y="232" width="84" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0jcxtv5_di" bpmnElement="Event_0jcxtv5">
        <dc:Bounds x="912" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_06klpp6_di" bpmnElement="BSFetchServiceDetails">
        <dc:Bounds x="390" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_071ll2i_di" bpmnElement="Gateway_071ll2i" isMarkerVisible="true">
        <dc:Bounds x="555" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_13tq9wy_di" bpmnElement="Event_13tq9wy">
        <dc:Bounds x="562" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
