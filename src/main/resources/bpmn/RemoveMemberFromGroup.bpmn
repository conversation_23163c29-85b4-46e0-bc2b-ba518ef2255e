<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1xm6jg7" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.15.0">
  <bpmn:process id="RemoveMemberFromGroup" name="Remove Member From Group" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1iytpzm</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="NCCRemoveMember" name="NCC Remove Member" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_0cls1ay</bpmn:incoming>
      <bpmn:outgoing>Flow_12r2fyt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="BSRemoveMember" name="Billing Remove Member" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0lnkgbi</bpmn:incoming>
      <bpmn:outgoing>Flow_1hkj0xl</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_0ic07ow" default="Flow_0lnkgbi">
      <bpmn:incoming>Flow_12r2fyt</bpmn:incoming>
      <bpmn:outgoing>Flow_0lnkgbi</bpmn:outgoing>
      <bpmn:outgoing>Flow_0isf98d</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_12r2fyt" sourceRef="NCCRemoveMember" targetRef="Gateway_0ic07ow" />
    <bpmn:sequenceFlow id="Flow_0lnkgbi" name="Success" sourceRef="Gateway_0ic07ow" targetRef="BSRemoveMember" />
    <bpmn:endEvent id="Event_10wl2jy">
      <bpmn:incoming>Flow_0isf98d</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0isf98d" name="Failure" sourceRef="Gateway_0ic07ow" targetRef="Event_10wl2jy">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_17q7v01" default="Flow_0frz4h8">
      <bpmn:incoming>Flow_1hkj0xl</bpmn:incoming>
      <bpmn:outgoing>Flow_0iw4d57</bpmn:outgoing>
      <bpmn:outgoing>Flow_0frz4h8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1hkj0xl" sourceRef="BSRemoveMember" targetRef="Gateway_17q7v01" />
    <bpmn:endEvent id="Event_072ue2p">
      <bpmn:incoming>Flow_0iw4d57</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_0iw4d57" name="Failure" sourceRef="Gateway_17q7v01" targetRef="Event_072ue2p">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>SequenceFlow_1w0fnw3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1iytpzm" sourceRef="orderExecStart" targetRef="BSViewGroupMember" />
    <bpmn:sequenceFlow id="Flow_0frz4h8" name="Success" sourceRef="Gateway_17q7v01" targetRef="SMOfferDeactivation" />
    <bpmn:serviceTask id="BSViewGroupMember" name="BS fetch member info" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1iytpzm</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0qv404c</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0qv404c" sourceRef="BSViewGroupMember" targetRef="ExclusiveGateway_1so3xms" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_1so3xms" default="SequenceFlow_0cls1ay">
      <bpmn:incoming>SequenceFlow_0qv404c</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0cls1ay</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1u5kl5r</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0cls1ay" name="Success" sourceRef="ExclusiveGateway_1so3xms" targetRef="NCCRemoveMember" />
    <bpmn:endEvent id="EndEvent_1mvni94">
      <bpmn:incoming>SequenceFlow_1u5kl5r</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_1u5kl5r" name="Failure" sourceRef="ExclusiveGateway_1so3xms" targetRef="EndEvent_1mvni94">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="BSCancelSubscription" name="BS Cancel subscription" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_18016jw</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1w0fnw3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1w0fnw3" sourceRef="BSCancelSubscription" targetRef="orderExecEnd" />
    <bpmn:serviceTask id="SMOfferDeactivation" name="Offer Deactivation in SM" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0frz4h8</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0bvaxxk</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0bvaxxk" sourceRef="SMOfferDeactivation" targetRef="ExclusiveGateway_1u6m42q" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_1u6m42q" default="SequenceFlow_18016jw">
      <bpmn:incoming>SequenceFlow_0bvaxxk</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_18016jw</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1cxgnna</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_18016jw" name="Success" sourceRef="ExclusiveGateway_1u6m42q" targetRef="BSCancelSubscription" />
    <bpmn:endEvent id="EndEvent_0hya9od">
      <bpmn:incoming>SequenceFlow_1cxgnna</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_1cxgnna" name="Failure" sourceRef="ExclusiveGateway_1u6m42q" targetRef="EndEvent_0hya9od">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Status=='1'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:message id="Message_0rcq5am" name="SOMDeleteCallback" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="RemoveMemberFromGroup">
      <bpmndi:BPMNEdge id="SequenceFlow_1cxgnna_di" bpmnElement="SequenceFlow_1cxgnna">
        <di:waypoint x="1259" y="142" />
        <di:waypoint x="1259" y="202" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1258" y="169" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_18016jw_di" bpmnElement="SequenceFlow_18016jw">
        <di:waypoint x="1284" y="117" />
        <di:waypoint x="1353" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1298" y="99" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0bvaxxk_di" bpmnElement="SequenceFlow_0bvaxxk">
        <di:waypoint x="1189" y="117" />
        <di:waypoint x="1234" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1w0fnw3_di" bpmnElement="SequenceFlow_1w0fnw3">
        <di:waypoint x="1453" y="117" />
        <di:waypoint x="1542" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1u5kl5r_di" bpmnElement="SequenceFlow_1u5kl5r">
        <di:waypoint x="462" y="142" />
        <di:waypoint x="462" y="209" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="470" y="167" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0cls1ay_di" bpmnElement="SequenceFlow_0cls1ay">
        <di:waypoint x="487" y="117" />
        <di:waypoint x="543" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="494" y="99" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0qv404c_di" bpmnElement="SequenceFlow_0qv404c">
        <di:waypoint x="367" y="117" />
        <di:waypoint x="437" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0frz4h8_di" bpmnElement="Flow_0frz4h8">
        <di:waypoint x="1012" y="117" />
        <di:waypoint x="1089" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1029" y="99" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1iytpzm_di" bpmnElement="Flow_1iytpzm">
        <di:waypoint x="192" y="117" />
        <di:waypoint x="267" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0iw4d57_di" bpmnElement="Flow_0iw4d57">
        <di:waypoint x="987" y="142" />
        <di:waypoint x="987" y="202" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="986" y="179" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hkj0xl_di" bpmnElement="Flow_1hkj0xl">
        <di:waypoint x="907" y="117" />
        <di:waypoint x="962" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0isf98d_di" bpmnElement="Flow_0isf98d">
        <di:waypoint x="723" y="142" />
        <di:waypoint x="723" y="202" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="723" y="179" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lnkgbi_di" bpmnElement="Flow_0lnkgbi">
        <di:waypoint x="748" y="117" />
        <di:waypoint x="807" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="756" y="99" width="43" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12r2fyt_di" bpmnElement="Flow_12r2fyt">
        <di:waypoint x="643" y="117" />
        <di:waypoint x="698" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="156" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_15whmup_di" bpmnElement="NCCRemoveMember">
        <dc:Bounds x="543" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_093rfbl_di" bpmnElement="BSRemoveMember">
        <dc:Bounds x="807" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ic07ow_di" bpmnElement="Gateway_0ic07ow" isMarkerVisible="true">
        <dc:Bounds x="698" y="92" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_10wl2jy_di" bpmnElement="Event_10wl2jy">
        <dc:Bounds x="705" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_17q7v01_di" bpmnElement="Gateway_17q7v01" isMarkerVisible="true">
        <dc:Bounds x="962" y="92" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_072ue2p_di" bpmnElement="Event_072ue2p">
        <dc:Bounds x="969" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1y90ydb_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="1542" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0l5qly9_di" bpmnElement="BSViewGroupMember">
        <dc:Bounds x="267" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1so3xms_di" bpmnElement="ExclusiveGateway_1so3xms" isMarkerVisible="true">
        <dc:Bounds x="437" y="92" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1mvni94_di" bpmnElement="EndEvent_1mvni94">
        <dc:Bounds x="444" y="209" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1g76e1l_di" bpmnElement="BSCancelSubscription">
        <dc:Bounds x="1353" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_03ka9nz_di" bpmnElement="SMOfferDeactivation">
        <dc:Bounds x="1089" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1u6m42q_di" bpmnElement="ExclusiveGateway_1u6m42q" isMarkerVisible="true">
        <dc:Bounds x="1234" y="92" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0hya9od_di" bpmnElement="EndEvent_0hya9od">
        <dc:Bounds x="1241" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
