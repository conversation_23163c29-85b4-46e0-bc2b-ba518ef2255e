<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_1en83cp" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="UpdatePaymentPreference" name="UpdatePaymentPreference" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_1p6tqng</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1p6tqng" sourceRef="orderExecStart" targetRef="BSUpdatePaymentPreference" />
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_19z47d6</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_19z47d6" sourceRef="BSUpdatePaymentPreference" targetRef="orderExecEnd" />
    <bpmn:serviceTask id="BSUpdatePaymentPreference" name="Update Payment Preference at profile level in Billing" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_1p6tqng</bpmn:incoming>
      <bpmn:outgoing>Flow_19z47d6</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="UpdatePaymentPreference">
      <bpmndi:BPMNEdge id="Flow_19z47d6_di" bpmnElement="Flow_19z47d6">
        <di:waypoint x="370" y="117" />
        <di:waypoint x="432" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p6tqng_di" bpmnElement="Flow_1p6tqng">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="270" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="orderExecStart">
        <dc:Bounds x="179" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ysswpg_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="432" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1n9fw3z_di" bpmnElement="BSUpdatePaymentPreference">
        <dc:Bounds x="270" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
