<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="Definitions_0xt6om3" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.18.0">
  <bpmn:process id="AddServiceToNewAccount-Rollback" name="AddServiceToAccount-Rollback" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" camunda:asyncBefore="true">
      <bpmn:outgoing>SequenceFlow_140om64</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="SequenceFlow_140om64" sourceRef="StartEvent_1" targetRef="Gateway_1b82tj6" />
    <bpmn:endEvent id="EndEvent_17dzzi8">
      <bpmn:incoming>Flow_13ru0wj</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_0d7y1uh">
      <bpmn:incoming>Flow_14inqnu</bpmn:incoming>
      <bpmn:incoming>Flow_06fnhsh</bpmn:incoming>
      <bpmn:outgoing>Flow_0vh9bws</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1obwj2f" name="Is AccountId created" default="Flow_18xowd8">
      <bpmn:incoming>Flow_0leq5hs</bpmn:incoming>
      <bpmn:outgoing>Flow_19jia3m</bpmn:outgoing>
      <bpmn:outgoing>Flow_18xowd8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="BSDeleteAccount" name="BS Delete Account" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0bszyrh</bpmn:incoming>
      <bpmn:outgoing>Flow_14inqnu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="OCSSDeleteAccount" name="OCS Delete Account" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_19jia3m</bpmn:incoming>
      <bpmn:outgoing>Flow_16e4tzk</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_1b82tj6" name="Is ServiceId created" default="Flow_1djwc7a">
      <bpmn:incoming>SequenceFlow_140om64</bpmn:incoming>
      <bpmn:outgoing>Flow_183154l</bpmn:outgoing>
      <bpmn:outgoing>Flow_1djwc7a</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_19ly376">
      <bpmn:incoming>Flow_1g2eu1k</bpmn:incoming>
      <bpmn:incoming>Flow_15p97sr</bpmn:incoming>
      <bpmn:outgoing>Flow_0leq5hs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="BSDeleteService" name="BS Delete Service" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_01dqlrg</bpmn:incoming>
      <bpmn:outgoing>Flow_1g2eu1k</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="NMSDeleteService" name="NMS Delete Service" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_183154l</bpmn:incoming>
      <bpmn:outgoing>Flow_0x610hc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_14inqnu" sourceRef="BSDeleteAccount" targetRef="Gateway_0d7y1uh" />
    <bpmn:sequenceFlow id="Flow_0leq5hs" sourceRef="Gateway_19ly376" targetRef="Gateway_1obwj2f" />
    <bpmn:sequenceFlow id="Flow_19jia3m" name="Yes" sourceRef="Gateway_1obwj2f" targetRef="OCSSDeleteAccount">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${esb_create_account}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_16e4tzk" sourceRef="OCSSDeleteAccount" targetRef="Gateway_0134p4a" />
    <bpmn:sequenceFlow id="Flow_0x610hc" sourceRef="NMSDeleteService" targetRef="Gateway_19rt3na" />
    <bpmn:sequenceFlow id="Flow_1g2eu1k" sourceRef="BSDeleteService" targetRef="Gateway_19ly376" />
    <bpmn:sequenceFlow id="Flow_183154l" sourceRef="Gateway_1b82tj6" targetRef="NMSDeleteService">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${nms_pair_sim}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0vh9bws" sourceRef="Gateway_0d7y1uh" targetRef="Gateway_15g5w27" />
    <bpmn:exclusiveGateway id="Gateway_1ggbwnp" default="Flow_06fnhsh">
      <bpmn:incoming>Flow_04jqezh</bpmn:incoming>
      <bpmn:outgoing>Flow_0bszyrh</bpmn:outgoing>
      <bpmn:outgoing>Flow_06fnhsh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0bszyrh" sourceRef="Gateway_1ggbwnp" targetRef="BSDeleteAccount">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${bs_create_account}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_06fnhsh" sourceRef="Gateway_1ggbwnp" targetRef="Gateway_0d7y1uh" />
    <bpmn:exclusiveGateway id="Gateway_0134p4a">
      <bpmn:incoming>Flow_16e4tzk</bpmn:incoming>
      <bpmn:incoming>Flow_18xowd8</bpmn:incoming>
      <bpmn:outgoing>Flow_04jqezh</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_04jqezh" sourceRef="Gateway_0134p4a" targetRef="Gateway_1ggbwnp" />
    <bpmn:sequenceFlow id="Flow_18xowd8" sourceRef="Gateway_1obwj2f" targetRef="Gateway_0134p4a" />
    <bpmn:exclusiveGateway id="Gateway_19rt3na">
      <bpmn:incoming>Flow_0x610hc</bpmn:incoming>
      <bpmn:incoming>Flow_1djwc7a</bpmn:incoming>
      <bpmn:outgoing>Flow_13f9xc0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_13f9xc0" sourceRef="Gateway_19rt3na" targetRef="Gateway_1iqqbsl" />
    <bpmn:sequenceFlow id="Flow_1djwc7a" sourceRef="Gateway_1b82tj6" targetRef="Gateway_19rt3na" />
    <bpmn:exclusiveGateway id="Gateway_1iqqbsl" default="Flow_15p97sr">
      <bpmn:incoming>Flow_13f9xc0</bpmn:incoming>
      <bpmn:outgoing>Flow_01dqlrg</bpmn:outgoing>
      <bpmn:outgoing>Flow_15p97sr</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_01dqlrg" sourceRef="Gateway_1iqqbsl" targetRef="BSDeleteService">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${bs_service_creation}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_15p97sr" sourceRef="Gateway_1iqqbsl" targetRef="Gateway_19ly376" />
    <bpmn:exclusiveGateway id="Gateway_1194ij9">
      <bpmn:incoming>Flow_09zuos7</bpmn:incoming>
      <bpmn:incoming>Flow_0kebhvu</bpmn:incoming>
      <bpmn:outgoing>Flow_13ru0wj</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="NMSUnblockSim" name="NMS Unblock Sim" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_07nnhzs</bpmn:incoming>
      <bpmn:outgoing>Flow_09zuos7</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_15g5w27" default="Flow_0kebhvu">
      <bpmn:incoming>Flow_0vh9bws</bpmn:incoming>
      <bpmn:outgoing>Flow_0kebhvu</bpmn:outgoing>
      <bpmn:outgoing>Flow_07nnhzs</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_09zuos7" sourceRef="NMSUnblockSim" targetRef="Gateway_1194ij9" />
    <bpmn:sequenceFlow id="Flow_0kebhvu" sourceRef="Gateway_15g5w27" targetRef="Gateway_1194ij9" />
    <bpmn:sequenceFlow id="Flow_07nnhzs" sourceRef="Gateway_15g5w27" targetRef="NMSUnblockSim">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${nms_block_asset}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_13ru0wj" sourceRef="Gateway_1194ij9" targetRef="EndEvent_17dzzi8" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="AddServiceToNewAccount-Rollback">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="132" y="172" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_17dzzi8_di" bpmnElement="EndEvent_17dzzi8">
        <dc:Bounds x="2712" y="172" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0d7y1uh_di" bpmnElement="Gateway_0d7y1uh" isMarkerVisible="true">
        <dc:Bounds x="1905" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1obwj2f_di" bpmnElement="Gateway_1obwj2f" isMarkerVisible="true">
        <dc:Bounds x="1155" y="165" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1150" y="222" width="61" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_01c8z0y_di" bpmnElement="BSDeleteAccount">
        <dc:Bounds x="1740" y="150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0q2m5bb_di" bpmnElement="OCSSDeleteAccount">
        <dc:Bounds x="1300" y="150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1b82tj6_di" bpmnElement="Gateway_1b82tj6" isMarkerVisible="true">
        <dc:Bounds x="305" y="165" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="301" y="225" width="58" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_19ly376_di" bpmnElement="Gateway_19ly376" isMarkerVisible="true">
        <dc:Bounds x="1005" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_19lgt4b_di" bpmnElement="BSDeleteService">
        <dc:Bounds x="810" y="150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ats9yu_di" bpmnElement="NMSDeleteService">
        <dc:Bounds x="440" y="150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1ggbwnp_di" bpmnElement="Gateway_1ggbwnp" isMarkerVisible="true">
        <dc:Bounds x="1535" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0134p4a_di" bpmnElement="Gateway_0134p4a" isMarkerVisible="true">
        <dc:Bounds x="1445" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_19rt3na_di" bpmnElement="Gateway_19rt3na" isMarkerVisible="true">
        <dc:Bounds x="605" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1iqqbsl_di" bpmnElement="Gateway_1iqqbsl" isMarkerVisible="true">
        <dc:Bounds x="705" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0dchu6y" bpmnElement="Gateway_1194ij9" isMarkerVisible="true">
        <dc:Bounds x="2465" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ojg0yp" bpmnElement="NMSUnblockSim">
        <dc:Bounds x="2300" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_036pzyi" bpmnElement="Gateway_15g5w27" isMarkerVisible="true">
        <dc:Bounds x="2095" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_140om64_di" bpmnElement="SequenceFlow_140om64">
        <di:waypoint x="168" y="190" />
        <di:waypoint x="305" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14inqnu_di" bpmnElement="Flow_14inqnu">
        <di:waypoint x="1840" y="190" />
        <di:waypoint x="1905" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0leq5hs_di" bpmnElement="Flow_0leq5hs">
        <di:waypoint x="1055" y="190" />
        <di:waypoint x="1155" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19jia3m_di" bpmnElement="Flow_19jia3m">
        <di:waypoint x="1205" y="190" />
        <di:waypoint x="1300" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1223" y="213" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16e4tzk_di" bpmnElement="Flow_16e4tzk">
        <di:waypoint x="1400" y="190" />
        <di:waypoint x="1445" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x610hc_di" bpmnElement="Flow_0x610hc">
        <di:waypoint x="540" y="190" />
        <di:waypoint x="605" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g2eu1k_di" bpmnElement="Flow_1g2eu1k">
        <di:waypoint x="910" y="190" />
        <di:waypoint x="1005" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_183154l_di" bpmnElement="Flow_183154l">
        <di:waypoint x="355" y="190" />
        <di:waypoint x="440" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vh9bws_di" bpmnElement="Flow_0vh9bws">
        <di:waypoint x="1955" y="190" />
        <di:waypoint x="2095" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bszyrh_di" bpmnElement="Flow_0bszyrh">
        <di:waypoint x="1585" y="190" />
        <di:waypoint x="1740" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06fnhsh_di" bpmnElement="Flow_06fnhsh">
        <di:waypoint x="1560" y="165" />
        <di:waypoint x="1560" y="90" />
        <di:waypoint x="1930" y="90" />
        <di:waypoint x="1930" y="165" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04jqezh_di" bpmnElement="Flow_04jqezh">
        <di:waypoint x="1495" y="190" />
        <di:waypoint x="1535" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18xowd8_di" bpmnElement="Flow_18xowd8">
        <di:waypoint x="1180" y="165" />
        <di:waypoint x="1180" y="110" />
        <di:waypoint x="1470" y="110" />
        <di:waypoint x="1470" y="165" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13f9xc0_di" bpmnElement="Flow_13f9xc0">
        <di:waypoint x="655" y="190" />
        <di:waypoint x="705" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1djwc7a_di" bpmnElement="Flow_1djwc7a">
        <di:waypoint x="330" y="165" />
        <di:waypoint x="330" y="90" />
        <di:waypoint x="630" y="90" />
        <di:waypoint x="630" y="165" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_01dqlrg_di" bpmnElement="Flow_01dqlrg">
        <di:waypoint x="755" y="190" />
        <di:waypoint x="810" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_15p97sr_di" bpmnElement="Flow_15p97sr">
        <di:waypoint x="730" y="165" />
        <di:waypoint x="730" y="90" />
        <di:waypoint x="1030" y="90" />
        <di:waypoint x="1030" y="165" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0eilycu" bpmnElement="Flow_09zuos7">
        <di:waypoint x="2400" y="190" />
        <di:waypoint x="2465" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1wnpu06" bpmnElement="Flow_0kebhvu">
        <di:waypoint x="2120" y="165" />
        <di:waypoint x="2120" y="90" />
        <di:waypoint x="2490" y="90" />
        <di:waypoint x="2490" y="165" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0t3hzx0" bpmnElement="Flow_07nnhzs">
        <di:waypoint x="2145" y="190" />
        <di:waypoint x="2300" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13ru0wj_di" bpmnElement="Flow_13ru0wj">
        <di:waypoint x="2515" y="190" />
        <di:waypoint x="2712" y="190" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
