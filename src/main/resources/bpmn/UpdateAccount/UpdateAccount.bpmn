<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/********/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/********/DI" xmlns:dc="http://www.omg.org/spec/DD/********/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/********/DI" id="Definitions_1a1qybb" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.21.0">
  <bpmn:process id="UpdateAccount" name="UpdateAccount" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:serviceTask id="BSUpdateAccount" name="Billing UpdateAccount" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>Flow_0sm0u4x</bpmn:incoming>
      <bpmn:outgoing>Flow_1nzkyg3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:endEvent id="orderExecEnd" camunda:asyncBefore="true">
      <bpmn:incoming>Flow_1nzkyg3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>Flow_0sm0u4x</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0sm0u4x" sourceRef="orderExecStart" targetRef="BSUpdateAccount" />
    <bpmn:sequenceFlow id="Flow_1nzkyg3" sourceRef="BSUpdateAccount" targetRef="orderExecEnd" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="UpdateAccount">
      <bpmndi:BPMNShape id="Activity_0oi7fzj_di" bpmnElement="BSUpdateAccount">
        <dc:Bounds x="300" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1qkykgh_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="512" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1mgzr44_di" bpmnElement="orderExecStart">
        <dc:Bounds x="152" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0sm0u4x_di" bpmnElement="Flow_0sm0u4x">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="300" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nzkyg3_di" bpmnElement="Flow_1nzkyg3">
        <di:waypoint x="400" y="120" />
        <di:waypoint x="512" y="120" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
