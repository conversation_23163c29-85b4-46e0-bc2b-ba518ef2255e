<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_0jfkxu9" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="4.12.0">
  <bpmn:process id="CreateAdjustment" name="CreateAdjustment" isExecutable="true" camunda:jobPriority="${priority}" camunda:versionTag="${version}">
    <bpmn:startEvent id="orderExecStart">
      <bpmn:outgoing>SequenceFlow_1f65mfl</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="orderExecEnd">
      <bpmn:incoming>Flow_0aw9w7r</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="BSCreateAdjustment" name="Billing CreateAdjustment" camunda:asyncBefore="true" camunda:delegateExpression="${genericTaskExecutor}">
      <bpmn:incoming>SequenceFlow_1f65mfl</bpmn:incoming>
      <bpmn:outgoing>Flow_0aw9w7r</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_1f65mfl" sourceRef="orderExecStart" targetRef="BSCreateAdjustment" />
    <bpmn:sequenceFlow id="Flow_0aw9w7r" sourceRef="BSCreateAdjustment" targetRef="orderExecEnd" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CreateAdjustment">
      <bpmndi:BPMNEdge id="Flow_0aw9w7r_di" bpmnElement="Flow_0aw9w7r">
        <di:waypoint x="377" y="123" />
        <di:waypoint x="472" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1f65mfl_di" bpmnElement="SequenceFlow_1f65mfl">
        <di:waypoint x="192" y="123" />
        <di:waypoint x="277" y="123" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="StartEvent_00b5rc6_di" bpmnElement="orderExecStart">
        <dc:Bounds x="156" y="105" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_05s0ocb_di" bpmnElement="orderExecEnd">
        <dc:Bounds x="472" y="105" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1jea4er_di" bpmnElement="BSCreateAdjustment">
        <dc:Bounds x="277" y="83" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
