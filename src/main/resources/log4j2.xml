<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO">

    <CustomLevels>
        <CustomLevel name="CDR" intLevel="550"/>
    </CustomLevels>

    <Appenders>
        <Console name="ConsoleAppender" target="SYSTEM_OUT">
            <PatternLayout disableAnsi="false">
                <pattern>
                    <![CDATA[%highlight{[COM order-orchestrator $${env:HOSTNAME}] %d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd'T'HH:mm:ss.SSS'Z'}}  [%style{%-40.40C{1.}}{cyan} [%style{%-3L}{magenta}]::[%X{traceId} %X{requestId}]:%highlight{${LOG_LEVEL_PATTERN:-%5p}}{FATAL=red blink, ERROR=red, WARN=yellow bold, INFO=green, DEBUG=green bold, TRACE=blue} :[APP]::[%X{entityId}] : %replace{%m}{(?i)(?<=addrLine1=)(?:'([^']+)'|([^,]+))|(?<=\"addrLine1\":\")[^\"]+?(?=\")|(?<=\"addrLine1\":)(?:'([^']+)'|([^,]+))|(?<=addrLine2=)(?:'([^',]+)'|([^,]+(?:,[^,]+)))|(?<=\"addrLine2\":\")[^\"]+?(?=\")|(?<=\"addrLine2\":)(?:'([^']+)'|([^,]+(?:,[^,]+)))|(?<=addrLine6=)(?:'([^']+)'|([^,]+))|(?<=\"addrLine6\":\")[^\"]+?(?=\")|(?<=\"addrLine6\":)(?:'([^']+)'|([^,]+))|(?<=addrLine7=)(?:'([^']+)'|([^,]+))|(?<=\"addrLine7\":\")[^\"]+?(?=\")|(?<=\"addrLine7\":)(?:'([^']+)'|([^,]+))|(?<=addrLine8=)(?:'([^']+)'|([^,]+))|(?<=\"addrLine8\":\")[^\"]+?(?=\")|(?<=\"addrLine8\":)(?:'([^']+)'|([^,]+))|(?<=firstName=)(?:'([^']+)'|([^,]+))|(?<=\"firstName\":\")[^\"]+?(?=\")|(?<=\"firstName\":)(?:'([^']+)'|([^,]+))|(?<=lastName=)(?:'([^']+)'|([^,]+))|(?<=\"lastName\":\")[^\"]+?(?=\")|(?<=\"lastName\":)(?:'([^']+)'|([^,]+))|(?<=middleName=)(?:'([^']+)'|([^,]+))|(?<=\"middleName\":\")[^\"]+?(?=\")|(?<=\"middleName\":)(?:'([^']+)'|([^,]+))|(?<=nationality=)(?:'([^']+)'|([^,]+))|(?<=\"nationality\":\")[^\"]+?(?=\")|(?<=\"nationality\":)(?:'([^']+)'|([^,]+))|(?<=emailId=)(?:'([^']+)'|([^,]+))|(?<=\"emailId\":\")[^\"]+?(?=\")|(?<=\"emailId\":)(?:'([^']+)'|([^,]+))|(?<=dateOfBirth=)(?:'([^']+)'|([^,]+))|(?<=\"dateOfBirth\":\")[^\"]+?(?=\")|(?<=\"dateOfBirth\":)(?:'([^']+)'|([^,]+))|(?<=identificationNumber=)(?:'([^']+)'|([^,]+))|(?<=\"identificationNumber\":\")[^\"]+?(?=\")|(?<=\"identificationNumber\":)(?:'([^']+)'|([^,]+))|(?<=identificationType=)(?:'([^']+)'|([^,]+))|(?<=\"identificationType\":\")[^\"]+?(?=\")|(?<=\"identificationType\":)(?:'([^']+)'|([^,]+))|(?<=contactEmailId=)(?:'([^']+)'|([^,]+))|(?<=\"contactEmailId\":\")[^\"]+?(?=\")|(?<=\"contactEmailId\":)(?:'([^']+)'|([^,]+))|(?<=notificationEmailId=)(?:'([^']+)'|([^,]+))|(?<=\"notificationEmailId\":\")[^\"]+?(?=\")|(?<=\"notificationEmailId\":)(?:'([^']+)'|([^,]+))}{*********}%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}]]>
                </pattern>
            </PatternLayout>
        </Console>
        <RollingFile name="LogToRollingFile"
                     fileName="D:/singtel-orchestrator/app.log"
                     filePattern="D:/singtel-orchestrator/app-%d{MM-dd-yyyy}-%i.log">
            <PatternLayout disableAnsi="false">
                <pattern>
                    <![CDATA[%highlight{[COM order-orchestrator $${env:HOSTNAME}] %d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd'T'HH:mm:ss.SSS'Z'}}  [%style{%-40.40C{1.}}{cyan} [%style{%-3L}{magenta}]::[%X{traceId} %X{requestId}]:%highlight{${LOG_LEVEL_PATTERN:-%5p}}{FATAL=red blink, ERROR=red, WARN=yellow bold, INFO=green, DEBUG=green bold, TRACE=blue} :[APP]::[%X{entityId}] : %replace{%m}{(?i)(?<=addrLine1=)(?:'([^']+)'|([^,]+))|(?<=\"addrLine1\":\")[^\"]+?(?=\")|(?<=\"addrLine1\":)(?:'([^']+)'|([^,]+))|(?<=addrLine2=)(?:'([^',]+)'|([^,]+(?:,[^,]+)))|(?<=\"addrLine2\":\")[^\"]+?(?=\")|(?<=\"addrLine2\":)(?:'([^']+)'|([^,]+(?:,[^,]+)))|(?<=addrLine6=)(?:'([^']+)'|([^,]+))|(?<=\"addrLine6\":\")[^\"]+?(?=\")|(?<=\"addrLine6\":)(?:'([^']+)'|([^,]+))|(?<=addrLine7=)(?:'([^']+)'|([^,]+))|(?<=\"addrLine7\":\")[^\"]+?(?=\")|(?<=\"addrLine7\":)(?:'([^']+)'|([^,]+))|(?<=addrLine8=)(?:'([^']+)'|([^,]+))|(?<=\"addrLine8\":\")[^\"]+?(?=\")|(?<=\"addrLine8\":)(?:'([^']+)'|([^,]+))|(?<=firstName=)(?:'([^']+)'|([^,]+))|(?<=\"firstName\":\")[^\"]+?(?=\")|(?<=\"firstName\":)(?:'([^']+)'|([^,]+))|(?<=lastName=)(?:'([^']+)'|([^,]+))|(?<=\"lastName\":\")[^\"]+?(?=\")|(?<=\"lastName\":)(?:'([^']+)'|([^,]+))|(?<=middleName=)(?:'([^']+)'|([^,]+))|(?<=\"middleName\":\")[^\"]+?(?=\")|(?<=\"middleName\":)(?:'([^']+)'|([^,]+))|(?<=nationality=)(?:'([^']+)'|([^,]+))|(?<=\"nationality\":\")[^\"]+?(?=\")|(?<=\"nationality\":)(?:'([^']+)'|([^,]+))|(?<=emailId=)(?:'([^']+)'|([^,]+))|(?<=\"emailId\":\")[^\"]+?(?=\")|(?<=\"emailId\":)(?:'([^']+)'|([^,]+))|(?<=dateOfBirth=)(?:'([^']+)'|([^,]+))|(?<=\"dateOfBirth\":\")[^\"]+?(?=\")|(?<=\"dateOfBirth\":)(?:'([^']+)'|([^,]+))|(?<=identificationNumber=)(?:'([^']+)'|([^,]+))|(?<=\"identificationNumber\":\")[^\"]+?(?=\")|(?<=\"identificationNumber\":)(?:'([^']+)'|([^,]+))|(?<=identificationType=)(?:'([^']+)'|([^,]+))|(?<=\"identificationType\":\")[^\"]+?(?=\")|(?<=\"identificationType\":)(?:'([^']+)'|([^,]+))|(?<=contactEmailId=)(?:'([^']+)'|([^,]+))|(?<=\"contactEmailId\":\")[^\"]+?(?=\")|(?<=\"contactEmailId\":)(?:'([^']+)'|([^,]+))|(?<=notificationEmailId=)(?:'([^']+)'|([^,]+))|(?<=\"notificationEmailId\":\")[^\"]+?(?=\")|(?<=\"notificationEmailId\":)(?:'([^']+)'|([^,]+))}{*********}%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}]]>
                </pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"
                                           modulate="true"/>
                <SizeBasedTriggeringPolicy size="1 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!-- kafka -->
        <!--      <Kafka name="Kafka" topic="com-edr">
              <PatternLayout>
                    <Pattern>%m\n</Pattern>
                </PatternLayout>
               <Property name="bootstrap.servers">10.0.12.214:9093,10.0.12.215:9093,10.0.12.216:9093</Property>
              <Property name="security.protocol">SASL_PLAINTEXT</Property>
              <Property name="sasl.mechanism">SCRAM-SHA-512</Property>
               <Property name="requiredNumAcks">0</Property>
               <Property name="syncSend">false</Property>"
               <Property name="sasl.jaas.config">org.apache.kafka.common.security.scram.ScramLoginModule required username="admin" password="6dAdmin6D";</Property>

           </Kafka> -->


        <!-- edr log -->
        <RollingFile name="EDRAppender"
                     fileName="D:/singtel-orchestrator/edr/edr.log"
                     filePattern="D:/singtel-orchestrator/edr/edr-%d{MM-dd-yyyy}-%i.log">
            <PatternLayout>
                <Pattern>%m\n</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"
                                           modulate="true"/>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
        </RollingFile>

    </Appenders>
    <Loggers>
        <Root level="INFO">
            <AppenderRef ref="ConsoleAppender"/>
            <AppenderRef ref="LogToRollingFile"/>
        </Root>

        <Logger name="in.co.sixdee.bss.com.edr.EdrWritingService">
            <AppenderRef ref="EDRAppender"/>
            <AppenderRef ref="Kafka"/>
        </Logger>

    </Loggers>
</Configuration>