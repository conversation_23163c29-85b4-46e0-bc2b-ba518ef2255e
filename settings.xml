<settings xmlns="http://maven.apache.org/SETTINGS/1.2.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.2.0 http://maven.apache.org/xsd/settings-1.2.0.xsd">
 
  <!-- For maven deploy - start here -->
  <servers>
		<server>
			<id>singtel-sg-mvneupgrade-int-singtel</id>
			<username>singtel-sg-mvn-int-singtel-mvn-user</username>
			<password>QKE5anVwiyegmOEU</password>
		</server>
	</servers>
  <!-- Copy below to pom.xml -->
  <!-- 
  <distributionManagement>
		<repository>
			<id>singtel-sg-mvneupgrade-int-singtel</id>
			<name>singtel-sg-mvneupgrade-int-singtel</name>
			<url>http://10.0.13.77:8081/repository/singtel-sg-mvneupgrade-int-singtel-mvn/</url>
		</repository>
	</distributionManagement>
  -->
  <!-- Maven deploy dends here -->

   <mirrors>
        <mirror>
            <id>maven-project-group</id>
			      <mirrorOf>*</mirrorOf>
            <name>maven-project-group</name>
            <url>http://10.0.13.77:8081/repository/singtel-sg-mvneupgrade-int-singtel-mvn-grp/</url>            
        </mirror>
    </mirrors>

</settings>


