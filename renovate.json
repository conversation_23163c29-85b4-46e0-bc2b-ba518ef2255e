{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended", ":rebaseStalePrs", ":enableVulnerabilityAlertsWithLabel('security')"], "ignorePresets": ["workarounds:javaLTSVersions"], "baseBranches": ["development"], "enabledManagers": ["maven", "maven-wrapper"], "dependencyDashboard": true, "dependencyDashboardApproval": false, "branchPrefix": "dependencycheck/", "packageRules": [{"updateTypes": ["major"], "addLabels": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"updateTypes": ["minor"], "addLabels": ["Sem<PERSON><PERSON>"]}, {"updateTypes": ["patch", "digest", "bump"], "addLabels": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"languages": ["java"], "addLabels": ["Lang Java"]}]}