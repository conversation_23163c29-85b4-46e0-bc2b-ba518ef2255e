## Autogenerated script based on developer request for repository
stages:
  - dev_def_env_6-buildapp-maven
  - dev_def_ap_env_6-artifactpush-maven
  - qa_def_ap_env_6-artifactpush-maven
  - dev_def_cs_env_6-codescan-maven
  - dev_def_dp_env_mavenjar_6-dockerbuildandpush-maven
  - qa_def_dp_env_mavenjar_6-dockerbuildandpush-maven
  - dev_def_mu_k8slocal-dev-kmize_1-kustomizemanifestupdate-k8s




variables:  
  TAG: $CI_COMMIT_TAG
  TAG_CHECK: $CI_DEFAULT_BRANCH-$CI_COMMIT_TAG
  CUSTOMER_ID: $CUSTOMER_ID
  APPLICATION_TYPE: $APPLICATION_TYPE
  CI_BUILD_APP_IMAGE: $CI_BUILD_APP_IMAGE
  CI_TEST_APP_IMAGE: $CI_BUILD_APP_IMAGE  
  PACKAGE_TYPE: $PACKAGE_TYPE
  IMAGE_NAME: $IMAGE_NAME
  CONTAINER_NAME: $CONTAINER_NAME
  MODULE_NAME: $MODULE_NAME
  VERSION_COM_ORDER_ORCHESTRATOR_SVC: $CI_COMMIT_TAG
  DOCKER_REGISTRY_6D: $DOCKER_REGISTRY_6D
  LOCAL_NEXUS_REPOSITORY_BASE_URL: $LOCAL_NEXUS_REPOSITORY_BASE_URL
  SONAR_PROJECT_KEY: $SONAR_PROJECT_KEY
  SONAR_SCAN_IMAGE: $SONAR_SCAN_IMAGE
  EMAIL: $GIT_CD_EMAIL
  GIT_AUTHOR_EMAIL: $GIT_CD_EMAIL
  GIT_AUTHOR_NAME: $GIT_CD_USER
  GIT_COMMITTER_EMAIL: $GIT_CD_EMAIL
  GIT_COMMITTER_NAME: $GIT_CD_USER
  GIT_SSL_NO_VERIFY: 1
  



dev_def_env_6-buildapp-maven:
 
  tags:
    - maven

  stage: dev_def_env_6-buildapp-maven
  image: $CI_BUILD_APP_IMAGE
  only:  
    - /^v\d+\.\d+\.\d+-dev-(20\d{2})(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])(\d{3})$/
    - /^v\d+\.\d+\.\d+-qa-(20\d{2})(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])(\d{3})$/
    - merge_requests
  script:
    - env 
    - >
      rm -rf target/*
    - >
      mvn clean install -DskipTests=true -s ./settings.xml
  
    
  artifacts:
    paths:
      - target/*.$PACKAGE_TYPE
  
  

dev_def_ap_env_6-artifactpush-maven:
 
  tags:
    - maven

  stage: dev_def_ap_env_6-artifactpush-maven
  image: $LOCAL_CI_PUBLISH_IMAGE
  only:  
    - /^v\d+\.\d+\.\d+-dev-(20\d{2})(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])(\d{3})$/
  script:
    - env 
    - >
      ls -all $CI_PROJECT_DIR/target
    - >
      tar -czvf $CI_PROJECT_DIR/artifacts-${TAG}.tar.gz $CI_PROJECT_DIR/target/*.$PACKAGE_TYPE
    - >
      echo "tar -czvf $CI_PROJECT_DIR/artifacts-${TAG}.tar.gz $CI_PROJECT_DIR/target/*.$PACKAGE_TYPE"
    - >
      curl -v -X "POST" "$LOCAL_NEXUS_REPOSITORY_BASE_URL/service/rest/v1/components?repository=$NEXUS_REPO_RAW"  -H "accept: application/json" -H "Content-Type: multipart/form-data" -H "Authorization: Basic $NEXUS_REPO_RAW_TOKEN" -F "raw.directory=dev/$APPLICATION_TYPE" -F "raw.asset1=@$CI_PROJECT_DIR/artifacts-${TAG}.tar.gz;type=application/x-zip-compressed" -F "raw.asset1.filename=${CI_PROJECT_TITLE}-${TAG}.tar.gz"
    - >
      rm -f -R $CI_PROJECT_DIR/target/*
    - >
      echo "Artifact download URL: $LOCAL_NEXUS_REPOSITORY_BASE_URL/repository/$NEXUS_REPO_RAW/dev/$APPLICATION_TYPE/${CI_PROJECT_TITLE}-${TAG}.tar.gz"
  
  
  

qa_def_ap_env_6-artifactpush-maven:
 
  tags:
    - maven

  stage: qa_def_ap_env_6-artifactpush-maven
  image: $LOCAL_CI_PUBLISH_IMAGE
  only:  
    - /^v\d+\.\d+\.\d+-qa-(20\d{2})(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])(\d{3})$/
  script:
    - env 
    - >
      ls -all $CI_PROJECT_DIR/target
    - >
      tar -czvf $CI_PROJECT_DIR/artifacts-${TAG}.tar.gz $CI_PROJECT_DIR/target/*.$PACKAGE_TYPE
    - >
      echo "tar -czvf $CI_PROJECT_DIR/artifacts-${TAG}.tar.gz $CI_PROJECT_DIR/target/*.$PACKAGE_TYPE"
    - >
      curl -v -X "POST" "$LOCAL_NEXUS_REPOSITORY_BASE_URL/service/rest/v1/components?repository=$NEXUS_REPO_RAW"  -H "accept: application/json" -H "Content-Type: multipart/form-data" -H "Authorization: Basic $NEXUS_REPO_RAW_TOKEN" -F "raw.directory=qa/$APPLICATION_TYPE" -F "raw.asset1=@$CI_PROJECT_DIR/artifacts-${TAG}.tar.gz;type=application/x-zip-compressed" -F "raw.asset1.filename=${CI_PROJECT_TITLE}-${TAG}.tar.gz"
    - >
      rm -f -R $CI_PROJECT_DIR/target/*
    - >
      echo "Artifact download URL: $LOCAL_NEXUS_REPOSITORY_BASE_URL/repository/$NEXUS_REPO_RAW/qa/$APPLICATION_TYPE/${CI_PROJECT_TITLE}-${TAG}.tar.gz"
  
  
  

dev_def_cs_env_6-codescan-maven:
 
  tags:
    - maven

  stage: dev_def_cs_env_6-codescan-maven
  image: $CI_CODESCAN_APP_IMAGE
  variables:
      GIT_DEPTH: "0"
      SONAR_USER_HOME: "$CI_PROJECT_DIR/.sonar"
  only:  
    - /^v\d+\.\d+\.\d+-dev-(20\d{2})(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])(\d{3})$/
    - /^v\d+\.\d+\.\d+-qa-(20\d{2})(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])(\d{3})$/
    - merge_requests
  script:
    - env 
    - >
      export APP_VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
    - >
      mvn verify sonar:sonar -Dsonar.projectKey="${SONAR_PROJECT_KEY}" -Dsonar.projectName="${CUSTOMER_ID}-${CI_PROJECT_TITLE}" -Dsonar.projectVersion="${APP_VERSION}" -s ./settings.xml
  cache:
    key: "$CI_JOB_NAME"
    paths:
      - .sonar/cache
  allow_failure: true
  
  
  

dev_def_dp_env_mavenjar_6-dockerbuildandpush-maven:
 
  tags:
    - maven

  stage: dev_def_dp_env_mavenjar_6-dockerbuildandpush-maven
  image: $CI_LOCALDOCKER_PUSH_IMAGE
  only:  
    - /^v\d+\.\d+\.\d+-dev-(20\d{2})(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])(\d{3})$/
  before_script:
    - env 
    - >
      docker --version
    - >
      echo $DOCKER_AUTH_CONFIG > ~/.docker/config.json
    - >
      [ "$IGNORE_APP_BUILD" = "N" ] && curl -v -L $LOCAL_NEXUS_REPOSITORY_BASE_URL/repository/$NEXUS_REPO_RAW/dev/$APPLICATION_TYPE/$CI_PROJECT_TITLE-$TAG.tar.gz -o $CI_PROJECT_TITLE-$TAG.tar.gz || echo "No artifact to pull as build is disabled"
    - >
      [ "$IGNORE_APP_BUILD" = "N" ] && tar -xzvf $CI_PROJECT_TITLE-$TAG.tar.gz -C / || echo "No untar as build is disabled"
    - >
      docker login  -u $LOCALDEV_DOCKER_USER -p $LOCALDEV_DOCKER_PWD $LOCALDEV_DOCKER_REGISTRY
  script:
    - env 
    - >
      docker build -t $LOCALDEV_DOCKER_REGISTRY/$CUSTOMER_ID/$PRODUCT_GROUP/$MODULE_NAME/$CONTAINER_NAME:$TAG --build-arg "APPLICATION_VERSION=$TAG" .
    - >
      docker push $LOCALDEV_DOCKER_REGISTRY/$CUSTOMER_ID/$PRODUCT_GROUP/$MODULE_NAME/$CONTAINER_NAME:$TAG
    - >
      echo "Successfully uploaded Container Image Artifact:  $LOCALDEV_DOCKER_REGISTRY/$CUSTOMER_ID/$PRODUCT_GROUP/$MODULE_NAME/$CONTAINER_NAME:$TAG"
  
  
  

qa_def_dp_env_mavenjar_6-dockerbuildandpush-maven:
 
  tags:
    - maven

  stage: qa_def_dp_env_mavenjar_6-dockerbuildandpush-maven
  image: $CI_LOCALDOCKER_PUSH_IMAGE
  only:  
    - /^v\d+\.\d+\.\d+-qa-(20\d{2})(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])(\d{3})$/
  before_script:
    - env 
    - >
      docker --version
    - >
      echo $DOCKER_AUTH_CONFIG > ~/.docker/config.json
    - >
      [ "$IGNORE_APP_BUILD" = "N" ] && curl -v -L $LOCAL_NEXUS_REPOSITORY_BASE_URL/repository/$NEXUS_REPO_RAW/qa/$APPLICATION_TYPE/$CI_PROJECT_TITLE-$TAG.tar.gz -o $CI_PROJECT_TITLE-$TAG.tar.gz || echo "No artifact to pull as build is disabled"
    - >
      [ "$IGNORE_APP_BUILD" = "N" ] && tar -xzvf $CI_PROJECT_TITLE-$TAG.tar.gz -C / || echo "No untar as build is disabled"
    - >
      docker login  -u $LOCALQA_DOCKER_USER -p $LOCALQA_DOCKER_PWD $LOCALQA_DOCKER_REGISTRY
  script:
    - env 
    - >
      docker build -t $LOCALQA_DOCKER_REGISTRY/$CUSTOMER_ID/$PRODUCT_GROUP/$MODULE_NAME/$CONTAINER_NAME:$TAG --build-arg "APPLICATION_VERSION=$TAG" .
    - >
      docker push $LOCALQA_DOCKER_REGISTRY/$CUSTOMER_ID/$PRODUCT_GROUP/$MODULE_NAME/$CONTAINER_NAME:$TAG
    - >
      echo "Successfully uploaded Container Image Artifact:  $LOCALQA_DOCKER_REGISTRY/$CUSTOMER_ID/$PRODUCT_GROUP/$MODULE_NAME/$CONTAINER_NAME:$TAG"
  
  
  

dev_def_mu_k8slocal-dev-kmize_1-kustomizemanifestupdate-k8s:
 
  tags:
    - maven

  stage: dev_def_mu_k8slocal-dev-kmize_1-kustomizemanifestupdate-k8s
  image: $CI_KUSTOMIZE_MANIFESTUPDATE_IMAGE
  only:  
    - /^v\d+\.\d+\.\d+-dev-(20\d{2})(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])(\d{3})$/
  script:
    - env 
    - >
      echo "git clone --branch development  $DEV_MANIFEST_REPO_URL /tmp/cddeploy"
    - >
      git clone --branch development  $DEV_MANIFEST_REPO_URL /tmp/cddeploy
    - >
      export GIT_AUTHOR_NAME="System CI User"
    - >
      export GIT_AUTHOR_EMAIL="<EMAIL>"
    - >
      export GIT_COMMITTER_NAME="System CI User"
    - >
      export GIT_COMMITTER_EMAIL="<EMAIL>"
    - >
      git config --global user.name "System CI User"
    - >
      git config --global user.email "<EMAIL>"
    - >
      cd /tmp/cddeploy && git fetch && git checkout development
    - >
      cd /tmp/cddeploy/overlay/$DEV_DEPLOY_CLUSTER_FOLDER/$DEPLOY_MODULE_NAME
    - >
      kustomize edit set image $CONTAINER_NAME=$LOCALDEV_DOCKER_REGISTRY/$CUSTOMER_ID/$PRODUCT_GROUP/$MODULE_NAME/$CONTAINER_NAME:$TAG
    - >
      cat kustomization.yaml
    - >
      git commit -am "[skip ci autoupdate] image update $CONTAINER_NAME $TAG"
    - >
      git pull --rebase
    - >
      git push origin development
  
  
  


