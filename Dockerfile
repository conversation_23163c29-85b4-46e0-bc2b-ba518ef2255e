FROM **********:5009/tools/eclipse-temurin-17-jdk-base:v1.1.0
MAINTAINER 6d
ARG APPLICATION_VERSION=""
ENV APPLICATION_VERSION=$APPLICATION_VERSION
ENV JAVA_OPTS=""
RUN groupadd --gid 1001 appgroup
RUN useradd --uid 1001 --gid appgroup --home /6dapps appuser
RUN mkdir -p /6dapps/config
RUN mkdir -p /6dapps/LOGS
RUN mkdir -p /6dapps/CDR
ENV TZ Asia/Kolkata
EXPOSE 8080
ARG JAR_FILE=target/order-orchestrator-10.0.0.jar
ADD ${JAR_FILE} app.jar
RUN chown -R 1001:1001 /6dapps/config
RUN chown -R 1001:1001 /6dapps/LOGS
RUN chown -R 1001:1001 /6dapps/CDR
RUN chown -R 1001:1001 /6dapps
RUN chmod -R 777 /6dapps
ENTRYPOINT [ "sh", "-c", "java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -jar /app.jar"]
